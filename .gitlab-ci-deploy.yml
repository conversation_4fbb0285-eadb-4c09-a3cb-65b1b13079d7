stages:
   - build and push

.build:
 image: maven:3.8-openjdk-11
 stage: build and push
 allow_failure: false
 variables:
   GIT_STRATEGY: clone
 script:
#    - sudo usermod -aG docker gitlab-runner
   - export JAVA_HOME=$(readlink -f /usr/bin/java | sed "s:/bin/java::")
   - echo $JAVA_HOME
   - echo $USER
   - pwd
   - mvn --version
   - mvn clean package -Dmaven.test.skip=true -e
   - sleep 10s
   - ls
   - docker build -t noc-index-manager .
   - sleep 3s
   - docker stop noc-index-manager || true && docker rm noc-index-manager || true
   - sleep 3s
   - docker-compose -f noc-index-manager-module.yml up -d
#    - docker-compose up --build -d
   - sleep 1s
#    - docker rmi -f $(docker images -f "dangling=true" -q)

runner_build:
 extends:
   - .build
 tags:
   - noc-dev-1-shell
 only:
   # - tags
   - dev

