stages:
  - Sonar
Sonar:
  stage: Sonar
  variables:
    GIT_STRATEGY: clone
  allow_failure: true
  tags:
    - noc-dev-1-shell
  script:
    - export JAVA_HOME=$(readlink -f /usr/bin/java | sed "s:/bin/java::")
    - mvn install -Dmaven.test.skip=true -e
    - sleep 5s
    - DOCKER_BUILDKIT=1 docker run -u 996:992 --rm --network host -v `pwd`:/app --workdir="/app"  sonarsource/sonar-scanner-cli:5.0.1 sonar-scanner -X $SONAR_SCANNER_OPTS -Dsonar.host.url="$SONAR_HOST_URL" -Dsonar.login="$SONAR_TOKEN" -Dsonar.java.binaries="./target/classes" -Dsonar.sources="./src/" -Dsonar.projectKey="${CI_PROJECT_NAME}" -Dsonar.projectName="$CI_PROJECT_NAME" -Dsonar.projectVersion="${CI_COMMIT_TAG}_${CI_COMMIT_SHORT_SHA}";
  only:
    - tags

