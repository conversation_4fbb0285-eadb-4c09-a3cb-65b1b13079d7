version: '3'
services:
  nocindexmanager:
#    image: noc-index-manager-module:1.0.0.2
    container_name: noc-index-manager
    build:
      context: .
    image: noc-index-manager
    restart: always
    environment:
      - _JAVA_OPTIONS=-Xmx128m -Xms128m
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=7070
      - MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED=true
      - SPRING_R2DBC_URL=r2dbc:postgresql://192.168.11.19::5432/reporting?schema=noc_user
      - SPRING_R2DBC_USERNAME=noc
      - SPRING_R2DBC_PASSWORD=Cntt@123#
      - SPRING_CLOUD_CONFIG_URI=*************************************/config
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=*************************************/eureka
      - STORAGE_FILE_DIRECTORY=/opt/app/storage/
    volumes:
      - /home/<USER>/RESOURCE/storage/:/opt/app/storage/
    ports:
      - 7070:7070