package nocsystem.indexmanager;

import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuOverViewDto;
import nocsystem.indexmanager.repositories.ChiSoKinhDoanhRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuChiNhanhRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@SpringBootTest
class IndexManagerApplicationTests {

	@Test
	void contextLoads() {

	}

}
