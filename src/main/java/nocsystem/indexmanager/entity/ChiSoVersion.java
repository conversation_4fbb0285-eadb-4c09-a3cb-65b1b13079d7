package nocsystem.indexmanager.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "chiso_version")
@IdClass(ChiSoVersionKey.class)
public class ChiSoVersion {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;
    @Id
    @Column(name = "ma_chiso")
    private String maChiSo;
    @Column(name = "version")
    private Long version;
    @Column(name = "updated_at")
    private Long updatedAt;

}
