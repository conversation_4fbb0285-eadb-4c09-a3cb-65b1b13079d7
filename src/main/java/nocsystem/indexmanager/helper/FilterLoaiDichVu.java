package nocsystem.indexmanager.helper;

public class FilterLoaiDichVu {
    public String loaiDichVuLogistic(Integer loaiDichVu) {
        String loaiDichVuCV = "";
        if (loaiDichVu == 0) {
            loaiDichVuCV = "";
        } else if (loaiDichVu == 1) {
            loaiDichVuCV = "FORWARDING";
        } else if (loaiDichVu == 2) {
            loaiDichVuCV = "KHO-VAN";
        }

        return loaiDichVuCV;
    }

    public String loaiDichVuCP(Integer loaiDichVu) {
        String loaiDichVuCV = "";
        if (loaiDichVu == 0) {
            loaiDichVuCV = "";
        } else if (loaiDichVu == 1) {
            loaiDichVuCV = "NoN-COD";
        } else if (loaiDichVu == 2) {
            loaiDichVuCV = "COD";
        } else if (loaiDichVu == 3) {
            loaiDichVuCV = "EXP";
        }

        return loaiDichVuCV;
    }
}
