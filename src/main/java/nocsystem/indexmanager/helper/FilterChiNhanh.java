package nocsystem.indexmanager.helper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FilterChiNhanh {
    public static List<String> NOTREGULAR = List.of(
            new String[]{"CNT", "CTLT", "MG1", "MG2", "MG3", "MG4", "MAM", "SBU FFM", "KDAN", "KDNN", "KHLC",
                    "SBUFM", "VHCM", "TMDV"}
    );

//    public static Map<String, String> maTinh = new HashMap<>() {{
//        put("AGG", "An Giang");
//        put("BDG", "Bình Dư<PERSON>");
//        put("BDH", "Bình Ðịnh");
//        put("BGG", "Bắc Giang");
//        put("BKN", "Bắc Kạn");
//        put("BLU", "Bạ<PERSON> Li<PERSON>");
//        put("<PERSON>N<PERSON>", "<PERSON><PERSON><PERSON>");
//        put("<PERSON><PERSON>", "<PERSON><PERSON>nh <PERSON>ớ<PERSON>");
//        put("BT<PERSON>", "Bến Tre");
//        put("BTN", "<PERSON><PERSON><PERSON> Thuận");
//        put("CBG", "Cao Bằng");
//        put("CMU", "Cà Mau");
//        put("CTO", "Cần Thơ");
//        put("DBN", "Điện Biên");
//        put("DKG", "Đăk Nông");
//        put("DLK", "Daklak");
//        put("DNG", "Đà Nẵng");
//        put("DNI", "Ðồng Nai");
//        put("DTP", "Ðồng Tháp");
//        put("GLI", "Gia Lai");
//        put("HBH", "Hòa Bình");
//        put("HCM", "Hồ Chí Minh");
//        put("HDG", "Hải Dương");
//        put("HGG", "Hà Giang");
//        put("HNI", "Hà Nội");
//        put("HNM", "Hà Nam");
//        put("HPG", "Hải Phòng");
//        put("HTH", "Hà Tĩnh");
//        put("HUE", "Thừa Thiên - Huế");
//        put("HUG", "Hậu Giang");
//        put("HYN", "Hưng Yên");
//        put("KGG", "Kiên Giang");
//        put("KHA", "Khánh Hòa");
//        put("KTM", "Kon Tum");
//        put("LAN", "Long An");
//        put("LCI", "Lào Cai");
//        put("LCU", "Lai Châu");
//        put("LDG", "Lâm Ðồng");
//        put("LSN", "Lạng Sơn");
//        put("NAN", "Nghệ An");
//        put("NBH", "Ninh Bình");
//        put("NDH", "Nam Ðịnh");
//        put("NTN", "Ninh Thuận");
//        put("PHO", "Phú Thọ");
//        put("PYN", "Phú Yên");
//        put("QBH", "Quảng Bình");
//        put("QNH", "Quảng Ninh");
//        put("QNI", "Quảng Ngãi");
//        put("QNM", "Quảng Nam");
//        put("QTI", "Quảng Trị");
//        put("SLA", "Sơn La");
//        put("STG", "Sóc Trăng");
//        put("TBH", "Thái Bình");
//        put("TGG", "Tiền Giang");
//        put("THA", "Thanh Hóa");
//        put("TNH", "Tây Ninh");
//        put("TNN", "Thái Nguyên");
//        put("TQG", "Tuyên Quang");
//        put("TVH", "Trà Vinh");
//        put("VLG", "Vĩnh Long");
//        put("VPC", "Vĩnh Phúc");
//        put("VTU", "Bà Rịa - Vũng Tầu");
//        put("YBN", "Yên Bái");
//    }};

    public static Map<String, String> maTinh;
    static {
        maTinh = new HashMap<>();
        maTinh.put("AGG", "An Giang");
        maTinh.put("BDG", "Bình Dương");
        maTinh.put("BDH", "Bình Ðịnh");
        maTinh.put("BGG", "Bắc Giang");
        maTinh.put("BKN", "Bắc Kạn");
        maTinh.put("BLU", "Bạc Liêu");
        maTinh.put("BNH", "Bắc Ninh");
        maTinh.put("BPC", "Bình Phước");
        maTinh.put("BTE", "Bến Tre");
        maTinh.put("BTN", "Bình Thuận");
        maTinh.put("CBG", "Cao Bằng");
        maTinh.put("CMU", "Cà Mau");
        maTinh.put("CTO", "Cần Thơ");
        maTinh.put("DBN", "Điện Biên");
        maTinh.put("DKG", "Đăk Nông");
        maTinh.put("DLK", "Daklak");
        maTinh.put("DNG", "Đà Nẵng");
        maTinh.put("DNI", "Ðồng Nai");
        maTinh.put("DTP", "Ðồng Tháp");
        maTinh.put("GLI", "Gia Lai");
        maTinh.put("HBH", "Hòa Bình");
        maTinh.put("HCM", "Hồ Chí Minh");
        maTinh.put("HDG", "Hải Dương");
        maTinh.put("HGG", "Hà Giang");
        maTinh.put("HNI", "Hà Nội");
        maTinh.put("HNM", "Hà Nam");
        maTinh.put("HPG", "Hải Phòng");
        maTinh.put("HTH", "Hà Tĩnh");
        maTinh.put("HUE", "Thừa Thiên - Huế");
        maTinh.put("HUG", "Hậu Giang");
        maTinh.put("HYN", "Hưng Yên");
        maTinh.put("KGG", "Kiên Giang");
        maTinh.put("KHA", "Khánh Hòa");
        maTinh.put("KTM", "Kon Tum");
        maTinh.put("LAN", "Long An");
        maTinh.put("LCI", "Lào Cai");
        maTinh.put("LCU", "Lai Châu");
        maTinh.put("LDG", "Lâm Ðồng");
        maTinh.put("LSN", "Lạng Sơn");
        maTinh.put("NAN", "Nghệ An");
        maTinh.put("NBH", "Ninh Bình");
        maTinh.put("NDH", "Nam Ðịnh");
        maTinh.put("NTN", "Ninh Thuận");
        maTinh.put("PHO", "Phú Thọ");
        maTinh.put("PYN", "Phú Yên");
        maTinh.put("QBH", "Quảng Bình");
        maTinh.put("QNH", "Quảng Ninh");
        maTinh.put("QNI", "Quảng Ngãi");
        maTinh.put("QNM", "Quảng Nam");
        maTinh.put("QTI", "Quảng Trị");
        maTinh.put("SLA", "Sơn La");
        maTinh.put("STG", "Sóc Trăng");
        maTinh.put("TBH", "Thái Bình");
        maTinh.put("TGG", "Tiền Giang");
        maTinh.put("THA", "Thanh Hóa");
        maTinh.put("TNH", "Tây Ninh");
        maTinh.put("TNN", "Thái Nguyên");
        maTinh.put("TQG", "Tuyên Quang");
        maTinh.put("TVH", "Trà Vinh");
        maTinh.put("VLG", "Vĩnh Long");
        maTinh.put("VPC", "Vĩnh Phúc");
        maTinh.put("VTU", "Bà Rịa - Vũng Tầu");
        maTinh.put("YBN", "Yên Bái");
    }

    public List<String> listOfRightBranch() {
        List<String> listChiNhanh = new ArrayList<>();
        listChiNhanh.add("VLG");
        listChiNhanh.add("VTU");
        listChiNhanh.add("YBN");
        return listChiNhanh;
    }
}
