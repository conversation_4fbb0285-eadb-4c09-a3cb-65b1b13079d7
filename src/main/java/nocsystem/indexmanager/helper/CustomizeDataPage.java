package nocsystem.indexmanager.helper;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.util.ArrayList;
import java.util.List;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CustomizeDataPage<T> {
    @JsonProperty("total")
    private int total = 0;

    @JsonProperty("offset")
    private int offset = 0;

    @JsonProperty("limit")
    private int limit = 0;

    @JsonProperty("content")
    List<T> content;
}
