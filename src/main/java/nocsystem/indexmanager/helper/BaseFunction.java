package nocsystem.indexmanager.helper;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class BaseFunction {
    public LocalDate getFirstDayOfMonth(LocalDate lastDay) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String month = String.valueOf(lastDay.getMonth().getValue());
        month = month.length() < 2 ? "0" + month : month;
        String year = String.valueOf(lastDay.getYear());
        String firstDayOfMonth = year + "-" + month + "-" + "01";
        LocalDate ngayBatDau = LocalDate.parse(firstDayOfMonth, formatter);

        return ngayBatDau;
    }
}
