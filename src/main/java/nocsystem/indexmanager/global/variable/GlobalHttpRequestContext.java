package nocsystem.indexmanager.global.variable;

import nocsystem.indexmanager.config.aop.logging.GlobalHttpRequest;

public class GlobalHttpRequestContext {
    private static final ThreadLocal<GlobalHttpRequest> globalHttpRequestContext = new ThreadLocal<>();

    public static void setGlobalHttpRequestData(GlobalHttpRequest globalHttpRequest) {
        globalHttpRequestContext.set(globalHttpRequest);
    }

    public static GlobalHttpRequest getGlobalHttpRequestData() {
        return globalHttpRequestContext.get();
    }

    public static void clear() {
        globalHttpRequestContext.remove();
    }
}
