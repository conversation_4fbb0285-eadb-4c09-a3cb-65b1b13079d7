package nocsystem.indexmanager.global.variable;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import nocsystem.indexmanager.models.GlobalConfig.JWTInformDTO;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class UserInfo {
    private String maNhanVien;

    private List<String> listBuuCucVeriable = new ArrayList<>();

    private List<String> listChiNhanhVeriable = new ArrayList<>();

    private String isTcld;
    private String isAdmin;

    private String role;

    private String isPhapChe;

    private String isLanhDaoBuuCuc;

    private String isLanhDaoChiNhanh;

    private String isViewAllDwsReport;

    private String isViewAllCounterWmsReport;

    private String isViewAllDashChiNhanhBuuCuc;

    private JWTInformDTO jwtInformGlobal;

    private String isViewAllBcTiktok;

    private String isRoleViewAllKHL;

    private String isRoleViewDashTCT;

    private String isRoleTctTtvhTtdvkh;

    private String isRoleCbnvChiNhanh;

    private String isRoleCbnvBuuCuc;

    private String isRoleCtyLog;

    private List<String> listBuuCucUserPhuTrach;

    private List<String> listTinhUserPhuTrach;

    private String isPermissionViewTopChiNhanhCLDV;

    private String isViewDTKT;

    private String isTrinhKyDTKT;

    private String isTTDVKH;

    private String isRoleLanhDaoCtyLog;

    private String isRoleMKTTT;

    private String isRoleViewNocPro;

    private String isRoleViewSLCK;

    private String isViewDashNguonLuc;

    public UserInfo() {
        this.listChiNhanhVeriable = new ArrayList<>();
        this.listBuuCucUserPhuTrach = new ArrayList<>();
    }

    public String getMaNhanVien() {
        return maNhanVien;
    }

    public void setMaNhanVien(String maNhanVien) {
        this.maNhanVien = maNhanVien;
    }

    public List<String> getListBuuCucVeriable() {
        if (this.listBuuCucVeriable == null) {
            this.listBuuCucVeriable = new ArrayList<>();
        }
        return listBuuCucVeriable;
    }

    public void setListBuuCucVeriable(List<String> listBuuCucVeriable) {
        this.listBuuCucVeriable = listBuuCucVeriable;
    }

    public List<String> getListChiNhanhVeriable() {
        if (this.listChiNhanhVeriable == null) {
            this.listChiNhanhVeriable = new ArrayList<>();
        }
        return listChiNhanhVeriable;
    }

    public void setListChiNhanhVeriable(List<String> listChiNhanhVeriable) {
        this.listChiNhanhVeriable = listChiNhanhVeriable;
    }

    public String getIsTcld() {
        return isTcld;
    }

    public void setIsTcld(String isTcld) {
        this.isTcld = isTcld;
    }

    public String getIsAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(String isAdmin) {
        this.isAdmin = isAdmin;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getIsPhapChe() {
        return isPhapChe;
    }

    public void setIsPhapChe(String isPhapChe) {
        this.isPhapChe = isPhapChe;
    }

    public String getIsLanhDaoBuuCuc() {
        return isLanhDaoBuuCuc;
    }

    public void setIsLanhDaoBuuCuc(String isLanhDaoBuuCuc) {
        this.isLanhDaoBuuCuc = isLanhDaoBuuCuc;
    }

    public String getIsLanhDaoChiNhanh() {
        return isLanhDaoChiNhanh;
    }

    public void setIsLanhDaoChiNhanh(String isLanhDaoChiNhanh) {
        this.isLanhDaoChiNhanh = isLanhDaoChiNhanh;
    }

    public String getIsViewAllDwsReport() {
        return isViewAllDwsReport;
    }

    public void setIsViewAllDwsReport(String isViewAllDwsReport) {
        this.isViewAllDwsReport = isViewAllDwsReport;
    }

    public String getIsViewAllCounterWmsReport() {
        return isViewAllCounterWmsReport;
    }

    public void setIsViewAllCounterWmsReport(String isViewAllCounterWmsReport) {
        this.isViewAllCounterWmsReport = isViewAllCounterWmsReport;
    }

    public String getIsViewAllDashChiNhanhBuuCuc() {
        return isViewAllDashChiNhanhBuuCuc;
    }

    public void setIsViewAllDashChiNhanhBuuCuc(String isViewAllDashChiNhanhBuuCuc) {
        this.isViewAllDashChiNhanhBuuCuc = isViewAllDashChiNhanhBuuCuc;
    }

    public JWTInformDTO getJwtInformGlobal() {
        return jwtInformGlobal;
    }

    public void setJwtInformGlobal(JWTInformDTO jwtInformGlobal) {
        this.jwtInformGlobal = jwtInformGlobal;
    }

    public String getIsViewAllBcTiktok() {
        return isViewAllBcTiktok;
    }

    public void setIsViewAllBcTiktok(String isViewAllBcTiktok) {
        this.isViewAllBcTiktok = isViewAllBcTiktok;
    }

    public String getIsRoleViewAllKHL() {
        return isRoleViewAllKHL;
    }

    public void setIsRoleViewAllKHL(String isRoleViewAllKHL) {
        this.isRoleViewAllKHL = isRoleViewAllKHL;
    }

    public String getIsRoleViewDashTCT() {
        return isRoleViewDashTCT;
    }

    public void setIsRoleViewDashTCT(String isRoleViewDashTCT) {
        this.isRoleViewDashTCT = isRoleViewDashTCT;
    }

    public String getIsRoleTctTtvhTtdvkh() {
        return isRoleTctTtvhTtdvkh;
    }

    public void setIsRoleTctTtvhTtdvkh(String isRoleTctTtvhTtdvkh) {
        this.isRoleTctTtvhTtdvkh = isRoleTctTtvhTtdvkh;
    }

    public String getIsRoleCbnvChiNhanh() {
        return isRoleCbnvChiNhanh;
    }

    public void setIsRoleCbnvChiNhanh(String isRoleCbnvChiNhanh) {
        this.isRoleCbnvChiNhanh = isRoleCbnvChiNhanh;
    }

    public String getIsRoleCbnvBuuCuc() {
        return isRoleCbnvBuuCuc;
    }

    public void setIsRoleCbnvBuuCuc(String isRoleCbnvBuuCuc) {
        this.isRoleCbnvBuuCuc = isRoleCbnvBuuCuc;
    }

    public List<String> getListBuuCucUserPhuTrach() {
        return listBuuCucUserPhuTrach;
    }

    public void setListBuuCucUserPhuTrach(List<String> listBuuCucUserPhuTrach) {
        this.listBuuCucUserPhuTrach = listBuuCucUserPhuTrach;
    }

    public String getIsRoleCtyLog() {
        return isRoleCtyLog;
    }

    public void setIsRoleCtyLog(String isRoleCtyLog) {
        this.isRoleCtyLog = isRoleCtyLog;
    }

    public List<String> getListTinhUserPhuTrach() {
        return listTinhUserPhuTrach;
    }

    public void setListTinhUserPhuTrach(List<String> listTinhUserPhuTrach) {
        this.listTinhUserPhuTrach = listTinhUserPhuTrach;
    }

    public String getIsTTDVKH() {
        return isTTDVKH;
    }

    public void setIsTTDVKH(String isTTDVKH) {
        this.isTTDVKH = isTTDVKH;
    }

    public String getIsRoleLanhDaoCtyLog() {
        return isRoleLanhDaoCtyLog;
    }

    public void setIsRoleLanhDaoCtyLog(String isRoleLanhDaoCtyLog) {
        this.isRoleLanhDaoCtyLog = isRoleLanhDaoCtyLog;
    }

    public String getIsRoleMKTTT() {
        return isRoleMKTTT;
    }

    public void setIsRoleMKTTT(String isRoleMKTTT) {
        this.isRoleMKTTT = isRoleMKTTT;
    }

    public String getIsTrinhKyDTKT() {
        return isTrinhKyDTKT;
    }

    public void setIsTrinhKyDTKT(String isTrinhKyDTKT) {
        this.isTrinhKyDTKT = isTrinhKyDTKT;
    }
}
