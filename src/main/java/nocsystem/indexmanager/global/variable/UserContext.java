package nocsystem.indexmanager.global.variable;

public class UserContext {
    private static final ThreadLocal<UserInfo> userContext = new ThreadLocal<>();

    public static void setUserData(UserInfo userData) {
        userContext.set(userData);
    }

    public static UserInfo getUserData() {
        return userContext.get();
    }

    public static void clear() {
        userContext.remove();
    }
}
