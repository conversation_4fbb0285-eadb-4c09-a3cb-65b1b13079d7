package nocsystem.indexmanager.config;

import java.util.Base64;

public class ProcessStringUtils{

    public static String encodeString(String text){

        try{
            return Base64.getEncoder().encodeToString(text.getBytes());
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("String incorrect format");
        }

    }

    public static String decodeString(String text){
        try{
            byte[] bytes = Base64.getDecoder().decode(text);

            return new String(bytes);
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("String incorrect format");
        }

    }




}
