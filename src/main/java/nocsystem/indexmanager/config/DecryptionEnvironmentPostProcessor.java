package nocsystem.indexmanager.config;

import com.google.common.base.Strings;
import com.viettel.security.PassTranformer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class DecryptionEnvironmentPostProcessor implements EnvironmentPostProcessor {

    private static final String DECRYPTED_PROPERTY_SOURCE_NAME = "prod";

    private static final String DECRYPTION_KEY = "CNTT@123#";

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        if (isDevProfileActive(environment)) {
            PassTranformer.setInputKey(DECRYPTION_KEY);
            Map<String, Object> decryptedProps = new HashMap<>();
            try {
                List<String> propertyNameList = readConfigFile();
                for (String propertyName : propertyNameList) {
                    String keyENV = parseProperties(propertyName)[0];
                    String keyProperties = parseProperties(propertyName)[1];

                    String encryptedValue = environment.getProperty(keyENV);
                    String decryptedValue;
                    if (!Strings.isNullOrEmpty(encryptedValue)) {
                        decryptedValue = PassTranformer.decrypt(encryptedValue);
                        decryptedProps.put(keyProperties, decryptedValue);
                    }
                }
                environment.getPropertySources().addFirst(new MapPropertySource(DECRYPTED_PROPERTY_SOURCE_NAME, decryptedProps));
                System.out.println("Decrypted Properties: " + decryptedProps);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private List<String> readConfigFile() throws IOException {
        List<String> lines = new ArrayList<>();
        ClassPathResource resource = new ClassPathResource("config.txt");
        String content = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
        String[] splitLines = content.split("\\r?\\n");
        for (String line : splitLines) {
            if (!Strings.isNullOrEmpty(line)){
                lines.add(line.trim());
            }
        }

        return lines;
    }
    private String[] parseProperties(String config) {
        return config.split("=", 2);
    }

    private boolean isDevProfileActive(ConfigurableEnvironment environment) {
        return Arrays.asList(environment.getActiveProfiles()).contains("prod");
    }
}



