package nocsystem.indexmanager.config.SupperSet;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import nocsystem.indexmanager.models.SupperSet.ConnectorInform;
import nocsystem.indexmanager.services.SupperSet.PickAvailableConnectorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.sql.*;
import java.util.HashMap;
import java.util.List;

@Service
public class GenerateConnection {
    @Autowired
    private PickAvailableConnectorService pickAvailableConnectorService;

    @Value("${db-min-connections}")
    private int DB_MIN_CONNECTIONS;

    @Value("${db-max-connections}")
    private int DB_MAX_CONNECTIONS;

    @Value("${db-time-out}")
    private int DB_TIME_OUT;

    @Value("${db-max-life_time}")
    private int DB_MAX_LIFE_TIME;

    @Value("${db-max-connection-time-out}")
    private int DB_CONNECTION_TIME_OUT;

    private HashMap<String, HikariDataSource> hikariDataSourceHashMap;

    @Value("${database.prefix}")
    List<String> dataBasePrefix;

    @PostConstruct
    void init() {
        try {
            hikariDataSourceHashMap = new HashMap<>(10);
            List<String> listPrefix = dataBasePrefix;
//            List<String> listPrefix = List.of("noc-index-manager");
            for (int i = 0; i < listPrefix.size(); i++) {
                String prefix = listPrefix.get(i);
                ConnectorInform connectorInform = pickAvailableConnectorService.getConfigConnector(prefix);
                String url = connectorInform.getUrl();
                String user = connectorInform.getUser();
                String password = connectorInform.getPassword();

                HikariConfig cfg = new HikariConfig();
                cfg.setJdbcUrl(url);
                cfg.setUsername(user);
                cfg.setPassword(password);

                HikariDataSource ds = new HikariDataSource(cfg);
                ds.setMinimumIdle(DB_MIN_CONNECTIONS);
                ds.setMaximumPoolSize(DB_MAX_CONNECTIONS);// Lượng connection tối đa pool có thể chứa, lượng này có thể tăng lên tùy theo Ram, CPU của hệ thống DBMS
                ds.setIdleTimeout(DB_TIME_OUT);// Cấu hình time out cho connection trong pool
                ds.setMaxLifetime(DB_MAX_LIFE_TIME);// Thời gian sống cho 1 connection
                ds.setConnectionTimeout(DB_CONNECTION_TIME_OUT);// Set timeout xử lý cho 1 request

                hikariDataSourceHashMap.put(prefix, ds);
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }


    public Connection createConnection(String prefix) throws SQLException {
        try {
            Connection connection = this.hikariDataSourceHashMap.get(prefix).getConnection();

            return connection;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

    }
}
