package nocsystem.indexmanager.config.SupperSet;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "noc-base")
@Getter
@Setter
public class NocBase {
    @Value("${noc-base-url}")
    private String url;

    @Value("${noc-base-username}")
    private String username;

    @Value("${noc-base-password}")
    private String password;
}
