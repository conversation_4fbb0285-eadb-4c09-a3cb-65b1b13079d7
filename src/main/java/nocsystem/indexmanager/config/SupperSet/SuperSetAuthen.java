package nocsystem.indexmanager.config.SupperSet;

import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.models.SupperSet.SuperSetLogin;
import nocsystem.indexmanager.util.JpaDto;
import org.springframework.beans.factory.annotation.Value;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class SuperSetAuthen {
    @Value("${superset-server.username}")
    private String username;

    @Value("${superset-server.password}")
    private String password;

    @Value("${superset-server.provider}")
    private String provider;

    public String getRequestBody() {
        SuperSetLogin superSetLogin = new SuperSetLogin();
        superSetLogin.setUsername(username);
        superSetLogin.setPassword(password);
        superSetLogin.setProvider(provider);

        return new <PERSON><PERSON>().to<PERSON><PERSON>(superSetLogin);
    }
}
