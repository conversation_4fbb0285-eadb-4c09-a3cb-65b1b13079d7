package nocsystem.indexmanager.config.SupperSet;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "noc-index-manager")
@Getter
@Setter
public class NocIndexManager {
    private String url;
    private String username;
    private String password;
}
