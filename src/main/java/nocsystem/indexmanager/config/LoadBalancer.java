package nocsystem.indexmanager.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class LoadBalancer {
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate(RestTemplateBuilder rtb) {
        return rtb.build();
    }
}
