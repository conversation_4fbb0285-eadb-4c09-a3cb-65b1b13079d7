package nocsystem.indexmanager.config.Hbase;

import org.springframework.context.annotation.ComponentScan;

import java.sql.Connection;
import java.sql.DriverManager;

@ComponentScan
public class PhoenixConnectionFactory {

    //    private static PhoenixConnectionFactory ourInstance;
    //
    //
    ////    private String connection = ConfigInfor.PHOENIX_SERVERS;
    //
    //    public static PhoenixConnectionFactory getInstance() {
    //        if (ourInstance == null) {
    //            synchronized (PhoenixConnectionFactory.class) {
    ////                if (ourInstance == null) {
    //                ourInstance = new PhoenixConnectionFactory();
    ////                }
    //            }
    //        }
    //        return ourInstance;
    //    }
    //
    //    private PhoenixConnectionFactory() {
    //    }
    private PhoenixConnectionFactory() {}

    /*   private static class SingletonHolder {

           private static final PhoenixConnectionFactory INSTANCE = new PhoenixConnectionFactory();
       }

       public static PhoenixConnectionFactory getInstance() {
           return SingletonHolder.INSTANCE;
       }*/
    private static PhoenixConnectionFactory ourInstance;

    public static synchronized PhoenixConnectionFactory getInstance() {
        if (ourInstance == null) {
            ourInstance = new PhoenixConnectionFactory();
        }
        return ourInstance;
    }

    public Connection getPhoenixConnection() throws Exception {
        HbaseConnectionConfig config = BeanUtils.getBean(HbaseConnectionConfig.class);
        String conn = config.getConnectionInfo();

        //staging
        //        return DriverManager.getConnection("*************************,192.168.11.10,192.168.11.11");
        //prod
//        return DriverManager.getConnection("***************************,192.168.12.224,192.168.12.225");
        return DriverManager.getConnection(conn);
    }
}
