package nocsystem.indexmanager.config;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

public class DateTimeUltis {
    public static Long DateToLong(String stringDate) {

        SimpleDateFormat sf = new SimpleDateFormat("dd-MM-yyyy");

        try {
            Date date = sf.parse(stringDate);
            return date.getTime();

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Date Time incorrect format");
        }
    }

    public static Long DateToLong(Date date) {
        SimpleDateFormat sf = new SimpleDateFormat("dd-MM-yyyy");

        try {
            String stringDate = sf.format(date);
            Date parseDate = sf.parse(stringDate);
            return parseDate.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Date Time incorrect format");
        }
    }

    public static String LongToDateString(Long longDate) {

        try {
            Date date = new Date(longDate);
            SimpleDateFormat sf = new SimpleDateFormat("dd-MM-yyyy");

            return sf.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Date Time incorrect format");
        }

    }

    public static LocalDate StringToLocalDate(String dateString) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

            //convert String to LocalDate
            return LocalDate.parse(dateString, formatter);

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Date Time incorrect format");
        }
    }

    public static String convertMilliSecondsToDate(String milli) {
        try {
            long timestamp = Long.parseLong(milli);
            Date date = new Date(timestamp);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            return simpleDateFormat.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Date Time incorrect format");
        }
    }

    public static String convertLongTimestampToString(Long timestamp) {
        if (timestamp != null) {
            Instant instant = Instant.ofEpochMilli(timestamp); // Chuyển đổi thành mili giây
            Date date = Date.from(instant);

            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
            return outputFormat.format(date);
        }
        return null;
    }

    public static String convertLongTimestampToMMDDYYYY(Long timestamp) {
        if (timestamp != null) {
            Instant instant = Instant.ofEpochMilli(timestamp); // Chuyển đổi thành mili giây
            Date date = Date.from(instant);

            SimpleDateFormat outputFormat = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
            outputFormat.setTimeZone(TimeZone.getTimeZone("GMT+7"));
            return outputFormat.format(date);
        }
        return null;
    }
}
