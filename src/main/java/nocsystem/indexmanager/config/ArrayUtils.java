package nocsystem.indexmanager.config;

import java.util.ArrayList;
import java.util.List;

public class ArrayUtils {
    public static <T> List<List<T>> chunk(List<T> array, int chunkSize) {
        List<List<T>> result = new ArrayList<>();
        int index = 0;
        while (index < array.size()) {
            result.add(array.subList(index, Math.min(index + chunkSize, array.size())));
            index += chunkSize;
        }
        return result;
    }
}
