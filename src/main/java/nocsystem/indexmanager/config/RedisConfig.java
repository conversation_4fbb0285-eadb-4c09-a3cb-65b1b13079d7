package nocsystem.indexmanager.config;

import io.lettuce.core.ReadFrom;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Configuration
@ConfigurationProperties(prefix = "redis")
public class RedisConfig {
    @Value("${redis.host}")
    private String redisHost;

    @Value("${redis.port}")
    private int redisPort;

    @Value("${redis.password}")
    private String passWord;

    @Value("${redis.mode}")
    private String redisMode;

    @Value("${spring.redis.cluster.nodes}")
    private String clusterNodes;

    private RedisInstance master;
    private List<RedisInstance> slaves;

    RedisInstance getMaster() {
        return master;
    }

    void setMaster(RedisInstance master) {
        this.master = master;
    }

    List<RedisInstance> getSlaves() {
        return slaves;
    }

    void setSlaves(List<RedisInstance> slaves) {
        this.slaves = slaves;
    }

    @Bean
    public LettuceConnectionFactory redisConnectionFactory() throws Exception {
        try {
            switch (redisMode) {
                // Tạo Cluster Connection tới Redis
                case "cluster":
                    RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration();
                    redisClusterConfiguration.setClusterNodes(getClusterNodes());
                    redisClusterConfiguration.setMaxRedirects(5000);
                    redisClusterConfiguration.setPassword(passWord);
                    return new LettuceConnectionFactory(redisClusterConfiguration);

                // Tạo Replicates Connection tới Redis
                case "replicates":
                    LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                            .readFrom(ReadFrom.REPLICA_PREFERRED)
                            .build();
                    RedisStaticMasterReplicaConfiguration staticMasterReplicaConfiguration = new RedisStaticMasterReplicaConfiguration(this.getMaster().getHost(), this.getMaster().getPort());
                    this.getSlaves().forEach(slave -> staticMasterReplicaConfiguration.addNode(slave.getHost(), slave.getPort()));
                    staticMasterReplicaConfiguration.setPassword(passWord);
                    return new LettuceConnectionFactory(staticMasterReplicaConfiguration, clientConfig);

                // Tạo Basic Connection tới Redis
                default:
                    RedisStandaloneConfiguration redisConf = new RedisStandaloneConfiguration();
                    redisConf.setHostName(redisHost);
                    redisConf.setPort(redisPort);
                    redisConf.setPassword(passWord);
                    return new LettuceConnectionFactory(redisConf);
            }
        } catch (Exception e) {
            throw new Exception(e.getMessage());
        }
    }

    @Bean
    @Primary
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        // tạo ra một RedisTemplate
        // Với Key là Object
        // Value là Object
        // RedisTemplate giúp chúng ta thao tác với Redis
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }

    private static class RedisInstance {

        private String host;
        private int port;

        String getHost() {
            return host;
        }

        void setHost(String host) {
            this.host = host;
        }

        int getPort() {
            return port;
        }

        void setPort(int port) {
            this.port = port;
        }
    }

    private Iterable<RedisNode> getClusterNodes() {
        String[] hostAndPorts = StringUtils.commaDelimitedListToStringArray(clusterNodes);
        Set<RedisNode> clusterNodes = new HashSet<>();
        for (String hostAndPort : hostAndPorts) {
            int lastScIndex = hostAndPort.lastIndexOf(":");
            if (lastScIndex == -1) continue;

            try {
                String host = hostAndPort.substring(0, lastScIndex);
                Integer port = Integer.parseInt(hostAndPort.substring(lastScIndex + 1));
                clusterNodes.add(new RedisNode(host, port));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return clusterNodes;
    }
}
