package nocsystem.indexmanager.config.aop.logging;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleException(HttpServletRequest request, Exception e) {
        String path = (request != null) ? request.getRequestURI() : "";

        // Retrieve the request ID from the MDC context
        String requestId = !MDC.get("requestId").equals("") ? MDC.get("requestId") : "";

        // Log the exception with the request ID
        logger.error(requestId, e);

        // You can include the request ID in the default error response using a custom error message
        String errorMessage = "Request with ID " + requestId + " encountered an exception.";

        // Create a ResponseEntity with the default error response structure
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new CustomErrorResponse("Internal Server Error", errorMessage, HttpStatus.INTERNAL_SERVER_ERROR, path));
    }
}
