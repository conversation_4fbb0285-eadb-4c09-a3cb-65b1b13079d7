package nocsystem.indexmanager.config.aop.logging;

import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;

public class CustomErrorResponse {
    private String error;
    private String message;
    private String timestamp;
    private HttpStatus status;
    private String path;

    // Constructors, getters, and setters
    public CustomErrorResponse() {
    }

    public CustomErrorResponse(String error, String message, HttpStatus status, String path) {
        this.error = error;
        this.message = message;
        this.timestamp = LocalDateTime.now().toString();
        this.status = status;
        this.path = path;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public HttpStatus getStatus() {
        return status;
    }

    public void setStatus(HttpStatus status) {
        this.status = status;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}