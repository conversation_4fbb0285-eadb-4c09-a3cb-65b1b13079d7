package nocsystem.indexmanager.config.aop.logging;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.util.IOUtils;
import nocsystem.indexmanager.global.variable.GlobalHttpRequestContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.LoggerFactory;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;

@Aspect
@Component
public class LoggingAspectExample {
    private static final Logger LOGGER = LogManager.getLogger(LoggingAspectExample.class);

    @Value("${message-logger.exchange.name}")
    private String exchange;

    @Value("${message-logger.routing.key}")
    private String routingKey;

//    @Autowired
//    private RabbitTemplate rabbitTemplate;

    @Value("${service.name}")
    private String service;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * Pointcut that matches all repositories, services and Web REST endpoints.
     */
    @Pointcut(
            "within(@org.springframework.stereotype.Repository *)" +
                    " || within(@org.springframework.stereotype.Service *)" +
                    " || within(@org.springframework.web.bind.annotation.RestController *)"
    )
    public void springBeanPointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    /**
     * Pointcut that matches all Spring beans in the application's main packages.
     */
    @Pointcut("within(nocsystem.indexmanager..*)")
    public void applicationPackagePointcut() {
        // Method is empty as this is just a Pointcut, the implementations are in the advices.
    }

    /**
     * Retrieves the {@link org.slf4j.Logger} associated to the given {@link JoinPoint}.
     *
     * @param joinPoint join point we want the logger for.
     * @return {@link org.slf4j.Logger} associated to the given {@link JoinPoint}.
     */
    private org.slf4j.Logger logger(JoinPoint joinPoint) {
        return LoggerFactory.getLogger(joinPoint.getSignature().getDeclaringTypeName());
    }

    @AfterThrowing(pointcut = "springBeanPointcut()", throwing = "e")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable e) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        StringBuffer sb = new StringBuffer(500);
        StackTraceElement[] st = e.getStackTrace();
        String flagToDefinition = "LOG AT ============> " + service + "\n";
        sb.append(flagToDefinition + e.getClass().getName() + ": " + e.getMessage() + "\n");
        for (int i = 0; i < st.length; i++) {
            sb.append("\t at " + st[i].toString() + "\n");
        }

        /* Show all Request */
        HttpServletRequest httpServletRequest = GlobalHttpRequestContext.getGlobalHttpRequestData().getHttpServletRequest();
        httpServletRequest.getRequestURI();

        sb.append("\n");
        sb.append("======================================REQUEST-LOG=============================================== \n");

        /* Show url information */
        sb.append("URL \n");
        sb.append("\t " + httpServletRequest.getRequestURI() +" \n");
        sb.append("------------------------------------------------------------------ \n");

        /* Show header information */
        sb.append("Headers \n");
        Enumeration headerNames = httpServletRequest.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = (String) headerNames.nextElement();
            sb.append("\t " + headerName + ":" + httpServletRequest.getHeader(headerName) + "\n");
        }

        /* Show param-request information */
        sb.append("------------------------------------------------------------------ \n");
        sb.append("Param-Request \n");
        Enumeration params = httpServletRequest.getParameterNames();
        while (params.hasMoreElements()) {
            String paramName = (String) params.nextElement();
            sb.append("\t " + paramName + ":" + httpServletRequest.getParameter(paramName) + "\n");
        }

        /* Show param-body information */
        sb.append("------------------------------------------------------------------ \n");
        sb.append("Param-Body \n");
        String requestBody = IOUtils.toString(httpServletRequest.getInputStream(), StandardCharsets.UTF_8);
        sb.append(requestBody);

        LOGGER.info(String.format("===========> Message Error-Logger sent to Kafka"));
        kafkaTemplate.send("message-logger-exchange-name", String.valueOf(sb));
//        rabbitTemplate.convertAndSend(exchange, routingKey, sb);
    }
}
