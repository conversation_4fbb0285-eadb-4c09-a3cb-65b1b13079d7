package nocsystem.indexmanager.config.aop.logging;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.util.UUID;

@Component
public class RequestIdFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        // Generate a unique request ID
//        String requestId = UUID.randomUUID().toString();
        String requestId = (String) request.getAttribute("request_id");

        try {
            // Put it into the MDC context
            MDC.put("requestId", requestId);

            // Continue processing the request
            chain.doFilter(request, response);
        } finally {
            // Remove the request ID from the MDC context (cleanup)
            MDC.remove("requestId");
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Initialization code, if needed
    }

    @Override
    public void destroy() {
        // Cleanup code, if needed
    }
}
