package nocsystem.indexmanager.config;

public class SimpleAPIResponse {
    int error;

    String message;

    Object data;

    public SimpleAPIResponse() {
    }

    public SimpleAPIResponse(int error, String message, Object data) {
        this.error = error;
        this.message = message;
        this.data = data;
    }

    public SimpleAPIResponse(Object data) {
        this.data = data;
    }

    public static SimpleAPIResponse data(Object data) {
        return new SimpleAPIResponse(0, null, data);
    }

    public int getError() {
        return error;
    }

    public void setError(int error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
