package nocsystem.indexmanager.config.Interceptor;

import io.micrometer.core.instrument.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.config.GlobalConstant.JwtInformGlobal;
import nocsystem.indexmanager.config.aop.logging.GlobalHttpRequest;
import nocsystem.indexmanager.global.variable.GlobalHttpRequestContext;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.global.variable.UserInfo;
import nocsystem.indexmanager.models.GlobalConfig.JWTInformDTO;
import nocsystem.indexmanager.models.Response.Partner.SystemUserModel;
import nocsystem.indexmanager.servicesIpm.Permission.ListAvailabeChiNhanhBuuCucIpm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.List;

@Slf4j
@Component
public class Interceptor extends HandlerInterceptorAdapter {
    private final ListAvailabeChiNhanhBuuCucIpm listAvailabeChiNhanhBuuCucIpm;

    public Interceptor(ListAvailabeChiNhanhBuuCucIpm listAvailabeChiNhanhBuuCucIpm) {
        this.listAvailabeChiNhanhBuuCucIpm = listAvailabeChiNhanhBuuCucIpm;
    }

    @Value("${message-logger.exchange.name}")
    private String exchange;

    @Value("${message-logger.routing.key}")
    private String routingKey;

//    @Autowired
//    private RabbitTemplate rabbitTemplate;

    @Value("${service.name}")
    private String service;

    @Value("${instance.ip}")
    private String instanceIp;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        System.out.println("======> INSTANCE_IP: " + instanceIp);

        UserInfo userInfo = new UserInfo();
        GlobalHttpRequest globalHttpRequest = new GlobalHttpRequest();
        globalHttpRequest.setHttpServletRequest(request);

        String objChiNhanhBuuCuc = request.getHeader("cn-bc");
        String isTcld = "false";
        String isAdmin = "false";
        String isPhapChe = "false";
        String isRoleViewAllKHL = "false";
        String isRoleViewDashTCT = "false";
        ListVariableLocation.isLanhDaoChiNhanh = "false";
        ListVariableLocation.isLanhDaoBuuCuc = "false";
        ListVariableLocation.isViewAllDwsReport = "false";
        ListVariableLocation.isViewAllCounterWmsReport = "false";
        ListVariableLocation.isViewAllDashChiNhanhBuuCuc = "false";
        String isViewAllBcTiktok = "false";
        String isPermissionViewTopChiNhanhCLDV = "false";
        String isViewDTKT = "false";
        String isTrinhKyDTKT = "false";
        String isTTDVKH = "false";
        String isLanhDaoCtyLog = "false";
        String isViewNocPro = "false";
        String isViewSLCK = "false";
        String isViewDashNguonLuc = "false";

        //Update mới không dùng ListVariableLocation nữa
        userInfo.setIsLanhDaoChiNhanh("false");
        userInfo.setIsLanhDaoBuuCuc("false");
        userInfo.setIsViewAllDashChiNhanhBuuCuc("false");
        userInfo.setIsRoleTctTtvhTtdvkh("false");
        userInfo.setIsRoleCbnvChiNhanh("false");
        userInfo.setIsRoleCbnvBuuCuc("false");
        userInfo.setIsRoleCtyLog("false");
        userInfo.setIsRoleMKTTT("false");


        JwtInformGlobal.jwtInformDTO = new JWTInformDTO();
        if (listAvailabeChiNhanhBuuCucIpm.informFromJWT(request) != null) {
            JwtInformGlobal.jwtInformDTO = listAvailabeChiNhanhBuuCucIpm.informFromJWT(request);
            userInfo.setJwtInformGlobal(listAvailabeChiNhanhBuuCucIpm.informFromJWT(request));
        }

//        log.info("============> JWTInformDTO: username (" + JwtInformGlobal.jwtInformDTO.getSub() + ")");
        System.out.println("============> JWTInformDTO: username (" + JwtInformGlobal.jwtInformDTO.getSub() + ")");

//        String role = jwtInformDTO.getAuth() == null ? "NONE_ROLE" : jwtInformDTO.getAuth();
        String role = request.getHeader("role") == null ? "NONE_ROLE" : request.getHeader("role");
        List<String> listAllDataRole = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "CHUYEN_QUAN_TCT_KD", "CHUYEN_QUAN_TCT_CL", "ROLE_CONGTY_LOG", "ROLE_LANHDAO_CONGTYLOG");
        List<String> listViewAllBC = List.of("ADMIN_CHINHANH", "LANH_DAO_CHINHANH", "CHUYEN_QUAN_CHINHANH", "CN_DIEU_HANH_KD");
        List<String> viewAllReportDWSList = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "CHUYEN_QUAN_TCT_CL", "ROLE_CONGTY_LOG", "ROLE_LANHDAO_CONGTYLOG", "TCT_PCN");
        List<String> viewAllCounterReportWMS = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "TCT_PCN", "ROLE_CONGTY_LOG", "ROLE_LANHDAO_CONGTYLOG");
        List<String> viewAllDashChiNhanhBuuCuc = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "CHUYEN_QUAN_TCT_CL", "LANH_DAO_TTKD", "CHUYEN_QUAN_TCT_KD", "TCT_TTDVKH", "TCT_TCLD", "TCT_PHAP_CHE", "ROLE_LANHDAO_CONGTYLOG");
        List<String> viewAllBcTiktok = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "TCT_PHAP_CHE", "CHUYEN_QUAN_TCT_KD", "LANH_DAO_TTKD", "CHUYEN_QUAN_TCT_CL", "ROLE_CONGTY_LOG", "TCT_TTDVKH");
        List<String> listRoleViewAllDashKHL = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "CHUYEN_QUAN_TCT_KD", "CHUYEN_QUAN_TCT_CL", "CHUYEN_QUAN_TCT_KD", "TCT_PHAP_CHE", "LANH_DAO_CHINHANH", "CHUYEN_QUAN_CHINHANH", "CN_DIEU_HANH_KD", "LANH_DAO_BUUCUC", "TCT_TTDVKH");

        // authorize for Satisfaction Level Function
        List<String> SATISFACTION_LEVEL_ROLE_TCT_TTVH_TTDVKH = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "TCT_TTDVKH");
        List<String> SATISFACTION_LEVEL_ROLE_CHINHANH = List.of("LANH_DAO_CHINHANH", "CN_DIEU_HANH_KD");
        List<String> SATISFACTION_LEVEL_ROLE_BUUCUC = List.of("LANH_DAO_BUUCUC");
        List<String> listRoleViewDashTCT = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "ROLE_CONGTY_LOG", "ROLE_LANHDAO_CONGTYLOG", "LANH_DAO_CHINHANH", "CHUYEN_QUAN_CHINHANH", "CN_DIEU_HANH_KD", "LANH_DAO_BUUCUC");
        List<String> listRoleViewTopChiNhanhCLDV = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH","ROLE_CONGTY_LOG","ROLE_LANHDAO_CONGTYLOG", "LANH_DAO_CHINHANH", "CHUYEN_QUAN_CHINHANH", "CN_DIEU_HANH_KD","LANH_DAO_BUUCUC");
        List<String> listRoleViewDTKT = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "ROLE_CONGTY_LOG", "ROLE_LANHDAO_CONGTYLOG", "TCT_TAI_CHINH");
        List<String> roleMKTTT = List.of("ROLE_MKT_TT");
        List<String> listRoleViewNocPro = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTKD", "CHUYEN_QUAN_TCT_KD", "CHUYEN_QUAN_TCT_CL", "LANH_DAO_CHINHANH", "CHUYEN_QUAN_CHINHANH", "LANH_DAO_BUUCUC", "CN_DIEU_HANH_KD");

        List<String> listRoleTrinhKyDTKT = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "ROLE_LANHDAO_CONGTYLOG");
        List<String> listRoleViewSLCK = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTKD", "CHUYEN_QUAN_TCT_KD", "LANH_DAO_CHINHANH", "CHUYEN_QUAN_CHINHANH", "CN_DIEU_HANH_KD", "LANH_DAO_BUUCUC");
        List<String> listViewDashNguonLuc = List.of("ROLE_ADMIN", "LANH_DAO_TCT", "LANH_DAO_TTVH", "CHUYEN_QUAN_TCT_TTVH", "ROLE_CONGTY_LOG", "ROLE_LANHDAO_CONGTYLOG");

        if (role.equals("TCT_TCLD")) {
            isTcld = "true";
        }
        if (listAllDataRole.contains(role)) {
            isAdmin = "true";
        }
        if (viewAllReportDWSList.contains(role)) {
            ListVariableLocation.isViewAllDwsReport = "true";
        }
        if (viewAllCounterReportWMS.contains(role)) {
            ListVariableLocation.isViewAllCounterWmsReport = "true";
        }
        if (role.equals("TCT_PHAP_CHE")) {
            isPhapChe = "true";
        }
        if (listRoleViewAllDashKHL.contains(role)) {
            isRoleViewAllKHL = "true";
        }
        if (listRoleViewDashTCT.contains(role)) {
            isRoleViewDashTCT = "true";
        }

        if (viewAllBcTiktok.contains(role)) {
            isViewAllBcTiktok = "true";
        }

        if (listViewAllBC.contains(role)) {
            ListVariableLocation.isLanhDaoChiNhanh = "true";
            userInfo.setIsLanhDaoChiNhanh("true");
        }
        if (role.equalsIgnoreCase("LANH_DAO_BUUCUC")) {
            ListVariableLocation.isLanhDaoBuuCuc = "true";
            userInfo.setIsLanhDaoBuuCuc("true");
        }
        if (viewAllDashChiNhanhBuuCuc.contains(role)) {
            ListVariableLocation.isViewAllDashChiNhanhBuuCuc = "true";
            userInfo.setIsViewAllDashChiNhanhBuuCuc("true");
        }
        if (role.equalsIgnoreCase("ROLE_CONGTY_LOG")) {
            ListVariableLocation.isRoleCtyLog = "true";
            userInfo.setIsRoleCtyLog("true");
        }

        if (role.equalsIgnoreCase("ROLE_ADMIN") && SATISFACTION_LEVEL_ROLE_TCT_TTVH_TTDVKH.contains(role)) {
            ListVariableLocation.isAdmin = "true";
            userInfo.setIsAdmin("true");
        } else if (!role.equalsIgnoreCase("ROLE_ADMIN") && SATISFACTION_LEVEL_ROLE_TCT_TTVH_TTDVKH.contains(role)) {
            userInfo.setIsRoleTctTtvhTtdvkh("true");
        }

        if (role.equalsIgnoreCase("LANH_DAO_CHINHANH") && SATISFACTION_LEVEL_ROLE_CHINHANH.contains(role)) {
            ListVariableLocation.isLanhDaoChiNhanh = "true";
            userInfo.setIsLanhDaoChiNhanh("true");
        } else if (!role.equalsIgnoreCase("LANH_DAO_CHINHANH") && SATISFACTION_LEVEL_ROLE_CHINHANH.contains(role)) {
            userInfo.setIsRoleCbnvChiNhanh("true");
        }

        if (role.equalsIgnoreCase("LANH_DAO_BUUCUC") && SATISFACTION_LEVEL_ROLE_BUUCUC.contains(role)) {
            ListVariableLocation.isLanhDaoBuuCuc = "true";
            userInfo.setIsLanhDaoBuuCuc("true");
        } else if (!role.equalsIgnoreCase("LANH_DAO_BUUCUC") && SATISFACTION_LEVEL_ROLE_BUUCUC.contains(role)) {
            userInfo.setIsRoleCbnvBuuCuc("true");
        }

        if(listRoleViewTopChiNhanhCLDV.contains(role)) {
            isPermissionViewTopChiNhanhCLDV = "true";
        }
        if(listRoleViewDTKT.contains(role)) {
            isViewDTKT = "true";
            if (role.equals("ROLE_CONGTY_LOG")) {
                isAdmin = "false";
            }
        }

        if(listRoleTrinhKyDTKT.contains(role)) {
            isTrinhKyDTKT = "true";
        }

        if(role.equalsIgnoreCase("TCT_TTDVKH")) {
            isTTDVKH = "true";
        }
        if(role.equalsIgnoreCase("ROLE_LANHDAO_CONGTYLOG")) {
            isLanhDaoCtyLog = "true";
        }

        if (roleMKTTT.contains(role)) {
            ListVariableLocation.isRoleMKTTT = "true";
            userInfo.setIsRoleMKTTT("true");
        }
        if(listRoleViewNocPro.contains(role)) {
            isViewNocPro = "true";
        }
        if(listRoleViewSLCK.contains(role)) {
            isViewSLCK = "true";
        }
        if (listViewDashNguonLuc.contains(role)) {
            isViewDashNguonLuc = "true";
        }

        System.out.println("Giá trị header cn-bc: " + request.getHeader("cn-bc"));
        System.out.println("Giá trị header role: " + request.getHeader("role"));

        SystemUserModel systemUserModel = listAvailabeChiNhanhBuuCucIpm.getAvailabeChiNhanhBuuCuc(objChiNhanhBuuCuc, isAdmin, role);

        ListVariableLocation.listBuuCucVeriable = systemUserModel.getDanhMucBuuCuc();
        ListVariableLocation.listChiNhanhVeriable = systemUserModel.getDanhMucChiNhanh();
        ListVariableLocation.role = systemUserModel.getRole();

        System.out.println("Danh sach tinh =============>" + systemUserModel.getDanhMucChiNhanh());

        ListVariableLocation.isAdmin = isAdmin;
        ListVariableLocation.isPhapChe = isPhapChe;
        ListVariableLocation.isTcld = isTcld;
        ListVariableLocation.isRoleViewAllKHL = isRoleViewAllKHL;
        System.out.println("*****Inside PreHandle Interceptor********");

//        GlobalHttpRequest.httpServletRequest = request;
        GlobalHttpRequestContext.setGlobalHttpRequestData(globalHttpRequest);

        userInfo.setIsAdmin(isAdmin);
        userInfo.setIsPhapChe(isPhapChe);
        userInfo.setListBuuCucVeriable(systemUserModel.getDanhMucBuuCuc());
        userInfo.setRole(role);
        userInfo.setListChiNhanhVeriable(systemUserModel.getDanhMucChiNhanh());
        userInfo.setIsViewAllBcTiktok(isViewAllBcTiktok);
        userInfo.setIsTcld(isTcld);
        userInfo.setIsRoleViewAllKHL(isRoleViewAllKHL);
        userInfo.setIsRoleViewDashTCT(isRoleViewDashTCT);
        userInfo.setListTinhUserPhuTrach(systemUserModel.getDanhMucChiNhanh());
        userInfo.setListBuuCucUserPhuTrach(systemUserModel.getDanhMucBuuCuc());
        userInfo.setIsPermissionViewTopChiNhanhCLDV(isPermissionViewTopChiNhanhCLDV);
        userInfo.setIsViewDTKT(isViewDTKT);
        userInfo.setIsTrinhKyDTKT(isTrinhKyDTKT);
        userInfo.setIsTTDVKH(isTTDVKH);
        userInfo.setIsRoleLanhDaoCtyLog(isLanhDaoCtyLog);
        userInfo.setIsRoleViewNocPro(isViewNocPro);
        userInfo.setIsRoleViewSLCK(isViewSLCK);
        userInfo.setIsViewDashNguonLuc(isViewDashNguonLuc);
//        userInfo.setIsLanhDaoChiNhanh(ListVariableLocation.isLanhDaoChiNhanh);
//        userInfo.setIsLanhDaoBuuCuc(ListVariableLocation.isLanhDaoBuuCuc);
//        userInfo.setIsViewAllDashChiNhanhBuuCuc(ListVariableLocation.isViewAllDashChiNhanhBuuCuc);
        UserContext.setUserData(userInfo);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
        System.out.println("*******Inside postHandle interceptor******");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception exception) throws IOException {
        System.out.println("*********Inside afterCompletion iterceptor********");
        UserInfo listV2 = UserContext.getUserData();
        if (!listV2.getIsAdmin().equals(ListVariableLocation.isAdmin)
        ) {
            log.info("=========> Có lỗi Interceptor");
        }
        UserContext.clear();
    }

    public void alertError(HttpServletRequest request) throws IOException {
        /* Show all Request */
        HttpServletRequest httpServletRequest = GlobalHttpRequestContext.getGlobalHttpRequestData().getHttpServletRequest();
        httpServletRequest.getRequestURI();

        StringBuffer sb = new StringBuffer(500);
        sb.append("\n");
        sb.append("======================================REQUEST-LOG=============================================== \n");

        /* Show url information */
        sb.append("URL \n");
        sb.append("\t " + httpServletRequest.getRequestURI() + " \n");
        sb.append("------------------------------------------------------------------ \n");

        /* Show header information */
        sb.append("Headers \n");
        Enumeration headerNames = httpServletRequest.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = (String) headerNames.nextElement();
            sb.append("\t " + headerName + ":" + httpServletRequest.getHeader(headerName) + "\n");
        }

        /* Show param-request information */
        sb.append("------------------------------------------------------------------ \n");
        sb.append("Param-Request \n");
        Enumeration params = httpServletRequest.getParameterNames();
        while (params.hasMoreElements()) {
            String paramName = (String) params.nextElement();
            sb.append("\t " + paramName + ":" + httpServletRequest.getParameter(paramName) + "\n");
        }

        /* Show param-body information */
        sb.append("------------------------------------------------------------------ \n");
        sb.append("Param-Body \n");
        String requestBody = IOUtils.toString(httpServletRequest.getInputStream(), StandardCharsets.UTF_8);
        sb.append(requestBody);

//        rabbitTemplate.convertAndSend(exchange, routingKey, sb);
    }
}
