package nocsystem.indexmanager.config;

public class SimpleAPIResponseV2 {
    int error;

    String message;

    Object data;

    Pagination pagination;

    public SimpleAPIResponseV2() {
    }

    public SimpleAPIResponseV2(int error, String message, Object data, Pagination pagination) {
        this.error = error;
        this.message = message;
        this.data = data;
        this.pagination = pagination;
    }

    public int getError() {
        return error;
    }

    public void setError(int error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Pagination getPagination() {
        return pagination;
    }

    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }
}
