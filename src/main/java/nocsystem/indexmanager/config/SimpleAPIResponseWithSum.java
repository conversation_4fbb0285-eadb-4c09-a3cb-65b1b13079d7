package nocsystem.indexmanager.config;

public class SimpleAPIResponseWithSum {
    int error;

    String message;

    Object data;

    Object summary;

    public SimpleAPIResponseWithSum() {
    }

    public SimpleAPIResponseWithSum(int error, String message, Object data) {
        this.error = error;
        this.message = message;
        this.data = data;
    }

    public SimpleAPIResponseWithSum(int error, String message, Object data, Object summary) {
        this.error = error;
        this.message = message;
        this.data = data;
        this.summary = summary;
    }

    public SimpleAPIResponseWithSum(Object data) {
        this.data = data;
    }

    public static SimpleAPIResponseWithSum data(Object data) {
        return new SimpleAPIResponseWithSum(0, null, data);
    }

    public int getError() {
        return error;
    }

    public void setError(int error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Object getSummary() {
        return summary;
    }

    public void setSummary(Object summary) {
        this.summary = summary;
    }
}
