package nocsystem.indexmanager.config;

public class Pagination {
    private int page;
    private int size;
    private long total;

    public Pagination() {}

    public Pagination(int p, int s, long t) {
        page = p;
        size = s;
        total = t;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
