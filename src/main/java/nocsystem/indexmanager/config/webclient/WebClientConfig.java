package nocsystem.indexmanager.config.webclient;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;

import javax.net.ssl.SSLException;
import java.time.Duration;


@Configuration
public class WebClientConfig {

    private final int TIMEOUT_RESPONSE_EXPORT_EXCEL = 150;

    @Bean
    public ClientHttpConnector clientHttpConnector(@Value("${webclient.proxy.enabled:}") String enableProxy,
                                                   @Value("${webclient.proxy.host:}") String proxyHost,
                                                   @Value("${webclient.proxy.port:}") Integer proxyPort,
                                                   @Value("${webclient.ssl.ignore:}") String ignoreSSL) throws SSLException, SSLException {

        HttpClient httpClient = HttpClient.create();
        httpClient.resolver(nameResolverSpec -> nameResolverSpec.queryTimeout(Duration.ofSeconds(TIMEOUT_RESPONSE_EXPORT_EXCEL)));

        if ("true".equals(enableProxy) || "1".equals(enableProxy)) {
            httpClient = httpClient.proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP)
                    .host(proxyHost)
                    .port(proxyPort));
        }
        if ("true".equals(ignoreSSL) || "1".equals(ignoreSSL)) {
            SslContext sslContext = SslContextBuilder
                    .forClient()
                    .trustManager(InsecureTrustManagerFactory.INSTANCE)
                    .build();
            httpClient = httpClient.secure(t -> t.sslContext(sslContext));
        }

        return new ReactorClientHttpConnector(httpClient);
    }
//
//    @Bean
//    public ExchangeStrategies exchangeStrategies(ObjectMapper objectMapper) {
//        final int size = 16 * 1024 * 1024;
//        return ExchangeStrategies
//                .builder()
//                .codecs(codec -> {
//                    codec.defaultCodecs().jackson2JsonEncoder(new Jackson2JsonEncoder(objectMapper, MediaType.APPLICATION_JSON));
//                    codec.defaultCodecs().jackson2JsonDecoder(new Jackson2JsonDecoder(objectMapper, MediaType.APPLICATION_JSON));
//                    codec.defaultCodecs().maxInMemorySize(size);
//                }).build();
//    }
//    @Bean
//    public WebClient.Builder webclientBuilder(ClientHttpConnector conn, ExchangeStrategies strategies) {
//        return WebClient.builder().clientConnector(conn).exchangeStrategies(strategies);
//    }
//
    @Bean
    public WebClient.Builder webclientBuilder(ClientHttpConnector conn) {
        return WebClient.builder().clientConnector(conn);
    }


}
