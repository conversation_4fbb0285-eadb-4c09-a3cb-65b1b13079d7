package nocsystem.indexmanager.config;


import org.springframework.data.domain.Pageable;

import java.util.List;

public class PageWithTotalNumber<T> {
    private List<T> content;
    Pageable pageable;
    long total;

    public PageWithTotalNumber(List<T> content, Pageable pageable, long total) {
        this.content = content;
        this.pageable = pageable;
        this.total = total;
    }

    public PageWithTotalNumber() {
    }

    public List<T> getContent() {
        return content;
    }

    public Pageable getPageable() {
        return pageable;
    }

    public long getTotal() {
        return total;
    }

    public void setContent(List<T> content) {
        this.content = content;
    }

    public void setPageable(Pageable pageable) {
        this.pageable = pageable;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
