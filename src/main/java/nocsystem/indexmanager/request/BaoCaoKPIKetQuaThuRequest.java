package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.htrace.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaoCaoKPIKetQuaThuRequest {
    
    private String chiNhanh = "";
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate ngayBaoCao;
    
    private String vung = "";
    
    private String vungCon = "";
    
    private String buuCuc = "";
    
    private String doiTac = "";
    
    private String dichVu = "";
    
    private Integer luyke = 0;
    
    private String loaiDon = "";
}
