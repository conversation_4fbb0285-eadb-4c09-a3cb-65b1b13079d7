package nocsystem.indexmanager.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FilterDetailReconciliationReq {
    @JsonProperty("ngay_phat_thanh_cong_from")
    private LocalDate ngayPhatThanhCongFrom;

    @JsonProperty("ngay_phat_thanh_cong_to")
    private LocalDate ngayPhatThanhCongTo;

    @JsonProperty("ma_chi_nhanh_gui")
    private List<String> maChiNhanhGui;

    @JsonProperty("ma_chi_nhanh_nhan")
    private List<String> maChiNhanhNhan;

    @JsonProperty("ma_van_don")
    private String maVanDon;

    @JsonProperty("page_index")
    private int page;

    @JsonProperty("page_size")
    private int size;
}
