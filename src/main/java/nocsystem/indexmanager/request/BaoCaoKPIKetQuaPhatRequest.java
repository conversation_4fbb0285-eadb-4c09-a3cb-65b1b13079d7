package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.htrace.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaoCaoKPIKetQuaPhatRequest {
    
    private Integer luyKe = 0;
    
    private Integer loaiBaoCao = 1;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate ngayBaoCao;
    
    private String vung = "";
    
    private String vungCon = "";
    
    private String chiNhanh = "";
    
    private String buuCuc = "";
    
    private String buuTa = "";
    
    private String doiTac = "";
    
    private String dichVu = "";
}
