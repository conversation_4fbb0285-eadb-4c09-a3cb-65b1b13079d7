package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.htrace.fasterxml.jackson.annotation.JsonFormat;
import org.apache.htrace.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaoCaoGrabThuRequest {
    
    @JsonProperty("luy_ke")
    private Integer luyKe = 0;

    private Integer cap = 1;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @JsonProperty("ngay_bao_cao")
    private LocalDate ngayBaoCao;

    private String vung = "";
    @JsonProperty("vung_con")
    private String vungCon = "";

    @JsonProperty("chi_nhanh")
    private String chiNhanh = "";

    @JsonProperty("buu_cuc")
    private String buuCuc = "";

    @JsonProperty("buu_ta")
    private String buuTa = "";

    @JsonProperty("doi_tac")
    private String doiTac = "";

    @JsonProperty("dich_vu")
    private String dichVu = "";

    @JsonProperty("loai_don")
    private String loaiDon = "";
    
    private Integer page = 0;
    
    private Integer pageSize = 10;
}
