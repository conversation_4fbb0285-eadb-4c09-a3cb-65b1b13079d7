package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GetBaoCaoTyLeHaiLongRqExcel {

    private LocalDate startDate;
    private LocalDate endDate;

    private List<String> vung;

    private List<String> maChiNhanh;

    private List<String> maBuuCuc;

    private String type;

    private Integer page;

    private Integer size;

    private String sortBy;

    private String sort;

    public GetBaoCaoTyLeHaiLongRqExcel(LocalDate startDate, LocalDate endDate , List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
    }

}
