package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Positive;


@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
public class TongDoanhThuCPReq {
    @Positive(message = "This field is required")
    private String to_time;

    private String ma_chinhanh;

    private String ma_buucuc;
}
