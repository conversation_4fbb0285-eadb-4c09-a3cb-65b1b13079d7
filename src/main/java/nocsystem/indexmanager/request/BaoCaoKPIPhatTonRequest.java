package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaoCaoKPIPhatTonRequest {
    
    private String vung = "";

    @JsonProperty("vung_con")
    private String vungCon = "";

    @JsonProperty("chi_nhanh")
    private String chiNhanh = "";

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    @JsonProperty("ngay_bao_cao")
    private LocalDate ngayBaoCao;

    @JsonProperty("doi_tac")
    private String doiTac = "";

    @JsonProperty("buu_cuc")
    private String buuCuc = "";

    @JsonProperty("dich_vu")
    private String dichVu = "";

    @JsonProperty("buu_ta")
    private String buuTa = "";

    @JsonProperty("luy_ke")
    private Integer luyke = 0;
}
