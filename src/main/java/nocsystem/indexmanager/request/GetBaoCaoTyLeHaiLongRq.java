package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GetBaoCaoTyLeHaiLongRq {
    private LocalDate ngayBaoCao;

    private List<String> vung;

    private List<String> maChiNhanh;

    private List<String> maBuuCuc;

    private String type;

    private Integer page;

    private Integer size;

    private String sortBy;

    private String sort;

    public GetBaoCaoTyLeHaiLongRq(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) {
        this.ngayBaoCao = ngayBaoCao;
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
    }
}
