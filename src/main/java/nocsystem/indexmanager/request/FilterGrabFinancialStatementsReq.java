package nocsystem.indexmanager.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FilterGrabFinancialStatementsReq {
    @JsonProperty("ngay_phat_thanh_cong_from")
    private LocalDate ngayPhatThanhCongFrom;

    @JsonProperty("ngay_phat_thanh_cong_to")
    private LocalDate ngayPhatThanhCongTo;

    @JsonProperty("ma_chi_nhanh")
    private List<String> chiChanh;

    @JsonProperty("ma_buu_cuc")
    private List<String> buuCuc;

    @JsonProperty("page_index")
    private int page;

    @JsonProperty("page_size")
    private int size;

    @JsonProperty("is_export_excel")
    private Boolean isExportExcel;
}
