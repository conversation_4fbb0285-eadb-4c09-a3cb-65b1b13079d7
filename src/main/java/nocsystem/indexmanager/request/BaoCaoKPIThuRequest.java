package nocsystem.indexmanager.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.htrace.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaoCaoKPIThuRequest {
    
    private Integer luyKe = 0;
    
    private Integer cap = 1;
    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate ngayBaoCao;
    
    private String vung = "";
    
    private String vungCon = "";
    
    private String chiNhanh = "";
    
    private String buuCuc = "";
    
    private String doiTac = "";
    
    private String buuTa = "";
    
    private String dichVu = "";
    
    private String loaiDon = "";
    
    private String order = "";
    
    private String orderBy = "";
    
    private Integer page = 0;
    
    private Integer pageSize = 10;
}
