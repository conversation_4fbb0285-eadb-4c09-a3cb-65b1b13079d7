package nocsystem.indexmanager.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoTaiChinhResponseDb;
import nocsystem.indexmanager.services.baocao_taichinh.response.KeHoach3NgayResponseDb;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaoCaoTaiChinhGetForm {

    private List<BaoCaoTaiChinhResponseDb> taiChinhResponseB01B03;
    private List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02;
    private List<KeHoach3NgayResponseDb>  keHoach3NgayResponse;

    private List<BaoCaoTaiChinhResponseDb> taiChinhResponseB01B03_N1;
    private List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02_N1;

    private List<BaoCaoTaiChinhResponseDb> taiChinhResponseKyTruoc;
    private List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02_KyTruoc;
    private BaoCaoTaiChinhBody body;
    private BaoCaoTaiChinhBody bodyN1;
    private BaoCaoTaiChinhBody bodyKyTruoc;


}
