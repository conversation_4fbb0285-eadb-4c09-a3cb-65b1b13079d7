package nocsystem.indexmanager.common;

import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoTaiChinhResponseDb;
import nocsystem.indexmanager.services.baocao_taichinh.response.KeHoach3NgayResponseDb;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ObjectMapValueBaoCaoTC {

    public Map<String, Float> mapBaoCaoTaiChinh(List<BaoCaoTaiChinhResponseDb> list){
        Map<String, Float> dict = new HashMap<>();
        if(list == null || list.isEmpty()) return dict;

        // todo : vd 02_B01, tai sao lai put thang ma ko can check lap vi bo key ( ma_so + loai_bang ) query theo thang luon la unique
        for(BaoCaoTaiChinhResponseDb o : list){
            String key = o.getMaSo() + "_" + o.getLoaiBang();
            dict.put(key, o.getGiaTriLoaiBaoCao());
        }
        return dict;
    }

    public Map<String, Float> mapKeHoachTaiChi(List<KeHoach3NgayResponseDb> list){
        Map<String, Float> dict = new HashMap<>();
        if(list == null || list.isEmpty()) return dict;

        // todo : vd 'ROCE (Return On Capital Employed)'_'KH'
        for(KeHoach3NgayResponseDb o : list){
            String key = o.getChiTieu() + "_" + o.getLoaiBang();
            dict.put(key, o.getGiaTriLoaiBaoCao());
        }
        return dict;
    }


    public Map<String, Float> mapValueB02(List<BaoCaoTaiChinhResponseDb> listB02){
        Map<String, Float> dict = new HashMap<>();
        if(listB02 == null || listB02.isEmpty()) return dict;

        for(BaoCaoTaiChinhResponseDb o : listB02){

            String key = o.getMaSo() + "_B02";
            if(!dict.containsKey(key)){
                dict.put(key, o.getGiaTriLoaiBaoCao());
            }
            else {
                dict.put(key, CalculateCommon.sum2Nums(dict.get(key), o.getGiaTriLoaiBaoCao()));
            }
        }
        return dict;
    }

    public Map<String, Float> mergetDictB02_B01(Map<String, Float> dictB02, Map<String, Float> dictB01_B03){
        if(dictB01_B03 == null || dictB02 == null) return new HashMap<>();

        for(String o : dictB02.keySet()){
            dictB01_B03.put(o, dictB02.get(o));
        }
        return dictB01_B03;
    }


}
