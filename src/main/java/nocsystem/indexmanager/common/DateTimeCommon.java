package nocsystem.indexmanager.common;


public class DateTimeCommon {


//    public static void main(String[] args) {
//        String input = "12023";
//        String[] quarterMonths = getQuarterMonths(input);
//        System.out.println("First month of quarter: " + quarterMonths[0]);
//        System.out.println("Last month of quarter: " + quarterMonths[1]);
//        System.out.println(quarterMonths[1].substring(0,input.length()-4));
//        System.out.println(quarterMonths[1].substring(input.length()-4, input.length()));
//    }

//    public static String[] getQuarterMonths(String input) {
    public static String[] getQuarterMonths(String thang, String nam) {
//        if (!(input.length() == 6 || input.length() == 5)) {
//            throw new IllegalArgumentException("Input must be in format MYYYY (e.g., 82023)");
//        }
//
//        int month = Character.getNumericValue(input.charAt(0));
//        int year = Integer.parseInt(input.substring(1));
//
//        if (month < 1 || month > 12) {
//            throw new IllegalArgumentException("Invalid month value");
//        }
//
//        int quarter = (month - 1) / 3 + 1;
//        int firstMonthOfQuarter = (quarter - 1) * 3 + 1;
//        int lastMonthOfQuarter = firstMonthOfQuarter + 2;
//
//        return new String[]{firstMonthOfQuarter + String.valueOf(year), lastMonthOfQuarter + String.valueOf(year)};
        String [] str_ThangQuy = new String[2];
        if(thang == null || thang.isEmpty() || nam == null || nam.isEmpty()){
            return str_ThangQuy;
        }
        else {
            int thangTmp = Integer.parseInt(thang);
            if(thangTmp >= 1 && thangTmp <= 3){
                str_ThangQuy[0] = "1" + nam;
                str_ThangQuy[1] = "3" + nam;

            }
            else if(thangTmp >= 4 && thangTmp <= 6){
                str_ThangQuy[0] = "4" + nam;
                str_ThangQuy[1] = "6" + nam;

            }
            else if(thangTmp >= 7 && thangTmp <= 9){
                str_ThangQuy[0] = "7" + nam;
                str_ThangQuy[1] = "9" + nam;

            }
            else {
                str_ThangQuy[0] = "10" + nam;
                str_ThangQuy[1] = "12" + nam;
            }
        }
        return str_ThangQuy;
    }


}
