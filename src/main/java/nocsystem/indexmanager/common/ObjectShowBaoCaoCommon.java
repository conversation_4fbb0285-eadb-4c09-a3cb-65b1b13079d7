package nocsystem.indexmanager.common;

import nocsystem.indexmanager.repositories.baocao_taichinh.logic.BaoCaoTaiChinhRepoImpl;
import nocsystem.indexmanager.repositories.baocao_taichinh.logic.KeHoachRepoImpl;
import nocsystem.indexmanager.repositories.baocao_taichinh.logic.query.BaoCaoTaiChinhSql;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoTaiChinhResponseDb;
import nocsystem.indexmanager.services.baocao_taichinh.response.KeHoach3NgayResponseDb;
import nocsystem.indexmanager.services.baocao_taichinh.response.MaSoRequireQuerry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ObjectShowBaoCaoCommon {

    @Autowired
    BaoCaoTaiChinhRepoImpl baoCaoTaiChinhRepo;


    @Autowired
    KeHoachRepoImpl keHoach3NgayRepo;


    public BaoCaoTaiChinhGetForm getForm(BaoCaoTaiChinhBody body , String type) {
        BodyCustom bodyCustom = new BodyCustom();
        String sqlB02 = "";
        String sqlB02_N1 = "";
        String sqlB02_KyTruoc = "";
        String sqlB01_B03 = BaoCaoTaiChinhSql.getSqlFromLoaiBaoCao(body.loaiBaoCao, MaSoRequireQuerry.maSoShowBaoCao);

        List<BaoCaoTaiChinhResponseDb> taiChinhResponse;
        List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02;
        List<KeHoach3NgayResponseDb> keHoachNgay3Response;

        List<BaoCaoTaiChinhResponseDb> taiChinhResponseN1;
        List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02_N1;

        List<BaoCaoTaiChinhResponseDb> taiChinhResponseKyTruoc;
        List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02KyTruoc;


        BaoCaoTaiChinhBody bodyTmp;
        BaoCaoTaiChinhBody bodyN1;
        BaoCaoTaiChinhBody bodyKyTruoc;

        if (type.equals("thang")) {
            bodyTmp = body;
            Integer[] thangTinhB02 = new Integer[1];
            thangTinhB02[0] = Integer.parseInt(body.filterThang);

            sqlB02 = BaoCaoTaiChinhSql.getSqlTableB02(body.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, body.filterNam));

            taiChinhResponse = baoCaoTaiChinhRepo.getDataFromFilter(body, sqlB01_B03);
            taiChinhResponseB02 = baoCaoTaiChinhRepo.getDataB02(body, sqlB02);
            keHoachNgay3Response = keHoach3NgayRepo.getDataFromFilter(body, "thang");

            bodyN1 = bodyCustom.getBodyNamN1(body, "thang");
            Integer[] thangTinhB02_N1 = new Integer[1];
            thangTinhB02_N1[0] = Integer.parseInt(bodyN1.filterThang);
            sqlB02_N1 = BaoCaoTaiChinhSql.getSqlTableB02(body.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02_N1, bodyN1.filterNam));
            taiChinhResponseN1 = baoCaoTaiChinhRepo.getDataFromFilter(bodyN1, sqlB01_B03);
            taiChinhResponseB02_N1 = baoCaoTaiChinhRepo.getDataB02(bodyN1, sqlB02_N1);


            bodyKyTruoc = bodyCustom.getBodyKyTruoc(body, "thang");
            Integer[] thangTinhB02_KyTruoc = new Integer[1];
            thangTinhB02_KyTruoc[0] = Integer.parseInt(bodyKyTruoc.filterThang);

            sqlB02_KyTruoc = BaoCaoTaiChinhSql.getSqlTableB02(body.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02_KyTruoc, bodyKyTruoc.filterNam));
            taiChinhResponseKyTruoc = baoCaoTaiChinhRepo.getDataFromFilter(bodyKyTruoc, sqlB01_B03);
            taiChinhResponseB02KyTruoc = baoCaoTaiChinhRepo.getDataB02(bodyKyTruoc, sqlB02_KyTruoc);


        } else if (type.equals("quy")) {
            bodyTmp = bodyCustom.getBodyQuy(body);
            Integer[] thangTinhB02 = ObjectProcessBaoCaoTC_B02.getThangTrongQuy(bodyTmp.filterThang);
            sqlB02 = BaoCaoTaiChinhSql.getSqlTableB02(bodyTmp.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyTmp.filterNam));
            taiChinhResponse = baoCaoTaiChinhRepo.getDataFromFilter(bodyTmp, sqlB01_B03);
            taiChinhResponseB02 = baoCaoTaiChinhRepo.getDataB02(bodyTmp, sqlB02);
            keHoachNgay3Response = keHoach3NgayRepo.getDataFromFilter(bodyTmp, "quy");

            bodyN1 = bodyCustom.getBodyNamN1(bodyTmp, "quy");
            Integer[] thangTinhB02_N1 = ObjectProcessBaoCaoTC_B02.getThangTrongQuy(bodyN1.filterThang);
            sqlB02_N1 = BaoCaoTaiChinhSql.getSqlTableB02(bodyN1.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02_N1, bodyN1.filterNam));
            taiChinhResponseN1 = baoCaoTaiChinhRepo.getDataFromFilter(bodyN1, sqlB01_B03);
            taiChinhResponseB02_N1 = baoCaoTaiChinhRepo.getDataB02(bodyN1, sqlB02_N1);

            bodyKyTruoc = bodyCustom.getBodyKyTruoc(bodyTmp, "quy");
            Integer[] thangTinhB02KyTruoc = ObjectProcessBaoCaoTC_B02.getThangTrongQuy(bodyKyTruoc.filterThang);
            sqlB02_KyTruoc = BaoCaoTaiChinhSql.getSqlTableB02(bodyKyTruoc.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02KyTruoc, bodyKyTruoc.filterNam));
            taiChinhResponseKyTruoc = baoCaoTaiChinhRepo.getDataFromFilter(bodyKyTruoc, sqlB01_B03);
            taiChinhResponseB02KyTruoc = baoCaoTaiChinhRepo.getDataB02(bodyKyTruoc, sqlB02_KyTruoc);


        } else {


            Integer[] thangTinhB02 =  {1,2,3,4,5,6,7,8,9,10,11,12};

            bodyTmp = bodyCustom.getBodyNam(body);
            sqlB02 = BaoCaoTaiChinhSql.getSqlTableB02(bodyTmp.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyTmp.filterNam));
            taiChinhResponse = baoCaoTaiChinhRepo.getDataFromFilter(bodyTmp, sqlB01_B03);
            taiChinhResponseB02 = baoCaoTaiChinhRepo.getDataB02(bodyTmp, sqlB02);

            keHoachNgay3Response = keHoach3NgayRepo.getDataFromFilter(body, "nam");

            bodyN1 = bodyCustom.getBodyNamN1(bodyTmp, "nam");
            sqlB02_N1 = BaoCaoTaiChinhSql.getSqlTableB02(bodyN1.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyN1.filterNam));
            taiChinhResponseN1 = baoCaoTaiChinhRepo.getDataFromFilter(bodyN1, sqlB01_B03);
            taiChinhResponseB02_N1 = baoCaoTaiChinhRepo.getDataB02(bodyN1, sqlB02_N1);

            bodyKyTruoc = bodyCustom.getBodyKyTruoc(bodyTmp, "nam");
            sqlB02_KyTruoc = BaoCaoTaiChinhSql.getSqlTableB02(bodyKyTruoc.loaiBaoCao, MaSoRequireQuerry.maSoTableB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyKyTruoc.filterNam));
            taiChinhResponseKyTruoc = baoCaoTaiChinhRepo.getDataFromFilter(bodyKyTruoc, sqlB01_B03);
            taiChinhResponseB02KyTruoc = baoCaoTaiChinhRepo.getDataB02(bodyKyTruoc, sqlB02_KyTruoc);

        }

        BaoCaoTaiChinhGetForm baoCaoTaiChinhGetForm = new BaoCaoTaiChinhGetForm();
        baoCaoTaiChinhGetForm.setTaiChinhResponseB01B03(taiChinhResponse);
        baoCaoTaiChinhGetForm.setTaiChinhResponseB02(taiChinhResponseB02);

        baoCaoTaiChinhGetForm.setKeHoach3NgayResponse(keHoachNgay3Response);

        baoCaoTaiChinhGetForm.setTaiChinhResponseB01B03_N1(taiChinhResponseN1);
        baoCaoTaiChinhGetForm.setTaiChinhResponseB02_N1(taiChinhResponseB02_N1);

        baoCaoTaiChinhGetForm.setTaiChinhResponseKyTruoc(taiChinhResponseKyTruoc);
        baoCaoTaiChinhGetForm.setTaiChinhResponseB02_KyTruoc(taiChinhResponseB02KyTruoc);

        baoCaoTaiChinhGetForm.setBody(bodyTmp);
        baoCaoTaiChinhGetForm.setBodyN1(bodyN1);
        baoCaoTaiChinhGetForm.setBodyKyTruoc(bodyKyTruoc);

        return baoCaoTaiChinhGetForm;

    }


// todo : chart
    public BaoCaoTaiChinhGetForm getFormChart(BaoCaoTaiChinhBody body , String type, String listMasoB01, String listMasoB02) {
        BodyCustom bodyCustom = new BodyCustom();
        String sqlB02 = "";
        String sqlB02_N1 = "";
        String sqlB02_KyTruoc = "";
        String sqlB01_B03 = BaoCaoTaiChinhSql.getSqlFromLoaiBaoCao(body.loaiBaoCao, listMasoB01);

        List<BaoCaoTaiChinhResponseDb> taiChinhResponse;
        List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02;
        List<KeHoach3NgayResponseDb> keHoachNgay3Response;

        List<BaoCaoTaiChinhResponseDb> taiChinhResponseN1;
        List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02_N1;

        List<BaoCaoTaiChinhResponseDb> taiChinhResponseKyTruoc;
        List<BaoCaoTaiChinhResponseDb> taiChinhResponseB02KyTruoc;


        BaoCaoTaiChinhBody bodyTmp;
        BaoCaoTaiChinhBody bodyN1;
        BaoCaoTaiChinhBody bodyKyTruoc;

        if (type.equals("thang")) {

            Integer[] thangTinhB02 = new Integer[1];
            thangTinhB02[0] = Integer.parseInt(body.filterThang);

            // hien tai
            bodyTmp = body;
            sqlB02 = BaoCaoTaiChinhSql.getSqlTableB02(body.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, body.filterNam));
            taiChinhResponse = baoCaoTaiChinhRepo.getDataFromFilter(body, sqlB01_B03);
            taiChinhResponseB02 = baoCaoTaiChinhRepo.getDataB02(body, sqlB02);

            keHoachNgay3Response = keHoach3NgayRepo.getDataFromFilter(body, "thang");

            // cung ky
            bodyN1 = bodyCustom.getBodyNamN1(body, "thang");
            Integer[] thangTinhB02_N1 = new Integer[1];
            thangTinhB02_N1[0] = Integer.parseInt(bodyN1.filterThang);
            sqlB02_N1 = BaoCaoTaiChinhSql.getSqlTableB02(body.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02_N1, bodyN1.filterNam));
            taiChinhResponseN1 = baoCaoTaiChinhRepo.getDataFromFilter(bodyN1, sqlB01_B03);
            taiChinhResponseB02_N1 = baoCaoTaiChinhRepo.getDataB02(bodyN1, sqlB02_N1);


            // ky truoc
            bodyKyTruoc = bodyCustom.getBodyKyTruoc(body, "thang");
            Integer[] thangTinhB02_KyTruoc = new Integer[1];
            thangTinhB02_KyTruoc[0] = Integer.parseInt(bodyKyTruoc.filterThang);
            sqlB02_KyTruoc = BaoCaoTaiChinhSql.getSqlTableB02(body.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02_KyTruoc, bodyKyTruoc.filterNam));
            taiChinhResponseKyTruoc = baoCaoTaiChinhRepo.getDataFromFilter(bodyKyTruoc, sqlB01_B03);
            taiChinhResponseB02KyTruoc = baoCaoTaiChinhRepo.getDataB02(bodyKyTruoc, sqlB02_KyTruoc);


        } else if (type.equals("quy")) {
            // hien tai
            bodyTmp = bodyCustom.getBodyQuy(body);
            Integer[] thangTinhB02 = ObjectProcessBaoCaoTC_B02.getThangTrongQuy(bodyTmp.filterThang);
            sqlB02 = BaoCaoTaiChinhSql.getSqlTableB02(bodyTmp.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyTmp.filterNam));
            taiChinhResponse = baoCaoTaiChinhRepo.getDataFromFilter(bodyTmp, sqlB01_B03);
            taiChinhResponseB02 = baoCaoTaiChinhRepo.getDataB02(bodyTmp, sqlB02);
            keHoachNgay3Response = keHoach3NgayRepo.getDataFromFilter(bodyTmp, "quy");


            // cung ky
            bodyN1 = bodyCustom.getBodyNamN1(bodyTmp, "quy");
            Integer[] thangTinhB02_N1 = ObjectProcessBaoCaoTC_B02.getThangTrongQuy(bodyN1.filterThang);
            sqlB02_N1 = BaoCaoTaiChinhSql.getSqlTableB02(bodyN1.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02_N1, bodyN1.filterNam));
            taiChinhResponseN1 = baoCaoTaiChinhRepo.getDataFromFilter(bodyN1, sqlB01_B03);
            taiChinhResponseB02_N1 = baoCaoTaiChinhRepo.getDataB02(bodyN1, sqlB02_N1);


            // ky truoc
            bodyKyTruoc = bodyCustom.getBodyKyTruoc(bodyTmp, "quy");
            Integer[] thangTinhB02KyTruoc = ObjectProcessBaoCaoTC_B02.getThangTrongQuy(bodyKyTruoc.filterThang);
            sqlB02_KyTruoc = BaoCaoTaiChinhSql.getSqlTableB02(bodyKyTruoc.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02KyTruoc, bodyKyTruoc.filterNam));
            taiChinhResponseKyTruoc = baoCaoTaiChinhRepo.getDataFromFilter(bodyKyTruoc, sqlB01_B03);
            taiChinhResponseB02KyTruoc = baoCaoTaiChinhRepo.getDataB02(bodyKyTruoc, sqlB02_KyTruoc);



        } else {


            Integer[] thangTinhB02 =  {1,2,3,4,5,6,7,8,9,10,11,12};

            // hien tai
            bodyTmp = bodyCustom.getBodyNam(body);
            sqlB02 = BaoCaoTaiChinhSql.getSqlTableB02(bodyTmp.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyTmp.filterNam));
            taiChinhResponse = baoCaoTaiChinhRepo.getDataFromFilter(bodyTmp, sqlB01_B03);
            taiChinhResponseB02 = baoCaoTaiChinhRepo.getDataB02(bodyTmp, sqlB02);

            keHoachNgay3Response = keHoach3NgayRepo.getDataFromFilter(body, "nam");

            // cung ky
            bodyN1 = bodyCustom.getBodyNamN1(bodyTmp, "nam");
            sqlB02_N1 = BaoCaoTaiChinhSql.getSqlTableB02(bodyN1.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyN1.filterNam));
            taiChinhResponseN1 = baoCaoTaiChinhRepo.getDataFromFilter(bodyN1, sqlB01_B03);
            taiChinhResponseB02_N1 = baoCaoTaiChinhRepo.getDataB02(bodyN1, sqlB02_N1);

            // ky truoc
            bodyKyTruoc = bodyCustom.getBodyKyTruoc(bodyTmp, "nam");
            sqlB02_KyTruoc = BaoCaoTaiChinhSql.getSqlTableB02(bodyKyTruoc.loaiBaoCao, listMasoB02, ObjectProcessBaoCaoTC_B02.convertThangNam(thangTinhB02, bodyKyTruoc.filterNam));
            taiChinhResponseKyTruoc = baoCaoTaiChinhRepo.getDataFromFilter(bodyKyTruoc, sqlB01_B03);
            taiChinhResponseB02KyTruoc = baoCaoTaiChinhRepo.getDataB02(bodyKyTruoc, sqlB02_KyTruoc);

        }

        BaoCaoTaiChinhGetForm baoCaoTaiChinhGetForm = new BaoCaoTaiChinhGetForm();
        baoCaoTaiChinhGetForm.setTaiChinhResponseB01B03(taiChinhResponse);
        baoCaoTaiChinhGetForm.setTaiChinhResponseB02(taiChinhResponseB02);
        baoCaoTaiChinhGetForm.setKeHoach3NgayResponse(keHoachNgay3Response);
        baoCaoTaiChinhGetForm.setTaiChinhResponseB01B03_N1(taiChinhResponseN1);
        baoCaoTaiChinhGetForm.setTaiChinhResponseB02_N1(taiChinhResponseB02_N1);
        baoCaoTaiChinhGetForm.setTaiChinhResponseKyTruoc(taiChinhResponseKyTruoc);
        baoCaoTaiChinhGetForm.setTaiChinhResponseB02_KyTruoc(taiChinhResponseB02KyTruoc);
        baoCaoTaiChinhGetForm.setBody(bodyTmp);
        baoCaoTaiChinhGetForm.setBodyN1(bodyN1);
        baoCaoTaiChinhGetForm.setBodyKyTruoc(bodyKyTruoc);

        return baoCaoTaiChinhGetForm;

    }


}
