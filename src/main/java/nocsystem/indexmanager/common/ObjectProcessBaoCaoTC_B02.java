package nocsystem.indexmanager.common;

import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;

public class ObjectProcessBaoCaoTC_B02 {

    public static String convertThangNam(Integer[] listThangCuaQuy, String nam){
        if(listThangCuaQuy == null || listThangCuaQuy.length == 0) return null;
        StringBuilder thangCuaQuy = new StringBuilder();
        int k = 0;
        int len = listThangCuaQuy.length;
        for(Integer i : listThangCuaQuy){
            String s = "'" +i + nam +"'";
            thangCuaQuy.append(s);
            if(k < len-1){
                thangCuaQuy.append(" , ");
            }
            k++;
        }
        return thangCuaQuy.toString();
    }

    public static Integer[] getThangTrongQuy( String thang){
        if(thang == null || thang.isEmpty()) return null;
        int thangTmp = Integer.parseInt(thang);
        Integer[] listThang = new Integer[3];
        int thangDau;
        int thangGiua;
        int thangCuoi;
        if(thangTmp >= 1 && thangTmp <= 3){
            thangDau = 1;
            thangGiua = 2;
            thangCuoi = 3;
        }
        else if(thangTmp >= 4 && thangTmp <= 6){
            thangDau = 4;
            thangGiua = 5;
            thangCuoi = 6;
        }

        else if(thangTmp >= 7 && thangTmp <= 9){
            thangDau = 7;
            thangGiua = 8;
            thangCuoi = 9;
        }
        else {
            thangDau = 10;
            thangGiua = 11;
            thangCuoi = 12;
        }
        listThang[0] = thangDau;
        listThang[1] = thangGiua;
        listThang[2] = thangCuoi;
        return listThang;
    }

}
