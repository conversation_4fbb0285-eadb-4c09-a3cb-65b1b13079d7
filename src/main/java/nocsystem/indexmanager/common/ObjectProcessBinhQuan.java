package nocsystem.indexmanager.common;

import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;

public class ObjectProcessBinhQuan {
    public static BaoCaoTaiChinhBody[] binhQuandauKy_CuoiKyThang(BaoCaoTaiChinhBody body){
        String thang = body.filterThang;
        String nam = body.filterNam;
        BaoCaoTaiChinhBody objDK = new BaoCaoTaiChinhBody();
        BaoCaoTaiChinhBody objCK = new BaoCaoTaiChinhBody();
        if(thang.equals("1")){
            objDK.setLoaiBaoCao(body.loaiBaoCao);
            objDK.setFilterThang("12");
            objDK.setFilterNam(String.valueOf(Integer.parseInt(nam) - 1));

            objCK = body;
        }
        else {
            objDK = body;

            objCK.setLoaiBaoCao(body.loaiBaoCao);
            objCK.setFilterThang(String.valueOf(Integer.parseInt(thang)-1));
            objCK.setFilterNam(nam);


        }
        BaoCaoTaiChinhBody[] obj = new BaoCaoTaiChinhBody[2];
        obj[0] = objDK;
        obj[1] = objCK;
        return obj;
    }


    public static BaoCaoTaiChinhBody[] binhQuanDauKy_CuoiKyQuy(BaoCaoTaiChinhBody body){
        String nam = body.filterNam;
        String thang =body.filterThang;

        int thangTmp = Integer.parseInt(thang);
        BaoCaoTaiChinhBody objDK = new BaoCaoTaiChinhBody();
        BaoCaoTaiChinhBody objCK = new BaoCaoTaiChinhBody();
        objDK.setLoaiBaoCao(body.loaiBaoCao);
        objCK.setLoaiBaoCao(body.loaiBaoCao);
        if(thangTmp >= 1 && thangTmp <= 3){
            objDK.setFilterThang("12");
            objDK.setFilterNam( (Integer.parseInt(nam) - 1) + "");
            objCK.setFilterThang("3");
            objCK.setFilterNam(nam);
        }
        else if(thangTmp >= 4 && thangTmp <= 6){
            objDK.setFilterThang("3");
            objDK.setFilterNam(nam);
            objCK.setFilterThang("6");
            objCK.setFilterNam(nam);
        } else if (thangTmp >= 7 && thangTmp <= 9) {
            objDK.setFilterThang("6");
            objDK.setFilterNam(nam);
            objCK.setFilterThang("9");
            objCK.setFilterNam(nam);
        } else {
            objDK.setFilterThang("9");
            objDK.setFilterNam(nam);
            objCK.setFilterThang("12");
            objCK.setFilterNam(nam);
        }
        BaoCaoTaiChinhBody[] obj = new BaoCaoTaiChinhBody[2];
        obj[0] = objDK;
        obj[1] = objCK;
        return obj;
    }

    public static BaoCaoTaiChinhBody[] binhQuan4QuyNam(BaoCaoTaiChinhBody body){
        String nam = body.filterNam;

        BaoCaoTaiChinhBody objQuy1 = new BaoCaoTaiChinhBody();
        BaoCaoTaiChinhBody objQuy2 = new BaoCaoTaiChinhBody();
        BaoCaoTaiChinhBody objQuy3 = new BaoCaoTaiChinhBody();
        BaoCaoTaiChinhBody objQuy4 = new BaoCaoTaiChinhBody();

        objQuy1.setLoaiBaoCao(body.loaiBaoCao);
        objQuy2.setLoaiBaoCao(body.loaiBaoCao);
        objQuy3.setLoaiBaoCao(body.loaiBaoCao);
        objQuy4.setLoaiBaoCao(body.loaiBaoCao);

        objQuy1.setFilterNam(nam);
        objQuy2.setFilterNam(nam);
        objQuy3.setFilterNam(nam);
        objQuy4.setFilterNam(nam);

        objQuy1.setFilterThang("3");
        objQuy2.setFilterThang("6");
        objQuy3.setFilterThang("9");
        objQuy4.setFilterThang("12");

        BaoCaoTaiChinhBody[] obj = new BaoCaoTaiChinhBody[4];
        obj[0] = objQuy1;
        obj[1] = objQuy2;
        obj[2] = objQuy3;
        obj[3] = objQuy4;
        return obj;
    }


}
