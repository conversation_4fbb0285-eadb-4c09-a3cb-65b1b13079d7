package nocsystem.indexmanager.common;

import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class DateTimeUtil {

    public static LocalDate getToday() {
        return LocalDate.now();
    }

    public static LocalDate getPreviousMonthSameDay(LocalDate date) {
        // Lấy ngày của tháng trước
        LocalDate previousMonth = date.minusMonths(1);

        // Tr<PERSON> về cùng ngày của tháng trước
        int dayOfMonth = date.getDayOfMonth();
        int lastDayOfPreviousMonth = previousMonth.lengthOfMonth();
        int previousDayOfMonth = Math.min(dayOfMonth, lastDayOfPreviousMonth);

        return LocalDate.of(previousMonth.getYear(), previousMonth.getMonth(), previousDayOfMonth);
    }

    public static long getTimeNow(){
        Instant now = Instant.now();

        LocalDateTime localDateTime = LocalDateTime.ofInstant(now, ZoneId.of("UTC+7"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        String formattedDateTime = localDateTime.format(formatter);
        LocalDateTime localDateTime1 = LocalDateTime.parse(formattedDateTime, formatter);

        return localDateTime1.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Date stringToSqlDate(String dateString) {
        if(dateString == null || dateString.isEmpty()) return null;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        try {
            java.util.Date utilDate = formatter.parse(dateString);
            return new Date(utilDate.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }


}
