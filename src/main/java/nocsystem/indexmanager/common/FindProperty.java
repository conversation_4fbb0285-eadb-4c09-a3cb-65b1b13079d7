package nocsystem.indexmanager.common;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class FindProperty<T> {

    private Class<T> objectType;

    public FindProperty(Class<T> objectType) {
        this.objectType = objectType;
    }

    public Method getProperty(String propertyName) throws NoSuchMethodException {
        String methodName = "get" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
        return objectType.getMethod(methodName);
    }

    /**
     *
     * @param propertyName
     * @param method
     * @param l : list data muốn lấy
     * @param i : chỉ số i của list data
     * @return
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */

    public Object getDataPropertyInList(String propertyName, Method method, List<T> l, int i) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        return getProperty(propertyName).invoke(l.get(i));
    }

    public Object getDataPropertyInObj(String propertyName, Method method, T l) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        return getProperty(propertyName).invoke(l);
    }

    public Class<T> getObjectType() {
        return objectType;
    }

    public void setObjectType(Class<T> objectType) {
        this.objectType = objectType;
    }


    public List<T> getDataSort(List<T> l, String propertySort, String typeSort, Class<T> objectType) throws NoSuchMethodException {
        if (l.isEmpty()) {
            return new ArrayList<>();
        }

        FindProperty<T> findProperty = new FindProperty<>(objectType);

        Comparator<T> comparator = null;
        if("desc".equalsIgnoreCase(typeSort)){
            comparator = Comparator.comparing(t -> {
                try {
                    return (Comparable) (findProperty.getProperty(propertySort)).invoke(t);
                } catch (Exception e) {
                    throw new RuntimeException("lỗi tên thuộc tính không đúng", e);
                }
            },Comparator.nullsLast(Comparator.reverseOrder()));
        }
        else if("asc".equalsIgnoreCase(typeSort)){
            comparator = Comparator.comparing(t -> {
                try {
                    return (Comparable) findProperty.getProperty(propertySort).invoke(t);
                } catch (Exception e) {
                    throw new RuntimeException("lỗi tên thuộc tính không đúng", e);
                }
            },Comparator.nullsLast(Comparator.naturalOrder()));
        }
        else {
            return l;
        }
        return l.stream()
                .sorted(Comparator.nullsLast(comparator))
                .collect(Collectors.toList());
    }



    /** cách sử dụng
     *
     *
     * c1 :
     *         // đối với list  ================================
     *         List<SinhVien> lsv = new ArrayList<>();
     *         SinhVien sv = new SinhVien("ma1");
     *         SinhVien sv1 = new SinhVien("ma12");
     *
     *         lsv.add(sv);
     *         lsv.add(sv1);
     *
     *         LRUCache<SinhVien> cache = new LRUCache<>(SinhVien.class);
     *         Method myMethod = cache.getProperty("ma");
     *         System.out.println(cache.getDataProperty("ma", lsv, 3));
     *
     *
     *         // đối với obj  =====================================
     *         List<SinhVien> lsv = new ArrayList<>();
     *         SinhVien sv = new SinhVien("ma1");
     *         SinhVien sv1 = new SinhVien("ma12");
     *
     *         lsv.add(sv);
     *         lsv.add(sv1);
     *
     *         LRUCache<SinhVien> cache = new LRUCache<>(SinhVien.class);
     *         Method myMethod = cache.getProperty("ma");
     *         System.out.println(cache.getDataPropertyInObj("ma", myMethod, sv));
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *
     *c2:
     *
     *          List<SinhVien> lsv = new ArrayList<>();
     *         List<HocSinh> lhs = new ArrayList<>();
     *         SinhVien sv3 = new SinhVien("1");
     *         SinhVien sv = new SinhVien(null);m
     *         SinhVien sv1 = new SinhVien("2");
     *
     *         HocSinh hs1 = new HocSinh("4");
     *         HocSinh hs2 = new HocSinh(null);
     *         HocSinh hs3 = new HocSinh("5");
     *
     *         lsv.add(sv3);
     *         lsv.add(sv);
     *         lsv.add(sv1);
     *
     *         lhs.add(hs1);
     *         lhs.add(hs2);
     *         lhs.add(hs3);
     *         FindProperty<SinhVien> cache = new FindProperty<>(SinhVien.class);
     *         FindProperty<HocSinh> cache1 = new FindProperty<>(HocSinh.class);
     *         List<SinhVien> sortedList = cache.getDataSort(lsv, "ma", "desc");
     *         List<HocSinh> sortedListHs = cache1.getDataSort(lhs, "maHs", "desc");
     *         System.out.println(sortedList.toString());
     *         System.out.println(sortedListHs.toString());
     *
     *
     *         c3 :
     *           FindProperty<KpiPhatLayerDungGioResponse> cache = new FindProperty<>(KpiPhatLayerDungGioResponse.class);
     *           DashboardLmNewServiceImpl<KpiPhatLayerDungGioResponse> d = new DashboardLmNewServiceImpl<>(); // class chủ
     *           l = d.getDataSort(l, sortBy, typeSort, cache);
     *
     *
     *         public List<T> getDataSort(List<T> l, String propertySort, String typeSort, FindProperty<T> findProperty) throws NoSuchMethodException {
     *         if(l.isEmpty()){;
     *             return new ArrayList<>();
     *         }
     *         return findProperty.getDataSort(l, propertySort, typeSort, findProperty.getObjectType());
     *     }
     *
     *
     */






}


