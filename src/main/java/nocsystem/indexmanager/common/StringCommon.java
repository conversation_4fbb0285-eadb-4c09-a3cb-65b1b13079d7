package nocsystem.indexmanager.common;

import org.springframework.util.StringUtils;

import java.text.Normalizer;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class StringCommon {
    public static String convertListToStringForQueryIn(List<String> list_str) {
        if (list_str == null || list_str.isEmpty()) {
            return null;
        }
        List<String> list_tmp = new ArrayList<>();
        for (String element : list_str) {
            if (!(element == null || element.isEmpty())) {
                list_tmp.add(element.trim());
            }
        }
        String str = "";
        int length = list_tmp.size() - 1;
        for (int i = 0; i < length; i++) {
            str += "'" + list_tmp.get(i) + "'" + ", ";
        }
        str += "'" + list_tmp.get(length) + "'";
        return str;
    }


    public static String convertVersion(Long version){
        if(version == null){
            return  " version is null ";
        }
        else {
            return " version = "+ version;
        }
    }

    public static String convertToSnakeCase(String input) {
        if(input == null) return "bao_cao";
        input = input.trim();
        // Loại bỏ dấu tiếng Việt
        String normalized = Normalizer.normalize(input, Normalizer.Form.NFD);
        String noAccent = normalized.replaceAll("\\p{M}", "");

        // Chuyển đổi thành chữ thường và thay khoảng trắng bằng dấu gạch dưới
        String snakeCase = noAccent.toLowerCase().replaceAll(" ", "_");

        // Loại bỏ các ký tự không phải là chữ cái và dấu gạch dưới
        return snakeCase.replaceAll("[^a-z0-9_]", "");
    }


    public static String getDateFromSheet(String date){
        if(StringUtils.isEmpty(date)){
            return null;
        }
        Pattern pattern = Pattern.compile("(\\d{2}/\\d{2}/\\d{4})");
        Matcher matcher = pattern.matcher(date);
        return matcher.find() ? matcher.group(1) : null;
    }

    public static int convertDateStringToInt(String dateStr) {
        if(StringUtils.isEmpty(dateStr)){
            return 0;
        }
        try {
            int thang = Integer.parseInt(dateStr.substring(dateStr.indexOf('/') + 1).replace("/", ""));
            return thang;
        }catch (Exception e){
            return -1;
        }
    }

    public static String processString(String input) {
        if(StringUtils.isEmpty(input)){
            return null;
        }
        if (input.matches("\\d+\\.0$")) {
            return input.substring(0, input.length() - 2);
        }
        return input;
    }

    public static String convertListToString(List<String> list) {
        if(list == null || list.isEmpty()){
            return "'#@!'";
        }
        return "'" + String.join("', '", list) + "'";
    }

    public static String normalizeString(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return input.trim().replaceAll("\\s+", " ");
    }


}
