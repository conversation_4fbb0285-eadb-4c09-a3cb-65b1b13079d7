package nocsystem.indexmanager.common;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class CalculateCommon {

    public static Float sub2Nums(Float num1, Float num2) {
        if(num1 == null) num1 = 0f;
        if(num2 == null) num2 = 0f;
        return num1 - num2;
    }

    public static Float sum2Nums(Float num1, Float num2) {
        if(num1 == null) num1 = 0f;
        if(num2 == null) num2 = 0f;
        return num1 + num2;
    }

    public static Float sum3Nums(Float num1, Float num2, Float num3) {
        if(num1 == null) num1 = 0f;
        if(num2 == null) num2 = 0f;
        if(num3 == null) num3 = 0f;
        return num1 + num2 + num3;
    }

    public static Float div2Nums(Float num1, Float num2) {
        if(num1 == null) return null;
        if(num2 == null || num2 == 0) return null;
        return 100 * num1 / num2;
    }


    public static Float div2NumsNoPercent(Float num1, Float num2) {
        if(num1 == null) return null;
        if(num2 == null || num2 == 0) return null;
        return num1 / num2;
    }


    public static Float roundFloat(Float number) {
//        if (number == null) return null; // Xử lý trường hợp null
        if (number == null) return 0F; // Xử lý trường hợp null
        BigDecimal bd = new BigDecimal(Float.toString(number));
        bd = bd.setScale(2, RoundingMode.HALF_UP); // Làm tròn 2 chữ số sau dấu thập phân
        return bd.floatValue();
    }






}
