package nocsystem.indexmanager.common;


import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoChartTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;


public class BodyCustom {

    public BaoCaoTaiChinhBody getBodyNamN1(BaoCaoTaiChinhBody body, String type){
        BaoCaoTaiChinhBody bodyN1 = new BaoCaoTaiChinhBody();
        bodyN1.loaiBaoCao = body.getLoaiBaoCao();
        if(type.equals("thang")){
            bodyN1.filterThang = body.filterThang;
            bodyN1.filterNam = String.valueOf(Integer.parseInt(body.filterNam) - 1);
        }
        else if(type.equals("quy")){

            int thangTmp = Integer.parseInt(body.filterThang);
            if(thangTmp >= 1 && thangTmp <= 3){
                bodyN1.setFilterThang("3");
                bodyN1.setFilterNam(String.valueOf(Integer.parseInt(body.filterNam)-1));
            }
            else if(thangTmp>= 4 && thangTmp <= 6){
                bodyN1.setFilterThang("6");
                bodyN1.setFilterNam(String.valueOf(Integer.parseInt(body.filterNam)-1));
            } else if (thangTmp>= 7 && thangTmp <= 9) {
                bodyN1.setFilterThang("9");
                bodyN1.setFilterNam(String.valueOf(Integer.parseInt(body.filterNam)-1));
            }
            else {
                bodyN1.setFilterThang("12");
                bodyN1.setFilterNam(String.valueOf(Integer.parseInt(body.filterNam)-1));
            }
        }
        else {
            bodyN1.setFilterThang("12");
            bodyN1.setFilterNam(String.valueOf(Integer.parseInt(body.filterNam)-1));
        }
        return bodyN1;
    }

    public BaoCaoTaiChinhBody getBodyKyTruoc(BaoCaoTaiChinhBody body, String type){
        BaoCaoTaiChinhBody bodyN1 = new BaoCaoTaiChinhBody();
        bodyN1.loaiBaoCao = body.getLoaiBaoCao();

        if(type.equals("thang")){
            if(body.filterThang.equals("1")){
                bodyN1.filterThang = "12";
                bodyN1.filterNam = String.valueOf(Integer.parseInt(body.filterNam) - 1);
            }
            else {
                bodyN1.filterThang = String.valueOf(Integer.parseInt(body.filterThang) - 1);
                bodyN1.filterNam = body.filterNam;
            }
        }
        else if(type.equals("quy")){
            int thangTmp = Integer.parseInt(body.filterThang);
            if(thangTmp >= 1 && thangTmp <= 3){
                bodyN1.setFilterThang("12");
                bodyN1.setFilterNam(String.valueOf(Integer.parseInt(body.filterNam)-1));
            }
            else if(thangTmp>= 4 && thangTmp <= 6){
                bodyN1.setFilterThang("3");
                bodyN1.setFilterNam(body.filterNam);
            } else if (thangTmp>= 7 && thangTmp <= 9) {
                bodyN1.setFilterThang("6");
                bodyN1.setFilterNam(body.filterNam);
            }
            else {
                bodyN1.setFilterThang("9");
                bodyN1.setFilterNam(body.filterNam);
            }
        }
        else {
            bodyN1.setFilterThang("12");
            bodyN1.setFilterNam(String.valueOf(Integer.parseInt(body.filterNam) - 1));
        }

        return bodyN1;
    }


    public BaoCaoTaiChinhBody getBodyQuy(BaoCaoTaiChinhBody body){
        BaoCaoTaiChinhBody bodyQuy = new BaoCaoTaiChinhBody();
        bodyQuy.setLoaiBaoCao(body.loaiBaoCao);
//        String thangCuoiQuy = DateTimeCommon.getQuarterMonths(body.filterThang+body.filterNam)[1];
        String thangCuoiQuy = DateTimeCommon.getQuarterMonths(body.filterThang, body.filterNam)[1];
        bodyQuy.setFilterThang(thangCuoiQuy.substring(0, thangCuoiQuy.length()-4));
        bodyQuy.setFilterNam(thangCuoiQuy.substring(thangCuoiQuy.length()-4));
        return bodyQuy;
    }

    public BaoCaoTaiChinhBody getBodyNam(BaoCaoTaiChinhBody body){
        BaoCaoTaiChinhBody bodyNam = new BaoCaoTaiChinhBody();
        bodyNam.setLoaiBaoCao(body.loaiBaoCao);
        bodyNam.setFilterThang("12");
        bodyNam.setFilterNam(body.filterNam);
        return bodyNam;
    }


    public String getQuyHienTaiChoBangKeHoach(String thang){
        int thangTmp = Integer.parseInt(thang);
        if(thangTmp >= 1 && thangTmp <= 3){
            return "1" ;
        }
        else if(thangTmp >= 4 && thangTmp <= 6){
            return "2";
        }
        else if(thangTmp >= 7 && thangTmp <= 9){
            return "3" ;
        }
        else {
            return "4" ;
        }
    }


    // todo cho chart, code hoi dai nhung tuong minh :))), thich thi luc nao che function lai

    public  BaoCaoTaiChinhBody[] getBodyChart(BaoCaoChartTaiChinhBody body){

        String thang = body.filterThang;;
        String nam = body.filterNam;
        String loaiBaoCao = body.loaiBaoCao;
        String type = body.type;

        if(type.equals("thang")){
            int len =  Integer.parseInt(thang) ;
            BaoCaoTaiChinhBody[] listBoDy = new BaoCaoTaiChinhBody[len];
            for(int i = 0 ; i < len; i++){
                BaoCaoTaiChinhBody bodyTmp = new BaoCaoTaiChinhBody();
                bodyTmp.setLoaiBaoCao(loaiBaoCao);
                bodyTmp.setFilterNam(nam);
                bodyTmp.setFilterThang((i+1) + "");
                listBoDy[i] = bodyTmp;
            }
            return listBoDy;
        }
        else if(type.equals("quy")){
            int thangTmp = Integer.parseInt(thang);
            if(thangTmp >= 1 && thangTmp <= 3){
                BaoCaoTaiChinhBody[] listBoDy = new BaoCaoTaiChinhBody[1];

                BaoCaoTaiChinhBody bodyTmp = new BaoCaoTaiChinhBody();
                bodyTmp.setLoaiBaoCao(loaiBaoCao);
                bodyTmp.setFilterNam(nam);
                bodyTmp.setFilterThang("3");
                listBoDy[0] = bodyTmp;
                return listBoDy;
            }
            else if(thangTmp >= 4 && thangTmp <= 6){
                BaoCaoTaiChinhBody[] listBoDy = new BaoCaoTaiChinhBody[2];
                int thangCuoiQuy = 3;
                for(int i = 0 ; i < 2 ; i++){
                    BaoCaoTaiChinhBody bodyTmp = new BaoCaoTaiChinhBody();
                    bodyTmp.setLoaiBaoCao(loaiBaoCao);
                    bodyTmp.setFilterNam(nam);
                    bodyTmp.setFilterThang(thangCuoiQuy + "");
                    thangCuoiQuy += 3;

                    listBoDy[i] = bodyTmp;
                }
                return listBoDy;
            }

            else if(thangTmp >= 7 && thangTmp <= 9){
                BaoCaoTaiChinhBody[] listBoDy = new BaoCaoTaiChinhBody[3];
                int thangCuoiQuy = 3;
                for(int i = 0 ; i < 3 ; i++){
                    BaoCaoTaiChinhBody bodyTmp = new BaoCaoTaiChinhBody();
                    bodyTmp.setLoaiBaoCao(loaiBaoCao);
                    bodyTmp.setFilterNam(nam);
                    bodyTmp.setFilterThang(thangCuoiQuy + "");
                    thangCuoiQuy += 3;

                    listBoDy[i] = bodyTmp;
                }
                return listBoDy;
            }
            else {
                BaoCaoTaiChinhBody[] listBoDy = new BaoCaoTaiChinhBody[4];
                int thangCuoiQuy = 3;
                for(int i = 0 ; i < 4 ; i++){
                    BaoCaoTaiChinhBody bodyTmp = new BaoCaoTaiChinhBody();
                    bodyTmp.setLoaiBaoCao(loaiBaoCao);
                    bodyTmp.setFilterNam(nam);
                    bodyTmp.setFilterThang(thangCuoiQuy + "");
                    thangCuoiQuy += 3;

                    listBoDy[i] = bodyTmp;
                }
                return listBoDy;
            }

        }
        else {
            BaoCaoTaiChinhBody[] listBoDy = new BaoCaoTaiChinhBody[5];
            int namTmp = Integer.parseInt(nam) - 4;
            for(int i = 0; i <= 4; i++){
                BaoCaoTaiChinhBody bodyTmp = new BaoCaoTaiChinhBody();
                bodyTmp.setLoaiBaoCao(loaiBaoCao);
                bodyTmp.setFilterNam(namTmp + "");
                namTmp ++;
                bodyTmp.setFilterThang("12");
                listBoDy[i] = bodyTmp;
            }
            return listBoDy;
        }
    }
//
//    public static void main(String[] args) {
//        BaoCaoChartTaiChinhBody body = new BaoCaoChartTaiChinhBody();
//        body.setFilterNam("2025");
//        body.setFilterThang("2");
//        body.setLoaiBaoCao("cty_me");
//        body.setStt("1");
//        body.setType("nam");
//        BaoCaoTaiChinhBody[] listBoDy  = getBodyNam(body);
//        for(BaoCaoTaiChinhBody o : listBoDy){
//            System.out.println(o.toString());
//        }
//
//    }

}
