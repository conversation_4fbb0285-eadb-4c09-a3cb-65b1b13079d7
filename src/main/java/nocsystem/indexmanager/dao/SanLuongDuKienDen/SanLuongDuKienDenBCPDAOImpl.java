package nocsystem.indexmanager.dao.SanLuongDuKienDen;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.SanLuongDuKienDen.SanLuongDuKienDenBCPResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static nocsystem.indexmanager.util.StringUtil.castListToQueryParam;

@Service
public class SanLuongDuKienDenBCPDAOImpl extends AbstractDao implements SanLuongDuKienDenBCPDAO {

    @Override
    public List<SanLuongDuKienDenBCPResponse> getBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanhSso, List<String> buuCucSso, List<String> chiNhanh, List<String> buuCuc, String orderBy, String sort, Integer pageNumber, Integer pageSize, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sql = sqlQueryBaoCaoSL(ngayBaoCao, chiNhanhSso, buuCucSso, chiNhanh, buuCuc, orderBy, sort, hangDacThu, khDacThuGui, khDacThuNhan) +
                " limit " + pageSize + " offset " + pageNumber * pageSize;

        System.out.println("sql............." + sql);

        List<SanLuongDuKienDenBCPResponse> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SanLuongDuKienDenBCPResponse data = new SanLuongDuKienDenBCPResponse();
                data.setChiNhanh(rs.getString("macn"));
                if ((chiNhanh != null && !chiNhanh.isEmpty()) || (chiNhanhSso != null && !chiNhanhSso.isEmpty())) {
                    data.setBuuCuc(rs.getString("mabuucuc"));
                }
                data.setTongTrongLuong(rs.getLong("trongluong"));
                data.setCaSangNgayN(rs.getLong("casangn"));
                data.setCaChieuNgayN(rs.getLong("cachieun"));
                data.setCaToiNgayN(rs.getLong("catoin"));
                data.setTongSLNgayN(rs.getLong("tongsln"));
                data.setCaSangNgayN1(rs.getLong("casangn1"));
                data.setCaChieuNgayN1(rs.getLong("cachieun1"));
                data.setCaToiNgayN1(rs.getLong("catoin1"));
                data.setTongSLNgayN1(rs.getLong("tongsln1"));
                data.setCaSangNgayN2(rs.getLong("casangn2"));
                data.setCaChieuNgayN2(rs.getLong("cachieun2"));
                data.setCaToiNgayN2(rs.getLong("catoin2"));
                data.setTongSLNgayN2(rs.getLong("tongsln2"));
                data.setTongSLDuKien(rs.getLong("tongsldukien"));
                data.setThoiGianCapNhat(rs.getLong("updatedat"));
                detailList.add(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public Long getTotalRecordBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanhSso, List<String> buuCucSso, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sql = "select count(*) as count from (" + sqlQueryBaoCaoSL(ngayBaoCao, chiNhanhSso, buuCucSso, chiNhanh, buuCuc, null, null, hangDacThu, khDacThuGui, khDacThuNhan) + ")";

        Long totalRecord = 0L;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                totalRecord = rs.getLong("count");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return totalRecord;
    }

    @Override
    public List<SanLuongDuKienDenBCPResponse> getTotalBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanhSso, List<String> buuCucSso, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sql = sqlQueryBaoCaoSL(ngayBaoCao, chiNhanhSso, buuCucSso, chiNhanh, buuCuc, null, null, hangDacThu, khDacThuGui, khDacThuNhan);

        System.out.println("sql............." + sql);
        List<SanLuongDuKienDenBCPResponse> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                SanLuongDuKienDenBCPResponse data = new SanLuongDuKienDenBCPResponse();
                data.setTongTrongLuong(rs.getLong("trongluong"));
                data.setCaSangNgayN(rs.getLong("casangn"));
                data.setCaChieuNgayN(rs.getLong("cachieun"));
                data.setCaToiNgayN(rs.getLong("catoin"));
                data.setTongSLNgayN(rs.getLong("tongsln"));
                data.setCaSangNgayN1(rs.getLong("casangn1"));
                data.setCaChieuNgayN1(rs.getLong("cachieun1"));
                data.setCaToiNgayN1(rs.getLong("catoin1"));
                data.setTongSLNgayN1(rs.getLong("tongsln1"));
                data.setCaSangNgayN2(rs.getLong("casangn2"));
                data.setCaChieuNgayN2(rs.getLong("cachieun2"));
                data.setCaToiNgayN2(rs.getLong("catoin2"));
                data.setTongSLNgayN2(rs.getLong("tongsln2"));
                data.setTongSLDuKien(rs.getLong("tongsldukien"));
                data.setThoiGianCapNhat(rs.getLong("updatedat"));
                detailList.add(data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    private String sqlQueryBaoCaoSL(LocalDate ngayBaoCao, List<String> chiNhanhSso, List<String> buuCucSso, List<String> chiNhanh, List<String> buuCuc, String orderBy, String sort, String hangDacThu, String khDacThuGui, String khDacThuNhan) {
        String chiNhanhString = "";
        String buuCucString = "";
        String chiNhanhSsoString = "";
        String buuCucSsoString = "";
        if (chiNhanh != null && !chiNhanh.isEmpty()) {
            chiNhanhString = castListToQueryParam(chiNhanh);
        }
        if (buuCuc != null && !buuCuc.isEmpty()) {
            buuCucString = castListToQueryParam(buuCuc);
        }
        if (chiNhanhSso != null && !chiNhanhSso.isEmpty()) {
            chiNhanhSsoString = castListToQueryParam(chiNhanhSso);
        }
        if (buuCucSso != null && !buuCucSso.isEmpty()) {
            buuCucSsoString = castListToQueryParam(buuCucSso);
        }
        String filterDate = "ngay_baocao = date '" + ngayBaoCao + "'";
        String filterChiNhanh = chiNhanhString.equals("") ? "" : " and ma_cn in " + chiNhanhString;
        String filterBuuCuc = buuCucString.equals("") ? "" : " and ma_buucuc in " + buuCucString;
        String filterChiNhanhSso = chiNhanhSsoString.equals("") ? "" : " and ma_cn in " + chiNhanhSsoString;
        String filterBuuCucSso = buuCucSsoString.equals("") ? "" : " and ma_buucuc in " + buuCucSsoString;
        String filterHangDacThu = ObjectUtils.isNotEmpty(hangDacThu) ? " and hang_dac_thu = '" + hangDacThu.toUpperCase() + "'" : "";
        String filterKhDacThuGui = ObjectUtils.isNotEmpty(khDacThuGui) ? " and kh_dacthu_gui = '" + khDacThuGui + "'" : "";
        String filterKhDacThuNhan = ObjectUtils.isNotEmpty(khDacThuNhan) ? " and kh_dacthu_nhan = '" + khDacThuNhan + "'" : "";
//        String filterOrderBy = orderBy == null ? "" : " order by " + orderBy;
//        String filterSort = sort == null ? "" : " " + sort;
        String filterOrderBy = "";
        String filterSort = "";
        if (sort != null) {
            filterSort = " " + sort;
            if (orderBy != null) {
                filterOrderBy = " order by " + orderBy.toLowerCase(Locale.ROOT).replace("_", "");
            }
        }

        String sql;
        if ((chiNhanh != null && !chiNhanh.isEmpty()) || (chiNhanhSso != null && !chiNhanhSso.isEmpty())) {
            sql = "select ma_cn as macn, ma_buucuc as mabuucuc, sum(trong_luong) as trongluong, " +
                    "sum(ca_sang_n) as casangn, sum(ca_chieu_n) as cachieun, sum(ca_toi_n) as catoin, " +
                    "sum(ca_sang_n + ca_chieu_n + ca_toi_n) as tongsln, " +
                    "sum(ca_sang_n1) as casangn1, sum(ca_chieu_n1) cachieun1, sum(ca_toi_n1) as catoin1, " +
                    "sum(ca_sang_n1 + ca_chieu_n1 + ca_toi_n1) as tongsln1, " +
                    "sum(ca_sang_n2) as casangn2, sum(ca_chieu_n2) as cachieun2, sum(ca_toi_n2) as catoin2, " +
                    "sum(ca_sang_n2 + ca_chieu_n2 + ca_toi_n2) as tongsln2, " +
                    "sum(ca_sang_n + ca_chieu_n + ca_toi_n + ca_sang_n1 + ca_chieu_n1 + ca_toi_n1 + ca_sang_n2 + ca_chieu_n2 + ca_toi_n2) as tongsldukien, " +
                    "max(updated_at) as updatedat " +
                    "from bcvh_sanluong_dukien " +
                    "where " +
                    filterDate +
                    filterChiNhanh +
                    filterBuuCuc +
                    filterChiNhanhSso +
                    filterBuuCucSso +
                    filterHangDacThu +
                    filterKhDacThuGui +
                    filterKhDacThuNhan +
                    " group by ma_cn, ma_buucuc" +
                    filterOrderBy +
                    filterSort;
        } else {
            sql = "select ma_cn as macn, sum(trong_luong) as trongluong, " +
                    "sum(ca_sang_n) as casangn, sum(ca_chieu_n) as cachieun, sum(ca_toi_n) as catoin, " +
                    "sum(ca_sang_n + ca_chieu_n + ca_toi_n) as tongsln, " +
                    "sum(ca_sang_n1) as casangn1, sum(ca_chieu_n1) cachieun1, sum(ca_toi_n1) as catoin1, " +
                    "sum(ca_sang_n1 + ca_chieu_n1 + ca_toi_n1) as tongsln1, " +
                    "sum(ca_sang_n2) as casangn2, sum(ca_chieu_n2) as cachieun2, sum(ca_toi_n2) as catoin2, " +
                    "sum(ca_sang_n2 + ca_chieu_n2 + ca_toi_n2) as tongsln2, " +
                    "sum(ca_sang_n + ca_chieu_n + ca_toi_n + ca_sang_n1 + ca_chieu_n1 + ca_toi_n1 + ca_sang_n2 + ca_chieu_n2 + ca_toi_n2) as tongsldukien, " +
                    "max(updated_at) as updatedat " +
                    "from bcvh_sanluong_dukien " +
                    "where " +
                    filterDate +
                    filterChiNhanh +
                    filterChiNhanhSso +
                    filterBuuCucSso +
                    filterHangDacThu +
                    filterKhDacThuGui +
                    filterKhDacThuNhan +
                    " group by ma_cn" +
                    filterOrderBy +
                    filterSort;
        }


        return sql;
    }
}
