package nocsystem.indexmanager.dao.SanLuongDuKienDen;

import nocsystem.indexmanager.models.Response.SanLuongDuKienDen.SanLuongDuKienDenBCPResponse;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

public interface SanLuongDuKienDenBCPDAO {
    List<SanLuongDuKienDenBCPResponse> getBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanhSso, List<String> buuCucSso, List<String> chiNhanh, List<String> buuCuc, String orderBy, String sort, Integer pageNumber, Integer pageSize, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException;
    Long getTotalRecordBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanhSso, List<String> buuCucSso, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException;
    List<SanLuongDuKienDenBCPResponse> getTotalBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanhSso, List<String> buuCucSso, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException;

}
