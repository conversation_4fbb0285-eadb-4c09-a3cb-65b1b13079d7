package nocsystem.indexmanager.dao.sanLuongCamKet;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.sanLuongCamKet.SanLuong;
import nocsystem.indexmanager.models.Response.sanLuongCamKet.Tyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import static java.time.temporal.TemporalAdjusters.lastDayOfMonth;
import static nocsystem.indexmanager.util.StringUtils.list2StringQuery;
import static nocsystem.indexmanager.util.TimeUtils.localDateToString;
import static nocsystem.indexmanager.util.TimeUtils.timestampToString;

@Service
public class SanLuongCamKetDAOImpl extends AbstractDao implements SanLuongCamKetDAO {
    private static final Logger logger = LoggerFactory.getLogger(SanLuongCamKetDAOImpl.class);

    @Override
    public List<SanLuong> getSanLuong(LocalDate ngayBaoCao, String vung, String chiNhanh, List<String> chiNhanhSSO, String vungCon, String buuCuc, List<String> buuCucSSO, String cusid, List<String> dichVu) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getMaxVersion(ngayBaoCao, "san_luong_cam_ket");

        String filterVung = vung.equals("") ? "" : " and ma_vung = '" + vung + "'";
        String filterChiNhanh = chiNhanh.isEmpty() ? "" : " and ma_chinhanh = '" + chiNhanh + "'";
        String filterBuuCuc = buuCuc.isEmpty() ? "" : " and ma_buucuc = '" + buuCuc + "'";
        String filterChiNhanhSSO = chiNhanhSSO.isEmpty() ? "" : " and ma_chinhanh in " + list2StringQuery(chiNhanhSSO);
        String filterBuuCucSSO = buuCucSSO.isEmpty() ? "" : " and ma_buucuc in " + list2StringQuery(buuCucSSO);
        String filterVungCon = vungCon.equals("") ? "" : " and ma_vungcon = '" + vungCon + "'";
        String filterCusid = cusid.isEmpty() ? "" : " and cus_id like '" + cusid + "%'";
        String filterDichVu = (dichVu == null || dichVu.isEmpty()) ? "" : " and ma_dichvu in " + list2StringQuery(dichVu);

        String sql = "select " +
                " ma_chinhanh, cus_id, ma_khgui, ma_dichvu, san_luong_cam_ket, tong_sanluong_gui, tong_doanhthu " +
                " from san_luong_cam_ket_tong_hop " +
                " where ngay_baocao = '" + localDateToString(ngayBaoCao) + "'" +
                " and version = " + version
                + filterVung + filterChiNhanh + filterChiNhanhSSO + filterVungCon + filterBuuCuc + filterBuuCucSSO + filterCusid + filterDichVu;


        System.out.println(sql);
        logger.info("sql query " + sql);

        List<SanLuong> listRes = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            while (rs.next()) {
                SanLuong sanLuong = new SanLuong(
                        rs.getString("ma_chinhanh"),
                        rs.getString("cus_id"),
                        rs.getString("ma_khgui"),
                        rs.getString("ma_dichvu"),
                        rs.getString("san_luong_cam_ket"),
                        rs.getLong("tong_sanluong_gui"),
                        rs.getDouble("tong_doanhthu")
                );
                listRes.add(sanLuong);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error when getDetail in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listRes;
    }

    @Override
    public List<Tyle> getTyLe(LocalDate ngayBaoCao, String vung, String chiNhanh, List<String> chiNhanhSSO, String vungCon, String buuCuc, List<String> buuCucSSO, String cusid, List<String> dichVu) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getMaxVersion(ngayBaoCao, "san_luong_cam_ket");
        LocalDate lastDayOfMonth = ngayBaoCao.with(lastDayOfMonth());
        int daysRemain = (int) ChronoUnit.DAYS.between(ngayBaoCao, lastDayOfMonth);

        String filterVung = vung.equals("") ? "" : " and ma_vung = '" + vung + "'";
        String filterChiNhanh = chiNhanh.isEmpty() ? "" : " and ma_chinhanh = '" + chiNhanh + "'";
        String filterBuuCuc = buuCuc.isEmpty() ? "" : " and ma_buucuc = '" + buuCuc + "'";
        String filterChiNhanhSSO = chiNhanhSSO.isEmpty() ? "" : " and ma_chinhanh in " + list2StringQuery(chiNhanhSSO);
        String filterBuuCucSSO = buuCucSSO.isEmpty() ? "" : " and ma_buucuc in " + list2StringQuery(buuCucSSO);
        String filterVungCon = vungCon.equals("") ? "" : " and ma_vungcon = '" + vungCon + "'";
        String filterCusid = cusid.isEmpty() ? "" : " and cus_id like '" + cusid + "%'";
        String filterDichVu = (dichVu == null || dichVu.isEmpty()) ? "" : " and ma_dichvu in " + list2StringQuery(dichVu);

        String sql = "select " +
                " cus_id, ma_dichvu, san_luong_cam_ket, so_ngay_phat_sinh, thoi_gian_cap_nhat_bg, sum(tong_sanluong_gui) as tong_sanluong_gui " +
                " from san_luong_cam_ket_tong_hop " +
                " where ngay_baocao = '" + localDateToString(ngayBaoCao) + "'" +
                " and version = " + version
                + filterVung + filterChiNhanh + filterChiNhanhSSO + filterVungCon + filterBuuCuc + filterBuuCucSSO + filterCusid + filterDichVu +
                " group by cus_id, ma_dichvu, san_luong_cam_ket, so_ngay_phat_sinh, thoi_gian_cap_nhat_bg ";


        System.out.println(sql);
        logger.info("sql query " + sql);

        List<Tyle> listRes = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            while (rs.next()) {
                Tyle tyle = new Tyle(
                        rs.getString("cus_id"),
                        rs.getString("ma_dichvu"),
                        timestampToString(rs.getString("thoi_gian_cap_nhat_bg")),
                        (double) rs.getLong("tong_sanluong_gui") / Double.parseDouble(rs.getString("san_luong_cam_ket")) * 100,
                        ((double) rs.getLong("tong_sanluong_gui") * (double) daysRemain / (double) rs.getLong("so_ngay_phat_sinh") + (double) rs.getLong("tong_sanluong_gui")) / Double.parseDouble(rs.getString("san_luong_cam_ket")) * 100
                );
                listRes.add(tyle);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("Error when getDetail in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listRes;
    }

    private Long getMaxVersion(LocalDate ngayBaoCao, String loaiBC) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sql = "select max(version) as version from chiso_version where ngay_baocao = date '" + ngayBaoCao + "' and ma_chiso = '" + loaiBC + "'";
        System.out.println(sql);
        Long version = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            while (rs.next()) {
                version = rs.getLong("version");
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return version;
    }
}
