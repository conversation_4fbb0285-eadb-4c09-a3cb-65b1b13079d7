package nocsystem.indexmanager.dao.sanLuongCamKet;

import nocsystem.indexmanager.models.Response.sanLuongCamKet.SanLuong;
import nocsystem.indexmanager.models.Response.sanLuongCamKet.Tyle;

import java.time.LocalDate;
import java.util.List;

public interface SanLuongCamKetDAO {
    List<SanLuong> getSanLuong(LocalDate ngayBaoCao, String vung, String chiNhanh, List<String> chiNhanhSSO, String vungCon, String buuCuc, List<String> buuCucSSO, String cusid, List<String> dichVu);
    List<Tyle> getTyLe(LocalDate ngayBaoCao, String vung, String chiNhanh, List<String> chiNhanhSSO, String vungCon, String buuCuc, List<String> buuCucSSO, String cusid, List<String> dichVu);

}
