package nocsystem.indexmanager.dao.TonNhapMay;

import nocsystem.indexmanager.common.StringCommon;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TonNhapMayDAOImpl extends AbstractDao implements TonNhapMayDAO {
    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    public List<TonChuaKetNoiResponse> getListChuaNhapMay(LocalDate ngayBaoCao, String loaiDon, List<String> listCN, List<String> listBC, Integer dichVu, String maChiNhanh) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-bcg");
        String cnbc;
        String dichVuQuery = "";

        switch (dichVu) {
            case 0:
                dichVuQuery = " and dich_vu = 'VCK' ";
                break;
            case 1:
                dichVuQuery = " and dich_vu != 'VCK' ";
                break;
            default:
                break;
        }

        if (maChiNhanh.equals("")) {
            cnbc = "tinh_nhan";
        } else {
            cnbc = "tinh_nhan, MA_BUUCUC_GOC";
        }
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl, sum(sl_ntc) as sl_ntc, " +
                "sum(tong_kl) as tong_kl, sum(kl_ntc) as kl_ntc " +
                "FROM ALL_TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and tinh_nhan in " + list2String(listCN) + "" : "") +
                (!loaiDon.equals("") ? " and loai_don = '" + loaiDon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + list2String(listBC) + "" : "") +
                dichVuQuery +
                " group by " + cnbc +
                " order by tinh_nhan ";

        System.out.println(".................SQL: " + sql);

        List<TonChuaKetNoiResponse> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TonChuaKetNoiResponse detail = new TonChuaKetNoiResponse(
                        rs.getString("tinh_nhan"),
                        !maChiNhanh.equals("") ? rs.getString("MA_BUUCUC_GOC") : null,
                        rs.getLong("tong_sl"),
                        rs.getLong("sl_ntc"),
                        rs.getLong("tong_kl"),
                        rs.getLong("kl_ntc")
                );
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<DashTonHanhTrinhDto> getExcelChuaNhapMay(LocalDate ngayBaoCao, String loaiDon, List<String> listCN, List<String> listBC, Integer dichVu) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-bcg");
        String dichVuQuery = "";

        switch (dichVu) {
            case 0:
                dichVuQuery = " and dich_vu = 'VCK' ";
                break;
            case 1:
                dichVuQuery = " and dich_vu != 'VCK' ";
                break;
            default:
                break;
        }

        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + list2String(listCN) + "" : "") +
                (!loaiDon.equals("") ? " and loai_don = '" + loaiDon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + list2String(listBC) + "" : "") +
                dichVuQuery;

        System.out.println(".................SQL: " + sql);

        List<DashTonHanhTrinhDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                DashTonHanhTrinhDto detail = new DashTonHanhTrinhDto(
                        ngayBaoCao.toString(),
                        rs.getString("MA_PHIEUGUI"),
                        rs.getString("tinh_nhan"),
                        rs.getString("huyen_nhan"),
                        rs.getString("ten_huyen_nhan"),
                        rs.getString("tinh_phat"),
                        rs.getString("huyen_phat"),
                        rs.getString("ten_huyen_phat"),
                        rs.getString("dich_vu"),
                        rs.getString("ma_buucuc_goc"),
                        chuyenNgayTuLongThanhString(rs.getString("time_tac_dong")),
                        rs.getString("trang_thai"),
                        rs.getString("ma_buucuc_ht"),
                        rs.getString("chi_nhanh_ht"),
                        rs.getString("ma_doitac"),
                        rs.getString("ma_khgui"),
                        rs.getString("ma_buucuc_phat"),
                        rs.getString("tien_cod"),
                        rs.getString("nguoi_nhap_may"),
                        chuyenNgayTuLongThanhString(rs.getString("ngay_nhap_may")),
                        rs.getString("trong_luong"),
                        rs.getString("tien_cuoc"),
                        rs.getString("loai_hh"),
                        rs.getString("ma_dv_cong_them"),
                        chuyenNgayTuLongThanhString(rs.getString("time_ps"))
                        );
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public String getTimeTinhToan(LocalDate ngayBaoCao, String loaiDon, List<String> listCN, List<String> listBC, Integer dichVu) throws SQLException{
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-bcg");

        String dichVuQuery = "";

        switch (dichVu) {
            case 0:
                dichVuQuery = " and dich_vu = 'VCK' ";
                break;
            case 1:
                dichVuQuery = " and dich_vu != 'VCK' ";
                break;
            default:
                break;
        }
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT max(updated_at) as updated_at "+
                "FROM ALL_TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and tinh_nhan in " + list2String(listCN) + "" : "") +
                (!loaiDon.equals("") ? " and loai_don = '" + loaiDon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + list2String(listBC) + "" : "") +
                dichVuQuery;

        System.out.println(".................SQL: " + sql);

        String timeTinhToan = "";

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                timeTinhToan = chuyenNgayTuLongThanhString(rs.getString("updated_at"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return timeTinhToan;
    }

    @Override
    public List<TonNhapMayChuaKetNoiResponse> getBieuDoTonNhapMay(LocalDate ngayBaoCao, LocalDate ngayBatDau, String chiNhanh, String buuCuc) throws SQLException{
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
//        Long version = getVersionMax(ngayBaoCao, "ton-bcg");

        Map<Date, Long> mapVersion = getMultipleVersion(ngayBaoCao, ngayBatDau, "ton-bcg");

        List<Long> listVersion = new ArrayList<>();

        if(!mapVersion.isEmpty())
            listVersion = new ArrayList<>(mapVersion.values());
        else
            listVersion = List.of(0L);

        String sql = "";

        int count  = 0;

        for(Long version : listVersion) {
            count++;
            if(count >= 2) sql += " UNION ALL ";
            sql +=  " SELECT ngay_baocao, sum(case when sl_ntc is not null then sl_ntc else 0 end) as sl_ntc," +
                    "sum(case when tong_sl is not null then tong_sl else 0 end) as tong_sl " +
                    "FROM ALL_TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
                    " version = " + version +
                    (!chiNhanh.equals("") ? " and tinh_nhan = '" + chiNhanh + "'" : "") +
                    (!buuCuc.isEmpty() ? " and MA_BUUCUC_GOC = '" + buuCuc + "'" : "") +
                    " group by ngay_baocao ";
        }

        System.out.println(".................SQL: " + sql);

        List<TonNhapMayChuaKetNoiResponse> listResult = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TonNhapMayChuaKetNoiResponse ton = new TonNhapMayChuaKetNoiResponse();
                ton.setNgayBaoCao(rs.getDate("ngay_baocao").toLocalDate());
                ton.setTongSl(rs.getLong("sl_ntc"));
                ton.setSlTon(rs.getLong("tong_sl"));
                listResult.add(ton);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listResult;
    }

    public Long getVersionMax(LocalDate ngayBaoCao, String loaiBC) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT version from chiso_version where ngay_baocao = date '" + ngayBaoCao + "' and ma_chiso = '" + loaiBC + "'";
        System.out.println(sqlMax);
        Long maxVersion = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();

            while (rs.next()) {
                maxVersion = rs.getLong("version");
//                mapVersion.put(date,maxVersion);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return maxVersion;
    }

    public Map<Date, Long> getMultipleVersion(LocalDate ngayBaoCao, LocalDate ngayDauThang, String loaiBC) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT ngay_baocao, version from chiso_version where ngay_baocao >= date '" + ngayDauThang +
                "' and ngay_baocao <= date '" + ngayBaoCao + "' and ma_chiso = '" + loaiBC + "'" + " group by ngay_baocao";
        System.out.println(sqlMax);

        Long maxVersion = null;
        Date date = null;
        Map<Date, Long> mapVersion = new HashMap<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);

            rs = stmt.executeQuery();
            while (rs.next()) {
                date = rs.getDate("ngay_baocao");
                maxVersion = rs.getLong("version");
                mapVersion.put(date,maxVersion);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return mapVersion;
    }

    private final Logger logger = LoggerFactory.getLogger(TonNhapMayDAOImpl.class);

    public String list2String(List<String> list) {
        String version = "";

        if (!list.isEmpty()) {
            //chuyển list thành string để query
            version = list.stream()
                    .map(n -> String.valueOf(n))
                    .collect(Collectors.joining("','", "('", "')"));

        }

        return version;
    }

    public String listLong2String(List<Long> list) {
        String version = "";

        if (!list.isEmpty()) {
            //chuyển list thành string để query
            version = list.stream()
                    .map(n -> String.valueOf(n))
                    .collect(Collectors.joining(",", "(", ")"));

        }

        return version;
    }

    public String chuyenNgayTuLongThanhString(String ngay) {
        if (ngay != null) {
            long ngays = Long.parseLong(ngay);
            Timestamp ts = new Timestamp(ngays);
            Date date = new Date(ts.getTime());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        }
        return null;
    }

}
