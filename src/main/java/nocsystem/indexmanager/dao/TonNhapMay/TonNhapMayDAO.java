package nocsystem.indexmanager.dao.TonNhapMay;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

public interface TonNhapMayDAO {

    List<TonChuaKetNoiResponse> getListChuaNhapMay(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuu<PERSON>uc, Integer dichVu, String maChiNhanh) throws SQLException;

    List<DashTonHanhTrinhDto> getExcelChuaNhapMay(LocalDate ngayBaoCao, String loaiDon, List<String> listCN, List<String> listBC, Integer dichVu) throws SQLException;

    String getTimeTinhToan(LocalDate ngayBaoCao, String loaiDon, List<String> listCN, List<String> listBC, Integer dichVu) throws SQLException;

    List<TonNhapMayChuaKetNoiResponse> getBieuDoTonNhapMay(LocalDate ngayBaoCao, LocalDate ngayBatDau, String chiNhanh, String buuCuc) throws SQLException;
}
