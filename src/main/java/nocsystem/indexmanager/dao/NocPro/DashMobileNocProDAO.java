package nocsystem.indexmanager.dao.NocPro;

import nocsystem.indexmanager.models.Response.NocPro.ChiTietHieuQuaKDResponse;
import nocsystem.indexmanager.models.Response.NocPro.TongQuanHieuQuaKDResponse;

import java.time.LocalDate;
import java.util.List;

public interface DashMobileNocProDAO {
    TongQuanHieuQuaKDResponse getDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String vungCon, String nhomKhachHang, List<String> nhomDichVu, List<String> mucTrongLuong, String dichVu);
    List<ChiTietHieuQuaKDResponse> getDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCuc<PERSON>O, String vungCon, String nhomKhachHang, List<String> nhomDichVu, List<String> mucTrongLuong, String dichVu);
    ChiTietHieuQuaKDResponse getTotalDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String vungCon, String nhomKhachHang, List<String> nhomDichVu, List<String> mucTrongLuong, String dichVu);

}
