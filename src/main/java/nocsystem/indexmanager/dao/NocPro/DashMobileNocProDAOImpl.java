package nocsystem.indexmanager.dao.NocPro;

import com.google.common.base.Strings;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.NocPro.ChiTietHieuQuaKDResponse;
import nocsystem.indexmanager.models.Response.NocPro.DonGiaBillResponse;
import nocsystem.indexmanager.models.Response.NocPro.DonGiaKgResponse;
import nocsystem.indexmanager.models.Response.NocPro.TongQuanHieuQuaKDResponse;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static nocsystem.indexmanager.util.StringUtils.list2StringQuery;
import static nocsystem.indexmanager.util.TimeUtils.localDateToString;

@Service
public class DashMobileNocProDAOImpl extends AbstractDao implements DashMobileNocProDAO {
    @Override
    public TongQuanHieuQuaKDResponse getDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String vungCon, String nhomKhachHang, List<String> nhomDichVu, List<String> mucTrongLuong, String dichVu) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getMaxVersion(ngayBaoCao, "noc_pro_hieu_qua_doanh_thu");

        String cnbc = "";
        List<String> listTrongLuongNho = Arrays.asList("DEN_1KG", "DEN_2KG", "DEN_5KG");
        List<String> listTrongLuongLon = Arrays.asList("TREN_5KG");
        Collections.sort(mucTrongLuong);

        if (nhomDichVu.stream().map(String::toString).collect(Collectors.joining()).equalsIgnoreCase("CPTN")
                && nhomKhachHang.equalsIgnoreCase("KH_NGOAISAN")
                && (mucTrongLuong.equals(listTrongLuongNho) || mucTrongLuong.equals(listTrongLuongLon))) {
            if (!buuCuc.isEmpty()) {
                cnbc = " max(kh_don_gia_kg_buu_cuc) as kh_don_gia_kg, max(kh_don_gia_bill_buu_cuc) as kh_don_gia_bill, ";
            } else {
                cnbc = " max(kh_don_gia_kg_chi_nhanh) as kh_don_gia_kg, max(kh_don_gia_bill_chi_nhanh) as kh_don_gia_bill, ";
            }
        }
        String filterChiNhanh = chiNhanh.isEmpty() ? "" : " and ma_chi_nhanh = '" + chiNhanh + "'";
        String filterBuuCuc = buuCuc.isEmpty() ? "" : " and ma_buu_cuc = '" + buuCuc + "'";
        String filterChiNhanhSSO = chiNhanhSSO.isEmpty() ? "" : " and ma_chi_nhanh in " + list2StringQuery(chiNhanhSSO);
        String filterBuuCucSSO = buuCucSSO.isEmpty() ? "" : " and ma_buu_cuc in " + list2StringQuery(buuCucSSO);
        String filterVungCon = vungCon.equals("") ? "" : " and ma_vung_con = '" + vungCon + "'";
        String filterMien = mien.equals("") ? "" : " and ma_mien = '" + mien + "'";
        String filterNhomKH = nhomKhachHang.equals("") ? "" : " and nhom_khach_hang = '" + nhomKhachHang + "'";
        String filterNhomDV = nhomDichVu.isEmpty() ? "" : " and nhom_dich_vu in " + list2StringQuery(nhomDichVu);
        String filterMucTrongLuong = mucTrongLuong.isEmpty() ? "" : " and muc_trong_luong in " + list2StringQuery(mucTrongLuong);
        String filterDichVu = dichVu.equals("") ? "" : " and dich_vu = '" + dichVu + "'";

        String sql = "select " + cnbc +
                " sum(doanh_thu_thuc_hien_ngay_n_1) as doanh_thu_thuc_hien_ngay_n_1, sum(trong_luong_thuc_hien_ngay_n_1) as trong_luong_thuc_hien_ngay_n_1, " +
                " sum(doanh_thu_luy_ke_thang_n) as doanh_thu_luy_ke_thang_n, sum(trong_luong_luy_ke_thang_n) as trong_luong_luy_ke_thang_n, " +
                " sum(doanh_thu_luy_ke_thang_n_1) as doanh_thu_luy_ke_thang_n_1, sum(trong_luong_luy_ke_thang_n_1) as trong_luong_luy_ke_thang_n_1, " +
                " sum(san_luong_thuc_hien_ngay_n_1) as san_luong_thuc_hien_ngay_n_1, sum(san_luong_luy_ke_thang_n) as san_luong_luy_ke_thang_n, " +
                " sum(san_luong_luy_ke_thang_n_1) as san_luong_luy_ke_thang_n_1 " +
                " from noc_pro_hieu_qua_doanh_thu " +
                " where ngay_bao_cao = '" + localDateToString(ngayBaoCao) + "'" +
                " and version = " + version
                + filterChiNhanh + filterChiNhanhSSO + filterVungCon + filterBuuCuc + filterBuuCucSSO + filterMien + filterNhomKH + filterNhomDV + filterMucTrongLuong + filterDichVu;

        System.out.println(sql);

        TongQuanHieuQuaKDResponse res = new TongQuanHieuQuaKDResponse();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            while (rs.next()) {
                double khDonGiaKg = 0D;
                double khDonGiaBill = 0D;
                if (!cnbc.isEmpty()) {
                    khDonGiaKg = rs.getDouble("kh_don_gia_kg");
                    khDonGiaBill = rs.getDouble("kh_don_gia_bill");
                }
                double doanhThuThucHienNgayN1 = rs.getDouble("doanh_thu_thuc_hien_ngay_n_1");
                double trongLuongThucHienNgayN1 = rs.getDouble("trong_luong_thuc_hien_ngay_n_1");
                double doanhThuLuyKeThangN = rs.getDouble("doanh_thu_luy_ke_thang_n");
                double trongLuongLuyKeThangN = rs.getDouble("trong_luong_luy_ke_thang_n");
                double doanhThuLuyKeThangN1 = rs.getDouble("doanh_thu_luy_ke_thang_n_1");
                double trongLuongLuyKeThangN1 = rs.getDouble("trong_luong_luy_ke_thang_n_1");
                long sanLuongThucHienNgayN1  =rs.getLong("san_luong_thuc_hien_ngay_n_1");
                long sanLuongLuyKeThangN = rs.getLong("san_luong_luy_ke_thang_n");
                long sanLuongLuyKeThangN1 = rs.getLong("san_luong_luy_ke_thang_n_1");

                String khDonGiaKgStr;
                String khDonGiaBillStr;
                if (chiNhanh.isEmpty() && chiNhanhSSO.isEmpty()
                        && buuCuc.isEmpty() && buuCucSSO.isEmpty()) {
                    // Case tính dữ liệu cho TCT
                    // Chưa có dữ liệu cho TCT nên để "N/A"
                    khDonGiaKgStr = "N/A";
                    khDonGiaBillStr = "N/A";
                } else {
                    if (!cnbc.isEmpty()) {
                        khDonGiaKgStr = String.valueOf(khDonGiaKg / 1000);
                        khDonGiaBillStr = String.valueOf(khDonGiaBill);
                    } else {
                        khDonGiaKgStr = "N/A";
                        khDonGiaBillStr = "N/A";
                    }
                }
                String donGiaKgNgay;
                String donGiaKgThang;
                String donGiaKgThangN1;
                String tlhtKg;
                String tangGiamKgThang;
                String tangGiamKgSoVoiKH;
                String donGiaBillNgay;
                String donGiaBillThang;
                String donGiaBillThangN1;
                String tlhtBill;
                String tangGiamBillThang;
                String tangGiamBillSoVoiKH;

                //Tính chỉ số đơn giá/kg
                if (doanhThuThucHienNgayN1 == 0.0 || trongLuongThucHienNgayN1 == 0.0) {
                    donGiaKgNgay = "N/A";
                } else {
                    donGiaKgNgay = String.valueOf((doanhThuThucHienNgayN1 / trongLuongThucHienNgayN1) / 1000);
                }

                if (doanhThuLuyKeThangN == 0.0 || trongLuongLuyKeThangN == 0.0) {
                    donGiaKgThang = "N/A";
                } else {
                    donGiaKgThang = String.valueOf((doanhThuLuyKeThangN / trongLuongLuyKeThangN) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || khDonGiaKgStr.equals("N/A")) {
                    tlhtKg = "N/A";
                } else if (Double.parseDouble(donGiaKgThang) == 0.0 && Double.parseDouble(khDonGiaKgStr) == 0.0) {
                    tlhtKg = "0.0";
                } else if (Double.parseDouble(donGiaKgThang) == 0.0) {
                    tlhtKg = "-100.0";
                } else if (Double.parseDouble(khDonGiaKgStr) == 0.0) {
                    tlhtKg = "100.0";
                } else {
                    tlhtKg = String.valueOf(Double.parseDouble(donGiaKgThang) / Double.parseDouble(khDonGiaKgStr) * 100);
                }

                if (doanhThuLuyKeThangN1 == 0.0 || trongLuongLuyKeThangN1 == 0.0) {
                    donGiaKgThangN1 = "N/A";
                } else {
                    donGiaKgThangN1 = String.valueOf((doanhThuLuyKeThangN1 / trongLuongLuyKeThangN1) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || donGiaKgThangN1.equals("N/A")) {
                    tangGiamKgThang = "N/A";
                } else {
                    tangGiamKgThang = String.valueOf(Double.parseDouble(donGiaKgThang) - Double.parseDouble(donGiaKgThangN1));
                }

                if (donGiaKgThang.equals("N/A") || khDonGiaKgStr.equals("N/A")) {
                    tangGiamKgSoVoiKH = "N/A";
                } else {
                    tangGiamKgSoVoiKH = String.valueOf(Double.parseDouble(khDonGiaKgStr) - Double.parseDouble(donGiaKgThang));
                }


                //Tính chỉ số đơn giá/bill
                if (doanhThuThucHienNgayN1 == 0.0 || sanLuongThucHienNgayN1 == 0.0) {
                    donGiaBillNgay = "N/A";
                } else {
                    donGiaBillNgay = String.valueOf(doanhThuThucHienNgayN1 / sanLuongThucHienNgayN1);
                }

                if (doanhThuLuyKeThangN == 0.0 || sanLuongLuyKeThangN == 0.0) {
                    donGiaBillThang = "N/A";
                } else {
                    donGiaBillThang = String.valueOf(doanhThuLuyKeThangN / sanLuongLuyKeThangN);
                }

                if (donGiaBillThang.equals("N/A") || khDonGiaBillStr.equals("N/A")) {
                    tlhtBill = "N/A";
                } else if (Double.parseDouble(donGiaBillThang) == 0.0 && Double.parseDouble(khDonGiaBillStr) == 0.0) {
                    tlhtBill = "0.0";
                } else if (Double.parseDouble(donGiaBillThang) == 0.0) {
                    tlhtBill = "-100.0";
                } else if (Double.parseDouble(khDonGiaBillStr) == 0.0) {
                    tlhtBill = "100.0";
                } else {
                    tlhtBill = String.valueOf(Double.parseDouble(donGiaBillThang) / Double.parseDouble(khDonGiaBillStr) * 100);
                }

                if (doanhThuLuyKeThangN1 == 0.0 || sanLuongLuyKeThangN1 == 0.0) {
                    donGiaBillThangN1 = "N/A";
                } else {
                    donGiaBillThangN1 = String.valueOf(doanhThuLuyKeThangN1 / sanLuongLuyKeThangN1);
                }

                if (donGiaBillThang.equals("N/A") || donGiaBillThangN1.equals("N/A")) {
                    tangGiamBillThang = "N/A";
                } else {
                    tangGiamBillThang = String.valueOf(Double.parseDouble(donGiaBillThang) - Double.parseDouble(donGiaBillThangN1));
                }

                if (donGiaBillThang.equals("N/A") || khDonGiaBillStr.equals("N/A")) {
                    tangGiamBillSoVoiKH = "N/A";
                } else {
                    tangGiamBillSoVoiKH = String.valueOf(Double.parseDouble(khDonGiaBillStr) - Double.parseDouble(donGiaBillThang));
                }

                return new TongQuanHieuQuaKDResponse(
                        khDonGiaKgStr.equals("N/A") ? khDonGiaKgStr : String.valueOf((double) Math.round(Double.parseDouble(khDonGiaKgStr) * 100) / 100),
                        donGiaKgNgay.equals("N/A") ? donGiaKgNgay : String.valueOf(Math.round(Double.parseDouble(donGiaKgNgay))),
                        donGiaKgThang.equals("N/A") ? donGiaKgThang : String.valueOf(Math.round(Double.parseDouble(donGiaKgThang))),
                        tlhtKg.equals("N/A") ? tlhtKg : String.valueOf((double) Math.round(Double.parseDouble(tlhtKg) * 100) / 100),
                        tangGiamKgThang.equals("N/A") ? tangGiamKgThang : String.valueOf(Math.round(Double.parseDouble(tangGiamKgThang))),
                        tangGiamKgSoVoiKH.equals("N/A") ? tangGiamKgSoVoiKH : String.valueOf(Math.round(Double.parseDouble(tangGiamKgSoVoiKH))),
                        khDonGiaBillStr.equals("N/A") ? khDonGiaBillStr : String.valueOf((double) Math.round(Double.parseDouble(khDonGiaBillStr) * 100) / 100),
                        donGiaBillNgay.equals("N/A") ? donGiaBillNgay : String.valueOf(Math.round(Double.parseDouble(donGiaBillNgay))),
                        donGiaBillThang.equals("N/A") ? donGiaBillThang : String.valueOf(Math.round(Double.parseDouble(donGiaBillThang))),
                        tlhtBill.equals("N/A") ? tlhtBill : String.valueOf((double) Math.round(Double.parseDouble(tlhtBill) * 100) / 100),
                        tangGiamBillThang.equals("N/A") ? tangGiamBillThang : String.valueOf(Math.round(Double.parseDouble(tangGiamBillThang))),
                        tangGiamBillSoVoiKH.equals("N/A") ? tangGiamBillSoVoiKH : String.valueOf(Math.round(Double.parseDouble(tangGiamBillSoVoiKH)))
                );
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return res;
    }

    @Override
    public List<ChiTietHieuQuaKDResponse> getDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String vungCon, String nhomKhachHang, List<String> nhomDichVu, List<String> mucTrongLuong, String dichVu) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getMaxVersion(ngayBaoCao, "noc_pro_hieu_qua_doanh_thu");

        String cnbc;
        String khdg = "";
        String groupBy;
        List<String> listTrongLuongNho = Arrays.asList("DEN_1KG","DEN_2KG","DEN_5KG");
        List<String> listTrongLuongLon = Arrays.asList("TREN_5KG");
        Collections.sort(mucTrongLuong);

        if (chiNhanh.isEmpty()) {
            cnbc = " ma_chi_nhanh, ";
            groupBy = " group by ma_chi_nhanh ";
        } else {
            cnbc = " ma_chi_nhanh, ma_buu_cuc, ";
            groupBy = " group by ma_chi_nhanh, ma_buu_cuc ";
        }
        if (nhomDichVu.stream().map(String::toString).collect(Collectors.joining()).equalsIgnoreCase("CPTN")
                && nhomKhachHang.equalsIgnoreCase("KH_NGOAISAN")
                && (mucTrongLuong.equals(listTrongLuongNho) || mucTrongLuong.equals(listTrongLuongLon))) {
            if (chiNhanh.isEmpty()) {
                khdg = " max(kh_don_gia_kg_chi_nhanh) as kh_don_gia_kg, max(kh_don_gia_bill_chi_nhanh) as kh_don_gia_bill, ";
            } else {
                khdg = " max(kh_don_gia_kg_buu_cuc) as kh_don_gia_kg, max(kh_don_gia_bill_buu_cuc) as kh_don_gia_bill, ";
            }
        }

        String filterChiNhanh = chiNhanh.isEmpty() ? "" : " and ma_chi_nhanh = '" + chiNhanh + "'";
        String filterBuuCuc = buuCuc.isEmpty() ? "" : " and ma_buu_cuc = '" + buuCuc + "'";
        String filterChiNhanhSSO = chiNhanhSSO.isEmpty() ? "" : " and ma_chi_nhanh in " + list2StringQuery(chiNhanhSSO);
        String filterBuuCucSSO = buuCucSSO.isEmpty() ? "" : " and ma_buu_cuc in " + list2StringQuery(buuCucSSO);
        String filterVungCon = vungCon.equals("") ? "" : " and ma_vung_con = '" + vungCon + "'";
        String filterMien = mien.equals("") ? "" : " and  ma_mien = '" + mien + "'";
        String filterNhomKH = nhomKhachHang.equals("") ? "" : " and nhom_khach_hang = '" + nhomKhachHang + "'";
        String filterNhomDV = nhomDichVu.isEmpty() ? "" : " and nhom_dich_vu in " + list2StringQuery(nhomDichVu);
        String filterMucTrongLuong = mucTrongLuong.isEmpty() ? "" : " and muc_trong_luong in " + list2StringQuery(mucTrongLuong);
        String filterDichVu = dichVu.equals("") ? "" : " and dich_vu = '" + dichVu + "'";

        String sql = "select " + cnbc + khdg +
                " sum(doanh_thu_thuc_hien_ngay_n_1) as doanh_thu_thuc_hien_ngay_n_1, sum(doanh_thu_luy_ke_thang_n) as doanh_thu_luy_ke_thang_n, " +
                " sum(trong_luong_thuc_hien_ngay_n_1) as trong_luong_thuc_hien_ngay_n_1, sum(trong_luong_luy_ke_thang_n) as trong_luong_luy_ke_thang_n, " +
                " sum(doanh_thu_luy_ke_thang_n_1) as doanh_thu_luy_ke_thang_n_1, sum(trong_luong_luy_ke_thang_n_1) as trong_luong_luy_ke_thang_n_1, " +
                " sum(doanh_thu_luy_ke_thang_n_nam_n_1) as doanh_thu_luy_ke_thang_n_nam_n_1, sum(trong_luong_luy_ke_thang_n_nam_n_1) as trong_luong_luy_ke_thang_n_nam_n_1, " +
                " sum(san_luong_thuc_hien_ngay_n_1) as san_luong_thuc_hien_ngay_n_1, sum(san_luong_luy_ke_thang_n) as san_luong_luy_ke_thang_n, " +
                " sum(san_luong_luy_ke_thang_n_1) as san_luong_luy_ke_thang_n_1, sum(san_luong_luy_ke_thang_n_nam_n_1) as san_luong_luy_ke_thang_n_nam_n_1 " +
                " from noc_pro_hieu_qua_doanh_thu " +
                " where ngay_bao_cao = '" + localDateToString(ngayBaoCao) + "'" +
                " and version = " + version
                + filterChiNhanh + filterChiNhanhSSO + filterVungCon + filterBuuCuc + filterBuuCucSSO + filterMien + filterNhomKH + filterNhomDV + filterMucTrongLuong + filterDichVu
                + groupBy;

        System.out.println(sql);

        List<ChiTietHieuQuaKDResponse> listResult = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            String maChiNhanh = null;
            String maBuuCuc = null;
            double khDonGiaKg = 0D;
            double khDonGiaBill = 0D;
            double doanhThuThucHienNgayN1;
            double doanhThuLuyKeThangN;
            double trongLuongThucHienNgayN1;
            double trongLuongLuyKeThangN;
            double doanhThuLuyKeThangN1;
            double trongLuongLuyKeThangN1;
            double doanhThuLuyKeThangNNamN1;
            double trongLuongLuyKeThangNNamN1;
            long sanLuongThucHienNgayN1;
            long sanLuongLuyKeThangN;
            long sanLuongLuyKeThangN1;
            long sanLuongLuyKeThangNNamN1;

            while (rs.next()) {
                maChiNhanh = rs.getString("ma_chi_nhanh");
                if (!chiNhanh.isEmpty()) {
                    maBuuCuc = rs.getString("ma_buu_cuc");
                }
                if (!khdg.isEmpty()) {
                    khDonGiaKg = rs.getDouble("kh_don_gia_kg");
                    khDonGiaBill = rs.getDouble("kh_don_gia_bill");
                }
                doanhThuThucHienNgayN1 = rs.getDouble("doanh_thu_thuc_hien_ngay_n_1");
                doanhThuLuyKeThangN = rs.getDouble("doanh_thu_luy_ke_thang_n");
                trongLuongThucHienNgayN1 = rs.getDouble("trong_luong_thuc_hien_ngay_n_1");
                trongLuongLuyKeThangN = rs.getDouble("trong_luong_luy_ke_thang_n");
                doanhThuLuyKeThangN1 = rs.getDouble("doanh_thu_luy_ke_thang_n_1");
                trongLuongLuyKeThangN1 = rs.getDouble("trong_luong_luy_ke_thang_n_1");
                doanhThuLuyKeThangNNamN1 = rs.getDouble("doanh_thu_luy_ke_thang_n_nam_n_1");
                trongLuongLuyKeThangNNamN1 = rs.getDouble("trong_luong_luy_ke_thang_n_nam_n_1");
                sanLuongThucHienNgayN1  =rs.getLong("san_luong_thuc_hien_ngay_n_1");
                sanLuongLuyKeThangN = rs.getLong("san_luong_luy_ke_thang_n");
                sanLuongLuyKeThangN1 = rs.getLong("san_luong_luy_ke_thang_n_1");
                sanLuongLuyKeThangNNamN1 = rs.getLong("san_luong_luy_ke_thang_n_nam_n_1");

                String khDonGiaKgStr;
                String khDonGiaBillStr;
                if (!khdg.isEmpty()) {
                    khDonGiaKgStr = String.valueOf(khDonGiaKg / 1000);
                    khDonGiaBillStr = String.valueOf(khDonGiaBill);
                } else {
                    khDonGiaKgStr = "N/A";
                    khDonGiaBillStr = "N/A";
                }
                String donGiaKgNgay;
                String donGiaKgThang;
                String tlhtKg;
                String donGiaKgThangN1;
                String deltaTangGiamKgThang;
                String donGiaKgThangNNamN1;
                String deltaTangGiamKgNam;
                String tlhtKgDeltaThang;
                String donGiaBillNgay;
                String donGiaBillThang;
                String tlhtBill;
                String donGiaBillThangN1;
                String donGiaBillThangNNamN1;
                String deltaTangGiamBillThang;
                String tlhtBillDeltaThang;
                String deltaTangGiamBillNam;


                //Tính chỉ số đơn giá/kg
                if (doanhThuThucHienNgayN1 == 0.0 || trongLuongThucHienNgayN1 == 0.0) {
                    donGiaKgNgay = "N/A";
                } else {
                    donGiaKgNgay = String.valueOf((doanhThuThucHienNgayN1 / trongLuongThucHienNgayN1) / 1000);
                }

                if (doanhThuLuyKeThangN == 0.0 || trongLuongLuyKeThangN == 0.0) {
                    donGiaKgThang = "N/A";
                } else {
                    donGiaKgThang = String.valueOf((doanhThuLuyKeThangN / trongLuongLuyKeThangN) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || khDonGiaKgStr.equals("N/A")) {
                    tlhtKg = "N/A";
                } else if (Double.parseDouble(donGiaKgThang) == 0.0 && Double.parseDouble(khDonGiaKgStr) == 0.0) {
                    tlhtKg = "0.0";
                } else if (Double.parseDouble(donGiaKgThang) == 0.0) {
                    tlhtKg = "-100.0";
                } else if (Double.parseDouble(khDonGiaKgStr) == 0.0) {
                    tlhtKg = "100.0";
                } else {
                    tlhtKg = String.valueOf(Double.parseDouble(donGiaKgThang) / Double.parseDouble(khDonGiaKgStr) * 100);
                }

                if (doanhThuLuyKeThangN1 == 0.0 || trongLuongLuyKeThangN1 == 0.0) {
                    donGiaKgThangN1 = "N/A";
                } else {
                    donGiaKgThangN1 = String.valueOf((doanhThuLuyKeThangN1 / trongLuongLuyKeThangN1) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || donGiaKgThangN1.equals("N/A")) {
                    deltaTangGiamKgThang = "N/A";
                } else {
                    deltaTangGiamKgThang = String.valueOf(Double.parseDouble(donGiaKgThang) - Double.parseDouble(donGiaKgThangN1));
                }

                if (doanhThuLuyKeThangNNamN1 == 0.0 || trongLuongLuyKeThangNNamN1 == 0.0) {
                    donGiaKgThangNNamN1 = "N/A";
                } else {
                    donGiaKgThangNNamN1 = String.valueOf((doanhThuLuyKeThangNNamN1 / trongLuongLuyKeThangNNamN1) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || donGiaKgThangNNamN1.equals("N/A")) {
                    deltaTangGiamKgNam = "N/A";
                } else {
                    deltaTangGiamKgNam = String.valueOf(Double.parseDouble(donGiaKgThang) - Double.parseDouble(donGiaKgThangNNamN1));
                }

                if (deltaTangGiamKgThang.equals("N/A") || donGiaKgThangN1.equals("N/A") || khDonGiaKgStr.equals("N/A")) {
                    tlhtKgDeltaThang = "N/A";
                } else if (Double.parseDouble(deltaTangGiamKgThang) == 0.0 && (Double.parseDouble(khDonGiaKgStr) - Double.parseDouble(donGiaKgThangN1)) == 0.0) {
                    tlhtKgDeltaThang = "0.0";
                } else if (Double.parseDouble(deltaTangGiamKgThang) == 0.0) {
                    tlhtKgDeltaThang = "-100.0";
                } else if (Double.parseDouble(khDonGiaKgStr) - Double.parseDouble(donGiaKgThangN1) == 0.0) {
                    tlhtKgDeltaThang = "100.0";
                } else {
                    tlhtKgDeltaThang = String.valueOf(Double.parseDouble(deltaTangGiamKgThang) / (Double.parseDouble(khDonGiaKgStr) - Double.parseDouble(donGiaKgThangN1)) * 100);
                }


                //Tính chỉ số đơn giá/bill
                if (doanhThuThucHienNgayN1 == 0.0 || sanLuongThucHienNgayN1 == 0.0) {
                    donGiaBillNgay = "N/A";
                } else {
                    donGiaBillNgay = String.valueOf(doanhThuThucHienNgayN1 / sanLuongThucHienNgayN1);
                }

                if (doanhThuLuyKeThangN == 0.0 || sanLuongLuyKeThangN == 0.0) {
                    donGiaBillThang = "N/A";
                } else {
                    donGiaBillThang = String.valueOf(doanhThuLuyKeThangN / sanLuongLuyKeThangN);
                }

                if (donGiaBillThang.equals("N/A") || khDonGiaBillStr.equals("N/A")) {
                    tlhtBill = "N/A";
                } else if (Double.parseDouble(donGiaBillThang) == 0.0 && Double.parseDouble(khDonGiaBillStr) == 0.0) {
                    tlhtBill = "0.0";
                } else if (Double.parseDouble(donGiaBillThang) == 0.0) {
                    tlhtBill = "-100.0";
                } else if (Double.parseDouble(khDonGiaBillStr) == 0.0) {
                    tlhtBill = "100.0";
                } else {
                    tlhtBill = String.valueOf(Double.parseDouble(donGiaBillThang) / Double.parseDouble(khDonGiaBillStr) * 100);
                }

                if (doanhThuLuyKeThangN1 == 0.0 || sanLuongLuyKeThangN1 == 0.0) {
                    donGiaBillThangN1 = "N/A";
                } else {
                    donGiaBillThangN1 = String.valueOf(doanhThuLuyKeThangN1 / sanLuongLuyKeThangN1);
                }

                if (doanhThuLuyKeThangNNamN1 == 0.0 || sanLuongLuyKeThangNNamN1 == 0.0) {
                    donGiaBillThangNNamN1 = "N/A";
                } else {
                    donGiaBillThangNNamN1 = String.valueOf(doanhThuLuyKeThangNNamN1 / sanLuongLuyKeThangNNamN1);
                }

                if (donGiaBillThang.equals("N/A") || donGiaBillThangN1.equals("N/A")) {
                    deltaTangGiamBillThang = "N/A";
                } else {
                    deltaTangGiamBillThang = String.valueOf(Double.parseDouble(donGiaBillThang) - Double.parseDouble(donGiaBillThangN1));
                }

                if (deltaTangGiamBillThang.equals("N/A") || donGiaBillThangN1.equals("N/A") || khDonGiaBillStr.equals("N/A")) {
                    tlhtBillDeltaThang = "N/A";
                } else if (Double.parseDouble(deltaTangGiamBillThang) == 0.0 && (Double.parseDouble(khDonGiaBillStr) - Double.parseDouble(donGiaBillThangN1)) == 0.0) {
                    tlhtBillDeltaThang = "0.0";
                } else if (Double.parseDouble(deltaTangGiamBillThang) == 0.0) {
                    tlhtBillDeltaThang = "-100.0";
                } else if (Double.parseDouble(khDonGiaBillStr) - Double.parseDouble(donGiaBillThangN1) == 0.0) {
                    tlhtBillDeltaThang = "100.0";
                } else {
                    tlhtBillDeltaThang = String.valueOf(Double.parseDouble(deltaTangGiamBillThang) / (Double.parseDouble(khDonGiaBillStr) - Double.parseDouble(donGiaBillThangN1)) * 100);
                }

                if (donGiaBillThang.equals("N/A") || donGiaBillThangNNamN1.equals("N/A")) {
                    deltaTangGiamBillNam = "N/A";
                } else {
                    deltaTangGiamBillNam = String.valueOf(Double.parseDouble(donGiaBillThang) - Double.parseDouble(donGiaBillThangNNamN1));
                }

                DonGiaKgResponse donGiaKgRes = new DonGiaKgResponse(
                        khDonGiaKgStr.equals("N/A") ? khDonGiaKgStr : String.valueOf((double) Math.round(Double.parseDouble(khDonGiaKgStr) * 100) / 100),
                        String.valueOf((double) Math.round(doanhThuThucHienNgayN1 * 100) / 100),
                        String.valueOf((double) Math.round(doanhThuLuyKeThangN * 100) / 100),
                        String.valueOf((double) Math.round(trongLuongThucHienNgayN1 * 100) / 100),
                        String.valueOf((double) Math.round(trongLuongLuyKeThangN * 100) / 100),
                        donGiaKgNgay.equals("N/A") ? donGiaKgNgay : String.valueOf(Math.round(Double.parseDouble(donGiaKgNgay))),
                        donGiaKgThang.equals("N/A") ? donGiaKgThang : String.valueOf(Math.round(Double.parseDouble(donGiaKgThang))),
                        tlhtKg.equals("N/A") ? tlhtKg : String.valueOf((double) Math.round(Double.parseDouble(tlhtKg) * 100) / 100),
                        donGiaKgThangN1.equals("N/A") ? donGiaKgThangN1 : String.valueOf(Math.round(Double.parseDouble(donGiaKgThangN1))),
                        donGiaKgThangNNamN1.equals("N/A") ? donGiaKgThangNNamN1 : String.valueOf(Math.round(Double.parseDouble(donGiaKgThangNNamN1))),
                        deltaTangGiamKgThang.equals("N/A") ? deltaTangGiamKgThang : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamKgThang))),
                        tlhtKgDeltaThang.equals("N/A") ? tlhtKgDeltaThang : String.valueOf((double) Math.round(Double.parseDouble(tlhtKgDeltaThang) * 100) / 100),
                        deltaTangGiamKgNam.equals("N/A") ? deltaTangGiamKgNam : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamKgNam)))
                );

                DonGiaBillResponse donGiaBillRes = new DonGiaBillResponse(
                        khDonGiaBillStr.equals("N/A") ? khDonGiaBillStr : String.valueOf((double) Math.round(Double.parseDouble(khDonGiaBillStr) * 100) / 100),
                        donGiaBillNgay.equals("N/A") ? donGiaBillNgay : String.valueOf(Math.round(Double.parseDouble(donGiaBillNgay))),
                        donGiaBillThang.equals("N/A") ? donGiaBillThang : String.valueOf(Math.round(Double.parseDouble(donGiaBillThang))),
                        tlhtBill.equals("N/A") ? tlhtBill : String.valueOf((double) Math.round(Double.parseDouble(tlhtBill) * 100) / 100),
                        donGiaBillThangN1.equals("N/A") ? donGiaBillThangN1 : String.valueOf(Math.round(Double.parseDouble(donGiaBillThangN1))),
                        donGiaBillThangNNamN1.equals("N/A") ? donGiaBillThangNNamN1 : String.valueOf(Math.round(Double.parseDouble(donGiaBillThangNNamN1))),
                        deltaTangGiamBillThang.equals("N/A") ? deltaTangGiamBillThang : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamBillThang))),
                        tlhtBillDeltaThang.equals("N/A") ? tlhtBillDeltaThang : String.valueOf((double) Math.round(Double.parseDouble(tlhtBillDeltaThang) * 100) / 100),
                        deltaTangGiamBillNam.equals("N/A") ? deltaTangGiamBillNam : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamBillNam)))
                );

                ChiTietHieuQuaKDResponse res = new ChiTietHieuQuaKDResponse(
                        maChiNhanh,
                        Strings.isNullOrEmpty(maBuuCuc) ? null : maBuuCuc,
                        donGiaKgRes,
                        donGiaBillRes
                );
                listResult.add(res);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listResult;
    }

    @Override
    public ChiTietHieuQuaKDResponse getTotalDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String vungCon, String nhomKhachHang, List<String> nhomDichVu, List<String> mucTrongLuong, String dichVu) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getMaxVersion(ngayBaoCao, "noc_pro_hieu_qua_doanh_thu");

        String cnbc = "";
        String khdg = "";
        String groupBy = "";
        List<String> listTrongLuongNho = Arrays.asList("DEN_1KG","DEN_2KG","DEN_5KG");
        List<String> listTrongLuongLon = Arrays.asList("TREN_5KG");
        Collections.sort(mucTrongLuong);

        if (!chiNhanh.isEmpty()) {
            if (buuCuc.isEmpty()) {
                cnbc = " ma_chi_nhanh, ";
                groupBy = " group by ma_chi_nhanh ";
            } else {
                cnbc = " ma_chi_nhanh, ma_buu_cuc, ";
                groupBy = " group by ma_chi_nhanh, ma_buu_cuc ";
            }
        }
        if (nhomDichVu.stream().map(String::toString).collect(Collectors.joining()).equalsIgnoreCase("CPTN")
                && nhomKhachHang.equalsIgnoreCase("KH_NGOAISAN")
                && (mucTrongLuong.equals(listTrongLuongNho) || mucTrongLuong.equals(listTrongLuongLon))) {
            if (!chiNhanh.isEmpty()) {
                if (buuCuc.isEmpty()) {
                    khdg = " max(kh_don_gia_kg_chi_nhanh) as kh_don_gia_kg, max(kh_don_gia_bill_chi_nhanh) as kh_don_gia_bill, ";
                } else {
                    khdg = " max(kh_don_gia_kg_buu_cuc) as kh_don_gia_kg, max(kh_don_gia_bill_buu_cuc) as kh_don_gia_bill, ";
                }
            }
        }

        String filterChiNhanh = chiNhanh.isEmpty() ? "" : " and ma_chi_nhanh = '" + chiNhanh + "'";
        String filterBuuCuc = buuCuc.isEmpty() ? "" : " and ma_buu_cuc = '" + buuCuc + "'";
        String filterChiNhanhSSO = chiNhanhSSO.isEmpty() ? "" : " and ma_chi_nhanh in " + list2StringQuery(chiNhanhSSO);
        String filterBuuCucSSO = buuCucSSO.isEmpty() ? "" : " and ma_buu_cuc in " + list2StringQuery(buuCucSSO);
        String filterVungCon = vungCon.equals("") ? "" : " and ma_vung_con = '" + vungCon + "'";
        String filterMien = mien.equals("") ? "" : " and ma_mien = '" + mien + "'";
        String filterNhomKH = nhomKhachHang.equals("") ? "" : " and nhom_khach_hang = '" + nhomKhachHang + "'";
        String filterNhomDV = nhomDichVu.isEmpty() ? "" : " and nhom_dich_vu in " + list2StringQuery(nhomDichVu);
        String filterMucTrongLuong = mucTrongLuong.isEmpty() ? "" : " and muc_trong_luong in " + list2StringQuery(mucTrongLuong);
        String filterDichVu = dichVu.equals("") ? "" : " and dich_vu = '" + dichVu + "'";

        String sql = "select " + cnbc + khdg +
                " sum(doanh_thu_thuc_hien_ngay_n_1) as doanh_thu_thuc_hien_ngay_n_1, sum(doanh_thu_luy_ke_thang_n) as doanh_thu_luy_ke_thang_n, " +
                " sum(trong_luong_thuc_hien_ngay_n_1) as trong_luong_thuc_hien_ngay_n_1, sum(trong_luong_luy_ke_thang_n) as trong_luong_luy_ke_thang_n, " +
                " sum(doanh_thu_luy_ke_thang_n_1) as doanh_thu_luy_ke_thang_n_1, sum(trong_luong_luy_ke_thang_n_1) as trong_luong_luy_ke_thang_n_1, " +
                " sum(doanh_thu_luy_ke_thang_n_nam_n_1) as doanh_thu_luy_ke_thang_n_nam_n_1, sum(trong_luong_luy_ke_thang_n_nam_n_1) as trong_luong_luy_ke_thang_n_nam_n_1, " +
                " sum(san_luong_thuc_hien_ngay_n_1) as san_luong_thuc_hien_ngay_n_1, sum(san_luong_luy_ke_thang_n) as san_luong_luy_ke_thang_n, " +
                " sum(san_luong_luy_ke_thang_n_1) as san_luong_luy_ke_thang_n_1, sum(san_luong_luy_ke_thang_n_nam_n_1) as san_luong_luy_ke_thang_n_nam_n_1 " +
                " from noc_pro_hieu_qua_doanh_thu " +
                " where ngay_bao_cao = '" + localDateToString(ngayBaoCao) + "'" +
                " and version = " + version
                + filterChiNhanh + filterChiNhanhSSO + filterVungCon + filterBuuCuc + filterBuuCucSSO + filterMien + filterNhomKH + filterNhomDV + filterMucTrongLuong + filterDichVu
                + groupBy;

        System.out.println(sql);

        ChiTietHieuQuaKDResponse response = new ChiTietHieuQuaKDResponse();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            String maChiNhanh = null;
            String maBuuCuc = null;
            double khDonGiaKg = 0D;
            double khDonGiaBill = 0D;
            double doanhThuThucHienNgayN1;
            double doanhThuLuyKeThangN;
            double trongLuongThucHienNgayN1;
            double trongLuongLuyKeThangN;
            double doanhThuLuyKeThangN1;
            double trongLuongLuyKeThangN1;
            double doanhThuLuyKeThangNNamN1;
            double trongLuongLuyKeThangNNamN1;
            long sanLuongThucHienNgayN1;
            long sanLuongLuyKeThangN;
            long sanLuongLuyKeThangN1;
            long sanLuongLuyKeThangNNamN1;

            while (rs.next()) {
                if (!chiNhanh.isEmpty()) {
                    maChiNhanh = rs.getString("ma_chi_nhanh");
                }
                if (!buuCuc.isEmpty()) {
                    maBuuCuc = rs.getString("ma_buu_cuc");
                }
                if (!khdg.isEmpty()) {
                    khDonGiaKg = rs.getDouble("kh_don_gia_kg");
                    khDonGiaBill = rs.getDouble("kh_don_gia_bill");
                }
                doanhThuThucHienNgayN1 = rs.getDouble("doanh_thu_thuc_hien_ngay_n_1");
                doanhThuLuyKeThangN = rs.getDouble("doanh_thu_luy_ke_thang_n");
                trongLuongThucHienNgayN1 = rs.getDouble("trong_luong_thuc_hien_ngay_n_1");
                trongLuongLuyKeThangN = rs.getDouble("trong_luong_luy_ke_thang_n");
                doanhThuLuyKeThangN1 = rs.getDouble("doanh_thu_luy_ke_thang_n_1");
                trongLuongLuyKeThangN1 = rs.getDouble("trong_luong_luy_ke_thang_n_1");
                doanhThuLuyKeThangNNamN1 = rs.getDouble("doanh_thu_luy_ke_thang_n_nam_n_1");
                trongLuongLuyKeThangNNamN1 = rs.getDouble("trong_luong_luy_ke_thang_n_nam_n_1");
                sanLuongThucHienNgayN1  =rs.getLong("san_luong_thuc_hien_ngay_n_1");
                sanLuongLuyKeThangN = rs.getLong("san_luong_luy_ke_thang_n");
                sanLuongLuyKeThangN1 = rs.getLong("san_luong_luy_ke_thang_n_1");
                sanLuongLuyKeThangNNamN1 = rs.getLong("san_luong_luy_ke_thang_n_nam_n_1");

                String khDonGiaKgStr;
                String khDonGiaBillStr;
                if (chiNhanh.isEmpty() && chiNhanhSSO.isEmpty()
                        && buuCuc.isEmpty() && buuCucSSO.isEmpty()) {
                    // Case tính dữ liệu cho TCT
                    // Chưa có dữ liệu cho TCT nên để "N/A"
                    khDonGiaKgStr = "N/A";
                    khDonGiaBillStr = "N/A";
                } else {
                    if (!khdg.isEmpty()) {
                        khDonGiaKgStr = String.valueOf(khDonGiaKg / 1000);
                        khDonGiaBillStr = String.valueOf(khDonGiaBill);
                    } else {
                        khDonGiaKgStr = "N/A";
                        khDonGiaBillStr = "N/A";
                    }
                }
                String donGiaKgNgay;
                String donGiaKgThang;
                String tlhtKg;
                String donGiaKgThangN1;
                String deltaTangGiamKgThang;
                String donGiaKgThangNNamN1;
                String deltaTangGiamKgNam;
                String tlhtKgDeltaThang;
                String donGiaBillNgay;
                String donGiaBillThang;
                String tlhtBill;
                String donGiaBillThangN1;
                String donGiaBillThangNNamN1;
                String deltaTangGiamBillThang;
                String tlhtBillDeltaThang;
                String deltaTangGiamBillNam;


                //Tính chỉ số đơn giá/kg
                if (doanhThuThucHienNgayN1 == 0.0 || trongLuongThucHienNgayN1 == 0.0) {
                    donGiaKgNgay = "N/A";
                } else {
                    donGiaKgNgay = String.valueOf((doanhThuThucHienNgayN1 / trongLuongThucHienNgayN1) / 1000);
                }

                if (doanhThuLuyKeThangN == 0.0 || trongLuongLuyKeThangN == 0.0) {
                    donGiaKgThang = "N/A";
                } else {
                    donGiaKgThang = String.valueOf((doanhThuLuyKeThangN / trongLuongLuyKeThangN) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || khDonGiaKgStr.equals("N/A")) {
                    tlhtKg = "N/A";
                } else if (Double.parseDouble(donGiaKgThang) == 0.0 && Double.parseDouble(khDonGiaKgStr) == 0.0) {
                    tlhtKg = "0.0";
                } else if (Double.parseDouble(donGiaKgThang) == 0.0) {
                    tlhtKg = "-100.0";
                } else if (Double.parseDouble(khDonGiaKgStr) == 0.0) {
                    tlhtKg = "100.0";
                } else {
                    tlhtKg = String.valueOf(Double.parseDouble(donGiaKgThang) / Double.parseDouble(khDonGiaKgStr) * 100);
                }

                if (doanhThuLuyKeThangN1 == 0.0 || trongLuongLuyKeThangN1 == 0.0) {
                    donGiaKgThangN1 = "N/A";
                } else {
                    donGiaKgThangN1 = String.valueOf((doanhThuLuyKeThangN1 / trongLuongLuyKeThangN1) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || donGiaKgThangN1.equals("N/A")) {
                    deltaTangGiamKgThang = "N/A";
                } else {
                    deltaTangGiamKgThang = String.valueOf(Double.parseDouble(donGiaKgThang) - Double.parseDouble(donGiaKgThangN1));
                }

                if (doanhThuLuyKeThangNNamN1 == 0.0 || trongLuongLuyKeThangNNamN1 == 0.0) {
                    donGiaKgThangNNamN1 = "N/A";
                } else {
                    donGiaKgThangNNamN1 = String.valueOf((doanhThuLuyKeThangNNamN1 / trongLuongLuyKeThangNNamN1) / 1000);
                }

                if (donGiaKgThang.equals("N/A") || donGiaKgThangNNamN1.equals("N/A")) {
                    deltaTangGiamKgNam = "N/A";
                } else {
                    deltaTangGiamKgNam = String.valueOf(Double.parseDouble(donGiaKgThang) - Double.parseDouble(donGiaKgThangNNamN1));
                }

                if (deltaTangGiamKgThang.equals("N/A") || donGiaKgThangN1.equals("N/A") || khDonGiaKgStr.equals("N/A")) {
                    tlhtKgDeltaThang = "N/A";
                } else if (Double.parseDouble(deltaTangGiamKgThang) == 0.0 && (Double.parseDouble(khDonGiaKgStr) - Double.parseDouble(donGiaKgThangN1)) == 0.0) {
                    tlhtKgDeltaThang = "0.0";
                } else if (Double.parseDouble(deltaTangGiamKgThang) == 0.0) {
                    tlhtKgDeltaThang = "-100.0";
                } else if (Double.parseDouble(khDonGiaKgStr) - Double.parseDouble(donGiaKgThangN1) == 0.0) {
                    tlhtKgDeltaThang = "100.0";
                } else {
                    tlhtKgDeltaThang = String.valueOf(Double.parseDouble(deltaTangGiamKgThang) / (Double.parseDouble(khDonGiaKgStr) - Double.parseDouble(donGiaKgThangN1)) * 100);
                }


                //Tính chỉ số đơn giá/bill
                if (doanhThuThucHienNgayN1 == 0.0 || sanLuongThucHienNgayN1 == 0.0) {
                    donGiaBillNgay = "N/A";
                } else {
                    donGiaBillNgay = String.valueOf(doanhThuThucHienNgayN1 / sanLuongThucHienNgayN1);
                }

                if (doanhThuLuyKeThangN == 0.0 || sanLuongLuyKeThangN == 0.0) {
                    donGiaBillThang = "N/A";
                } else {
                    donGiaBillThang = String.valueOf(doanhThuLuyKeThangN / sanLuongLuyKeThangN);
                }

                if (donGiaBillThang.equals("N/A") || khDonGiaBillStr.equals("N/A")) {
                    tlhtBill = "N/A";
                } else if (Double.parseDouble(donGiaBillThang) == 0.0 && Double.parseDouble(khDonGiaBillStr) == 0.0) {
                    tlhtBill = "0.0";
                } else if (Double.parseDouble(donGiaBillThang) == 0.0) {
                    tlhtBill = "-100.0";
                } else if (Double.parseDouble(khDonGiaBillStr) == 0.0) {
                    tlhtBill = "100.0";
                } else {
                    tlhtBill = String.valueOf(Double.parseDouble(donGiaBillThang) / Double.parseDouble(khDonGiaBillStr) * 100);
                }

                if (doanhThuLuyKeThangN1 == 0.0 || sanLuongLuyKeThangN1 == 0.0) {
                    donGiaBillThangN1 = "N/A";
                } else {
                    donGiaBillThangN1 = String.valueOf(doanhThuLuyKeThangN1 / sanLuongLuyKeThangN1);
                }

                if (doanhThuLuyKeThangNNamN1 == 0.0 || sanLuongLuyKeThangNNamN1 == 0.0) {
                    donGiaBillThangNNamN1 = "N/A";
                } else {
                    donGiaBillThangNNamN1 = String.valueOf(doanhThuLuyKeThangNNamN1 / sanLuongLuyKeThangNNamN1);
                }

                if (donGiaBillThang.equals("N/A") || donGiaBillThangN1.equals("N/A")) {
                    deltaTangGiamBillThang = "N/A";
                } else {
                    deltaTangGiamBillThang = String.valueOf(Double.parseDouble(donGiaBillThang) - Double.parseDouble(donGiaBillThangN1));
                }

                if (deltaTangGiamBillThang.equals("N/A") || donGiaBillThangN1.equals("N/A") || khDonGiaBillStr.equals("N/A")) {
                    tlhtBillDeltaThang = "N/A";
                } else if (Double.parseDouble(deltaTangGiamBillThang) == 0.0 && (Double.parseDouble(khDonGiaBillStr) - Double.parseDouble(donGiaBillThangN1)) == 0.0) {
                    tlhtBillDeltaThang = "0.0";
                } else if (Double.parseDouble(deltaTangGiamBillThang) == 0.0) {
                    tlhtBillDeltaThang = "-100.0";
                } else if (Double.parseDouble(khDonGiaBillStr) - Double.parseDouble(donGiaBillThangN1) == 0.0) {
                    tlhtBillDeltaThang = "100.0";
                } else {
                    tlhtBillDeltaThang = String.valueOf(Double.parseDouble(deltaTangGiamBillThang) / (Double.parseDouble(khDonGiaBillStr) - Double.parseDouble(donGiaBillThangN1)) * 100);
                }

                if (donGiaBillThang.equals("N/A") || donGiaBillThangNNamN1.equals("N/A")) {
                    deltaTangGiamBillNam = "N/A";
                } else {
                    deltaTangGiamBillNam = String.valueOf(Double.parseDouble(donGiaBillThang) - Double.parseDouble(donGiaBillThangNNamN1));
                }

                DonGiaKgResponse donGiaKgRes = new DonGiaKgResponse(
                        khDonGiaKgStr.equals("N/A") ? khDonGiaKgStr : String.valueOf((double) Math.round(Double.parseDouble(khDonGiaKgStr) * 100) / 100),
                        String.valueOf((double) Math.round(doanhThuThucHienNgayN1 * 100) / 100),
                        String.valueOf((double) Math.round(doanhThuLuyKeThangN * 100) / 100),
                        String.valueOf((double) Math.round(trongLuongThucHienNgayN1 * 100) / 100),
                        String.valueOf((double) Math.round(trongLuongLuyKeThangN * 100) / 100),
                        donGiaKgNgay.equals("N/A") ? donGiaKgNgay : String.valueOf(Math.round(Double.parseDouble(donGiaKgNgay))),
                        donGiaKgThang.equals("N/A") ? donGiaKgThang : String.valueOf(Math.round(Double.parseDouble(donGiaKgThang))),
                        tlhtKg.equals("N/A") ? tlhtKg : String.valueOf((double) Math.round(Double.parseDouble(tlhtKg) * 100) / 100),
                        donGiaKgThangN1.equals("N/A") ? donGiaKgThangN1 : String.valueOf(Math.round(Double.parseDouble(donGiaKgThangN1))),
                        donGiaKgThangNNamN1.equals("N/A") ? donGiaKgThangNNamN1 : String.valueOf(Math.round(Double.parseDouble(donGiaKgThangNNamN1))),
                        deltaTangGiamKgThang.equals("N/A") ? deltaTangGiamKgThang : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamKgThang))),
                        tlhtKgDeltaThang.equals("N/A") ? tlhtKgDeltaThang : String.valueOf((double) Math.round(Double.parseDouble(tlhtKgDeltaThang) * 100) / 100),
                        deltaTangGiamKgNam.equals("N/A") ? deltaTangGiamKgNam : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamKgNam)))
                );

                DonGiaBillResponse donGiaBillRes = new DonGiaBillResponse(
                        khDonGiaBillStr.equals("N/A") ? khDonGiaBillStr : String.valueOf((double) Math.round(Double.parseDouble(khDonGiaBillStr) * 100) / 100),
                        donGiaBillNgay.equals("N/A") ? donGiaBillNgay : String.valueOf(Math.round(Double.parseDouble(donGiaBillNgay))),
                        donGiaBillThang.equals("N/A") ? donGiaBillThang : String.valueOf(Math.round(Double.parseDouble(donGiaBillThang))),
                        tlhtBill.equals("N/A") ? tlhtBill : String.valueOf((double) Math.round(Double.parseDouble(tlhtBill) * 100) / 100),
                        donGiaBillThangN1.equals("N/A") ? donGiaBillThangN1 : String.valueOf(Math.round(Double.parseDouble(donGiaBillThangN1))),
                        donGiaBillThangNNamN1.equals("N/A") ? donGiaBillThangNNamN1 : String.valueOf(Math.round(Double.parseDouble(donGiaBillThangNNamN1))),
                        deltaTangGiamBillThang.equals("N/A") ? deltaTangGiamBillThang : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamBillThang))),
                        tlhtBillDeltaThang.equals("N/A") ? tlhtBillDeltaThang : String.valueOf((double) Math.round(Double.parseDouble(tlhtBillDeltaThang) * 100) / 100),
                        deltaTangGiamBillNam.equals("N/A") ? deltaTangGiamBillNam : String.valueOf(Math.round(Double.parseDouble(deltaTangGiamBillNam)))
                );

                response = new ChiTietHieuQuaKDResponse(
                        Strings.isNullOrEmpty(maChiNhanh) ? null : maChiNhanh,
                        Strings.isNullOrEmpty(maBuuCuc) ? null : maBuuCuc,
                        donGiaKgRes,
                        donGiaBillRes
                );
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    public Long getMaxVersion(LocalDate ngayBaoCao, String loaiBC) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sql = "select max(version) as version from chiso_version where ngay_baocao = date '" + ngayBaoCao + "' and ma_chiso = '" + loaiBC + "'";
        System.out.println(sql);
        Long version = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            while (rs.next()) {
                version = rs.getLong("version");
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return version;
    }
}
