package nocsystem.indexmanager.dao.TonKhac;

//import liquibase.repackaged.org.apache.commons.lang3.time.StopWatch;

import nocsystem.indexmanager.common.StringCommon;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.excel_helper.ExportExcelResulSet;
import nocsystem.indexmanager.excel_helper.HeaderExcel;
import nocsystem.indexmanager.excel_helper.ZipHelper;
import nocsystem.indexmanager.exception.ObjectException;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.*;
import nocsystem.indexmanager.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.yarn.webapp.hamlet2.Hamlet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.*;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static nocsystem.indexmanager.util.TimeUtils.localDateToStringInteger;

@Service
public class TonKhacDAOImpl extends AbstractDao implements TonKhacDAO {
    private static final Logger log = LoggerFactory.getLogger(TonKhacDAOImpl.class);

    public final Integer LIMIT_SIZE = 300000;

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Override
    @Async
    public CompletableFuture<List<TongHopTonDto>> getListTrangThai(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        logger.info("begin getListTrangThai ");

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_trang_thai");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "tinh_nhan";
        } else {
            cnbc = "tinh_nhan, ma_vung_con, ma_buucuc_goc";
        }

        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM TON_TRANG_THAI_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                ((!listCN.isEmpty()) ? " and tinh_nhan in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDto detail = new TongHopTonDto();
                detail.setChiNhanh(rs.getString("tinh_nhan"));

                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("ma_buucuc_goc"));
                    detail.setVungCon(rs.getString("ma_vung_con"));
                }
                detail.setTonHanhTrinh(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        logger.info("end getListTrangThai ");

        return CompletableFuture.completedFuture(detailList);
    }

    @Override
    @Async
    public CompletableFuture<List<TongHopTonDto>> getListChuaPCP(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        logger.info("begin getListChuaPCP ");

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_chua_pcp");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "chi_nhanh_ht";
        } else {
            cnbc = "chi_nhanh_ht, ma_vung_con, ma_buucuc_ht";
        }

        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM TON_CHUA_PCP_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDto detail = new TongHopTonDto();
                detail.setChiNhanh(rs.getString("chi_nhanh_ht"));
                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("ma_buucuc_ht"));
                    detail.setVungCon(rs.getString("ma_vung_con"));
                }
                detail.setTonChuaPCP(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        logger.info("end getListChuaPCP ");

        return CompletableFuture.completedFuture(detailList);
    }

    @Override
    @Async
    public CompletableFuture<List<TongHopTonDto>> getListChuaNhapMay(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        logger.info("begin getListChuaNhapMay ");

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-bcg");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "tinh_nhan";
        } else {
            cnbc = "tinh_nhan, vung_con, MA_BUUCUC_GOC";
        }

        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl, sum(sl_kien_cham) as sl_kiencham " +
                "FROM ALL_TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and tinh_nhan in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDto detail = new TongHopTonDto();
                detail.setChiNhanh(rs.getString("tinh_nhan"));
                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("MA_BUUCUC_GOC"));
                    detail.setVungCon(rs.getString("vung_con"));
                }
                detail.setTonNhapMay(rs.getLong("tong_sl"));
                detail.setTonKienCham(rs.getLong("sl_kiencham"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        logger.info("end getListChuaNhapMay ");

        return CompletableFuture.completedFuture(detailList);
    }

    @Override
    @Async
    public CompletableFuture<List<TongHopTonDto>> getListTonNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        logger.info("begin getListTonNhanBanGiao ");

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-nbg");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "chi_nhanh_ht";
        } else {
            cnbc = "chi_nhanh_ht, vung_con, ma_buucuc_ht";
        }
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM all_ton_nhan_ban_giao WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDto detail = new TongHopTonDto();
                detail.setChiNhanh(rs.getString("chi_nhanh_ht"));
                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("ma_buucuc_ht"));
                    detail.setVungCon(rs.getString("vung_con"));
                }
                detail.setTonNhanBanGiao(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        logger.info("end getListTonNhanBanGiao ");

        return CompletableFuture.completedFuture(detailList);
    }

    @Override
    @Async
    public CompletableFuture<TongHopTonDto> getSumTonNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-nbg");
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT sum(tong_sl) as tong_sl " +
                "FROM all_ton_nhan_ban_giao WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "");

        System.out.println(".................SQL: " + sql);

        TongHopTonDto detail = new TongHopTonDto();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                detail.setTonNhanBanGiao(rs.getLong("tong_sl"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return CompletableFuture.completedFuture(detail);
    }

    @Override
    @Async
    public CompletableFuture<List<TongHopTonDto>> getListTonChuaCoHanhTrinh(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        logger.info("begin getListTonChuaCoHanhTrinh ");

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_chua_co_hanh_trinh");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "chi_nhanh_ht";
        } else {
            cnbc = "chi_nhanh_ht, ma_vung_con, ma_buucuc_ht";
        }
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM TON_CHUA_CO_HANH_TRINH_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDto detail = new TongHopTonDto();
                detail.setChiNhanh(rs.getString("chi_nhanh_ht"));
                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("ma_buucuc_ht"));
                    detail.setVungCon(rs.getString("ma_vung_con"));
                }
                detail.setTonChuaCoHanhTrinh(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        logger.info("end getListTonChuaCoHanhTrinh ");

        return CompletableFuture.completedFuture(detailList);
    }

    @Override
    @Async
    public CompletableFuture<List<TongHopTonDto>> getListTonDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        logger.info("begin getListTonDvGiaTang ");

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_dv_gia_tang");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "chi_nhanh_ht";
        } else {
            cnbc = "chi_nhanh_ht, ma_vung_con, ma_buucuc_ht";
        }
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM TON_DV_GIA_TANG_TONG_HOP_V2 WHERE ngay_baocao =  " + localDateToStringInteger(ngayBaoCao) + " and " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDto detail = new TongHopTonDto();
                detail.setChiNhanh(rs.getString("chi_nhanh_ht"));
                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("ma_buucuc_ht"));
                    detail.setVungCon(rs.getString("ma_vung_con"));
                }
                detail.setTonDvGiaTang(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        logger.info("end getListTonDvGiaTang ");

        return CompletableFuture.completedFuture(detailList);
    }

    @Override
    @Async
    public CompletableFuture<TongHopTonDto> getSumTonChuaCoHanhTrinh(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_chua_co_hanh_trinh");
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT sum(tong_sl) as tong_sl " +
                "FROM TON_CHUA_CO_HANH_TRINH_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "");

        System.out.println(".................SQL: " + sql);

        TongHopTonDto detail = new TongHopTonDto();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                detail.setTonChuaCoHanhTrinh(rs.getLong("tong_sl"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return CompletableFuture.completedFuture(detail);
    }

    @Override
    @Async
    public CompletableFuture<TongHopTonDto> getSumTrangThai(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_trang_thai");
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT sum(tong_sl) as tong_sl " +
                "FROM TON_TRANG_THAI_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                ((!listCN.isEmpty()) ? " and tinh_nhan in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "");

        System.out.println(".................SQL: " + sql);

        TongHopTonDto detail = new TongHopTonDto();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                detail.setTonHanhTrinh(rs.getLong("tong_sl"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return CompletableFuture.completedFuture(detail);
    }

    @Override
    @Async
    public CompletableFuture<TongHopTonDto> getSumChuaPCP(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_chua_pcp");
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT sum(tong_sl) as tong_sl " +
                "FROM TON_CHUA_PCP_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "")+
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "");

        System.out.println(".................SQL: " + sql);

        TongHopTonDto detail = new TongHopTonDto();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                detail.setTonChuaPCP(rs.getLong("tong_sl"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return CompletableFuture.completedFuture(detail);
    }

    @Override
    @Async
    public CompletableFuture<TongHopTonDto> getSumChuaNhapMay(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-bcg");
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT sum(tong_sl) as tong_sl, sum(sl_kien_cham) as sl_kiencham  " +
                "FROM ALL_TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
//                " version = " + version +
                sql_version +
                " and ngay_baocao = date '" + ngayBaoCao + "' " +
                (!listCN.isEmpty() ? " and tinh_nhan in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "");

        System.out.println(".................SQL: " + sql);

        TongHopTonDto detail = new TongHopTonDto();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                detail.setTonNhapMay(rs.getLong("tong_sl"));
                detail.setTonKienCham(rs.getLong("sl_kiencham"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return CompletableFuture.completedFuture(detail);
    }

    @Override
    @Async
    public CompletableFuture<TongHopTonDto> getSumDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_dv_gia_tang");
        String sql_version = StringCommon.convertVersion(version);

        String sql = " SELECT sum(tong_sl) as tong_sl " +
                "FROM TON_DV_GIA_TANG_TONG_HOP_V2 WHERE ngay_baocao = " + localDateToStringInteger(ngayBaoCao) + " and " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hang = '" + loaiHang + "'" : "");

        System.out.println(".................SQL: " + sql);

        TongHopTonDto detail = new TongHopTonDto();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                detail.setTonDvGiaTang(rs.getLong("tong_sl"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return CompletableFuture.completedFuture(detail);
    }

    @Override
    public List<TonHanhTrinhListingResponse> getDetailTrangThai(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan,  String loaiCanhBao) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_trang_thai");
        String order = "";

        order = " order by loai_don , kh_dacthu_gui, kh_dacthu_nhan ";

        if(!listBC.isEmpty() || !doiTac.equals("") || trangThai != 0 || !loaiHang.equals("") || isStatusNotNull(trangThaiList))
            order += ", trang_thai";

        StringBuilder sqlBuilder;
        if(version != null){
            sqlBuilder = new StringBuilder("SELECT * FROM TON_TRANG_THAI_CHI_TIET WHERE version = ")
                    .append(version);
        }
        else {
            sqlBuilder = new StringBuilder("SELECT * FROM TON_TRANG_THAI_CHI_TIET WHERE version is null ");
        }


        if (!listCN.isEmpty()) {
            sqlBuilder.append(" and tinh_nhan in ").append(StringUtils.list2StringQuery(listCN));
        }

        if (!vungCon.isEmpty()) {
            sqlBuilder.append(" and ma_vung_con = '").append(vungCon).append("'");
        }

        if (!listBC.isEmpty()) {
            sqlBuilder.append(" and MA_BUUCUC_GOC in ").append(StringUtils.list2StringQuery(listBC));
        }

        if (!doiTac.isEmpty()) {
            sqlBuilder.append(" and ma_doitac = '").append(doiTac).append("'");
        }

        if (trangThai != 0) {
            sqlBuilder.append(" and trang_thai = ").append(trangThai);
        }

        if (isStatusNotNull(trangThaiList)) {
            sqlBuilder.append(" and trang_thai in (").append(convertListToString(trangThaiList)).append(")");
        }

        if (!loaiHang.isEmpty()) {
            sqlBuilder.append(" and loai_hh = '").append(loaiHang).append("'");
        }

        if (!loaiDon.equals("all")) {
            sqlBuilder.append(" and loai_don = '").append(loaiDon).append("'");
        }

        if (!khDacThuGui.equals("0")) {
            sqlBuilder.append(" and kh_dacthu_gui = '").append(khDacThuGui).append("'");
        }

        if (!khDacThuNhan.equals("0")) {
            sqlBuilder.append(" and kh_dacthu_nhan = '").append(khDacThuNhan).append("'");
        }

        if(!(loaiCanhBao == null || loaiCanhBao.isEmpty())){
            sqlBuilder.append(" and loai_canh_bao = '").append(loaiCanhBao).append("'");
        }
        sqlBuilder
                .append(order)
                .append(" limit ").append(pageSize)
                .append(" offset ").append(page * pageSize);

        String sql = sqlBuilder.toString();

        System.out.println(".................SQL: " + sql);

        logger.info("+++++++++++++++++++++++++++++++++++ sql ++++++++++++++ : "+ sql);


        List<TonHanhTrinhListingResponse> detailList = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TonHanhTrinhListingResponse detail = new TonHanhTrinhListingResponse();
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setMaBuuCucGoc(rs.getString("ma_buucuc_goc"));
                detail.setChiNhanhGoc(rs.getString("tinh_nhan"));
                detail.setNgayGuiBP(StringUtils.longTimestamp2DateString(rs.getString("ngay_gui_bp")));
                detail.setNgayNhapMay(StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")));
                detail.setMaDvViettel(rs.getString("ma_dv_viettel"));
                detail.setVungCon(rs.getString("ma_vung_con"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    private String convertListToString(List<Integer> stringList) {
        return stringList.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    private boolean isStatusNotNull(List<Integer> trangThaiList) {
        return CollectionUtils.isNotEmpty(trangThaiList) && !trangThaiList.stream().allMatch(Objects::isNull);
    }

    @Override
    public List<TonHanhTrinhListingResponse> getDetailTrangThai(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException {
        return getDetailTrangThai(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, page, pageSize, loaiHang, null,"all","0","0", null);
    }

    @Override
    public List<String> getAllMaDoiTac(LocalDate ngayBaoCao) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        PreparedStatement stmt2 = null;
        ResultSet rs2 = null;
        PreparedStatement stmt3 = null;
        ResultSet rs3 = null;

        List<String> allKeyList = new ArrayList<>();

        String sql1 = " SELECT distinct ma_doitac as ma_doitac " +
                "FROM TON_NHAP_MAY_CHUA_KET_NOI";

        String sql2 = " SELECT distinct ma_doitac as ma_doitac " +
                "FROM TON_CHUA_PCP_CHI_TIET";

        String sql3 = " SELECT distinct ma_doitac as ma_doitac " +
                "FROM TON_TRANG_THAI_CHI_TIET";

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql1);
            rs = stmt.executeQuery();

            Map<String, String> list1 = new HashMap<>();
            while (rs.next()) {
                list1.put(rs.getString("ma_doitac"), "");
            }

            Map<String, String> list2 = new HashMap<>();
            stmt2 = conn.prepareStatement(sql2);
            rs2 = stmt2.executeQuery();

            while (rs2.next()) {
                list2.put(rs2.getString("ma_doitac"), "");
            }

            Map<String, String> list3 = new HashMap<>();
            stmt3 = conn.prepareStatement(sql3);
            rs3 = stmt3.executeQuery();

            while (rs3.next()) {
                list3.put(rs3.getString("ma_doitac"), "");
            }
            Set<String> allKeySet = Stream.of(list1.keySet(), list2.keySet(), list3.keySet()).flatMap(Collection::stream).collect(Collectors.toSet());

            allKeyList = new ArrayList<>(allKeySet);

        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
            releaseConnect(conn, stmt2, rs2);
            releaseConnect(conn, stmt3, rs3);
        }
        return allKeyList;
    }

    @Override
    public List<TonChuaPCPListingResponse> getDetailChuaPCP(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_chua_pcp");
        String order = "";
        if(!listBC.isEmpty() || !doiTac.equals("") || trangThai != 0 || !loaiHang.equals(""))
            order = " order by trang_thai ";
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM TON_CHUA_PCP_chi_tiet WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                order +
                " limit " + pageSize + " offset " + page * pageSize;

        System.out.println(".................SQL: " + sql);

        List<TonChuaPCPListingResponse> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TonChuaPCPListingResponse detail = new TonChuaPCPListingResponse();
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setChiNhanhGoc(rs.getString("tinh_nhan"));
                detail.setBuuCucHT(rs.getString("ma_buucuc_ht"));
                detail.setChiNhanhHT(rs.getString("chi_nhanh_ht"));
                detail.setTimeTacDong(StringUtils.longTimestamp2DateString(rs.getString("time_tac_dong")));
                detail.setNgayNhapMay(StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")));
                detail.setMaDvViettel(rs.getString("ma_dv_viettel"));
                detail.setVungCon(rs.getString("ma_vung_con"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<TonHanhTrinhListingResponse> getDetailChuaNhapMay(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang, Integer kienCham) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-bcg");
        String order = "";
        if(!listBC.isEmpty() || !doiTac.equals("") || trangThai != 0 || !loaiHang.equals("") || kienCham != 0)
            order = " order by trang_thai ";
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "") +
                (kienCham != 0 ? " and kien_cham = " + kienCham + "" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                order +
                " limit " + pageSize + " offset " + page * pageSize;

        System.out.println(".................SQL: " + sql);

        List<TonHanhTrinhListingResponse> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TonHanhTrinhListingResponse detail = new TonHanhTrinhListingResponse();
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setMaBuuCucGoc(rs.getString("ma_buucuc_ht"));
                detail.setChiNhanhGoc(rs.getString("chi_nhanh_ht"));
                detail.setNgayGuiBP(StringUtils.longTimestamp2DateString(rs.getString("ngay_gui_bp")));
                detail.setNgayNhapMay(StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")));
                detail.setMaDvViettel(rs.getString("dich_vu"));
                detail.setVungCon(rs.getString("vung_con"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    public String getSqlTonToanTrinh(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham, List<Integer> trangThaiList, String loaiDon, String khDacThuGui, String khDacThuNhan) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = Long.valueOf(0);
        String chiNhanh = "chi_nhanh_ht";
        String buuCuc = "ma_buucuc_ht";
        String vung = "ma_vung_con";
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ma_phieugui, tinh_nhan, huyen_nhan, ten_huyen_nhan, tinh_phat, huyen_phat, " +
                " ten_huyen_phat, ma_buucuc_goc, time_tac_dong , trang_thai, ma_buucuc_ht, chi_nhanh_ht," +
                "ma_doitac, ma_khgui, ma_buucuc_phat, ngay_gui_bp, tien_cod, nguoi_nhap_may, ngay_nhap_may, loai_hh, tien_cuoc ");

        switch (table) {
            case "TON_TRANG_THAI_CHI_TIET":
                sqlBuilder.append(", so_gio, updated_at ");
                version = getVersionMax(ngayBaoCao, "ton_trang_thai");
                sqlBuilder.append(" , MA_DV_CONG_THEM ");
                chiNhanh = "tinh_nhan";
                buuCuc = "ma_buucuc_goc";

                sqlBuilder.append(" , ma_dv_viettel  ");
                sqlBuilder.append(" , (case when kh_dacthu_gui = '1' then 'SVIP1, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                        "when kh_dacthu_gui = '2' then 'SVIP2, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                        "when kh_dacthu_gui = '3' then 'VIP-NG, KH đối ngoại, ngoại giao' " +
                        "when kh_dacthu_gui = '4' then 'VIP-KHL, KH thân thiết doanh thu lớn' " +
                        "when kh_dacthu_gui = '5' then 'VIP-KHHT, KH hệ thống, sàn TMĐT' " +
                        "when kh_dacthu_gui = '6' then 'ĐT-MXH, KH đặc thù - Báo chí, mạng xã hội' " +
                        "when kh_dacthu_gui = '7' then 'ĐT-KN, KH đặc thù, khiếu nại' " +
                        "else kh_dacthu_gui end) as kh_dacthu_gui ");
                sqlBuilder.append(" , (case when kh_dacthu_nhan = '1' then 'SVIP1, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                        "when kh_dacthu_nhan = '2' then 'SVIP2, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                        "when kh_dacthu_nhan = '3' then 'VIP-NG, KH đối ngoại, ngoại giao' " +
                        "when kh_dacthu_nhan = '4' then 'VIP-KHL, KH thân thiết doanh thu lớn' " +
                        "when kh_dacthu_nhan = '5' then 'VIP-KHHT, KH hệ thống, sàn TMĐT' " +
                        "when kh_dacthu_nhan = '6' then 'ĐT-MXH, KH đặc thù - Báo chí, mạng xã hội' " +
                        "when kh_dacthu_nhan = '7' then 'ĐT-KN, KH đặc thù, khiếu nại' " +
                        "else kh_dacthu_nhan end) as kh_dacthu_nhan ");
                sqlBuilder.append(" , loai_canh_bao ");

                break;
            case "TON_CHUA_PCP_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_chua_pcp");

                sqlBuilder.append(" , ma_dv_viettel ");
                sqlBuilder.append(", trong_luong");

                break;
            case "TON_NHAP_MAY_CHUA_KET_NOI":
                version = getVersionMax(ngayBaoCao, "ton-bcg");
                vung = "vung_con";

                sqlBuilder.append(" , dich_vu");


                break;
            case "TON_CHUA_CO_HANH_TRINH_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_chua_co_hanh_trinh");

                sqlBuilder.append(" , ma_dv_viettel ");
                sqlBuilder.append(", loai_canh_bao");
                sqlBuilder.append(", trong_luong");
                sqlBuilder.append(" , ma_dv_cong_them");

                break;
            case "TON_NHAN_BAN_GIAO":
                sqlBuilder.append( " , TEN_PHUONGXA_PHAT ");
                version = getVersionMax(ngayBaoCao, "ton-nbg");
                vung = "vung_con";

                sqlBuilder.append(", dich_vu ");
//                sqlBuilder.append(", trong_luong");
                sqlBuilder.append(", ma_dv_cong_them");
                sqlBuilder.append(",loai_canh_bao");

                break;
            default:
                break;
        }


//        sqlBuilder.append("SELECT * FROM ")
        if(version == null){
            sqlBuilder.append(" FROM ")
                    .append(table)
                    .append(" WHERE version is null ");
        }else {
            sqlBuilder.append(" FROM ")
                    .append(table)
                    .append(" WHERE version = ")
                    .append(version);
        }


        if (!listCN.isEmpty()) {
            sqlBuilder.append(" AND ")
                    .append(chiNhanh)
                    .append(" IN ")
                    .append(StringUtils.list2StringQuery(listCN));
        }

        if (!vungCon.equals("")) {
            sqlBuilder.append(" AND ")
                    .append(vung)
                    .append(" = '")
                    .append(vungCon)
                    .append("'");
        }

        if (!listBC.isEmpty()) {
            sqlBuilder.append(" AND ")
                    .append(buuCuc)
                    .append(" IN ")
                    .append(StringUtils.list2StringQuery(listBC));
        }

        if (!doiTac.equals("")) {
            sqlBuilder.append(" AND ma_doitac = '")
                    .append(doiTac)
                    .append("'");
        }

        if (!loaiHang.equals("")) {
            sqlBuilder.append(" AND loai_hh = '")
                    .append(loaiHang)
                    .append("'");
        }

        if (isStatusNotNull(trangThaiList)) {
            sqlBuilder.append(" AND trang_thai IN (")
                    .append(convertListToString(trangThaiList))
                    .append(")");
        }

        if (!loaiCanhBao.equals("")) {
            sqlBuilder.append(" AND LOAI_CANH_BAO = '")
                    .append(loaiCanhBao)
                    .append("'");
        }

        if (trangThai != 0) {
            sqlBuilder.append(" AND trang_thai = ")
                    .append(trangThai);
        }

        if (table.equals("TON_TRANG_THAI_CHI_TIET")) {
            if (!loaiDon.equals("all")) {
                sqlBuilder.append(" AND LOAI_DON = '")
                        .append(loaiDon)
                        .append("'");
            }

            if (!khDacThuGui.equals("0")) {
                sqlBuilder.append(" AND KH_DACTHU_GUI = '")
                        .append(khDacThuGui)
                        .append("'");
            }

            if (!khDacThuNhan.equals("0")) {
                sqlBuilder.append(" AND KH_DACTHU_NHAN = '")
                        .append(khDacThuNhan)
                        .append("'");
            }
        }

        if (kienCham != 0) {
            sqlBuilder.append(" AND kien_cham = ")
                    .append(kienCham);
        }

        return sqlBuilder.toString();
    }


    public Map<String, Boolean> fieldConvert(){
        Map<String,Boolean> dictConvert = new HashMap<>();
        dictConvert.put("time_tac_dong", true);
        dictConvert.put("ngay_gui_bp", true);
        dictConvert.put("ngay_nhap_may", true);

        dictConvert.put("TIME_TAC_DONG", true);
        dictConvert.put("NGAY_GUI_BP", true);
        dictConvert.put("NGAY_NHAP_MAY", true);
        dictConvert.put("NGAY_BAO_CAO", true);
        dictConvert.put("UPDATED_AT", true);

        return dictConvert;
    }
    @Override
    public ResponseEntity<?> exportExcelTonHanhTrinh(HttpServletResponse response,
                                                     LocalDate ngayBaoCao, String vungCon,
                                                     List<String> listCN, List<String> listBC,
                                                     String doiTac, Integer trangThai, String table,
                                                     String loaiHang, String loaiCanhBao, Integer kienCham,
                                                     List<Integer> trangThaiList, String loaiDon, String khDacThuGui, String khDacThuNhan, String tenBaoCao) throws SQLException, IOException {
        String sql = getSqlTonToanTrinh(
                ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, table, loaiHang,
                loaiCanhBao, kienCham, trangThaiList, loaiDon, khDacThuGui, khDacThuNhan
        );
        log.info("sql ton toan trinh "+ sql);
        if (sql == null || sql.isEmpty()){
            ZipHelper zipHelper = new ZipHelper();
            byte[] zippedBytes = zipHelper.zipStreams(new ArrayList<>(), tenBaoCao);
            log.info(" file trong ton_hanh_trinh ");
            zipHelper.returnZipFile(response, zippedBytes, tenBaoCao);
            return ResponseEntity.ok().build();
        }


        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + "ton_hanh_trinh" + ".xlsx";
        response.setHeader(headerKey, headerValue);

        Connection conn = null;
        HeaderExcel headerExcel = new HeaderExcel();
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try{
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            HeaderExcel finalHeaderExcel = headerExcel.getColumFromSql(rs.getMetaData());

            log.info("list header excel ton hanh trinh "+ finalHeaderExcel.getListHeader());
            log.info("map header excel ton hanh trinh  "+ finalHeaderExcel.getMapHeader());
            int numRow = 0;
            ExportExcelResulSet exportExcelResulSet = new ExportExcelResulSet(conn, stmt, rs, finalHeaderExcel.getMapHeader(), finalHeaderExcel.getListHeader(), finalHeaderExcel.getListHeader(), numRow, "sheet_1");

            if(table.equals("TON_TRANG_THAI_CHI_TIET")){
                exportExcelResulSet.writeDataToStreamTonHanhTrinh(LIMIT_SIZE, String.valueOf(ngayBaoCao), fieldConvert());
            }
            else {
                exportExcelResulSet.writeDataToStream(LIMIT_SIZE, String.valueOf(ngayBaoCao), fieldConvert());
            }



            List<ByteArrayOutputStream> outputStreamList = exportExcelResulSet.outputStreamList;
            if((outputStreamList == null || outputStreamList.isEmpty()) ){
                outputStreamList = new ArrayList<>();
            }

            ZipHelper zipHelper = new ZipHelper();
            byte[] zippedBytes = zipHelper.zipStreams(outputStreamList, tenBaoCao);

            log.info(" sau khi byte[] zippedBytes = zipStreams(outputStreamList, tenBaoCao) ");
            zipHelper.returnZipFile(response, zippedBytes, "ton_hanh_trinh");

            zippedBytes = null;
            outputStreamList.clear();
            exportExcelResulSet  = null;
            finalHeaderExcel = null;

        } catch (Exception e) {

            e.printStackTrace();
            eLogger.error("Error from excel_export_zip_not_thread, reason: {}", e);
            throw new ObjectException(" export excel ton hanh trinh fail ");
        } finally {
            releaseConnect(conn, stmt, rs);
            System.gc();
        }
        return ResponseEntity.ok().build();
    }

    //lấy data chung cho các loại tồn để xuất excel
    @Override
    public List<DashTonHanhTrinhDto> getDetailExcel(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = Long.valueOf(0);
        String chiNhanh = "chi_nhanh_ht";
        String buuCuc = "ma_buucuc_ht";
        String vung = "ma_vung_con";

        switch (table) {
            case "TON_TRANG_THAI_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_trang_thai");
                chiNhanh = "tinh_nhan";
                buuCuc = "ma_buucuc_goc";
                break;
            case "TON_CHUA_PCP_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_chua_pcp");
                break;
            case "TON_NHAP_MAY_CHUA_KET_NOI":
                version = getVersionMax(ngayBaoCao, "ton-bcg");
                vung = "vung_con";
                break;
            case "TON_CHUA_CO_HANH_TRINH_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_chua_co_hanh_trinh");
                break;
            case "TON_NHAN_BAN_GIAO":
                version = getVersionMax(ngayBaoCao, "ton-nbg");
                vung = "vung_con";
                break;
            default:
                break;
        }

        StringBuilder sqlBuilder = new StringBuilder();
        if(version != null){
            sqlBuilder.append("SELECT * FROM ")
                    .append(table)
                    .append(" WHERE version = ")
                    .append(version);
        }
        else {
            sqlBuilder.append("SELECT * FROM ")
                    .append(table)
                    .append(" WHERE version is null ");
        }

        if (!listCN.isEmpty()) {
            sqlBuilder.append(" AND ")
                    .append(chiNhanh)
                    .append(" IN ")
                    .append(StringUtils.list2StringQuery(listCN));
        }

        if (!vungCon.equals("")) {
            sqlBuilder.append(" AND ")
                    .append(vung)
                    .append(" = '")
                    .append(vungCon)
                    .append("'");
        }

        if (!listBC.isEmpty()) {
            sqlBuilder.append(" AND ")
                    .append(buuCuc)
                    .append(" IN ")
                    .append(StringUtils.list2StringQuery(listBC));
        }

        if (!doiTac.equals("")) {
            sqlBuilder.append(" AND ma_doitac = '")
                    .append(doiTac)
                    .append("'");
        }

        if (!loaiHang.equals("")) {
            sqlBuilder.append(" AND loai_hh = '")
                    .append(loaiHang)
                    .append("'");
        }

        if (isStatusNotNull(trangThaiList)) {
            sqlBuilder.append(" AND trang_thai IN (")
                    .append(convertListToString(trangThaiList))
                    .append(")");
        }

        if (!loaiCanhBao.equals("")) {
            sqlBuilder.append(" AND LOAI_CANH_BAO = '")
                    .append(loaiCanhBao)
                    .append("'");
        }

        if (trangThai != 0) {
            sqlBuilder.append(" AND trang_thai = ")
                    .append(trangThai);
        }

        if (table.equals("TON_TRANG_THAI_CHI_TIET")){
            if (!loaiDon.equals("all")) {
                sqlBuilder.append(" AND LOAI_DON = '")
                        .append(loaiDon)
                        .append("'");
            }

            if (!khDacThuGui.equals("0")) {
                sqlBuilder.append(" AND KH_DACTHU_GUI = '")
                        .append(khDacThuGui)
                        .append("'");
            }

            if (!khDacThuNhan.equals("0")) {
                sqlBuilder.append(" AND KH_DACTHU_NHAN = '")
                        .append(khDacThuNhan)
                        .append("'");
            }
        }

        if (kienCham != 0) {
            sqlBuilder.append(" AND kien_cham = ")
                    .append(kienCham);
        }

        String sql = sqlBuilder.toString();

//        String sql = " SELECT * " +
//                "FROM " + table + " WHERE " +
//                " version = " + version +
//                (!listCN.isEmpty() ? " and " + chiNhanh + " in " + StringUtils.list2StringQuery(listCN) + "" : "") +
//                (!vungCon.equals("") ? " and " + vung + " = '" + vungCon + "'" : "") +
//                (!listBC.isEmpty() ? " and " + buuCuc + " in " + StringUtils.list2StringQuery(listBC) + "" : "") +
//                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
//                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
//                (isStatusNotNull(trangThaiList) ? " and trang_thai in(" + convertListToString(trangThaiList) + ")" : "") +
//                (!loaiCanhBao.equals("") ? " and LOAI_CANH_BAO = '" + loaiCanhBao + "'" : "") +
//                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "") +
//                (kienCham != 0 ? " and kien_cham = " + kienCham + "" : "");

        System.out.println(".................SQL: " + sql);

        List<DashTonHanhTrinhDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                String dichVu = "";
                String trongLuong = "";
                String canhBao = "";
                String dvCongThem = "";
                String khDacthuGui = "";
                String khDacthuNhan = "";
                String nguoiTacDong = "";
                String buuTaPhat = "";
                switch (table) {
                    case "TON_TRANG_THAI_CHI_TIET":
                        dichVu = rs.getString("ma_dv_viettel");
                        khDacthuGui = rs.getString("kh_dacthu_gui");
                        khDacthuNhan = rs.getString("kh_dacthu_nhan");
                        canhBao = rs.getString("loai_canh_bao");
                        break;
                    case "TON_CHUA_PCP_CHI_TIET":
                        dvCongThem = rs.getString("MA_DV_CONG_THEM");
                        dichVu = rs.getString("ma_dv_viettel");
                        trongLuong = rs.getString("trong_luong");
                        break;
                    case "TON_NHAP_MAY_CHUA_KET_NOI":
                        dichVu = rs.getString("dich_vu");
                        dvCongThem = rs.getString("MA_DV_CONG_THEM");
                        break;
                    case "TON_CHUA_CO_HANH_TRINH_CHI_TIET":
                        dichVu = rs.getString("ma_dv_viettel");
                        canhBao = rs.getString("loai_canh_bao");
                        trongLuong = rs.getString("trong_luong");
                        dvCongThem = rs.getString("ma_dv_cong_them");
                        nguoiTacDong = rs.getString("NGUOI_TAC_DONG");
                        buuTaPhat = rs.getString("buu_ta_phat");
                        break;
                    case "TON_NHAN_BAN_GIAO":
                        dichVu = rs.getString("dich_vu");
                        trongLuong = rs.getString("trong_luong");
                        dvCongThem = rs.getString("ma_dv_cong_them");
                        canhBao = rs.getString("loai_canh_bao");
                        break;
                    default:
                        break;
                }

                DashTonHanhTrinhDto detail = new DashTonHanhTrinhDto(
                        ngayBaoCao.toString(),
                        rs.getString("ma_phieugui"),
                        rs.getString("tinh_nhan"),
                        rs.getString("huyen_nhan"),
                        rs.getString("ten_huyen_nhan"),
                        rs.getString("tinh_phat"),
                        rs.getString("huyen_phat"),
                        rs.getString("ten_huyen_phat"),
                        dichVu,
                        rs.getString("ma_buucuc_goc"),
                        StringUtils.longTimestamp2DateString(rs.getString("time_tac_dong")),
                        rs.getString("trang_thai"),
                        rs.getString("ma_buucuc_ht"),
                        rs.getString("chi_nhanh_ht"),
                        rs.getString("ma_doitac"),
                        rs.getString("ma_khgui"),
                        rs.getString("ma_buucuc_phat"),
                        StringUtils.longTimestamp2DateString(rs.getString("ngay_gui_bp")),
                        rs.getString("tien_cod"),
                        rs.getString("nguoi_nhap_may"),
                        StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")),
                        trongLuong,
                        rs.getString("loai_hh"),
                        rs.getString("tien_cuoc"),
                        canhBao == null || canhBao.equals("") ? null : canhBao,
                        dvCongThem,
                        khDacthuGui,
                        khDacthuNhan,
                        nguoiTacDong,
                        buuTaPhat
                );
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<DashTonHanhTrinhDto> getDetailExcel(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham) throws SQLException {
        return getDetailExcel(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, table, loaiHang, loaiCanhBao, kienCham, null,"all","0","0");
    }
    public String getSqlDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String loaiHang) throws SQLException {
        Long version  = getVersionMax(ngayBaoCao, "ton_dv_gia_tang");
        if(version == null) return null;
        String chiNhanh = "chi_nhanh_ht";
        String buuCuc = "ma_buucuc_ht";
        String vung = "ma_vung_con";

        String sql = " SELECT ma_phieugui, tinh_nhan, huyen_nhan, ten_huyen_nhan, tinh_phat, huyen_phat, ten_huyen_phat, ma_dv_viettel, ma_buucuc_goc , time_tac_dong" +
                ", trang_thai, ma_buucuc_ht, chi_nhanh_ht, dv_gia_tang, ma_doitac, ma_khgui, ma_buucuc_phat, ngay_gui_bp, tien_cod, nguoi_nhap_may, ngay_nhap_may, trong_luong, loai_hh, tien_cuoc " +
                "FROM TON_DV_GIA_TANG_CHI_TIET_V2 WHERE " +
                " version = " + version +
                (!listCN.isEmpty() ? " and " + chiNhanh + " in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and " + vung + " = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and " + buuCuc + " in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "");

        return sql;
    }

    @Override
    public ResponseEntity<?> exportExcelTonDvGiaTangToiUu(HttpServletResponse response, LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String loaiHang) throws SQLException, IOException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sql = getSqlDvGiaTang(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, loaiHang);
        if(sql == null){
            ZipHelper zipHelper = new ZipHelper();
            byte[] zippedBytes = zipHelper.zipStreams(new ArrayList<>(), "ton-dich-vu-gia-tang-excel");
            log.info(" file trống ton-dich-vu-gia-tang-excel  ");
            zipHelper.returnZipFile(response, zippedBytes, "ton-dich-vu-gia-tang-excel");
            return ResponseEntity.ok().build();
        }
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            HeaderExcel headerExcel = new HeaderExcel();
            HeaderExcel finalHeaderExcel = headerExcel.getColumFromSql(rs.getMetaData());
            int numRow = 0;
            ExportExcelResulSet exportExcelResulSet = new ExportExcelResulSet(conn, stmt, rs, finalHeaderExcel.getMapHeader(), finalHeaderExcel.getListHeader(), finalHeaderExcel.getListHeader(), numRow, "sheet_1");
            exportExcelResulSet.writeDataToStream(LIMIT_SIZE, String.valueOf(ngayBaoCao), fieldConvert());

            List<ByteArrayOutputStream> outputStreamList = exportExcelResulSet.outputStreamList;
            if((outputStreamList == null || outputStreamList.isEmpty()) ){
                outputStreamList = new ArrayList<>();
            }

            ZipHelper zipHelper = new ZipHelper();
            byte[] zippedBytes = zipHelper.zipStreams(outputStreamList, "ton_dich_vu_gia_tang");

            log.info(" sau khi byte[] zippedBytes = zipStreams(outputStreamList, tenBaoCao) ");
            zipHelper.returnZipFile(response, zippedBytes, "ton_dich_vu_gia_tang");

            zippedBytes = null;
            outputStreamList.clear();
            exportExcelResulSet  = null;
            finalHeaderExcel = null;

        } catch (Exception e) {

            e.printStackTrace();
            eLogger.error("Error from excel_export_zip_not_thread, reason: {}", e);
            throw new ObjectException(" export excel ton dv gia tang fail ");
        } finally {
            releaseConnect(conn, stmt, rs);
            System.gc();
        }
        return ResponseEntity.ok().build();
    }


    @Override
    public List<TonDvGiaTangExcelDto> getDetailExcelTonDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version  = getVersionMax(ngayBaoCao, "ton_dv_gia_tang");
        String chiNhanh = "chi_nhanh_ht";
        String buuCuc = "ma_buucuc_ht";
        String vung = "ma_vung_con";
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM TON_DV_GIA_TANG_CHI_TIET_V2 WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and " + chiNhanh + " in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and " + vung + " = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and " + buuCuc + " in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "");

        System.out.println(".................SQL: " + sql);

        List<TonDvGiaTangExcelDto> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TonDvGiaTangExcelDto detail = new TonDvGiaTangExcelDto(
                        ngayBaoCao.toString(),
                        rs.getString("ma_phieugui"),
                        rs.getString("tinh_nhan"),
                        rs.getString("huyen_nhan"),
                        rs.getString("ten_huyen_nhan"),
                        rs.getString("tinh_phat"),
                        rs.getString("huyen_phat"),
                        rs.getString("ten_huyen_phat"),
                        rs.getString("ma_dv_viettel"),
                        rs.getString("ma_buucuc_goc"),
                        StringUtils.longTimestamp2DateString(rs.getString("time_tac_dong")),
                        rs.getString("trang_thai"),
                        rs.getString("ma_buucuc_ht"),
                        rs.getString("chi_nhanh_ht"),
                        rs.getString("dv_gia_tang"),
                        rs.getString("ma_doitac"),
                        rs.getString("ma_khgui"),
                        rs.getString("ma_buucuc_phat"),
                        StringUtils.longTimestamp2DateString(rs.getString("ngay_gui_bp")),
                        rs.getLong("tien_cod"),
                        rs.getString("nguoi_nhap_may"),
                        StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")),
                        rs.getDouble("trong_luong"),
                        rs.getString("loai_hh"),
                        rs.getLong("tien_cuoc")
                );
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public Long getTotalDetail(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = Long.valueOf(0);
        String chiNhanh = "chi_nhanh_ht";
        String buuCuc = "ma_buucuc_ht";
        String vung = "ma_vung_con";

        switch (table) {
            case "TON_TRANG_THAI_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_trang_thai");
                chiNhanh = "tinh_nhan";
                buuCuc = "ma_buucuc_goc";
                break;
            case "TON_CHUA_PCP_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_chua_pcp");
                break;
            case "TON_NHAP_MAY_CHUA_KET_NOI":
                version = getVersionMax(ngayBaoCao, "ton-bcg");
                vung = "vung_con";
                break;
            case "TON_CHUA_CO_HANH_TRINH_CHI_TIET":
                version = getVersionMax(ngayBaoCao, "ton_chua_co_hanh_trinh");
                break;
            case "TON_NHAN_BAN_GIAO":
                version = getVersionMax(ngayBaoCao, "ton-nbg");
                vung = "vung_con";
                break;
            case "TON_DV_GIA_TANG_CHI_TIET_V2":
                version = getVersionMax(ngayBaoCao, "ton_dv_gia_tang");
                break;
            default:
                break;
        }

        StringBuilder sqlBuilder;
        if(version != null){
            sqlBuilder = new StringBuilder("SELECT count(*) as total FROM ")
                    .append(table)
                    .append(" WHERE version = ")
                    .append(version);
        }
        else {
            sqlBuilder = new StringBuilder("SELECT count(*) as total FROM ")
                    .append(table)
                    .append(" WHERE version is null ");
        }

        if (!listCN.isEmpty()) {
            sqlBuilder.append(" and ").append(chiNhanh).append(" in ").append(StringUtils.list2StringQuery(listCN));
        }

        if (!vungCon.isEmpty()) {
            sqlBuilder.append(" and ").append(vung).append(" = '").append(vungCon).append("'");
        }

        if (!listBC.isEmpty()) {
            sqlBuilder.append(" and ").append(buuCuc).append(" in ").append(StringUtils.list2StringQuery(listBC));
        }

        if (!doiTac.isEmpty()) {
            sqlBuilder.append(" and ma_doitac = '").append(doiTac).append("'");
        }

        if (!loaiHang.isEmpty()) {
            sqlBuilder.append(" and loai_hh = '").append(loaiHang).append("'");
        }

        if (isStatusNotNull(trangThaiList)) {
            sqlBuilder.append(" and trang_thai in (").append(convertListToString(trangThaiList)).append(")");
        }

        if (!(loaiCanhBao == null || loaiCanhBao.isEmpty())) {
            sqlBuilder.append(" and LOAI_CANH_BAO = '").append(loaiCanhBao).append("'");
        }

        if (kienCham != 0) {
            sqlBuilder.append(" and kien_cham = ").append(kienCham);
        }

        if (trangThai != 0) {
            sqlBuilder.append(" and trang_thai = ").append(trangThai);
        }

        if (!loaiDon.equals("all")) {
            sqlBuilder.append(" and loai_don = '")
                    .append(loaiDon)
                    .append("'");
        }

        if (!khDacThuGui.equals("0")) {
            sqlBuilder.append(" and kh_dacthu_gui = '")
                    .append(khDacThuGui)
                    .append("'");
        }

        if (!khDacThuNhan.equals("0")) {
            sqlBuilder.append(" and kh_dacthu_nhan = '")
                    .append(khDacThuNhan)
                    .append("'");
        }

        String sql = sqlBuilder.toString();



        System.out.println(".................SQL: " + sql);

        List<DashTonHanhTrinhDto> detailList = new ArrayList<>();
        Long total = Long.valueOf(0);

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            if (rs.next()) {
                total = rs.getLong("total");
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return total;
    }

    @Override
    public Long getTotalDetail(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham ) throws SQLException {
        return getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, table, loaiHang, loaiCanhBao, kienCham, null,"all","0","0");
    }

    @Override
    public TonDetailBillResponse getDetailMaPhieuGui(String maPhieuGui, String table) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = Long.valueOf(0);

        switch (table) {
            case "TON_TRANG_THAI_CHI_TIET":
                version = getVersionMax(LocalDate.now(), "ton_trang_thai");
                break;
            case "TON_CHUA_PCP_CHI_TIET":
                version = getVersionMax(LocalDate.now(), "ton_chua_pcp");
                break;
            case "TON_NHAP_MAY_CHUA_KET_NOI":
                version = getVersionMax(LocalDate.now(), "ton-bcg");
                break;
            default:
                break;
        }
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM " + table + " WHERE " +
//                " version = " + version +
                sql_version +
                " and ma_phieugui = '" + maPhieuGui + "'";

        System.out.println(".................SQL: " + sql);
        TonDetailBillResponse detail = new TonDetailBillResponse();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setBuuCucHienTai(rs.getString("ma_buucuc_ht"));
//                detail.setLoaiDichVu(rs.getString("nhom_dv"));
                switch (table) {
                    case "TON_TRANG_THAI_CHI_TIET":
                        detail.setChiNhanh(rs.getString("tinh_nhan"));
                        detail.setBuuCucNhan(rs.getString("huyen_nhan"));
                        detail.setMaDichVu(rs.getString("ma_dv_viettel"));
                        detail.setTenBaoCao("Tồn trạng thái");
                        break;
                    case "TON_CHUA_PCP_CHI_TIET":
                        detail.setChiNhanh(rs.getString("chi_nhanh_ht"));
                        detail.setBuuCucNhan(rs.getString("ma_buucuc_ht"));
                        detail.setMaDichVu(rs.getString("ma_dv_viettel"));
                        detail.setTenBaoCao("Tồn chưa PCP");
                        break;
                    case "TON_NHAP_MAY_CHUA_KET_NOI":
                        detail.setChiNhanh(rs.getString("tinh_nhan"));
                        detail.setBuuCucNhan(rs.getString("huyen_nhan"));
                        detail.setMaDichVu(rs.getString("dich_vu"));
                        detail.setTenBaoCao("Tồn nhập máy chưa kết nối");
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detail;
    }

    @Override
    public DashboardTongTonDto getTongTon(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String table) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = Long.valueOf(0);
        String chiNhanhRow = "chi_nhanh_ht";
        String buuCucRow = "ma_buucuc_ht";

        switch (table) {
            case "TON_TRANG_THAI_TONG_HOP":
                version = getVersionMax(ngayBaoCao, "ton_trang_thai");
                chiNhanhRow = "tinh_nhan";
                buuCucRow = "ma_buucuc_goc";
                break;
            case "TON_CHUA_PCP_TONG_HOP":
                version = getVersionMax(ngayBaoCao, "ton_chua_pcp");
                break;
            case "ALL_TON_NHAP_MAY_CHUA_KET_NOI":
                version = getVersionMax(ngayBaoCao, "ton-bcg");
                chiNhanhRow = "tinh_nhan";
                buuCucRow = "ma_buucuc_goc";
                break;
            case "TON_CHUA_CO_HANH_TRINH_TONG_HOP":
                version = getVersionMax(ngayBaoCao, "ton_chua_co_hanh_trinh");
                break;
            case "ALL_TON_NHAN_BAN_GIAO":
                version = getVersionMax(ngayBaoCao, "ton-nbg");
                break;
            default:
                break;
        }
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT updated_at, sum(tong_sl) as tong_sl " +
                "FROM " + table + " WHERE " +
//                " version = " + version +
                sql_version +
                (!chiNhanh.equals("") ? " and " + chiNhanhRow + " = '" + chiNhanh + "'" : "") +
                (!buuCuc.equals("") ? " and " + buuCucRow + " = '" + buuCuc + "'" : "") +
                " group by updated_at";

        System.out.println(".................SQL: " + sql);

        DashboardTongTonDto result = new DashboardTongTonDto();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                result.setSl(rs.getLong("tong_sl"));
                result.setUpdatedAt(StringUtils.longTimestamp2DateString(rs.getString("updated_at")));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return result;
    }

    @Override
    public List<TongHopTonDtoNoVungCon> getListTrangThaiMobile(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_trang_thai");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "tinh_nhan";
        } else {
            cnbc = "tinh_nhan, ma_buucuc_goc";
        }
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM TON_TRANG_THAI_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                ((!listCN.isEmpty()) ? " and tinh_nhan in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDtoNoVungCon> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDtoNoVungCon detail = new TongHopTonDtoNoVungCon();
                detail.setChiNhanh(rs.getString("tinh_nhan"));

                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("ma_buucuc_goc"));
                }
                detail.setTonHanhTrinh(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<TongHopTonDtoNoVungCon> getListChuaPCPMobile(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_chua_pcp");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "chi_nhanh_ht";
        } else {
            cnbc = "chi_nhanh_ht,  ma_buucuc_ht";
        }
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM TON_CHUA_PCP_TONG_HOP WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDtoNoVungCon> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDtoNoVungCon detail = new TongHopTonDtoNoVungCon();
                detail.setChiNhanh(rs.getString("chi_nhanh_ht"));
                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("ma_buucuc_ht"));
                }
                detail.setTonChuaPCP(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<TongHopTonDtoNoVungCon> getListChuaNhapMayMobile(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-bcg");
        String cnbc;

        if (maChiNhanh.equals("")) {
            cnbc = "tinh_nhan";
        } else {
            cnbc = "tinh_nhan, MA_BUUCUC_GOC";
        }
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT " + cnbc + ", " +
                "sum(tong_sl) as tong_sl " +
                "FROM ALL_TON_NHAP_MAY_CHUA_KET_NOI WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and tinh_nhan in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and MA_BUUCUC_GOC in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                " group by " + cnbc;

        System.out.println(".................SQL: " + sql);

        List<TongHopTonDtoNoVungCon> detailList = new ArrayList<>();

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);

            rs = stmt.executeQuery();
            while (rs.next()) {
                TongHopTonDtoNoVungCon detail = new TongHopTonDtoNoVungCon();
                detail.setChiNhanh(rs.getString("tinh_nhan"));
                if (!maChiNhanh.equals("")) {
                    detail.setBuuCuc(rs.getString("MA_BUUCUC_GOC"));
                }
                detail.setTonNhapMay(rs.getLong("tong_sl"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<TonChuaPCPListingResponse> getDetailHanhTrinh(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac,
                                                              Integer trangThai, Integer page, Integer pageSize, String loaiHang, String loaiCanhBao) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_chua_co_hanh_trinh");

        String order = "";
        if(!listBC.isEmpty() || !doiTac.equals("") || trangThai != 0 || !loaiHang.equals("") || !loaiCanhBao.equals(""))
            order = " order by trang_thai ";
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM ton_chua_co_hanh_trinh_chi_tiet WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                (!loaiCanhBao.equals("") ? " and LOAI_CANH_BAO = '" + loaiCanhBao + "'" : "") +
                order +
                " limit " + pageSize + " offset " + page * pageSize;

        System.out.println(".................SQL: " + sql);

        List<TonChuaPCPListingResponse> detailList = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TonChuaPCPListingResponse detail = new TonChuaPCPListingResponse();
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setLoaiCanhBao(rs.getString("LOAI_CANH_BAO"));
                detail.setBuuCucHT(rs.getString("ma_buucuc_ht"));
                detail.setChiNhanhHT(rs.getString("chi_nhanh_ht"));
                detail.setTimeTacDong(StringUtils.longTimestamp2DateString(rs.getString("TIME_TAC_DONG")));
                detail.setNgayNhapMay(StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")));
                detail.setMaDvViettel(rs.getString("ma_dv_viettel"));
                detail.setVungCon(rs.getString("ma_vung_con"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<TonDvGiaTangListingResponse> getDetailDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac,
                                                                Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton_dv_gia_tang");

        String order = " ";
        if(!listBC.isEmpty() || !doiTac.equals("") || trangThai != 0 || !loaiHang.equals(""))
            order = " order by trang_thai ";
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM TON_DV_GIA_TANG_CHI_TIET_V2 WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and ma_vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                order +
                " limit " + pageSize + " offset " + page * pageSize;

        System.out.println(".................SQL: " + sql);

        List<TonDvGiaTangListingResponse> detailList = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TonDvGiaTangListingResponse detail = new TonDvGiaTangListingResponse();
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setChiNhanhGoc(rs.getString("ma_chinhanh_goc"));
                detail.setChiNhanhHT(rs.getString("chi_nhanh_ht"));
                detail.setBuuCucGoc(rs.getString("ma_buucuc_goc"));
                detail.setBuuCucHT(rs.getString("ma_buucuc_ht"));
                detail.setTimeTacDong(StringUtils.longTimestamp2DateString(rs.getString("TIME_TAC_DONG")));
                detail.setNgayNhapMay(StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")));
                detail.setMaDvViettel(rs.getString("ma_dv_viettel"));
                detail.setMaDvGiaTang(rs.getString("dv_gia_tang"));
                detail.setVungCon(rs.getString("ma_vung_con"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }

    @Override
    public List<TonChuaPCPListingResponse> getDetailNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac,
                                                                Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-nbg");

        String order = "";
        if(!listBC.isEmpty() || !doiTac.equals("") || trangThai != 0 || !loaiHang.equals(""))
            order = " order by trang_thai ";
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM ton_nhan_ban_giao WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                order +
                " limit " + pageSize + " offset " + page * pageSize;

        System.out.println(".................SQL: " + sql);

        List<TonChuaPCPListingResponse> detailList = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TonChuaPCPListingResponse detail = new TonChuaPCPListingResponse();
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setBuuCucHT(rs.getString("ma_buucuc_ht"));
                detail.setChiNhanhHT(rs.getString("chi_nhanh_ht"));
                detail.setTimeTacDong(StringUtils.longTimestamp2DateString(rs.getString("TIME_TAC_DONG")));
                detail.setNgayNhapMay(StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")));
                detail.setMaDvViettel(rs.getString("dich_vu"));
                detail.setVungCon(rs.getString("vung_con"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }


    @Override
    public List<TonChuaPCPListingResponse> getDetailNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac,
                                                                Integer trangThai, Integer page, Integer pageSize, String loaiHang, String loaiCanhBao) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long version = getVersionMax(ngayBaoCao, "ton-nbg");

        String order = "";
        if(!listBC.isEmpty() || !doiTac.equals("") || trangThai != 0 || !loaiHang.equals("") || !loaiCanhBao.equals(""))
            order = " order by trang_thai ";
        String sql_version = StringCommon.convertVersion(version);
        String sql = " SELECT * " +
                "FROM ton_nhan_ban_giao WHERE " +
//                " version = " + version +
                sql_version +
                (!listCN.isEmpty() ? " and chi_nhanh_ht in " + StringUtils.list2StringQuery(listCN) + "" : "") +
                (!vungCon.equals("") ? " and vung_con = '" + vungCon + "'" : "") +
                (!listBC.isEmpty() ? " and ma_buucuc_ht in " + StringUtils.list2StringQuery(listBC) + "" : "") +
                (!doiTac.equals("") ? " and ma_doitac = '" + doiTac + "'" : "") +
                (trangThai != 0 ? " and trang_thai = " + trangThai + "" : "") +
                (!loaiHang.equals("") ? " and loai_hh = '" + loaiHang + "'" : "") +
                (!loaiCanhBao.equals("") ? " and loai_canh_bao = '" + loaiCanhBao + "'" : "") +
                order +
                " limit " + pageSize + " offset " + page * pageSize;

        System.out.println(".................SQL: " + sql);

        List<TonChuaPCPListingResponse> detailList = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TonChuaPCPListingResponse detail = new TonChuaPCPListingResponse();
                detail.setMaPhieuGui(rs.getString("ma_phieugui"));
                detail.setTrangThai(rs.getString("trang_thai"));
                detail.setBuuCucHT(rs.getString("ma_buucuc_ht"));
                detail.setChiNhanhHT(rs.getString("chi_nhanh_ht"));
                detail.setTimeTacDong(StringUtils.longTimestamp2DateString(rs.getString("TIME_TAC_DONG")));
                detail.setNgayNhapMay(StringUtils.longTimestamp2DateString(rs.getString("ngay_nhap_may")));
                detail.setMaDvViettel(rs.getString("dich_vu"));
                detail.setVungCon(rs.getString("vung_con"));
                detailList.add(detail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return detailList;
    }


    public Long getVersionMax(LocalDate ngayBaoCao, String loaiBC) {
        logger.info("begin get version ");
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT version from chiso_version where ngay_baocao = date '" + ngayBaoCao + "' and ma_chiso = '" + loaiBC + "'";
        System.out.println(sqlMax);
        Long maxVersion = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();

            while (rs.next()) {
                maxVersion = rs.getLong("version");
//                mapVersion.put(date,maxVersion);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        logger.info("end get version ");
        return maxVersion;
    }

    private final Logger logger = LoggerFactory.getLogger(TonKhacDAOImpl.class);

}
