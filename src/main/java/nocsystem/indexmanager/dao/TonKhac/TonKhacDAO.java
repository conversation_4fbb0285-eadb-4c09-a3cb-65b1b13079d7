package nocsystem.indexmanager.dao.TonKhac;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.*;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface TonKhacDAO {
    CompletableFuture<List<TongHopTonDto>> getListTrangThai
            (LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<List<TongHopTonDto>> getListChuaPCP(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuu<PERSON><PERSON>, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<List<TongHopTonDto>> getListChuaNhapMay(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<List<TongHopTonDto>> getListTonNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<TongHopTonDto> getSumTonNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<List<TongHopTonDto>> getListTonChuaCoHanhTrinh(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<List<TongHopTonDto>> getListTonDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<TongHopTonDto> getSumTonChuaCoHanhTrinh(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<TongHopTonDto> getSumTrangThai(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<TongHopTonDto> getSumChuaPCP(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<TongHopTonDto> getSumChuaNhapMay(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    CompletableFuture<TongHopTonDto> getSumDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, String maChiNhanh, String loaiHang) throws SQLException;

    List<TonHanhTrinhListingResponse> getDetailTrangThai(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException;
    List<TonHanhTrinhListingResponse> getDetailTrangThai(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan, String loaiCanhBao) throws SQLException;
    List<TonHanhTrinhListingResponse> getDetailChuaNhapMay(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang, Integer kienCham) throws SQLException;

    List<TonChuaPCPListingResponse> getDetailChuaPCP(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException;

    List<TonDvGiaTangListingResponse> getDetailDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException;

    List<TongHopTonDtoNoVungCon> getListTrangThaiMobile
            (LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh) throws SQLException;

    DashboardTongTonDto getTongTon(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String table) throws SQLException;

    List<TongHopTonDtoNoVungCon> getListChuaPCPMobile(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh) throws SQLException;

    List<TongHopTonDtoNoVungCon> getListChuaNhapMayMobile(LocalDate ngayBaoCao, String vungCon, List<String> listChiNhanh, List<String> maBuuCuc, String doiTac, String maChiNhanh) throws SQLException;

    List<String> getAllMaDoiTac(LocalDate ngayBaoCao) throws SQLException;

    List<DashTonHanhTrinhDto> getDetailExcel(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham) throws SQLException;
    List<DashTonHanhTrinhDto> getDetailExcel(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan) throws SQLException;

    ResponseEntity<?> exportExcelTonHanhTrinh(HttpServletResponse response, LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham, List<Integer> trangThaiList, String loaiDon, String khDacThuGui, String khDacThuNhan, String tenBaoCao) throws SQLException, IOException;
    List<TonDvGiaTangExcelDto> getDetailExcelTonDvGiaTang(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String loaiHang) throws SQLException;
    ResponseEntity<?> exportExcelTonDvGiaTangToiUu(HttpServletResponse response, LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String loaiHang) throws SQLException, IOException;

    Long getTotalDetail(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham) throws SQLException;
    Long getTotalDetail(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, String table, String loaiHang, String loaiCanhBao, Integer kienCham, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan) throws SQLException;

    TonDetailBillResponse getDetailMaPhieuGui(String maPhieuGui, String table) throws SQLException;

    List<TonChuaPCPListingResponse> getDetailHanhTrinh(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac, Integer trangThai, Integer page, Integer pageSize, String loaiHang, String loaiCanhBao) throws SQLException;

    List<TonChuaPCPListingResponse> getDetailNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac,
                                                         Integer trangThai, Integer page, Integer pageSize, String loaiHang) throws SQLException;


    List<TonChuaPCPListingResponse> getDetailNhanBanGiao(LocalDate ngayBaoCao, String vungCon, List<String> listCN, List<String> listBC, String doiTac,
                                                         Integer trangThai, Integer page, Integer pageSize, String loaiHang, String loaiCanhBao) throws SQLException;

}
