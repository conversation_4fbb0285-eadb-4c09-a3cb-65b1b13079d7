package nocsystem.indexmanager.dao.LOG;

import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacExcel;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacResponse;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacTrinhKyExcel;

public interface DoanhThuKhaiThacDAO {
    List<DoanhThuKhaiThacResponse> doanhThuKhaiThac(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu, Integer page, Integer pageSize) throws SQLException;
    Long doanhThuKhaiThacTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException;
    DoanhThuKhaiThacResponse doanhThuKhaiThacTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException;
    List<DoanhThuKhaiThacExcel> exportExcelDoanhThuKhaiThac(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException;
    List<DoanhThuKhaiThacTrinhKyExcel> exportExcelTrinhKyDoanhThu(LocalDate ngayBaoCao, Integer luyKe) throws SQLException;

}
