package nocsystem.indexmanager.dao.LOG;

import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.constants.Constant;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacExcel;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacResponse;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacTrinhKyExcel;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static nocsystem.indexmanager.util.StringUtil.castListToQueryParam;
import static nocsystem.indexmanager.util.TimeUtils.localDateToStringInteger;
@Slf4j
@Service
public class DoanhThuKhaiThacDAOImpl extends AbstractDao implements DoanhThuKhaiThacDAO {

    @Override
    public List<DoanhThuKhaiThacResponse> doanhThuKhaiThac(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu, Integer page, Integer pageSize) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterChiNhanh = !chiNhanh.equals("") ? " and chinhanh_goc = '" + chiNhanh + "'" : "";
        String filterBuuCuc = !buuCuc.equals("") ? " and buucuc_goc = '" + buuCuc + "'" : "";
        String filterTTKT = !ttkt.equals("") ? " and ma_ttkt = '" + ttkt + "'" : "";
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterChiNhanhSso = !chiNhanhSso.isEmpty() ? " and chinhanh_goc in " + castListToQueryParam(chiNhanhSso) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and buucuc_goc in " + castListToQueryParam(buuCucSso) : "";

        String sql;
        if (chiNhanh.equals("")) {
            sql = "select chinhanh_goc, " +
                    "round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(dt_sanluong_nho), 2) as dt_sanluong_nho, " +
                    "round(sum(dt_trongluong_nho), 2) as dt_trongluong_nho, round(sum(tong_doanhthu_nho), 2) as tong_doanhthu_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                    "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(dt_sanluong_lon), 2) as dt_sanluong_lon, round(sum(dt_trongluong_lon), 2) as dt_trongluong_lon, " +
                    "round(sum(tong_doanhthu_lon), 2) as tong_doanhthu_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                    "round(sum(tong_trongluong_dtkt), 2) as tong_trongluong_dtkt, round(sum(tong_dt_sanluong), 2) as tong_dt_sanluong, " +
                    "round(sum(tong_dt_trongluong), 2) as tong_dt_trongluong, round(sum(tong_doanhthu), 2) as tong_doanhthu " +
                    "from LOG_BCDTKT_TH " +
                    "where " + filterDate + filterChiNhanh + filterBuuCuc + filterChiNhanhSso + filterBuuCucSso + filterMaDV + filterTTKT + filterTTKTCha
                    + " group by chinhanh_goc"
                    + " limit " + pageSize + " offset " + page * pageSize;
        } else {
            sql = "select chinhanh_goc, buucuc_goc, " +
                    "round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(dt_sanluong_nho), 2) as dt_sanluong_nho, " +
                    "round(sum(dt_trongluong_nho), 2) as dt_trongluong_nho, round(sum(tong_doanhthu_nho), 2) as tong_doanhthu_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                    "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(dt_sanluong_lon), 2) as dt_sanluong_lon, round(sum(dt_trongluong_lon), 2) as dt_trongluong_lon, " +
                    "round(sum(tong_doanhthu_lon), 2) as tong_doanhthu_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                    "round(sum(tong_trongluong_dtkt), 2) as tong_trongluong_dtkt, round(sum(tong_dt_sanluong), 2) as tong_dt_sanluong, " +
                    "round(sum(tong_dt_trongluong), 2) as tong_dt_trongluong, round(sum(tong_doanhthu), 2) as tong_doanhthu " +
                    "from LOG_BCDTKT_TH " +
                    "where " + filterDate + filterChiNhanh + filterBuuCuc + filterChiNhanhSso + filterBuuCucSso + filterMaDV + filterTTKT + filterTTKTCha
                    + " group by chinhanh_goc, buucuc_goc"
                    + " limit " + pageSize + " offset " + page * pageSize;
        }

        System.out.println(".................SQL: " + sql);

        List<DoanhThuKhaiThacResponse> listResponse = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                DoanhThuKhaiThacResponse response = new DoanhThuKhaiThacResponse();
                response.setChiNhanh(rs.getString("chinhanh_goc"));
                if (!chiNhanh.equals("")) {
                    response.setBuuCuc(rs.getString("buucuc_goc"));
                }
                response.setSlNho(rs.getLong("sanluong_nho"));
                response.setTlNho(rs.getDouble("trongluong_nho"));
                response.setDoanhThuSlNho(rs.getDouble("dt_sanluong_nho"));
                response.setDoanhThuTlNho(rs.getDouble("dt_trongluong_nho"));
                response.setTongDoanhThuNho(rs.getDouble("tong_doanhthu_nho"));
                response.setSlLon(rs.getLong("sanluong_lon"));
                response.setTlLon(rs.getDouble("trongluong_lon"));
                response.setDoanhThuSlLon(rs.getDouble("dt_sanluong_lon"));
                response.setDoanhThuTlLon(rs.getDouble("dt_trongluong_lon"));
                response.setTongDoanhThuLon(rs.getDouble("tong_doanhthu_lon"));
                response.setTongSl(rs.getLong("tong_sanluong"));
                response.setTongTl(rs.getDouble("tong_trongluong"));
                response.setTongTlDTKT(rs.getDouble("tong_trongluong_dtkt"));
                response.setTongDoanhThuSl(rs.getLong("tong_dt_sanluong"));
                response.setTongDoanhThuTl(rs.getDouble("tong_dt_trongluong"));
                response.setTongDoanhThuTruocKpi(rs.getDouble("tong_doanhthu"));
                response.setTongDoanhThuSauKpi(rs.getDouble("tong_doanhthu"));

                listResponse.add(response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get doanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listResponse;
    }

    @Override
    public Long doanhThuKhaiThacTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Long result = 0L;
        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterChiNhanh = !chiNhanh.equals("") ? " and chinhanh_goc = '" + chiNhanh + "'" : "";
        String filterBuuCuc = !buuCuc.equals("") ? " and buucuc_goc = '" + buuCuc + "'" : "";
        String filterTTKT = !ttkt.equals("") ? " and ma_ttkt = '" + ttkt + "'" : "";
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterChiNhanhSso = !chiNhanhSso.isEmpty() ? " and chinhanh_goc in " + castListToQueryParam(chiNhanhSso) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and buucuc_goc in " + castListToQueryParam(buuCucSso) : "";

        String sql;
        if (chiNhanh.equals("")) {
            sql = "select count(*) as count from " +
                    "(select chinhanh_goc, " +
                    "sum(sanluong_nho), sum(trongluong_nho), sum(dt_sanluong_nho), " +
                    "sum(dt_trongluong_nho), sum(tong_doanhthu_nho), sum(sanluong_lon), " +
                    "sum(trongluong_lon), sum(dt_sanluong_lon), sum(dt_trongluong_lon), " +
                    "sum(tong_doanhthu_lon), sum(tong_sanluong), sum(tong_trongluong), " +
                    "sum(tong_trongluong_dtkt), sum(tong_dt_sanluong), " +
                    "sum(tong_dt_trongluong), sum(tong_doanhthu) " +
                    "from LOG_BCDTKT_TH " +
                    "where " + filterDate + filterChiNhanh + filterBuuCuc + filterChiNhanhSso + filterBuuCucSso + filterMaDV + filterTTKT + filterTTKTCha +
                    " group by chinhanh_goc)";
        } else {
            sql = "select count(*) as count from " +
                    "(select chinhanh_goc, buucuc_goc, " +
                    "sum(sanluong_nho), sum(trongluong_nho), sum(dt_sanluong_nho), " +
                    "sum(dt_trongluong_nho), sum(tong_doanhthu_nho), sum(sanluong_lon), " +
                    "sum(trongluong_lon), sum(dt_sanluong_lon), sum(dt_trongluong_lon), " +
                    "sum(tong_doanhthu_lon), sum(tong_sanluong), sum(tong_trongluong), " +
                    "sum(tong_trongluong_dtkt), sum(tong_dt_sanluong), " +
                    "sum(tong_dt_trongluong), sum(tong_doanhthu) " +
                    "from LOG_BCDTKT_TH " +
                    "where " + filterDate + filterChiNhanh + filterBuuCuc + filterChiNhanhSso + filterBuuCucSso + filterMaDV + filterTTKT + filterTTKTCha +
                    " group by chinhanh_goc, buucuc_goc)";
        }

        System.out.println(".................SQL: " + sql);

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                result = rs.getLong("count");
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get doanhThuKhaiThacTotalRecord in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return result;
    }

    @Override
    public DoanhThuKhaiThacResponse doanhThuKhaiThacTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterChiNhanh = !chiNhanh.equals("") ? " and chinhanh_goc = '" + chiNhanh + "'" : "";
        String filterBuuCuc = !buuCuc.equals("") ? " and buucuc_goc = '" + buuCuc + "'" : "";
        String filterTTKT = !ttkt.equals("") ? " and ma_ttkt = '" + ttkt + "'" : "";
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterChiNhanhSso = !chiNhanhSso.isEmpty() ? " and chinhanh_goc in " + castListToQueryParam(chiNhanhSso) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and buucuc_goc in " + castListToQueryParam(buuCucSso) : "";

        String sql = "select round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(dt_sanluong_nho), 2) as dt_sanluong_nho, " +
                "round(sum(dt_trongluong_nho), 2) as dt_trongluong_nho, round(sum(tong_doanhthu_nho), 2) as tong_doanhthu_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(dt_sanluong_lon), 2) as dt_sanluong_lon, round(sum(dt_trongluong_lon), 2) as dt_trongluong_lon, " +
                "round(sum(tong_doanhthu_lon), 2) as tong_doanhthu_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                "round(sum(tong_trongluong_dtkt), 2) as tong_trongluong_dtkt, round(sum(tong_dt_sanluong), 2) as tong_dt_sanluong, " +
                "round(sum(tong_dt_trongluong), 2) as tong_dt_trongluong, round(sum(tong_doanhthu), 2) as tong_doanhthu " +
                "from LOG_BCDTKT_TH " +
                "where " + filterDate + filterChiNhanh + filterBuuCuc + filterChiNhanhSso + filterBuuCucSso + filterMaDV + filterTTKT + filterTTKTCha;

        System.out.println(".................SQL: " + sql);

        DoanhThuKhaiThacResponse response = new DoanhThuKhaiThacResponse();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                response.setSlNho(rs.getLong("sanluong_nho"));
                response.setTlNho(rs.getDouble("trongluong_nho"));
                response.setDoanhThuSlNho(rs.getDouble("dt_sanluong_nho"));
                response.setDoanhThuTlNho(rs.getDouble("dt_trongluong_nho"));
                response.setTongDoanhThuNho(rs.getDouble("tong_doanhthu_nho"));
                response.setSlLon(rs.getLong("sanluong_lon"));
                response.setTlLon(rs.getDouble("trongluong_lon"));
                response.setDoanhThuSlLon(rs.getDouble("dt_sanluong_lon"));
                response.setDoanhThuTlLon(rs.getDouble("dt_trongluong_lon"));
                response.setTongDoanhThuLon(rs.getDouble("tong_doanhthu_lon"));
                response.setTongSl(rs.getLong("tong_sanluong"));
                response.setTongTl(rs.getDouble("tong_trongluong"));
                response.setTongTlDTKT(rs.getDouble("tong_trongluong_dtkt"));
                response.setTongDoanhThuSl(rs.getLong("tong_dt_sanluong"));
                response.setTongDoanhThuTl(rs.getDouble("tong_dt_trongluong"));
                response.setTongDoanhThuTruocKpi(rs.getDouble("tong_doanhthu"));
                response.setTongDoanhThuSauKpi(rs.getDouble("tong_doanhthu"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get doanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public List<DoanhThuKhaiThacExcel> exportExcelDoanhThuKhaiThac(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSso, List<String> buuCucSso, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterChiNhanh = !chiNhanh.equals("") ? " and chinhanh_goc = '" + chiNhanh + "'" : "";
        String filterBuuCuc = !buuCuc.equals("") ? " and buucuc_goc = '" + buuCuc + "'" : "";
        String filterTTKT = !ttkt.equals("") ? " and ma_ttkt = '" + ttkt + "'" : "";
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterChiNhanhSso = !chiNhanhSso.isEmpty() ? " and chinhanh_goc in " + castListToQueryParam(chiNhanhSso) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and buucuc_goc in " + castListToQueryParam(buuCucSso) : "";

        String sql;
        if (chiNhanh.equals("")) {
            sql = "select chinhanh_goc, " +
                    "round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(dt_sanluong_nho), 2) as dt_sanluong_nho, " +
                    "round(sum(dt_trongluong_nho), 2) as dt_trongluong_nho, round(sum(tong_doanhthu_nho), 2) as tong_doanhthu_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                    "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(dt_sanluong_lon), 2) as dt_sanluong_lon, round(sum(dt_trongluong_lon), 2) as dt_trongluong_lon, " +
                    "round(sum(tong_doanhthu_lon), 2) as tong_doanhthu_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                    "round(sum(tong_trongluong_dtkt), 2) as tong_trongluong_dtkt, round(sum(tong_dt_sanluong), 2) as tong_dt_sanluong, " +
                    "round(sum(tong_dt_trongluong), 2) as tong_dt_trongluong, round(sum(tong_doanhthu), 2) as tong_doanhthu " +
                    "from LOG_BCDTKT_TH " +
                    "where " + filterDate + filterChiNhanh + filterBuuCuc + filterChiNhanhSso + filterBuuCucSso + filterMaDV + filterTTKT + filterTTKTCha
                    + " group by chinhanh_goc";
        } else {
            sql = "select chinhanh_goc, buucuc_goc, " +
                    "round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(dt_sanluong_nho), 2) as dt_sanluong_nho, " +
                    "round(sum(dt_trongluong_nho), 2) as dt_trongluong_nho, round(sum(tong_doanhthu_nho), 2) as tong_doanhthu_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                    "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(dt_sanluong_lon), 2) as dt_sanluong_lon, round(sum(dt_trongluong_lon), 2) as dt_trongluong_lon, " +
                    "round(sum(tong_doanhthu_lon), 2) as tong_doanhthu_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                    "round(sum(tong_trongluong_dtkt), 2) as tong_trongluong_dtkt, round(sum(tong_dt_sanluong), 2) as tong_dt_sanluong, " +
                    "round(sum(tong_dt_trongluong), 2) as tong_dt_trongluong, round(sum(tong_doanhthu), 2) as tong_doanhthu " +
                    "from LOG_BCDTKT_TH " +
                    "where " + filterDate + filterChiNhanh + filterBuuCuc + filterChiNhanhSso + filterBuuCucSso + filterMaDV + filterTTKT + filterTTKTCha
                    + " group by chinhanh_goc, buucuc_goc";
        }

        System.out.println(".................SQL: " + sql);

        List<DoanhThuKhaiThacExcel> listResponse = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                DoanhThuKhaiThacExcel response;
                if (chiNhanh.equals("")) {
                    response = new DoanhThuKhaiThacExcel(
                            rs.getString("chinhanh_goc"),
                            rs.getLong("sanluong_nho"),
                            rs.getDouble("trongluong_nho"),
                            rs.getDouble("dt_sanluong_nho"),
                            rs.getDouble("dt_trongluong_nho"),
                            rs.getDouble("tong_doanhthu_nho"),
                            rs.getLong("sanluong_lon"),
                            rs.getDouble("trongluong_lon"),
                            rs.getDouble("dt_sanluong_lon"),
                            rs.getDouble("dt_trongluong_lon"),
                            rs.getDouble("tong_doanhthu_lon"),
                            rs.getLong("tong_sanluong"),
                            rs.getDouble("tong_trongluong"),
                            rs.getDouble("tong_trongluong_dtkt"),
                            rs.getLong("tong_dt_sanluong"),
                            rs.getDouble("tong_dt_trongluong"),
                            rs.getDouble("tong_doanhthu"),
                            rs.getDouble("tong_doanhthu")
                    );
                } else {
                    response = new DoanhThuKhaiThacExcel(
                            rs.getString("chinhanh_goc"),
                            rs.getString("buucuc_goc"),
                            rs.getLong("sanluong_nho"),
                            rs.getDouble("trongluong_nho"),
                            rs.getDouble("dt_sanluong_nho"),
                            rs.getDouble("dt_trongluong_nho"),
                            rs.getDouble("tong_doanhthu_nho"),
                            rs.getLong("sanluong_lon"),
                            rs.getDouble("trongluong_lon"),
                            rs.getDouble("dt_sanluong_lon"),
                            rs.getDouble("dt_trongluong_lon"),
                            rs.getDouble("tong_doanhthu_lon"),
                            rs.getLong("tong_sanluong"),
                            rs.getDouble("tong_trongluong"),
                            rs.getDouble("tong_trongluong_dtkt"),
                            rs.getLong("tong_dt_sanluong"),
                            rs.getDouble("tong_dt_trongluong"),
                            rs.getDouble("tong_doanhthu"),
                            rs.getDouble("tong_doanhthu")
                    );
                }
                listResponse.add(response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get exportExcelDoanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listResponse;
    }

    @Override
    public List<DoanhThuKhaiThacTrinhKyExcel> exportExcelTrinhKyDoanhThu(LocalDate ngayBaoCao, Integer luyKe) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }


        String sql = "select ma_ttkt_cha, " +
                "round(sum(tong_sanluong), 0) as tong_sanluong, " +
                "round(sum(tong_trongluong), 0) as tong_trongluong, " +
                "round(sum(tong_doanhthu) * " + Constant.HE_SO_HIEU_QUA + " / 100, 0) as tong_doanhthu, " +
                "round(sum(tong_doanhthu) * 8 / 100, 0) as vat, " +
                "round(sum(tong_doanhthu) + (sum(tong_doanhthu) * 8 / 100), 0) as tong_cong " +
                "from LOG_BCDTKT_TH " +
                "where " + filterDate +
                " group by ma_ttkt_cha order by ma_ttkt_cha asc";

        log.info(".................SQL: " + sql);
        System.out.println(".................SQL: " + sql);

        List<DoanhThuKhaiThacTrinhKyExcel> listResponse = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                DoanhThuKhaiThacTrinhKyExcel response = new DoanhThuKhaiThacTrinhKyExcel(
                        rs.getString("ma_ttkt_cha"),
                        rs.getLong("tong_sanluong"),
                        rs.getLong("tong_trongluong"),
                        rs.getLong("tong_doanhthu"),
                        rs.getLong("vat"),
                        rs.getLong("tong_cong")
                    );
                listResponse.add(response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get exportExcelDoanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {

            releaseConnect(conn, stmt, rs);
        }
        return listResponse;
    }

    public String getTTKTString(List<String> listTTKT) {
        String result = "'";
        result += String.join("', '", listTTKT);
        result += "'";
        return result;
    };
}
