package nocsystem.indexmanager.dao.NguonKhaiThacKhuVuc;

import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucExcel;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucResponse;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

public interface NguonKhaiThacKhuVucDAO {
    List<NguonKhaiThacKhuVucResponse> nguonKhaiThacKhuVuc(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu, Integer page, Integer pageSize) throws SQLException;
    Long nguonKhaiThacKhuVucTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu) throws SQLException;
    NguonKhaiThacKhuVucResponse nguonKhaiThacKhuVucTong(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu) throws SQLException;
    List<NguonKhaiThacKhuVucExcel> exportExcelNguonKhaiThacKhuVuc(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu) throws SQLException;
}
