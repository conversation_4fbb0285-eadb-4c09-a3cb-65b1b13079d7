package nocsystem.indexmanager.dao.NguonKhaiThacKhuVuc;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucExcel;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucResponse;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static nocsystem.indexmanager.util.StringUtil.castListToQueryParam;
import static nocsystem.indexmanager.util.TimeUtils.localDateToStringInteger;

@Service
public class NguonKhaiThacKhuVucDAOImpl extends AbstractDao implements NguonKhaiThacKhuVucDAO {
    @Override
    public List<NguonKhaiThacKhuVucResponse> nguonKhaiThacKhuVuc(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu, Integer page, Integer pageSize) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterTTKT = !ttktQuetNhan.equals("") ? " and ma_ttkt = '" + ttktQuetNhan + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and ma_ttkt in " + castListToQueryParam(buuCucSso) : "";

        String sql = "select ma_ttkt_cha, ma_ttkt, " +
                "round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(nguon_sanluong_nho), 2) as nguon_sanluong_nho, " +
                "round(sum(nguon_trongluong_nho), 2) as nguon_trongluong_nho, round(sum(tong_nguon_nho), 2) as tong_nguon_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(nguon_sanluong_lon), 2) as nguon_sanluong_lon, round(sum(nguon_trongluong_lon), 2) as nguon_trongluong_lon, " +
                "round(sum(tong_nguon_lon), 2) as tong_nguon_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                "round(sum(tong_trongluong_nguonkt), 2) as tong_trongluong_nguonkt, round(sum(tong_nguon_sanluong), 2) as tong_nguon_sanluong, " +
                "round(sum(tong_nguon_trongluong), 2) as tong_nguon_trongluong, round(sum(tong_nguon), 2) as tong_nguon " +
                "from LOG_BCNKT_TH " +
                "where " + filterDate + filterTTKTCha + filterTTKT + filterBuuCucSso + filterMaDV + filterTTKT
                + " group by ma_ttkt_cha, ma_ttkt"
                + " limit " + pageSize + " offset " + page * pageSize;

        System.out.println(".................SQL: " + sql);

        List<NguonKhaiThacKhuVucResponse> listResponse = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                NguonKhaiThacKhuVucResponse response = new NguonKhaiThacKhuVucResponse();
                response.setTtktCha(rs.getString("ma_ttkt_cha"));
                response.setTtktQuetNhan(rs.getString("ma_ttkt"));
                response.setSlNho(rs.getLong("sanluong_nho"));
                response.setTlNho(rs.getDouble("trongluong_nho"));
                response.setNguonSlNho(rs.getDouble("nguon_sanluong_nho"));
                response.setNguonTlNho(rs.getDouble("nguon_trongluong_nho"));
                response.setTongNguonNho(rs.getDouble("tong_nguon_nho"));
                response.setSlLon(rs.getLong("sanluong_lon"));
                response.setTlLon(rs.getDouble("trongluong_lon"));
                response.setNguonSlLon(rs.getDouble("nguon_sanluong_lon"));
                response.setNguonTlLon(rs.getDouble("nguon_trongluong_lon"));
                response.setTongNguonLon(rs.getDouble("tong_nguon_lon"));
                response.setTongSl(rs.getLong("tong_sanluong"));
                response.setTongTl(rs.getDouble("tong_trongluong"));
                response.setTongTlTinhNguon(rs.getDouble("tong_trongluong_nguonkt"));
                response.setTongNguonSl(rs.getLong("tong_nguon_sanluong"));
                response.setTongNguonTl(rs.getDouble("tong_nguon_trongluong"));
                response.setTongNguonTruocKpi(rs.getDouble("tong_nguon"));
                response.setTongNguonSauKpi(rs.getDouble("tong_nguon"));

                listResponse.add(response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get doanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listResponse;
    }

    @Override
    public Long nguonKhaiThacKhuVucTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Long result = 0L;
        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterTTKT = !ttktQuetNhan.equals("") ? " and ma_ttkt = '" + ttktQuetNhan + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and ma_ttkt in " + castListToQueryParam(buuCucSso) : "";

        String sql = "select count(*) as count from " +
                "(select ma_ttkt_cha, ma_ttkt, " +
                "round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(nguon_sanluong_nho), 2) as nguon_sanluong_nho, " +
                "round(sum(nguon_trongluong_nho), 2) as nguon_trongluong_nho, round(sum(tong_nguon_nho), 2) as tong_nguon_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(nguon_sanluong_lon), 2) as nguon_sanluong_lon, round(sum(nguon_trongluong_lon), 2) as nguon_trongluong_lon, " +
                "round(sum(tong_nguon_lon), 2) as tong_nguon_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                "round(sum(tong_trongluong_nguonkt), 2) as tong_trongluong_nguonkt, round(sum(tong_nguon_sanluong), 2) as tong_nguon_sanluong, " +
                "round(sum(tong_nguon_trongluong), 2) as tong_nguon_trongluong, round(sum(tong_nguon), 2) as tong_nguon " +
                "from LOG_BCNKT_TH " +
                "where " + filterDate + filterTTKTCha + filterTTKT + filterBuuCucSso + filterMaDV + filterTTKT
                + " group by ma_ttkt_cha, ma_ttkt)";

        System.out.println(".................SQL: " + sql);

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                result = rs.getLong("count");
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get doanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return result;
    }

    @Override
    public NguonKhaiThacKhuVucResponse nguonKhaiThacKhuVucTong(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterTTKT = !ttktQuetNhan.equals("") ? " and ma_ttkt = '" + ttktQuetNhan + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and ma_ttkt in " + castListToQueryParam(buuCucSso) : "";

        String sql = "select round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(nguon_sanluong_nho), 2) as nguon_sanluong_nho, " +
                "round(sum(nguon_trongluong_nho), 2) as nguon_trongluong_nho, round(sum(tong_nguon_nho), 2) as tong_nguon_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(nguon_sanluong_lon), 2) as nguon_sanluong_lon, round(sum(nguon_trongluong_lon), 2) as nguon_trongluong_lon, " +
                "round(sum(tong_nguon_lon), 2) as tong_nguon_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                "round(sum(tong_trongluong_nguonkt), 2) as tong_trongluong_nguonkt, round(sum(tong_nguon_sanluong), 2) as tong_nguon_sanluong, " +
                "round(sum(tong_nguon_trongluong), 2) as tong_nguon_trongluong, round(sum(tong_nguon), 2) as tong_nguon " +
                "from LOG_BCNKT_TH " +
                "where " + filterDate + filterTTKTCha + filterTTKT + filterBuuCucSso + filterMaDV + filterTTKT;

        System.out.println(".................SQL: " + sql);

        NguonKhaiThacKhuVucResponse response = new NguonKhaiThacKhuVucResponse();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                response.setSlNho(rs.getLong("sanluong_nho"));
                response.setTlNho(rs.getDouble("trongluong_nho"));
                response.setNguonSlNho(rs.getDouble("nguon_sanluong_nho"));
                response.setNguonTlNho(rs.getDouble("nguon_trongluong_nho"));
                response.setTongNguonNho(rs.getDouble("tong_nguon_nho"));
                response.setSlLon(rs.getLong("sanluong_lon"));
                response.setTlLon(rs.getDouble("trongluong_lon"));
                response.setNguonSlLon(rs.getDouble("nguon_sanluong_lon"));
                response.setNguonTlLon(rs.getDouble("nguon_trongluong_lon"));
                response.setTongNguonLon(rs.getDouble("tong_nguon_lon"));
                response.setTongSl(rs.getLong("tong_sanluong"));
                response.setTongTl(rs.getDouble("tong_trongluong"));
                response.setTongTlTinhNguon(rs.getDouble("tong_trongluong_nguonkt"));
                response.setTongNguonSl(rs.getLong("tong_nguon_sanluong"));
                response.setTongNguonTl(rs.getDouble("tong_nguon_trongluong"));
                response.setTongNguonTruocKpi(rs.getDouble("tong_nguon"));
                response.setTongNguonSauKpi(rs.getDouble("tong_nguon"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get doanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public List<NguonKhaiThacKhuVucExcel> exportExcelNguonKhaiThacKhuVuc(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSso, List<String> maDichVu) throws SQLException {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int ngayBaoCaoQuery = localDateToStringInteger(ngayBaoCao);
        String filterDate;
        if (luyKe == 0) {
            filterDate = "data_date_key = " + ngayBaoCaoQuery;
        } else {
            int ngayDauThangQuery = localDateToStringInteger(ngayBaoCao.withDayOfMonth(1));
            filterDate = "data_date_key >= " + ngayDauThangQuery + " and data_date_key <= " + ngayBaoCaoQuery;
        }
        String filterTTKTCha = !ttktCha.equals("") ? " and ma_ttkt_cha = '" + ttktCha + "'" : "";
        String filterTTKT = !ttktQuetNhan.equals("") ? " and ma_ttkt = '" + ttktQuetNhan + "'" : "";
        String filterMaDV = !maDichVu.isEmpty() ? " and ma_dv_viettel in " + castListToQueryParam(maDichVu) : "";
        String filterBuuCucSso = !buuCucSso.isEmpty() ? " and ma_ttkt in " + castListToQueryParam(buuCucSso) : "";

        String sql = "select ma_ttkt_cha, ma_ttkt, " +
                "round(sum(sanluong_nho), 2) as sanluong_nho, round(sum(trongluong_nho), 2) as trongluong_nho, round(sum(nguon_sanluong_nho), 2) as nguon_sanluong_nho, " +
                "round(sum(nguon_trongluong_nho), 2) as nguon_trongluong_nho, round(sum(tong_nguon_nho), 2) as tong_nguon_nho, round(sum(sanluong_lon), 2) as sanluong_lon, " +
                "round(sum(trongluong_lon), 2) as trongluong_lon, round(sum(nguon_sanluong_lon), 2) as nguon_sanluong_lon, round(sum(nguon_trongluong_lon), 2) as nguon_trongluong_lon, " +
                "round(sum(tong_nguon_lon), 2) as tong_nguon_lon, round(sum(tong_sanluong), 2) as tong_sanluong, round(sum(tong_trongluong), 2) as tong_trongluong, " +
                "round(sum(tong_trongluong_nguonkt), 2) as tong_trongluong_nguonkt, round(sum(tong_nguon_sanluong), 2) as tong_nguon_sanluong, " +
                "round(sum(tong_nguon_trongluong), 2) as tong_nguon_trongluong, round(sum(tong_nguon), 2) as tong_nguon " +
                "from LOG_BCNKT_TH " +
                "where " + filterDate + filterTTKTCha + filterTTKT + filterBuuCucSso + filterMaDV + filterTTKT
                + " group by ma_ttkt_cha, ma_ttkt";

        System.out.println(".................SQL: " + sql);

        List<NguonKhaiThacKhuVucExcel> listResponse = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                NguonKhaiThacKhuVucExcel response = new NguonKhaiThacKhuVucExcel(
                    rs.getString("ma_ttkt_cha"),
                    rs.getString("ma_ttkt"),
                    rs.getLong("sanluong_nho"),
                    rs.getDouble("trongluong_nho"),
                    rs.getDouble("nguon_sanluong_nho"),
                    rs.getDouble("nguon_trongluong_nho"),
                    rs.getDouble("tong_nguon_nho"),
                    rs.getLong("sanluong_lon"),
                    rs.getDouble("trongluong_lon"),
                    rs.getDouble("nguon_sanluong_lon"),
                    rs.getDouble("nguon_trongluong_lon"),
                    rs.getDouble("tong_nguon_lon"),
                    rs.getLong("tong_sanluong"),
                    rs.getDouble("tong_trongluong"),
                    rs.getDouble("tong_trongluong_nguonkt"),
                    rs.getLong("tong_nguon_sanluong"),
                    rs.getDouble("tong_nguon_trongluong"),
                    rs.getDouble("tong_nguon"),
                    rs.getDouble("tong_nguon")
                );

                listResponse.add(response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when get doanhThuKhaiThac in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listResponse;
    }
}
