package nocsystem.indexmanager.dao.danhgia_nguonluc;

import lombok.RequiredArgsConstructor;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.danhgianguonluc.CoHoiBanHangDTO;
import nocsystem.indexmanager.models.Response.danhgianguonluc.DgnlXeTaiDangChayDTO;
import nocsystem.indexmanager.models.Response.danhgianguonluc.TongHqxDuphongDTO;
import nocsystem.indexmanager.models.Response.hieuquaxe.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class HieuQuaXeDAOImpl extends AbstractDao implements HieuQuaXeDAO {
    private static final Logger log = LoggerFactory.getLogger(HieuQuaXeDAOImpl.class);

    @Override
    public TongQuanHQXRes tongQuanHQX(LocalDate ngayBaoCao, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        TongQuanHQXRes resDetail = new TongQuanHQXRes();

        //Ngay hien tai
        Map<String, Long> mapDataVersion = getVersionMax("dgnl_xetai_dangchay", ngayBaoCao);
        System.out.println("mapDataVersion " + mapDataVersion.toString());
        Long versionHienTai = mapDataVersion.get("version");
        Long ngayBaoCaoVs = mapDataVersion.get("ngayBaoCaoVs");

        if (mapDataVersion.get("version") == null || mapDataVersion.get("ngayBaoCaoVs") == null) {
            log.info("Ko có data ngày: {} getVersionMax: {} ", ngayBaoCao, mapDataVersion);
            return resDetail;
        }

        Map<String, Long> tongSoChuyen = tongSoChuyen(ngayBaoCaoVs, versionHienTai, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        Long tongSoChuyenDi = tongSoChuyen.get("tongSoChuyenDi");
        Long tongSoChuyenVe = tongSoChuyen.get("tongSoChuyenVe");

        resDetail.setMaCn(chiNhanh);
        resDetail.setMaDvc(maDVC);
        resDetail.setTongSoTuyenDangChay(tongSoChuyenDi + tongSoChuyenVe);

        TongHqxDuphongDTO tongHqxDuphongDTO = tongHqxDuphong(ngayBaoCaoVs, versionHienTai, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        LocalDateTime localDateTime = ngayBaoCao.atStartOfDay();
        Long timestamp = localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        resDetail.setThoiGianCapNhat(timestamp);


        Double tongHqxDuphongChieuDi = tongHqxDuphongDTO.getTongHqxDuphongChieuDi();
        Double tongHqxDuphongChieuVe = tongHqxDuphongDTO.getTongHqxDuphongChieuVe();

        Long tongSoXe = resDetail.getTongSoTuyenDangChay();

        Double hqxChieuDi = 0D;
        Double hqxChieuVe = 0D;

        hqxChieuDi = safeDivide(tongHqxDuphongChieuDi, tongSoChuyenDi);
        hqxChieuVe = safeDivide(tongHqxDuphongChieuVe, tongSoChuyenVe);

        // Ngay N-7
        Map<String, Long> mapDataVersionMinus7 = getVersionMax("dgnl_xetai_dangchay", ngayBaoCao.minusDays(7));
        log.info("mapDataVersionMinus7: " + mapDataVersionMinus7.toString());
        Long versionMinus7 = Objects.requireNonNullElse(mapDataVersionMinus7.get("version"), versionHienTai);
        Long ngayN_7 = Objects.requireNonNullElse(mapDataVersionMinus7.get("ngayBaoCaoVs"), ngayBaoCaoVs);

        System.out.println("versionMinus7 " + versionMinus7 + "__" + ngayN_7);

        Map<String, Long> tongSoChuyenN_7 = tongSoChuyen(ngayN_7, versionMinus7, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        Long tongSoChuyenDiN_7 = tongSoChuyenN_7.get("tongSoChuyenDi");
        Long tongSoChuyenVeN_7 = tongSoChuyenN_7.get("tongSoChuyenVe");


        TongHqxDuphongDTO tongHqxDuphongN_7 = tongHqxDuphong(ngayN_7, versionMinus7, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        Double tongHqxDuphongChieuDiN_7 = tongHqxDuphongN_7.getTongHqxDuphongChieuDi();
        Double tongHqxDuphongChieuVeN_7 = tongHqxDuphongN_7.getTongHqxDuphongChieuVe();

//        Double tongHqxDp = tongKLXe2ChieuN_7.get("tongKlXeChieuVe");

        Double hqxChieuDiN_7 = safeDivide(tongHqxDuphongChieuDiN_7, tongSoChuyenDiN_7);
        Double hqxChieuVeN_7 = safeDivide(tongHqxDuphongChieuVeN_7, tongSoChuyenVeN_7);

        resDetail.setHqxChieudi(hqxChieuDi);
        resDetail.setSoVoiTTChieudi(hqxChieuDi - hqxChieuDiN_7);

        resDetail.setHqxChieuve(hqxChieuVe);
        resDetail.setSoVoiTTChieuve(hqxChieuVe - hqxChieuVeN_7);

        List<CoHoiBanHangDTO> data = coHoiBanHang(ngayBaoCaoVs, versionHienTai, null, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        resDetail.setCoHoiBanHang(data.size());

        // version hien tai - (version_1)
        Map<String, Long> nearestVersionMap = getClosestVersionBeforeDate("dgnl_xetai_dangchay", ngayBaoCao, 7);

        Long versionGanNhat = 0L;
        Long ngayBaoCaoGanNhat = 0L;

        if (nearestVersionMap.isEmpty()) {
            log.info("Không tìm thấy version gần nhất trong 7 ngày trước đó.");
        } else {
            versionGanNhat = nearestVersionMap.get("version");
            ngayBaoCaoGanNhat = nearestVersionMap.get("ngayBaoCaoVs");
        }

        System.out.println("Version gan nhat: " + versionGanNhat + " ngay: " + ngayBaoCaoGanNhat);

        Map<String, Long> tongSoChuyenGanNhat = tongSoChuyen(ngayBaoCaoGanNhat, versionGanNhat, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        Long tongSoChuyenDiGannhat = tongSoChuyenGanNhat.get("tongSoChuyenDi");
        Long tongSoChuyenVeGannhat = tongSoChuyenGanNhat.get("tongSoChuyenVe");
        Long soTuyenDangchayTanggiam = resDetail.getTongSoTuyenDangChay() - (tongSoChuyenDiGannhat + tongSoChuyenVeGannhat);
        resDetail.setSoTuyenDangChayTangGiam(soTuyenDangchayTanggiam);

        return resDetail;
    }

    @Override
    public CanhBaoHQXTheoChieuRes canhBaoHQX(LocalDate ngayBaoCao, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        CanhBaoHQXTheoChieuRes data = new CanhBaoHQXTheoChieuRes();
        data.setMaCn(chiNhanh);
        data.setMaDvc(maDVC);
        //Ngay hien tai
        Map<String, Long> mapDataVersion = getVersionMax("dgnl_xetai_dangchay", ngayBaoCao);

        Long ngayBaoCaoVs = mapDataVersion.get("ngayBaoCaoVs");
        Long versionHienTai = mapDataVersion.get("version");

        if (mapDataVersion.get("version") == null || mapDataVersion.get("ngayBaoCaoVs") == null) {
            log.info("Ko có data ngày: {} getVersionMax: {} ", ngayBaoCao, mapDataVersion);
            return data;
        }

        List<CanhBaoHQX> getListCanhBaoHQX = getListCanhBaoHQX(ngayBaoCaoVs, versionHienTai, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        Map<String, Double> getHieuQuaDkTB = getHieuQuaDuKienTb(ngayBaoCaoVs, versionHienTai, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        Double hieuQuaDkTBChieuDi = getHieuQuaDkTB.get("hieuQuaDkTBChieuDi");
        Double hieuQuaDkTBChieuVe = getHieuQuaDkTB.get("hieuQuaDkTBChieuVe");
        for (CanhBaoHQX item : getListCanhBaoHQX) {
            if (item.getChieu().equals("chieuDi")) {
                item.setHieuQuaDkTB(hieuQuaDkTBChieuDi);
            }
            if (item.getChieu().equals("chieuVe")) {
                item.setHieuQuaDkTB(hieuQuaDkTBChieuVe);
            }
        }
        System.out.println("getListCanhBaoHQX" + getListCanhBaoHQX.toString());

        data.setCanhBaoHQXs(getListCanhBaoHQX);
        List<PhanTichTheoKG> listData = phanTichTheoKhungGio(ngayBaoCaoVs, versionHienTai, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        data.setPhanTichTheoKGs(listData);
        return data;
    }

    @Override
    public List<DanhSachXeChuY> danhSachXeCanChuY(Integer khungGio, LocalDate ngayBaoCao, List<Integer> types, Integer chieu, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {

        List<DanhSachXeChuY> data = new ArrayList<>();
        // Xác định khung giờ hiện tại theo Asia/Ho_Chi_Minh
        int currentHour = java.time.ZonedDateTime.now(ZoneId.of("Asia/Ho_Chi_Minh")).getHour();
        int currentSlot;
        if (currentHour < 6) {
            currentSlot = 1;
        } else if (currentHour < 12) {
            currentSlot = 2;
        } else if (currentHour < 18) {
            currentSlot = 3;
        } else {
            currentSlot = 4;
        }

        // Điều chỉnh ngày đánh giá theo quy tắc: nếu giờ hiện tại > khung giờ chọn thì ngày đánh giá = dd + 1
        LocalDate evalDate = (khungGio != null && khungGio < currentSlot) ? ngayBaoCao.plusDays(1) : ngayBaoCao;

        Map<String, Long> mapDataVersion = getVersionMax("dgnl_xetai_dangchay", evalDate);

        Long ngayBaoCaoVs = mapDataVersion.get("ngayBaoCaoVs");
        Long versionHienTai = mapDataVersion.get("version");

        if (mapDataVersion.get("version") == null || mapDataVersion.get("ngayBaoCaoVs") == null) {
            log.info("Ko có data ngày: {} getVersionMax: {} ", evalDate, mapDataVersion);
            return data;
        }

        Map<Integer, long[]> khungGioMap = getKhungGioEpoch(evalDate);
        Long startEpochMillis = 0L;
        Long endEpochMillis = 0L;
        if (khungGio == null || !khungGioMap.containsKey(khungGio)) {
            startEpochMillis = 0L;
            endEpochMillis = 0L;
        } else {
            long[] epochRange = khungGioMap.get(khungGio);
            startEpochMillis = epochRange[0];
            endEpochMillis = epochRange[1];
        }
        if (types.isEmpty() && chieu != null) {
            data = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 1, chieu, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        }

        if (types.contains(0) && !(types.contains(1) && types.contains(2) && types.contains(3))) {
            data = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 1, chieu, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        }

        if ((types.contains(1) && types.contains(2) && types.contains(3))) {
            data = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, null, null, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        }
        if (types.contains(1) && !(types.contains(2) && types.contains(3))) {  // còn lại đạt hq
            data = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 2, null, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        }

        if (types.contains(2) && !(types.contains(1) && types.contains(3))) {  // chiều về ko hq
            data = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 1, 2, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        }

        if (types.contains(3) && !(types.contains(1) && types.contains(2))) {  // chiều đi ko hq
            data = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 1, 1, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
        }

        if (types.contains(1) && types.contains(2) && !types.contains(3)) {  // còn lại đạt hq + chiều về ko hq
            List<DanhSachXeChuY> list1 = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 2, null, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
            List<DanhSachXeChuY> list2 = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 1, 2, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
            list1.addAll(list2);
            list1.sort(Comparator.comparing(DanhSachXeChuY::getHqxLuyKe)
                    .thenComparing(DanhSachXeChuY::getBiensoXe));
            data = list1;
        }

        if (types.contains(2) && types.contains(3) && !types.contains(1)) {  // chiều về ko hq + chiều đi ko hq
            data = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 2, null, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        }

        if (types.contains(1) && types.contains(3) && !types.contains(2)) {  // còn lại đạt hq + chiều đi ko hq
            List<DanhSachXeChuY> list1 = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 2, null, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
            List<DanhSachXeChuY> list2 = getListDanhSachXeChuY(ngayBaoCaoVs, versionHienTai, 1, 1, startEpochMillis, endEpochMillis, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);
            list1.addAll(list2);
            list1.sort(Comparator.comparing(DanhSachXeChuY::getHqxLuyKe)
                    .thenComparing(DanhSachXeChuY::getBiensoXe));
            data = list1;
        }

        return data;
    }

    @Override
    public List<DuBaoHieuQuaTuyen> duBaoHieuQuaTuyenXe(LocalDate ngayBaoCao, String maChuyenxe, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<DuBaoHieuQuaTuyen> data = new ArrayList<>();
        Map<String, Long> mapDataVersion = getVersionMax("dgnl_xetai_dangchay", ngayBaoCao);

        Long ngayBaoCaoVs = mapDataVersion.get("ngayBaoCaoVs");
        Long versionHienTai = mapDataVersion.get("version");

        if (mapDataVersion.get("version") == null || mapDataVersion.get("ngayBaoCaoVs") == null) {
            log.info("Ko có data ngày: {} getVersionMax: {} ", ngayBaoCao, mapDataVersion);
            return data;
        }

        data = getListHanhTrinhTuyenXe(ngayBaoCaoVs, versionHienTai, maChuyenxe, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        return data;
    }

    @Override
    public List<TopCoHoiBanHang> topCoHoiBanHang(LocalDate ngayBaoCao, String maChuyenxe, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {

        List<TopCoHoiBanHang> data = new ArrayList<>();

        Map<String, Long> mapDataVersion = getVersionMax("dgnl_xetai_dangchay", ngayBaoCao);

        Long ngayBaoCaoVs = mapDataVersion.get("ngayBaoCaoVs");
        Long versionHienTai = mapDataVersion.get("version");

        if (mapDataVersion.get("version") == null || mapDataVersion.get("ngayBaoCaoVs") == null) {
            log.info("Ko có data ngày: {} getVersionMax: {} ", ngayBaoCao, mapDataVersion);
            return data;
        }

        data = getTopCoHoiBanHang(ngayBaoCaoVs, versionHienTai, maChuyenxe, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO);

        return data;
    }

    public Double safeDivide(Double numerator, Long denominator) {
        if (numerator == null || denominator == null || denominator == 0L) {
            return 0D;
        }
        return numerator / denominator;
    }

//    public Double safeDivideThree(Double a, Double b, Long c) {
//        if (c == null || c == 0L) {
//            return 0D;
//        }
//        Double intermediate = safeDivide(a, b);
//        return safeDivide(intermediate, c.doubleValue());
//    }

    // --------- Query tong hqx dự phóng 2 chiều -----------
    private TongHqxDuphongDTO tongHqxDuphong(Long ngayBaoCao, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        TongHqxDuphongDTO data = new TongHqxDuphongDTO();

        StringBuilder sqlString = new StringBuilder(" SELECT chieu, SUM(hqx_luyke) AS tong_hqx_du_phong FROM dgnl_xetai_dangchay where is_diemcuoi = 1 ");

        List<Object> params = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(ngayBaoCao)) {
            sqlString.append(" AND ngay_baocao = ?");
            params.add(ngayBaoCao);
        }

        if (ObjectUtils.isNotEmpty(version)) {
            sqlString.append(" AND version = ?");
            params.add(version);
        }

        if (chiNhanh != null) {
            sqlString.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }
//            sqlString += " group by tg_capnhat";
        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        sqlString.append(" group by chieu");

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                int chieu = rs.getInt("chieu");
                double tongHqxDuphong = rs.getDouble("tong_hqx_du_phong");

                if (chieu == 1) {
                    data.setTongHqxDuphongChieuDi(tongHqxDuphong);
                } else if (chieu == 2) {
                    data.setTongHqxDuphongChieuVe(tongHqxDuphong);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query dgnl_xetai_dangchay tong Hqx Du Phong" + e.getMessage());
        }
        return data;
    }

    // --------- Query tổng chuyến xe chạy theo từng chiều -----------
    private Map<String, Long> tongSoChuyen(Long ngayBaoCao, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        Map<String, Long> mapData = new HashMap<>();

        StringBuilder sqlString = new StringBuilder("SELECT chieu,  COUNT(DISTINCT ma_chuyenxe) AS tong_chuyen FROM dgnl_xetai_dangchay " +
                "WHERE trangthai_xe = 1 and is_diemcuoi = 1 ");

        List<Object> params = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(ngayBaoCao)) {
            sqlString.append(" AND ngay_baocao = ?");
            params.add(ngayBaoCao);
        }

        if (ObjectUtils.isNotEmpty(version)) {
            sqlString.append(" AND version = ?");
            params.add(version);
        }

        if (chiNhanh != null) {
            sqlString.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        sqlString.append(" group by chieu");
        System.out.println("sqlString___" + sqlString.toString());
        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();

            mapData.put("tongSoChuyenDi", 0L);
            mapData.put("tongSoChuyenVe", 0L);
            while (rs.next()) {
                int chieu = rs.getInt("chieu");
                long tong_chuyen = rs.getInt("tong_chuyen");

                if (chieu == 1) {
                    mapData.put("tongSoChuyenDi", tong_chuyen);

                } else if (chieu == 2) {
                    mapData.put("tongSoChuyenVe", tong_chuyen);
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query count ma_chuyenxe from dgnl_xetai_dangchay: " + e.getMessage());
        }

        return mapData;
    }

    // --------- Query tổng trọng tải tính cước xe theo từng chiều -----------
//    private TongTrongTaiXeCacChieuDTO tongTrongTaiXe2Chieu(Long ngayBaoCao, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
//        TongTrongTaiXeCacChieuDTO mapData = new TongTrongTaiXeCacChieuDTO();
//
//        StringBuilder sqlTaiTrong = new StringBuilder("SELECT ma_chuyenxe, chieu, MAX(taitrong_xe) AS taitrong, tg_capnhat " +
//                "FROM dgnl_xetai_dangchay WHERE trangthai_xe = 1");
//
////        StringBuilder subQuery = new StringBuilder(" SELECT chieu, SUM(taitrong) AS tong_taitrong FROM (SELECT ma_chuyenxe, chieu, MAX(taitrong_xe) AS taitrong" +
////                "    FROM dgnl_xetai_dangchay WHERE ngay_baocao = ? AND version = ? AND trangthai_xe = 1 AND ma_cn = ? and ma_dvc = ?" +
////                " and ma_cn in ? and ma_dvc in  GROUP BY ma_chuyenxe, chieu) t GROUP BY chieu ");
//
//        List<Object> params = new ArrayList<>();
//
//        if (ObjectUtils.isNotEmpty(ngayBaoCao)) {
//            sqlTaiTrong.append(" AND ngay_baocao = ?");
//            params.add(ngayBaoCao);
//        }
//
//        if (ObjectUtils.isNotEmpty(version)) {
//            sqlTaiTrong.append(" AND version = ?");
//            params.add(version);
//        }
//
//        if (chiNhanh != null) {
//            sqlTaiTrong.append(" AND ma_cn = ?");
//            params.add(chiNhanh);
//        }
//        if (maDVC != null) {
//            sqlTaiTrong.append(" AND ma_dvc = ?");
//            params.add(maDVC);
//        }
//
//        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
//            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
//            sqlTaiTrong.append(" AND ma_cn IN (").append(placeholders).append(")");
//            params.addAll(chiNhanhSSO);
//        }
//        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
//            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
//            sqlTaiTrong.append(" AND ma_dvc IN (").append(placeholders).append(")");
//            params.addAll(buuCucSSO);
//        }
//
//        sqlTaiTrong.append(" group by chieu, tg_capnhat");
//
//        String finalSql = "SELECT chieu, SUM(taitrong) AS tong_taitrong, tg_capnhat FROM (" + sqlTaiTrong.toString() + ") t GROUP BY chieu, tg_capnhat";
//
//        System.out.println("finalSQl: " + finalSql);
//
//        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
//             PreparedStatement stmt = conn.prepareStatement(finalSql)) {
//
//            for (int i = 0; i < params.size(); i++) {
//                stmt.setObject(i + 1, params.get(i));
//            }
//
//            ResultSet rs = stmt.executeQuery();
//
//            while (rs.next()) {
//                int chieu = rs.getInt("chieu");
//                double tongTaiTrong = rs.getDouble("tong_taitrong");
//                long tgCapnhat = rs.getLong("tg_capnhat");
//
//                if (chieu == 1) {
//                    mapData.setTongTaiTrongChieuDi(tongTaiTrong);
//                    mapData.setTgCapnhat(tgCapnhat);
//                }
//                if (chieu == 2) {
//                    mapData.setTongTaiTrongChieuVe(tongTaiTrong);
//                    mapData.setTgCapnhat(tgCapnhat);
//                }
//            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new RuntimeException("Error query sum taitrong_xe from dgnl_xetai_dangchay " + e.getMessage());
//        }
//        return mapData;
//    }

    private List<CoHoiBanHangDTO> coHoiBanHang(Long ngayBaoCao, Long version, String maChuyenXe, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<CoHoiBanHangDTO> result = new ArrayList<>();

        StringBuilder sqlString = new StringBuilder("SELECT b.* FROM dgnl_xetai_cohoi_banhang b JOIN dgnl_xetai_dangchay d " +
                "ON b.ngay_baocao = d.ngay_baocao AND b.version = d.version AND b.ma_chuyenxe = d.ma_chuyenxe " +
                "WHERE b.ngay_baocao = ? AND b.version = ? AND d.trangthai_xe = 1 ");

        List<Object> params = new ArrayList<>();

        params.add(ngayBaoCao);
        params.add(version);

        if (chiNhanh != null) {
            sqlString.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }


        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                CoHoiBanHangDTO dto = new CoHoiBanHangDTO();
                dto.setDiemCuoi(rs.getString("diem_dau"));
                dto.setDiemCuoi(rs.getString("diem_cuoi"));
                dto.setMaBcDiemDau(rs.getString("ma_buucuc_diemdau"));
                dto.setMaBcDiemCuoi(rs.getString("ma_buucuc_diemcuoi"));
                dto.setKlCotheBan(rs.getDouble("kl_cothe_ban"));
                dto.setKmDukien(rs.getDouble("km_dukien"));
                dto.setTgCheckinDukien(rs.getLong("tg_checkin_dukien"));

                result.add(dto);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query dgnl_xetai_cohoi_banhang: " + e.getMessage());
        }
        return result;
    }

    private Map<String, Long> getVersionMax(String tenBaoCao, LocalDate ngayBaoCao) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlMax = "";
        Map<String, Long> mapDataVersion = new HashMap<>();
        if (ngayBaoCao != null) {
            sqlMax = "SELECT ngay_baocao, MAX(version) AS version FROM chiso_version where ma_chiso = ? AND ngay_baocao = date '" + ngayBaoCao + "' GROUP BY ngay_baocao, ma_chiso";

        } else {
            return null;
        }

        Long maxVersion = null;
        LocalDate ngayBaoCaoChiSovs = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            stmt.setString(1, tenBaoCao);
//            stmt.setDate(2, java.sql.Date.valueOf(ngayBaoCao));            // ngay_baocao

            rs = stmt.executeQuery();
            mapDataVersion.put("version", null);
            mapDataVersion.put("ngayBaoCaoVs", null);

            while (rs.next()) {
                maxVersion = rs.getLong("version");
                ngayBaoCaoChiSovs = rs.getDate("ngay_baocao").toLocalDate();

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                Long ngayBaoCaoVs = Long.parseLong(ngayBaoCaoChiSovs.format(formatter));

                mapDataVersion.put("version", maxVersion);
                mapDataVersion.put("ngayBaoCaoVs", ngayBaoCaoVs);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        return mapDataVersion;
    }

    private Map<String, Long> getClosestVersionBeforeDate(String tenBaoCao, LocalDate ngayBaoCao, int maxDaysLookBack) {
        Map<String, Long> versionNowMap = getVersionMax(tenBaoCao, ngayBaoCao);

        if (versionNowMap != null && versionNowMap.get("version") != null) {
            Long versionNow = versionNowMap.get("version");

            // Trường hợp version hiện tại > 1: return version - 1
            if (versionNow > 1) {
                Map<String, Long> result = new HashMap<>();
                result.put("version", versionNow - 1);
                result.put("ngayBaoCaoVs", versionNowMap.get("ngayBaoCaoVs"));
                return result;
            }

            // version == 1 -> lùi ngày, tìm ngày có version lớn nhất
            for (int i = 1; i <= maxDaysLookBack; i++) {
                LocalDate prevDate = ngayBaoCao.minusDays(i);
                Map<String, Long> versionPrevMap = getVersionMax(tenBaoCao, prevDate);
                if (versionPrevMap != null && versionPrevMap.get("version") != null) {
                    return versionPrevMap;
                }
            }
        }

        return new HashMap<>();
    }

    public Long getNullableLong(ResultSet rs, String column) throws SQLException {
        long value = rs.getLong(column);
        return rs.wasNull() ? null : value;
    }

    public Double getNullableDouble(ResultSet rs, String column) throws SQLException {
        double value = rs.getDouble(column);
        return rs.wasNull() ? null : value;
    }

    public Integer getNullableInt(ResultSet rs, String column) throws SQLException {
        int value = rs.getInt(column);
        return rs.wasNull() ? null : value;
    }

    public Boolean getNullableBoolean(ResultSet rs, String column) throws SQLException {
        boolean value = rs.getBoolean(column);
        return rs.wasNull() ? null : value;
    }

    private List<CanhBaoHQX> getListCanhBaoHQX(Long ngayBaoCao, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<CanhBaoHQX> dataList = new ArrayList<>();

        StringBuilder sqlString = new StringBuilder("SELECT chieu,  COUNT(CASE WHEN hqx_luyke < 70 THEN 1 ELSE NULL END) AS so_xe_dk_kohieuqua, " +
                " COUNT(CASE WHEN hqx_luyke < 70 THEN 1 ELSE NULL END) AS so_xe_hqx_duoi70, " +
                " COUNT(CASE WHEN hqx_luyke < 50 THEN 1 ELSE NULL END) AS so_xe_hqx_duoi50, " +
                " COUNT(CASE WHEN hqx_luyke < 30 THEN 1 ELSE NULL END) AS so_xe_hqx_duoi30 " +
                " FROM dgnl_xetai_dangchay" +
                " WHERE ngay_baocao = ? AND version = ? AND trangthai_xe = 1 AND is_diemcuoi = 1 AND is_hanhtrinh = 1 ");

        List<Object> params = new ArrayList<>();

        params.add(ngayBaoCao);
        params.add(version);

        if (chiNhanh != null) {
            sqlString.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        sqlString.append(" group by chieu");
        System.out.println("sqlString: " + sqlString);
        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                CanhBaoHQX dto = new CanhBaoHQX();
                int chieu = rs.getInt("chieu");
                int so_xe_dk_kohieuqua = rs.getInt("so_xe_dk_kohieuqua");
                int so_xe_hqx_duoi70 = rs.getInt("so_xe_hqx_duoi70");
                int so_xe_hqx_duoi50 = rs.getInt("so_xe_hqx_duoi50");
                int so_xe_hqx_duoi30 = rs.getInt("so_xe_hqx_duoi30");

                if (chieu == 1) {
                    dto.setChieu("chieuDi");
                    dto.setSoXeDkKoHieuqua(so_xe_dk_kohieuqua);
                    dto.setSoXeHQXDuoi70(so_xe_hqx_duoi70);
                    dto.setSoXeHQXDuoi50(so_xe_hqx_duoi50);
                    dto.setSoXeHQXDuoi30(so_xe_hqx_duoi30);
                } else if (chieu == 2) {
                    dto.setChieu("chieuVe");

                    dto.setSoXeDkKoHieuqua(so_xe_dk_kohieuqua);
                    dto.setSoXeHQXDuoi70(so_xe_hqx_duoi70);
                    dto.setSoXeHQXDuoi50(so_xe_hqx_duoi50);
                    dto.setSoXeHQXDuoi30(so_xe_hqx_duoi30);
                }
//                dto.setChieu(getNullableInt(rs, "chieu"));
                dataList.add(dto);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query get list dgnl_xetai_dangchay: " + e.getMessage());
        }
        return dataList;
    }

    private Map<String, Double> getHieuQuaDuKienTb(Long ngayBaoCao, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        Map<String, Double> mapData = new HashMap<>();

        StringBuilder stringBuilder = new StringBuilder("SELECT chieu, " +
                " SUM(CASE " +
                " WHEN hqx_luyke < 70 " +
                "  AND is_diemcuoi = 1 " +
                "  AND is_hanhtrinh = 1 " +
                "  AND trangthai_xe = 1 " +
                "  THEN hqx_luyke ELSE 0 END) AS tong_hqx_khong_hieuqua, " +
                "  COUNT(DISTINCT CASE " +
                "  WHEN trangthai_xe = 1 THEN ma_chuyenxe ELSE NULL END) AS tong_so_chuyen " +
                "FROM dgnl_xetai_dangchay where 1 = 1 ");

        List<Object> params = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(ngayBaoCao)) {
            stringBuilder.append(" AND ngay_baocao = ?");
            params.add(ngayBaoCao);
        }

        if (ObjectUtils.isNotEmpty(version)) {
            stringBuilder.append(" AND version = ?");
            params.add(version);
        }

        if (chiNhanh != null) {
            stringBuilder.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            stringBuilder.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            stringBuilder.append(" AND ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            stringBuilder.append(" AND ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        stringBuilder.append(" group by chieu");

        System.out.println("sqlString___ hqdukienTB " + stringBuilder.toString());

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(stringBuilder.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();

            mapData.put("hieuQuaDkTBChieuDi", 0D);
            mapData.put("hieuQuaDkTBChieuVe", 0D);

            while (rs.next()) {

                int chieu = rs.getInt("chieu");
                long tongHqxKoDatHq = rs.getLong("tong_hqx_khong_hieuqua");                          //Tổng hiệu quả xe của các chuyến dự kiến không đạt hiệu quả
                long tong_chuyen = rs.getInt("tong_so_chuyen");
                double hieuQua = ((double) tongHqxKoDatHq / tong_chuyen);

                if (chieu == 1 && tong_chuyen != 0) {
                    mapData.put("hieuQuaDkTBChieuDi", hieuQua);

                } else if (chieu == 2 && tong_chuyen != 0) {
                    mapData.put("hieuQuaDkTBChieuVe", hieuQua);
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query hieuQuaDkTB from dgnl_xetai_dangchay: " + e.getMessage());
        }

        return mapData;
    }

    private List<DgnlXeTaiDangChayDTO> getListDgnlXeTai(Long ngayBaoCao, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<DgnlXeTaiDangChayDTO> dataList = new ArrayList<>();

        StringBuilder sqlString = new StringBuilder("SELECT * FROM dgnl_xetai_dangchay  " +
                "WHERE ngay_baocao = ? AND version = ? AND trangthai_xe = 1 ");

        List<Object> params = new ArrayList<>();

        params.add(ngayBaoCao);
        params.add(version);

        if (chiNhanh != null) {
            sqlString.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                DgnlXeTaiDangChayDTO dto = new DgnlXeTaiDangChayDTO();

                dto.setNgayBaocao(getNullableLong(rs, "ngay_baocao"));
                dto.setVersion(getNullableLong(rs, "version"));
                dto.setNgayKhoihanh(getNullableLong(rs, "ngay_khoihanh"));

                dto.setMaChuyenxe(rs.getString("ma_chuyenxe"));
                dto.setMaDvc(rs.getString("ma_dvc"));
                dto.setChieu(getNullableInt(rs, "chieu"));

                dto.setMaBuucuc(rs.getString("ma_buucuc"));
                dto.setMaCn(rs.getString("ma_cn"));
                dto.setThuTu(getNullableLong(rs, "thu_tu"));

                dto.setIsDiemdungHt(getNullableBoolean(rs, "is_diemdung_ht"));
                dto.setIsDaQua(getNullableBoolean(rs, "is_da_qua"));
                dto.setTgCheckinDukien(getNullableLong(rs, "tg_checkin_dukien"));
                dto.setTgCheckoutDukien(getNullableLong(rs, "tg_checkout_dukien"));
                dto.setTgCheckinThucte(getNullableLong(rs, "tg_checkin_thucte"));
                dto.setTgCheckoutThucte(getNullableLong(rs, "tg_checkout_thucte"));

                dto.setKlLenThucte(getNullableDouble(rs, "kl_len_thucte"));
                dto.setKlLenDukien(getNullableDouble(rs, "kl_len_dukien"));
                dto.setTongKlLenThucteLuyke(getNullableDouble(rs, "tong_kl_len_thucte_luyke"));

                dto.setKlXuongThucte(getNullableDouble(rs, "kl_xuong_thucte"));
                dto.setKlXuongDukien(getNullableDouble(rs, "kl_xuong_dukien"));
                dto.setTongKlXuongThucteLuyke(getNullableDouble(rs, "tong_kl_xuong_thucte_luyke"));

                dto.setTrangthaiXe(getNullableLong(rs, "trangthai_xe"));
                dto.setBiensoXe(rs.getString("bienso_xe"));
                dto.setTaitrongXe(getNullableDouble(rs, "taitrong_xe"));
                dto.setTenChuyenxe(rs.getString("ten_chuyenxe"));
                dto.setTenLaixe(rs.getString("ten_laixe"));
                dto.setSdtLaixe(rs.getString("sdt_laixe"));

                dto.setSoKmSovoiDiemTruocdo(getNullableDouble(rs, "so_km_sovoi_diem_truocdo"));
                dto.setSoKmSovoiDiemSaudo(getNullableDouble(rs, "so_km_sovoi_diem_saudo"));
                dto.setSoKmLuykeDendiem(getNullableDouble(rs, "so_km_luyke_dendiem"));
                dto.setSoKmConLai(getNullableDouble(rs, "so_km_con_lai"));
                dto.setIsHanhtrinh(getNullableInt(rs, "is_hanhtrinh"));
                dto.setIsDiemdau(getNullableInt(rs, "is_diemdau"));
                dto.setIsDiemcuoi(getNullableInt(rs, "is_diemcuoi"));

                dto.setHqxLuyke(getNullableDouble(rs, "hqx_luyke"));
                dto.setHqx1km(getNullableDouble(rs, "hqx_1km"));
                dto.setTileLapday(getNullableDouble(rs, "tile_lapday"));

                dto.setTgCapnhat(getNullableLong(rs, "tg_capnhat"));

                dataList.add(dto);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query get list dgnl_xetai_dangchay: " + e.getMessage());
        }
        return dataList;
    }

    public Map<Integer, long[]> getKhungGioEpoch(LocalDate ngayDanhGia) {

        System.out.println("ngayDanhGia" + ngayDanhGia);
        ZoneId zone = ZoneId.of("Asia/Ho_Chi_Minh");
        Map<Integer, long[]> khungGioMap = new LinkedHashMap<>();

        // Xác định khung giờ hiện tại theo thời gian hệ thống (Asia/Ho_Chi_Minh)
        int currentHour = java.time.ZonedDateTime.now(zone).getHour();
        int currentSlot;
        if (currentHour < 6) {
            currentSlot = 1;
        } else if (currentHour < 12) {
            currentSlot = 2;
        } else if (currentHour < 18) {
            currentSlot = 3;
        } else {
            currentSlot = 4;
        }

        //  chọn ngày đánh giá cho từng khung giờ
        java.util.function.Function<Integer, LocalDate> evalDateForSlot = slotIdx ->
                (slotIdx < currentSlot) ? ngayDanhGia.plusDays(1) : ngayDanhGia;

        // Khung 1: 00:00 - 06:00
        LocalDate ev1 = evalDateForSlot.apply(1);
        khungGioMap.put(1, new long[]{
                ev1.atTime(0, 0).atZone(zone).toInstant().toEpochMilli(),
                ev1.atTime(6, 0).atZone(zone).toInstant().toEpochMilli()
        });

        // Khung 2: 06:00 - 12:00
        LocalDate ev2 = evalDateForSlot.apply(2);
        khungGioMap.put(2, new long[]{
                ev2.atTime(6, 0).atZone(zone).toInstant().toEpochMilli(),
                ev2.atTime(12, 0).atZone(zone).toInstant().toEpochMilli()
        });

        // Khung 3: 12:00 - 18:00
        LocalDate ev3 = evalDateForSlot.apply(3);
        khungGioMap.put(3, new long[]{
                ev3.atTime(12, 0).atZone(zone).toInstant().toEpochMilli(),
                ev3.atTime(18, 0).atZone(zone).toInstant().toEpochMilli()
        });

        // Khung 4: 18:00 - 00:00 (kết thúc ở 00:00 ngày kế tiếp của ngày đánh giá của khung 4)
        LocalDate ev4 = evalDateForSlot.apply(4);
        khungGioMap.put(4, new long[]{
                ev4.atTime(18, 0).atZone(zone).toInstant().toEpochMilli(),
                ev4.plusDays(1).atStartOfDay(zone).toInstant().toEpochMilli()
        });

        return khungGioMap;
    }

    public List<PhanTichTheoKG> phanTichTheoKhungGio(Long ngayBaoCao, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<PhanTichTheoKG> resultList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        String ngayBaoCaoStr = String.valueOf(ngayBaoCao);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate localNgayBaoCao = LocalDate.parse(ngayBaoCaoStr, formatter);

        Map<Integer, long[]> khungGioMap = getKhungGioEpoch(localNgayBaoCao); // 1 → [start, end]

        // Xác định khung giờ hiện tại
        int currentHour = java.time.ZonedDateTime.now(ZoneId.of("Asia/Ho_Chi_Minh")).getHour();
        int currentSlot;
        if (currentHour < 6) {
            currentSlot = 1;
        } else if (currentHour < 12) {
            currentSlot = 2;
        } else if (currentHour < 18) {
            currentSlot = 3;
        } else {
            currentSlot = 4;
        }

        for (int khungGio = 1; khungGio <= 4; khungGio++) {
            long[] epochRange = khungGioMap.get(khungGio);

            // Nếu giờ hiện tại > khung giờ, ngày đánh giá cho khung đó = dd + 1
            LocalDate evalDateForSlot = khungGio < currentSlot ? localNgayBaoCao.plusDays(1) : localNgayBaoCao;
            Long ngayBaoCaoForSlot = Long.parseLong(evalDateForSlot.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

            PhanTichTheoKG dto = queryDataTheoKhungGio(ngayBaoCaoForSlot, epochRange[0], epochRange[1], version, chiNhanh, maDVC, chiNhanhSSO, buuCucSSO, khungGio);
            System.out.println("epochRange: " + epochRange[0] + "__ " + epochRange[1]);
            resultList.add(dto);
        }
        return resultList;
    }

    private PhanTichTheoKG queryDataTheoKhungGio(Long ngayBaoCao, long startEpoch, long endEpoch, Long version, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO, int khungGio) {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT chieu, ")
                .append(" COUNT(*) AS tong_xe, ")
                .append(" SUM(CASE WHEN hqx_luyke < 70 THEN 1 ELSE 0 END) AS chuyen_khong_dat_hq ")
                .append("FROM dgnl_xetai_dangchay ")
                .append("WHERE is_diemcuoi = 1 ")
                .append(" AND ngay_baocao = ? AND tg_checkin_dukien >= ? AND tg_checkin_dukien < ?");

        List<Object> params = new ArrayList<>();
        params.add(ngayBaoCao);
        params.add(startEpoch);
        params.add(endEpoch);

        if (version != null) {
            sql.append(" AND version = ?");
            params.add(version);
        }

        if (chiNhanh != null) {
            sql.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }

        if (maDVC != null) {
            sql.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }

        if (!CollectionUtils.isEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sql.append(" AND ma_cn IN (" + placeholders + ")");
            params.addAll(chiNhanhSSO);
        }

        if (!CollectionUtils.isEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sql.append(" AND ma_dvc IN (" + placeholders + ")");
            params.addAll(buuCucSSO);
        }

        sql.append(" GROUP BY chieu");
        System.out.println("sqlString queryDataTheoKhungGio: " + sql);
        int tongXe = 0;
        int tongKoHQ = 0;
        int tongKoHQChieu1 = 0;
        int tongKoHQChieu2 = 0;

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                int chieu = rs.getInt("chieu");
                int tong = rs.getInt("tong_xe");
                int khongHQ = rs.getInt("chuyen_khong_dat_hq");

                if (chieu == 1) {
                    tongKoHQChieu1 = khongHQ;
                    System.out.println("chieu di: tongKoHQChieu1 " + tongKoHQChieu1);
                } else if (chieu == 2) {
                    tongKoHQChieu2 = khongHQ;
                    System.out.println("chieu di: tongKoHQChieu2 " + tongKoHQChieu2);
                }

                tongXe += tong;
                tongKoHQ += khongHQ;
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query get list canh bao hieu qua xe theo chieu: " + e.getMessage());
        }

        // Tổng xe còn lại đạt hiệu quả là: tổng xe - số xe không đạt
        int tongXeDatHQ = tongXe - tongKoHQ;

        double hqTB = (tongXe == 0) ? 0D : ((double) tongKoHQ / tongXe) * 100;

        return new PhanTichTheoKG(
                khungGio,
                tongKoHQChieu1,
                tongKoHQChieu2,
                tongXeDatHQ,
                hqTB
        );
    }

    private List<DanhSachXeChuY> getListDanhSachXeChuY(Long ngayBaoCao, Long version, Integer hqxLuyKe, Integer chieu, Long startEpochMillis, Long endEpochMillis,
                                                       String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<DanhSachXeChuY> dataList = new ArrayList<>();

        System.out.println("startEpochMillis: " + startEpochMillis);
        System.out.println("endEpochMillis: " + endEpochMillis);

        StringBuilder sqlString = new StringBuilder("SELECT d.chieu as chieu,d.is_diemdung_ht as is_diemdung_ht, d.ma_chuyenxe as ma_chuyenxe, d.bienso_xe as bienso_xe, d.ten_chuyenxe as ten_chuyenxe, d.taitrong_xe as taitrong_xe, " +
                "d.tong_kl_len_thucte_luyke as tong_kl_len_thucte_luyke, d.so_km_con_lai as so_km_con_lai, d.ten_laixe as ten_laixe, d.sdt_laixe as sdt_laixe, d.hqx_luyke as hqx_luyke, " +
                " d.ma_buucuc AS diem_ht_buu_cuc, d.ma_cn AS diem_ht_cn, dc.ma_buucuc AS diem_cuoi_buu_cuc, dc.ma_cn AS diem_cuoi_cn " +
                " FROM dgnl_xetai_dangchay d " +
                " LEFT JOIN dgnl_xetai_dangchay dc ON d.ma_chuyenxe = dc.ma_chuyenxe AND dc.ngay_baocao = ? AND dc.version = ? AND dc.is_diemdau = 1 " +
                " WHERE d.ngay_baocao = ? AND d.version = ? AND d.is_diemcuoi = 1 ");

        List<Object> params = new ArrayList<>();

        params.add(ngayBaoCao);
        params.add(version);
        params.add(ngayBaoCao);
        params.add(version);

        if (hqxLuyKe != null && hqxLuyKe == 1) {         // hqx_luyke khong dat hq
            sqlString.append(" AND d.hqx_luyke < 70");
        }

        if (hqxLuyKe != null && hqxLuyKe == 2) {
            sqlString.append(" AND d.hqx_luyke >= 70");  // hqx_luyke dat hq
        }

        if (chieu != null && chieu != 0) {
            sqlString.append(" AND d.chieu = ?");
            params.add(chieu);
        }

        if (startEpochMillis != null && startEpochMillis != 0) {
            sqlString.append(" AND d.tg_checkin_dukien >= ?");
            params.add(startEpochMillis);
        }
        if (endEpochMillis != null && endEpochMillis != 0) {
            sqlString.append(" AND d.tg_checkin_dukien < ?");
            params.add(endEpochMillis);
        }

        if (chiNhanh != null) {
            sqlString.append(" AND d.ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND d.ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND d.ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND d.ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        sqlString.append(" ORDER BY d.hqx_luyke ASC, d.bienso_xe ASC ");

        System.out.println("sqlString: " + sqlString);
        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                DanhSachXeChuY dto = new DanhSachXeChuY();
//                long tgCapnhat = rs.getLong("tg_capnhat");
//                int chieu = rs.getInt("chieu");

                dto.setChieu(rs.getInt("chieu"));
                dto.setHqxLuyKe(rs.getDouble("hqx_luyke"));
                dto.setBiensoXe(rs.getString("bienso_xe"));
                dto.setMaChuyenxe(rs.getString("ma_chuyenxe"));
                dto.setTenChuyenxe(rs.getString("ten_chuyenxe"));
                dto.setTaitrongXe(rs.getDouble("taitrong_xe"));
                dto.setTongTaiTrongHienTai(rs.getDouble("tong_kl_len_thucte_luyke"));

                dto.setDiemHienTaiBc(rs.getString("diem_ht_buu_cuc"));
                dto.setDiemHienTaiCn(rs.getString("diem_ht_cn"));
                dto.setDiemCuoiBc(rs.getString("diem_cuoi_buu_cuc"));
                dto.setDiemCuoiCn(rs.getString("diem_cuoi_cn"));

                dto.setSokmConLai(rs.getDouble("so_km_con_lai"));
                dto.setTenLaixe(rs.getString("ten_laixe"));
                dto.setSdtLaixe(rs.getString("sdt_laixe"));


                dataList.add(dto);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query get list dgnl_xetai_dangchay: " + e.getMessage());
        }
        return dataList;
    }

    private List<DuBaoHieuQuaTuyen> getListHanhTrinhTuyenXe(Long ngayBaoCao, Long version, String maChuyenxe, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<DuBaoHieuQuaTuyen> dataList = new ArrayList<>();

        StringBuilder sqlString = new StringBuilder("SELECT  * FROM dgnl_xetai_dangchay" +
                " WHERE ngay_baocao = ? AND version = ? AND is_hanhtrinh = 1 ");

        List<Object> params = new ArrayList<>();

        params.add(ngayBaoCao);
        params.add(version);

        if (maChuyenxe != null) {
            sqlString.append(" AND ma_chuyenxe = ?");
            params.add(maChuyenxe);
        }

        if (chiNhanh != null) {
            sqlString.append(" AND ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

//        sqlString.append(" group by chieu, tg_capnhat");
        System.out.println("sqlString: " + sqlString);
        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                DuBaoHieuQuaTuyen dto = new DuBaoHieuQuaTuyen();
//                long tgCapnhat = rs.getLong("tg_capnhat");

                dto.setTenChuyenxe(rs.getString("ten_chuyenxe"));
                dto.setMaChuyenxe(rs.getString("ma_chuyenxe"));
                dto.setMaCn(rs.getString("ma_cn"));
                dto.setMaBuuCuc(rs.getString("ma_buucuc"));

                dto.setThuTu(rs.getString("thu_tu"));
                dto.setIsDiemdungHienTai(rs.getInt("is_diemdung_ht") == 1 ? true : false);
                //Với điểm đến mà xe đã check in, check out thì hiển thị thời gian thực tế check in, check out)

                int isDaQua = rs.getInt("is_da_qua");
                dto.setIsDaQua(isDaQua == 1);
                if (isDaQua == 0) {     // chưa qua điểm này (Chưa checkin)
                    dto.setKlGiao(rs.getDouble("kl_xuong_dukien"));
                    dto.setKlNhan(rs.getDouble("kl_len_dukien"));
                    dto.setTgDuKienDen(rs.getLong("tg_checkin_dukien"));
                    dto.setTgDuKienDi(rs.getLong("tg_checkout_dukien"));
                } else {                                  //
                    dto.setKlGiao(rs.getDouble("kl_xuong_thucte"));
                    dto.setKlNhan(rs.getDouble("kl_len_thucte"));

                    dto.setTgDuKienDen(rs.getLong("tg_checkin_thucte"));
                    dto.setTgDuKienDi(rs.getLong("tg_checkout_thucte"));
                }
                //Khối lượng hàng trên xe = Tổng lũy kế khối lượng hàng đã nhận đến điểm - Tổng lũy kế khối lượng hàng đã giao đến điểm
                dto.setKlHangTrenXe(rs.getDouble("tong_kl_len_thucte_luyke") - rs.getDouble("tong_kl_xuong_thucte_luyke"));
                // Điểm dừng hiện tại với các điểm checkin/checkout

                dto.setHqxLuyKe(rs.getDouble("hqx_luyke"));
                Double taiTrongTinhCuoc = rs.getDouble("taitrong_xe");
                if (taiTrongTinhCuoc != 0) {
                    dto.setTyleLapday((dto.getKlHangTrenXe() / taiTrongTinhCuoc) * 100);
                }
                dto.setHqxTrenKm(rs.getDouble("hqx_1km"));
                dto.setTyleLapdayDiemDau(rs.getDouble("tile_lapday"));

                dataList.add(dto);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query get list dgnl_xetai_dangchay: " + e.getMessage());
        }
        return dataList;
    }

    private List<TopCoHoiBanHang> getTopCoHoiBanHang(Long ngayBaoCao, Long version, String maChuyenxe, String chiNhanh,
                                                     String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<TopCoHoiBanHang> dataList = new ArrayList<>();

        StringBuilder sqlString = new StringBuilder("SELECT * FROM dgnl_xetai_cohoi_banhang" +
                " WHERE ngay_baocao = ? AND version = ? ");

        List<Object> params = new ArrayList<>();

        params.add(ngayBaoCao);
        params.add(version);

        if (maChuyenxe != null) {
            sqlString.append(" AND ma_chuyenxe = ?");
            params.add(maChuyenxe);
        }

        if (chiNhanh != null) {
            sqlString.append(" AND d.ma_cn = ?");
            params.add(chiNhanh);
        }
        if (maDVC != null) {
            sqlString.append(" AND d.ma_dvc = ?");
            params.add(maDVC);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND d.ma_cn IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND d.ma_dvc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

//        sqlString.append(" ORDER BY d.hqx_luyke ASC, d.bienso_xe ASC ");

        System.out.println("sqlString: " + sqlString);
        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                TopCoHoiBanHang dto = new TopCoHoiBanHang();

                dto.setMaChuyenxe(rs.getString("ma_chuyenxe"));
                dto.setDiemDau(rs.getString("diem_dau"));
                dto.setMaBcDiemDau(rs.getString("ma_buucuc_diemdau"));

                dto.setDiemCuoi(rs.getString("diem_cuoi"));
                dto.setMaBcDiemCuoi(rs.getString("ma_buucuc_diemcuoi"));
                dto.setSokgCoTheBan(rs.getDouble("kl_cothe_ban"));
                dto.setSoKmduKien(rs.getDouble("km_dukien"));
                dto.setTgCheckinDukien(rs.getLong("tg_checkin_dukien"));

                dataList.add(dto);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query get getTopCoHoiBanHang from dgnl_xetai_cohoi_banhang: " + e.getMessage());
        }
        return dataList;
    }
}
