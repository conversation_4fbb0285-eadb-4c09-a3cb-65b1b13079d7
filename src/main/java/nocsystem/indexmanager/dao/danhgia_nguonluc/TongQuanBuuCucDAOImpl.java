package nocsystem.indexmanager.dao.danhgia_nguonluc;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.DmBuuCucClone;
import nocsystem.indexmanager.models.Response.*;
import nocsystem.indexmanager.repositories.DmBuuCucCloneRepository;
import nocsystem.indexmanager.util.TongQuanNguonLucUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class TongQuanBuuCucDAOImpl extends AbstractDao implements TongQuanBuuCucDAO{
    private final DmBuuCucCloneRepository dmBuuCucCloneRepository;

    public TongQuanBuuCucDAOImpl(DmBuuCucCloneRepository dmBuuCucCloneRepository) {
        this.dmBuuCucCloneRepository = dmBuuCucCloneRepository;
    }

    @Override
    public TongQuanBuuCucResp getTongQuanBuuCuc(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        TongQuanBuuCucResp tongQuanBuuCucResp = new TongQuanBuuCucResp();
        Long tgHienTai = getMaxVersionChiSo(ngayBaoCao, "baocao_danhgia_nguonluc");
        List<DmBuuCucClone> dmBuuCucList = dmBuuCucCloneRepository.findBuuCucsByIsActiveAndCapIn(1, Arrays.asList(10, 30, 50, 55, 90), chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO);
        tongQuanBuuCucResp.setTongSoBuuCuc(dmBuuCucList.size());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        YearMonth currentMonth = YearMonth.from(ngayBaoCao);
        long count = dmBuuCucList.stream().filter(bc -> {
            try {
                LocalDateTime createTime = LocalDateTime.parse(bc.getCreateTime(), formatter);
                YearMonth createdMonth = YearMonth.from(createTime);
                return createdMonth.equals(currentMonth);
            } catch (Exception e) {
                return false;
            }
        }).count();
        tongQuanBuuCucResp.setSoBuuCucChenhLech((int) count);
        if (ObjectUtils.isNotEmpty(tgHienTai)) {
            tongQuanBuuCucResp.setThoiGianCapNhat(tgHienTai);
            getSoLuongBuuCuc(tgHienTai, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, tongQuanBuuCucResp);
        }

        Long maxVersionSucCo = getMaxVersionSuCo(ngayBaoCao, "baocao_danhgia_nguonluc");
        if (ObjectUtils.isNotEmpty(maxVersionSucCo)) {
            Long tongBuuCucSuCo = getTongBuuCucSuCo(maxVersionSucCo, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO);
            tongQuanBuuCucResp.setBuuCucSuCo(tongBuuCucSuCo);
        }
        //Du bao 7 ngay
        if (ObjectUtils.isNotEmpty(tgHienTai) && ObjectUtils.isNotEmpty(maxVersionSucCo)) {
            DuBaoTongQuanResponse duBaoTongQuanResponse = getBuuCucSuCo(maxVersionSucCo, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO);
            if (ObjectUtils.isNotEmpty(duBaoTongQuanResponse) && CollectionUtils.isNotEmpty(duBaoTongQuanResponse.getBuuCuc())) {
                tongQuanBuuCucResp.setBuuCucNguyCoSuCo(duBaoTongQuanResponse.getBuuCuc().size());
                Long nhanSuDieuChuyen = getNhanSuDieuChuyen(tgHienTai, duBaoTongQuanResponse.getChiNhanh(), duBaoTongQuanResponse.getBuuCuc());
                tongQuanBuuCucResp.setNhanSuDieuChuyen(ObjectUtils.isNotEmpty(nhanSuDieuChuyen) ? nhanSuDieuChuyen : null);
            }
        }
        return tongQuanBuuCucResp;
    }

    @Override
    public List<DubaoChuyenCapResponse> getThongTinDuBaoChuyenCap(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<DubaoChuyenCapResponse> responseList = new ArrayList<>();
        Long tgCapNhat = getMaxVersionChiSo(ngayBaoCao, "baocao_danhgia_nguonluc");
        Long maxVersionSuCo = getMaxVersionSuCo(ngayBaoCao, "baocao_danhgia_nguonluc");
        if (ObjectUtils.isNotEmpty(tgCapNhat) && ObjectUtils.isNotEmpty(maxVersionSuCo)) {
            List<SuCoBuuCucResponse> suCoBuuCucResponses = getDuLieuSuCo(maxVersionSuCo, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO);
            if (CollectionUtils.isNotEmpty(suCoBuuCucResponses)) {
                List<ChiSoBuuCucResponse> chiSoResponses = getDuLieuChiSo(chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, tgCapNhat);
                // map lại nếu cùng chi nhánh, bưu cục thì tính toán ra response
                responseList = mapThongTinDuBaoSuCo(suCoBuuCucResponses, chiSoResponses, tgCapNhat);
            }
        }
        if (CollectionUtils.isNotEmpty(responseList)) {
            return responseList.stream().sorted(Comparator.comparing(DubaoChuyenCapResponse::getThoiGianPhatSinhSuCo, Comparator.nullsLast(Long::compareTo))).collect(Collectors.toList());
        }
        return responseList;
    }

    @Override
    public ChiTietSuCoResp getChiTietSuCo(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        ChiTietSuCoResp chiTietSuCoResp = new ChiTietSuCoResp();
        List<DubaoChuyenCapResponse> responses = getThongTinDuBaoChuyenCap(ngayBaoCao, chiNhanh, buuCuc, null, null);
        if (CollectionUtils.isNotEmpty(responses)){
            DubaoChuyenCapResponse resp = responses.get(0);
            chiTietSuCoResp.setCapSuCo(resp.getCapSuCo());
            chiTietSuCoResp.setXuHuongChuyenCap(resp.getXuHuongChuyenCap());
            chiTietSuCoResp.setTongThoiGianSuCo(resp.getTongThoiGianSuCo());
            chiTietSuCoResp.setCapDoDuBao(resp.getCapDoDuBao());
            if (chiTietSuCoResp.getXuHuongChuyenCap() != null && (chiTietSuCoResp.getXuHuongChuyenCap() >= 0.3 || chiTietSuCoResp.getXuHuongChuyenCap() <= -0.3)) {
                chiTietSuCoResp.setNguyCoChuyenCap((double) Math.round(chiTietSuCoResp.getXuHuongChuyenCap() * 100 * 0.9));
            }
            chiTietSuCoResp.setChiSoTonKhaiThac(resp.getChiSoTonKhaiThac());
            chiTietSuCoResp.setChiSoLacTuyen(resp.getChiSoLacTuyen());
            chiTietSuCoResp.setChiSoTonXuat(resp.getChiSoTonXuat());
            chiTietSuCoResp.setChiSoTonPhat(resp.getChiSoTonPhat());
            chiTietSuCoResp.setChiSoBienDongNhanSu(resp.getChiSoBienDongNhanSu());
            chiTietSuCoResp.setChiSoTonPhatDo789(resp.getChiSoTonPhatDo789());
            Long tgHienTai = ObjectUtils.isNotEmpty(resp.getTgCapNhat()) ? resp.getTgCapNhat() : null;
            Integer tgQuanSat = null;
            Integer gioGanNhatQuetJob = null;
            Integer gioHienTai = getChiSoGio(tgHienTai);
            if (tgHienTai != null) {
                Long tgGanNhat = getThoiGianGanNhat(tgHienTai, "baocao_danhgia_nguonluc", ngayBaoCao);
                tgQuanSat = subtractChiSo(getChiSoGio(tgHienTai), getChiSoGio(tgGanNhat));
                gioGanNhatQuetJob = getChiSoGio(tgGanNhat);
            }
            Map<String, Double> wVuotNguong = new LinkedHashMap<>();
            Integer capBuuCuc = dmBuuCucCloneRepository.findCapBuuCuc(1, chiNhanh, buuCuc);
            Map<String, Double> weightMap = Collections.emptyMap();
            if (capBuuCuc != null) {
                weightMap = TongQuanNguonLucUtils.getWeightMapByCap(capBuuCuc);
            }
            if (chiTietSuCoResp.getChiSoTonKhaiThac() > 120) {
                chiTietSuCoResp.setTgTonKhaiThac(tinhThoiGianOnDinh(chiTietSuCoResp.getChiSoTonKhaiThac(), gioHienTai, gioGanNhatQuetJob, tgQuanSat, 1.2, 120));
                wVuotNguong.put("w1", weightMap.get("w1"));
            }
            if (chiTietSuCoResp.getChiSoLacTuyen() > 10) {
                chiTietSuCoResp.setTgLacTuyen(tinhThoiGianOnDinh(chiTietSuCoResp.getChiSoLacTuyen(), gioHienTai, gioGanNhatQuetJob, tgQuanSat, 1.5, 10));
                wVuotNguong.put("w2", weightMap.get("w2"));
            }
            if (chiTietSuCoResp.getChiSoTonXuat() > 5) {
                chiTietSuCoResp.setTgTonXuat(tinhThoiGianOnDinh(chiTietSuCoResp.getChiSoTonXuat(), gioHienTai, gioGanNhatQuetJob, tgQuanSat, 1.2, 5));
                wVuotNguong.put("w3", weightMap.get("w3"));
            }
            if (chiTietSuCoResp.getChiSoTonPhat() > 200) {
                chiTietSuCoResp.setTgTonPhat(tinhThoiGianOnDinh(chiTietSuCoResp.getChiSoTonPhat(), gioHienTai, gioGanNhatQuetJob, tgQuanSat, 1.3, 200));
                wVuotNguong.put("w4", weightMap.get("w4"));
            }
            if (chiTietSuCoResp.getChiSoBienDongNhanSu() > 80) {
                wVuotNguong.put("w5", weightMap.get("w5"));
            }
            if (chiTietSuCoResp.getChiSoTonPhatDo789() > 20) {
                chiTietSuCoResp.setTgTonDo789(tinhThoiGianOnDinh(chiTietSuCoResp.getChiSoTonPhatDo789(), gioHienTai, gioGanNhatQuetJob, tgQuanSat, 1.5, 20));
                wVuotNguong.put("w6", weightMap.get("w6"));
            }
            chiTietSuCoResp.setSlDonTonPhat(resp.getSlTonPhat());
            chiTietSuCoResp.setSlNhanSuThieu(resp.getNhanVienThieu());
            chiTietSuCoResp.setSlDonTonXuat(resp.getSlTonXuat());
            chiTietSuCoResp.setSlDonTonKhaiThac(subtractChiSo(resp.getSlPhaiKhaiThac(), resp.getSlDaKhaiThac()));

            List<String> nguyenNhanSuCoList = getDanhSachNguyenNhan(wVuotNguong);
            chiTietSuCoResp.setDanhSachNguyenNhan(CollectionUtils.isNotEmpty(nguyenNhanSuCoList) ? nguyenNhanSuCoList : new ArrayList<>());
            //return chiTietSuCoResp;
        }
        return chiTietSuCoResp;
    }

    private List<String> getDanhSachNguyenNhan(Map<String, Double> wVuotNguong) {
        List<String> danhSachWSorted = new ArrayList<>();
        if (wVuotNguong != null && !wVuotNguong.isEmpty()) {
            danhSachWSorted = wVuotNguong.entrySet().stream()
                    .filter(entry -> entry.getValue() != null) // Loại bỏ value null
                    .sorted(Map.Entry.<String, Double>comparingByValue(Comparator.reverseOrder()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
        }
        return danhSachWSorted;
    }

    @Override
    public ThongTinDieuChuyenResp getThongTinDieuChuyenHoTro(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        ThongTinDieuChuyenResp resp = new ThongTinDieuChuyenResp();
        Long tgCapNhat = getMaxVersionChiSo(ngayBaoCao, "baocao_danhgia_nguonluc");
        if (ObjectUtils.isNotEmpty(tgCapNhat)) {
            ChiSoBuuCucResponse chiso = getSoLuongDon(chiNhanh, buuCuc, tgCapNhat);
            Integer tuSo = null;
            Integer tongSanLuongDon = subtract(addNumber(chiso.getSlTonPhat(), chiso.getSlTonChuaPcp()), chiso.getNangLucPhatBq());
            if (ObjectUtils.isEmpty(tongSanLuongDon)) {
                tongSanLuongDon = 0;
            }
            tuSo = subtract(tongSanLuongDon * 100, 200);
            Integer mauSo = addNumber(chiso.getSlTonPhat(), chiso.getSlTonChuaPcp());
            resp.setDonDieuChuyen(multiply(tuSo, mauSo));
        }
        Long maxVersionSucCo = getMaxVersionSuCo(ngayBaoCao, "baocao_danhgia_nguonluc");
        if (ObjectUtils.isNotEmpty(maxVersionSucCo)) {
            List<String> buuCucNguyCoThap = getBuuCucNguyCoThap(maxVersionSucCo, chiNhanh);
            if (ObjectUtils.isNotEmpty(tgCapNhat)) {
                setThongTinDieuChuyen(tgCapNhat, chiNhanh, buuCucNguyCoThap, resp);
            }
        }

        return resp;
    }

    @Override
    public BaoCaoChiTietSuCoResp baoCaoSoSanhChiSo(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        BaoCaoChiTietSuCoResp resp = new BaoCaoChiTietSuCoResp();
        ChiSoSuCoResp chiSoSuCoHienTai = new ChiSoSuCoResp();
        ChiSoSuCoResp chiSoSuCo4hTruoc = new ChiSoSuCoResp();
        ChiSoSuCoResp chiSoSuCoHomQua = new ChiSoSuCoResp();
        ChiSoSuCoResp chiSoSuCoThayDoi = new ChiSoSuCoResp();
        LocalDate ngayHomQua = ngayBaoCao.minusDays(1);
        Map<LocalDate, Long> tgCapNhatMap = getVersionMax(ngayBaoCao, ngayHomQua, "baocao_danhgia_nguonluc");
        Long tgHienTai = tgCapNhatMap.getOrDefault(ngayBaoCao, null);
        Long tgCapNhatHomQua = tgCapNhatMap.getOrDefault(ngayHomQua, null);
        Long tg4hTruoc = getThoiGianCapNhat4hTruoc(tgHienTai, ngayBaoCao);
        if (ObjectUtils.isEmpty(tgHienTai) && ObjectUtils.isEmpty(tgCapNhatHomQua) && ObjectUtils.isEmpty(tg4hTruoc)) {
            return resp;
        }
        List<Long> listTgCapNhat = Arrays.asList(tgHienTai, tgCapNhatHomQua, tg4hTruoc);
        Map<Long, ChiSoBuuCucResponse> chiSoResponses = getDuLieuChiSoTheoThoiGianUpdate(chiNhanh, buuCuc, listTgCapNhat);
        ChiSoBuuCucResponse hienTai = chiSoResponses.get(tgHienTai);
        ChiSoBuuCucResponse capNhatHomQua = chiSoResponses.get(tgCapNhatHomQua);
        ChiSoBuuCucResponse capNhat4hTruoc = chiSoResponses.get(tg4hTruoc);
        if (ObjectUtils.isNotEmpty(hienTai)) {
            setChiSoSuCo(hienTai, chiSoSuCoHienTai);
        }
        if (ObjectUtils.isNotEmpty(capNhatHomQua)) {
            setChiSoSuCo(capNhatHomQua, chiSoSuCoHomQua);
        }
        if (ObjectUtils.isNotEmpty(capNhat4hTruoc)) {
            setChiSoSuCo(capNhat4hTruoc, chiSoSuCo4hTruoc);
        }
        setChiSoThayDoi(chiSoSuCoHomQua, chiSoSuCoHienTai, chiSoSuCoThayDoi);
        resp.setChiSoHienTai(chiSoSuCoHienTai);
        resp.setChiSoThayDoi(chiSoSuCoThayDoi);
        resp.setChiSo4hTruoc(chiSoSuCo4hTruoc);
        resp.setChiSoHomQua(chiSoSuCoHomQua);
        return resp;
    }

    @Override
    public ChiSoChuyenCapResp chiSoChuyenCapSuCo(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        ChiSoChuyenCapResp resp = new ChiSoChuyenCapResp();
        Long maxVersionSuCo = getMaxVersionSuCo(ngayBaoCao, "baocao_danhgia_nguonluc");
        if (ObjectUtils.isNotEmpty(maxVersionSuCo)) {
            resp = getChiSoChuyenCapSuCo(maxVersionSuCo, chiNhanh, buuCuc);
        }
        return resp;
    }

    private ChiSoChuyenCapResp getChiSoChuyenCapSuCo(Long maxVersionSuCo, String chiNhanh, String buuCuc) {
        ChiSoChuyenCapResp resp = new ChiSoChuyenCapResp();
        StringBuilder sql = new StringBuilder("SELECT heso_cuongdo_vipham, heso_thoigian, heso_vantoc_bienthien, heso_moitruong, heso_phuchoi FROM dgnl_suco_buucuc WHERE cap_suco > 0 ");
        List<Object> params = new ArrayList<>();

        if (maxVersionSuCo != null) {
            sql.append(" AND version = ?");
            params.add(maxVersionSuCo);
        }
        if (chiNhanh != null) {
            sql.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }
        if (buuCuc != null) {
            sql.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }
        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                double cuongDo = rs.getDouble("heso_cuongdo_vipham");
                if (!rs.wasNull()) {
                    resp.setCuongDoViPham((int) Math.round(cuongDo));
                }
                double thoiGian = rs.getDouble("heso_thoigian");
                if (!rs.wasNull()) {
                    resp.setThoiGian((double) Math.round(thoiGian * 100) / 100);
                }
                double vanToc = rs.getDouble("heso_vantoc_bienthien");
                if (!rs.wasNull()) {
                    resp.setVanToc((double) Math.round(vanToc * 100) / 100);
                }
                double moiTruong = rs.getDouble("heso_moitruong");
                if (!rs.wasNull()) {
                    resp.setMoiTruong((double) Math.round(moiTruong * 100) / 100);
                }
                double phucHoi = rs.getDouble("heso_phuchoi");
                if (!rs.wasNull()) {
                    resp.setPhucHoi((double) Math.round(phucHoi * 100) / 100);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("Lỗi truy vấn dgnl_suco_buucuc: " + ex.getMessage());
        }
        return resp;
    }

    private void setChiSoThayDoi(ChiSoSuCoResp homQua, ChiSoSuCoResp hienTai, ChiSoSuCoResp chiSoSuCoThayDoi) {
        if (ObjectUtils.isEmpty(homQua) && ObjectUtils.isEmpty(hienTai)) {
            return;
        }
        chiSoSuCoThayDoi.setChiSoTonKT(subtractChiSo(homQua.getChiSoTonKT(), hienTai.getChiSoTonKT()));
        chiSoSuCoThayDoi.setChiSoLacTuyen(subtractChiSo(homQua.getChiSoLacTuyen(), hienTai.getChiSoLacTuyen()));
        chiSoSuCoThayDoi.setChiSoTonXuat(subtractChiSo(homQua.getChiSoTonXuat(), hienTai.getChiSoTonXuat()));
        chiSoSuCoThayDoi.setChiSoTonPhat(subtractChiSo(homQua.getChiSoTonPhat(), hienTai.getChiSoTonPhat()));
        chiSoSuCoThayDoi.setChiSoBienDongNhanSu(subtractChiSo(homQua.getChiSoBienDongNhanSu(), hienTai.getChiSoBienDongNhanSu()));

        chiSoSuCoThayDoi.setDonTonXuat(subtractChiSo(homQua.getDonTonXuat(), hienTai.getDonTonXuat()));
        chiSoSuCoThayDoi.setDonTonPhat(subtractChiSo(homQua.getDonTonPhat(), hienTai.getDonTonPhat()));
        chiSoSuCoThayDoi.setNhanSuThieu(subtractChiSo(homQua.getNhanSuThieu(), hienTai.getNhanSuThieu()));
    }

    private void setChiSoSuCo(ChiSoBuuCucResponse chiSo, ChiSoSuCoResp resp) {
        resp.setChiSoTonKT(tinhTileGeneric(chiSo.getSlPhaiKhaiThac(), chiSo.getSlDaKhaiThacN1(), true));
        resp.setChiSoLacTuyen(tinhTileGeneric(chiSo.getSlLacTuyen(), chiSo.getSlDenTrongCa(), false));
        resp.setChiSoTonXuat(tinhTileGeneric(chiSo.getSlTonChuaKetNoi(), chiSo.getSlNhapDoanhThuN1(), false));
        resp.setChiSoTonPhat(tinhTileGeneric(add(chiSo.getSlTonPhat(), chiSo.getSlTonChuaPcp()), chiSo.getNangLucPhatBq(), true));
        resp.setChiSoBienDongNhanSu(tinhTileGeneric(chiSo.getBuutaN1(), chiSo.getBuutaBq7ngay(), false));

        resp.setDonTonXuat(chiSo.getSlTonChuaKetNoi());
        resp.setDonTonPhat(subtractChiSo(chiSo.getSlPhaiKhaiThac(), chiSo.getSlDaKhaiThacN1()));
        resp.setDonTonPhat(addNumber(chiSo.getSlTonPhat(), chiSo.getSlTonChuaPcp()));
        resp.setNhanSuThieu(chiSo.getBuutaThieu());
    }

    private Map<Long, ChiSoBuuCucResponse> getDuLieuChiSoTheoThoiGianUpdate(String chiNhanh, String buuCuc, List<Long> listTgCapNhat) {
        Map<Long, ChiSoBuuCucResponse> chiSoBuuCucResponseMap = new HashMap<>();
        StringBuilder sql = new StringBuilder("SELECT sl_phai_khaithac, sl_da_khaithac_n1, sl_lac_tuyen, sl_den_trong_ca, sl_ton_chua_ket_noi, sl_nhap_doanh_thu_n1, sl_ton_phat, sl_ton_chua_pcp, nangluc_phat_bq, sl_tonphat_do_789, buuta_n1, buuta_bq_7ngay, buuta_thieu, tg_capnhat FROM dgnl_chiso_buucuc WHERE 1=1");
        List<Object> params = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(listTgCapNhat)) {
            String placeholders = listTgCapNhat.stream().map(s -> "?").collect(Collectors.joining(","));
            sql.append(" AND tg_capnhat IN (").append(placeholders).append(")");
            params.addAll(listTgCapNhat);
        }
        if (chiNhanh != null) {
            sql.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }
        if (buuCuc != null) {
            sql.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                ChiSoBuuCucResponse e = new ChiSoBuuCucResponse();
                e.setSlPhaiKhaiThac(getNullableInt(rs, "sl_phai_khaithac"));
                e.setSlDaKhaiThacN1(getNullableInt(rs, "sl_da_khaithac_n1"));
                e.setSlLacTuyen(getNullableInt(rs, "sl_lac_tuyen"));
                e.setSlDenTrongCa(getNullableInt(rs, "sl_den_trong_ca"));
                e.setSlTonChuaKetNoi(getNullableInt(rs, "sl_ton_chua_ket_noi"));
                e.setSlNhapDoanhThuN1(getNullableInt(rs, "sl_nhap_doanh_thu_n1"));
                e.setSlTonPhat(getNullableInt(rs, "sl_ton_phat"));
                e.setSlTonChuaPcp(getNullableInt(rs, "sl_ton_chua_pcp"));
                e.setNangLucPhatBq(getNullableInt(rs, "nangluc_phat_bq"));
                e.setBuutaBq7ngay(getNullableInt(rs, "buuta_bq_7ngay"));
                e.setBuutaN1(getNullableInt(rs, "buuta_n1"));
                e.setSlTonPhatDo789(getNullableInt(rs, "sl_tonphat_do_789"));
                e.setBuutaThieu(getNullableInt(rs, "buuta_thieu"));
                chiSoBuuCucResponseMap.put(rs.getLong("tg_capnhat"), e);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("Lỗi truy vấn dgnl_chiso_buucuc: " + ex.getMessage());
        }

        return chiSoBuuCucResponseMap;
    }

    private Long getThoiGianCapNhat4hTruoc(Long tgHienTai, LocalDate ngayBaoCao) {
        if (ObjectUtils.isEmpty(tgHienTai)) {
            return null;
        }
        Integer gioCapNhat = getChiSoGio(tgHienTai);
        System.out.println("gioCapNhat: " + gioCapNhat);
        Integer tgChenhLech = subtract(gioCapNhat, 4);
        System.out.println("tgChenhLech: " + tgChenhLech);
        if (gioCapNhat != null) {
            if (tgChenhLech >= 0) {
                return subtract(tgHienTai, 4L);
            } else {
                return null;
            }
        }
        return null;
    }

    private void setThongTinDieuChuyen(Long tgCapNhat, String chiNhanh, List<String> buuCucNguyCoThap, ThongTinDieuChuyenResp resp) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT COUNT(CASE WHEN buuta_thieu = 0 AND buuta_duthua >= 0 THEN 1 END) AS buucuc_nhan_chuyen, " +
                " COUNT(CASE WHEN buuta_thieu = 0 AND buuta_duthua > 0 THEN 1 END) AS buucuc_ho_tro, " +
                " SUM(CASE WHEN buuta_thieu = 0 AND buuta_duthua > 0 THEN buuta_duthua ELSE 0 END) AS nhansu_cuu_tro " +
                " FROM dgnl_chiso_buucuc WHERE tg_capnhat = " + tgCapNhat + " AND ma_chinhanh = '" + chiNhanh + "' AND ma_buucuc IN (" + convertListToString(buuCucNguyCoThap) + ")";
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            if (rs.next()) {
                resp.setBuuCucNhanChuyen(rs.getInt("buucuc_nhan_chuyen"));
                resp.setNhanSuCuuTro(rs.getInt("nhansu_cuu_tro"));
                resp.setBuuCucNhanSuHoTro(rs.getInt("buucuc_ho_tro"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
    }

    private List<String> getBuuCucNguyCoThap(Long maxVersionSucCo, String chiNhanh) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT ma_buucuc as ma_buucuc from dgnl_suco_buucuc where version = " + maxVersionSucCo + " and ma_chinhanh = '" + chiNhanh + "' and chiso_nguyco < 0.4 ";
        List<String> buuCucList = new ArrayList<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            while (rs.next()) {
                buuCucList.add(rs.getString("ma_buucuc"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return buuCucList;
    }

    private ChiSoBuuCucResponse getSoLuongDon(String chiNhanh, String buuCuc, Long tgCapNhat) {
        ChiSoBuuCucResponse response = new ChiSoBuuCucResponse();
        StringBuilder sql = new StringBuilder("SELECT sl_ton_phat, sl_ton_chua_pcp, nangluc_phat_bq FROM dgnl_chiso_buucuc WHERE 1=1 ");
        List<Object> params = new ArrayList<>();

        if (tgCapNhat != null) {
            sql.append(" AND tg_capnhat = ?");
            params.add(tgCapNhat);
        }
        if (chiNhanh != null) {
            sql.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }
        if (buuCuc != null) {
            sql.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            if(rs.next()) {
                response.setSlTonPhat(getNullableInt(rs, "sl_ton_phat"));
                response.setSlTonChuaPcp(getNullableInt(rs, "sl_ton_chua_pcp"));
                response.setNangLucPhatBq(getNullableInt(rs, "nangluc_phat_bq"));
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("Lỗi truy vấn dgnl_chiso_buucuc: " + ex.getMessage());
        }

        return response;
    }
    private List<DubaoChuyenCapResponse> mapThongTinDuBaoSuCo(List<SuCoBuuCucResponse> suCoBuuCucResponses, List<ChiSoBuuCucResponse> chiSoResponses, Long tgCapNhat) {
        List<DubaoChuyenCapResponse> responseList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(suCoBuuCucResponses) && CollectionUtils.isNotEmpty(chiSoResponses)) {
            // Map chiSo theo key: maChiNhanh#maBuuCuc để dễ join
            Map<String, ChiSoBuuCucResponse> chiSoMap = chiSoResponses.stream()
                    .collect(Collectors.toMap(c -> c.getMaChiNhanh() + "#" + c.getMaBuuCuc(), Function.identity()));

            for (SuCoBuuCucResponse suCo : suCoBuuCucResponses) {
                String key = suCo.getMaChiNhanh() + "#" + suCo.getMaBuuCuc();
                ChiSoBuuCucResponse chiSo = chiSoMap.get(key);

                if (ObjectUtils.isNotEmpty(chiSo)) {
                    DubaoChuyenCapResponse response = new DubaoChuyenCapResponse();
                    response.setMaChiNhanh(suCo.getMaChiNhanh());
                    response.setMaBuuCuc(suCo.getMaBuuCuc());
                    response.setCapSuCo(suCo.getCapSuCo());
                    Double xuHuong = suCo.getXuHuongChuyenCap();
                    if (xuHuong != null) {
                        double rounded = Math.round(xuHuong * 100.0) / 100.0;
                        response.setXuHuongChuyenCap(rounded);
                    }

                    // Tính các chỉ số từ chiSo:
                    response.setChiSoTonKhaiThac(tinhTileGeneric(subtractChiSo(chiSo.getSlPhaiKhaiThac(), chiSo.getSlDaKhaiThacN1()), chiSo.getSlDaKhaiThacN1(), true));
                    response.setChiSoLacTuyen(tinhTileGeneric(chiSo.getSlLacTuyen(), chiSo.getSlDenTrongCa(), false));
                    response.setChiSoTonXuat(tinhTileGeneric(chiSo.getSlTonChuaKetNoi(), chiSo.getSlNhapDoanhThuN1(), false));

                    response.setChiSoTonPhat(tinhTileGeneric(add(chiSo.getSlTonPhat(), chiSo.getSlTonChuaPcp()), chiSo.getNangLucPhatBq(), true));
                    response.setChiSoBienDongNhanSu(tinhTileGeneric(chiSo.getBuutaN1(), chiSo.getBuutaBq7ngay(), false));

                    if (chiSo.getSlTonPhatDo789() != null && chiSo.getSlTonPhatDo789() <= 0) {
                        response.setChiSoTonPhatDo789(0);
                    } else {
                        Integer tonChoPhep = tinhTonPhatChoPhep(chiSo.getBuutaBq7ngay());
                        response.setChiSoTonPhatDo789(tinhTileGeneric(subtract(chiSo.getSlTonPhatDo789(), tonChoPhep), tonChoPhep));
                    }
                    response.setThoiGianPhatSinhSuCo(suCo.getThoiGianPhatSinh());
                    response.setTongThoiGianSuCo(tinhChenhlechGio(suCo.getThoiGianPhatSinh()));

                    if (response.getXuHuongChuyenCap() != null && (response.getXuHuongChuyenCap() > 0.3 || response.getXuHuongChuyenCap() < -0.3)) {
                        response.setCapDoDuBao(suCo.getDuBaoChuyenCap());
                    }
                    response.setThoiGianDuKienChuyenCap(suCo.getDuBaoThoiGianChuyen());
                    response.setVuotKhaNang(subtractChiSo(response.getChiSoTonPhat(), 200));
                    response.setKhoiLuongDuBao(add(chiSo.getSlTonPhat(), chiSo.getSlTonChuaPcp(), chiSo.getSlDuKienDenHt()));
                    response.setNhanVienThieu(chiSo.getBuutaThieu());
                    response.setKhoiLuongTang(subtractChiSo(response.getChiSoTonPhat(), 100));
                    response.setTonDongCao(add(chiSo.getSlTonPhat(), chiSo.getSlTonChuaPcp()));
                    response.setTgCapNhat(tgCapNhat);

                    if (suCo.getXuHuongChuyenCap() != null) {
                        response.setKhaNangChuyenCap((double) Math.round(suCo.getXuHuongChuyenCap() * 100));
                        response.setDoTinCayDuBao((double) Math.round(suCo.getXuHuongChuyenCap() * 100));
                    }
                    response.setCapDoAnhHuong(chiSo.getSlTonPhatTonPcp());
                    response.setSlTonXuat(chiSo.getSlTonChuaKetNoi());
                    response.setSlPhaiKhaiThac(chiSo.getSlPhaiKhaiThac());
                    response.setSlDaKhaiThac(chiSo.getSlDaKhaiThacN1());
                    response.setSlTonPhat(addNumber(chiSo.getSlTonPhat(), chiSo.getSlTonChuaPcp()));

                    responseList.add(response);
                }
            }
            return responseList;
        }
        return responseList;
    }

    private List<ChiSoBuuCucResponse> getDuLieuChiSo(String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, Long tgCapNhat) {
        List<ChiSoBuuCucResponse> list = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT ma_chinhanh, ma_buucuc, sl_phai_khaithac, sl_da_khaithac_n1, sl_lac_tuyen, sl_den_trong_ca, sl_ton_chua_ket_noi, sl_nhap_doanh_thu_n1, sl_ton_phat, sl_ton_chua_pcp, nangluc_phat_bq, sl_ptc_max_7ngay, sl_tonphat_do_789, buuta_n1, buuta_bq_7ngay, sl_du_kien_den_ht, buuta_thieu, sl_cus_id_tonphat_tonpcp FROM dgnl_chiso_buucuc WHERE 1=1 ");
        List<Object> params = new ArrayList<>();

        if (tgCapNhat != null) {
            sql.append(" AND tg_capnhat = ?");
            params.add(tgCapNhat);
        }
        if (chiNhanh != null) {
            sql.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }
        if (buuCuc != null) {
            sql.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }
        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sql.append(" AND ma_chinhanh IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sql.append(" AND ma_buucuc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                ChiSoBuuCucResponse e = new ChiSoBuuCucResponse();
                e.setMaChiNhanh(rs.getString("ma_chinhanh"));
                e.setMaBuuCuc(rs.getString("ma_buucuc"));
                e.setSlPhaiKhaiThac(getNullableInt(rs, "sl_phai_khaithac"));
                e.setSlDaKhaiThacN1(getNullableInt(rs, "sl_da_khaithac_n1"));
                e.setSlLacTuyen(getNullableInt(rs, "sl_lac_tuyen"));
                e.setSlDenTrongCa(getNullableInt(rs, "sl_den_trong_ca"));
                e.setSlTonChuaKetNoi(getNullableInt(rs, "sl_ton_chua_ket_noi"));
                e.setSlNhapDoanhThuN1(getNullableInt(rs, "sl_nhap_doanh_thu_n1"));
                e.setSlTonPhat(getNullableInt(rs, "sl_ton_phat"));
                e.setSlTonChuaPcp(getNullableInt(rs, "sl_ton_chua_pcp"));
                e.setNangLucPhatBq(getNullableInt(rs, "nangluc_phat_bq"));
                e.setBuutaBq7ngay(getNullableInt(rs, "buuta_bq_7ngay"));
                e.setBuutaN1(getNullableInt(rs, "buuta_n1"));
                e.setSlTonPhatDo789(getNullableInt(rs, "sl_tonphat_do_789"));
                e.setSlDuKienDenHt(getNullableInt(rs, "sl_du_kien_den_ht"));
                e.setBuutaThieu(getNullableInt(rs, "buuta_thieu"));
                e.setSlTonPhatTonPcp(getNullableLong(rs, "sl_cus_id_tonphat_tonpcp"));
                list.add(e);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("Lỗi truy vấn dgnl_chiso_buucuc: " + ex.getMessage());
        }

        return list;
    }

    private List<SuCoBuuCucResponse> getDuLieuSuCo(Long maxVersionSuCo, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<SuCoBuuCucResponse> list = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT ma_chinhanh, ma_buucuc, cap_suco, xu_huong_chuyen_cap, thoigian_phatsinh_suco, du_bao_chuyen_cap, du_bao_thoi_gian_chuyen FROM dgnl_suco_buucuc WHERE cap_suco > 0 ");
        List<Object> params = new ArrayList<>();

        if (maxVersionSuCo != null) {
            sql.append(" AND version = ?");
            params.add(maxVersionSuCo);
        }
        if (chiNhanh != null) {
            sql.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }
        if (buuCuc != null) {
            sql.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }
        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sql.append(" AND ma_chinhanh IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sql.append(" AND ma_buucuc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                SuCoBuuCucResponse e = new SuCoBuuCucResponse();
                e.setMaChiNhanh(rs.getString("ma_chinhanh"));
                e.setMaBuuCuc(rs.getString("ma_buucuc"));
                e.setCapSuCo(rs.getInt("cap_suco"));

                e.setXuHuongChuyenCap(getNullableDouble(rs, "xu_huong_chuyen_cap"));
                e.setThoiGianPhatSinh(getNullableLong(rs, "thoigian_phatsinh_suco"));
                e.setDuBaoChuyenCap(getNullableInt(rs, "du_bao_chuyen_cap"));
                e.setDuBaoThoiGianChuyen(getNullableInt(rs, "du_bao_thoi_gian_chuyen"));
                list.add(e);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException("Lỗi truy vấn dgnl_suco_buucuc: " + ex.getMessage());
        }
        return list;
    }
    private DuBaoTongQuanResponse getBuuCucSuCo(Long maxVersionSucCo, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        StringBuilder sql = new StringBuilder("SELECT ma_chinhanh, ma_buucuc FROM dgnl_suco_buucuc  WHERE 1=1 \n");
        List<Object> params = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(maxVersionSucCo)) {
            sql.append(" AND version = ?");
            params.add(maxVersionSucCo);
        }

        if (ObjectUtils.isNotEmpty(chiNhanh)) {
            sql.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }

        if (ObjectUtils.isNotEmpty(buuCuc)) {
            sql.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }
        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(i -> "?").collect(Collectors.joining(", "));
            sql.append(" AND ma_chinhanh IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }

        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(i -> "?").collect(Collectors.joining(", "));
            sql.append(" AND ma_buucuc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }
        sql.append(" AND chiso_nguyco >= 0.6 ");
        DuBaoTongQuanResponse response = new DuBaoTongQuanResponse();
        Set<String> chiNhanhResp = new HashSet<>();
        Set<String> buuCucResp = new HashSet<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql.toString());
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            rs = stmt.executeQuery();

            while (rs.next()) {
                chiNhanhResp.add(rs.getString("ma_chinhanh"));
                buuCucResp.add(rs.getString("ma_buucuc"));
            }
            response.setChiNhanh(new ArrayList<>(chiNhanhResp));
            response.setBuuCuc(new ArrayList<>(buuCucResp));
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error in getTongBuuCucSuCo, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private Long getNhanSuDieuChuyen(Long tgHienTai, List<String> chiNhanhList, List<String> buuCucList) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        StringBuilder sql = new StringBuilder("SELECT SUM(buuta_thieu) AS tong_buuta_thieu FROM dgnl_chiso_buucuc WHERE 1=1 ");
        List<Object> params = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(tgHienTai)) {
            sql.append(" AND tg_capnhat = ?");
            params.add(tgHienTai);
        }

        if (CollectionUtils.isNotEmpty(chiNhanhList)) {
            String placeholders = chiNhanhList.stream().map(i -> "?").collect(Collectors.joining(", "));
            sql.append(" AND ma_chinhanh IN (").append(placeholders).append(")");
            params.addAll(chiNhanhList);
        }

        if (CollectionUtils.isNotEmpty(buuCucList)) {
            String placeholders = buuCucList.stream().map(i -> "?").collect(Collectors.joining(", "));
            sql.append(" AND ma_buucuc IN (").append(placeholders).append(")");
            params.addAll(buuCucList);
        }

        Long nhanSuDieuChuyen = null;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql.toString());

            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            rs = stmt.executeQuery();
            if (rs.next()) {
                long nhanSu = rs.getLong("tong_buuta_thieu");
                nhanSuDieuChuyen = rs.wasNull() ? null : nhanSu;
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error in getNhanSuDieuChuyen, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        return nhanSuDieuChuyen;
    }
    private Long getTongBuuCucSuCo(Long maxVersionSucCo, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long tongBuuCucSuCo = 0L;

        StringBuilder sql = new StringBuilder("SELECT COUNT(ma_buucuc) as so_buucuc FROM dgnl_suco_buucuc WHERE version = ?");
        List<Object> params = new ArrayList<>();
        params.add(maxVersionSucCo);

        if (ObjectUtils.isNotEmpty(chiNhanh)) {
            sql.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }

        if (ObjectUtils.isNotEmpty(buuCuc)) {
            sql.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }
        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(i -> "?").collect(Collectors.joining(", "));
            sql.append(" AND ma_chinhanh IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }

        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(i -> "?").collect(Collectors.joining(", "));
            sql.append(" AND ma_buucuc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }

        sql.append(" AND cap_suco > 0");

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql.toString());
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }

            rs = stmt.executeQuery();

            if (rs.next()) {
                tongBuuCucSuCo = rs.getLong("so_buucuc");
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error in getTongBuuCucSuCo, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        return tongBuuCucSuCo;
    }


    private Long getMaxVersionSuCo(LocalDate ngayBaoCao, String baocaoDanhgiaNguonluc) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT max(version) as version from chiso_version where ngay_baocao = date '" + ngayBaoCao + "' and ma_chiso = '" + baocaoDanhgiaNguonluc + "'";
        Long maxVersion = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            if (rs.next()) {
                long rawVersion = rs.getLong("version");
                if (rs.wasNull()) {
                    maxVersion = null;
                } else {
                    maxVersion = rawVersion;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return maxVersion;
    }

    private void getSoLuongBuuCuc(Long tgCapNhat, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, TongQuanBuuCucResp resp) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            String sqlMax = "SELECT COUNT(CASE WHEN buuta_thieu <= 0 THEN 1 END) AS buucuc_du from dgnl_chiso_buucuc ";
            if (ObjectUtils.isNotEmpty(tgCapNhat)) {
                sqlMax += " WHERE tg_capnhat = " + tgCapNhat;
            }
            if (ObjectUtils.isNotEmpty(chiNhanh)) {
                sqlMax += " AND ma_chinhanh = '" + chiNhanh + "'";
            }
            if (ObjectUtils.isNotEmpty(buuCuc)) {
                sqlMax += " AND ma_buucuc = '" + buuCuc + "'";
            }
            if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
                sqlMax += " AND ma_chinhanh IN (" + convertListToString(chiNhanhSSO) + ")";
            }
            if (CollectionUtils.isNotEmpty(buuCucSSO)) {
                sqlMax += " AND ma_buucuc IN (" + convertListToString(buuCucSSO) + ")";
            }
            sqlMax += " group by tg_capnhat";
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            if (rs.next()) {
                Long buuCucCanThiet = rs.getLong("buucuc_du");
                resp.setHieuSuatNguonLuc(tinhTileGeneric(buuCucCanThiet, resp.getTongSoBuuCuc()));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
    }


    private Map<LocalDate, Long> getVersionMax(LocalDate ngayBaoCao, LocalDate endOfMonth, String tenBaoCao) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long maxVersion = null;
        LocalDate date = null;
        Map<LocalDate, Long> mapVersion = new HashMap<>();
        try {
            String sqlMax = String.format("SELECT NGAY_BAOCAO as data_key, max(updated_at) as updated_at from CHISO_VERSION WHERE ma_chiso = '%s'", tenBaoCao) + " AND NGAY_BAOCAO IN ( DATE'" + ngayBaoCao + "'" +
                    ", DATE '" + endOfMonth + "') group by NGAY_BAOCAO";

            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            while (rs.next()) {
                date = rs.getDate("data_key").toLocalDate();
                maxVersion = rs.getLong("updated_at");
                mapVersion.put(date,maxVersion);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return mapVersion;
    }

    private Long getMaxVersionChiSo(LocalDate ngayBaoCao, String baocaoDanhgiaNguonluc) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT max(updated_at) as updated_at from chiso_version where ngay_baocao = date '" + ngayBaoCao + "' and ma_chiso = '" + baocaoDanhgiaNguonluc + "'";
        Long tg_capNhat = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            if (rs.next()) {
                long rawVersion = rs.getLong("updated_at");
                if (rs.wasNull()) {
                    tg_capNhat = null;
                } else {
                    tg_capNhat = rawVersion;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tg_capNhat;
    }

    private String convertLongToString(List<Long> longList) {
        return longList.stream().map(s -> "" + s + "").collect(Collectors.joining(","));
    }
    public static Integer tinhTileGeneric(Number du, Number tong) {
        if (tong == null || tong.doubleValue() <= 0) {
            return null;
        }

        double soDu = (du != null) ? du.doubleValue() : 0.0;
        double tongSo = tong.doubleValue();

        return (int) Math.round((soDu * 100.0) / tongSo);
    }
    public static Integer tinhTileGeneric(Number du, Number tong, boolean isMaxTiLe) {
        if (du ==  null || tong == null) {
            return null;
        }

        double tongSo = tong.doubleValue();
        if (tongSo < 0) {
            return null;
        }

        if (tongSo == 0.0) {
            return isMaxTiLe ? 999 : 0;
        }
        double soDu = du.doubleValue();
        return (int) Math.round((soDu * 100.0) / tongSo);
    }


    public static Long subtract(Long a, Long b) {
        if (a == null || b == null) {
            return null;
        }
        return a - b;
    }
    public static Integer subtract(Integer a, Integer b) {
        if (a == null || b == null) {
            return null;
        }
        return a - b;
    }

    public static Long add(Integer... numbers) {
        if (numbers == null || numbers.length == 0) return null;

        long sum = 0L;
        boolean hasNonNull = false;

        for (Integer num : numbers) {
            if (num != null) {
                sum += num;
                hasNonNull = true;
            }
        }

        return hasNonNull ? sum : null;
    }

    public static Integer tinhTonPhatChoPhep(Integer b) {
        if (b == null) return null;

        return 15 * (b + 1);
    }
    public static Double tinhChenhlechGio(Long tgPhatSinh) {
        if (tgPhatSinh == null) return null;

        String timeStr = tgPhatSinh.toString();
        DateTimeFormatter formatter;

        if (timeStr.length() == 10) {
            formatter = DateTimeFormatter.ofPattern("yyyyMMddHH");
        } else if (timeStr.length() == 14) {
            formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        } else {
            return null;
        }
        
        LocalDateTime thoiGianPhatSinh;
        try {
            thoiGianPhatSinh = LocalDateTime.parse(timeStr, formatter);
        } catch (Exception e) {
            return null;
        }

        Duration duration = Duration.between(thoiGianPhatSinh, LocalDateTime.now());
        double hours = duration.getSeconds() / 3600.0;

        return Math.round(hours * 10.0) / 10.0;
    }
    private String convertListToString(List<String> stringList) {
        return stringList.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
    }
    private Double getNullableDouble(ResultSet rs, String columnLabel) throws SQLException {
        double val = rs.getDouble(columnLabel);
        return rs.wasNull() ? null : val;
    }

    private Long getNullableLong(ResultSet rs, String columnLabel) throws SQLException {
        long val = rs.getLong(columnLabel);
        return rs.wasNull() ? null : val;
    }

    private Integer getNullableInt(ResultSet rs, String columnLabel) throws SQLException {
        int val = rs.getInt(columnLabel);
        return rs.wasNull() ? null : val;
    }
    private Integer getChiSoGio(Long tgHienTai) {
        if (ObjectUtils.isEmpty(tgHienTai)) {
            return null;
        }
        String tgStr = String.valueOf(tgHienTai);
        Integer gio = null;
        if (tgStr.length() == 10) {
            gio = Integer.parseInt(tgStr.substring(8, 10));
        }
        return gio;
    }

    private Long getThoiGianGanNhat(Long tgHienTai, String baocaoDanhgiaNguonluc, LocalDate ngayBaoCao) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlMax = "SELECT max(tg_capnhat) as updated_at from dgnl_chiso_buucuc where tg_capnhat < " + tgHienTai;
        Long tg_capNhat = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            if (rs.next()) {
                long rawVersion = rs.getLong("updated_at");
                if (rs.wasNull()) {
                    tg_capNhat = null;
                } else {
                    tg_capNhat = rawVersion;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tg_capNhat;
    }

    public static Integer subtractChiSo(Integer a, Integer b) {
        if (a == null && b == null) {
            return null;
        }
        int a1 = a == null ? 0 : a;
        int b1 = b == null ? 0 : b;
        return a1 - b1;
    }
    private static Double tinhThoiGianOnDinh(Integer chiSoHienTai, Integer chiSoGioHienTai, Integer chiSoGioGanNhat, Integer thoiGianQuanSatGio, Double heSoDieuChinh, Integer nguongTieuChuan) {

        if (thoiGianQuanSatGio == null || thoiGianQuanSatGio == 0 ||
                heSoDieuChinh == null || nguongTieuChuan == null) {
            return null;
        }

        int chiSo = chiSoHienTai != null ? chiSoHienTai : 0;
        int gioHienTai = chiSoGioHienTai != null ? chiSoGioHienTai : 0;
        int gioGanNhat = chiSoGioGanNhat != null ? chiSoGioGanNhat : 0;

        if ((gioHienTai - gioGanNhat) == 0) {
            return null;
        }

        double tuSo = chiSo - nguongTieuChuan;
        double mauSo = ((double) (gioHienTai - gioGanNhat) / thoiGianQuanSatGio) * 100;

        if (mauSo == 0) {
            return null;
        }

        double ketQua = (tuSo / mauSo) * heSoDieuChinh;

        return Math.round(ketQua * 10.0) / 10.0;
    }

    public static Integer addNumber(Integer... numbers) {
        if (numbers == null || numbers.length == 0) return null;

        int sum = 0;
        boolean hasNonNull = false;

        for (Integer num : numbers) {
            if (num != null) {
                sum += num;
                hasNonNull = true;
            }
        }

        return hasNonNull ? sum : null;
    }
    public static Integer multiply(Integer a, Integer b) {
        if (a == null && b == null) {
            return null;
        }
        int val1 = (a != null) ? a : 0;
        int val2 = (b != null) ? b : 0;
        return val1 * val2;
    }
}
