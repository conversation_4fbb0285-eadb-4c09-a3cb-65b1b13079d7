package nocsystem.indexmanager.dao.danhgia_nguonluc;

import lombok.RequiredArgsConstructor;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.Response.danhgianguonluc.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TinhTrangNhanSuDAOImpl extends AbstractDao implements TinhTrangNhanSuDAO {
    private final TongQuanBuuCucDAOImpl tongQuanBuuCucDAOImpl;

    @Override
    public List<DanhSachBcTinhTrangNhanSu> getTTNhanSuDuBaoNhuCau(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        TinhTrangNSDuBaoNhuCauRes tinhTrangNSDuBaoNhuCauRes = new TinhTrangNSDuBaoNhuCauRes();

        Map<String, Long> mapDataVersion = getVersionMax("baocao_danhgia_nguonluc", ngayBaoCao);
        System.out.println("date: " + ngayBaoCao.toString() + "maxVersion: " + mapDataVersion.get("version"));
        Long version = mapDataVersion.get("version");

        return getTinhTrangNhanSuDuBaoNhuCau(mapDataVersion.get("tgCapNhat"), chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO);
    }

    @Override
    public TienDoXLTonVaDuBaoKLHang3NgayRes getTienDoXuLyTonDongNhomHang(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        TienDoXLTonVaDuBaoKLHang3NgayRes tienDoXLTonVaDuBaoKLHang3NgayRes = new TienDoXLTonVaDuBaoKLHang3NgayRes();
        Map<String, Long> mapDataVersion = getVersionMax("baocao_danhgia_nguonluc", ngayBaoCao);
        // get version hien tai voi updated_at > 8h
        TienDoXuLyTonDongTheoNhomHangRes tienDoXuLyTonDongTheoNhomHangRes = new TienDoXuLyTonDongTheoNhomHangRes();


        Long tgCapNhat8h = generateCapnhatAt8h(ngayBaoCao);

        List<DanhGiaNLChiSoBCDto> dataAt8h = getDetailDgnlChiSoBc(tgCapNhat8h, chiNhanh, buuCuc, null, null);
        List<DanhGiaNLChiSoBCDto> dataCurrent = getDetailDgnlChiSoBc(mapDataVersion.get("tgCapNhat"), chiNhanh, buuCuc, null, null);


        if (CollectionUtils.isNotEmpty(dataAt8h)) {

            // lay du lieu theo tung BC
            DetailTienDoXuLy detailTienDoXuLy7 = new DetailTienDoXuLy();

            detailTienDoXuLy7.setSlDauNgay(dataAt8h.get(0).getSlTonphatDo7());   // sl ban dau 8h sang
            detailTienDoXuLy7.setSlHienTai(dataCurrent.get(0).getSlTonphatDo7());
            detailTienDoXuLy7.setSlDaXuLy(subtractChiSo(detailTienDoXuLy7.getSlDauNgay(), detailTienDoXuLy7.getSlHienTai()));
            detailTienDoXuLy7.setSlPhatSinhThem(subtractChiSo(detailTienDoXuLy7.getSlHienTai(), detailTienDoXuLy7.getSlDauNgay()));
            if (detailTienDoXuLy7.getSlDaXuLy() != 0 && detailTienDoXuLy7.getSlDaXuLy() > 0) {
                detailTienDoXuLy7.setTyLeXuLy(tinhTyLe(detailTienDoXuLy7.getSlDaXuLy(), detailTienDoXuLy7.getSlDauNgay()));
            } else {
                detailTienDoXuLy7.setTyLeXuLy(0.0);
            }


            DetailTienDoXuLy detailTienDoXuLy8 = new DetailTienDoXuLy();
            detailTienDoXuLy8.setSlDauNgay(dataAt8h.get(0).getSlTonphatDo8());
            detailTienDoXuLy8.setSlHienTai(dataCurrent.get(0).getSlTonphatDo8());
            detailTienDoXuLy8.setSlDaXuLy(subtractChiSo(detailTienDoXuLy8.getSlDauNgay(), detailTienDoXuLy8.getSlHienTai()));
            detailTienDoXuLy8.setSlPhatSinhThem(subtractChiSo(detailTienDoXuLy8.getSlHienTai(), detailTienDoXuLy8.getSlDauNgay()));
            if (detailTienDoXuLy8.getSlDaXuLy() != null && detailTienDoXuLy8.getSlDaXuLy() > 0) {
                detailTienDoXuLy8.setTyLeXuLy(tinhTyLe(detailTienDoXuLy8.getSlDaXuLy(), detailTienDoXuLy8.getSlDauNgay()));
            } else {
                detailTienDoXuLy8.setTyLeXuLy(0.0);
            }

            DetailTienDoXuLy detailTienDoXuLy9 = new DetailTienDoXuLy();
            detailTienDoXuLy9.setSlDauNgay(dataAt8h.get(0).getSlTonphatDo9());
            detailTienDoXuLy9.setSlHienTai(dataCurrent.get(0).getSlTonphatDo9());
            detailTienDoXuLy9.setSlDaXuLy(subtractChiSo(detailTienDoXuLy9.getSlDauNgay(), detailTienDoXuLy9.getSlHienTai()));
            detailTienDoXuLy9.setSlPhatSinhThem(subtractChiSo(detailTienDoXuLy9.getSlHienTai(), detailTienDoXuLy9.getSlDauNgay()));
            if (detailTienDoXuLy9.getSlDaXuLy() != null && detailTienDoXuLy9.getSlDaXuLy() > 0) {
                detailTienDoXuLy9.setTyLeXuLy(tinhTyLe(detailTienDoXuLy9.getSlDaXuLy(), detailTienDoXuLy9.getSlDauNgay()));
            } else {
                detailTienDoXuLy9.setTyLeXuLy(0.0);
            }

            tienDoXuLyTonDongTheoNhomHangRes.setMaCn(dataCurrent.get(0).getMaChinhanh());
            tienDoXuLyTonDongTheoNhomHangRes.setMaBc(dataCurrent.get(0).getMaBuucuc());
            tienDoXuLyTonDongTheoNhomHangRes.setNhomHangDo7(detailTienDoXuLy7);
            tienDoXuLyTonDongTheoNhomHangRes.setNhomHangDo8(detailTienDoXuLy8);
            tienDoXuLyTonDongTheoNhomHangRes.setNhomHangDo9(detailTienDoXuLy9);
            tienDoXuLyTonDongTheoNhomHangRes.setThoiGianCapNhat(mapDataVersion.get("tgCapNhat"));
        }
        System.out.println("data version" + mapDataVersion.toString() + "__ : " + mapDataVersion.get("tgCapNhat"));
        tienDoXLTonVaDuBaoKLHang3NgayRes.setTienDoXuLyTonDongTheoNhomHangRes(tienDoXuLyTonDongTheoNhomHangRes);


        DuBaoKLHang3NgayToiRes duBaoKLHang3NgayToiRes = new DuBaoKLHang3NgayToiRes();

        duBaoKLHang3NgayToiRes.setMaCn(dataCurrent.get(0).getMaChinhanh());
        duBaoKLHang3NgayToiRes.setMaBc(dataCurrent.get(0).getMaBuucuc());
        duBaoKLHang3NgayToiRes.setThoiGianCapNhat(mapDataVersion.get("tgCapNhat"));
        duBaoKLHang3NgayToiRes.setHomNay(dataCurrent.get(0).getSlDuKienDenHt());
        Integer tyleTangHomNay;
        if (dataCurrent.get(0).getSlDuKienDenTb7ngay() != 0) {
            tyleTangHomNay =Math.round(((float) dataCurrent.get(0).getSlDuKienDenHt() / dataCurrent.get(0).getSlDuKienDenTb7ngay()) * 100);

        } else {
            tyleTangHomNay = 0;
        }
        duBaoKLHang3NgayToiRes.setTyLeTangHomNay(tyleTangHomNay);
        duBaoKLHang3NgayToiRes.setNgayMai(dataCurrent.get(0).getSlDuKienDenN1());
        Integer tyleTangNgayMai;
        if (dataCurrent.get(0).getSlDuKienDenTb7ngay() != 0) {
            tyleTangNgayMai = Math.round(((float) dataCurrent.get(0).getSlDuKienDenN1() / dataCurrent.get(0).getSlDuKienDenTb7ngay()) * 100);
        } else {
            tyleTangNgayMai = 0;
        }
        duBaoKLHang3NgayToiRes.setTyLeTangNgayMai(tyleTangNgayMai);
        Integer tyleTangNgayKia;
        if (dataCurrent.get(0).getSlDuKienDenTb7ngay() != 0) {
            tyleTangNgayKia = Math.round(((float) dataCurrent.get(0).getSlDuKienDenN2() / dataCurrent.get(0).getSlDuKienDenTb7ngay()) * 100);
        } else {
            tyleTangNgayKia = 0;
        }

        duBaoKLHang3NgayToiRes.setTyLeTangNgayKia(tyleTangNgayKia);
        duBaoKLHang3NgayToiRes.setNgayKia(dataCurrent.get(0).getSlDuKienDenN2());

        tienDoXLTonVaDuBaoKLHang3NgayRes.setDuBaoKLHang3NgayToiRes(duBaoKLHang3NgayToiRes);

        return tienDoXLTonVaDuBaoKLHang3NgayRes;

    }

    private List<DanhSachBcTinhTrangNhanSu> getTinhTrangNhanSuDuBaoNhuCau(Long tgCapNhat, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {

        DanhSachBcTinhTrangNhanSu resDetail = null;
        List<DanhSachBcTinhTrangNhanSu> listDataBc = new ArrayList<>();

        StringBuilder sqlString = new StringBuilder("SELECT * from dgnl_chiso_buucuc where 1=1 ");
        List<Object> params = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(tgCapNhat)) {
            sqlString.append(" AND tg_capnhat = ?");
            params.add(tgCapNhat);
        }

        if (chiNhanh != null) {
            sqlString.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }
        if (buuCuc != null) {
            sqlString.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }
//            sqlString += " group by tg_capnhat";
        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_chinhanh IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_buucuc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }
        System.out.println("string query" + sqlString.toString());

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {
            System.out.println("sqlString query: " + sqlString);
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            System.out.println("stmt: " + stmt.toString());
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                resDetail = new DanhSachBcTinhTrangNhanSu();
                Map<String, Long> resDetailDuBaoNhuCau = new LinkedHashMap<>();

                tgCapNhat = rs.getLong("tg_capnhat");
                resDetail.setThoiGianCapNhat(tgCapNhat);

                resDetail.setMaCn(rs.getString("ma_chinhanh"));
                resDetail.setMaBc(rs.getString("ma_buucuc"));
                resDetail.setNsHienTai(rs.getInt("buuta_bq_7ngay"));
                resDetail.setNsCanThiet(rs.getInt("buuta_canthiet"));
                resDetail.setNsThieuHut(rs.getInt("buuta_thieu"));
                resDetail.setNsDuThua(rs.getInt("buuta_duthua"));


                //trangThaiNs = Thiếu hụt / Cần thiết hoặc Dư Thừa / Cần Thiết
                Double trangThaiNs = 0D;
                if (resDetail.getNsCanThiet() != 0 && resDetail.getNsDuThua().equals(0)) {
                    trangThaiNs = -((resDetail.getNsThieuHut().doubleValue()) / resDetail.getNsCanThiet().doubleValue()) * 100;
                } else if (resDetail.getNsCanThiet() != 0 && resDetail.getNsThieuHut().equals(0)) {
                    trangThaiNs = (resDetail.getNsDuThua() / resDetail.getNsCanThiet().doubleValue()) * 100;
                }

                resDetail.setTrangThaiNs(Math.ceil(trangThaiNs * 100) / 100);

                //Ngày 1 = (Tồn phát + tồn phân công phát đến thời điểm hiện tại)x
                // tỷ lệ giải phóng hết tồn trung bình 3 ngày gần nhất kể từ ngày hiện tại + đơn dự kiến đến vào mai
                Integer tuNgay1 = rs.getInt("sl_ton_phat") + rs.getInt("sl_ton_chua_pcp");
                Integer slDonDuKienDenNgayN = rs.getInt("sl_du_kien_den_ht");
                Double ngay1 = tuNgay1.doubleValue() * 0.6 + slDonDuKienDenNgayN;
                resDetailDuBaoNhuCau.put("ngay_1", Math.round(ngay1));

                //Ngày 2 = Ngày 1 *  tỷ lệ giải phóng hết tồn trung bình 3 ngày gần nhất kể từ ngày hiện tại + đơn dự kiến đến vào ngày kia
                Integer slDonDuKienDenNgayN1 = rs.getInt("sl_du_kien_den_n1");
                Double ngay2 = ngay1 * 0.6 + slDonDuKienDenNgayN1;
                resDetailDuBaoNhuCau.put("ngay_2", Math.round(ngay2));

                //Ngày 3 = Ngày 2 *  tỷ lệ giải phóng hết tồn trung bình 3 ngày gần nhất kể từ ngày hiện tại + đơn dự kiến đến vào ngày kìa
                Integer slDonDuKienDenNgayN2 = rs.getInt("sl_du_kien_den_n2");
                Double ngay3 = ngay2 * 0.6 + slDonDuKienDenNgayN2;
                resDetailDuBaoNhuCau.put("ngay_3", Math.round(ngay3));

                resDetail.setDuBaoNhuCau(resDetailDuBaoNhuCau);
                listDataBc.add(resDetail);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query dgnl_chiso_buucuc" + e.getMessage());
        }
        //Lấy ra danh sách bưu cục đang bị thiếu nhân sự, sắp xếp số lượng nhân sự thiếu từ cao đến thấp,
        // nếu có 2 bưu cục cùng thiếu số lượng nhân sự tương đương thì sx theo bảng chữ cái của mã BC
        List<DanhSachBcTinhTrangNhanSu> listDataFilter = listDataBc.stream()
                .filter(item -> item.getNsThieuHut() != null && item.getNsThieuHut() > 0)
                .sorted(Comparator.comparing(DanhSachBcTinhTrangNhanSu::getNsThieuHut, Comparator.reverseOrder())
                        .thenComparing(DanhSachBcTinhTrangNhanSu::getMaBc))
                .collect(Collectors.toList());
        return listDataFilter;
    }

    public Map<String, Long> getVersionMax(String tenBaoCao, LocalDate ngayBaoCao) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlMax = "";
        Map<String, Long> mapDataVersion = new HashMap<>();
        if (ngayBaoCao != null) {
            sqlMax = " SELECT version, updated_at FROM chiso_version WHERE ma_chiso = ? AND version = ( SELECT MAX(version) FROM chiso_version WHERE ma_chiso = ?)";
        } else {
            return null;
        }
        System.out.println("ngayBaoCao: " + ngayBaoCao + "__ " + "sqlMax: " + sqlMax);
        Long maxVersion = null;
        Long tgCapNhat = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            stmt.setString(1, tenBaoCao);
            stmt.setString(2, tenBaoCao);

            rs = stmt.executeQuery();

            while (rs.next()) {
                maxVersion = rs.getLong("version");
                tgCapNhat = rs.getLong("updated_at");

                mapDataVersion.put("version", maxVersion);
                mapDataVersion.put("tgCapNhat", tgCapNhat);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        return mapDataVersion;
    }


    private Long generateCapnhatAt8h(LocalDate ngayBaoCao) {
        // localdata at 8h
        LocalDateTime timeAt8AM = ngayBaoCao.atTime(8, 0);

        // formatted yyyyMMddHH
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHH");
        String formatted = timeAt8AM.format(formatter);

        return Long.parseLong(formatted);
    }

    public List<DanhGiaNLChiSoBCDto> getDetailDgnlChiSoBc(Long tgCapNhat, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO) {
        List<DanhGiaNLChiSoBCDto> data = new ArrayList<>();

        StringBuilder sqlString = new StringBuilder("SELECT * from dgnl_chiso_buucuc where 1=1 ");
        List<Object> params = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(tgCapNhat)) {
            sqlString.append(" AND tg_capnhat = ?");
            params.add(tgCapNhat);
        }

        if (chiNhanh != null) {
            sqlString.append(" AND ma_chinhanh = ?");
            params.add(chiNhanh);
        }
        if (buuCuc != null) {
            sqlString.append(" AND ma_buucuc = ?");
            params.add(buuCuc);
        }
//            sqlString += " group by tg_capnhat";
        if (CollectionUtils.isNotEmpty(chiNhanhSSO)) {
            String placeholders = chiNhanhSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_chinhanh IN (").append(placeholders).append(")");
            params.addAll(chiNhanhSSO);
        }
        if (CollectionUtils.isNotEmpty(buuCucSSO)) {
            String placeholders = buuCucSSO.stream().map(s -> "?").collect(Collectors.joining(","));
            sqlString.append(" AND ma_buucuc IN (").append(placeholders).append(")");
            params.addAll(buuCucSSO);
        }
        System.out.println("string query" + sqlString.toString());

        try (Connection conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
             PreparedStatement stmt = conn.prepareStatement(sqlString.toString())) {
            System.out.println("sqlString query: " + sqlString);
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            System.out.println("stmt: " + stmt.toString());
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                DanhGiaNLChiSoBCDto dataDetail = convertToDanhGiaNLChiSoBCDto(rs);
                data.add(dataDetail);
            }
            System.out.println("Data: " + data.toString());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error query dgnl_chiso_buucuc" + e.getMessage());
        }
        return data;
    }

    private DanhGiaNLChiSoBCDto convertToDanhGiaNLChiSoBCDto(ResultSet rs) throws SQLException {
        DanhGiaNLChiSoBCDto dataDetail = new DanhGiaNLChiSoBCDto();

        dataDetail.setTgCapnhat(rs.getLong("tg_capnhat"));
        dataDetail.setMaChinhanh(rs.getString("ma_chinhanh"));
        dataDetail.setMaBuucuc(rs.getString("ma_buucuc"));

        dataDetail.setSlPhaiKhaithac(rs.getInt("sl_phai_khaithac"));
        dataDetail.setSlDaKhaithacN1(rs.getInt("sl_da_khaithac_n1"));

        dataDetail.setSlLacTuyen(rs.getInt("sl_lac_tuyen"));
        dataDetail.setSlDenTrongCa(rs.getInt("sl_den_trong_ca"));

        dataDetail.setSlTonChuaKetNoi(rs.getInt("sl_ton_chua_ket_noi"));
        dataDetail.setSlNhapDoanhThuN1(rs.getInt("sl_nhap_doanh_thu_n1"));

        dataDetail.setSlTonPhat(rs.getInt("sl_ton_phat"));
        dataDetail.setSlTonChuaPcp(rs.getInt("sl_ton_chua_pcp"));
        dataDetail.setNanglucPhatBq(rs.getInt("nangluc_phat_bq"));
        dataDetail.setSlPtcMax7ngay(rs.getLong("sl_ptc_max_7ngay"));

        dataDetail.setSlTonphatDo7(rs.getInt("sl_tonphat_do7"));
        dataDetail.setSlTonphatDo8(rs.getInt("sl_tonphat_do8"));
        dataDetail.setSlTonphatDo9(rs.getInt("sl_tonphat_do9"));
        dataDetail.setSlTonphatDo789(rs.getInt("sl_tonphat_do_789"));

        dataDetail.setBuutaN1(rs.getInt("buuta_n1"));
        dataDetail.setBuutaBq7ngay(rs.getInt("buuta_bq_7ngay"));

        dataDetail.setBuutaCanthiet(rs.getInt("buuta_canthiet"));
        dataDetail.setBuutaDuthua(rs.getInt("buuta_duthua"));
        dataDetail.setBuutaThieu(rs.getInt("buuta_thieu"));

        dataDetail.setSlDuKienDenHt(rs.getInt("sl_du_kien_den_ht"));
        dataDetail.setSlDuKienDenN1(rs.getInt("sl_du_kien_den_n1"));
        dataDetail.setSlDuKienDenN2(rs.getInt("sl_du_kien_den_n2"));
        dataDetail.setSlDuKienDenTb7ngay(rs.getInt("sl_du_kien_den_tb_7ngay"));

        dataDetail.setSlCusIdTonphatTonpcp(rs.getLong("sl_cus_id_tonphat_tonpcp"));

        return dataDetail;
    }
    public static Integer subtractChiSo(Integer a, Integer b) {
        if (a == null && b == null) {
            return null;
        }
        int a1 = a == null ? 0 : a;
        int b1 = b == null ? 0 : b;
        return a1 - b1;
    }

    public static Double tinhTyLe(Number a, Number b) {
        if (a == null || b == null) return 0.0;
        double numerator = a.doubleValue();
        double denominator = b.doubleValue();
        if (denominator == 0.0) return 0.0;
        double result = (numerator / denominator) * 100.0;
        return Math.ceil(result * 10) / 10.0; // làm tròn lên 1 chữ số thập phân
    }
}
