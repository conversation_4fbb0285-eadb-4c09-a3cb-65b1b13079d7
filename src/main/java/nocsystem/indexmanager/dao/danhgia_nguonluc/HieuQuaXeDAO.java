package nocsystem.indexmanager.dao.danhgia_nguonluc;

import nocsystem.indexmanager.models.Response.hieuquaxe.*;

import java.time.LocalDate;
import java.util.List;

public interface HieuQuaXeDAO {
    TongQuanHQXRes tongQuanHQX(LocalDate ngayBaoCao, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO);

    CanhBaoHQXTheoChieuRes canhBaoHQX(LocalDate ngayBaoCao, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO);

    List<DanhSachXeChuY> danhSachXeCanChuY(Integer khungGio, LocalDate ngayBaoCao, List<Integer> types, Integer chieu, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO);

    List<DuBaoHieuQuaTuyen> duBaoHieuQuaTuyenXe(LocalDate ngayBaoCao, String maChuyenxe, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO);

    List<TopCoHoiBanHang> topCoHoiBanHang(LocalDate ngayBaoCao, String maChuyenxe, String chiNhanh, String maDVC, List<String> chiNhanhSSO, List<String> buuCucSSO);
}
