package nocsystem.indexmanager.dao.danhgia_nguonluc;

import nocsystem.indexmanager.models.Response.*;

import java.time.LocalDate;
import java.util.List;

public interface TongQuanBuuCucDAO {
    TongQuanBuuCucResp getTongQuanBuuCuc(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO);

    List<DubaoChuyenCapResponse> getThongTinDuBaoChuyenCap(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO);

    ChiTietSuCoResp getChiTietSuCo(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);

    ThongTinDieuChuyenResp getThongTinDieuChuyenHoTro(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);

    BaoCaoChiTietSuCoResp baoCaoSoSanhChiSo(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);

    ChiSoChuyenCapResp chiSoChuyenCapSuCo(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);
}
