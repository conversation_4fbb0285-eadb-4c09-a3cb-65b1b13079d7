package nocsystem.indexmanager.dao.danhgia_nguonluc;

import nocsystem.indexmanager.models.Response.danhgianguonluc.DanhSachBcTinhTrangNhanSu;
import nocsystem.indexmanager.models.Response.danhgianguonluc.TienDoXLTonVaDuBaoKLHang3NgayRes;

import java.time.LocalDate;
import java.util.List;

public interface TinhTrangNhanSuDAO {
    List<DanhSachBcTinhTrangNhanSu> getTTNhanSuDuBaoNhuCau(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO);

    TienDoXLTonVaDuBaoKLHang3NgayRes getTienDoXuLyTonDongNhomHang(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);

}
