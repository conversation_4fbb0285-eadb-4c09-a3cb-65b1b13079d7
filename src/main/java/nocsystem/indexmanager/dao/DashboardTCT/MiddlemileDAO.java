package nocsystem.indexmanager.dao.DashboardTCT;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.SLDiemKetNoiDto;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.TLKetNoiDto;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.TTKTDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.util.*;

import static nocsystem.indexmanager.util.StringUtil.castListToQuery;
import static nocsystem.indexmanager.util.StringUtil.castListToQueryParam;

@Service
public class MiddlemileDAO extends AbstractDao {
    private final Logger logger = LoggerFactory.getLogger(MiddlemileDAO.class);
    private String FILTER_VERSION = "FILTER_DATE_KEY";
    private String FILTER_DATE_VERSION_KEY = "FILTER_DATE_VERSION_KEY";

    public List<TTKTDto> getTTKT(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<TTKTDto> listTTKT = new ArrayList<>();

        Map<String, String> queryFilter = filterDate(ngayBaoCao, luyKe, "ngay_bao_cao", "version", "kpi-log-ttkt-khai-thac");
        String filterDateVersion = queryFilter.get(FILTER_DATE_VERSION_KEY);
        String filterListVersion = queryFilter.get(FILTER_VERSION);

        if (filterDateVersion.equals("") || filterListVersion.equals("")) {
            return listTTKT;
        }

        String filterChiNhanh = chiNhanh.equals("") ? "" : "and chi_nhanh = '" + chiNhanh + "' ";
        String filterBuuCuc = buuCuc.equals("") ? "" : "and don_vi = '" + buuCuc + "' ";

        String sql = "select don_vi, sum(tg_khai_thac) as tg_khai_thac, sum(tong_don_hang) as tong_don_hang " +
                "from kpi_log_ttkt_khai_thac_th " +
                "where chi_nhanh = 'LOG' " +
                filterChiNhanh + filterBuuCuc +
                filterListVersion + filterDateVersion +
                "and don_vi in ('TTKT1', 'TTKT2', 'TTKT3', 'TTKT4', 'TTKT5') " +
                "group by don_vi " +
                "order by don_vi";
        logger.info("sql", sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TTKTDto ttkt = new TTKTDto();
                ttkt.setTtkt(rs.getString("don_vi"));
                if ( rs.getLong("tong_don_hang") != 0L) {
                    Double tgKhaiThacMillis = (double)(rs.getLong("tg_khai_thac") / rs.getLong("tong_don_hang"));
                    Double tgKhaiThac = castMillisToHours(tgKhaiThacMillis);
                    ttkt.setTgKhaiThac((double) Math.round(tgKhaiThac * 100) / 100);
                }
                listTTKT.add(ttkt);
            }
            logger.info("list ttkt", listTTKT);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return listTTKT;
    }

    public Long getSoBPKhaiThac(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> queryFilter = filterDate(ngayBaoCao, luyKe, "ngay_bao_cao", "version", "kpi-log-ttkt-khai-thac");
        String filterDateVersion = queryFilter.get(FILTER_DATE_VERSION_KEY);
        String filterListVersion = queryFilter.get(FILTER_VERSION);

        if (filterDateVersion.equals("") || filterListVersion.equals("")) {
            return null;
        }

        String filterChiNhanh = chiNhanh.equals("") ? "" : "and chi_nhanh = '" + chiNhanh + "' ";
        String filterBuuCuc = buuCuc.equals("") ? "" : "and don_vi = '" + buuCuc + "' ";

        String sql = "select sum(tong_don_hang) as tong_don_hang " +
                "from kpi_log_ttkt_khai_thac_th " +
                "where chi_nhanh = 'LOG' " +
                filterChiNhanh + filterBuuCuc +
                filterListVersion + filterDateVersion;
        logger.info("sql", sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                return rs.getLong("tong_don_hang");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return null;
    }

    public Double getTGKhaiThac(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> queryFilter = filterDate(ngayBaoCao, luyKe, "ngay_bao_cao", "version", "kpi-log-ttkt-khai-thac");
        String filterDateVersion = queryFilter.get(FILTER_DATE_VERSION_KEY);
        String filterListVersion = queryFilter.get(FILTER_VERSION);

        if (filterDateVersion.equals("") || filterListVersion.equals("")) {
            return null;
        }

        String filterChiNhanh = chiNhanh.equals("") ? "" : "and chi_nhanh = '" + chiNhanh + "' ";
        String filterBuuCuc = buuCuc.equals("") ? "" : "and don_vi = '" + buuCuc + "' ";

        String sql = "select sum(tg_khai_thac) as tg_khai_thac, sum(tong_don_hang) as tong_don_hang " +
                "from kpi_log_ttkt_khai_thac_th " +
                "where chi_nhanh = 'LOG' " +
                filterChiNhanh + filterBuuCuc +
                filterListVersion + filterDateVersion;
        logger.info("sql", sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                if (rs.getLong("tong_don_hang") != 0L) {
                    Double tgKhaiThacMillis = (double) (rs.getLong("tg_khai_thac") / rs.getLong("tong_don_hang"));
                    return (double) Math.round(castMillisToHours(tgKhaiThacMillis) * 100) / 100;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return null;
    }

    public Long getSLTaiKienNhap(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> queryFilter = filterDate(ngayBaoCao, luyKe, "ngay_bao_cao", "version", "kpi-log-ttkt-nhap-xuat");
        String filterDateVersion = queryFilter.get(FILTER_DATE_VERSION_KEY);
        String filterListVersion = queryFilter.get(FILTER_VERSION);

        if (filterDateVersion.equals("") || filterListVersion.equals("")) {
            return null;
        }

        String filterChiNhanh = chiNhanh.equals("") ? "" : "and chi_nhanh = '" + chiNhanh + "' ";
        String filterBuuCuc = buuCuc.equals("") ? "" : "and don_vi = '" + buuCuc + "' ";

        String sql = "select sum(tong_tai_kien_nhap) as tong_tai_kien_nhap " +
                "from kpi_log_ttkt_nhap_xuat_th " +
                "where chi_nhanh = 'LOG' " +
                filterChiNhanh + filterBuuCuc +
                filterListVersion + filterDateVersion;
        logger.info("sql", sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                return rs.getLong("tong_tai_kien_nhap");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return null;
    }

    public SLDiemKetNoiDto getSLDiemKetNoi(LocalDate ngayBaoCao, Integer luyKe) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        SLDiemKetNoiDto slDiemKetNoiDto = new SLDiemKetNoiDto();

        Map<String, String> queryFilter = filterDate(ngayBaoCao, luyKe, "data_date_key", "version", "");
        String filterDateVersion = queryFilter.get(FILTER_DATE_VERSION_KEY);
        String filterListVersion = queryFilter.get(FILTER_VERSION);

        if (filterDateVersion.equals("") || filterListVersion.equals("")) {
            return slDiemKetNoiDto;
        }

        String sql = "SELECT " +
                "SUM(CASE WHEN TEN_CHI_SO = 'DU_SO_DIEM' THEN GIA_TRI ELSE 0 END) AS slDiemCamKet, " +
                "SUM(CASE WHEN TEN_CHI_SO = 'DU_SO_DIEM_REAL' THEN GIA_TRI ELSE 0 END) AS slDiemKetNoiTT, " +
                "SUM(CASE WHEN TEN_CHI_SO = 'DUNG_SO_DIEM' THEN GIA_TRI ELSE 0 END) AS slDiemKetNoiDG " +
                "FROM LOG_BCTLDD_TH " +
                "WHERE 1 = 1 " +
                filterListVersion + filterDateVersion;
        logger.info("sql", sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                if (rs.getDouble("slDiemCamKet") != 0.0) {
                    slDiemKetNoiDto.setSlDiemCamKet(rs.getDouble("slDiemCamKet"));
                }
                if (rs.getDouble("slDiemKetNoiTT") != 0.0) {
                    slDiemKetNoiDto.setSlDiemKetNoiTT(rs.getDouble("slDiemKetNoiTT"));
                }
                if (rs.getDouble("slDiemKetNoiDG") != 0.0) {
                    slDiemKetNoiDto.setSlDiemKetNoiDG(rs.getDouble("slDiemKetNoiDG"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return slDiemKetNoiDto;
    }

    public TLKetNoiDto getTLKetNoi(LocalDate ngayBaoCao, Integer luyKe, Integer direction) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        TLKetNoiDto tlKetNoiDto = new TLKetNoiDto();

        Map<String, String> queryFilter = filterDate(ngayBaoCao, luyKe, "data_date_key", "version", "");
        String filterDateVersion = queryFilter.get(FILTER_DATE_VERSION_KEY);
        String filterListVersion = queryFilter.get(FILTER_VERSION);

        if (filterDateVersion.equals("") || filterListVersion.equals("")) {
            return tlKetNoiDto;
        }

        String filterDirection = direction != null ? "and direction = " + direction : "";

        String sql = "SELECT " +
                "SUM(CASE WHEN TEN_CHI_SO = 'DU_SO_DIEM' THEN GIA_TRI ELSE 0 END) AS slDiemCamKet, " +
                "SUM(CASE WHEN TEN_CHI_SO = 'DU_SO_DIEM_REAL' THEN GIA_TRI ELSE 0 END) AS slDiemKetNoiTT, " +
                "SUM(CASE WHEN TEN_CHI_SO = 'DUNG_SO_DIEM' THEN GIA_TRI ELSE 0 END) AS slDiemKetNoiDG " +
                "FROM LOG_BCTLDD_TH " +
                "WHERE 1 = 1 " +
                filterDirection + filterListVersion + filterDateVersion;
        logger.info("sql", sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                if (rs.getDouble("slDiemCamKet") != 0.0) {
                    tlKetNoiDto.setTlKetNoiDu((double) Math.round((rs.getDouble("slDiemKetNoiTT") * 100 / rs.getDouble("slDiemCamKet")) * 100) / 100);
                }
                if (rs.getDouble("slDiemKetNoiTT") != 0.0) {
                    tlKetNoiDto.setTlKetNoiDG((double) Math.round((rs.getDouble("slDiemKetNoiDG") * 100 / rs.getDouble("slDiemKetNoiTT")) * 100) / 100);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tlKetNoiDto;
    }

    public Double getHieuQuaXe(LocalDate ngayBaoCao, Integer luyKe, List<String> hanhTrinh, Integer direction) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String filterDate = luyKe == 1 ?
                "data_date_key >= date '" + ngayBaoCao.withDayOfMonth(1) + "' and data_date_key <= date '" + ngayBaoCao + "' "
                : "data_date_key = date '" + ngayBaoCao + "' ";
        String filterHanhtrinh = "and hanh_trinh in " + castListToQueryParam(hanhTrinh);
        String filterDirection = direction != null ? "and direction = " + direction : "";

        String sql = "select sum(khoi_luong) as khoi_luong, sum(trong_tai) as trong_tai " +
                "from log_bchqx_ct " +
                "where " + filterDate + filterHanhtrinh + filterDirection;
        logger.info("sql", sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                if (rs.getDouble("trong_tai") != 0.0) {
                    return (double) Math.round((rs.getDouble("khoi_luong") * 100 / rs.getDouble("trong_tai")) * 100) / 100;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return null;
    }

    public Map<String, String> filterDate(LocalDate ngayBaoCao, Integer luyKe, String dateColumnName, String versionColumnName, String maChiSo){
        Map<String, String> queryFilter = new HashMap<>();
        Map<Date, Long> maxVersion = getVersionMax(ngayBaoCao, luyKe, maChiSo);

        List<Date> listDate = new ArrayList<>(maxVersion.keySet());
        if(maxVersion.keySet() == null || maxVersion.keySet().isEmpty()){
            queryFilter.put(FILTER_VERSION, "");
            queryFilter.put(FILTER_DATE_VERSION_KEY, "");
            return queryFilter;
        }

        List<Long> versionList = new ArrayList<>(maxVersion.values());
        String multiVersion = castListToQuery(versionList);

        String multiDateVersion = "";
        for(int i = 0; i < listDate.size(); i++){
            Date date = listDate.get(i);
            Long version = maxVersion.get(date);
            String dateVersion = String.format(" ( %s = date '%s' AND %s = %s ) ", dateColumnName, date, versionColumnName, version);
            if(i != listDate.size() - 1){
                dateVersion = dateVersion + " OR ";
            }
            multiDateVersion += dateVersion;
        }

        multiDateVersion =  String.format(" AND ( %s ) ", multiDateVersion);
        multiVersion = String.format(" AND version in %s ", multiVersion);

        queryFilter.put(FILTER_DATE_VERSION_KEY, multiDateVersion);
        queryFilter.put(FILTER_VERSION, multiVersion);
        return queryFilter;
    }

    public Map<Date, Long> getVersionMax(LocalDate ngayBaoCao, Integer luyKe, String maChiSo) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        LocalDate ngayDauThang = luyKe == 1 ? ngayBaoCao.withDayOfMonth(1) : ngayBaoCao;

        String sqlMax;
        if (maChiSo.isEmpty()) {
            sqlMax = "select data_date_key, max(version) as version from LOG_BCTLDD_VERSION where data_date_key >= date '" + ngayDauThang +
                    "' and data_date_key <= date '" + ngayBaoCao + "' group by data_date_key";
        } else {
            sqlMax = String.format("select ngay_baocao as data_date_key, max(version) as version from chiso_version where ma_chiso = '%s'", maChiSo)
                    + " and ngay_baocao >= date '" + ngayDauThang + "' and ngay_baocao <= date '" + ngayBaoCao + "' group by ngay_baocao";
        }
        Long maxVersion = null;
        Date date = null;
        Map<Date, Long> mapVersion = new HashMap<>();
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            while (rs.next()) {
                date = rs.getDate("data_date_key");
                maxVersion = rs.getLong("version");
                mapVersion.put(date,maxVersion);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return mapVersion;
    }

    public static Double castMillisToHours(Double millis){
        if(millis != null){
            return millis / (1000.0 * 60 * 60);
        }
        return null;
    }
}
