package nocsystem.indexmanager;

import nocsystem.indexmanager.services.ReconciliationGrab.ReconciliationGrabService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@SpringBootApplication
@EnableScheduling
public class ScheduledTasksApplication {
//    @Autowired
//    ReconciliationGrabService reconciliationGrabService;

//    @Scheduled(cron = "0 0/30 6 * * *")
    public void jobPullPositiveTopLevel() throws Exception {
//        reconciliationGrabService.processToSaveDataBase();
    }
}
