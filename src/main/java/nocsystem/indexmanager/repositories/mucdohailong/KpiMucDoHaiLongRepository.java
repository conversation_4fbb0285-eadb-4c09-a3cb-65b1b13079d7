package nocsystem.indexmanager.repositories.mucdohailong;

import nocsystem.indexmanager.models.mucdohailong.KpiMucDoHaiLong;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface KpiMucDoHaiLongRepository extends JpaRepository<KpiMucDoHaiLong, Long> {

    @Query("select m from KpiMucDoHaiLong m where " +
            "(COALESCE(:maVung, NULL) is NULL OR m.maVung in (:maVung)) " +
            "AND (COALESCE(:maChiNhanh, NULL) is NULL OR m.maChiNhanh in (:maChiNhanh)) " +
            "AND m.nam = :nam")
    List<KpiMucDoHaiLong> findByKeysInput(List<String> maVung, List<String> ma<PERSON><PERSON><PERSON><PERSON><PERSON>, String nam);
}
