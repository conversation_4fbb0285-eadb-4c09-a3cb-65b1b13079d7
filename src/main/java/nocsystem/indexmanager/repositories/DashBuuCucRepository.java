package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatChiTietDisplayDto;
import nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatChiTietExcelDto;
import nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatDetail;

import nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatDisplayDto;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhat;
import org.springframework.data.domain.Page;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface DashBuuCucRepository extends JpaRepository<DashBuuCucTonPhatDetail, Long> {

    //find all data new
    @Query(value = "select new nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatDisplayDto( " +
            " d.chiNhanh, " +
            " SUM(CASE WHEN d.loaiCanhBao ='VANG' THEN d.tongSL ELSE 0 END) as vang," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_1' THEN d.tongSL ELSE 0 END) as do1," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_2' THEN d.tongSL ELSE 0 END) as do2," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_3' THEN d.tongSL ELSE 0 END) as do3," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_4' THEN d.tongSL ELSE 0 END) as do4," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_5' THEN d.tongSL ELSE 0 END) as do5," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_6' THEN d.tongSL ELSE 0 END) as do6," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_7' THEN d.tongSL ELSE 0 END) as do7," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_8' THEN d.tongSL ELSE 0 END) as do8," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_9' THEN d.tongSL ELSE 0 END) as do9," +
            " SUM(CASE WHEN d.loaiCanhBao ='CHUA_XAC_DINH' THEN d.tongSL ELSE 0 END) as chuaxacdinh," +
            "SUM(d.tongSL) as tong)" +
            " from TonPhat d " +
            " where d.ngayBaoCao = :ngayBaoCao " +
            " AND (COALESCE(:chiNhanh, NULL) is NULL OR d.chiNhanh in (:chiNhanh)) " +
            " AND (COALESCE(:buuCuc, NULL) is NULL OR d.maBuuCuc in (:buuCuc)) " +
            " AND (:nguongTon is null OR d.loaiTon = :nguongTon) " +
            " AND (:loaiCanhBao is null OR d.loaiCanhBao = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.nhomDV = :loaiDichVu)" +
            " AND (:tuyenBuuTaPhat is null OR d.buuTaPhat = :tuyenBuuTaPhat) " +
            " AND (:trangThai is null OR d.trangThai = :trangThai)" +
            " AND (:vungCon is null OR d.vungCon = :vungCon)" +
            " AND (:maDoiTac is null OR d.maDoiTac = :maDoiTac)" +
            " AND (:loaiHH is null OR d.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or d.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or d.khDacThuNhan = :khDacThuNhan)" +
            " AND d.version = :version " +
            " group by d.chiNhanh "
    )
    Page<DashBuuCucTonPhatDisplayDto> findAllNew(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            List<String> buuCuc,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String tuyenBuuTaPhat,
            String trangThai,
            String vungCon,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    );

    @Query(value = "select new nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatDisplayDto( " +
            " d.chiNhanh , d.vungCon, d.maBuuCuc," +
            " SUM(CASE WHEN d.loaiCanhBao ='VANG' THEN d.tongSL ELSE 0 END) as vang," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_1' THEN d.tongSL ELSE 0 END) as do1," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_2' THEN d.tongSL ELSE 0 END) as do2," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_3' THEN d.tongSL ELSE 0 END) as do3," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_4' THEN d.tongSL ELSE 0 END) as do4," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_5' THEN d.tongSL ELSE 0 END) as do5," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_6' THEN d.tongSL ELSE 0 END) as do6," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_7' THEN d.tongSL ELSE 0 END) as do7," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_8' THEN d.tongSL ELSE 0 END) as do8," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_9' THEN d.tongSL ELSE 0 END) as do9," +
            " SUM(CASE WHEN d.loaiCanhBao ='CHUA_XAC_DINH' THEN d.tongSL ELSE 0 END) as chuaxacdinh," +
            "SUM(d.tongSL) as tong)" +
            " from TonPhat d " +
            " where d.ngayBaoCao = :ngayBaoCao " +
            " AND (COALESCE(:chiNhanh, NULL) is NULL OR d.chiNhanh in (:chiNhanh)) " +
            " AND (COALESCE(:buuCuc, NULL) is NULL OR d.maBuuCuc in (:buuCuc)) " +
            " AND (:nguongTon is null OR d.loaiTon = :nguongTon) " +
            " AND (:loaiCanhBao is null OR d.loaiCanhBao = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.nhomDV = :loaiDichVu)" +
            " AND (:tuyenBuuTaPhat is null OR d.buuTaPhat = :tuyenBuuTaPhat)" +
            " AND (:trangThai is null OR d.trangThai = :trangThai)" +
            " AND (:vungCon is null OR d.vungCon = :vungCon)" +
            " AND (:maDoiTac is null OR d.maDoiTac = :maDoiTac)" +
            " AND (:loaiHH is null OR d.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or d.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or d.khDacThuNhan = :khDacThuNhan)" +
            " AND d.version = :version " +
            " group by d.chiNhanh , d.vungCon, d.maBuuCuc"
    )
    Page<DashBuuCucTonPhatDisplayDto> findAllNew1(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            List<String> buuCuc,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String tuyenBuuTaPhat,
            String trangThai,
            String vungCon,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    );

    @Query(value = "select new nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatDisplayDto( " +
            " d.chiNhanh , d.vungCon, d.maBuuCuc, d.buuTaPhat," +
            " SUM(CASE WHEN d.loaiCanhBao ='VANG' THEN d.tongSL ELSE 0 END) as vang," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_1' THEN d.tongSL ELSE 0 END) as do1," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_2' THEN d.tongSL ELSE 0 END) as do2," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_3' THEN d.tongSL ELSE 0 END) as do3," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_4' THEN d.tongSL ELSE 0 END) as do4," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_5' THEN d.tongSL ELSE 0 END) as do5," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_6' THEN d.tongSL ELSE 0 END) as do6," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_7' THEN d.tongSL ELSE 0 END) as do7," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_8' THEN d.tongSL ELSE 0 END) as do8," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_9' THEN d.tongSL ELSE 0 END) as do9," +
            " SUM(CASE WHEN d.loaiCanhBao ='CHUA_XAC_DINH' THEN d.tongSL ELSE 0 END) as chuaxacdinh," +
            "SUM(d.tongSL) as tong)" +
            " from TonPhat d " +
            " where d.ngayBaoCao = :ngayBaoCao " +
            " AND (COALESCE(:chiNhanh, NULL) is NULL OR d.chiNhanh in (:chiNhanh)) " +
            " AND (COALESCE(:buuCuc, NULL) is NULL OR d.maBuuCuc in (:buuCuc)) " +
            " AND (:nguongTon is null OR d.loaiTon = :nguongTon)" +
            " AND (:loaiCanhBao is null OR d.loaiCanhBao = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.nhomDV = :loaiDichVu)" +
            " AND (:tuyenBuuTaPhat is null OR d.buuTaPhat = :tuyenBuuTaPhat)" +
            " AND (:trangThai is null OR d.trangThai = :trangThai)" +
            " AND (:vungCon is null OR d.vungCon = :vungCon)" +
            " AND (:maDoiTac is null OR d.maDoiTac = :maDoiTac)" +
            " AND (:loaiHH is null OR d.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or d.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or d.khDacThuNhan = :khDacThuNhan)" +
            " AND d.version = :version  " +
            " group by d.chiNhanh , d.vungCon, d.maBuuCuc, d.buuTaPhat"
    )
    Page<DashBuuCucTonPhatDisplayDto> findAllNew2(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            List<String> buuCuc,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String tuyenBuuTaPhat,
            String trangThai,
            String vungCon,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    );

    @Query(value = "select new nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatDisplayDto( " +
            " d.chiNhanh , d.vungCon, d.maBuuCuc, d.buuTaPhat," +
            " SUM(CASE WHEN d.loaiCanhBao ='VANG' THEN d.tongSL ELSE 0 END) as vang," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_1' THEN d.tongSL ELSE 0 END) as do1," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_2' THEN d.tongSL ELSE 0 END) as do2," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_3' THEN d.tongSL ELSE 0 END) as do3," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_4' THEN d.tongSL ELSE 0 END) as do4," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_5' THEN d.tongSL ELSE 0 END) as do5," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_6' THEN d.tongSL ELSE 0 END) as do6," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_7' THEN d.tongSL ELSE 0 END) as do7," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_8' THEN d.tongSL ELSE 0 END) as do8," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_9' THEN d.tongSL ELSE 0 END) as do9," +
            " SUM(CASE WHEN d.loaiCanhBao ='CHUA_XAC_DINH' THEN d.tongSL ELSE 0 END) as chuaxacdinh," +
            "SUM(d.tongSL) as tong)" +
            " from TonPhat d " +
            " where d.ngayBaoCao = :ngayBaoCao " +
            " AND (COALESCE(:chiNhanh, NULL) is NULL OR d.chiNhanh in (:chiNhanh)) " +
            " AND (COALESCE(:buuCuc, NULL) is NULL OR d.maBuuCuc in (:buuCuc)) " +
            " AND (:nguongTon is null OR d.loaiTon = :nguongTon)" +
            " AND (:loaiCanhBao is null OR d.loaiCanhBao = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.nhomDV = :loaiDichVu)" +
            " AND (:tuyenBuuTaPhat is null OR d.buuTaPhat = :tuyenBuuTaPhat) " +
            " AND (:trangThai is null OR d.trangThai = :trangThai)" +
            " AND (:vungCon is null OR d.vungCon = :vungCon)" +
            " AND (:maDoiTac is null OR d.maDoiTac = :maDoiTac)" +
            " AND (:loaiHH is null OR d.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or d.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or d.khDacThuNhan = :khDacThuNhan)" +
            " AND d.version = :version  " +
            " group by  d.chiNhanh , d.vungCon, d.maBuuCuc, d.buuTaPhat"
    )
    Page<DashBuuCucTonPhatDisplayDto> findAllNew3(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            List<String> buuCuc,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String tuyenBuuTaPhat,
            String trangThai,
            String vungCon,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    );

    @Query(value = "select new nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatDisplayDto( " +
            " SUM(CASE WHEN d.loaiCanhBao ='VANG' THEN d.tongSL ELSE 0 END) as vang," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_1' THEN d.tongSL ELSE 0 END) as do1," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_2' THEN d.tongSL ELSE 0 END) as do2," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_3' THEN d.tongSL ELSE 0 END) as do3," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_4' THEN d.tongSL ELSE 0 END) as do4," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_5' THEN d.tongSL ELSE 0 END) as do5," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_6' THEN d.tongSL ELSE 0 END) as do6," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_7' THEN d.tongSL ELSE 0 END) as do7," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_8' THEN d.tongSL ELSE 0 END) as do8," +
            " SUM(CASE WHEN d.loaiCanhBao ='DO_9' THEN d.tongSL ELSE 0 END) as do9," +
            " SUM(CASE WHEN d.loaiCanhBao ='CHUA_XAC_DINH' THEN d.tongSL ELSE 0 END) as chuaxacdinh," +
            "SUM(d.tongSL) as tong)" +
            " from TonPhat d " +
            " where d.ngayBaoCao = :ngayBaoCao " +
            " AND (COALESCE(:chiNhanh, NULL) is NULL OR d.chiNhanh in (:chiNhanh)) " +
            " AND (COALESCE(:buuCuc, NULL) is NULL OR d.maBuuCuc in (:buuCuc)) " +
            " AND (:nguongTon is null OR d.loaiTon = :nguongTon) " +
            " AND (:loaiCanhBao is null OR d.loaiCanhBao = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.nhomDV = :loaiDichVu)" +
            " AND (:tuyenBuuTaPhat is null OR d.buuTaPhat = :tuyenBuuTaPhat) " +
            " AND (:vungCon is null OR d.vungCon = :vungCon) " +
            " AND (:maDoiTac is null OR d.maDoiTac = :maDoiTac)" +
            " AND (:loaiHH is null OR d.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or d.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or d.khDacThuNhan = :khDacThuNhan)" +
            " AND d.version = :version " +
            " AND (:trangThai is null OR d.trangThai = :trangThai)"
    )
    DashBuuCucTonPhatDisplayDto findSumNew(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            List<String> buuCuc,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String tuyenBuuTaPhat,
            String trangThai,
            String vungCon,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    );

    @Query("Select distinct  d.tuyenBuuTaPhat from DashBuuCucTonPhatDetail d" +
            " where d.ngayBaoCao = :ngayBaoCao" +
            " AND (COALESCE(:chiNhanh, NULL) is NULL OR d.chiNhanhHT in (:chiNhanh)) " +
            " AND (COALESCE(:buuCuc, NULL) is NULL OR d.maBuuCucHT in (:buuCuc)) " +
            " AND (:nguongTon is null OR d.nguongTon = :nguongTon) " +
            " AND (:loaiCanhBao is null OR d.dgMocLM = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.loaiDichVu = :loaiDichVu)" +
            " AND (:tuyenBuuTaPhat is null OR d.tuyenBuuTaPhat = :tuyenBuuTaPhat) " +
            " AND d.version = :version " +
            " AND (:trangThai is null OR d.trangThai = :trangThai)"
    )
    List<String> findAllTuyenBuuTa(LocalDate ngayBaoCao,
                                   List<String> chiNhanh,
                                   List<String> buuCuc,
                                   String nguongTon,
                                   String loaiCanhBao,
                                   String loaiDichVu,
                                   String tuyenBuuTaPhat,
                                   String trangThai,
                                   long version
    );




    @Query(value = "select new nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatChiTietDisplayDto( " +
            " d.maPhieuGui , d.trangThai, d.dgMocLM, d.chiNhanhHT, d.vungCon, d.maBuuCucHT, d.tuyenBuuTaPhat, d.loaiDichVu, d.nguongTon, d.timePCP)" +
            " from DashBuuCucTonPhatDetail d where d.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:chiNhanhPhat, NULL) is NULL or d.chiNhanhHT in (:chiNhanhPhat)) " +
            " and (COALESCE(:buuCucPhat, NULL) is NULL or d.maBuuCucHT in (:buuCucPhat)) " +
            " AND (:tuyenBuuTaPhat is null OR d.tuyenBuuTaPhat = :tuyenBuuTaPhat) " +
            " AND (:nguongTon is null OR d.nguongTon = :nguongTon) " +
            " AND (:loaiCanhBao is null OR d.dgMocLM = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.loaiDichVu = :loaiDichVu)" +
            " AND (:vungCon is null OR d.vungCon = :vungCon)" +
            " AND (:maDoiTac is null OR d.maDoiTac = :maDoiTac)" +
            " AND (:loaiHH is null OR d.loaiHangHoa = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or d.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or d.khDacThuNhan = :khDacThuNhan)" +
            " AND d.version = :version" +
            " AND (:trangThai is null OR d.trangThai = :trangThai)"
    )
    Page<DashBuuCucTonPhatChiTietDisplayDto> findAll1(
            LocalDate ngayBaoCao,
            List<String> chiNhanhPhat,
            List<String> buuCucPhat,
            String tuyenBuuTaPhat,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String trangThai,
            String vungCon,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    );

    @Query(value = "select new nocsystem.indexmanager.models.DashBuuCuc.DashBuuCucTonPhatChiTietExcelDto( " +
            " d.maPhieuGui , TO_CHAR(d.ngayBaoCao,'YYYY-MM-dd'), d.tinhNhan, d.huyenNhan, d.tenHuyenNhan, d.ttktFrom, d.chiNhanhPhat," +
            " d.huyenPhat, d.tenHuyenPhat, d.ttktTo, d.maDvViettel, d.maBuuCucGoc, d.timeTacDong, d.trangThai," +
            " d.maBuuCucHT, d.chiNhanhHT, d.maDoiTac, d.maKHGui, d.buuCucPhat, d.timePCP, d.timeGachBP," +
            " d.ngayguiBP, d.danhGia, d.loaiPG, d.lanPhat, d.tgConPhat, d.tuyenBuuTaPhat, d.tienCOD, d.khauFM," +
            " d.khauMM, d.khauLM, d.tgQuyDinh, d.tgTTLuyKe, d.tgChenhLech, d.dgMocMM, d.nguongTon," +
            " d.loaiPCP, d.loaiCanhBao, d.dgMocLM, d.loaiDichVu, d.version, d.timeTinhToan, d.trongLuong, d.tienCuoc, d.loaiHangHoa, " +
            "d.khDacThuGui, d.khDacThuNhan, d.idPhuongXaPhat, d.tenPhuongXaPhat, d.maDichVuCongThem, d.tgTonPcpCuoiCung, d.timePcpDauTien, d.loaiDon)" +
            " from DashBuuCucTonPhatDetail d where d.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:chiNhanhPhat, NULL) is NULL or d.chiNhanhHT in (:chiNhanhPhat)) " +
            " and (COALESCE(:buuCucPhat, NULL) is NULL or d.maBuuCucHT in (:buuCucPhat)) " +
            " AND (:tuyenBuuTaPhat is null OR d.tuyenBuuTaPhat = :tuyenBuuTaPhat) " +
            " AND (:nguongTon is null OR d.nguongTon = :nguongTon) " +
            " AND (:loaiCanhBao is null OR d.dgMocLM = :loaiCanhBao)" +
            " AND (:loaiDichVu is null OR d.loaiDichVu = :loaiDichVu)" +
            " AND (:vungCon is null OR d.vungCon = :vungCon)" +
            " AND (:maDoiTac is null OR d.maDoiTac = :maDoiTac)" +
            " AND (:loaiHH is null OR d.loaiHangHoa = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or d.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or d.khDacThuNhan = :khDacThuNhan)" +
            " AND d.version = :version " +
            " AND (:trangThai is null OR d.trangThai = :trangThai)"
    )
    List<DashBuuCucTonPhatChiTietExcelDto> findAllListExportExcel(
            LocalDate ngayBaoCao,
            List<String> chiNhanhPhat,
            List<String> buuCucPhat,
            String tuyenBuuTaPhat,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String vungCon,
            String trangThai,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    );

    @Query(value = "select tp.time_tinh_toan from tonphat_tonghop_v2 tp where tp.ngay_baocao  = :ngayBaoCao and tp.version = :version limit 1", nativeQuery = true)
    String findFirstByVersion(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,@Param("version") long  version
    );
}

