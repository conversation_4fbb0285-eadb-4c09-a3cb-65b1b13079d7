package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuLogisticJPADto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDTLogisticChiNhanhV1Dto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticOverViewDto;
import nocsystem.indexmanager.models.TienDoDoanhThuLogistic.TienDoDoanhThuLogisticBuuCuc;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDTLogisticBuuCucV1Dto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;

public interface TienDoDoanhThuLogisticBuuCucRepository extends PagingAndSortingRepository<TienDoDoanhThuLogisticBuuCuc, Long> {
    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, " +
            "dtbc.tlht as tlht, dtbc.tienDo as tienDo, dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, " +
            "dtbc.ttNam as ttNam, dtbc.keHoach as keHoach, dtbc.thucHien as thucHien " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtbc.dichVu = :dichVu) " +
            "AND dtbc.maBuuCuc = :maBuuCuc " +
            "AND dtbc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDTLogisticBuuCucV1Dto> findTienDoDoanhThuBuuCucAndChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("dichVu") String dichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, " +
            "dtbc.tlht as tlht, dtbc.tienDo as tienDo, dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, " +
            "dtbc.ttNam as ttNam, dtbc.thucHien as thucHien, dtbc.keHoach as keHoach " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtbc.dichVu = :dichVu) " +
            "AND dtbc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDTLogisticBuuCucV1Dto> findTienDoDoanhChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("dichVu") String dichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, " +
            "dtbc.tlht as tlht, dtbc.tienDo as tienDo, dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, " +
            "dtbc.ttNam as ttNam, dtbc.thucHien as thucHien, dtbc.keHoach as keHoach " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh = null OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtbc.dichVu = :dichVu) " +
            "AND (dtbc.maBuuCuc IN :listBuuCucVeriable )" +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDTLogisticBuuCucV1Dto> findTienDoDoanhChiNhanhV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("dichVu") String dichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, " +
            "dtbc.tlht as tlht, dtbc.tienDo as tienDo, dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, " +
            "dtbc.ttNam as ttNam, dtbc.thucHien as thucHien, dtbc.keHoach as keHoach " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtbc " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtbc.dichVu = :dichVu) " +
            "AND (dtbc.maChiNhanh IN :listBuuCucVeriable )" +
            "order by dtbc.maChiNhanh asc ")
    List<TienDoDoanhThuLogisticOverViewDto> findTienDoDoanhThuChiNhanhDetailV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable,
            @Param("dichVu") String dichVu
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, " +
            "dtbc.tlht as tlht, dtbc.tienDo as tienDo, dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, " +
            "dtbc.ttNam as ttNam, dtbc.thucHien as thucHien, dtbc.keHoach as keHoach " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtbc " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtbc.dichVu = :dichVu) " +
            "order by dtbc.maChiNhanh asc ")
    List<TienDoDoanhThuLogisticOverViewDto> findTienDoDoanhThuChiNhanhDetail(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            @Param("dichVu") String dichVu
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, dtbc.keHoach as keHoach, " +
            "dtbc.thucHien as thucHien, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.thangTruoc as thangTruoc," +
            "dtbc.namTruoc as namTruoc, dtbc.cungKyNgay as cungKyNgay, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.ttNam as ttNam, dtbc.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND dtbc.maBuuCuc = :maBuuCuc")
    List<TienDoDoanhThuLogisticOverViewDto> findTienDoDoanhThuBuuCucOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, dtbc.keHoach as keHoach, " +
            "dtbc.thucHien as thucHien, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.thangTruoc as thangTruoc," +
            "dtbc.namTruoc as namTruoc, dtbc.cungKyNgay as cungKyNgay, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.ttNam as ttNam, dtbc.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE (:maChiNhanh is null OR :maChiNhanh = '' OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR dtbc.maBuuCuc = :maBuuCuc ) " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    List<TienDoDoanhThuLogisticOverViewDto> findTienDoDoanhThuBuuCucOverViewV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("toTime") LocalDate toTime,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    /**
     * @param maChiNhanh
     * @param maBuuCuc
     * @param loaiDichVuCV
     * @param toTime
     * @return
     */
    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, dtbc.keHoach as keHoach, " +
            "dtbc.thucHien as thucHien, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.thangTruoc as thangTruoc," +
            "dtbc.namTruoc as namTruoc, dtbc.cungKyNgay as cungKyNgay, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.ttNam as ttNam, dtbc.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:maBuuCuc = '' OR :maBuuCuc is null OR dtbc.maBuuCuc = :maBuuCuc) " +
            "AND (:loaiDichVuCV = '' OR :loaiDichVuCV is null OR dtbc.dichVu = :loaiDichVuCV) ")
    Slice<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuBuuCuc(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("loaiDichVuCV") String loaiDichVuCV,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, dtbc.keHoach as keHoach, " +
            "dtbc.thucHien as thucHien, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.thangTruoc as thangTruoc," +
            "dtbc.namTruoc as namTruoc, dtbc.cungKyNgay as cungKyNgay, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.ttNam as ttNam, dtbc.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:maBuuCuc = '' OR :maBuuCuc is null OR dtbc.maBuuCuc = :maBuuCuc) " +
            "AND (:loaiDichVuCV = '' OR :loaiDichVuCV is null OR dtbc.dichVu = :loaiDichVuCV) " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    Slice<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuBuuCucV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("loaiDichVuCV") String loaiDichVuCV,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, dtbc.keHoach as keHoach, " +
            "dtbc.thucHien as thucHien, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.thangTruoc as thangTruoc," +
            "dtbc.namTruoc as namTruoc, dtbc.cungKyNgay as cungKyNgay, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.ttNam as ttNam, dtbc.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:maBuuCuc = '' OR :maBuuCuc is null OR dtbc.maBuuCuc = :maBuuCuc) " +
            "AND (:loaiDichVuCV = '' OR :loaiDichVuCV is null OR dtbc.dichVu = :loaiDichVuCV) ")
    List<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuBuuCucOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("loaiDichVuCV") String loaiDichVuCV,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, dtbc.keHoach as keHoach, " +
            "dtbc.thucHien as thucHien, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.thangTruoc as thangTruoc," +
            "dtbc.namTruoc as namTruoc, dtbc.cungKyNgay as cungKyNgay, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.ttNam as ttNam, dtbc.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtbc.maChiNhanh = :maChiNhanh) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:maBuuCuc = '' OR :maBuuCuc is null OR dtbc.maBuuCuc = :maBuuCuc) " +
            "AND (:loaiDichVuCV = '' OR :loaiDichVuCV is null OR dtbc.dichVu = :loaiDichVuCV) " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    List<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuBuuCucOverViewV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("loaiDichVuCV") String loaiDichVuCV,
            @Param("toTime") LocalDate toTime,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT dtbc.tiLeTangTruongNgay as tiLeTangTruongNgay, dtbc.ngayBaoCao as ngayBaoCao, dtbc.dichVu as loaiDichVu " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE dtbc.ngayBaoCao >= :ngayBatDau " +
            "AND dtbc.ngayBaoCao <= :ngayKetThuc " +
            "AND dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.maBuuCuc = :maBuuCuc ")
    List<BieuDoTtTienDoDoanhThuLogisticJPADto> dataBieuDoTienDoDoanhThuLogisticBC(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc
    );

    @Query("SELECT dtbc.maChiNhanh as maChiNhanh, dtbc.dichVu as dichVu, dtbc.keHoach as keHoach, " +
            "dtbc.thucHien as thucHien, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.thangTruoc as thangTruoc," +
            "dtbc.namTruoc as namTruoc, dtbc.cungKyNgay as cungKyNgay, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.ttNam as ttNam, dtbc.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticBuuCuc dtbc " +
            "WHERE dtbc.ngayBaoCao = :toTime " +
            "AND (:loaiDichVuCV = '' OR :loaiDichVuCV is null OR dtbc.dichVu = :loaiDichVuCV) " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    Page<TienDoDTLogisticChiNhanhV1Dto> findTienDoDTLogisticBCDetail(
            @Param("loaiDichVuCV") String loaiDichVuCV,
            @Param("toTime") LocalDate toTime,
            Pageable paging,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );
}
