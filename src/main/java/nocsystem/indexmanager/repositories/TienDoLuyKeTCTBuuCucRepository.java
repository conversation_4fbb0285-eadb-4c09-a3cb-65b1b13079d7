package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTBCResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.ThongKeLuyKe.TienDoLuyKeTCTCN;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoLuyKeTCTBuuCucRepository extends JpaRepository<TienDoLuyKeTCTCN, Long>, PagingAndSortingRepository<TienDoLuyKeTCTCN, Long> {
    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, tdLuyKeTCT.buuCuc as buuCuc," +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.lkthang as luyKeThang, tdLuyKeTCT.khthang as keHoach, tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTBuuCuc tdLuyKeTCT " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeTCT.buuCuc = :buuCuc) " +
            "AND tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.chiNhanh = :chiNhanh " +
            "AND tdLuyKeTCT.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<TienDoluyKeTCTBCResDto> findTienDoLuyKeTCTBC(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, tdLuyKeTCT.buuCuc as buuCuc," +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.lkthang as luyKeThang, tdLuyKeTCT.khthang as keHoach, tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTBuuCuc tdLuyKeTCT " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeTCT.buuCuc = :buuCuc) " +
            "AND tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.chiNhanh = :chiNhanh " +
            "AND (tdLuyKeTCT.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<TienDoluyKeTCTBCResDto> findTienDoLuyKeTCTBCV2(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, tdLuyKeTCT.buuCuc as buuCuc," +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.lkthang as luyKeThang, tdLuyKeTCT.khthang as keHoach, tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTBuuCuc tdLuyKeTCT " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeTCT.buuCuc = :buuCuc) " +
            "AND tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.chiNhanh = :chiNhanh " +
            "AND tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeTCTBCDetail(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable);

    @Query("SELECT tdLuyKeBC.ngayBaoCao as ngayBaoCao, tdLuyKeBC.chiNhanh as chiNhanh, tdLuyKeBC.buuCuc as buuCuc," +
            "tdLuyKeBC.mien as mien, tdLuyKeBC.tlht as tlht, tdLuyKeBC.tienDo as tienDo, " +
            "tdLuyKeBC.ttThang as ttThang, tdLuyKeBC.ttTbnThang as ttTbnThang," +
            "tdLuyKeBC.ttNam as ttNam, tdLuyKeBC.ttTbnNam as ttTbnNam, " +
            "tdLuyKeBC.danhgia as danhgia, tdLuyKeBC.lkthang as luyKeThang, tdLuyKeBC.khthang as keHoach, tdLuyKeBC.tienDoNgay as tienDoNgay, tdLuyKeBC.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeBC.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeBC.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTBuuCuc tdLuyKeBC " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeBC.buuCuc = :buuCuc) " +
            "AND tdLuyKeBC.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeBC.chiNhanh = 'HNI' " +
            "AND tdLuyKeBC.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY tdLuyKeBC.buuCuc asc ")
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeTCTBCChiNhanhHN(
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable);

    @Query("SELECT tdLuyKeCNHCM.ngayBaoCao as ngayBaoCao, tdLuyKeCNHCM.chiNhanh as chiNhanh, tdLuyKeCNHCM.buuCuc as buuCuc," +
            "tdLuyKeCNHCM.mien as mien, tdLuyKeCNHCM.tlht as tlht, tdLuyKeCNHCM.tienDo as tienDo, " +
            "tdLuyKeCNHCM.ttThang as ttThang, tdLuyKeCNHCM.ttTbnThang as ttTbnThang," +
            "tdLuyKeCNHCM.ttNam as ttNam, tdLuyKeCNHCM.ttTbnNam as ttTbnNam, " +
            "tdLuyKeCNHCM.danhgia as danhgia, tdLuyKeCNHCM.lkthang as luyKeThang, tdLuyKeCNHCM.khthang as keHoach, " +
            "tdLuyKeCNHCM.tienDoNgay as tienDoNgay, tdLuyKeCNHCM.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeCNHCM.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeCNHCM.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTBuuCuc tdLuyKeCNHCM " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeCNHCM.buuCuc = :buuCuc) " +
            "AND tdLuyKeCNHCM.ngayBaoCao = :ngayBaoCao " +
            "AND (:chiNhanh is null or :chiNhanh = '' or tdLuyKeCNHCM.chiNhanh = :chiNhanh ) " +
            "AND tdLuyKeCNHCM.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY tdLuyKeCNHCM.chiNhanh asc ")
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeTCTBC(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable);

    @Query("SELECT tdLuyKeCNHCM.ngayBaoCao as ngayBaoCao, tdLuyKeCNHCM.chiNhanh as chiNhanh, tdLuyKeCNHCM.buuCuc as buuCuc," +
            "tdLuyKeCNHCM.mien as mien, tdLuyKeCNHCM.tlht as tlht, tdLuyKeCNHCM.tienDo as tienDo, " +
            "tdLuyKeCNHCM.ttThang as ttThang, tdLuyKeCNHCM.ttTbnThang as ttTbnThang," +
            "tdLuyKeCNHCM.ttNam as ttNam, tdLuyKeCNHCM.ttTbnNam as ttTbnNam, " +
            "tdLuyKeCNHCM.danhgia as danhgia, tdLuyKeCNHCM.lkthang as luyKeThang, tdLuyKeCNHCM.khthang as keHoach, " +
            "tdLuyKeCNHCM.tienDoNgay as tienDoNgay, tdLuyKeCNHCM.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeCNHCM.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeCNHCM.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTBuuCuc tdLuyKeCNHCM " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeCNHCM.buuCuc = :buuCuc) " +
            "AND tdLuyKeCNHCM.ngayBaoCao = :ngayBaoCao " +
            "AND (:chiNhanh is null or :chiNhanh = '' or tdLuyKeCNHCM.chiNhanh = :chiNhanh ) " +
            "AND tdLuyKeCNHCM.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY tdLuyKeCNHCM.chiNhanh asc ")
    Page<TienDoluyKeTCTCNResDto> findTienDoLuyKeTheoKeHoach(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable);
}
