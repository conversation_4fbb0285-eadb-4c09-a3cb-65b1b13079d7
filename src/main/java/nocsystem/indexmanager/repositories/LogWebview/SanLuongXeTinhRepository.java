package nocsystem.indexmanager.repositories.LogWebview;

import nocsystem.indexmanager.models.LogWebview.SanLuongXeTinh;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh1DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh2DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinhAllXeDTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeKhaiThacTinhDetailXeDTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh1DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh2DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinhAllXeDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SanLuongXeTinhRepository extends JpaRepository<SanLuongXeTinh, Long> {

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh1DTO( " +
            " d.trungTamKhaiThacTinh, " +
            "count(case when d.typeCho = 1 then d.bienSoXe end) as duoi30p, " +
            "count(case when d.typeCho = 2 then d.bienSoXe end) as duoi1h, " +
            "count(case when d.typeCho = 3 then d.bienSoXe end) as duoi2h, " +
            "count(case when d.typeCho = 4 then d.bienSoXe end) as tren2h, " +
            "count(d.bienSoXe) as tong) " +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1" +
            " group by d.trungTamKhaiThacTinh")
    Page<XeChoKhaiThacTinh1DTO> soLuongXeChoKhaiThacTinh(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh1DTO( " +
            "count(case when d.typeCho = 1 then d.bienSoXe end), " +
            "count(case when d.typeCho = 2 then d.bienSoXe end), " +
            "count(case when d.typeCho = 3 then d.bienSoXe end), " +
            "count(case when d.typeCho = 4 then d.bienSoXe end), " +
            "count(d.bienSoXe)) " +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1")
    XeChoKhaiThacTinh1DTO soLuongXeChoKhaiThacTinhSum(
            List<String> dvvc,
            List<String> hanhTrinh);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh2DTO( " +
            " d.trungTamKhaiThacTinh, " +
            "sum(case when d.typeCho = 1 then d.trongLuong else 0 end) as duoi30p, " +
            "sum(case when d.typeCho = 2 then d.trongLuong else 0 end) as duoi1h, " +
            "sum(case when d.typeCho = 3 then d.trongLuong else 0 end) as duoi2h, " +
            "sum(case when d.typeCho = 4 then d.trongLuong else 0 end) as tren2h, " +
            "sum(d.trongLuong) as tong)" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1" +
            " group by d.trungTamKhaiThacTinh")
    Page<XeChoKhaiThacTinh2DTO> trongLuongXeChoKhaiThacTinh(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh2DTO( " +
            "sum(case when d.typeCho = 1 then d.trongLuong else 0 end), " +
            "sum(case when d.typeCho = 2 then d.trongLuong else 0 end), " +
            "sum(case when d.typeCho = 3 then d.trongLuong else 0 end), " +
            "sum(case when d.typeCho = 4 then d.trongLuong else 0 end), " +
            "sum(d.trongLuong))" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1")
    XeChoKhaiThacTinh2DTO trongLuongXeChoKhaiThacTinhSum(
            List<String> dvvc,
            List<String> hanhTrinh);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh1DTO( " +
            " d.trungTamKhaiThacTinh, " +
            "sum(case when d.typeCho = 1 then d.sanLuong else 0 end) as duoi30p, " +
            "sum(case when d.typeCho = 2 then d.sanLuong else 0 end) as duoi1h, " +
            "sum(case when d.typeCho = 3 then d.sanLuong else 0 end) as duoi2h, " +
            "sum(case when d.typeCho = 4 then d.sanLuong else 0 end) as tren2h, " +
            "sum(d.sanLuong) as tong)" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1" +
            " group by d.trungTamKhaiThacTinh")
    Page<XeChoKhaiThacTinh1DTO> soLuongBuuGuiChoKhaiThacTinh(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh1DTO( " +
            "sum(case when d.typeCho = 1 then d.sanLuong else 0 end), " +
            "sum(case when d.typeCho = 2 then d.sanLuong else 0 end), " +
            "sum(case when d.typeCho = 3 then d.sanLuong else 0 end), " +
            "sum(case when d.typeCho = 4 then d.sanLuong else 0 end), " +
            "sum(d.sanLuong))" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1")
    XeChoKhaiThacTinh1DTO soLuongBuuGuiChoKhaiThacTinhSum(
            List<String> dvvc,
            List<String> hanhTrinh);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinhAllXeDTO( " +
            " d.bienSoXe, d.trongLuong, d.thoiGianCho) " +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND (:ttkt is null or :ttkt = '' or d.trungTamKhaiThacTinh = :ttkt) " +
            " AND (:thoiGian is null or d.typeCho = :thoiGian)" +
            " AND d.status = 1 " +
            " and d.type = 1")
    Page<XeChoKhaiThacTinhAllXeDTO> soLuongXeChoKhaiThacTinhAllXe(
            List<String> dvvc,
            List<String> hanhTrinh,
            String ttkt,
            Integer thoiGian,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeKhaiThacTinhDetailXeDTO( " +
            " d.maChuyenXe, d.bienSoXe, d.tuyen, d.dvvc, " +
            " d.timeCheckin, d.userLai, d.soDienThoai, d.sanLuong, " +
            " d.trongLuong, d.trongTai, d.thoiGianCho) " +
            " from SanLuongXeTinh d " +
            " where d.bienSoXe = :bienSoXe " +
            " and d.status = 1 " +
            " and d.type = 1")
    XeKhaiThacTinhDetailXeDTO soLuongXeChoKhaiThacTinhDetailXe(String bienSoXe);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh1DTO( " +
            " d.trungTamKhaiThacTinh, " +
            " count(d.bienSoXe) as soLuongXe) " +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2" +
            " group by d.trungTamKhaiThacTinh")
    Page<XeDangKhaiThacTinh1DTO> soLuongXeDangKhaiThacTinh(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh1DTO( " +
            " count(d.bienSoXe)) " +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2")
    XeDangKhaiThacTinh1DTO soLuongXeDangKhaiThacTinhSum(
            List<String> dvvc,
            List<String> hanhTrinh);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh2DTO( " +
            " d.trungTamKhaiThacTinh, " +
            " sum(d.trongLuong) as trongLuongXe)" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2" +
            " group by d.trungTamKhaiThacTinh")
    Page<XeDangKhaiThacTinh2DTO> trongLuongXeDangKhaiThacTinh(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh2DTO( " +
            " sum(d.trongLuong))" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2")
    XeDangKhaiThacTinh2DTO trongLuongXeDangKhaiThacTinhSum(
            List<String> dvvc,
            List<String> hanhTrinh);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh1DTO( " +
            " d.trungTamKhaiThacTinh, " +
            " sum(d.sanLuong) as soLuongBuuGui)" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2" +
            " group by d.trungTamKhaiThacTinh")
    Page<XeDangKhaiThacTinh1DTO> soLuongBuuGuiDangKhaiThacTinh(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh1DTO( " +
            " sum(d.sanLuong))" +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2")
    XeDangKhaiThacTinh1DTO soLuongBuuGuiDangKhaiThacTinhSum(
            List<String> dvvc,
            List<String> hanhTrinh);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinhAllXeDTO( " +
            " d.bienSoXe, d.trongLuong, d.sanLuong) " +
            " from SanLuongXeTinh d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND (:ttkt is null or :ttkt = '' or d.trungTamKhaiThacTinh = :ttkt) " +
            " AND d.status = 1 " +
            " and d.type = 2")
    Page<XeDangKhaiThacTinhAllXeDTO> soLuongXeDangKhaiThacTinhAllXe(
            List<String> dvvc,
            List<String> hanhTrinh,
            String ttkt,
            Pageable page);

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeKhaiThacTinhDetailXeDTO( " +
            " d.maChuyenXe, d.bienSoXe, d.tuyen, d.dvvc, " +
            " d.timeCheckin, d.userLai, d.soDienThoai, d.sanLuong, d.trongLuong) " +
            " from SanLuongXeTinh d " +
            " where d.bienSoXe = :bienSoXe " +
            " and d.status = 1 " +
            " and d.type = 2")
    XeKhaiThacTinhDetailXeDTO soLuongXeDangKhaiThacTinhDetailXe(String bienSoXe);

    @Query(value="select d.time_tinh_toan from sl_xe_cho_va_dang_khaithac_tinh d limit 1", nativeQuery = true)
    String findCalculateTime();

}
