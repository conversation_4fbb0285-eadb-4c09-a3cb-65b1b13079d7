package nocsystem.indexmanager.repositories.LogWebview;

import nocsystem.indexmanager.models.LogWebview.ChiTietXe;
import nocsystem.indexmanager.models.Response.LogWebview.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChiTietXeRepository extends JpaRepository<ChiTietXe, Long> {

    //Query riêng cho api MHL tại TTKT5
    //chi tiết xe chờ khai thác (type = 1)
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.ChiTietXeDTO( " +
            " d.bienSoXe , d.trongLuong, d.san<PERSON>uo<PERSON>, d.thoi<PERSON><PERSON><PERSON>, d.timeDu<PERSON>ien)" +
            " from ChiTietXe d " +
            " where (:dvvc is null or :dvvc = '' or d.dvvc = :dvvc) " +
            " and (:hanhTrinh is null or :hanhTrinh = '' or d.hanhTrinh = :hanhTrinh) " +
            " AND d.status = 1 " +
            " and d.type = 1" +
            " and d.trungTamKhaiThac = 'TTKT5'" +
            " order by d.thoiGianCho desc"
    )
    Page<ChiTietXeDTO> chiTietXeChoKhaiThacTTKT5(
            String dvvc,
            String hanhTrinh,
            Pageable page
    );

    //chi tiết xe đang khai thác (type = 2)
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.ChiTietXeDTO( " +
            " d.bienSoXe , d.trongLuong, d.sanLuong, d.thoiGianCho, d.timeDuKien)" +
            " from ChiTietXe d " +
            " where (:dvvc is null or :dvvc = '' or d.dvvc = :dvvc) " +
            " and (:hanhTrinh is null or :hanhTrinh = '' or d.hanhTrinh = :hanhTrinh) " +
            " AND d.status = 1 " +
            " and d.trungTamKhaiThac = 'TTKT5'" +
            " and d.type = 2"
    )
    Page<ChiTietXeDTO> chiTietXeDangKhaiThacTTKT5(
            String dvvc,
            String hanhTrinh,
            Pageable page
    );

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.ChiTietXeDTO( " +
            " d.regNo , d.arrivalTime)" +
            " from TripLocation d " +
            " where d.arrivalTime > CURRENT_TIMESTAMP " +
            " order by d.arrivalTime"
    )
    Page<ChiTietXeDTO> chiTietXeDuKienDenTTKT5(
            Pageable page
    );

    //ALL
    //chi tiết xe chờ khai thác (type = 1)
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.ChiTietXeDTO( " +
            " d.maChuyenXe, d.bienSoXe , d.trongLuong, d.sanLuong, d.thoiGianCho, d.timeDuKien)" +
            " from ChiTietXe d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1" +
            " and (:ttkt is null or :ttkt = '' or d.trungTamKhaiThac = :ttkt) " +
            " and d.typeCho in :chiSo" +
            " order by d.thoiGianCho desc"
    )
    Page<ChiTietXeDTO> chiTietXeChoKhaiThac(
            List<String> dvvc,
            List<String> hanhTrinh,
            String ttkt,
            List<Integer> chiSo,
            Pageable page
    );

    //chi tiết xe đang khai thác (type = 2)
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.ChiTietXeDTO( " +
            " d.maChuyenXe, d.bienSoXe , d.trongLuong, d.sanLuong, d.thoiGianCho, d.timeDuKien)" +
            " from ChiTietXe d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and (:ttkt is null or :ttkt = '' or d.trungTamKhaiThac = :ttkt) " +
            " and d.type = 2"
    )
    Page<ChiTietXeDTO> chiTietXeDangKhaiThac(
            List<String> dvvc,
            List<String> hanhTrinh,
            String ttkt,
            Pageable page
    );

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.ChiTietXeDTO( " +
            " d.regNo , d.arrivalTime)" +
            " from TripLocation d " +
            " where d.arrivalTime > CURRENT_TIMESTAMP " +
            " order by d.arrivalTime"
    )
    Page<ChiTietXeDTO> chiTietXeDuKienDen(
            Pageable page
    );

    //chờ khai thác (type = 1)
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.XeChoKhaiThacDTO( " +
            "d.trungTamKhaiThac, " +
            "COUNT(CASE WHEN d.typeCho = 1 THEN d.trungTamKhaiThac END), " +
            "COUNT(CASE WHEN d.typeCho = 2 THEN d.trungTamKhaiThac END), " +
            "COUNT(CASE WHEN d.typeCho = 3 THEN d.trungTamKhaiThac END), " +
            "COUNT(CASE WHEN d.typeCho = 4 THEN d.trungTamKhaiThac END), " +
            "COUNT(d.trungTamKhaiThac), " +
            "SUM(CASE WHEN d.typeCho = 1 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 2 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 3 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 4 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(COALESCE(d.trongLuong, 0)), " +
            "SUM(CASE WHEN d.typeCho = 1 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 2 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 3 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 4 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(COALESCE(d.sanLuong, 0))) " +
            " from ChiTietXe d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1 " +
            " group by d.trungTamKhaiThac order by d.trungTamKhaiThac")
    Page<XeChoKhaiThacDTO> slXeChoKhaiThac(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page
    );

    //chờ khai thác (type = 1)
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.XeChoKhaiThacDTO( " +
            "COUNT(CASE WHEN d.typeCho = 1 THEN d.trungTamKhaiThac END), " +
            "COUNT(CASE WHEN d.typeCho = 2 THEN d.trungTamKhaiThac END), " +
            "COUNT(CASE WHEN d.typeCho = 3 THEN d.trungTamKhaiThac END), " +
            "COUNT(CASE WHEN d.typeCho = 4 THEN d.trungTamKhaiThac END)," +
            "COUNT(d.trungTamKhaiThac), " +
            "SUM(CASE WHEN d.typeCho = 1 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 2 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 3 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 4 THEN COALESCE(d.trongLuong, 0) ELSE 0 END), " +
            "SUM(COALESCE(d.trongLuong, 0)), " +
            "SUM(CASE WHEN d.typeCho = 1 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 2 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 3 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(CASE WHEN d.typeCho = 4 THEN COALESCE(d.sanLuong, 0) ELSE 0 END), " +
            "SUM(COALESCE(d.sanLuong, 0))) " +
            " from ChiTietXe d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 1 ")
    XeChoKhaiThacDTO slXeChoKhaiThacSum(
            List<String> dvvc,
            List<String> hanhTrinh
    );

    //đang khai thác (type = 2)
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.XeDangKhaiThacDTO( " +
            " d.trungTamKhaiThac , count(d.bienSoXe), sum(d.trongLuong), sum(d.sanLuong))" +
            " from ChiTietXe d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2" +
            " group by d.trungTamKhaiThac" +
            " order by d.trungTamKhaiThac"
    )
    Page<XeDangKhaiThacDTO> slXeDangKhaiThac(
            List<String> dvvc,
            List<String> hanhTrinh,
            Pageable page
    );

    //tổng xe đang khai thác
    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.XeDangKhaiThacDTO( " +
            " count(d.bienSoXe), sum(d.trongLuong), sum(d.sanLuong))" +
            " from ChiTietXe d " +
            " where (COALESCE(:dvvc, NULL) is NULL OR d.dvvc in (:dvvc)) " +
            " AND (COALESCE(:hanhTrinh, NULL) is NULL OR d.hanhTrinh in (:hanhTrinh)) " +
            " AND d.status = 1 " +
            " and d.type = 2"
    )
    XeDangKhaiThacDTO slXeDangKhaiThacSum(
            List<String> dvvc,
            List<String> hanhTrinh
    );

    @Query(value = "select new nocsystem.indexmanager.models.Response.LogWebview.ChiTietChuyenXeDTO( " +
            " d.maChuyenXe , d.dvvc, d.tuyen, d.bienSoXe, d.userLai, d.soDienThoai, d.sanLuong, d.trongLuong, d.trongTai, d.thoiGianCho, d.timeDuKien, d.timeCheckin)" +
            " from ChiTietXe d " +
            " where d.maChuyenXe = :maChuyenXe " +
            " AND d.status = 1 "
    )
    ChiTietChuyenXeDTO chiTietChuyenXe(
            String maChuyenXe
    );

    @Query(value="select d.time_tinh_toan from sl_xe_cho_va_dang_khaithac d limit 1", nativeQuery = true)
    String findTopByOrderByDateDesc();
}

