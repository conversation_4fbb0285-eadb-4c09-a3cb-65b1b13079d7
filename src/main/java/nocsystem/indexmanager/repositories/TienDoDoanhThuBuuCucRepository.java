package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.*;
import nocsystem.indexmanager.models.TienDoDoanhThu.TienDoDoanhThuBuuCuc;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuBuuCucRepository extends PagingAndSortingRepository<TienDoDoanhThuBuuCuc, Long> {
    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as ma<PERSON><PERSON><PERSON><PERSON><PERSON>, dtbc.nhomDoanhThu as nhomDoanhThu, " +
            "dtbc.thuc<PERSON>ien as thuc<PERSON>ien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, " +
            "dtbc.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuBuuCuc dtbc " +
            "WHERE dtbc.maBuuCuc = :maBuuCuc " +
            "AND dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu) " +
            "AND dtbc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDoanhThuBCV1ResDto> findTienDoDoanhThuBuuCucAndChiNhanh(
            @Param("maBuuCuc") String maBuuCuc,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh);

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDoanhThu, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, " +
            "dtbc.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuBuuCuc dtbc " +
            "WHERE dtbc.maBuuCuc = :maBuuCuc " +
            "AND dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu) " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDoanhThuBCV1ResDto> findTienDoDoanhThuBuuCucAndChiNhanhV2(
            @Param("maBuuCuc") String maBuuCuc,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable);

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDoanhThu, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuBuuCuc dtbc WHERE dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu) " +
            "AND dtbc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDoanhThuBCV1ResDto> findTienDoDoanhThuChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDoanhThu, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuBuuCuc dtbc WHERE dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu) " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDoanhThuBCV1ResDto> findTienDoDoanhThuChiNhanhV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDoanhThu, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuBuuCuc dtbc WHERE dtbc.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu) " +
            "AND dtbc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDoanhThuBCV1ResDto> findTienDoDoanhThuFollowTime(
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDoanhThu, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuBuuCuc dtbc WHERE dtbc.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu) " +
            "AND dtbc.maChiNhanh IN :listBuuCucVeriable " +
            "order by dtbc.maChiNhanh asc ")
    Page<TienDoDoanhThuBCV1ResDto> findTienDoDoanhThuFollowTimeV2(
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDt, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay, dtbc.thucHienNgay as thucHienNgay, " +
            "dtbc.thucHienNgayTruocDo as thucHienNgayTruocDo, dtbc.ngayQuyDoiThang as ngayQuyDoiThang, " +
            "dtbc.tiLeTangTruongNgay as tiLeTangTruongNgay, dtbc.hsNgay as hsNgay " +
            "FROM TienDoDoanhThuBuuCuc dtbc " +
            "WHERE dtbc.maChiNhanh = :maChiNhanh " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND dtbc.maBuuCuc = :maBuuCuc")
    List<CSKDTienDoDoanhThuOverViewDto> findTienDoDoanhThuBCOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDt, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay, dtbc.thucHienNgay as thucHienNgay, " +
            "dtbc.thucHienNgayTruocDo as thucHienNgayTruocDo, dtbc.ngayQuyDoiThang as ngayQuyDoiThang, " +
            "dtbc.tiLeTangTruongNgay as tiLeTangTruongNgay, dtbc.hsNgay as hsNgay " +
            "FROM TienDoDoanhThuBuuCuc dtbc " +
            "WHERE (:maChiNhanh is null OR :maChiNhanh = '' OR dtbc.maChiNhanh = :maChiNhanh ) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR dtbc.maBuuCuc = :maBuuCuc ) ")
    List<CSKDTienDoDoanhThuOverViewDto> findTienDoDoanhThuBCOverViewV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable,
            @Param("maBuuCuc") String maBuuCuc
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDt, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay " +
            "FROM TienDoDoanhThuBuuCuc dtbc " +
            "WHERE (:maChiNhanh is null OR :maChiNhanh = '' OR dtbc.maChiNhanh = :maChiNhanh ) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR dtbc.maBuuCuc = :maBuuCuc ) " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu ) ")
    Slice<CSKDTienDoDoanhThuOverViewDto> findListTienDoDoanhThuBC(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDt, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlht, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay " +
            "FROM TienDoDoanhThuBuuCuc dtbc " +
            "WHERE (:maChiNhanh is null OR :maChiNhanh = '' OR dtbc.maChiNhanh = :maChiNhanh ) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR dtbc.maBuuCuc = :maBuuCuc ) " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtbc.nhomDoanhThu = :nhomDoanhThu ) " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    Slice<CSKDTienDoDoanhThuOverViewDto> findListTienDoDoanhThuBCV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT tddt.tiLeTangTruongNgay as tiLeTangTruongNgay, tddt.ngayBaoCao as ngayBaoCao, tddt.nhomDoanhThu as nhomDt " +
            "FROM TienDoDoanhThuBuuCuc tddt " +
            "WHERE tddt.ngayBaoCao <= :ngayKetThuc " +
            "AND tddt.ngayBaoCao >= :ngayBatDau " +
            "AND (tddt.maChiNhanh is null OR tddt.maChiNhanh = '' OR tddt.maChiNhanh = :maChiNhanh) " +
            "AND (tddt.maBuuCuc is null OR tddt.maBuuCuc = '' OR tddt.maBuuCuc = :maBuuCuc) " +
            "order by tddt.ngayBaoCao asc ")
    public List<DataBieuDoTienDoDoanhThuDto> dataBieuDoTienDoDoanhThu(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc);

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.nhomDoanhThu as nhomDt, " +
            "dtbc.thucHien as thucHien, dtbc.keHoach as keHoach, dtbc.tlht as tlHoanThanh, dtbc.tienDo as tienDo, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay, dtbc.thucHienNgay as thucHienNgay, " +
            "dtbc.thucHienNgayTruocDo as thucHienNgayTruocDo, dtbc.ngayQuyDoiThang as ngayQuyDoiThang, " +
            "dtbc.tiLeTangTruongNgay as tiLeTangTruongNgay, dtbc.hsNgay as hsNgay " +
            "FROM TienDoDoanhThuBuuCuc dtbc " +
            "WHERE (:nhomDoanhThuCV is null OR :nhomDoanhThuCV = '' OR dtbc.nhomDoanhThu = :nhomDoanhThuCV ) " +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    Page<TienDoDoanhThuCNV1ResDto> findTienDoDoanhThuBC(
            @Param("nhomDoanhThuCV") String nhomDoanhThuCV,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );






}
