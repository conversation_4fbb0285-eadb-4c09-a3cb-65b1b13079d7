package nocsystem.indexmanager.repositories.BaoCaoKhachHang<PERSON>on;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.BaoCaoKPIThuBill;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoKPIThuBillRepository extends PagingAndSortingRepository<BaoCaoKPIThuBill, Long> {

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO(" +
            "tp.thoi<PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,tp.v<PERSON>, tp.<PERSON><PERSON><PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON>, tp.huy<PERSON><PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON><PERSON>, tp.ma<PERSON><PERSON><PERSON><PERSON>,tp.ma<PERSON>a<PERSON><PERSON>, " +
            "tp.maD<PERSON>Viettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.thoiGianTaoDon,tp.timeThuLan1, tp.timeThuLan2,tp.timeThuLan3, " +
            "tp.timeNhapMay, tp.maTrangThai, tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac," +
            "tp.tienCOD ,tp.tienhang ,tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.lyDoTT1, tp.lyDoTT2, tp.lyDoTT3, tp.buuTaNTC, tp.buuTaTD1, tp.chenhLech, tp.danhGia," +
            "tp.timeKetNoiDiTaiGoc, tp.loaiDon,tp.buu_ta_tonthu,tp.time_ht)" +
            " from BaoCaoKPIThuBill tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.tinhNhan = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucGoc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1 " +
            " and date(tp.timeNhapMay) = :ngayBaoCao")
    List<BaoCaoKPIThuBillResDTO> findBaoCaoKPIThuBillCN(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO(" +
            "tp.thoiGian, tp.maPhieuGui,tp.vung, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.thoiGianTaoDon,tp.timeThuLan1, tp.timeThuLan2,tp.timeThuLan3, " +
            "tp.timeNhapMay, tp.maTrangThai, tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac," +
            "tp.tienCOD ,tp.tienhang ,tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.lyDoTT1, tp.lyDoTT2, tp.lyDoTT3, tp.buuTaNTC, tp.buuTaTD1, tp.chenhLech, tp.danhGia," +
            "tp.timeKetNoiDiTaiGoc, tp.loaiDon,tp.buu_ta_tonthu,tp.time_ht)" +
            " from BaoCaoKPIThuBill tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and ( tp.tinhNhan in :chiNhanh)" +
            " and (tp.maBuuCucGoc in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and date(tp.timeNhapMay) = :ngayBaoCao")
    List<BaoCaoKPIThuBillResDTO> findBaoCaoKPIThuBillCN1(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO(" +
            "tp.thoiGian, tp.maPhieuGui,tp.vung, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.thoiGianTaoDon,tp.timeThuLan1, tp.timeThuLan2,tp.timeThuLan3, " +
            "tp.timeNhapMay, tp.maTrangThai, tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac," +
            "tp.tienCOD ,tp.tienhang ,tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.lyDoTT1, tp.lyDoTT2, tp.lyDoTT3, tp.buuTaNTC, tp.buuTaTD1, tp.chenhLech, tp.danhGia," +
            "tp.timeKetNoiDiTaiGoc, tp.loaiDon,tp.buu_ta_tonthu,tp.time_ht)" +
            " from BaoCaoKPIThuBill tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.tinhNhan = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucGoc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1 " +
            " and date(tp.timeNhapMay) between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoKPIThuBillResDTO> findBaoCaoKPIThuBillCNLK(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO(" +
            "tp.thoiGian, tp.maPhieuGui,tp.vung, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.thoiGianTaoDon,tp.timeThuLan1, tp.timeThuLan2,tp.timeThuLan3, " +
            "tp.timeNhapMay, tp.maTrangThai, tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac," +
            "tp.tienCOD ,tp.tienhang ,tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.lyDoTT1, tp.lyDoTT2, tp.lyDoTT3, tp.buuTaNTC, tp.buuTaTD1, tp.chenhLech, tp.danhGia," +
            "tp.timeKetNoiDiTaiGoc, tp.loaiDon,tp.buu_ta_tonthu,tp.time_ht)" +
            " from BaoCaoKPIThuBill tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and ( tp.tinhNhan in :chiNhanh)" +
            " and (tp.maBuuCucGoc in :buuCuc)" +
            " and tp.status = 1 " +
            " and date(tp.timeNhapMay) between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoKPIThuBillResDTO> findBaoCaoKPIThuBillCNLK1(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO(" +
            "tp.thoiGian, tp.maPhieuGui,tp.vung, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.thoiGianTaoDon,tp.timeThuLan1, tp.timeThuLan2,tp.timeThuLan3, " +
            "tp.timeNhapMay, tp.maTrangThai, tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac," +
            "tp.tienCOD ,tp.tienhang ,tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.lyDoTT1, tp.lyDoTT2, tp.lyDoTT3, tp.buuTaNTC, tp.buuTaTD1, tp.chenhLech, tp.danhGia," +
            "tp.timeKetNoiDiTaiGoc, tp.loaiDon,tp.buu_ta_tonthu,tp.time_ht)" +
            " from BaoCaoKPIThuBill tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.tinhNhan = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucGoc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1 " +
            " and tp.ton = 1 " +
            " and tp.thoiGian = :ngayBaoCao")
    List<BaoCaoKPIThuBillResDTO> findBaoCaoKPIThuTonCN(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO(" +
            "tp.thoiGian, tp.maPhieuGui,tp.vung, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.thoiGianTaoDon,tp.timeThuLan1, tp.timeThuLan2,tp.timeThuLan3, " +
            "tp.timeNhapMay, tp.maTrangThai, tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac," +
            "tp.tienCOD ,tp.tienhang ,tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.lyDoTT1, tp.lyDoTT2, tp.lyDoTT3, tp.buuTaNTC, tp.buuTaTD1, tp.chenhLech, tp.danhGia," +
            "tp.timeKetNoiDiTaiGoc, tp.loaiDon,tp.buu_ta_tonthu,tp.time_ht)" +
            " from BaoCaoKPIThuBill tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and ( tp.tinhNhan in :chiNhanh)" +
            " and (tp.maBuuCucGoc in :buuCuc)" +
            " and tp.status = 1" +
            " and tp.ton = 1" +
            " and tp.thoiGian = :ngayBaoCao")
    List<BaoCaoKPIThuBillResDTO> findBaoCaoKPIThuTonCN1(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

}
