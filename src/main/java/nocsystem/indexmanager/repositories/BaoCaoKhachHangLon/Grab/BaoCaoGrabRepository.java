package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.Grab;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.BaoCaoKPIPhatBill;
import nocsystem.indexmanager.models.BaoCaoKhachHangLon.Grab.BaoCaoGrab;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoGrabRepository extends PagingAndSortingRepository<BaoCaoGrab, Long> {

    /*-------------PHÁT-------------*/
    //Page phát TCT
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.vung, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.vung" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatTCT(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát vùng
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.chiNhanh, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.chiNhanh" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatVung(
            @Param("vungPhat") String vungPhat,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát chi nhánh
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuCuc" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCN(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page phát chi nhánh không admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCN(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBC(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát bưu cục không admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBC(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát đối tác
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.maDoiTac, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maDoiTac" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatDT(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát TCT lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.vung, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.vung" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatTCTLK(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát Vùng lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.chiNhanh, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.chiNhanh" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatVungLK(
            @Param("vungPhat") String vungPhat,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát Chi Nhánh lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc " +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCNLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát Chi Nhánh lũy kế khong admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon)as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCNLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát bưu cục lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBCLK(
            @Param("buuCuc") String buuCuc,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát bưu cục lũy kế khong admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBCLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Phát đối tác lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.maDoiTac, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.maDoiTac" +
            " order by tong desc ")
    Page<BaoCaoGrabPhatResDTO> baoCaoGrabPhatDTLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Lấy list tính tổng
    //List phát TCT
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.vung, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.vung")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatTCTSum(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Phát vùng
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.chiNhanh, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.chiNhanh")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatVungSum(
            @Param("vungPhat") String vungPhat,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Phát chi nhánh
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuCuc")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCNSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Phát chi nhánh khong admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuCuc")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCNSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Phát bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBCSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Phát bưu cục khong admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBCSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Phát đối tác
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.maDoiTac, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maDoiTac")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatDTSum(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Phát TCT lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.vung, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.vung")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatTCTLKSum(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Phát Vùng lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.chiNhanh, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.chiNhanh")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatVungLKSum(
            @Param("vungPhat") String vungPhat,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Phát Chi Nhánh lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCNLKSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Phát Chi Nhánh lũy kế khong admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuCuc, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatCNLKSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Phát bưu cục lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBCLKSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Phát bưu cục lũy kế khong admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.buuTa, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatBCLKSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Phát đối tác lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO(" +
            "tp.maDoiTac, sum(tp.slPhaiPhat), sum(tp.tonNhanBG), sum(tp.tonPhat500), sum(tp.tonPhatKhac500), sum(tp.slPhatTC), sum(tp.slPhatHoan), " +
            "sum(tp.slPhatHuy), sum(tp.slPhatTCDungGio), sum(tp.slPhatTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.maDoiTac")
    List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatDTLKSum(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );


    /*-------------THU-------------*/
    //Page thu TCT
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.vung, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.vung" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuTCT(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu vùng
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.chiNhanh, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.chiNhanh" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuVung(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu chi nhánh
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuCuc" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuCN(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu chi nhánh no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuCuc" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuCN(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuBC(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu bưu cục no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuBC(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu đối tác
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.maDoiTac, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maDoiTac" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuDT(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu TCT lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.vung, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.vung" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuTCTLK(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu Vùng lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.chiNhanh, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.chiNhanh" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuVungLK(
            @Param("vungPhat") String vungPhat,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu Chi Nhánh lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuCNLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu Chi Nhánh lũy kế no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuCNLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu bưu cục lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuBCLK(
            @Param("buuCuc") String buuCuc,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu bưu cục lũy kế no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuBCLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Page Thu đối tác lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.maDoiTac, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon) as tong)" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.maDoiTac" +
            " order by tong desc")
    Page<BaoCaoGrabThuResDTO> baoCaoGrabThuDTLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    //Lấy list để tính tổng thu
    //List thu TCT
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.vung, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.vung")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuTCTSum(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Thu vùng
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.chiNhanh, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.chiNhanh")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuVungSum(
            @Param("vungPhat") String vungPhat,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Thu chi nhánh
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuCuc")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuCNSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Thu bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuBCSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Thu chi nhánh no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuCuc")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuCNSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Thu bưu cục no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTa")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuBCSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Thu đối tác
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.maDoiTac, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maDoiTac")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuDTSum(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDon") String loaiDon
    );

    //List Thu TCT lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.vung, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vung = :vungPhat)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.vung")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuTCTLKSum(
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Thu Vùng lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.chiNhanh, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.chiNhanh")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuVungLKSum(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Thu Chi Nhánh lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuCNLKSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Thu bưu cục lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuBCLKSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuCuc, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuCuc")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuCNLKSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Thu bưu cục lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.buuTa, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (:buuTa is null or :buuTa = '' or tp.buuTa = :buuTa)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.buuTa")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuBCLKSum(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //List Thu đối tác lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO(" +
            "tp.maDoiTac, sum(tp.slPhatSinh), sum(tp.slDropOff), sum(tp.slThuTC), sum(tp.slThuTCDungGio), sum(tp.tonThuConHan), sum(tp.tonThuQuaHan), " +
            "sum(tp.slThuHuy), sum(tp.slThuTon))" +
            " from BaoCaoGrab tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayBatDau and :ngayKetThuc" +
            " group by tp.maDoiTac")
    List<BaoCaoGrabThuResDTO> baoCaoGrabThuDTLKSum(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("loaiDon") String loaiDon
    );

    //lấy time tính toán
    @Query("select tp.updatedAt from BaoCaoGrab tp where tp.ngayBaoCao = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );

    //tìm toàn bộ bưu tá
    @Query("SELECT " +
            "distinct tp.buuTa" +
            " from BaoCaoGrab tp" +
            " where  tp.status = 1" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc = :buuCuc" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao")
    List<String> findDistinctBuuTa(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc
    );
}
