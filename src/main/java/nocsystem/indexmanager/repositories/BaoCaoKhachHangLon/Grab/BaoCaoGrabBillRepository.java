package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.Grab;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.Grab.BaoCaoGrab;
import nocsystem.indexmanager.models.BaoCaoKhachHangLon.Grab.BaoCaoGrabBill;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuBillDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoGrabBillRepository extends PagingAndSortingRepository<BaoCaoGrabBill, Long> {

    //list excel bill phát
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhat = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhanCongPhat) = :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatBill(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill phát lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhat = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhanCongPhat) between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatBillLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill phát no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (tp.chiNhanhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhat in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhanCongPhat) = :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatBill(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill phát lũy kế no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (tp.chiNhanhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhat in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhanCongPhat) between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatBillLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill phát thành công
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhat = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhatTC) = :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatTCBill(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill phát lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhat = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhatTC) between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatTCBillLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill phát no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (tp.chiNhanhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhat in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhatTC) = :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatTCBill(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill phát lũy kế no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhPhat,CAST(tp.timePhatTC AS string),tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, CAST(tp.timePhanCongPhat AS string)," +
            "tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (tp.chiNhanhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhat in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.maTrangThai in :listTrangThai" +
            " and tp.status = 1" +
            " and date(tp.timePhatTC) between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoGrabPhatBillDTO> findBaoCaoGrabPhatTCBillLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("listTrangThai") List<String> listTrangThai
    );

    /* -------------------THU-----------------------*/
    //list excel bill thu
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhThu,tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, " +
            "tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vung is null or :vung = '' or tp.vungThu = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhNhan = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBCGoc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " AND (COALESCE(:listTrangThai, NULL) IS NULL OR tp.maTrangThai IN (:listTrangThai)) " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoGrabThuBillDTO> findBaoCaoGrabThuBill(
            @Param("chiNhanh") String chiNhanh,
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill thu lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhThu,tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, " +
            "tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vung is null or :vung = '' or tp.vungThu = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhNhan = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBCGoc = :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " AND (COALESCE(:listTrangThai, NULL) IS NULL OR tp.maTrangThai IN (:listTrangThai)) " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoGrabThuBillDTO> findBaoCaoGrabThuBillLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill thu no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhThu,tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, " +
            "tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vung is null or :vung = '' or tp.vungThu = :vung)" +
            " and (tp.chiNhanhNhan in :chiNhanh)" +
            " and (tp.maBCGoc in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " AND (COALESCE(:listTrangThai, NULL) IS NULL OR tp.maTrangThai IN (:listTrangThai)) " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoGrabThuBillDTO> findBaoCaoGrabThuBill(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listTrangThai") List<String> listTrangThai
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhThu,tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, " +
            "tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vung is null or :vung = '' or tp.vungThu = :vung)" +
            " AND (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanhNhan IN (:chiNhanh)) " +
            " AND (COALESCE(:buuCuc, NULL) IS NULL OR tp.maBCGoc IN (:buuCuc)) " +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " AND (COALESCE(:listTrangThai, NULL) IS NULL OR tp.maTrangThai IN (:listTrangThai)) " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and date(tp.timeHoanHuy) between :ngayBatDau and :ngayKetThuc")
    List<BaoCaoGrabThuBillDTO> findBaoCaoGrabThuBillHuy(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("listTrangThai") List<String> listTrangThai
    );

    //list excel bill thu lũy kế no admin
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuBillDTO(" +
            "tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maBCGoc,tp.maBCHt, " +
            "tp.timeYeuCauLayHang, tp.timeTaoDon, tp.timeNhapMay, tp.timeQuyDinhThu,tp.maTrangThai, tp.maDoiTac,tp.maKH, " +
            "tp.maDichVu, tp.loaiDon, tp.trongLuong, tp.loaiVung, tp.maVanDonGrab, tp.xaNhan, tp.xaPhat, " +
            "tp.soKm, tp.requestTime, tp.tongCuoc, tp.tongCOD," +
            "tp.lyDoHuy, tp.trangThaiGrab, tp.trangThaiVTP)" +
            " from BaoCaoGrabBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDichVu = :dichVu)" +
            " and (:vung is null or :vung = '' or tp.vungThu = :vung)" +
            " and (tp.chiNhanhNhan in :chiNhanh)" +
            " and (tp.maBCGoc in :buuCuc)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " AND (COALESCE(:listTrangThai, NULL) IS NULL OR tp.maTrangThai IN (:listTrangThai)) " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoGrabThuBillDTO> findBaoCaoGrabThuBillLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("listTrangThai") List<String> listTrangThai
    );
}
