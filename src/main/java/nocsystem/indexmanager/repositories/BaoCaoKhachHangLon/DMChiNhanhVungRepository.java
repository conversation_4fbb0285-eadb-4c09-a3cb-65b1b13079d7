package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanhVung;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the Permission entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DMChiNhanhVungRepository extends JpaRepository<DMChiNhanhVung, Long>, JpaSpecificationExecutor<DMChiNhanhVung> {
    boolean existsByMaVung(String ma_vung);
    @Query(
        "SELECT dm " +
            " FROM DMChiNhanhVung dm WHERE " +
            "(COALESCE(:maCn, NULL) IS NULL OR dm.dmChiNhanh.maCn IN (:maCn))" +
            "AND (COALESCE(:maVung, NULL) IS NULL OR dm.maVung IN (:maVung))"
    )
    Page<DMChiNhanhVung> getAllByMaCnAndMaVung(List<String> maCn, List<String> maVung, Pageable pageable);

}
