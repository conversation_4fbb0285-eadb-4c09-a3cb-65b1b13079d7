package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMVungMien;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


import java.time.LocalDate;
import java.util.List;

/**
 * Spring Data SQL repository for the Permission entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DMVungMienRepository extends JpaRepository<DMVungMien, Long>, JpaSpecificationExecutor<DMVungMien> {
    boolean existsByMaVungMien(String ma_vung);
    @Query(
        "SELECT dm " +
            " FROM DMVungMien dm WHERE " +
            ":maVung IS NULL OR dm.maVungMien = :maVung"
    )
    Page<DMVungMien> getAllByMaVung(String maVung, Pageable pageable);


}
