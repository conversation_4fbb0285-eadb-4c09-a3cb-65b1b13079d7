package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.BaoCaoKPIPhat;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoKPIPhatRepository extends PagingAndSortingRepository<BaoCaoKPIPhat, Long> {

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCT(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, tp.vungCon, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat, tp.vungCon" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTVaVungCon(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTBank(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, tp.vungCon, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat, tp.vungCon" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTBankVaVungCon(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTShopee(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, tp.vungCon, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat, tp.vungCon" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTShopeeVaVungCon(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh " +
            "order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVung(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh " +
            "order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungBank(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh " +
            "order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungShopee(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("vungCon") String  vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1Bank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1Shopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDT(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTBank(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1Bank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1Shopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSum(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSumBank(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSumShopee(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSum(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSumBank(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSumShopee(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSumBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSumShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1Bank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1Shopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK)) " +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSum(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau)) " +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSumBank(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSumShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSumBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSumShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDau để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1Bank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon), " +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1Shopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            " sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLK(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, tp.vungCon, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            " sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat, tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKVaVungCon(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            " sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKBank(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, tp.vungCon, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            " sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat, tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKBankVaVungCon(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            " sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKShopee(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, tp.vungCon, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            " sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat, tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKShopeeVaVungCon(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTTlHoanLK(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungLK(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungLKBank(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungLKShopee(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungTlHoanLK(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNLKBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNLKShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNTlHoanLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1LK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("vungCon")String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1LKBank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1LKShopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1TlHoanLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTLKBank(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTLKShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTTlHoanLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCLKBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCLKShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCTlHoanLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1LK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1LKBank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC) as sl, " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan),sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by sl desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1LKShopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1TlHoanLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSumLK(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSumLKBank(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon)," +
            "sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSumLKShopee(
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSumTlHoanLK(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSumLK(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSumLKBank(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSumLKShopee(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSumTlHoanLK(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSumLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSumLKBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSumLKShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSumTlHoanLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1LK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1LKBank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc, tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1LKShopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1TlHoanLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK)) " +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSumLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK)) " +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSumLKBank(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSumLKShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac, sum(tp.tuHoanLK), sum(tp.mauHoanLK)) " +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSumTlHoanLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSumLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSumLKBank(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSumLKShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSumTlHoanLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1LK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    //select thừa trường mauGiaoThanhCongLanDauLK để tránh bug trùng lặp constructor
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau), " +
            "sum(tp.tuGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK), sum(tp.mauGiaoThanhCongLanDauLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1LKBank(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
            "sum(tp.slPTCDungGioLanDau), sum(tp.slPTC + tp.slHoan + tp.slHuy), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
            "sum(tp.slHoan), sum(tp.slHuy), sum(tp.slDenBu), sum(tp.tonXanh), sum(tp.tonVang)," +
            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), " +
            "sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon),sum(tp.tuHoan), sum(tp.mauHoan), sum(tp.tuHoanLK), sum(tp.mauHoanLK), " +
            "sum(tp.tuGiaoHangDungHen), sum(tp.mauGiaoHangDungHen), " +
            "sum(tp.tuGiaoThanhCongLanDau), sum(tp.mauGiaoThanhCongLanDau))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1LKShopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.tuHoanLK), sum(tp.mauHoanLK))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1TlHoanLK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );


    //Lấy SL phải phát + tồn khác lũy kế theo ngày thay vì cộng lũy kế
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            " tp.vungPhat, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat " +
            " order by pp desc ")
    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKNew(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            " tp.vungPhat, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (tp.vungPhat in :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat" +
            " order by pp desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKInList(
            @Param("vung") List<String> vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            " tp.vungPhat, tp.vungCon, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (tp.vungPhat in :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat, tp.vungCon " +
            " order by pp desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTLKInListVaVungCon(
            @Param("vung") List<String> vung,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            " tp.chiNhanh, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh" +
            " order by pp desc ")
    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungLKNew(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            " tp.chiNhanh, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungLKInList(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            " tp.buuCuc, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc" +
            " order by pp desc ")
    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNLKNew(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNLKInList(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,tp.vungCon,sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc,tp.vungCon" +
            " order by pp desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1LKNew(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status")
    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1LKInList(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac" +
            " order by pp desc ")
    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTLKNew(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (tp.doiTac in :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTLKInList(
            @Param("doiTac") List<String> doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by pp desc ")
    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCLKNew(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            Pageable page
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (tp.tuyenBuuTa in :buuTa)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by pp desc ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCLKInList(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") List<String> buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by pp desc")
    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1LKNew(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.slPhaiPhat) as pp, sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (tp.tuyenBuuTa in :buuTa)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa" +
            " order by pp desc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1LKInList(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String>  buuCuc,
            @Param("buuTa") List<String> buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.vungPhat,sum(tp.slPhaiPhat), sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.loaiBaoCao = :loaiBaoCao)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.vungPhat ")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSumLKNew(
            @Param("vung") String vung,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.chiNhanh,sum(tp.slPhaiPhat), sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSumLKNew(
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,sum(tp.slPhaiPhat), sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSumLKNew(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.buuCuc,sum(tp.slPhaiPhat), sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.buuCuc")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1LKNew(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.doiTac,sum(tp.slPhaiPhat), sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.doiTac")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSumLKNew(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.slPhaiPhat), sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSumLKNew(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "tp.tuyenBuuTa, sum(tp.slPhaiPhat), sum(tp.tt500), sum(tp.ttKhac), sum(tp.tongTon))" +
            " from BaoCaoKPIPhat tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.loaiBaoCao = :loaiBaoCao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = :status" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1LKNew(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiBaoCao") Integer loaiBaoCao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTikTokKpiPhatResponse" +
            "( sum(kp.slPhaiPhat + kp.slPTC), sum(kp.slPTC), sum(kp.slPTCDungGio), sum(kp.slPTCDungGioLanDau), " +
            "sum(kp.tongTon), sum(kp.slPhaiPhat), kp.ngayBaoCao) " +
            "FROM BaoCaoKPIPhat kp " +
            "WHERE (kp.ngayBaoCao >= :previousDay AND kp.ngayBaoCao <= :requestDay) " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or lower(:maChiNhanh) = lower(kp.chiNhanh)) " +
            "AND (:maBuuCuc is null or :maBuuCuc = '' or lower(:maBuuCuc) = lower(kp.buuCuc)) " +
            "AND (:maDoiTac is null or :maDoiTac = '' or lower(:maDoiTac) = lower(kp.doiTac)) " +
            "AND kp.status = 1  AND kp.loaiBaoCao = 2 " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kp.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kp.buuCuc in :listBuucuc) " +
            "GROUP BY kp.ngayBaoCao " +
            "ORDER BY kp.ngayBaoCao desc")
    List<DashboardTikTokKpiPhatResponse> getInfoDashBoardKpiPhatTikTok(LocalDate requestDay, LocalDate previousDay, String maChiNhanh, String maBuuCuc, String maDoiTac, List<String> listChinhanh, List<String> listBuucuc );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
            "sum(kp.tonXanh), sum(kp.tonVang), sum(kp.tonDo1), sum(kp.tonDo2), sum(kp.tonDo3), sum(kp.tonDo4), sum(kp.tonDo5), sum(kp.tonDo6), sum(kp.tonDo7), sum(kp.tonDo8), sum(kp.tonDo9), sum(kp.tongTon)) " +
            "FROM BaoCaoKPIPhat kp " +
            "WHERE kp.status = 1 AND kp.loaiBaoCao = 2 " +
            "AND kp.ngayBaoCao = :ngayBaoBao " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kp.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kp.buuCuc in :listBuucuc) " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or lower(kp.chiNhanh) = lower(:maChiNhanh) ) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or lower(kp.buuCuc) = lower(:maBuuCuc) ) " +
            "AND (:maDoiTac is NULL or :maDoiTac = '' or lower(kp.doiTac) = lower(:maDoiTac)) ")
    BaoCaoKPIPhatResDTO2 getTongLoaiCanhBao(String maChiNhanh, String maBuuCuc, String maDoiTac, LocalDate ngayBaoBao, List<String> listChinhanh, List<String> listBuucuc);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokKpiPhatResult(" +
            "kp.chiNhanh, sum(kp.slPTC), sum(kp.slPhaiPhat), sum(kp.slPTCDungGio), sum(kp.slPTCDungGioLanDau))" +
            "FROM BaoCaoKPIPhat kp " +
            "WHERE kp.status = 1 AND kp.loaiBaoCao = 2 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kp.chiNhanh) " +
            "AND (kp.chiNhanh NOT IN ('FFM','LOG','CNTCT')) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kp.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kp.doiTac) " +
            "AND (kp.ngayBaoCao between :from and :to )  " +
            "GROUP BY kp.chiNhanh ")
    List<DashboardTop10TikTokKpiPhatResult> getDataChartKpiPhatChiNhanhTikTok(LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac);
    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokKpiPhatResult(" +
            "kp.buuCuc, sum(kp.slPTC), sum(kp.slPhaiPhat), sum(kp.slPTCDungGio), sum(kp.slPTCDungGioLanDau))" +
            "FROM BaoCaoKPIPhat kp " +
            "WHERE kp.status = 1 AND kp.loaiBaoCao = 2 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kp.chiNhanh) " +
            "AND (kp.chiNhanh NOT IN ('FFM','LOG','CNTCT')) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kp.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kp.doiTac) " +
            "AND (kp.ngayBaoCao between :from and :to ) " +
            "GROUP BY kp.buuCuc ")
    List<DashboardTop10TikTokKpiPhatResult> getDataChartKpiPhatBuuCucTikTok(LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokKpiPhatResult(kp.chiNhanh,sum(kp.slPhaiPhat), sum(kp.slPhaiPhat))" +
            "FROM BaoCaoKPIPhat  kp " +
            "WHERE kp.status = 1 AND kp.loaiBaoCao = 2 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kp.chiNhanh) " +
            "AND (kp.chiNhanh NOT IN ('FFM','LOG','CNTCT')) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kp.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kp.doiTac) " +
            "AND (kp.ngayBaoCao = :requestDate) group by kp.chiNhanh" )
    List<DashboardTop10TikTokKpiPhatResult> getTop10CNSlPhaiPhat(LocalDate requestDate, String maChiNhanh, String maBuuCuc, String doiTac);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokKpiPhatResult(kp.buuCuc,sum(kp.slPhaiPhat), sum(kp.slPhaiPhat))" +
            "FROM BaoCaoKPIPhat  kp " +
            "WHERE kp.status = 1 AND kp.loaiBaoCao = 2 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kp.chiNhanh) " +
            "AND (kp.chiNhanh NOT IN ('FFM','LOG','CNTCT')) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kp.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kp.doiTac) " +
            "AND (kp.ngayBaoCao = :requestDate) group by kp.buuCuc" )
    List<DashboardTop10TikTokKpiPhatResult> getTop10BCSlPhaiPhat(LocalDate requestDate, String maChiNhanh, String maBuuCuc, String doiTac);

    /* Phải phát LK = Phải phát ngày */
    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTikTokKpiPhatResponse(sum(kp.slPhaiPhat), sum(kp.slPhaiPhat))" +
            "FROM BaoCaoKPIPhat  kp " +
            "WHERE kp.status = 1 AND kp.loaiBaoCao = 2 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kp.chiNhanh) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kp.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kp.doiTac) " +
            "AND (kp.ngayBaoCao = :requestDate) " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kp.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kp.buuCuc in :listBuucuc) " )
    DashboardTikTokKpiPhatResponse getSanLuongPhaiPhat(LocalDate requestDate, String maChiNhanh, String maBuuCuc, String doiTac, List<String> listChinhanh, List<String> listBuucuc);

    @Query("SELECT SUM(kp.tongTon)" +
            "FROM BaoCaoKPIPhat  kp " +
            "WHERE kp.status = 1 AND kp.loaiBaoCao = 2 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kp.chiNhanh) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kp.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kp.doiTac) " +
            "AND (kp.ngayBaoCao = :requestDate) " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kp.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kp.buuCuc in :listBuucuc) " )
    Long getSlTongTon(LocalDate requestDate, String maChiNhanh, String maBuuCuc, String doiTac, List<String> listChinhanh, List<String> listBuucuc);

    //loại báo cáo = 2 -> báo cáo chi nhánh
    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.slPhaiPhat + tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.loaiBaoCao = 2" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.slPhaiPhat + tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh in :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLTCTNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.slPhaiPhat + tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh = :chiNhanh" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " and tp.status = 1" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.slPhaiPhat + tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " and tp.status = 1" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLCNNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.tuyenBuuTa, sum(tp.slPhaiPhat + tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.status = 1" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc = :buuCuc" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("maDoiTac") String maDoiTac
    );

    //top 10 sản lượng query phần lũy kế
    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao between :ngayBatDau and :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.loaiBaoCao = 2" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLTCTLuyKe(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao between :ngayBatDau and :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh in :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLTCTNoAdminLuyKe(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao between :ngayBatDau and :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLCNLuyKe(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao between :ngayBatDau and :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLCNNoAdminLuyKe(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.tuyenBuuTa, sum(tp.slPTC))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao between :ngayBatDau and :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.status = 1" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc = :buuCuc" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLBCLuyKe(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    //top 10 sản lượng query phần lũy kế(số lượng phải phát lũy kế lấy ở cột phai_phat_lk theo ngày)
    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.phaiPhatLK))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.loaiBaoCao = 2" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLTCTLuyKePhaiPhat(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.phaiPhatLK))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh in :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLTCTNoAdminLuyKePhaiPhat(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.phaiPhatLK))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLCNLuyKePhaiPhat(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.phaiPhatLK))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLCNNoAdminLuyKePhaiPhat(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.tuyenBuuTa, sum(tp.phaiPhatLK))" +
            " from BaoCaoKPIPhat tp" +
            " where tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiBaoCao = 2" +
            " and tp.status = 1" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc = :buuCuc" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuTop10SLResponse> findKPIPhatTop10SLBCLuyKePhaiPhat(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT " +
            "distinct tp.tuyenBuuTa" +
            " from BaoCaoKPIPhat tp" +
            " where  tp.status = 1" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc = :buuCuc" +
            " and tp.loaiBaoCao = 4" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao")
    List<String> findDistinctBuuTa(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc
    );

    @Query("select tp.updatedAt from BaoCaoKPIPhat tp where tp.ngayBaoCao = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );

}
