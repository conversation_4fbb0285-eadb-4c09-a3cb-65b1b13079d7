package nocsystem.indexmanager.repositories.BaoCaoKhachHang<PERSON>on;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.BaoCaoKPIPhatBill;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoKPIPhatBillRepository extends PagingAndSortingRepository<BaoCaoKPIPhatBill, Long> {

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngay<PERSON>ao<PERSON>ao, tp.ma<PERSON><PERSON><PERSON><PERSON><PERSON>, tp.tinh<PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON><PERSON>, tp.huy<PERSON><PERSON>, tp.ma<PERSON><PERSON><PERSON><PERSON>,tp.ma<PERSON>a<PERSON><PERSON>, " +
            "tp.maDV<PERSON>iettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao, tp.tgPTC, tp.dgTimeGachBP1, tp.dgTimeGachBP2," +
            " tp.dgTimeGachBP3, tp.maBuuCucPhatThucTe)" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.tinhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhatThucTe = :buuCuc)" +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.status = :status" +
            " and tp.ton = 1 " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatTon(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao, tp.tgPTC, tp.dgTimeGachBP1, " +
            "tp.dgTimeGachBP2, tp.dgTimeGachBP3, tp.maBuuCucPhatThucTe )" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.tinhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhatThucTe in :buuCuc)" +
            " and tp.status = :status" +
            " and tp.ton = 1 " +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatTon1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao, tp.tgPTC," +
            " tp.dgTimeGachBP1, tp.dgTimeGachBP2, tp.dgTimeGachBP3, tp.maBuuCucPhatThucTe)" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.tinhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhatThucTe = :buuCuc)" +
            " and tp.status = :status" +
            " and tp.ton = 1 " +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatTonLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao," +
            " tp.tgPTC, tp.dgTimeGachBP1, tp.dgTimeGachBP2, tp.dgTimeGachBP3, tp.maBuuCucPhatThucTe)" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.tinhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhatThucTe in :buuCuc)" +
            " and tp.status = :status" +
            " and tp.ton = 1 " +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatTonLK1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao, tp.tgPTC, tp.dgTimeGachBP1, tp.dgTimeGachBP2, tp.dgTimeGachBP3, " +
            "tp.maBuuCucPhatThucTe, tp.tgNhanTai)" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.tinhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhatThucTe = :buuCuc)" +
            " and tp.status = :status" +
            " and tp.phat = 1 " +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatBill(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao, tp.tgPTC, tp.dgTimeGachBP1, tp.dgTimeGachBP2, tp.dgTimeGachBP3, " +
            "tp.maBuuCucPhatThucTe, tp.tgNhanTai)" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.tinhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhatThucTe in :buuCuc)" +
            " and tp.status = :status" +
            " and tp.phat = 1 " +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatBill1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao, tp.tgPTC, tp.dgTimeGachBP1, tp.dgTimeGachBP2, tp.dgTimeGachBP3, " +
            "tp.maBuuCucPhatThucTe, tp.tgNhanTai)" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.tinhPhat = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.maBuuCucPhatThucTe = :buuCuc)" +
            " and tp.status = :status" +
            " and tp.phat = 1 " +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatBillLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.tinhPhat, tp.huyenNhan, tp.huyenPhat, tp.maXaNhan,tp.maXaPhat, " +
            "tp.maDVViettel, tp.maBuuCucGoc, tp.maBuuCucPhat, tp.timeYeuCauLayHang,tp.ngayGuiBP,tp.maTrangThai, tp.maBuuCucHT,tp.maChiNhanhHT, " +
            "tp.maDoiTac, tp.maKHGui, tp.maDichVuDoiTac, tp.tgQuyDinhPhat, tp.tgQuyDinh, tp.tgChenhLechPhat, tp.tgChenhLech," +
            "tp.timePCP, tp.timeGachBP, tp.timeGachBP2, tp.timeGachBP3, tp.lyDoTT, tp.lyDoTT2,tp.lyDoTT3 ,tp.tienCOD ,tp.tienhang ," +
            "tp.trongLuong, tp.tongCuoc, tp.canhBao, tp.tuyenBuuTa, tp.soLanGiao, tp.tgPTC, tp.dgTimeGachBP1, tp.dgTimeGachBP2, tp.dgTimeGachBP3, " +
            "tp.maBuuCucPhatThucTe, tp.tgNhanTai)" +
            " from BaoCaoKPIPhatBill tp " +
            " where  (:doiTac is null or :doiTac = '' or tp.maDoiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.maDVViettel = :dichVu)" +
            " and (:vungPhat is null or :vungPhat = '' or tp.vungPhat = :vungPhat)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.tinhPhat in :chiNhanh)" +
            " and (tp.maBuuCucPhatThucTe in :buuCuc)" +
            " and tp.status = :status" +
            " and tp.phat = 1 " +
            " and (:tuyenBuuTa is null or :tuyenBuuTa = '' or tp.tuyenBuuTa = :tuyenBuuTa)" +
            " and tp.ngayBaoCao between :ngayDauThang and :ngayBaoCao")
    List<BaoCaoKPIPhatBillResDTO> findBaoCaoKPIPhatBillLK1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("vungPhat") String vungPhat,
            @Param("vungCon") String vungCon,
            @Param("doiTac") String doiTac,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("status") Integer status,
            @Param("tuyenBuuTa") String tuyenBuuTa
    );

}
