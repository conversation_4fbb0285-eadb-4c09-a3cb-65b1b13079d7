package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMBuuCuc;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the Permission entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DMBuuCucRepository extends JpaRepository<DMBuuCuc, Long>, JpaSpecificationExecutor<DMBuuCuc> {
    boolean existsByMaBuuCuc(String maBC);
    @Query(
        "SELECT dm " +
            " FROM DMBuuCuc dm WHERE " +
            "(COALESCE(:cnId, NULL) IS NULL OR dm.dmChiNhanh.id IN (:cnId)) " +
            " AND (COALESCE(:cnVungId, NULL) IS NULL OR dm.dmChiNhanhVung.id IN (:cnVungId))" +
            " AND (COALESCE(:ma_cn, NULL) IS NULL OR dm.maCn IN (:ma_cn))" +
            " AND (COALESCE(:ma_bc, NULL) IS NULL OR dm.maBuuCuc IN (:ma_bc))"+
            " AND (COALESCE(:ma_vung_con, NULL) IS NULL OR dm.maVung IN (:ma_vung_con)) ORDER BY dm.maBuuCuc"
    )
    Page<DMBuuCuc> getAllByChiNhanhAndVungCon(List<Long> cnId, List<Long> cnVungId, List<String> ma_cn, List<String> ma_vung_con, List<String> ma_bc, Pageable pageable);

    @Query(
            "SELECT dm.maBuuCuc " +
                    " FROM DMBuuCuc dm WHERE " +
                    " (:ma_cn is null or :ma_cn = '' or dm.maCn = :ma_cn)"
    )
    List<String> getAllBCByCN(String ma_cn);

    @Query(
            "SELECT dm.maBuuCuc " +
                    " FROM DMBuuCuc dm WHERE " +
                    " (:ma_cn is null or :ma_cn = '' or dm.maCn = :ma_cn)"
    )
    Page<String> getPageBCByCN(String ma_cn, Pageable page);

    @Query(
            "SELECT count(dm.maBuuCuc) " +
                    " FROM DMBuuCuc dm WHERE " +
                    " (:ma_cn is null or :ma_cn = '' or dm.maCn = :ma_cn)"
    )
    Integer getBCByCNTotal(String ma_cn);

    @Query(
        "SELECT dm " +
            " FROM DMBuuCuc dm WHERE " +
            "(:maBuuCuc IS NULL OR dm.maBuuCuc LIKE %:maBuuCuc%) ORDER BY dm.maBuuCuc"
    )
    Page<DMBuuCuc> getAllByMaBC(String maBuuCuc, Pageable pageable);
}
