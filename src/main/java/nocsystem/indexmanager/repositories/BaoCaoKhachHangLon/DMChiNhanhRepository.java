package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanh;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiNhanhResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the Permission entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DMChiNhanhRepository extends JpaRepository<DMChiNhanh, Long>, JpaSpecificationExecutor<DMChiNhanh> {
    Boolean existsByMaCn(String ma_cn);

    DMChiNhanh findByMaCn(String ma_cn);

    @Query(
        "SELECT dm " +
            " FROM DMChiNhanh dm WHERE " +
            "(:maCn IS NULL OR dm.maCn LIKE %:maCn%) ORDER BY dm.tenCn"
    )
    Page<DMChiNhanh> getAllByMaCn(String maCn, Pageable pageable);

    @Query(
        "SELECT dm " +
            " FROM DMChiNhanh dm WHERE" +
            " (COALESCE(:maCn,NULL) IS NULL OR dm.maCn IN (:maCn))" +
            " AND ((dm.maCn = dm.deptCode) OR (dm.deptCode = 'SBUFM' OR dm.deptCode = 'TCT'))" +
            " AND (COALESCE(:maVung,NULL) IS NULL OR dm.dmVungMien.maVungMien IN (:maVung)) order by dm.tenCn"
    )
    Page<DMChiNhanh> getAllByMaVung(List<String> maCn, List<String> maVung, Pageable pageable);

    @Query(
            "SELECT dm.maCn " +
                    " FROM DMChiNhanh dm WHERE" +
                    " ((dm.maCn = dm.deptCode) OR (dm.deptCode = 'SBUFM' OR dm.deptCode = 'TCT'))" +
                    " and (:maVung is null or :maVung = '' or dm.dmVungMien.maVungMien = :maVung)"
    )
    List<String> getAllCNByVung( String maVung);

    @Query(
            "SELECT dm.maCn " +
                    " FROM DMChiNhanh dm WHERE" +
                    " ((dm.maCn = dm.deptCode) OR (dm.deptCode = 'SBUFM' OR dm.deptCode = 'TCT'))" +
                    " and (:maVung is null or :maVung = '' or dm.dmVungMien.maVungMien = :maVung)"
    )
    Page<String> getPageCNByVung(
            String maVung,
            Pageable page
    );

    @Query(
            "SELECT count(dm.maCn) " +
                    " FROM DMChiNhanh dm WHERE" +
                    " ((dm.maCn = dm.deptCode) OR (dm.deptCode = 'SBUFM' OR dm.deptCode = 'TCT'))" +
                    " and (:maVung is null or :maVung = '' or dm.dmVungMien.maVungMien = :maVung)"
    )
    Integer getCNByVung(
            String maVung
    );

    @Query(
        "SELECT DISTINCT dm.deptCode " +
            " FROM DMChiNhanh dm")
    List<String> getAllDeptCode();

    @Query(
        "SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiNhanhResponse(dm.maCn,dm.tenCn)   " +
            " FROM DMChiNhanh dm WHERE" +
            " (:deptCode IS NULL OR UPPER(dm.deptCode) = UPPER(:deptCode) )")
    ChiNhanhResponse getMaCnByDeptCode(String deptCode);
}
