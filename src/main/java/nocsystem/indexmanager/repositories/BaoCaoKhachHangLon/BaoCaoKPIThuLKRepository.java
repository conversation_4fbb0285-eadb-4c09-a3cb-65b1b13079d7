package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.BaoCaoKPIThuLK;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoKPIThuLKRepository extends PagingAndSortingRepository<BaoCaoKPIThuLK, Long> {

//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.vung, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.vung" +
//            " order by tp.vung")
//    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCT(
//            @Param("doiTac") String doiTac,
//            @Param("vung") String vung,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.chiNhanh, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.chiNhanh" +
//            " order by tp.chiNhanh")
//    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1(
//            @Param("doiTac") String doiTac,
//            @Param("vung") String vung,
//            @Param("chiNhanh") String chiNhanh,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.buuCuc, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.buuCuc" +
//            " order by tp.buuCuc")
//    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN(
//            @Param("doiTac") String doiTac,
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.buuCuc, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (tp.chiNhanh in :chiNhanh)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.buuCuc" +
//            " order by tp.buuCuc")
//    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN1(
//            @Param("doiTac") String doiTac,
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.doiTac, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.doiTac" +
//            " order by tp.doiTac")
//    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDT(
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.tuyenBuuTa, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa" +
//            " order by tp.tuyenBuuTa")
//    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC(
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.tuyenBuuTa, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (tp.chiNhanh in :chiNhanh)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa" +
//            " order by tp.tuyenBuuTa")
//    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC1(
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.vung, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.vung")
//    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTSum(
//            @Param("doiTac") String doiTac,
//            @Param("vung") String vung,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.chiNhanh, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.chiNhanh")
//    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1Sum(
//            @Param("doiTac") String doiTac,
//            @Param("vung") String vung,
//            @Param("chiNhanh") String chiNhanh,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.buuCuc, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.buuCuc")
//    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum(
//            @Param("doiTac") String doiTac,
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.buuCuc, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (tp.chiNhanh in :chiNhanh)" +
//            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.buuCuc")
//    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum1(
//            @Param("doiTac")  String doiTac,
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.doiTac, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThuLK tp " +
//            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.doiTac")
//    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTSum(
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.tuyenBuuTa, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThu tp " +
//            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa")
//    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum(
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
//            " tp.tuyenBuuTa, " +
//            " sum(tp.sL), sum(tp.slThuTC), sum(tp.slThuDG),sum(tp.slThuDG1)," +
//            "sum(tp.huy), sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTon))" +
//            " from BaoCaoKPIThu tp " +
//            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
//            " and (tp.chiNhanh in :chiNhanh)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.thoiGian = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa")
//    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum1(
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiDon") String loaiDon,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
}
