package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.BaoCaoKPIPhatLK;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoKPIPhatLKRepository extends PagingAndSortingRepository<BaoCaoKPIPhatLK, Long> {

//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (tp.loaiBaoCao = :loaiBaoCao)" +
//            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.vungPhat" +
//            " order by tp.vungPhat")
//    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCT(
//            @Param("vung") String vung,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.chiNhanh" +
//            " order by tp.chiNhanh")
//    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVung(
//            @Param("vung") String vung,
//            @Param("chiNhanh") String chiNhanh,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.buuCuc, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.buuCuc" +
//            " order by tp.buuCuc")
//    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN(
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.buuCuc, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and (tp.chiNhanh in :chiNhanh)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.buuCuc" +
//            " order by tp.buuCuc")
//    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCN1(
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon))" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa" +
//            " order by tp.tuyenBuuTa")
//    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC(
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon))" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (tp.chiNhanh in :chiNhanh)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa" +
//            " order by tp.tuyenBuuTa")
//    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBC1(
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.doiTac, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.doiTac " +
//            " order by tp.doiTac")
//    Page<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDT(
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.vungPhat,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (tp.loaiBaoCao = :loaiBaoCao)" +
//            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.vungPhat")
//    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatTCTSum(
//            @Param("vung") String vung,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.chiNhanh,sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and (:vung is null or :vung = '' or tp.vungPhat = :vung)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.chiNhanh")
//    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatVungSum(
//            @Param("vung") String vung,
//            @Param("chiNhanh") String chiNhanh,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.buuCuc, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.buuCuc")
//    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum(
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.buuCuc, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and (tp.chiNhanh in :chiNhanh)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.buuCuc")
//    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatCNSum1(
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.doiTac, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon) )" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.doiTac")
//    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatDTSum(
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon))" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
//            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa")
//    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum(
//            @Param("chiNhanh") String chiNhanh,
//            @Param("buuCuc") String buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
//
//    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2(" +
//            "tp.tuyenBuuTa, sum(tp.tongSanLuong), sum(tp.slPTCDungGio), " +
//            "sum(tp.slPTCDungGioLanDau), sum(tp.slPhaiPhat), sum(tp.slChuaPCP), sum(tp.tt500), sum(tp.ttKhac), sum(tp.slPTC), " +
//            "sum(tp.tonXanh), sum(tp.tonVang)," +
//            "sum(tp.tonDo1), sum(tp.tonDo2), sum(tp.tonDo3), sum(tp.tonDo4), sum(tp.tonDo5), sum(tp.tonDo6),sum(tp.tonDo7) ,sum(tp.tonDo8) ,sum(tp.tonDo9) ,sum(tp.tongTon))" +
//            " from BaoCaoKPIPhatLK tp " +
//            " where  (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
//            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
//            " and (tp.chiNhanh in :chiNhanh)" +
//            " and (tp.buuCuc in :buuCuc)" +
//            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
//            " and tp.loaiBaoCao = :loaiBaoCao" +
//            " and tp.ngayBaoCao = :ngayBaoCao" +
//            " group by tp.tuyenBuuTa")
//    List<BaoCaoKPIPhatResDTO2> findBaoCaoKPIPhatBCSum1(
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc,
//            @Param("buuTa") String buuTa,
//            @Param("doiTac") String doiTac,
//            @Param("dichVu") String dichVu,
//            @Param("loaiBaoCao") Integer loaiBaoCao,
//            @Param("ngayBaoCao") LocalDate ngayBaoCao
//    );
}
