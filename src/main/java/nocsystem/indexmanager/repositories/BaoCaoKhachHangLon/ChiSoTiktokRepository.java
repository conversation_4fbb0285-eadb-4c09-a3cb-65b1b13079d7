package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.ChisoTiktok;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiSoTiktokDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ChiSoTiktokRepository extends JpaRepository<ChisoTiktok, Long> {
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiSoTiktokDTO(" +
            " cs.maChiso, avg(cs.giatriVtp), avg(cs.giatriDoiTac), avg(cs.kpiDoiTac) " +
            ")" +
            " from ChisoTiktok cs " +
            " where cs.ngayBaocao = :ngayBaocao and cs.maDoiTac = :maDoiTac " +
            " group by cs.maChiso " +
            " order by cs.maChiso ASC")
    List<ChiSoTiktokDTO> getChiSoTiktokNgay(
            @Param("ngayBaocao") LocalDate ngayBaocao,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiSoTiktokDTO(" +
            " cs.maChiso, avg(cs.giatriVtp), avg(cs.giatriDoiTac), avg(cs.kpiDoiTac) " +
            ")" +
            " from ChisoTiktok cs " +
            " where cs.ngayBaocao between :ngayDauThang and :ngayBaocao and cs.maDoiTac = :maDoiTac group by cs.maChiso order by cs.maChiso ASC")
    List<ChiSoTiktokDTO> getChiSoTiktokThang(
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("ngayBaocao") LocalDate ngayBaocao,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiSoTiktokDTO(" +
            " cs.maChiso, avg(cs.giatriVtp), avg(cs.giatriDoiTac), avg(cs.kpiDoiTac) " +
            ")" +
            " from ChisoTiktok cs " +
            " where cs.ngayBaocao between :ngayBaocao and :ngayDauThang and cs.maDoiTac = :maDoiTac group by cs.maChiso order by cs.maChiso ASC")
    List<ChiSoTiktokDTO> getChiSoTiktokTuan(
            @Param("ngayBaocao") LocalDate ngayBaocao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("maDoiTac") String maDoiTac
    );
    @Query("SELECT DISTINCT cs.maDoiTac " +
            "FROM ChisoTiktok cs " +
            "WHERE cs.ngayBaocao between :ngayDauThang and :ngayBaocao")
    List<String> getListMaDoiTac( @Param("ngayDauThang") LocalDate ngayDauThang,@Param("ngayBaocao") LocalDate ngayBaocao);




}
