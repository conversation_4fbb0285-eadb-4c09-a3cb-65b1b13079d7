package nocsystem.indexmanager.repositories.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.BaoCaoKPIThu;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.*;
import org.apache.poi.util.LocaleID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface BaoCaoKPIThuRepository extends PagingAndSortingRepository<BaoCaoKPIThu, Long> {


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCT(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung, tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTVaVungCon(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTShopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung, tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTShopeeVaVungCon(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1Shopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNShopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN1(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN1Shopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDT(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC1Shopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTSum(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTSumShopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1Sum(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1SumShopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSumShopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum1(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum1Shopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTSum(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTSumShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSumShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum1(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(tp.xanh), sum(tp.vang), sum(tp.do1), sum(tp.do2), sum(tp.do3),sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum1Shopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTLK(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung, tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTLKVaVungCon(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end)," +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTLKShopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end)," +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung, tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTLKShopeeVaVungCon(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1LK(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate

    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end)," +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1LKShopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNLK(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end)," +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNLKShopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN1LK(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,

            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN1LKShopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate

    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end)," +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTLKShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end)," +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCLKShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC1LK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL) as sl, sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC1LKShopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTSumLK(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTSumLKShopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1SumLK(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1SumLKShopee(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSumLK(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate

    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSumLKShopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum1LK(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc,tp.vungCon, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.buuCuc,tp.vungCon")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum1LKShopee(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("vungCon") String vungCon,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTSumLK(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTSumLKShopee(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSumLK(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSumLKShopee(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum1LK(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.sL), sum(case when tp.loaiDon = 'DROPOFF' then tp.sL else 0 end), " +
            " sum(tp.slThuTC), sum(tp.huy), sum(tp.giaiTrinh), sum(tp.chuaGiaiTrinh), sum(tp.slThuDG),sum(tp.slThuDG1)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.xanh else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.vang else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do1 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do2 else 0 end), " +
            " sum(case when tp.thoiGian = :comparisonDate then tp.do3 else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.tongTonSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.quaHanSLA else 0 end)," +
            " sum(case when tp.thoiGian = :comparisonDate then tp.chuaQuaHanSLA else 0 end), " +
            " sum(tp.tongSlLayHangDungHen), " +
            " sum(tp.tongSlLayHangTcDungHen), " +
            " sum(tp.tongSlTrongKy))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum1LKShopee(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("comparisonDate") LocalDate comparisonDate
    );

    // lấy cột tồn SLA theo tổng toàn bộ tồn
    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " group by tp.vung")
    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTNew(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1New(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and tp.status = 1" +
            " group by tp.buuCuc")
    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNNew(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.status = 1" +
            " group by tp.buuCuc")
    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCN1New(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTNew(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCNew(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    Page<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBC1New(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.vung, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " group by tp.vung")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuTCTSumNew(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.chiNhanh, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:vung is null or :vung = '' or tp.vung = :vung)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuVung1SumNew(
            @Param("doiTac") String doiTac,
            @Param("vung") String vung,
            @Param("chiNhanh") String chiNhanh,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and tp.status = 1" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSumNew(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.buuCuc, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (tp.chiNhanh in :chiNhanh)" +
            " and (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and tp.status = 1" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuCNSum1New(
            @Param("doiTac") String doiTac,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.doiTac, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and tp.status = 1" +
            " group by tp.doiTac")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuDTSumNew(
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
            " and (:buuCuc is null or :buuCuc = '' or tp.buuCuc = :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSumNew(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            " tp.tuyenBuuTa, sum(tp.tongTonSLA), sum(tp.quaHanSLA), sum(tp.chuaQuaHanSLA))" +
            " from BaoCaoKPIThu tp " +
            " where (:doiTac is null or :doiTac = '' or tp.doiTac = :doiTac)" +
            " and (:dichVu is null or :dichVu = '' or tp.dichVu = :dichVu)" +
            " and (:loaiDon is null or :loaiDon = '' or tp.loaiDon = :loaiDon)" +
            " and (tp.chiNhanh in :chiNhanh)" +
            " and (tp.buuCuc in :buuCuc)" +
            " and (:buuTa is null or :buuTa = '' or tp.tuyenBuuTa like %:buuTa%)" +
            " and tp.status = 1" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuResDTO2> findBaoCaoKPIThuBCSum1New(
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("buuTa") String buuTa,
            @Param("doiTac") String doiTac,
            @Param("dichVu") String dichVu,
            @Param("loaiDon") String loaiDon
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTikTokKpiThuResponse(" +
            "sum(kt.sL), sum(kt.slThuTC), sum(kt.slThuDG), sum(kt.slThuDG1), sum(kt.tongTonSLA), sum(kt.huy), kt.thoiGian) " +
            "FROM BaoCaoKPIThu kt " +
            "WHERE kt.status = 1 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kt.chiNhanh) " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kt.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kt.buuCuc in :listBuucuc) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kt.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kt.doiTac) " +
            "AND (kt.thoiGian between :from and :to ) " +
            "GROUP BY kt.thoiGian " +
            "ORDER BY kt.thoiGian desc")
    List<DashboardTikTokKpiThuResponse> getInfoDashBoardKpiThuTikTok(LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac, List<String> listChinhanh, List<String> listBuucuc);

    @Query("SELECT sum(kt.sL)" +
            "from BaoCaoKPIThu kt " +
            "WHERE lower(kt.loaiDon) = lower(:loaiDon) " +
            "AND kt.status = 1 " +
            "AND (kt.thoiGian BETWEEN :from AND :to) " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or lower(:maChiNhanh) = lower(kt.chiNhanh) ) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or lower(:maBuuCuc) = lower(kt.buuCuc) ) " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kt.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kt.buuCuc in :listBuucuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or lower(:doiTac) = lower(kt.doiTac) ) "
    )
    Long sumByLoaiDon(String loaiDon, LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac, List<String> listChinhanh, List<String> listBuucuc);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokKpiThuResult(" +
            "kt.chiNhanh, sum(kt.sL), sum(kt.slThuTC), sum(kt.slThuDG), sum(kt.huy),sum(kt.slThuDG1))" +
            "FROM BaoCaoKPIThu kt " +
            "WHERE kt.status = 1 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kt.chiNhanh) " +
            "AND (kt.chiNhanh NOT IN ('FFM','LOG','CNTCT')) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kt.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kt.doiTac) " +
            "AND (kt.thoiGian between :from and :to )  " +
            "GROUP BY kt.chiNhanh ")
    List<DashboardTop10TikTokKpiThuResult> getDataChartKpiThuChiNhanhTikTok(LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokKpiThuResult(" +
            "kt.buuCuc, sum(kt.sL), sum(kt.slThuTC), sum(kt.slThuDG), sum(kt.huy), sum(kt.slThuDG1))" +
            "FROM BaoCaoKPIThu kt " +
            "WHERE kt.status = 1 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or :maChiNhanh = kt.chiNhanh) " +
            "AND (kt.chiNhanh NOT IN ('FFM','LOG','CNTCT')) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or :maBuuCuc = kt.buuCuc) " +
            "AND (:doiTac is NULL or :doiTac = '' or :doiTac = kt.doiTac) " +
            "AND (kt.thoiGian between :from and :to ) " +
            "GROUP BY kt.buuCuc ")
    List<DashboardTop10TikTokKpiThuResult> getDataChartKpiThuBuuCucTikTok(LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Top10TikTokKpiThuSlTheoLoaiDonResult(" +
            "kt.chiNhanh, sum(kt.sL)) " +
            "from BaoCaoKPIThu kt " +
            "WHERE lower(kt.loaiDon) = lower(:loaiDon) " +
            "AND kt.status = 1 " +
            "AND (kt.thoiGian BETWEEN :from AND :to) " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or lower(:maChiNhanh) = lower(kt.chiNhanh) ) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or lower(:maBuuCuc) = lower(kt.buuCuc) ) " +
            "AND (:doiTac is NULL or :doiTac = '' or lower(:doiTac) = lower(kt.doiTac)) "+
            "GROUP BY kt.chiNhanh "
    )
    List<Top10TikTokKpiThuSlTheoLoaiDonResult> sumSlChiNhanhByLoaiDon(String loaiDon, LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Top10TikTokKpiThuSlTheoLoaiDonResult(" +
            "kt.buuCuc, sum(kt.sL)) " +
            "from BaoCaoKPIThu kt " +
            "WHERE lower(kt.loaiDon) = lower(:loaiDon) " +
            "AND kt.status = 1 " +
            "AND (kt.thoiGian BETWEEN :from AND :to) " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or lower(:maChiNhanh) = lower(kt.chiNhanh) ) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or lower(:maBuuCuc) = lower(kt.buuCuc) ) " +
            "AND (:doiTac is NULL or :doiTac = '' or lower(:doiTac) = lower(kt.doiTac)) "+
            "GROUP BY kt.buuCuc "
    )
    List<Top10TikTokKpiThuSlTheoLoaiDonResult> sumSlBuuCucByLoaiDon(String loaiDon, LocalDate from, LocalDate to, String maChiNhanh, String maBuuCuc, String doiTac);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2(" +
            "sum(kt.xanh), sum(kt.vang), sum(kt.do1), sum(kt.do2), sum(kt.do3), sum(kt.tongTonSLA)) " +
            "FROM BaoCaoKPIThu kt " +
            "WHERE kt.status = 1 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or lower(kt.chiNhanh) = lower(:maChiNhanh) ) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or lower(kt.buuCuc) = lower(:maBuuCuc) ) " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kt.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kt.buuCuc in :listBuucuc) " +
            "AND (:maDoiTac is NULL or :maDoiTac = '' or lower(kt.doiTac) = lower(:maDoiTac)) ")
    BaoCaoKPIThuResDTO2 getTongLoaiCanhBao(String maChiNhanh, String maBuuCuc, String maDoiTac, List<String> listChinhanh, List<String> listBuucuc);

    @Query("select SUM(kt.tongTonSLA)" +
            "FROM BaoCaoKPIThu kt " +
            "WHERE kt.status = 1 " +
            "AND (:maChiNhanh is NULL or :maChiNhanh = '' or lower(kt.chiNhanh) = lower(:maChiNhanh) ) " +
            "AND (:maBuuCuc is NULL or :maBuuCuc = '' or lower(kt.buuCuc) = lower(:maBuuCuc) ) " +
            "AND (COALESCE(:listChinhanh, NULL) is NULL or kt.chiNhanh in :listChinhanh) " +
            "AND (COALESCE(:listBuucuc, NULL) is NULL or kt.buuCuc in :listBuucuc) " +
            "AND (:maDoiTac is NULL or :maDoiTac = '' or lower(kt.doiTac) = lower(:maDoiTac)) " +
            "AND kt.thoiGian = :ngaybaocao")
    Long getTongSlTonThu(String maChiNhanh, String maBuuCuc, String maDoiTac, List<String> listChinhanh, List<String> listBuucuc, LocalDate ngaybaocao);

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.sL))" +
            " from BaoCaoKPIThu tp" +
            " where tp.thoiGian between :ngayBatDau and :ngayBaoCao" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIThuTop10SLTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.chiNhanh, sum(tp.sL))" +
            " from BaoCaoKPIThu tp" +
            " where tp.thoiGian between :ngayBatDau and :ngayBaoCao" +
            " and tp.chiNhanh in :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.chiNhanh")
    List<BaoCaoKPIThuTop10SLResponse> findKPIThuTop10SLTCTNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.sL))" +
            " from BaoCaoKPIThu tp" +
            " where tp.thoiGian between :ngayBatDau and :ngayBaoCao" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIThuTop10SLCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.buuCuc, sum(tp.sL))" +
            " from BaoCaoKPIThu tp" +
            " where tp.thoiGian between :ngayBatDau and :ngayBaoCao" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc in :buuCuc" +
            " and tp.status = 1" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.buuCuc")
    List<BaoCaoKPIThuTop10SLResponse> findKPIThuTop10SLCNNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") List<String> buuCuc,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("SELECT NEW nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuTop10SLResponse(" +
            "tp.tuyenBuuTa, sum(tp.sL))" +
            " from BaoCaoKPIThu tp" +
            " where tp.thoiGian between :ngayBatDau and :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc = :buuCuc" +
            " AND (:maDoiTac is NULL or :maDoiTac = '' or :maDoiTac = tp.doiTac)" +
            " group by tp.tuyenBuuTa")
    List<BaoCaoKPIThuTop10SLResponse> findKPIThuTop10SLBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("maDoiTac") String maDoiTac
    );

    @Query("select tp.updatedAt from BaoCaoKPIThu tp where tp.thoiGian = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );

    @Query("SELECT " +
            "distinct tp.tuyenBuuTa" +
            " from BaoCaoKPIThu tp" +
            " where  tp.status = 1" +
            " and tp.chiNhanh = :chiNhanh" +
            " and tp.buuCuc = :buuCuc" +
            " and tp.thoiGian between :ngayDauThang and :ngayBaoCao")
    List<String> findDistinctBuuTa(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ngayDauThang") LocalDate ngayDauThang,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc
    );

}
