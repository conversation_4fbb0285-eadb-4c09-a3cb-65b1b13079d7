package nocsystem.indexmanager.repositories.lastmile_cron;

import nocsystem.indexmanager.controllers.lastmile_cldv.LastmileCronDto;
import nocsystem.indexmanager.controllers.lastmile_cldv.LastmileThangCronEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;

public interface LastmileRepoThangPostgres extends JpaRepository<LastmileThangCronEntity,Long> {
    @Query(
            "SELECT new nocsystem.indexmanager.controllers.lastmile_cldv.LastmileCronDto" +
                    "  ( dtcn.sl_phat, " +
                    "  dtcn.sl_phat_n1," +
                    "  dtcn.sl_phat_thanhcong," +
                    "  dtcn.sl_phat_thanhcong_n1, " +
                    "  dtcn.tyle_phat_thanhcong, " +
                    "  dtcn.tyle_phat_thanhcong_n1, " +
                    "  dtcn.tyle_phat_thanhcong_dunggio, " +
                    "  dtcn.tyle_phat_thanhcong_dunggio_n1, " +
                    "  dtcn.tyle_phat_thanhcong_ncod, " +
                    "  dtcn.tyle_phat_thanhcong_ncod_n1, " +
                    "  dtcn.tyle_phat_thanhcong_cod, " +
                    "  dtcn.tyle_phat_thanhcong_cod_n1, " +
                    "  dtcn.tyle_hoan, " +
                    "  dtcn.tyle_hoan_n1, " +
                    "  dtcn.tang_giam_sl_phat, " +
                    "  dtcn.tang_giam_sl_ptc, " +

                    "  dtcn.tang_giam_tyle_ptc, " +
                    "  dtcn.kpi_ptc, " +
                    "  dtcn.tang_giam_tyle_ptc_ncod, " +
                    "  dtcn.kpi_ptc_ncod, " +

                    "  dtcn.tang_giam_tyle_ptc_cod, " +
                    "  dtcn.kpi_ptc_cod, " +
                    "  dtcn.tang_giam_tyle_ptc_dunggio, " +
                    "  dtcn.kpi_ptc_dunggio, " +
                    "  dtcn.tang_giam_tyle_hoan, " +
                    "  dtcn.kpi_hoan )" +

                    " FROM LastmileThangCronEntity dtcn" +
                    " WHERE dtcn.ngay_kpi = :thang " +

                    " and dtcn.ma_bc = :maBc "+
                    " and dtcn.ma_cn = :maCn "
    )
    LastmileCronDto getDataLmFromFilter(
            Date thang,
            String maCn,
            String maBc
    );

}
