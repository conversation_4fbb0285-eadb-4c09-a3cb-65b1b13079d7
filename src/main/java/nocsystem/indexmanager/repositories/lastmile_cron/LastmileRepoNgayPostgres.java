package nocsystem.indexmanager.repositories.lastmile_cron;

import nocsystem.indexmanager.controllers.lastmile_cldv.LastmileChartCronDto;
import nocsystem.indexmanager.controllers.lastmile_cldv.LastmileNgayCronEntity;
import nocsystem.indexmanager.controllers.lastmile_cldv.ResponseChartNew;
import nocsystem.indexmanager.controllers.lastmile_cldv.LastmileCronDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LastmileRepoNgayPostgres extends JpaRepository<LastmileNgayCronEntity,Long> {

    @Query(
            "SELECT new nocsystem.indexmanager.controllers.lastmile_cldv.LastmileCronDto" +
                    "  ( dtcn.sl_phat, " +
                    "  dtcn.sl_phat_n1," +
                    "  dtcn.sl_phat_thanhcong," +
                    "  dtcn.sl_phat_thanhcong_n1, " +
                    "  dtcn.tyle_phat_thanhcong, " +
                    "  dtcn.tyle_phat_thanhcong_n1, " +
                    "  dtcn.tyle_phat_thanhcong_dunggio, " +
                    "  dtcn.tyle_phat_thanhcong_dunggio_n1, " +
                    "  dtcn.tyle_phat_thanhcong_ncod, " +
                    "  dtcn.tyle_phat_thanhcong_ncod_n1, " +
                    "  dtcn.tyle_phat_thanhcong_cod, " +
                    "  dtcn.tyle_phat_thanhcong_cod_n1, " +
                    "  dtcn.tyle_hoan, " +
                    "  dtcn.tyle_hoan_n1, " +
                    "  dtcn.tang_giam_sl_phat, " +
                    "  dtcn.tang_giam_sl_ptc, " +

                    "  dtcn.tang_giam_tyle_ptc, " +
                    "  dtcn.kpi_ptc, " +
                    "  dtcn.tang_giam_tyle_ptc_ncod, " +
                    "  dtcn.kpi_ptc_ncod, " +

                    "  dtcn.tang_giam_tyle_ptc_cod, " +
                    "  dtcn.kpi_ptc_cod, " +
                    "  dtcn.tang_giam_tyle_ptc_dunggio, " +
                    "  dtcn.kpi_ptc_dunggio, " +
                    "  dtcn.tang_giam_tyle_hoan, " +
                    "  dtcn.kpi_hoan )" +

                    " FROM LastmileNgayCronEntity dtcn" +
                    " WHERE dtcn.ngay_kpi = :thang " +

                    " and dtcn.ma_bc = :maBc "+
                    " and dtcn.ma_cn = :maCn "
    )
    LastmileCronDto getDataLmFromFilter(
            Date thang,
            String maCn,
            String maBc
    );


    @Query(
            "SELECT new nocsystem.indexmanager.controllers.lastmile_cldv.LastmileChartCronDto" +
                    " ( TO_CHAR(dtcn.ngay_kpi, 'YYYY-MM-DD') , dtcn.tyle_phat_thanhcong )" +
                    " FROM LastmileNgayCronEntity dtcn" +
                    " WHERE dtcn.ngay_kpi <= :ngayChon " +

                    " and dtcn.ngay_kpi >= :ngayDauThang "+
                    " and dtcn.ma_bc = :maBc "+
                    " and dtcn.ma_cn = :maCn "
    )
    List<LastmileChartCronDto> getDataChart(
            Date ngayChon,
            Date ngayDauThang,
            String maCn,
            String maBc
    );

}

