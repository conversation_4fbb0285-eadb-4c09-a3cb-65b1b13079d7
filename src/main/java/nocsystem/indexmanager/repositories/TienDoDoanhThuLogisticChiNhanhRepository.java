package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuLogisticJPADto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticOverViewDto;
import nocsystem.indexmanager.models.TienDoDoanhThuLogistic.TienDoDoanhThuLogisticChiNhanh;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDTLogisticChiNhanhV1Dto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuLogisticChiNhanhRepository extends JpaRepository<TienDoDoanhThuLogisticChiNhanh, Long> {
    @Query("SELECT dtcn_logis.maChiNhanh as maChiNhanh, dtcn_logis.dichVu as dichVu, " +
            "dtcn_logis.tlht as tlht, dtcn_logis.tienDo as tienDo, dtcn_logis.ttNam as ttNam, " +
            "dtcn_logis.ttThang as ttThang, dtcn_logis.ttTbnThang as ttTbnThang, dtcn_logis.keHoach as keHoach, " +
            "dtcn_logis.thucHien as thucHien " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtcn_logis " +
            "WHERE dtcn_logis.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtcn_logis.dichVu = :dichVu) " +
            "AND dtcn_logis.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtcn_logis.maChiNhanh asc ")
    Page<TienDoDTLogisticChiNhanhV1Dto> findTienDoDoanhThuChiNhanh(
            @Param("dichVu") String dichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcn_logis.maChiNhanh as maChiNhanh, dtcn_logis.dichVu as dichVu, " +
            "dtcn_logis.tlht as tlht, dtcn_logis.tienDo as tienDo, dtcn_logis.ttNam as ttNam, " +
            "dtcn_logis.ttThang as ttThang, dtcn_logis.ttTbnThang as ttTbnThang, dtcn_logis.keHoach as keHoach, " +
            "dtcn_logis.thucHien as thucHien " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtcn_logis " +
            "WHERE dtcn_logis.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtcn_logis.dichVu = :dichVu) " +
            "AND (dtcn_logis.maChiNhanh IN :listChiNhanhVeriable) " +
            "order by dtcn_logis.maChiNhanh asc ")
    Page<TienDoDTLogisticChiNhanhV1Dto> findTienDoDoanhThuChiNhanhV2(
            @Param("dichVu") String dichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT dtcn_logis.maChiNhanh as maChiNhanh, dtcn_logis.dichVu as dichVu, dtcn_logis.keHoach as keHoach, " +
            "dtcn_logis.thucHien as thucHien, dtcn_logis.tlht as tlHoanThanh, dtcn_logis.tienDo as tienDo, " +
            "dtcn_logis.ttThang as ttThang, dtcn_logis.ttTbnThang as ttTbnThang, dtcn_logis.thangTruoc as thangTruoc," +
            "dtcn_logis.namTruoc as namTruoc, dtcn_logis.cungKyNgay as cungKyNgay, " +
            "dtcn_logis.cungKyNam as cungKyNam, dtcn_logis.ttNam as ttNam, dtcn_logis.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtcn_logis " +
            "WHERE dtcn_logis.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR dtcn_logis.maChiNhanh = :maChiNhanh)")
    List<TienDoDoanhThuLogisticOverViewDto> findTienDoDoanhThuChiNhanhOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtcn_logis.maChiNhanh as maChiNhanh, dtcn_logis.dichVu as dichVu, dtcn_logis.keHoach as keHoach, " +
            "dtcn_logis.thucHien as thucHien, dtcn_logis.tlht as tlHoanThanh, dtcn_logis.tienDo as tienDo, " +
            "dtcn_logis.ttThang as ttThang, dtcn_logis.ttTbnThang as ttTbnThang, dtcn_logis.thangTruoc as thangTruoc," +
            "dtcn_logis.namTruoc as namTruoc, dtcn_logis.cungKyNgay as cungKyNgay, " +
            "dtcn_logis.cungKyNam as cungKyNam, dtcn_logis.ttNam as ttNam, dtcn_logis.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtcn_logis " +
            "WHERE dtcn_logis.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR dtcn_logis.maChiNhanh = :maChiNhanh) " +
            "AND dtcn_logis.maChiNhanh IN :listChiNhanhVeriable ")
    List<TienDoDoanhThuLogisticOverViewDto> findTienDoDoanhThuChiNhanhOverViewV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT dtcn_logis.maChiNhanh as maChiNhanh, dtcn_logis.dichVu as dichVu, dtcn_logis.keHoach as keHoach, " +
            "dtcn_logis.thucHien as thucHien, dtcn_logis.tlht as tlHoanThanh, dtcn_logis.tienDo as tienDo, " +
            "dtcn_logis.ttThang as ttThang, dtcn_logis.ttTbnThang as ttTbnThang, dtcn_logis.thangTruoc as thangTruoc," +
            "dtcn_logis.namTruoc as namTruoc, dtcn_logis.cungKyNgay as cungKyNgay, " +
            "dtcn_logis.cungKyNam as cungKyNam, dtcn_logis.ttNam as ttNam, dtcn_logis.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtcn_logis " +
            "WHERE dtcn_logis.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtcn_logis.dichVu = :dichVu) " +
            "AND (:maChiNhanh = '' OR :maChiNhanh is null OR dtcn_logis.maChiNhanh = :maChiNhanh) ")
    Slice<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("dichVu") String dichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT dtcn_logis.maChiNhanh as maChiNhanh, dtcn_logis.dichVu as dichVu, dtcn_logis.keHoach as keHoach, " +
            "dtcn_logis.thucHien as thucHien, dtcn_logis.tlht as tlHoanThanh, dtcn_logis.tienDo as tienDo, " +
            "dtcn_logis.ttThang as ttThang, dtcn_logis.ttTbnThang as ttTbnThang, dtcn_logis.thangTruoc as thangTruoc," +
            "dtcn_logis.namTruoc as namTruoc, dtcn_logis.cungKyNgay as cungKyNgay, " +
            "dtcn_logis.cungKyNam as cungKyNam, dtcn_logis.ttNam as ttNam, dtcn_logis.cungKyThang as cungKyThang " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtcn_logis " +
            "WHERE dtcn_logis.ngayBaoCao = :toTime " +
            "AND (:dichVu = '' OR :dichVu is null OR dtcn_logis.dichVu = :dichVu) " +
            "AND (:maChiNhanh = '' OR :maChiNhanh is null OR dtcn_logis.maChiNhanh = :maChiNhanh) " +
            "AND (dtcn_logis.maChiNhanh IN :listChiNhanhVeriable) ")
    Slice<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuChiNhanhV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("dichVu") String dichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT dtbc.tiLeTangTruongNgay as tiLeTangTruongNgay, dtbc.ngayBaoCao as ngayBaoCao, dtbc.dichVu as loaiDichVu " +
            "FROM TienDoDoanhThuLogisticChiNhanh dtbc " +
            "WHERE dtbc.ngayBaoCao >= :ngayBatDau AND dtbc.ngayBaoCao <= :ngayKetThuc " +
            "AND dtbc.maChiNhanh = :maChiNhanh ")
    List<BieuDoTtTienDoDoanhThuLogisticJPADto> dataBieuDoTienDoDoanhThuLogisticCN(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("maChiNhanh") String maChiNhanh
    );
}
