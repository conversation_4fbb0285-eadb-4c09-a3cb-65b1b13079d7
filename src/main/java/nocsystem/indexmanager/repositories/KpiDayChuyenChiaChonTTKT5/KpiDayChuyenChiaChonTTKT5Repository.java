package nocsystem.indexmanager.repositories.KpiDayChuyenChiaChonTTKT5;

import nocsystem.indexmanager.models.KpiDayChuyenChiaChon.KpiDayChuyenChiaChonEntity;
import nocsystem.indexmanager.models.Response.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface KpiDayChuyenChiaChonTTKT5Repository extends JpaRepository<KpiDayChuyenChiaChonEntity, Long> {

    @Query("SELECT new nocsystem.indexmanager.models.Response.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonResponse(kpi.stt, kpi.noiDung, kpi.kpi, kpi.ketQua) " +
            "FROM KpiDayChuyenChiaChonEntity kpi " +
            "WHERE kpi.ngayBaocao = :ngayBaoCao " +
            "ORDER BY kpi.stt ASC ")
    List<KpiDayChuyenChiaChonResponse> listAllKpiDayChuyenChiaChon(LocalDate ngayBaoCao);
}
