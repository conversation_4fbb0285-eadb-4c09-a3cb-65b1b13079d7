package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOriginDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOverViewDto;
import nocsystem.indexmanager.models.TongDoanhThu.TongDoanhThuCN;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuCNV1ResDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TongDoanhThuCNRepository extends PagingAndSortingRepository<TongDoanhThuCN, Long> {
    @Query("SELECT tdt_cn FROM TongDoanhThuCN tdt_cn " +
            "WHERE tdt_cn.maChiNhanh = :maCN " +
            "AND tdt_cn.ngayBaoCao = :toTime " +
            "AND tdt_cn.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by tdt_cn.maChiNhanh asc ")
    Page<TongDoanhThuCNV1ResDto> findTongDoanhThuCN(
            @Param("maCN") String maCN,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );


    @Query("SELECT tdt_cn FROM TongDoanhThuCN tdt_cn " +
            "WHERE tdt_cn.maChiNhanh = :maCN " +
            "AND tdt_cn.ngayBaoCao = :toTime " +
            "AND tdt_cn.maChiNhanh IN :listBuuCucVeriable " +
            "order by tdt_cn.maChiNhanh asc ")
    Page<TongDoanhThuCNV1ResDto> findTongDoanhThuCNV2(
            @Param("maCN") String maCN,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT tdt_cn.maChiNhanh as maChiNhanh, tdt_cn.tongDoanhThu as tongDoanhThu, tdt_cn.tlht as tlht, " +
            "tdt_cn.ttThang as ttThang, tdt_cn.ttNam as ttNam, tdt_cn.ttTbnThang as ttTbnThang, tdt_cn.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuCN tdt_cn " +
            "WHERE tdt_cn.ngayBaoCao = :toTime " +
            "AND tdt_cn.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by tdt_cn.maChiNhanh asc ")
    Page<TongDoanhThuCNV1ResDto> findTongDoanhThuCNAll(
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT tdt_cn.maChiNhanh as maChiNhanh, tdt_cn.tongDoanhThu as tongDoanhThu, tdt_cn.tlht as tlht, " +
            "tdt_cn.ttThang as ttThang, tdt_cn.ttNam as ttNam, tdt_cn.ttTbnThang as ttTbnThang, tdt_cn.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuCN tdt_cn " +
            "WHERE tdt_cn.ngayBaoCao = :toTime " +
            "AND (tdt_cn.maChiNhanh IN :listChiNhanhVeriable ) " +
            "order by tdt_cn.maChiNhanh asc ")
    Page<TongDoanhThuCNV1ResDto> findTongDoanhThuCNAllV2(
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT tdt_cn.ngayBaoCao as ngayBaoCao, tdt_cn.maChiNhanh as maChiNhanh, tdt_cn.tongDoanhThu as tongDoanhThu, " +
            "tdt_cn.tlht as tlht, tdt_cn.ttThang as ttThang, tdt_cn.ttNam as ttNam, tdt_cn.ttTbnThang as ttTbnThang, " +
            "tdt_cn.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuCN tdt_cn " +
            "WHERE tdt_cn.maChiNhanh = :maChiNhanh " +
            "AND tdt_cn.ngayBaoCao = :toTime")
    ThongKeTongDoanhThuBCV1ResDto findDoanhThuCN(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT tdt_cn.ngayBaoCao as ngayBaoCao, tdt_cn.maChiNhanh as maChiNhanh, tdt_cn.tongDoanhThu as tongDoanhThu, " +
            "tdt_cn.tlht as tlht, tdt_cn.ttThang as ttThang, tdt_cn.ttNam as ttNam, tdt_cn.ttTbnThang as ttTbnThang, " +
            "tdt_cn.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuCN tdt_cn " +
            "WHERE tdt_cn.maChiNhanh = :maChiNhanh " +
            "AND tdt_cn.ngayBaoCao = :toTime")
    CSKDTongDoanhThuOverViewDto findTongDoanhThuCNOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime
    );
}
