package nocsystem.indexmanager.repositories.TienDoDoanhThu;

import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.DataBieuDoTienDoDoanhThuDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOriginDto;
import nocsystem.indexmanager.models.TienDoDoanhThu.CSKDTienDoDoanhThu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuRepository extends JpaRepository<CSKDTienDoDoanhThu, Integer> {
    @Query("SELECT tddt.tiLeTangTruongNgay as tiLeTangTruongNgay, tddt.ngayBaoCao as ngayBaoCao, tddt.nhomDt as nhomDt " +
            "FROM CSKDTienDoDoanhThu tddt " +
            "WHERE tddt.ngayBaoCao <= :ngayKetThuc " +
            "AND tddt.ngayBaoCao >= :ngayBatDau " +
            "order by tddt.ngayBaoCao asc ")
    public List<DataBieuDoTienDoDoanhThuDto> dataBieuDoTienDoDoanhThu(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc);
}
