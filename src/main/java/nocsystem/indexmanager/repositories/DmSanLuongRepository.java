package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.DmSanLuong.DmSanLuongKho;
import nocsystem.indexmanager.models.DmSanLuong.LkSanLuongChiNhanh;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DmSanLuongRepository extends JpaRepository<DmSanLuongKho, Integer> {

    @Query("SELECT p.slDonLayNhanTC as slDonLayNhanTC," +
            " p.slDonDaPhanCongGiao as slDonDaPhanCongGiao ," +
            "p.slDonDaGiaoTC as slDonDaGiaoTC,  " +
            "p.slDonTonPhanCongNhan as slDonTonPhanCongNhan, " +
            "p.slDonLayTonNhan as slDonLayTonNhan, " +
            "p.slDonTonXuatTTHubSubBC as slDonTonXuatTTHubSubBC, " +
            "p.slDonTonXuatTTTTKT as slDonTonXuatTTTTKT, " +
            "p.slDonTonGiao as slDonTonGiao, " +
            "p.tenTinh as tenTinh, " +
            "p.maTinh as maTinh, " +
            "p.maBuuCuc as maBuuCuc, " +
            "p.ngayBaoCao as ngayBaoCao " +
            "FROM DmSanLuongKho p WHERE " +
            "(:maTinh is null or :maTinh = '' or p.maTinh = :maTinh) " +
            "and (:maBuuCuc is null or :maBuuCuc = '' or p.maBuuCuc = :maBuuCuc) " +
            "and p.ngayBaoCao = :ngayBaoCao")
    public List<DmSanLuongKhoResDto> searchKho(
            @Param("maTinh") String maTinh,
            @Param("maBuuCuc") String maBuuCuc, @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT p.maNhanVien as maNhanVien," +
            "p.maBuuCuc as maBuuCuc ," +
            "p.maChiNhanh as maChiNhanh,  " +
            "p.tongDonGiaoTC as tongDonGiaoTC, " +
            "p.tongDonDangGiao as tongDonDangGiao, " +
            "p.tongDonDelayGiaoHang as tongDonDelayGiaoHang, " +
            "p.tongDonNhanTC as tongDonNhanTC, " +
            "p.tongDonDangNhan as tongDonDangNhan, " +
            "p.tongDonDelayNhan as tongDonDelayNhan, " +
            "p.tongDonHuyNhan as tongDonHuyNhan, " +
            "p.ngayBaoCao as ngayBaoCao " +
            "FROM DmSanLuongBuuTa p WHERE " +
            "(:maNhanVien is null or :maNhanVien = '' or p.maNhanVien = :maNhanVien) " +
            "and (:maChiNhanh is null or :maChiNhanh = '' or p.maChiNhanh = :maChiNhanh) " +
            "and (:maBuuCuc is null or :maBuuCuc = '' or p.maBuuCuc = :maBuuCuc)" +
            "and p.ngayBaoCao = :ngayBaoCao ")
    public List<DmSanLuongBuuTaResDto> searchBuuTa(
            @Param("maNhanVien") String maNhanVien,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc, @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT p.updateAt as updateAt ," +
            "p.maNhanVien as maNhanVien," +
            "p.maBuuCuc as maBuuCuc ," +
            "p.maChiNhanh as maChiNhanh,  " +
            "p.tongDonGiaoTC as tongDonGiaoTC, " +
            "p.tongDonNhanTC as tongDonNhanTC, " +
            "p.tongDonDangNhan as tongDonDangNhan, " +
            "p.tongDonDelayNhan as tongDonDelayNhan, " +
            "p.tongDonHuyNhan as tongDonHuyNhan " +
            "FROM LKSanLuongBuuTa p WHERE " +
            "(:maNhanVien is null or :maNhanVien = '' or p.maNhanVien = :maNhanVien) " +
            "and (:maChiNhanh is null or :maChiNhanh = '' or p.maChiNhanh = :maChiNhanh) " +
            "and (:maBuuCuc is null or :maBuuCuc = '' or p.maBuuCuc = :maBuuCuc) " +
            "and p.updateAt = :ngayBaoCao ")
    public List<LkSanLuongBuuTaResDto> searchBuuTaLk(
            @Param("maNhanVien") String maNhanVien,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc, @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT p.updateAt as updateAt ," +
            "p.slDonLayNhanTC as slDonLayNhanTC," +
            "p.slDonDaGiaoTC as slDonDaGiaoTC,  " +
            "p.slDonTonPhanCongNhan as slDonTonPhanCongNhan, " +
            "p.slDonLayTonNhan as slDonLayTonNhan, " +
            "p.slDonTonXuatTTHubSubBC as slDonTonXuatTTHubSubBC, " +
            "p.slDonTonXuatTTTTKT as slDonTonXuatTTTTKT, " +
            "p.tenTinh as tenTinh, " +
            "p.maTinh as maTinh, " +
            "p.maBuuCuc as maBuuCuc " +
            "FROM LKSanLuongKho p WHERE " +
            "(:maTinh is null or :maTinh = '' or p.maTinh = :maTinh) " +
            "and (:maBuuCuc is null or :maBuuCuc = '' or p.maBuuCuc = :maBuuCuc) " +
            "and p.updateAt = :ngayBaoCao")
    public List<LkSanLuongKhoResDto> searchKhoLk(
            @Param("maTinh") String maTinh,
            @Param("maBuuCuc") String maBuuCuc, @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT " +
            "p.slDonLayNhanTC as slDonLayNhanTC, " +
            "p.slDonDaGiaoTC as slDonDaGiaoTC,  " +
            "p.slDonTonPhanCongNhan as slDonTonPhanCongNhan, " +
            "p.slDonLayTonNhan as slDonLayTonNhan, " +
            "p.slDonTonXuatTTHubSubBC as slDonTonXuatTTHubSubBC, " +
            "p.slDonTonXuatTTTTKT as slDonTonXuatTTTTKT, p.maChiNhanh as maChiNhanh, " +
            "p.tenTinh as tenTinh,p.updateAt as updateAt "+
            "FROM LkSanLuongChiNhanh p where p.maChiNhanh = :maTinh and p.updateAt = :ngayBaoCao")
    public List<LkSanLuongChiNhanhResDto> searchChiNhanhLk(
            @Param("maTinh") String maTinh,
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT p.slDonLayNhanTC as slDonLayNhanTC," +
            " p.slDonDaPhanCongGiao as slDonDaPhanCongGiao ," +
            "p.slDonDaGiaoTC as slDonDaGiaoTC,  " +
            "p.slDonTonPhanCongNhan as slDonTonPhanCongNhan, " +
            "p.slDonLayTonNhan as slDonLayTonNhan, " +
            "p.slDonTonXuatTTHubSubBC as slDonTonXuatTTHubSubBC, " +
            "p.slDonTonXuatTTTTKT as slDonTonXuatTTTTKT, " +
            "p.slDonTonGiao as slDonTonGiao, " +
            "p.tenTinh as tenTinh, " +
            "p.maChiNhanh as maChiNhanh, " +
            "p.ngayBaoCao as ngayBaoCao " +
            "FROM DmSanLuongChiNhanh p WHERE " +
            "p.maChiNhanh = :maTinh and p.ngayBaoCao = :ngayBaoCao")
    public List<DmSanLuongChiNhanhTResDto> searchKhoChiNhanhTheoNgay(
            @Param("maTinh") String maTinh,
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

}

