package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeCNHaNoiAndHCMResDto;
import nocsystem.indexmanager.models.ThongKeLuyKe.TienDoLuyKeTCTCN;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoLuyKeCNHaNoiAndHCMRepository extends JpaRepository<TienDoLuyKeTCTCN, Long>, PagingAndSortingRepository<TienDoLuyKeTCTCN, Long> {

    @Query("SELECT tdLuyKeCNHaNoi.ngayBaoCao as ngay<PERSON><PERSON><PERSON><PERSON>, tdLuyKeCNHaNoi.chiNhanh as chi<PERSON><PERSON><PERSON>, tdLuyKeCNHaNoi.buuCuc as buuCuc, " +
            "tdLuyKeCNHaNoi.mien as mien, tdLuyKeCNHaNoi.tlht as tlht, tdLuyKeCNHaNoi.tienDo as tienDo, " +
            "tdLuyKeCNHaNoi.ttThang as ttThang, tdLuyKeCNHaNoi.ttTbnThang as ttTbnThang," +
            "tdLuyKeCNHaNoi.ttNam as ttNam, tdLuyKeCNHaNoi.ttTbnNam as ttTbnNam, tdLuyKeCNHaNoi.khthang as khthang, " +
            "tdLuyKeCNHaNoi.lkthang as luyKeThang, tdLuyKeCNHaNoi.khthang as keHoach, tdLuyKeCNHaNoi.danhgia as danhgia, " +
            "tdLuyKeCNHaNoi.tienDoNgay as tienDoNgay, tdLuyKeCNHaNoi.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeCNHaNoi.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeCNHaNoi.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeCNHaNoiAndHCM tdLuyKeCNHaNoi " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeCNHaNoi.buuCuc = :buuCuc) " +
            "AND tdLuyKeCNHaNoi.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeCNHaNoi.chiNhanh = 'HNI' " +
            "ORDER BY tdLuyKeCNHaNoi.buuCuc asc ")
    Page<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHaNoi(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("buuCuc") String buuCuc,
            Pageable pageable);

    @Query("SELECT tdLuyKeCNHaNoi.ngayBaoCao as ngayBaoCao, tdLuyKeCNHaNoi.chiNhanh as chiNhanh, tdLuyKeCNHaNoi.buuCuc as buuCuc, " +
            "tdLuyKeCNHaNoi.mien as mien, tdLuyKeCNHaNoi.tlht as tlht, tdLuyKeCNHaNoi.tienDo as tienDo, " +
            "tdLuyKeCNHaNoi.ttThang as ttThang, tdLuyKeCNHaNoi.ttTbnThang as ttTbnThang," +
            "tdLuyKeCNHaNoi.ttNam as ttNam, tdLuyKeCNHaNoi.ttTbnNam as ttTbnNam, tdLuyKeCNHaNoi.khthang as khthang, " +
            "tdLuyKeCNHaNoi.lkthang as luyKeThang, tdLuyKeCNHaNoi.khthang as keHoach, tdLuyKeCNHaNoi.danhgia as danhgia, " +
            "tdLuyKeCNHaNoi.tienDoNgay as tienDoNgay, tdLuyKeCNHaNoi.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeCNHaNoi.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeCNHaNoi.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeCNHaNoiAndHCM tdLuyKeCNHaNoi " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeCNHaNoi.buuCuc = :buuCuc) " +
            "AND tdLuyKeCNHaNoi.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeCNHaNoi.chiNhanh = 'HNI' " +
            "AND tdLuyKeCNHaNoi.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY tdLuyKeCNHaNoi.buuCuc asc ")
    Page<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHaNoiV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable);

    @Query("SELECT tdLuyKeCNHCM.ngayBaoCao as ngayBaoCao, tdLuyKeCNHCM.chiNhanh as chiNhanh, tdLuyKeCNHCM.buuCuc as buuCuc, " +
            "tdLuyKeCNHCM.mien as mien, tdLuyKeCNHCM.tlht as tlht, tdLuyKeCNHCM.tienDo as tienDo, " +
            "tdLuyKeCNHCM.ttThang as ttThang, tdLuyKeCNHCM.ttTbnThang as ttTbnThang," +
            "tdLuyKeCNHCM.ttNam as ttNam, tdLuyKeCNHCM.ttTbnNam as ttTbnNam, " +
            "tdLuyKeCNHCM.danhgia as danhgia, tdLuyKeCNHCM.khthang as keHoach, tdLuyKeCNHCM.lkthang as luyKeThang, " +
            "tdLuyKeCNHCM.tienDoNgay as tienDoNgay, tdLuyKeCNHCM.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeCNHCM.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeCNHCM.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeCNHaNoiAndHCM tdLuyKeCNHCM " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeCNHCM.buuCuc = :buuCuc) " +
            "AND tdLuyKeCNHCM.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeCNHCM.chiNhanh = 'HCM' " +
            "ORDER BY tdLuyKeCNHCM.buuCuc asc ")
    Page<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHCM(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("buuCuc") String buuCuc,
            Pageable pageable);

    @Query("SELECT tdLuyKeCNHCM.ngayBaoCao as ngayBaoCao, tdLuyKeCNHCM.chiNhanh as chiNhanh, tdLuyKeCNHCM.buuCuc as buuCuc, " +
            "tdLuyKeCNHCM.mien as mien, tdLuyKeCNHCM.tlht as tlht, tdLuyKeCNHCM.tienDo as tienDo, " +
            "tdLuyKeCNHCM.ttThang as ttThang, tdLuyKeCNHCM.ttTbnThang as ttTbnThang," +
            "tdLuyKeCNHCM.ttNam as ttNam, tdLuyKeCNHCM.ttTbnNam as ttTbnNam, " +
            "tdLuyKeCNHCM.danhgia as danhgia, tdLuyKeCNHCM.khthang as keHoach, tdLuyKeCNHCM.lkthang as luyKeThang, " +
            "tdLuyKeCNHCM.tienDoNgay as tienDoNgay, tdLuyKeCNHCM.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeCNHCM.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeCNHCM.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeCNHaNoiAndHCM tdLuyKeCNHCM " +
            "WHERE (:buuCuc is null or :buuCuc = '' or tdLuyKeCNHCM.buuCuc = :buuCuc) " +
            "AND tdLuyKeCNHCM.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeCNHCM.chiNhanh = 'HCM' " +
            "AND tdLuyKeCNHCM.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY tdLuyKeCNHCM.buuCuc asc ")
    Page<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHCMV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable);
}
