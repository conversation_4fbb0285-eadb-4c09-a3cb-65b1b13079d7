package nocsystem.indexmanager.repositories.Grab;

import nocsystem.indexmanager.models.Grab.GrabReconciliation;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.GrabReconciliationByFileIdDto;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.HistoryImportExcelGrabDto;
import nocsystem.indexmanager.models.TienDoDoanhThu.CSKDTienDoDoanhThu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GrabReconciliationRepository extends JpaRepository<GrabReconciliation, Integer> {
//    @Query("SELECT new GrabReconciliation(Grab.createdAt, Grab.maVanDon, Grab.trangThai, Grab.tongTien, Grab.cuocChuyenDi, " +
//            "Grab.phuPhi, Grab.chietKhau, Grab.fileId) " +
//            "FROM GrabReconciliation Grab " +
//            "WHERE Grab.fileId = :fileId ")
//    public List<GrabReconciliation> getGrabReconciliationByFileId(
//            @Param("fileId") Integer fileId
//    );
}
