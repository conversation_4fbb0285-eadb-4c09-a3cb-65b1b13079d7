package nocsystem.indexmanager.repositories.Grab;

import nocsystem.indexmanager.models.Grab.GrabReconciliation;
import nocsystem.indexmanager.models.Grab.ImportExelReconciliation;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.HistoryImportExcelGrabDto;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.ImportExcelGrabDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.DataBieuDoTienDoDoanhThuDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ImportExelReconciliationRepository extends JpaRepository<ImportExelReconciliation, Integer> {
    @Query("SELECT IER.fileName as fileName, IER.directory as directory " +
            "FROM ImportExelReconciliation IER " +
            "WHERE IER.fileName = :fileName " +
            "AND IER.createdBy = :userName " +
            "AND IER.typeUpload = :typeUpload ORDER BY IER.id DESC")
    public List<ImportExcelGrabDto> dowloadUploadFileGrab(
            @Param("fileName") String fileName,
            @Param("typeUpload") Short typeUpload,
            @Param("userName") String userName
    );

    @Query("SELECT IER.id as id, IER.typeUpload as typeUpload, IER.createdAt as createdAt, IER.createdBy as createdBy, " +
            "IER.fileName as fileName, IER.directory as directory " +
            "FROM ImportExelReconciliation IER " +
            "WHERE IER.createdBy = :userName " +
            "AND (IER.typeUpload = :typeUpload ) " +
            "AND (IER.fileName = :fileName )" +
            "ORDER BY IER.id DESC ")
    public List<HistoryImportExcelGrabDto> historyUploadFileGrab(
            @Param("userName") String userName,
            @Param("typeUpload") short typeUpload,
            @Param("fileName") String fileName
    );

    @Query("SELECT IER.id as id, IER.typeUpload as typeUpload, IER.createdAt as createdAt, IER.createdBy as createdBy, " +
            "IER.fileName as fileName, IER.directory as directory " +
            "FROM ImportExelReconciliation IER " +
            "WHERE IER.createdBy = :userName " +
            "AND (IER.typeUpload = :typeUpload ) " +
            "AND (IER.fileName = :fileName )" +
            "ORDER BY IER.id DESC")
    public List<HistoryImportExcelGrabDto> reconciliationUploadSuccess(
            @Param("userName") String userName,
            @Param("typeUpload") short typeUpload,
            @Param("fileName") String fileName
    );

    @Query("SELECT IER.id as id, IER.typeUpload as typeUpload, IER.createdAt as createdAt, IER.createdBy as createdBy, " +
            "IER.fileName as fileName, IER.directory as directory, IER.dataSync as dataSyn " +
            "FROM ImportExelReconciliation IER " +
            "WHERE IER.dataSync = :dataSync " +
            "AND (IER.typeUpload = :typeUpload ) " +
            "ORDER BY IER.id DESC ")
    public List<HistoryImportExcelGrabDto> getNotSaveDataFile(
            @Param("dataSync") short dataSync,
            @Param("typeUpload") short typeUpload
    );

    @Query("SELECT IER.typeUpload as typeUpload, IER.createdAt as createdAt, IER.createdBy as createdBy, " +
            "IER.fileName as fileName, IER.directory as directory, IER.dataSync as dataSyn " +
            "FROM ImportExelReconciliation IER " +
            "WHERE IER.id = :fileId " +
            "AND IER.typeUpload = :typeUpload ")
    public List<HistoryImportExcelGrabDto> getNotSaveDataFileV2(
            @Param("fileId") Integer fileId,
            @Param("typeUpload") short typeUpload
    );

    @Query("SELECT new nocsystem.indexmanager.models.Grab.ImportExelReconciliation(IER.id,  IER.fileName,  IER.directory," +
            "IER.createdBy,  IER.createdAt, IER.typeUpload, IER.dataSync, IER.updatedAt) " +
            "FROM ImportExelReconciliation IER " +
            "WHERE IER.id = :fileId ")
    public ImportExelReconciliation getImportFileInformById(
            @Param("fileId") Integer fileId
    );
}
