package nocsystem.indexmanager.repositories.Grab;

import nocsystem.indexmanager.models.Grab.GrabDoiSoat;
import nocsystem.indexmanager.models.Grab.TongHopDoiSoatGrap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TongHopDoiSoatGrapRepository extends JpaRepository<TongHopDoiSoatGrap, Integer> {
//    @Query("SELECT new nocsystem.indexmanager.models.Grab.TongHopDoiSoatGrap(doiSoat.maChiNhanh, doiSoat.ngayNhapMay, doiSoat.ngayPhatThanhCong, doiSoat.chiNhanh, doiSoat.maBuuCuc, doiSoat.buuCuc," +
//            "doiSoat.vtpSoLuongDon, doiSoat.vtpTongTien, doiSoat.vtpCuocChieuDi, doiSoat.vtpPhuPhi, doiSoat.vtpChietKhau, doiSoat.grabSoLuongDon, doiSoat.grabTongTien, doiSoat.grabCuocChieuDi," +
//            "doiSoat.grabPhuPhi, doiSoat.grabChieuKhau, doiSoat.chenhLechTongTien, doiSoat.chenhLechChietKhau, doiSoat.ghiChu, doiSoat.fileId) " +
//            "FROM TongHopDoiSoatGrap doiSoat " +
//            "WHERE doiSoat.ngayPhatThanhCong >= :ngayPhatThanhCongFrom " +
//            "AND doiSoat.ngayPhatThanhCong <= :ngayPhatThanhCongTo " +
//            "AND (COALESCE(:chiNhanh,NULL) IS NULL OR doiSoat.maChiNhanh in (:chiNhanh)) " +
//            "AND (COALESCE(:buuCuc,NULL) IS NULL OR doiSoat.maBuuCuc in (:buuCuc)) "
//    )
//    public List<TongHopDoiSoatGrap> getFilterReconciliation(
//            @Param("ngayPhatThanhCongTo") LocalDate ngayPhatThanhCongTo,
//            @Param("ngayPhatThanhCongFrom") LocalDate ngayPhatThanhCongFrom,
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc
//    );

//    @Query("SELECT new nocsystem.indexmanager.models.Grab.TongHopDoiSoatGrap(" +
//            "doiSoat.maChiNhanh, doiSoat.ngayNhapMay, doiSoat.ngayPhatThanhCong, " +
//            "doiSoat.chiNhanh, doiSoat.maBuuCuc, doiSoat.buuCuc," +
//            "doiSoat.vtpSoLuongDon, doiSoat.vtpTongTien, doiSoat.vtpCuocChieuDi, doiSoat.vtpPhuPhi, doiSoat.vtpChietKhau, " +
//            "doiSoat.grabSoLuongDon, doiSoat.grabTongTien, doiSoat.grabCuocChieuDi, doiSoat.grabPhuPhi, doiSoat.grabChieuKhau, " +
//            "doiSoat.chenhLechTongTien, doiSoat.chenhLechChietKhau, doiSoat.ghiChu, doiSoat.fileId) " +
//            "FROM TongHopDoiSoatGrap doiSoat " +
//            "WHERE doiSoat.ngayPhatThanhCong >= :ngayPhatThanhCongFrom " +
//            "AND doiSoat.ngayPhatThanhCong <= :ngayPhatThanhCongTo " +
//            "AND (COALESCE(:chiNhanh,NULL) IS NULL OR doiSoat.maChiNhanh in (:chiNhanh)) " +
//            "AND (COALESCE(:buuCuc,NULL) IS NULL OR doiSoat.maBuuCuc in (:buuCuc)) "
//    )
//    public List<TongHopDoiSoatGrap> getFilterReconciliationV2(
//            @Param("ngayPhatThanhCongTo") LocalDate ngayPhatThanhCongTo,
//            @Param("ngayPhatThanhCongFrom") LocalDate ngayPhatThanhCongFrom,
//            @Param("chiNhanh") List<String> chiNhanh,
//            @Param("buuCuc") List<String> buuCuc
//    );
}
