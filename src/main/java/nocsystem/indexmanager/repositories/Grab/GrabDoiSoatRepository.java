package nocsystem.indexmanager.repositories.Grab;

import nocsystem.indexmanager.models.Grab.GrabDoiSoat;
import nocsystem.indexmanager.models.Grab.TongHopDoiSoatGrap;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.GrabDoiSoatByMaVanDonDto;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.GrabDoiSoatChiTietExcelDisplay;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.TongHopDoiSoatGrapResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public interface GrabDoiSoatRepository extends JpaRepository<GrabDoiSoat, Integer> {
    @Query("SELECT new nocsystem.indexmanager.models.Grab.GrabDoiSoat(" +
            "doiSoat.maVanDon, doiSoat.ngayNhapMay, doiSoat.ngayPhatThanhCong, doiSoat.orderId, doiSoat.khoangCach, doiSoat.maChiNhanhGoc," +
            "doiSoat.tenChiNhanhGoc, doiSoat.maBuuCucGoc, doiSoat.tenBuuCucGoc, doiSoat.maKhachHangGoc, doiSoat.tenKhachHangGoc, " +
            "doiSoat.maChiNhanhPhat, doiSoat.tenChiNhanhPhat, doiSoat.maBuuCucPhat, doiSoat.tenBuuCucPhat, doiSoat.tenKhachHangNhan," +
            "doiSoat.vung, doiSoat.maDichVuVietTel, doiSoat.trongLuong, doiSoat.vtpTrangThaiCuoiCung, doiSoat.vtpTongTien, " +
            "doiSoat.vtpCuocChieuDi, doiSoat.vtpPhuPhi, doiSoat.vtpChietKhau, doiSoat.grabTrangThai, doiSoat.grabTongTien, " +
            "doiSoat.grabCuocChieuDi, doiSoat.grabPhuPhi, doiSoat.grabChieuKhau, doiSoat.chenhLechTongTien, doiSoat.chenhLechCuocChieuDi," +
            "doiSoat.chenhLechPhuPhi, doiSoat.chenhLechChietKhau, doiSoat.ghiChu, doiSoat.fileId ) " +
            "FROM GrabDoiSoat doiSoat " +
            "WHERE doiSoat.maVanDon IN :listDonDoiSoat ")
    public List<GrabDoiSoat> getGrabReconciliationByFileIdV2(
            @Param("listDonDoiSoat") List<String> listDonDoiSoat
    );

    @Query("SELECT new nocsystem.indexmanager.models.Grab.GrabDoiSoat(" +
            "doiSoat.maVanDon, doiSoat.ngayNhapMay, doiSoat.ngayPhatThanhCong, doiSoat.orderId, doiSoat.khoangCach, doiSoat.maChiNhanhGoc," +
            "doiSoat.tenChiNhanhGoc, doiSoat.maBuuCucGoc, doiSoat.tenBuuCucGoc, doiSoat.maKhachHangGoc, doiSoat.tenKhachHangGoc, " +
            "doiSoat.maChiNhanhPhat, doiSoat.tenChiNhanhPhat, doiSoat.maBuuCucPhat, doiSoat.tenBuuCucPhat, doiSoat.tenKhachHangNhan," +
            "doiSoat.vung, doiSoat.maDichVuVietTel, doiSoat.trongLuong, doiSoat.vtpTrangThaiCuoiCung, doiSoat.vtpTongTien, " +
            "doiSoat.vtpCuocChieuDi, doiSoat.vtpPhuPhi, doiSoat.vtpChietKhau, doiSoat.grabTrangThai, doiSoat.grabTongTien, " +
            "doiSoat.grabCuocChieuDi, doiSoat.grabPhuPhi, doiSoat.grabChieuKhau, doiSoat.chenhLechTongTien, doiSoat.chenhLechCuocChieuDi," +
            "doiSoat.chenhLechPhuPhi, doiSoat.chenhLechChietKhau, doiSoat.ghiChu, doiSoat.fileId ) " +
            "FROM GrabDoiSoat doiSoat " +
            "WHERE date(doiSoat.ngayPhatThanhCong) < :ngayPhatThanhCongTo " +
            "AND date(doiSoat.ngayPhatThanhCong) >= :ngayPhatThanhCongFrom " +
            "AND (COALESCE(:tinhKhachHangGui,NULL) IS NULL OR doiSoat.maChiNhanhGoc IN :tinhKhachHangGui) " +
            "AND (COALESCE(:tinhKhachHangNhan,NULL) IS NULL OR doiSoat.maChiNhanhPhat IN :tinhKhachHangNhan) " +
            "AND (:maVanDon is null OR :maVanDon = '' OR doiSoat.maVanDon = :maVanDon )"
    )
    public Page<GrabDoiSoat> getFilterReconciliation(
            @Param("ngayPhatThanhCongFrom") LocalDateTime ngayPhatThanhCongFrom,
            @Param("ngayPhatThanhCongTo") LocalDateTime ngayPhatThanhCongTo,
            @Param("tinhKhachHangGui") List<String> tinhKhachHangGui,
            @Param("tinhKhachHangNhan") List<String> tinhKhachHangNhan,
            @Param("maVanDon") String maVanDon,
            Pageable paging
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.ReconciliationGrab.TongHopDoiSoatGrapResponse(" +
            "doiSoat.maChiNhanhGoc, doiSoat.tenChiNhanhGoc, doiSoat.maBuuCucGoc, doiSoat.tenBuuCucGoc, " +
            "count (doiSoat.vtpCuocChieuDi), sum(doiSoat.vtpTongTien), sum(doiSoat.vtpCuocChieuDi), sum(doiSoat.vtpPhuPhi), sum(doiSoat.vtpChietKhau), " +
            "count(doiSoat.grabCuocChieuDi), sum(doiSoat.grabTongTien), sum(doiSoat.grabCuocChieuDi), sum(doiSoat.grabPhuPhi), sum(doiSoat.grabChieuKhau)," +
            "(count (doiSoat.vtpCuocChieuDi) - count(doiSoat.grabCuocChieuDi) ), (sum(doiSoat.vtpTongTien) - sum(doiSoat.grabTongTien)), " +
            "(sum(doiSoat.vtpChietKhau) - sum(doiSoat.grabChieuKhau))) " +
            "FROM GrabDoiSoat doiSoat " +
            "WHERE date(doiSoat.ngayPhatThanhCong) >= :ngayPhatThanhCongFrom " +
            "AND date(doiSoat.ngayPhatThanhCong) < :ngayPhatThanhCongTo " +
            "AND (COALESCE(:maChiNhanhGoc,NULL) IS NULL OR doiSoat.maChiNhanhGoc IN :maChiNhanhGoc) " +
            "AND (COALESCE(:maBuuCucGoc,NULL) IS NULL OR doiSoat.maBuuCucGoc IN :maBuuCucGoc) " +
            "GROUP BY doiSoat.maChiNhanhGoc, doiSoat.tenChiNhanhGoc, doiSoat.maBuuCucGoc, doiSoat.tenBuuCucGoc")
    public Page<TongHopDoiSoatGrapResponse> getFilterReconciliationV3(
            @Param("ngayPhatThanhCongFrom") LocalDate ngayPhatThanhCongFrom,
            @Param("ngayPhatThanhCongTo") LocalDateTime ngayPhatThanhCongTo,
            @Param("maChiNhanhGoc") List<String> maChiNhanhGoc,
            @Param("maBuuCucGoc") List<String> maBuuCucGoc,
            Pageable paging
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.ReconciliationGrab.TongHopDoiSoatGrapResponse(" +
            "doiSoat.maChiNhanhGoc, doiSoat.tenChiNhanhGoc, doiSoat.maBuuCucGoc, doiSoat.tenBuuCucGoc, " +
            "count (doiSoat.vtpCuocChieuDi), sum(doiSoat.vtpTongTien), sum(doiSoat.vtpCuocChieuDi), sum(doiSoat.vtpPhuPhi), sum(doiSoat.vtpChietKhau), " +
            "count(doiSoat.grabCuocChieuDi), sum(doiSoat.grabTongTien), sum(doiSoat.grabCuocChieuDi), sum(doiSoat.grabPhuPhi), sum(doiSoat.grabChieuKhau)," +
            "(count (doiSoat.vtpCuocChieuDi) - count(doiSoat.grabCuocChieuDi) ), (sum(doiSoat.vtpTongTien) - sum(doiSoat.grabTongTien)), " +
            "(sum(doiSoat.vtpChietKhau) - sum(doiSoat.grabChieuKhau))) " +
            "FROM GrabDoiSoat doiSoat " +
            "WHERE date(doiSoat.ngayPhatThanhCong) >= :ngayPhatThanhCongFrom " +
            "AND date(doiSoat.ngayPhatThanhCong) < :ngayPhatThanhCongTo " +
            "AND (COALESCE(:maChiNhanhGoc,NULL) IS NULL OR doiSoat.maChiNhanhGoc IN :maChiNhanhGoc) " +
            "AND (COALESCE(:maBuuCucGoc,NULL) IS NULL OR doiSoat.maBuuCucGoc IN :maBuuCucGoc) " +
            "GROUP BY doiSoat.maChiNhanhGoc, doiSoat.tenChiNhanhGoc, doiSoat.maBuuCucGoc, doiSoat.tenBuuCucGoc")
    public List<TongHopDoiSoatGrapResponse> getFilterReconciliationTotal(
            @Param("ngayPhatThanhCongFrom") LocalDate ngayPhatThanhCongFrom,
            @Param("ngayPhatThanhCongTo") LocalDateTime ngayPhatThanhCongTo,
            @Param("maChiNhanhGoc") List<String> maChiNhanhGoc,
            @Param("maBuuCucGoc") List<String> maBuuCucGoc
    );

    // 2023-06-28 ngocnt add query for export excel detail grab
    @Query("SELECT new nocsystem.indexmanager.models.Response.ReconciliationGrab.GrabDoiSoatChiTietExcelDisplay(" +
            "doiSoat.maVanDon, TO_CHAR(doiSoat.ngayNhapMay,'YYYY-MM-DD HH24:MI:SS'), TO_CHAR(doiSoat.ngayPhatThanhCong,'YYYY-MM-DD HH24:MI:SS'), doiSoat.orderId, doiSoat.khoangCach, doiSoat.maChiNhanhGoc," +
            "doiSoat.tenChiNhanhGoc, doiSoat.maBuuCucGoc, doiSoat.tenBuuCucGoc, doiSoat.maKhachHangGoc, doiSoat.tenKhachHangGoc, " +
            "doiSoat.maChiNhanhPhat, doiSoat.tenChiNhanhPhat, doiSoat.maBuuCucPhat, doiSoat.tenBuuCucPhat, doiSoat.tenKhachHangNhan," +
            "doiSoat.vung, doiSoat.maDichVuVietTel, doiSoat.trongLuong, doiSoat.vtpTrangThaiCuoiCung, doiSoat.vtpTongTien, " +
            "doiSoat.vtpCuocChieuDi, doiSoat.vtpPhuPhi, doiSoat.vtpChietKhau, doiSoat.grabTrangThai, doiSoat.grabTongTien, " +
            "doiSoat.grabCuocChieuDi, doiSoat.grabPhuPhi, doiSoat.grabChieuKhau, doiSoat.chenhLechTongTien, doiSoat.chenhLechCuocChieuDi," +
            "doiSoat.chenhLechPhuPhi, doiSoat.chenhLechChietKhau, doiSoat.ghiChu, doiSoat.fileId ) " +
            "FROM GrabDoiSoat doiSoat " +
            "WHERE date(doiSoat.ngayPhatThanhCong) < :ngayPhatThanhCongTo " +
            "AND date(doiSoat.ngayPhatThanhCong) >= :ngayPhatThanhCongFrom " +
            "AND (COALESCE(:tinhKhachHangGui,NULL) IS NULL OR doiSoat.maChiNhanhGoc IN :tinhKhachHangGui) " +
            "AND (COALESCE(:tinhKhachHangNhan,NULL) IS NULL OR doiSoat.maChiNhanhPhat IN :tinhKhachHangNhan) " +
            "AND (:maVanDon is null OR :maVanDon = '' OR doiSoat.maVanDon = :maVanDon )"
    )
    List<GrabDoiSoatChiTietExcelDisplay> findAllList(@Param("ngayPhatThanhCongFrom") LocalDate ngayPhatThanhCongFrom,
                                                     @Param("ngayPhatThanhCongTo") LocalDateTime ngayPhatThanhCongTo,
                                                     @Param("tinhKhachHangGui") List<String> tinhKhachHangGui,
                                                     @Param("tinhKhachHangNhan") List<String> tinhKhachHangNhan,
                                                     @Param("maVanDon") String maVanDon);

}
