package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuDetailScreenDto;
import nocsystem.indexmanager.models.ThongKeTongDoanhThu.ThongKeTongDoanhThuBC;
import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuNCV1ResDto;
import nocsystem.indexmanager.models.ThongKeTongDoanhThu.ThongKeTongDoanhThuCN;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ThongKeTongDoanhThuCNRepository extends PagingAndSortingRepository<ThongKeTongDoanhThuCN, Long> {
    @Query("SELECT tdt_cn.ngayBaoCao as ngayBaoCao, tdt_cn.tongDoanhThu as tongDoanhThu, " +
            "tdt_cn.tlht as tlht, tdt_cn.ttThang as ttThang, tdt_cn.ttNam as ttNam, tdt_cn.ttTbnThang as ttTbnThang, " +
            "tdt_cn.ttTbnNam " +
            "FROM ThongKeTongDoanhThuCN tdt_cn " +
            "WHERE tdt_cn.ngayBaoCao = :toTime")
    ThongKeTongDoanhThuNCV1ResDto findThongKeTongDoanhThuCN(
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT tdt_nc.ngayBaoCao as ngayBaoCao, tdt_nc.tongDoanhThu as tongDoanhThu, " +
            "tdt_nc.tlht as tlht, tdt_nc.ttThang as ttThang, tdt_nc.ttNam as ttNam, tdt_nc.ttTbnThang as ttTbnThang, " +
            "tdt_nc.ttTbnNam as ttTbnNam " +
            "FROM ThongKeTongDoanhThuCN tdt_nc " +
            "WHERE tdt_nc.ngayBaoCao = :toTime")
    TienDoDoanhThuCNV1ResDto findTongDoanhThuCuaChiNhanh(
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT tdt_nc.ngayBaoCao as ngayBaoCao, tdt_nc.tongDoanhThu as tongDoanhThu, " +
            "tdt_nc.tlht as tlht, tdt_nc.ttThang as ttThang, tdt_nc.ttNam as ttNam, tdt_nc.ttTbnThang as ttTbnThang, " +
            "tdt_nc.ttTbnNam as ttTbnNam, tdt_nc.keHoach as keHoach, " +
            "tdt_nc.cungKyNgay as cungKyNgay, tdt_nc.cungKyThang as cungKyThang, tdt_nc.cungKyNam as cungKyNam, " +
            "tdt_nc.thangTruoc as thangTruoc, tdt_nc.namTruoc as namTruoc " +
            "FROM TongDoanhThuCN tdt_nc " +
            "WHERE tdt_nc.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR tdt_nc.maChiNhanh = :maChiNhanh) ")
    Slice<TongDoanhThuDetailScreenDto> findThongKeTongDoanhThuCNAll(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            Pageable paging
    );

    @Query("SELECT tdt_nc.ngayBaoCao as ngayBaoCao, tdt_nc.tongDoanhThu as tongDoanhThu, " +
            "tdt_nc.tlht as tlht, tdt_nc.ttThang as ttThang, tdt_nc.ttNam as ttNam, tdt_nc.ttTbnThang as ttTbnThang, " +
            "tdt_nc.ttTbnNam as ttTbnNam, tdt_nc.keHoach as keHoach, " +
            "tdt_nc.cungKyNgay as cungKyNgay, tdt_nc.cungKyThang as cungKyThang, tdt_nc.cungKyNam as cungKyNam, " +
            "tdt_nc.thangTruoc as thangTruoc, tdt_nc.namTruoc as namTruoc " +
            "FROM TongDoanhThuCN tdt_nc " +
            "WHERE tdt_nc.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR tdt_nc.maChiNhanh = :maChiNhanh) " +
            "AND tdt_nc.maChiNhanh IN :listChiNhanhVeriable")
    Slice<TongDoanhThuDetailScreenDto> findThongKeTongDoanhThuCNAllV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            Pageable paging,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );
}
