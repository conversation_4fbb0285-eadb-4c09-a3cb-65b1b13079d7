package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuJPADto;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.DmChatLuongKhoResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.CSKDTienDoDoanhThuCPOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;
import nocsystem.indexmanager.models.TienDoDoanhThuChuyenPhat.TienDoDoanhThuCP;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuCPRepository extends JpaRepository<TienDoDoanhThuCP, Long> {
    @Query("SELECT dtcp.ngayBaoCao as ngayBaoCao, dtcp.dichVu as loaiDichVu, dtcp.tlHoanThanh as tlHoanThanh, " +
            "dtcp.tienDo as tienDo, dtcp.ttThang as ttThang, dtcp.ttTbnThang as ttTbnThang, dtcp.ttNam as ttNam, " +
            "dtcp.keHoach as keHoach, dtcp.thucHien as thucHien, dtcp.namTruoc as namTruoc, dtcp.thangTruoc as thangTruoc, " +
            "dtcp.cungKyNgay as cungKyNgay, dtcp.cungKyThang as cungKyThang, dtcp.cungKyNam as cungKyNam " +
            "FROM TienDoDoanhThuCP dtcp " +
            "WHERE dtcp.ngayBaoCao = :toTime ")
    public List<CSKDTienDoDoanhThuCPOverViewDto> findDoanhThuCP(
            @Param("toTime") LocalDate toTime);

    @Query("SELECT dtcp.ngayBaoCao as ngayBaoCao, dtcp.dichVu as loaiDichVu, dtcp.tiLeTangTruongNgay as tiLeTangTruongNgay " +
            "FROM TienDoDoanhThuCP dtcp " +
            "WHERE dtcp.ngayBaoCao >= :ngayBatDau " +
            "AND dtcp.ngayBaoCao <= :ngayKetThuc ")
    public List<BieuDoTtTienDoDoanhThuJPADto> bieuDoTtTienDoDoanhThuCP(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc);
}
