package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.DmBuuCucClone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DmBuuCucCloneRepository extends JpaRepository<DmBuuCucClone, Long> {
    @Query("SELECT b FROM DmBuuCucClone b " +
            "WHERE b.isActive = :isActive " +
            "AND b.capBuuCuc IN :capBuuCucList " +
            "AND (:chiNhanh IS NULL OR b.maCn = :chiNhanh) " +
            "AND (:buuCuc IS NULL OR b.maBuuCuc = :buuCuc) " +
            "AND (:maChiNhanhSSO IS NULL OR b.maCn IN :maChiNhanhSSO) " +
            "AND (:maBuuCucSSO IS NULL OR b.maBuuCuc IN :maBuuCucSSO)")
    List<DmBuuCucClone> findBuuCucsByIsActiveAndCapIn(@Param("isActive") Integer isActive,
                                                      @Param("capBuuCucList") List<Integer> capBuuCucList,
                                                      @Param("chiNhanh") String chiNhanh,
                                                      @Param("buuCuc") String buuCuc,
                                                      @Param("maChiNhanhSSO") List<String> maChiNhanhSSO,
                                                      @Param("maBuuCucSSO") List<String> maBuuCucSSO);

    @Query("SELECT b.capBuuCuc FROM DmBuuCucClone b " +
            "WHERE b.isActive = :isActive " +
            "AND (:chiNhanh IS NULL OR b.maCn = :chiNhanh) " +
            "AND (:buuCuc IS NULL OR b.maBuuCuc = :buuCuc)")
    Integer findCapBuuCuc(@Param("isActive") Integer isActive,
                          @Param("chiNhanh") String chiNhanh,
                          @Param("buuCuc") String buuCuc);
}
