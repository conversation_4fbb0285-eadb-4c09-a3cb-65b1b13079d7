package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuDetailScreenDto;
import nocsystem.indexmanager.models.ThongKeTongDoanhThu.ThongKeTongDoanhThuBC;
import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCV1ResDto;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ThongKeTongDoanhThuBCRepository extends PagingAndSortingRepository<ThongKeTongDoanhThuBC, Long> {
    @Query("SELECT tdt_bc.maChiNhanh as maChiNhanh, tdt_bc.tlht as tlht, " +
            "tdt_bc.ttThang as ttThang, tdt_bc.ttNam as ttNam, tdt_bc.ttTbnThang as ttTbnThang, " +
            "tdt_bc.ttTbnNam as ttTbnNam, tdt_bc.tongDoanhThu as tongDoanhThu " +
            "FROM ThongKeTongDoanhThuBC tdt_bc " +
            "WHERE tdt_bc.maChiNhanh = :maCN " +
            "AND tdt_bc.ngayBaoCao = :toTime")
    ThongKeTongDoanhThuBCV1ResDto findThongKeTongDoanhThuBCAndCN(
            @Param("maCN") String maCN,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT tdt_bc.maChiNhanh as maChiNhanh, tdt_bc.tlht as tlht, " +
            "tdt_bc.ttThang as ttThang, tdt_bc.ttNam as ttNam, tdt_bc.ttTbnThang as ttTbnThang, " +
            "tdt_bc.ttTbnNam as ttTbnNam, tdt_bc.tongDoanhThu as tongDoanhThu " +
            "FROM ThongKeTongDoanhThuBC tdt_bc " +
            "WHERE tdt_bc.ngayBaoCao = :toTime " +
            "AND tdt_bc.maChiNhanh = :maChiNhanh")
    ThongKeTongDoanhThuBCV1ResDto findThongKeTongDoanhThuBCAndCNAll(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT tdt_bc.maChiNhanh as maChiNhanh, tdt_bc.tlht as tlht, " +
            "tdt_bc.ttThang as ttThang, tdt_bc.ttNam as ttNam, tdt_bc.ttTbnThang as ttTbnThang, " +
            "tdt_bc.ttTbnNam as ttTbnNam, tdt_bc.tongDoanhThu as tongDoanhThu, tdt_bc.thucHien as thucHien, " +
            "tdt_bc.keHoach as keHoach, tdt_bc.tienDo as tienDo " +
            "FROM ThongKeTongDoanhThuBC tdt_bc " +
            "WHERE tdt_bc.ngayBaoCao = :toTime " +
            "AND tdt_bc.maChiNhanh = :maChiNhanh")
    TienDoDoanhThuBCV1ResDto findDoanhThuMoiChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT tdt_nc.ngayBaoCao as ngayBaoCao, tdt_nc.tongDoanhThu as tongDoanhThu, " +
            "tdt_nc.tlht as tlht, tdt_nc.ttThang as ttThang, tdt_nc.ttNam as ttNam, tdt_nc.ttTbnThang as ttTbnThang, " +
            "tdt_nc.ttTbnNam as ttTbnNam, tdt_nc.keHoach as keHoach, " +
            "tdt_nc.cungKyNgay as cungKyNgay, tdt_nc.cungKyThang as cungKyThang, tdt_nc.cungKyNam as cungKyNam, " +
            "tdt_nc.thangTruoc as thangTruoc, tdt_nc.namTruoc as namTruoc " +
            "FROM TongDoanhThuBC tdt_nc " +
            "WHERE tdt_nc.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR tdt_nc.maChiNhanh = :maChiNhanh) " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR tdt_nc.maBuuCuc = :maBuuCuc) " +
            "AND tdt_nc.maBuuCuc IN :listBuuCucVeriable")
    Slice<TongDoanhThuDetailScreenDto> findThongKeTongDoanhThuBCAll(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("toTime") LocalDate toTime,
            Pageable paging,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

}
