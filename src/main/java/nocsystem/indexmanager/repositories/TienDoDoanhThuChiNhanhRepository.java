package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPBCDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.*;
import nocsystem.indexmanager.models.TienDoDoanhThu.TienDoDoanhThuChiNhanh;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuChiNhanhRepository extends PagingAndSortingRepository<TienDoDoanhThuChi<PERSON>hanh, Long> {
    @Query("SELECT dtcn.ma<PERSON>hi<PERSON><PERSON>h as ma<PERSON><PERSON><PERSON><PERSON><PERSON>, dtcn.nhomDoanhThu as nhomDoanhThu, " +
            "dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.tlht as tlht, dtcn.tienDo as tienDo, dtcn.ttThang as ttThang, " +
            "dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, dtcn.ttTbnNam as ttTbnNam " +
            " FROM TienDoDoanhThuChiNhanh dtcn " +
            " WHERE dtcn.maChiNhanh = :maChiNhanh " +
            " AND dtcn.ngayBaoCao = :toTime " +
            " AND (:nhomDoanhThu = '' OR dtcn.nhomDoanhThu = :nhomDoanhThu) " +
            " AND dtcn.maChiNhanh NOT IN :exceptionChiNhanh " +
            " order by dtcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCNV1ResDto> findTienDoDoanhThuChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDoanhThu, " +
            "dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.tlht as tlht, dtcn.tienDo as tienDo, dtcn.ttThang as ttThang, " +
            "dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, dtcn.ttTbnNam as ttTbnNam " +
            " FROM TienDoDoanhThuChiNhanh dtcn " +
            " WHERE dtcn.maChiNhanh = :maChiNhanh " +
            " AND dtcn.ngayBaoCao = :toTime " +
            " AND (:nhomDoanhThu = '' OR dtcn.nhomDoanhThu = :nhomDoanhThu) " +
            " AND dtcn.maChiNhanh NOT IN :exceptionChiNhanh " +
            " order by dtcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCNV1ResDto> findTienDoDoanhThuChiNhanhV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDoanhThu, " +
            "dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.tlht as tlht, dtcn.tienDo as tienDo, dtcn.ttThang as ttThang, " +
            "dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, dtcn.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtcn.nhomDoanhThu = :nhomDoanhThu)" +
            "AND dtcn.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCNV1ResDto> findTienDoDoanhThuChiNhanhFollowTime(
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDoanhThu, " +
            "dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.tlht as tlht, dtcn.tienDo as tienDo, dtcn.ttThang as ttThang, " +
            "dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, dtcn.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtcn.nhomDoanhThu = :nhomDoanhThu)" +
            "AND (dtcn.maChiNhanh IN :listChiNhanhVeriable) " +
            "order by dtcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCNV1ResDto> findTienDoDoanhThuChiNhanhFollowTimeV2(
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDoanhThu, " +
            "dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.tlht as tlht, dtcn.tienDo as tienDo, " +
            "dtcn.ttThang as ttThang, dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, " +
            "dtcn.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND dtcn.maChiNhanh = :maChiNhanh " +
            "AND dtcn.nhomDoanhThu = :nhom_doanhthu")
    TienDoDoanhThuBCV1ResDto findDoanhThuLogistic(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhom_doanhthu") String nhom_doanhthu,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDoanhThu, dtcn.tlht as tlht, dtcn.tienDo as tienDo, " +
            "dtcn.ttThang as ttThang, dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, dtcn.keHoach as keHoach, " +
            "dtcn.thucHien as thucHien, dtcn.ttTbnNam as ttTbnNam " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND dtcn.nhomDoanhThu = :nhomDoanhThu " +
            "AND dtcn.maChiNhanh = :maChiNhanh"
    )
    FindTotalTienDoDoanhThuCPBCDto findDoanhThuChuyenPhat(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtcn.nhomDoanhThu as nhomDt, dtcn.tlht as tlHoanThanh, dtcn.tienDo as tienDo, " +
            "dtcn.ttThang as ttThang, dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, " +
            "dtcn.ttTbnNam as ttTbnNam, dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.thangTruoc as thangTruoc, " +
            "dtcn.cungKyThang as cungKyThang, dtcn.namTruoc as namTruoc, dtcn.cungKyNam as cungKyNam, " +
            "dtcn.cungKyNgay as cungKyNgay, dtcn.thucHienNgay as thucHienNgay, dtcn.thucHienNgayTruocDo as thucHienNgayTruocDo, " +
            "dtcn.ngayQuyDoiThang as ngayQuyDoiThang, dtcn.tiLeTangTruongNgay as tiLeTangTruongNgay, dtcn.hsNgay as hsNgay " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR dtcn.maChiNhanh = :maChiNhanh) " +
            "AND (dtcn.maChiNhanh IN :listChiNhanhVeriable)"
    )
    List<CSKDTienDoDoanhThuOverViewDto> findTienDoDoanhThuChiNhanhOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT dtcn.nhomDoanhThu as nhomDt, dtcn.tlht as tlHoanThanh, dtcn.tienDo as tienDo, " +
            "dtcn.ttThang as ttThang, dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, " +
            "dtcn.ttTbnNam as ttTbnNam, dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.thangTruoc as thangTruoc, " +
            "dtcn.cungKyThang as cungKyThang, dtcn.namTruoc as namTruoc, dtcn.cungKyNam as cungKyNam, " +
            "dtcn.cungKyNgay as cungKyNgay, dtcn.thucHienNgay as thucHienNgay, dtcn.thucHienNgayTruocDo as thucHienNgayTruocDo, " +
            "dtcn.ngayQuyDoiThang as ngayQuyDoiThang, dtcn.tiLeTangTruongNgay as tiLeTangTruongNgay, dtcn.hsNgay as hsNgay " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR dtcn.maChiNhanh = :maChiNhanh) "
    )
    List<CSKDTienDoDoanhThuOverViewDto> findTienDoDoanhThuChiNhanhOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDt, dtcn.tlht as tlHoanThanh, dtcn.tienDo as tienDo, " +
            "dtcn.ttThang as ttThang, dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, " +
            "dtcn.ttTbnNam as ttTbnNam, dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.thangTruoc as thangTruoc, " +
            "dtcn.cungKyThang as cungKyThang, dtcn.namTruoc as namTruoc, dtcn.cungKyNam as cungKyNam, " +
            "dtcn.cungKyNgay as cungKyNgay, dtcn.thucHienNgay as thucHienNgay, dtcn.thucHienNgayTruocDo as thucHienNgayTruocDo, " +
            "dtcn.ngayQuyDoiThang as ngayQuyDoiThang, dtcn.tiLeTangTruongNgay as tiLeTangTruongNgay, dtcn.hsNgay as hsNgay " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR dtcn.maChiNhanh = :maChiNhanh) " +
            "AND dtcn.maChiNhanh IN :listChiNhanhVeriable "
    )
    List<CSKDTienDoDoanhThuOverViewDto> findTienDoDoanhThuChiNhanhOverViewV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDt, dtcn.tlht as tlht, dtcn.tienDo as tienDo, " +
            "dtcn.ttThang as ttThang, dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, " +
            "dtcn.ttTbnNam as ttTbnNam, dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.thangTruoc as thangTruoc, " +
            "dtcn.cungKyThang as cungKyThang, dtcn.namTruoc as namTruoc, dtcn.cungKyNam as cungKyNam, " +
            "dtcn.cungKyNgay as cungKyNgay " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtcn.nhomDoanhThu = :nhomDoanhThu) " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR dtcn.maChiNhanh = :maChiNhanh)"
    )
    Slice<CSKDTienDoDoanhThuOverViewDto> findListTienDoDoanhThuChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT dtcn.maChiNhanh as maChiNhanh, dtcn.nhomDoanhThu as nhomDt, dtcn.tlht as tlht, dtcn.tienDo as tienDo, " +
            "dtcn.ttThang as ttThang, dtcn.ttTbnThang as ttTbnThang, dtcn.ttNam as ttNam, " +
            "dtcn.ttTbnNam as ttTbnNam, dtcn.thucHien as thucHien, dtcn.keHoach as keHoach, dtcn.thangTruoc as thangTruoc, " +
            "dtcn.cungKyThang as cungKyThang, dtcn.namTruoc as namTruoc, dtcn.cungKyNam as cungKyNam, " +
            "dtcn.cungKyNgay as cungKyNgay " +
            "FROM TienDoDoanhThuChiNhanh dtcn " +
            "WHERE dtcn.ngayBaoCao = :toTime " +
            "AND (:nhomDoanhThu is null OR :nhomDoanhThu = '' OR dtcn.nhomDoanhThu = :nhomDoanhThu) " +
            "AND (:maChiNhanh is null OR :maChiNhanh = '' OR dtcn.maChiNhanh = :maChiNhanh) " +
            "AND (dtcn.maChiNhanh IN :listChiNhanhVeriable )"
    )
    Slice<CSKDTienDoDoanhThuOverViewDto> findListTienDoDoanhThuChiNhanhV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("nhomDoanhThu") String nhomDoanhThu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT tddt.tiLeTangTruongNgay as tiLeTangTruongNgay, tddt.ngayBaoCao as ngayBaoCao, tddt.nhomDoanhThu as nhomDt " +
            "FROM TienDoDoanhThuChiNhanh tddt " +
            "WHERE tddt.ngayBaoCao <= :ngayKetThuc " +
            "AND tddt.ngayBaoCao >= :ngayBatDau " +
            "AND tddt.maChiNhanh = :maChiNhanh " +
            "ORDER BY tddt.ngayBaoCao asc "
    )
    public List<DataBieuDoTienDoDoanhThuDto> dataBieuDoTienDoDoanhThu(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("maChiNhanh") String maChiNhanh);



}
