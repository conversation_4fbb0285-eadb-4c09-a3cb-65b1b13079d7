package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.ThongKeLuyKe.TienDoLuyKeTCTCN;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TongTienDoLuyKeCNHaNoiAndHCMRepository extends JpaRepository<TienDoLuyKeTCTCN, Long> {

    @Query( "SELECT TongTDLuyKeCNHN.ngayBaoCao as ngayBaoCao, TongTDLuyKeCNHN.chiNhanh as chiNhanh," +
            "TongTDLuyKeCNHN.mien as mien, TongTDLuyKeCNHN.tlht as tlht, TongTDLuyKeCNHN.tienDo as tienDo, " +
            "TongTDLuyKeCNHN.ttThang as ttThang, TongTDLuyKeCNHN.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeCNHN.ttNam as ttNam, TongTDLuyKeCNHN.ttTbnNam as ttTbnNam,TongTDLuyKeCNHN.khthang as keHoach, " +
            "TongTDLuyKeCNHN.lkthang as luyKeThang, TongTDLuyKeCNHN.danhgia as danhgia, TongTDLuyKeCNHN.tienDoNgay as tienDoNgay, " +
            "TongTDLuyKeCNHN.tiLeHoanThanhNgay as tiLeHoanThanhNgay, TongTDLuyKeCNHN.keHoachThangDenNgay as keHoachThangDenNgay, " +
            "TongTDLuyKeCNHN.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN TongTDLuyKeCNHN " +
            "WHERE TongTDLuyKeCNHN.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeCNHN.chiNhanh = 'HNI'")
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHaNoi(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query( "SELECT TongTDLuyKeCNHN.ngayBaoCao as ngayBaoCao, TongTDLuyKeCNHN.chiNhanh as chiNhanh," +
            "TongTDLuyKeCNHN.mien as mien, TongTDLuyKeCNHN.tlht as tlht, TongTDLuyKeCNHN.tienDo as tienDo, " +
            "TongTDLuyKeCNHN.ttThang as ttThang, TongTDLuyKeCNHN.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeCNHN.ttNam as ttNam, TongTDLuyKeCNHN.ttTbnNam as ttTbnNam,TongTDLuyKeCNHN.khthang as keHoach, " +
            "TongTDLuyKeCNHN.lkthang as luyKeThang, TongTDLuyKeCNHN.danhgia as danhgia, TongTDLuyKeCNHN.tienDoNgay as tienDoNgay, " +
            "TongTDLuyKeCNHN.tiLeHoanThanhNgay as tiLeHoanThanhNgay, TongTDLuyKeCNHN.keHoachThangDenNgay as keHoachThangDenNgay, " +
            "TongTDLuyKeCNHN.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN TongTDLuyKeCNHN " +
            "WHERE TongTDLuyKeCNHN.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeCNHN.chiNhanh = 'HNI' " +
            "AND TongTDLuyKeCNHN.chiNhanh IN :listChiNhanhVeriable ")
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHaNoiV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query( "SELECT TongTDLuyKeCNHCM.ngayBaoCao as ngayBaoCao, TongTDLuyKeCNHCM.chiNhanh as chiNhanh," +
            "TongTDLuyKeCNHCM.mien as mien, TongTDLuyKeCNHCM.tlht as tlht, TongTDLuyKeCNHCM.tienDo as tienDo, " +
            "TongTDLuyKeCNHCM.ttThang as ttThang, TongTDLuyKeCNHCM.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeCNHCM.ttNam as ttNam, TongTDLuyKeCNHCM.ttTbnNam as ttTbnNam, " +
            "TongTDLuyKeCNHCM.danhgia as danhgia,TongTDLuyKeCNHCM.khthang as keHoach, " +
            "TongTDLuyKeCNHCM.lkthang as luyKeThang, TongTDLuyKeCNHCM.tienDoNgay as tienDoNgay, TongTDLuyKeCNHCM.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "TongTDLuyKeCNHCM.keHoachThangDenNgay as keHoachThangDenNgay, TongTDLuyKeCNHCM.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN TongTDLuyKeCNHCM " +
            "WHERE TongTDLuyKeCNHCM.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeCNHCM.chiNhanh = 'HCM'")
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHCM(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query( "SELECT TongTDLuyKeCNHCM.ngayBaoCao as ngayBaoCao, TongTDLuyKeCNHCM.chiNhanh as chiNhanh," +
            "TongTDLuyKeCNHCM.mien as mien, TongTDLuyKeCNHCM.tlht as tlht, TongTDLuyKeCNHCM.tienDo as tienDo, " +
            "TongTDLuyKeCNHCM.ttThang as ttThang, TongTDLuyKeCNHCM.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeCNHCM.ttNam as ttNam, TongTDLuyKeCNHCM.ttTbnNam as ttTbnNam, " +
            "TongTDLuyKeCNHCM.danhgia as danhgia,TongTDLuyKeCNHCM.khthang as keHoach, " +
            "TongTDLuyKeCNHCM.lkthang as luyKeThang, TongTDLuyKeCNHCM.tienDoNgay as tienDoNgay, TongTDLuyKeCNHCM.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "TongTDLuyKeCNHCM.keHoachThangDenNgay as keHoachThangDenNgay, TongTDLuyKeCNHCM.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN TongTDLuyKeCNHCM " +
            "WHERE TongTDLuyKeCNHCM.ngayBaoCao = :ngayBaoCao " +
            "AND TongTDLuyKeCNHCM.chiNhanh = 'HCM' " +
            "AND TongTDLuyKeCNHCM.chiNhanh IN :listChiNhanhVeriable ")
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHCMV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );
}
