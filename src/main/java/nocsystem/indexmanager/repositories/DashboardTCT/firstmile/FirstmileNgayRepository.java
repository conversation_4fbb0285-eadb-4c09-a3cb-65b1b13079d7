package nocsystem.indexmanager.repositories.DashboardTCT.firstmile;

import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashFirstmileTLThuTCDto;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileNgay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FirstmileNgayRepository extends JpaRepository<DashTCTFirstmileNgay, Integer> {
    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto(d.ngayBaoCao, d.stt, d.type, d.title, " +
            "d.san<PERSON>ong<PERSON>, d.tyLe<PERSON>, d.sanluongN1, d.ty<PERSON>eN1, d.tang<PERSON><PERSON><PERSON>, d.ty<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d.kpi) " +
            "from DashTCTFirstmileNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.chiNhanh = 'ALL' " +
            "and d.buuCuc = 'ALL' " +
            "order by d.stt")
    List<DashTCTFirstmileDto> getListFirstmileAllNgay(Date ngayBaoCao);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto(d.ngayBaoCao, d.chiNhanh, d.stt, d.type, d.title, " +
            "d.sanluongN, d.tyLeN, d.sanluongN1, d.tyLeN1, d.tangTruong, d.tyLeTangTruong, d.kpi) " +
            "from DashTCTFirstmileNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.chiNhanh = :chiNhanh " +
            "and d.buuCuc = 'ALL' " +
            "order by d.stt")
    List<DashTCTFirstmileDto> getListFirstmileCNNgay(Date ngayBaoCao, String chiNhanh);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto(d.ngayBaoCao, d.chiNhanh, d.buuCuc, d.stt, d.type, d.title, " +
            "d.sanluongN, d.tyLeN, d.sanluongN1, d.tyLeN1, d.tangTruong, d.tyLeTangTruong, d.kpi) " +
            "from DashTCTFirstmileNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.chiNhanh = :chiNhanh " +
            "and d.buuCuc = :buuCuc " +
            "order by d.stt")
    List<DashTCTFirstmileDto> getListFirstmileCNBCNgay(Date ngayBaoCao, String chiNhanh, String buuCuc);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashFirstmileTLThuTCDto(d.ngayBaoCao, d.tyLeN) " +
            "from DashTCTFirstmileNgay d " +
            "where d.ngayBaoCao >= :ngayDauTien and d.ngayBaoCao <= :ngayBaoCao " +
            "and d.stt = :stt " +
            "and d.chiNhanh = 'ALL' " +
            "and d.buuCuc = 'ALL' " +
            "order by d.ngayBaoCao")
    List<DashFirstmileTLThuTCDto> getListDashFirstmileTLThuTCAllNgay(Date ngayDauTien, Date ngayBaoCao, String stt);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashFirstmileTLThuTCDto(d.ngayBaoCao, d.tyLeN) " +
            "from DashTCTFirstmileNgay d " +
            "where d.ngayBaoCao >= :ngayDauTien and d.ngayBaoCao <= :ngayBaoCao " +
            "and d.stt = :stt " +
            "and d.chiNhanh = :chiNhanh " +
            "and d.buuCuc = :buuCuc " +
            "order by d.ngayBaoCao")
    List<DashFirstmileTLThuTCDto> getListDashFirstmileTLThuTCCNBCNgay(Date ngayDauTien, Date ngayBaoCao, String stt, String chiNhanh, String buuCuc);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashFirstmileTLThuTCDto(d.ngayBaoCao, d.tyLeN) " +
            "from DashTCTFirstmileNgay d " +
            "where d.ngayBaoCao >= :ngayDauTien and d.ngayBaoCao <= :ngayBaoCao " +
            "and d.stt = :stt " +
            "and d.chiNhanh = :chiNhanh " +
            "and d.buuCuc = 'ALL' " +
            "order by d.ngayBaoCao")
    List<DashFirstmileTLThuTCDto> getListDashFirstmileTLThuTCCNNgay(Date ngayDauTien, Date ngayBaoCao, String stt, String chiNhanh);
}
