package nocsystem.indexmanager.repositories.DashboardTCT.firstmile;

import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileThang;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FirstmileThangRepository extends JpaRepository<DashTCTFirstmileThang, Integer> {
    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto(d.ngayBao<PERSON>ao, d.stt, d.type, d.title, " +
            "d.sanluongN, d.tyLeN, d.sanluongN1, d.tyLeN1, d.tang<PERSON><PERSON>, d.tyLe<PERSON>ang<PERSON>ruong, d.kpi) " +
            "from DashTCTFirstmileThang d " +
            "where d.ngayBaoCao = :ngayBao<PERSON>ao " +
            "and d.chiNhanh = 'ALL' " +
            "and d.buuCuc = 'ALL' " +
            "order by d.stt")
    List<DashTCTFirstmileDto> getListFirstmileAllThang(Date ngayBaoCao);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto(d.ngayBaoCao, d.chiNhanh, d.stt, d.type, d.title, " +
            "d.sanluongN, d.tyLeN, d.sanluongN1, d.tyLeN1, d.tangTruong, d.tyLeTangTruong, d.kpi) " +
            "from DashTCTFirstmileThang d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.chiNhanh = :chiNhanh " +
            "and d.buuCuc = 'ALL' " +
            "order by d.stt")
    List<DashTCTFirstmileDto> getListFirstmileCNThang(Date ngayBaoCao, String chiNhanh);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto(d.ngayBaoCao, d.chiNhanh, d.buuCuc, d.stt, d.type, d.title, " +
            "d.sanluongN, d.tyLeN, d.sanluongN1, d.tyLeN1, d.tangTruong, d.tyLeTangTruong, d.kpi) " +
            "from DashTCTFirstmileThang d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.chiNhanh = :chiNhanh " +
            "and d.buuCuc = :buuCuc " +
            "order by d.stt")
    List<DashTCTFirstmileDto> getListFirstmileCNBCThang(Date ngayBaoCao, String chiNhanh, String buuCuc);
}
