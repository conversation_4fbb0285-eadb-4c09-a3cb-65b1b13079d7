package nocsystem.indexmanager.repositories.DashboardTCT.leadtime;

import nocsystem.indexmanager.models.DashboardTCT.leadtime.*;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.MiddleMileDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface LeadtimeNgayRepository extends JpaRepository<DashTCTLeadtimeNgay, Long> {
    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeDto( " +
            "avg(d.tgTtAll), avg(d.tgFmAll), avg(d.tgMmAll), avg(d.tgLmAll), " +
            "avg(d.tgNoiTinhAll), avg(d.tgNoiMienAll), avg(d.tgLienMienAll), " +
            "avg(d.tgTtNhanh), avg(d.tgFmNhanh), avg(d.tgMmNhanh), avg(d.tgLmNhanh), " +
            "avg(d.tgNoiTinhNhanh), avg(d.tgNoiMienNhanh), avg(d.tgLienMienNhanh), " +
            "avg(d.tgTtTk), avg(d.tgFmTk), avg(d.tgMmTk), avg(d.tgLmTk), " +
            "avg(d.tgNoiTinhTk), avg(d.tgNoiMienTk), avg(d.tgLienMienTk), " +
            "avg(d.tgTtKienTk), avg(d.tgFmKienTk), avg(d.tgMmKienTk), avg(d.tgLmKienTk), " +
            "avg(d.tgNoiTinhKienTk), avg(d.tgNoiMienKienTk), avg(d.tgLienMienKienTk), " +
            "avg(d.tgTtTachKienTk), avg(d.tgFmTachKienTk), avg(d.tgMmTachKienTk), avg(d.tgLmTachKienTk), " +
            "avg(d.tgNoiTinhTachKienTk), avg(d.tgNoiMienTachKienTk), avg(d.tgLienMienTachKienTk)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao")
    LeadtimeDto getLeadtimeNgayAll(Date ngayBaoCao);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeDto( " +
            "avg(d.tgTtAll), avg(d.tgFmAll), avg(d.tgMmAll), avg(d.tgLmAll), " +
            "avg(d.tgNoiTinhAll), avg(d.tgNoiMienAll), avg(d.tgLienMienAll), " +
            "avg(d.tgTtNhanh), avg(d.tgFmNhanh), avg(d.tgMmNhanh), avg(d.tgLmNhanh), " +
            "avg(d.tgNoiTinhNhanh), avg(d.tgNoiMienNhanh), avg(d.tgLienMienNhanh), " +
            "avg(d.tgTtTk), avg(d.tgFmTk), avg(d.tgMmTk), avg(d.tgLmTk), " +
            "avg(d.tgNoiTinhTk), avg(d.tgNoiMienTk), avg(d.tgLienMienTk), " +
            "avg(d.tgTtKienTk), avg(d.tgFmKienTk), avg(d.tgMmKienTk), avg(d.tgLmKienTk), " +
            "avg(d.tgNoiTinhKienTk), avg(d.tgNoiMienKienTk), avg(d.tgLienMienKienTk), " +
            "avg(d.tgTtTachKienTk), avg(d.tgFmTachKienTk), avg(d.tgMmTachKienTk), avg(d.tgLmTachKienTk), " +
            "avg(d.tgNoiTinhTachKienTk), avg(d.tgNoiMienTachKienTk), avg(d.tgLienMienTachKienTk)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh")
    LeadtimeDto getLeadtimeNgayCN(Date ngayBaoCao, String maChiNhanh);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeDto( " +
            "d.tgTtAll, d.tgFmAll, d.tgMmAll, d.tgLmAll, " +
            "d.tgNoiTinhAll, d.tgNoiMienAll, d.tgLienMienAll, " +
            "d.tgTtNhanh, d.tgFmNhanh, d.tgMmNhanh, d.tgLmNhanh, " +
            "d.tgNoiTinhNhanh, d.tgNoiMienNhanh, d.tgLienMienNhanh, " +
            "d.tgTtTk, d.tgFmTk, d.tgMmTk, d.tgLmTk, " +
            "d.tgNoiTinhTk, d.tgNoiMienTk, d.tgLienMienTk, " +
            "d.tgTtKienTk, d.tgFmKienTk, d.tgMmKienTk, d.tgLmKienTk, " +
            "d.tgNoiTinhKienTk, d.tgNoiMienKienTk, d.tgLienMienKienTk, " +
            "d.tgTtTachKienTk, d.tgFmTachKienTk, d.tgMmTachKienTk, d.tgLmTachKienTk, " +
            "d.tgNoiTinhTachKienTk, d.tgNoiMienTachKienTk, d.tgLienMienTachKienTk) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh " +
            "and d.maBuuCuc = :maBuuCuc")
    LeadtimeDto getLeadtimeNgayCNBC(Date ngayBaoCao, String maChiNhanh, String maBuuCuc);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTAllResponse( " +
            "d.ngayBaoCao, avg(d.tgTtAll), avg(d.tgNoiTinhAll), avg(d.tgNoiMienAll), avg(d.tgLienMienAll)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao between :ngayBatDau and :ngayBaoCao " +
            "group by d.ngayBaoCao " +
            "order by d.ngayBaoCao")
    List<LeadtimeChartTTAllResponse> getLeadtimeChartTTAllNgayAll(Date ngayBatDau, Date ngayBaoCao);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTAllResponse( " +
            "d.ngayBaoCao, avg(d.tgTtAll), avg(d.tgNoiTinhAll), avg(d.tgNoiMienAll), avg(d.tgLienMienAll)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao between :ngayBatDau and :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh " +
            "group by d.ngayBaoCao " +
            "order by d.ngayBaoCao")
    List<LeadtimeChartTTAllResponse> getLeadtimeChartTTAllNgayCN(Date ngayBatDau, Date ngayBaoCao, String maChiNhanh);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTAllResponse( " +
            "d.ngayBaoCao, d.tgTtAll, d.tgNoiTinhAll, d.tgNoiMienAll, d.tgLienMienAll) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao between :ngayBatDau and :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh " +
            "and d.maBuuCuc = :maBuuCuc " +
            "order by d.ngayBaoCao")
    List<LeadtimeChartTTAllResponse> getLeadtimeChartTTAllNgayCNBC(Date ngayBatDau, Date ngayBaoCao, String maChiNhanh, String maBuuCuc);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTTachKienTKResponse( " +
            "d.ngayBaoCao, avg(d.tgTtTachKienTk), avg(d.tgNoiTinhTachKienTk), avg(d.tgNoiMienTachKienTk), avg(d.tgLienMienTachKienTk)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao between :ngayBatDau and :ngayBaoCao " +
            "group by d.ngayBaoCao " +
            "order by d.ngayBaoCao")
    List<LeadtimeChartTTTachKienTKResponse> getLeadtimeChartTTTachKienTKNgayAll(Date ngayBatDau, Date ngayBaoCao);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTTachKienTKResponse( " +
            "d.ngayBaoCao, avg(d.tgTtTachKienTk), avg(d.tgNoiTinhTachKienTk), avg(d.tgNoiMienTachKienTk), avg(d.tgLienMienTachKienTk)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao between :ngayBatDau and :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh " +
            "group by d.ngayBaoCao " +
            "order by d.ngayBaoCao")
    List<LeadtimeChartTTTachKienTKResponse> getLeadtimeChartTTTachKienTKNgayCN(Date ngayBatDau, Date ngayBaoCao, String maChiNhanh);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTTachKienTKResponse( " +
            "d.ngayBaoCao, d.tgTtTachKienTk, d.tgNoiTinhTachKienTk, d.tgNoiMienTachKienTk, d.tgLienMienTachKienTk) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao between :ngayBatDau and :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh " +
            "and d.maBuuCuc = :maBuuCuc " +
            "order by d.ngayBaoCao")
    List<LeadtimeChartTTTachKienTKResponse> getLeadtimeChartTTTachKienTKNgayCNBC(Date ngayBatDau, Date ngayBaoCao, String maChiNhanh, String maBuuCuc);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.middlemile.MiddleMileDto( " +
            "avg(d.tgMmAll), avg(d.tgMmNoiMien), avg(d.tgMmLienMien)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao")
    MiddleMileDto getMiddleMileNgayAll(Date ngayBaoCao);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.middlemile.MiddleMileDto( " +
            "avg(d.tgMmAll), avg(d.tgMmNoiMien), avg(d.tgMmLienMien)) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh")
    MiddleMileDto getMiddleMileNgayCN(Date ngayBaoCao, String maChiNhanh);

    @Query("select new nocsystem.indexmanager.models.DashboardTCT.middlemile.MiddleMileDto( " +
            "d.tgMmAll, d.tgMmNoiMien, d.tgMmLienMien) " +
            "from DashTCTLeadtimeNgay d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and d.maChiNhanh = :maChiNhanh " +
            "and d.maBuuCuc = :maBuuCuc")
    MiddleMileDto getMiddleMileNgayCNBC(Date ngayBaoCao, String maChiNhanh, String maBuuCuc);
}