package nocsystem.indexmanager.repositories.MatHanhTrinhTTKT;

public class MatHanhTrinhTTKTRepository {
    public static String getQueryMatHanhTrinhList(String chiNhanhPhat, String loaiHang, String hangGiaTri, String nhomDichVu, String phanLoaiKhachHang, Long version) {
        String query = "SELECT ma_buucuc, updated_at," +
                " COUNT(CASE WHEN moc_ton = 1 THEN 1 END) AS duoi1h," +
                " COUNT(CASE WHEN moc_ton = 2 THEN 1 END) AS duoi2h," +
                " COUNT(CASE WHEN moc_ton = 3 THEN 1 END) AS tren2h" +
                " FROM NGUY_CO_MAT_HT_CHI_TIET" +
                " WHERE version = " + version;

        if (!(chiNhanhPhat == null || chiNhanhPhat.isEmpty())) {
            query += " AND chi_nhanh_phat IN " + chiNhanhPhat;
        }
        if (!(loaiHang == null || loaiHang.isEmpty())) {
            query += " AND loai_hang IN " + loaiHang;
        }
        if (!(hangGiaTri == null || hangGiaTri.isEmpty())) {
            query += " AND hang_gia_tri_cao IN " + hangGiaTri;
        }

        if (!(nhomDichVu == null || nhomDichVu.isEmpty())) {
            query += " AND nhom_dv IN " + nhomDichVu;
        }

        if (!(phanLoaiKhachHang == null || phanLoaiKhachHang.isEmpty())) {
            query += " AND phan_loai_kh IN" + phanLoaiKhachHang;
        }
        query += " GROUP BY ma_buucuc, updated_at";

        return query;
    }

    public static String getQueryMatHanhTrinhListDetail(Long version, Integer mocTon, String maBuuCuc, String chiNhanhPhat, String loaiHang, String hangGiaTri,
                                                        String nhomDichVu, String phanLoaiKhachHang) {
        String query = "SELECT" +
                "  ma_phieugui ," +
                "  ma_tai ," +
                "  thoi_gian_nhan  ," +
                "  thoi_gian_ton ," +
                "  hang_gia_tri_cao," +
                "  don_hoan ," +
                "  chi_nhanh_nhan  ," +
                "  ma_buucuc_nhan ," +
                "  chi_nhanh_phat ," +
                "  ma_buucuc_phat ," +
                "  noi_dung_hang ," +
                "  khoi_luong_hang ," +
                "  moc_ton ," +
                "  nhom_dv ," +
                "  tien_thu_ho ," +
                "  phan_loai_kh" +
                "  FROM NGUY_CO_MAT_HT_CHI_TIET " +
                "  WHERE version = " + version;

        if (!(maBuuCuc == null || maBuuCuc.isEmpty())) {
            query += " AND ma_buucuc = " + maBuuCuc;
        }

        if (mocTon != null) {
            query += " AND moc_ton = " + mocTon;
        }

        if (!(chiNhanhPhat == null || chiNhanhPhat.isEmpty())) {
            query += " AND chi_nhanh_phat IN " + chiNhanhPhat;
        }
        if (!(loaiHang == null || loaiHang.isEmpty())) {
            query += " AND loai_hang IN " + loaiHang;
        }
        if (!(hangGiaTri == null || hangGiaTri.isEmpty())) {
            query += " AND hang_gia_tri_cao IN " + hangGiaTri;
        }

        if (!(nhomDichVu == null || nhomDichVu.isEmpty())) {
            query += " AND nhom_dv IN " + nhomDichVu;
        }

        if (!(phanLoaiKhachHang == null || phanLoaiKhachHang.isEmpty())) {
            query += " AND phan_loai_kh IN" + phanLoaiKhachHang;
        }

        query +=" ORDER BY CASE" +
                "             WHEN phan_loai_kh = '5' THEN 0 " +
                "             ELSE 1 " +
                "             END," +
                "          CASE hang_gia_tri_cao " +
                "             WHEN 'CO' THEN 0 " +
                "             ELSE 1 " +
                "             END, " +
                "          CASE nhom_dv " +
                "             WHEN 'HOA_TOC' THEN 0 " +
                "             WHEN 'NHANH' THEN 1 " +
                "             ELSE 2 " +
                "             END";

        return query;
    }

}
