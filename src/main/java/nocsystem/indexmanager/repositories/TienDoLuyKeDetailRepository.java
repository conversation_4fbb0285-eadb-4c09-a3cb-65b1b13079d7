package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.*;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeCNHaNoiAndHCMResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.ThongKeLuyKe.TienDoLuyKeDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.persistence.Column;
import javax.persistence.Id;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoLuyKeDetailRepository extends JpaRepository<TienDoLuyKeDetail, Long> {
    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'B' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<ListDetailLuyKeChiNhanhMBDto> findTienDoLuyKeCNMienBac(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'B' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<ListDetailLuyKeChiNhanhMBDto> findTienDoLuyKeCNMienBacV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'B' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.chiNhanh IN :listChiNhanhVeriable " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeChiNhanhMienBacMultiBranch(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'T' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<ListDetailLuyKeChiNhanhMBDto> findTienDoLuyKeBCMienTrung(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'T' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<ListDetailLuyKeChiNhanhMBDto> findTienDoLuyKeBCMienTrungV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'T' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.chiNhanh IN :listChiNhanhVeriable " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeBCMienTrungMultiBranch(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang, luyKeBCMB.heSoNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'B' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeBCMienBacDetail(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang, luyKeBCMB.heSoNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'B' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.buuCuc IN :listBuuCucVeriable " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<ListDetailLuyKeChiNhanhMBDto> findTienDoLuyKeBCMienBacDetailV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable,
            Pageable pageable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'N' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND luyKeBCMB.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<TinhTongLuyKeChiNhanhMNDto> findTienDoLuyKeBCMienNam(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'N' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<TinhTongLuyKeChiNhanhMNDto> findTienDoLuyKeBCMienNamV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'N' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.chiNhanh IN :listChiNhanhVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeBCMienNamMultiBranch(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'T' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeBCMienTrungDetail(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'T' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<ListDetailLuyKeChiNhanhMBDto> findTienDoLuyKeBCMienTrungDetailV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'N' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeBCMienNamForBuuCuc(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
//            "AND luyKeBCMB.mien = 'N' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.chiNhanh IN :listChiNhanhVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeManTongCtyMultiBranch(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    List<TienDoluyKeTCTCNResDto> findTienDoLuyKeKeHoachTongCtyBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'N' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND (tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable) " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<TinhTongLuyKeChiNhanhMNDto> listDetailLuyKeCNMienNamV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable);

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'N' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    Page<TinhTongLuyKeChiNhanhMNDto> findTienDoLuyKeManTongCtyForBuuCuc(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'N' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    TinhTongLuyKeChiNhanhMNDto findLuyKeBCMienNamOnlyOne(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'T' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    TinhTongLuyKeChiNhanhMTDto findLuyKeBCMienTrungOnlyOne(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT luyKeBCMB.ngayBaoCao as ngayBaoCao, luyKeBCMB.chiNhanh as chiNhanh, luyKeBCMB.mien as mien," +
            "luyKeBCMB.buuCuc as buuCuc, luyKeBCMB.tlht as tlht, luyKeBCMB.tienDo as tienDo, luyKeBCMB.ttThang as ttThang, " +
            "luyKeBCMB.ttTbnThang as ttTbnThang, luyKeBCMB.ttNam as ttNam, luyKeBCMB.ttTbnNam as ttTbnNam, " +
            "luyKeBCMB.danhgia as danhgia, luyKeBCMB.khthang as khthang, luyKeBCMB.lkthang as lkthang, " +
            "luyKeBCMB.tienDoNgay as tienDoNgay, luyKeBCMB.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "luyKeBCMB.keHoachThangDenNgay as keHoachThangDenNgay, luyKeBCMB.thucHienNgay as thucHienNgay, " +
            "luyKeBCMB.cungKyNgay as cungKyNgay, luyKeBCMB.cungKyThang as cungKyThang, luyKeBCMB.cungKyNam as cungKyNam, " +
            "luyKeBCMB.khthang as keHoach, luyKeBCMB.lkthang as luyKeThang, luyKeBCMB.thangTruoc as thangTruoc, " +
            "luyKeBCMB.namTruoc as namTruoc, luyKeBCMB.ngayQuyDoiThang as ngayQuyDoiThang " +
            "FROM TienDoLuyKeDetail luyKeBCMB " +
            "WHERE luyKeBCMB.ngayBaoCao = :ngayBaoCao " +
            "AND luyKeBCMB.mien = 'B' " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or luyKeBCMB.chiNhanh = :maChiNhanh) " +
            "AND (:buuCuc is null or :buuCuc = '' or luyKeBCMB.buuCuc = :buuCuc) " +
            "AND (luyKeBCMB.buuCuc IN :listBuuCucVeriable) " +
            "ORDER BY luyKeBCMB.buuCuc asc "
    )
    TinhTongLuyKeChiNhanhMBDto findLuyKeBCMienBacOnlyOne(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );
}
