package nocsystem.indexmanager.repositories;

import com.fasterxml.jackson.annotation.JsonProperty;
import nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong.ChiTieuChatLuongDto;
import nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong.ChiTieuKinhDoanhDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPCNDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDTConvertTienDoLogisticDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticV1Dto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOriginDto;
import nocsystem.indexmanager.models.ThongKeTongDoanhThu.CSKDTongDoanhThu;
import nocsystem.indexmanager.models.Response.ChiSoKinhDoanh.CSKDTienDoDoanhThuResDto;
import nocsystem.indexmanager.models.Response.ChiSoKinhDoanh.CSKDTongDoanhThuResDto;
import nocsystem.indexmanager.models.TienDoDoanhThu.CSKDTienDoDoanhThu;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ChiSoKinhDoanhRepository extends JpaRepository<CSKDTongDoanhThu, Integer> {

    @Query("SELECT cskd.tlDoanhThu as tlDoanhThu, cskd.tlHoanThanh as tlHoanThanh, " +
            "cskd.ttThang as ttThang, cskd.ttNam as ttNam, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ngayBaoCao as ngayBaoCao," +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, cskd.tienDo as tienDo, " +
            "cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTongDoanhThu cskd WHERE cskd.ngayBaoCao = :toTime")
    public CSKDTongDoanhThuOriginDto findCSKDTongDoanhThu(
            @Param("toTime") LocalDate toTime);


    @Query("SELECT cskd.nhomDt as nhomDt, " +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, " +
            "cskd.tlHoanThanh as tlHoanThanh, cskd.tienDo as tienDo , cskd.ttThang as ttThang, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ttNam as ttNam, cskd.ttTbnNam as ttTbnNam, cskd.thangTruoc as thangTruoc, " +
            "cskd.namTruoc as namTruoc, cskd.cungKyNgay as cungKyNgay, cskd.cungKyThang as cungKyThang, " +
            "cskd.cungKyNam as cungKyNam, cskd.thucHienNgay as thucHienNgay, cskd.thucHienNgayTruocDo as thucHienNgayTruocDo, " +
            "cskd.ngayQuyDoiThang as ngayQuyDoiThang, cskd.tiLeTangTruongNgay as tiLeTangTruongNgay, cskd.hsNgay as hsNgay " +
            "FROM CSKDTienDoDoanhThu cskd WHERE cskd.ngayBaoCao = :toTime")
    public List<CSKDTienDoDoanhThuOverViewDto> findCSKDTienDoDoanhThu(
            @Param("toTime") LocalDate toTime);

    @Query("SELECT cskd.nhomDt as nhomDt, cskd.ngayBaoCao as ngayBaoCao, " +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, " +
            "cskd.tlHoanThanh as tlHoanThanh, cskd.tienDo as tienDo , cskd.ttThang as ttThang, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ttNam as ttNam, cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTienDoDoanhThu cskd " +
            "WHERE cskd.ngayBaoCao = :toTime " +
            " AND cskd.nhomDt = :nhomDt")
    public TienDoDoanhThuCPV1Dto findCSKDTienDoDoanhThuV2(
            @Param("nhomDt") String nhomDt,
            @Param("toTime") LocalDate toTime);

    @Query("SELECT cskd.tlDoanhThu as tlDoanhThu, cskd.tlHoanThanh as tlHoanThanh, " +
            "cskd.ttThang as ttThang, cskd.ttNam as ttNam, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ngayBaoCao as ngayBaoCao," +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, cskd.tienDo as tienDo, " +
            " cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTongDoanhThu cskd WHERE cskd.ngayBaoCao = :toTime")
    public CSKDTienDoDoanhThuV2Dto findCSKDTongDoanhThuConvert(
            @Param("toTime") LocalDate toTime);

    @Query("SELECT cskd.nhomDt as nhomDt, cskd.ngayBaoCao as ngayBaoCao, " +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, " +
            "cskd.tlHoanThanh as tlHoanThanh, cskd.tienDo as tienDo , cskd.ttThang as ttThang, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ttNam as ttNam, cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTienDoDoanhThu cskd " +
            "WHERE cskd.ngayBaoCao = :toTime " +
            " AND cskd.nhomDt = :nhomDt")
    public TienDoDoanhThuLogisticV1Dto findCSKDTDDTConvertLogistic(
            @Param("nhomDt") String nhomDt,
            @Param("toTime") LocalDate toTime);

    @Query("SELECT cskd.nhomDt as nhomDt, " +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, " +
            "cskd.tlHoanThanh as tlHoanThanh, cskd.tienDo as tienDo , cskd.ttThang as ttThang, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ttNam as ttNam, cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTienDoDoanhThu cskd " +
            "WHERE cskd.ngayBaoCao = :toTime " +
            "AND cskd.nhomDt = :nhom_doanhthu")
    public TienDoDoanhThuCNV1ResDto findDoanhThuLogistic(
            @Param("toTime") LocalDate toTime,
            @Param("nhom_doanhthu") String nhom_doanhthu);

    //findDoanhThuChuyenPhat
    @Query("SELECT cskd.nhomDt as nhomDt, " +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, " +
            "cskd.tlHoanThanh as tlHoanThanh, cskd.tienDo as tienDo , cskd.ttThang as ttThang, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ttNam as ttNam, cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTienDoDoanhThu cskd " +
            "WHERE cskd.ngayBaoCao = :toTime " +
            "AND cskd.nhomDt = :nhom_doanhthu")
    public FindTotalTienDoDoanhThuCPCNDto findDoanhThuChuyenPhat(
            @Param("toTime") LocalDate toTime,
            @Param("nhom_doanhthu") String nhom_doanhthu);

    @Query("SELECT cskd.tlDoanhThu as tlDoanhThu, cskd.tlHoanThanh as tlHoanThanh, " +
            "cskd.ttThang as ttThang, cskd.ttNam as ttNam, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ngayBaoCao as ngayBaoCao," +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, cskd.tienDo as tienDo, " +
            "cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTongDoanhThu cskd WHERE cskd.ngayBaoCao = :toTime")
    public CSKDTienDoDoanhThuV2Dto findTongDoanhThuCuaChiNhanh(
            @Param("toTime") LocalDate toTime);

    @Query("SELECT cskd.nhomDt as nhomDt, " +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, " +
            "cskd.tlHoanThanh as tlHoanThanh, cskd.tienDo as tienDo , cskd.ttThang as ttThang, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ttNam as ttNam, cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTienDoDoanhThu cskd WHERE cskd.ngayBaoCao = :toTime")
    public List<CSKDTienDoDoanhThuV2Dto> findTongTienDoTheoChiNhanh(
            @Param("toTime") LocalDate toTime);

    @Query("SELECT cskd.tlDoanhThu as tlDoanhThu, cskd.tlHoanThanh as tlHoanThanh, " +
            "cskd.ttThang as ttThang, cskd.ttNam as ttNam, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ngayBaoCao as ngayBaoCao," +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, cskd.tienDo as tienDo, " +
            " cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTongDoanhThu cskd WHERE cskd.ngayBaoCao = :ngayBaoCao")
    public ChiTieuKinhDoanhDto findThongKeChiTieuChatLuong(
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT cskd.nhomDt as nhomDt, " +
            "cskd.keHoach as keHoach, cskd.thucHien as thucHien, " +
            "cskd.tlHoanThanh as tlHoanThanh, cskd.tienDo as tienDo , cskd.ttThang as ttThang, " +
            "cskd.ttTbnThang as ttTbnThang, cskd.ttNam as ttNam, cskd.ttTbnNam as ttTbnNam " +
            "FROM CSKDTienDoDoanhThu cskd " +
            "WHERE cskd.ngayBaoCao = :toTime")
    public List<CSKDTienDoDoanhThuV2Dto> findTienDoDoanhThuTheoNgay(
            @Param("toTime") LocalDate toTime);





}