package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonNhapMayChuaKetNoiResponse;
import nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse;
import nocsystem.indexmanager.models.canhbao.TonThu.AllTonNhapMay;
import nocsystem.indexmanager.models.canhbao.TonThu.TonNhapMayKey;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


@Repository
public interface AllTonChuaNhapMayRepository extends PagingAndSortingRepository<AllTonNhapMay, TonNhapMayKey> {

    @Query("SELECT sum(tnm.tongSl) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tnm.chiNhanh = :maChiNhanh)" +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tnm.maBuuCucGoc = :maBuuCuc) " +
            "AND tnm.status = 1")
    Long tonChuaNhapMay(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);

    @Query("select tp.chiNhanh, tp.maBuuCucGoc " +
            "from AllTonNhapMay tp " +
            "where tp.ngayBaoCao  = :ngayBaoCao")
    Map<String, String> getDistinctBuuCuc(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );


    @Query("select tp.updateAt from AllTonNhapMay tp where tp.ngayBaoCao  = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, tnm.maBuuCucGoc, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and (:dichVu is null or :dichVu = '' or tnm.dichVu = :dichVu) " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh, tnm.maBuuCucGoc")
    Page<TonChuaKetNoiResponse> tonChuaKetNoi(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String dichVu,
            String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and (:dichVu is null or :dichVu = '' or tnm.dichVu = :dichVu) " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh")
    Page<TonChuaKetNoiResponse> tonChuaKetNoiTCT(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String dichVu,
            String loaiDon,
            Pageable page
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, tnm.maBuuCucGoc, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and (:dichVu is null or :dichVu = '' or tnm.dichVu = :dichVu) " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh, tnm.maBuuCucGoc")
    List<TonChuaKetNoiResponse> tonChuaKetNoi(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String dichVu,
            String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and (:dichVu is null or :dichVu = '' or tnm.dichVu = :dichVu) " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh")
    List<TonChuaKetNoiResponse> tonChuaKetNoiTCT(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String dichVu,
            String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, tnm.maBuuCucGoc, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and  tnm.dichVu != 'VCK' " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh, tnm.maBuuCucGoc")
    Page<TonChuaKetNoiResponse> tonChuaKetNoiNonVCK(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and  tnm.dichVu != 'VCK' " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh")
    Page<TonChuaKetNoiResponse> tonChuaKetNoiTCTNonVCK(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String loaiDon,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, tnm.maBuuCucGoc, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and  tnm.dichVu != 'VCK' " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh, tnm.maBuuCucGoc")
    List<TonChuaKetNoiResponse> tonChuaKetNoiNonVCK(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "tnm.chiNhanh, sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and  tnm.dichVu != 'VCK' " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            " AND tnm.status = 1" +
            " group by tnm.chiNhanh")
    List<TonChuaKetNoiResponse> tonChuaKetNoiTCTNonVCK(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and (:dichVu is null or :dichVu = '' or tnm.dichVu = :dichVu) " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            "AND tnm.status = 1")
    TonChuaKetNoiResponse tonChuaKetNoiSum(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String dichVu,
            String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse(" +
            "sum(tnm.tongSl), sum(tnm.slNTC)," +
            "sum(tnm.klChuaKetNoi), sum(tnm.klNTC)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or tnm.chiNhanh in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or tnm.maBuuCucGoc in (:maBuuCuc)) " +
            " and  tnm.dichVu != 'VCK' " +
            " and (:loaiDon is null or :loaiDon = '' or tnm.loaiDon = :loaiDon) " +
            "AND tnm.status = 1")
    TonChuaKetNoiResponse tonChuaKetNoiSumNonVCK(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String loaiDon
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonNhapMayChuaKetNoiResponse(" +
            "tnm.ngayBaoCao,sum(case when tnm.slNTC is not null then tnm.slNTC else 0 end)," +
            "sum(case when tnm.tongSl is not null then tnm.tongSl else 0 end)) " +
            "FROM AllTonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao >= :ngayBatDau and tnm.ngayBaoCao <= :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tnm.chiNhanh = :maChiNhanh)" +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tnm.maBuuCucGoc = :maBuuCuc) " +
            "AND tnm.status = 1 " +
            "GROUP BY tnm.ngayBaoCao " +
            "order by tnm.ngayBaoCao")
    List<TonNhapMayChuaKetNoiResponse> bieuDoTonChuaNhapMay(
            LocalDate ngayBaoCao,
            LocalDate ngayBatDau,
            String maChiNhanh,
            String maBuuCuc
    );

}
