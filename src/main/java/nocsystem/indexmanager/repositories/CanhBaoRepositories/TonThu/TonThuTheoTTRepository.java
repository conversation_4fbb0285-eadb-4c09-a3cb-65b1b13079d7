package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO;
import nocsystem.indexmanager.models.canhbao.TonThu.TonThuTheoKhachHang;
import nocsystem.indexmanager.models.canhbao.TonThu.TonThuTheoTrangThai;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonThuTheoTTRepository extends PagingAndSortingRepository<TonThuTheoTrangThai, Long> {
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan <> 'ALL'" +
            " and tt.buuCucNhan = 'ALL'" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao" +
            " order by tt.chiNhanhNhan, tt.loaiCanhBao")
    Page<TonThuTheoTTResponseDTO> TonThuTheoTTTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan <> 'ALL'" +
            " and tt.buuCucNhan = 'ALL'" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao" +
            " order by tt.chiNhanhNhan, tt.loaiCanhBao")
    Page<TonThuTheoTTResponseDTO> TonThuTheoTTTCT1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("select distinct loaiCanhBao from TonThuTheoTrangThai ")
    List<String> findDistinctByLoaiCanhBao();


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.ca = :ca" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao" +
            " order by tt.chiNhanhNhan, tt.loaiCanhBao")
    Page<TonThuTheoTTResponseDTO> TonThuTheoTTBC(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.buuCucNhan in :buuCucNhan" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.ca = :ca" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao" +
            " order by tt.chiNhanhNhan, tt.loaiCanhBao")
    Page<TonThuTheoTTResponseDTO> TonThuTheoTTBC1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan in :buuCucNhan" +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.buuTaNhan <> 'ALL'" +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao" +
            " order by tt.chiNhanhNhan, tt.loaiCanhBao")
    Page<TonThuTheoTTResponseDTO> TonThuTheoTTBT(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan <> 'ALL'" +
            " and tt.buuCucNhan = 'ALL'" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTheoTTSum(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan <> 'ALL'" +
            " and tt.buuCucNhan = 'ALL'" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTheoTTSum1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTheoTTBCSum(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.buuCucNhan in :buuCucNhan" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTheoTTBCSum1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan in :buuCucNhan" +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao in :loaiCanhBao" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.buuTaNhan <> 'ALL'" +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTheoTTBTSum(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    //luongnv

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan = 'ALL'" +
            " and tt.buuCucNhan = 'ALL'" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> tonThuTTSumNgayBaoCaoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where tt.chiNhanhNhan <> 'ALL'"+
            "and tt.chiNhanhNhan in :chiNhanhNhan"+
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.buuCucNhan in :buuCucNhan" +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> tonThuTTSumNgayBaoCaoNotAdmin(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan =:chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan = 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.status = 1 " +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTTBCChiNhanhAdmin(
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca
    );
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where  tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.status = 1 " +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.buuCucNhan in :buuCucNhan" +
            " and tt.ca = :ca" +
            " and tt.loaiCanhBao <> 'ALL' " +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTTBCChiNhanhNotAdmin(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO(" +
            " tt.ngayBaoCao, tt.chiNhanhNhan, tt.buuCucNhan, " +
            " tt.buuTaNhan, tt.loaiCanhBao,tt.ca, tt.trangThai100, tt.trangThai102, tt.trangThai103, tt.trangThai104, tt.tong) " +
            " from TonThuTheoTrangThai tt " +
            " where tt.chiNhanhNhan in :chiNhanhNhan" +
            " and tt.chiNhanhNhan <> 'ALL' " +
            " and tt.buuCucNhan in :buuCucNhan" +
            " and tt.buuCucNhan <> 'ALL'" +
            " and tt.ca = :ca" +
            " and tt.status = 1 " +
            " and tt.buuTaNhan = 'ALL'" +
            " and tt.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoTTResponseDTO> TonThuTheoTTBTSumBuuCuc(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca
    );

    @Query("select tp.timeTinhToan from TonThuTheoTrangThai tp where tp.ngayBaoCao  = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );
}
