package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto;
import nocsystem.indexmanager.models.canhbao.TonThu.TonNhapMay2;
import nocsystem.indexmanager.models.canhbao.TonThu.TonNhapMayKey;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonChuaNhapMayRepository2 extends PagingAndSortingRepository<TonNhapMay2, TonNhapMayKey> {

    @Query("SELECT count(tnm.maPhieuGui) FROM TonNhapMay2 tnm " +
            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tnm.tinhNhan = :maChiNhanh)" +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tnm.maBuuCucGoc = :maBuuCuc) " +
            "AND tnm.status = 1")
    Long tongTonChuaNhapMay(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);



    //query tổng cty
    @Query("  select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            " TO_CHAR(dbcn.ngayBaoCao, 'YYYY-MM-DD'),dbcn.maPhieuGui,dbcn.tinhNhan,dbcn.huyenNhan," +
            "dbcn.tenHuyenNhan,dbcn.tinhPhat,dbcn.huyenPhat," +
            "dbcn.tenHuyenPhat,dbcn.maDvViettel,dbcn.maBuuCucGoc," +
            "dbcn.timeTacDong,dbcn.trangThai,dbcn.maBuuCucHt,dbcn.chiNhanhHt,dbcn.maDoiTac," +
            "dbcn.maKhGui,dbcn.maBuuCucPhat,dbcn.ngayGuiBP," +
            "dbcn.tienCOD, dbcn.nguoiNhapMay, dbcn.ngayNhapMay, dbcn.loaiHH, dbcn.tienCuoc) " +
            "from TonNhapMay2 dbcn " +
            "WHERE dbcn.ngayBaoCao = :ngayBaoCao " +
            "AND (:maChiNhanh IS NULL OR :maChiNhanh = '' OR dbcn.chiNhanhHt = :maChiNhanh ) " +
            "AND (:maBuuCuc IS NULL OR :maBuuCuc = '' OR dbcn.maBuuCucGoc = :maBuuCuc) " +
            " and (:trangThai is null or :trangThai = '' or dbcn.trangThai = :trangThai) " +
            "and dbcn.status = 1"
    )
    List<DashTonHanhTrinhDto> getDashBoardNhapMay(
            LocalDate ngayBaoCao,
            String maChiNhanh,
            String maBuuCuc,
            String trangThai
    );

    //tổng cty không admin
    @Query("  select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            " TO_CHAR(dbcn.ngayBaoCao, 'YYYY-MM-DD'),dbcn.maPhieuGui,dbcn.tinhNhan,dbcn.huyenNhan," +
            "dbcn.tenHuyenNhan,dbcn.tinhPhat,dbcn.huyenPhat," +
            "dbcn.tenHuyenPhat,dbcn.maDvViettel,dbcn.maBuuCucGoc," +
            "dbcn.timeTacDong,dbcn.trangThai,dbcn.maBuuCucHt,dbcn.chiNhanhHt,dbcn.maDoiTac," +
            "dbcn.maKhGui,dbcn.maBuuCucPhat,dbcn.ngayGuiBP," +
            "dbcn.tienCOD, dbcn.nguoiNhapMay, dbcn.ngayNhapMay, dbcn.loaiHH, dbcn.tienCuoc) " +
            "from TonNhapMay2 dbcn " +
            "WHERE dbcn.ngayBaoCao = :ngayBaoCao " +
            "AND (dbcn.chiNhanhHt in :maChiNhanh ) " +
            "AND (dbcn.maBuuCucGoc in :maBuuCuc) " +
            " and (:trangThai is null or :trangThai = '' or dbcn.trangThai = :trangThai) " +
            "and dbcn.status = 1"
    )
    List<DashTonHanhTrinhDto> getDashBoardNhapMayNoAdmin(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String trangThai
    );

    @Query("  select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            "dbcn.maPhieuGui,dbcn.maBuuCucGoc," +
            "dbcn.maDvViettel,dbcn.trangThai,dbcn.maBuuCucHt," +
            "dbcn.tinhNhan,dbcn.buuTaPhat,dbcn.loaiCanhBao," +
            "dbcn.nhomDV) " +
            "from TonNhapMay2 dbcn " +
            "WHERE dbcn.maPhieuGui = :maPhieuGui " +
            "and dbcn.status = 1"
    )
    DashTonHanhTrinhDto getDetailsTonNhapMay(
            String maPhieuGui
    );

    @Query("select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            " TO_CHAR(dbcn.ngayBaoCao, 'YYYY-MM-DD'),dbcn.maPhieuGui,dbcn.tinhNhan,dbcn.huyenNhan," +
            "dbcn.tenHuyenNhan,dbcn.tinhPhat,dbcn.huyenPhat," +
            "dbcn.tenHuyenPhat,dbcn.maDvViettel, dbcn.maBuuCucGoc," +
            "dbcn.timeTacDong,dbcn.trangThai,dbcn.maBuuCucHt,dbcn.chiNhanhHt,dbcn.maDoiTac," +
            "dbcn.maKhGui,dbcn.maBuuCucPhat," +
            "dbcn.tienCOD, dbcn.nguoiNhapMay, dbcn.ngayNhapMay,dbcn.trongLuong, dbcn.tienCuoc, dbcn.loaiHH, dbcn.maDvCongThem, dbcn.timePs) " +
            "from TonNhapMay2 dbcn " +
            "WHERE dbcn.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or dbcn.chiNhanhHt in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or dbcn.maBuuCucGoc in (:maBuuCuc)) " +
            " and  dbcn.maDvViettel != 'VCK' " +
            " and (:loaiDon is null or :loaiDon = '' or dbcn.loaiDon = :loaiDon) " +
            " AND dbcn.status = 1"
    )
    List<DashTonHanhTrinhDto> getTonChuaKetNoiExcelNonVCK(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String loaiDon
    );

    @Query("select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            " TO_CHAR(dbcn.ngayBaoCao, 'YYYY-MM-DD'),dbcn.maPhieuGui,dbcn.tinhNhan,dbcn.huyenNhan," +
            "dbcn.tenHuyenNhan,dbcn.tinhPhat,dbcn.huyenPhat," +
            "dbcn.tenHuyenPhat,dbcn.maDvViettel, dbcn.maBuuCucGoc," +
            "dbcn.timeTacDong,dbcn.trangThai,dbcn.maBuuCucHt,dbcn.chiNhanhHt,dbcn.maDoiTac," +
            "dbcn.maKhGui,dbcn.maBuuCucPhat," +
            "dbcn.tienCOD, dbcn.nguoiNhapMay, dbcn.ngayNhapMay,dbcn.trongLuong, dbcn.tienCuoc, dbcn.loaiHH, dbcn.maDvCongThem, dbcn.timePs) " +
            "from TonNhapMay2 dbcn " +
            "WHERE dbcn.ngayBaoCao = :ngayBaoCao " +
            " and (COALESCE(:maChiNhanh, NULL) is NULL or dbcn.chiNhanhHt in (:maChiNhanh)) " +
            " and (COALESCE(:maBuuCuc, NULL) is NULL or dbcn.maBuuCucGoc in (:maBuuCuc)) " +
            " and (:dichVu is null or :dichVu = '' or dbcn.maDvViettel = :dichVu) " +
            " and (:loaiDon is null or :loaiDon = '' or dbcn.loaiDon = :loaiDon) " +
            " AND dbcn.status = 1"
    )
    List<DashTonHanhTrinhDto> getTonChuaKetNoiExcelVCK(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String dichVu,
            String loaiDon
    );
}
