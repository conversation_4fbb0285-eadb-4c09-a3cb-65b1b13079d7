package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonNhapMayChuaKetNoiResponse;
import nocsystem.indexmanager.models.canhbao.TonThu.TonNhapMay;
import nocsystem.indexmanager.models.canhbao.TonThu.TonNhapMayKey;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonChuaNhapMayRepository extends PagingAndSortingRepository<TonNhapMay, Long> {

    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonNhapMayChuaKetNoiResponse(" +
            "tnm.ngayBaoCao,sum(case when tnm.type = 1 then tnm.tongSl else 0 end)," +
            "sum(case when tnm.type = 2 then tnm.tongSl else 0 end)) " +
            "FROM TonNhapMay tnm " +
            "WHERE tnm.ngayBaoCao >= :ngayBatDau and tnm.ngayBaoCao <= :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tnm.chiNhanh = :maChiNhanh)" +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tnm.maBuuCuc = :maBuuCuc) " +
            "AND tnm.status = 1 " +
            "GROUP BY tnm.ngayBaoCao " +
            "order by tnm.ngayBaoCao")
    List<TonNhapMayChuaKetNoiResponse> bieuDoTonChuaNhapMay(
            LocalDate ngayBaoCao,
            LocalDate ngayBatDau,
            String maChiNhanh,
            String maBuuCuc
    );

}
