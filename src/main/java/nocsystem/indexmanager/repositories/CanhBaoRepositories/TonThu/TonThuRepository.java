package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu;


import nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuBillResponseDTO;
import nocsystem.indexmanager.models.canhbao.TonThu.TonThuBill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonThuRepository extends PagingAndSortingRepository<TonThuBill, Long> {
    @Query(value = "select tp.time_tinh_toan from tonthu_tonghop_v2 tp where tp.version = :version and  tp.ngay_baocao  = :ngayBaoCao limit 1", nativeQuery = true)
    String getTimeTinhToan(@Param("version") Long version,@Param("ngayBaoCao") LocalDate ngayBaoCao);

    // màn hình listing tồn thu dash CN BC
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
            "tp.chiNhanh, " +
            "sum(case when tp.loaiCanhBao = 'XANH' then tp.tongSL else 0 end) as xanh," +
            "sum(case when tp.loaiCanhBao = 'VANG' then tp.tongSL else 0 end) as vang, " +
            "sum(case when tp.loaiCanhBao = 'DO_1' then tp.tongSL else 0 end) as do1," +
            "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tongSL else 0 end) as do2," +
            "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tongSL else 0 end) as do3," +
            "sum(case when tp.loaiCanhBao is not null then tp.tongSL else 0 end) as tong)" +
            " from TonThu tp " +
            " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanh IN (:chiNhanh))" +
            " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
//            " and tp.status = 1" +
            " and tp.version = :version" +
            " and tp.ca = :ca" +
            " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
            " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
            " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.chiNhanh")
    Page<TonThuDashCNBCResponseDTO> findPageTonThu(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("trangThai") String trangThai,
            @Param("vungCon") String vungCon,
            @Param("ca") String ca,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            String maKhGui,
            Long version
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
            "tp.chiNhanh, tp.vungCon, tp.maBuuCuc, " +
            "sum(case when tp.loaiCanhBao = 'XANH' then tp.tongSL else 0 end) as xanh," +
            "sum(case when tp.loaiCanhBao = 'VANG' then tp.tongSL else 0 end) as vang, " +
            "sum(case when tp.loaiCanhBao = 'DO_1' then tp.tongSL else 0 end) as do1," +
            "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tongSL else 0 end) as do2," +
            "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tongSL else 0 end) as do3," +
            "sum(case when tp.loaiCanhBao is not null then tp.tongSL else 0 end) as tong)" +
            " from TonThu tp " +
            " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanh IN (:chiNhanh))" +
            " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
//            " and tp.status = 1" +
            " and tp.version = :version" +
            " and tp.ca = :ca" +
            " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
            " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
            " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.chiNhanh, tp.vungCon, tp.maBuuCuc")
    Page<TonThuDashCNBCResponseDTO> findPageTonThuCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("trangThai") String trangThai,
            @Param("vungCon") String vungCon,
            @Param("ca") String ca,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            String maKhGui,
            Long version
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
            "tp.chiNhanh, tp.vungCon, tp.maBuuCuc, tp.buuTaNhan, " +
            "sum(case when tp.loaiCanhBao = 'XANH' then tp.tongSL else 0 end) as xanh," +
            "sum(case when tp.loaiCanhBao = 'VANG' then tp.tongSL else 0 end) as vang, " +
            "sum(case when tp.loaiCanhBao = 'DO_1' then tp.tongSL else 0 end) as do1," +
            "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tongSL else 0 end) as do2," +
            "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tongSL else 0 end) as do3," +
            "sum(case when tp.loaiCanhBao is not null then tp.tongSL else 0 end) as tong, " +
            "count (distinct tp.shopTon) as shopTon)" +
            " from TonThu tp " +
            " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanh IN (:chiNhanh))" +
            " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
//            " and tp.status = 1" +
            " and tp.version = :version" +
            " and tp.ca = :ca" +
            " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
            " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
            " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.chiNhanh, tp.vungCon, tp.maBuuCuc, tp.buuTaNhan")
    Page<TonThuDashCNBCResponseDTO> findPageTonThuBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("trangThai") String trangThai,
            @Param("vungCon") String vungCon,
            @Param("ca") String ca,
            Pageable page,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            String maKhGui,
            Long version
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
            "sum(case when tp.loaiCanhBao = 'XANH' then tp.tongSL else 0 end) as xanh," +
            "sum(case when tp.loaiCanhBao = 'VANG' then tp.tongSL else 0 end) as vang, " +
            "sum(case when tp.loaiCanhBao = 'DO_1' then tp.tongSL else 0 end) as do1," +
            "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tongSL else 0 end) as do2," +
            "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tongSL else 0 end) as do3," +
            "sum(case when tp.loaiCanhBao = 'XANH' then tp.tongSL else 0 end) as tong) " +
            " from TonThu tp " +
            " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanh IN (:chiNhanh))" +
            " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
//            " and tp.status = 1" +
            " and tp.version = :version" +
            " and tp.ca = :ca" +
            " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
            " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
            " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonThuDashCNBCResponseDTO> findTonThuSum(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("trangThai") String trangThai,
            @Param("vungCon") String vungCon,
            @Param("ca") String ca,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            String maKhGui,
            Long version
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
            "sum(case when tp.loaiCanhBao = 'XANH' then tp.tongSL else 0 end) as xanh," +
            "sum(case when tp.loaiCanhBao = 'VANG' then tp.tongSL else 0 end) as vang, " +
            "sum(case when tp.loaiCanhBao = 'DO_1' then tp.tongSL else 0 end) as do1," +
            "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tongSL else 0 end) as do2," +
            "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tongSL else 0 end) as do3," +
            "sum(case when tp.loaiCanhBao = 'XANH' then tp.tongSL else 0 end) as tong, " +
            "count (distinct tp.shopTon) as shopTon)" +
            " from TonThu tp " +
            " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanh IN (:chiNhanh))" +
            " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
//            " and tp.status = 1" +
            " and tp.version = :version" +
            " and tp.ca = :ca" +
            " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
            " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
            " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
            " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
            " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
            " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
            " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonThuDashCNBCResponseDTO> findTonThuSumBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("trangThai") String trangThai,
            @Param("vungCon") String vungCon,
            @Param("ca") String ca,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            String maKhGui,
            Long version
    );

}
