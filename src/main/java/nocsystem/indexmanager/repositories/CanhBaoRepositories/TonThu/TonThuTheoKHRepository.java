package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiExcelDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTOSum;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTop10CGTResDTO;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BieuDoTonResult;
import nocsystem.indexmanager.models.canhbao.TonThu.TonThuTheoKhachHang;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonThuTheoKHRepository extends PagingAndSortingRepository<TonThuTheoKhachHang, Long> {
    @Query("SELECT SUM(ttkh.tongSL) FROM TonThu ttkh " +
            "WHERE ttkh.ngayBaoCao = :ngayBaoCao " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or ttkh.chiNhanh = :maChiNhanh) " +
            "AND (:maBuuCuc is null or :maBuuCuc = '' or ttkh.maBuuCuc = :maBuuCuc) " +
            "AND ttkh.ca = :ca " +
//            "AND ttkh.status = 1"
            "AND ttkh.version = :version"
    )
    Long tongTonThu(LocalDate ngayBaoCao,String maChiNhanh,String maBuuCuc,String ca, Long version);

    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BieuDoTonResult(ttkh.loaiCanhBao,SUM(ttkh.tongSL)) FROM TonThu ttkh " +
            "WHERE ttkh.ngayBaoCao = :ngayBaoCao " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or ttkh.chiNhanh = :maChiNhanh) " +
            "AND (:maBuuCuc is null or :maBuuCuc = '' or ttkh.maBuuCuc = :maBuuCuc) " +
            "AND ttkh.ca = :ca " +
//            "AND ttkh.status = 1 " +
            "AND ttkh.version = :version " +
            "GROUP BY ttkh.loaiCanhBao")
    List<BieuDoTonResult> bieuDoTonThu(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc,String ca, Long version);

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL'" +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " order by ttkh.chiNhanhNhan, ttkh.loaiCanhBao")
    Page<TonThuTheoKHResponseDTO> TonThuTheoKHTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL'" +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " order by ttkh.chiNhanhNhan, ttkh.loaiCanhBao")
    Page<TonThuTheoKHResponseDTO> TonThuTheoKHTCT1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("select distinct loaiCanhBao from TonThuTheoKhachHang ")
    List<String> findDistinctByLoaiCanhBao();

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " order by ttkh.chiNhanhNhan, ttkh.loaiCanhBao")
    Page<TonThuTheoKHResponseDTO> TonThuTheoKHBC(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " order by ttkh.chiNhanhNhan, ttkh.loaiCanhBao")
    Page<TonThuTheoKHResponseDTO> TonThuTheoKHBC1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.buuTaNhan <> 'ALL'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " order by ttkh.chiNhanhNhan, ttkh.loaiCanhBao")
    Page<TonThuTheoKHResponseDTO> TonThuTheoKHBT(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL'" +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.status = 1 " +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHSum(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL'" +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHSum1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca" +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHBCSum(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca" +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHBCSum1(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.buuTaNhan <> 'ALL'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHBTSum(
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashTop10TCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashTop10TCT1(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashTop10CN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan " +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashTop10CN1(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan = :buuCucNhan" +
            " and ttkh.buuTaNhan <> 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashTop10BC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("buuCucNhan") String buuCucNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTop10CGTResDTO(" +
            " ttkh.chiNhanhNhan, sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuTaNhan = 'CHUA_GAN_TUYEN'" +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.chiNhanhNhan")
    List<TonThuTop10CGTResDTO> TonThuDashTop10TuyenTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTop10CGTResDTO(" +
            " ttkh.chiNhanhNhan, sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan " +
            " and ttkh.buuTaNhan = 'CHUA_GAN_TUYEN'" +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.chiNhanhNhan")
    List<TonThuTop10CGTResDTO> TonThuDashTop10TuyenTCT1(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTop10CGTResDTO(" +
            " ttkh.buuCucNhan, sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuTaNhan = 'CHUA_GAN_TUYEN'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.buuCucNhan")
    List<TonThuTop10CGTResDTO> TonThuDashTop10TuyenCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTop10CGTResDTO(" +
            " ttkh.buuCucNhan, sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan in :chiNhanhNhan " +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.buuTaNhan = 'CHUA_GAN_TUYEN'" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.buuCucNhan")
    List<TonThuTop10CGTResDTO> TonThuDashTop10TuyenCN1(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ca") String ca
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = 'ALL' " +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca" +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashTCT(
            @Param("ca") String ca,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTOSum(" +
            " ttkh.loaiCanhBao, sum(ttkh.khachHang0), sum(ttkh.khachHang1), sum(ttkh.khachHang2)," +
            " sum(ttkh.khachHang3), sum(ttkh.khachHang4), sum(ttkh.khachHang5), sum(ttkh.khachHang6)," +
            " sum(ttkh.khachHang7), sum(ttkh.khachHang8), sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.ca = :ca" +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao"+
            " group by ttkh.loaiCanhBao")
    List<TonThuTheoKHResponseDTOSum> TonThuTheoKHDashTCT01(
            @Param ("chiNhanhNhan")List<String> chiNhanhNhan,
            @Param("ca") String ca,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL'" +
            " and ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca" +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashCN(
            @Param("ca") String ca,
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTOSum(" +
            " ttkh.loaiCanhBao, sum(ttkh.khachHang0), sum(ttkh.khachHang1), sum(ttkh.khachHang2)," +
            " sum(ttkh.khachHang3), sum(ttkh.khachHang4), sum(ttkh.khachHang5), sum(ttkh.khachHang6)," +
            " sum(ttkh.khachHang7), sum(ttkh.khachHang8), sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.ca = :ca" +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao"+
            " group by ttkh.loaiCanhBao")
    List<TonThuTheoKHResponseDTOSum> TonThuTheoKHDashCN01(
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("ca") String ca,
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL'" +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuCucNhan in :buuCucNhan" +
            " and ttkh.chiNhanhNhan in :chiNhanhNhan" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao <> 'ALL' " +
            " and ttkh.ca = :ca" +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHDashBC(
            @Param("ca") String ca,
            @Param("buuCucNhan") List<String> buuCucNhan,
            @Param("chiNhanhNhan") List<String> chiNhanhNhan,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = 'ALL' " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> getDataNotiTGD(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.buuTaNhan = 'ALL' " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> getDataNotiCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.ngayBaoCao, ttkh.chiNhanhNhan, ttkh.buuCucNhan, " +
            " ttkh.buuTaNhan, ttkh.loaiCanhBao, ttkh.khachHang0, ttkh.khachHang1, ttkh.khachHang2, ttkh.khachHang3, ttkh.khachHang4, ttkh.khachHang5, ttkh.khachHang6, " +
            " ttkh.khachHang7, ttkh.khachHang8, ttkh.tong)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan = :buuCucNhan" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.buuTaNhan = 'ALL' " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuTheoKHResponseDTO> getDataNotiBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("buuCucNhan") String buuCucNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    TonThuTheoKHResponseDTO getDataSMSTGD(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.buuCucNhan, sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.buuTaNhan = 'ALL' " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.buuCucNhan")
    TonThuTheoKHResponseDTO getDataSMSCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.buuTaNhan, sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan = :buuCucNhan" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.buuTaNhan = 'ALL' " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.buuTaNhan")
    TonThuTheoKHResponseDTO getDataSMSBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("buuCucNhan") String buuCucNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiExcelDTO(" +
            " ttkh.chiNhanhNhan, ttkh.tong, ttkh.loaiCanhBao)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuNotiExcelDTO> getDataNotiExcelCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiExcelDTO(" +
            " ttkh.buuCucNhan, ttkh.tong, ttkh.loaiCanhBao)" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuNotiExcelDTO> getDataNotiExcelBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiExcelDTO(" +
            " ttkh.buuTaNhan, ttkh.tong, ttkh.loaiCanhBao)" +
            " from TonThuTheoKhachHang ttkh " +
            " where  ttkh.buuCucNhan = :buuCucNhan" +
            " and ttkh.buuTaNhan <> 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao" +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao")
    List<TonThuNotiExcelDTO> getDataNotiExcelTuyen(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("buuCucNhan") String buuCucNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query(value = "select tp.time_tinh_toan from tonthu_tonghop_v2 tp where tp.version = :version and  tp.ngay_baocao  = :ngayBaoCao limit 1", nativeQuery = true)
    String getTimeTinhToan(@Param("version")long version ,@Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.chiNhanhNhan,  sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan <> 'ALL' " +
            " and ttkh.buuCucNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.status = 1 " +
            " and ttkh.ca = :ca " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.chiNhanhNhan")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHSMSTop10TCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.buuCucNhan, sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan <> 'ALL'" +
            " and ttkh.buuTaNhan = 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.buuCucNhan")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHSMSTop10CN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO(" +
            " ttkh.buuTaNhan,  sum(ttkh.tong))" +
            " from TonThuTheoKhachHang ttkh " +
            " where ttkh.chiNhanhNhan = :chiNhanhNhan " +
            " and ttkh.buuCucNhan = :buuCucNhan" +
            " and ttkh.buuTaNhan <> 'ALL'" +
            " and ttkh.loaiCanhBao in :loaiCanhBao " +
            " and ttkh.ca = :ca " +
            " and ttkh.status = 1 " +
            " and ttkh.ngayBaoCao = :ngayBaoCao" +
            " group by ttkh.buuTaNhan")
    List<TonThuTheoKHResponseDTO> TonThuTheoKHSMSTop10BC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanhNhan") String chiNhanhNhan,
            @Param("buuCucNhan") String buuCucNhan,
            @Param("ca") String ca,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );
}
