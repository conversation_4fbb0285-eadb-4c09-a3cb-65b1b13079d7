package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu;


import nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuBillDashboarResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuBillResponseDTO;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BillDetailThu;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.SanLuongTonBuuTa;
import nocsystem.indexmanager.models.canhbao.TonThu.TonThuBill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonThuBillRepository extends PagingAndSortingRepository<TonThuBill, Long> {

        /**
         * CR 2024-04-08 thêm cột ID PHUONGXA NHAN, ID PHUONGXA NHAN ,ID PHUONGXA PHAT, TEN PHUONGXA PHAT
         * https://confluence.oc.viettelpost.vn/pages/viewpage.action?pageId=1454538771
        */
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuBillResponseDTO(" +
                "tp.ngayBaoCao, tp.maPhieuGui, tp.trangThai, tp.maDoiTac, cast(tp.ngayTao as string ), " +
                "tp.ngayDuyet,tp.ngayHenThu,tp.tinhKHGui,tp.tenKHGui,tp.diaChiKHGui," +
                "tp.tinhKHNhan,tp.maBuuCuc,tp.chiNhanh,tp.maDVViettel,tp.tongTien,tp.buuTaNhan," +
                "tp.tenBuuTa,tp.tgTon,tp.ngayTon,tp.loaiCanhBao, tp.nhomDV, tp.timeTinhToan, " +
                "(case when tp.khDacThuGui = '1' then 'SVIP1, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when tp.khDacThuGui = '2' then 'SVIP2, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when tp.khDacThuGui = '3' then 'VIP-NG, KH đối ngoại, ngoại giao' " +
                "when tp.khDacThuGui = '4' then 'VIP-KHL, KH thân thiết doanh thu lớn' " +
                "when tp.khDacThuGui = '5' then 'VIP-KHHT, KH hệ thống, sàn TMĐT' " +
                "when tp.khDacThuGui = '6' then 'ĐT-MXH, KH đặc thù - Báo chí, mạng xã hội' " +
                "when tp.khDacThuGui = '7' then 'ĐT-KN, KH đặc thù, khiếu nại' " +
                "else tp.khDacThuGui end), " +
                "(case when tp.khDacThuNhan = '1' then 'SVIP1, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when tp.khDacThuNhan = '2' then 'SVIP2, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when tp.khDacThuNhan = '3' then 'VIP-NG, KH đối ngoại, ngoại giao' " +
                "when tp.khDacThuNhan = '4' then 'VIP-KHL, KH thân thiết doanh thu lớn' " +
                "when tp.khDacThuNhan = '5' then 'VIP-KHHT, KH hệ thống, sàn TMĐT' " +
                "when tp.khDacThuNhan = '6' then 'ĐT-MXH, KH đặc thù - Báo chí, mạng xã hội' " +
                "when tp.khDacThuNhan = '7' then 'ĐT-KN, KH đặc thù, khiếu nại' " +
                "else tp.khDacThuNhan end), " +
                "tp.maKhGui,tp.idPhuongXaNhan,tp.tenPhuongXaNhan, tp.loaiDon, tp.tenQuanHuyenNhan, tp.maDichVuCongThem, " +
                "tp.time102CuoiCung, tp.soLan102LuyKe, tp.soLan102TrongNgay)" +
                " from TonThuBill tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanh IN (:chiNhanh)) " +
                " AND (COALESCE(:buuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:buuCuc)) " +
                " AND (COALESCE(:vungCon, NULL) IS NULL OR tp.vungCon IN (:vungCon)) " +
//                " and tp.status = 1" +
                " and tp.version = :version" +
                " and tp.ca = :ca" +
                " AND (COALESCE(:loaiCanhBao, NULL) IS NULL OR tp.loaiCanhBao IN (:loaiCanhBao)) " +
                " AND (COALESCE(:buuTaNhan, NULL) IS NULL OR tp.tenBuuTa IN (:buuTaNhan)) " +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
                " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
                " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
                " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
                " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuBillResponseDTO> findTonTTKTBill(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("vungCon") String vungCon,
                @Param("buuCuc") List<String> buuCuc,
                @Param("buuTaNhan") List<String> buuTaNhan,
                @Param("loaiCanhBao") List<String> loaiCanhBao,
                @Param("trangThai") String trangThai,
                @Param("ca") String ca,
                @Param("maDoiTac") String maDoiTac,
                @Param("loaiHH") String loaiHH,
                String khDacThuGui,
                String khDacThuNhan,
                String maKhGui,
                Long version
        );

        @Query("select tp.timeTinhToan from TonThuBill tp where tp.ngayBaoCao  = :ngayBaoCao")
        Page<String> getTimeTinhToan(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable page
        );

        @Query("select tp.timeTinhToan from TonThuTheoKhachHang tp where tp.ngayBaoCao  = :ngayBaoCao")
        Page<String> getTimeTinhToan2(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable page
        );

        // màn hình listing tồn thu dash CN BC
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.tinhKHGui, sum(case when tp.loaiCanhBao = 'XANH' then 1 else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then 1 else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then 1 else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then 1 else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then 1 else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao is not null then 1 else 0 end) as tong)" +
                " from TonThuBill tp " +
                " where (:chiNhanh is null or :chiNhanh = '' or tp.tinhKHGui = :chiNhanh)" +
                " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCuc = :maBuuCuc)" +
//                " and tp.status = 1" +
                " and tp.version = :version" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.tinhKHGui")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuBillTCT(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("ca") String ca,
                Pageable page,
                Long version
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.chiNhanhNhan, " +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhNhan = :chiNhanh)" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan = 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.chiNhanhNhan")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuTCT(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("ca") String ca,
                Pageable page
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanhNhan = :chiNhanh)" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan = 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuTCTSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("ca") String ca
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (tp.chiNhanhNhan = :chiNhanh)" +
                " and (:maBuuCuc is null or :maBuuCuc = '' or tp.buuCucNhan = :maBuuCuc)" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan <> 'ALL' " +
                " and tp.buuTaNhan = 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuCNSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("ca") String ca
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.tinhKHGui, tp.maBuuCuc, tp.tenBuuTa, sum(case when tp.loaiCanhBao = 'XANH' then 1 else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then 1 else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then 1 else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then 1 else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then 1 else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao is not null then 1 else 0 end) as tong)" +
                " from TonThuBill tp " +
                " where (:chiNhanh is null or :chiNhanh = '' or tp.tinhKHGui = :chiNhanh)" +
                " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCuc = :maBuuCuc)" +
//                " and tp.status = 1" +
                " and tp.version = :version" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.tinhKHGui, tp.maBuuCuc, tp.tenBuuTa")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuBillBC(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("ca") String ca,
                Pageable page,
                Long version
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.chiNhanhNhan, tp.buuCucNhan, tp.buuTaNhan," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (tp.chiNhanhNhan = :chiNhanh)" +
                " and (tp.buuCucNhan = :maBuuCuc)" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan <> 'ALL' " +
                " and tp.buuTaNhan <> 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.chiNhanhNhan, tp.buuCucNhan, tp.buuTaNhan")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuBC(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("ca") String ca,
                Pageable page
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (tp.chiNhanhNhan = :chiNhanh)" +
                " and (tp.buuCucNhan = :maBuuCuc)" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan <> 'ALL' " +
                " and tp.buuTaNhan <> 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuBCSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("ca") String ca
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.tinhKHGui,  sum(case when tp.loaiCanhBao = 'XANH' then 1 else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then 1 else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then 1 else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then 1 else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then 1 else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao is not null then 1 else 0 end) as tong)" +
                " from TonThuBill tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.tinhKHGui IN (:chiNhanh))" +
                " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
//                " and tp.status = 1" +
                " and tp.version = :version" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.tinhKHGui")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuBillNoAdmin(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("maBuuCuc") List<String> maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("ca") String ca,
                Pageable page,
                Long version
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.chiNhanhNhan, " +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanhNhan IN (:chiNhanh))" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan = 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.chiNhanhNhan")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuTCTNoAdmin(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ca") String ca,
                Pageable page
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanhNhan IN (:chiNhanh))" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan = 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuTCTNoAdminSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ca") String ca
        );


        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (tp.chiNhanhNhan in :chiNhanh)" +
                " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.buuCucNhan IN (:maBuuCuc))" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan <> 'ALL' " +
                " and tp.buuTaNhan = 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuCNNoAdminSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("maBuuCuc") List<String> maBuuCuc,
                @Param("ca") String ca
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.tinhKHGui, tp.maBuuCuc, tp.tenBuuTa, sum(case when tp.loaiCanhBao = 'XANH' then 1 else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then 1 else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then 1 else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then 1 else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then 1 else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao is not null then 1 else 0 end) as tong)" +
                " from TonThuBill tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.tinhKHGui IN (:chiNhanh))" +
                " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.tinhKHGui, tp.maBuuCuc, tp.tenBuuTa")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuBillNoAdminBC(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("maBuuCuc") List<String> maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("ca") String ca,
                Pageable page
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "tp.chiNhanhNhan, tp.buuCucNhan, tp.buuTaNhan," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (tp.chiNhanhNhan in :chiNhanh)" +
                " and (tp.buuCucNhan in :maBuuCuc)" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan <> 'ALL' " +
                " and tp.buuTaNhan <> 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " group by tp.chiNhanhNhan, tp.buuCucNhan, tp.buuTaNhan")
        Page<TonThuDashCNBCResponseDTO> findPageTonThuBCNoAdmin(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("maBuuCuc") List<String> maBuuCuc,
                @Param("ca") String ca,
                Pageable page
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then tp.tong else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then tp.tong else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then tp.tong else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then tp.tong else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao = 'XANH' then tp.tong else 0 end) as tong)" +
                " from TonThuTheoKhachHang tp " +
                " where (tp.chiNhanhNhan in :chiNhanh)" +
                " and (tp.buuCucNhan in :maBuuCuc)" +
                " and tp.chiNhanhNhan <> 'ALL' " +
                " and tp.buuCucNhan <> 'ALL' " +
                " and tp.buuTaNhan <> 'ALL' " +
                " and tp.loaiCanhBao <> 'ALL' " +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuBCNoAdminSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("maBuuCuc") List<String> maBuuCuc,
                @Param("ca") String ca
        );

        //lấy tổng
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then 1 else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then 1 else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then 1 else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then 1 else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then 1 else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao is not null then 1 else 0 end) as tong)" +
                " from TonThuBill tp " +
                " where (:chiNhanh is null or :chiNhanh = '' or tp.tinhKHGui = :chiNhanh)" +
                " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCuc = :maBuuCuc)" +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuBillTCTSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("ca") String ca
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO(" +
                "sum(case when tp.loaiCanhBao = 'XANH' then 1 else 0 end) as xanh," +
                "sum(case when tp.loaiCanhBao = 'VANG' then 1 else 0 end) as vang, sum(case when tp.loaiCanhBao = 'DO_1' then 1 else 0 end) as do1," +
                "sum(case when tp.loaiCanhBao = 'DO_2' then 1 else 0 end) as do2," +
                "sum(case when tp.loaiCanhBao = 'DO_3' then 1 else 0 end) as do3," +
                "sum(case when tp.loaiCanhBao is not null then 1 else 0 end) as tong)" +
                " from TonThuBill tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.tinhKHGui IN (:chiNhanh))" +
                " and (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc))" +
                " and tp.status = 1" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonThuDashCNBCResponseDTO> findPageTonThuBillSumNoAdmin(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("maBuuCuc") List<String> maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("ca") String ca
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuBillDashboarResponseDTO(" +
                "tp.maPhieuGui, tp.trangThai, tp.tinhKHGui, tp.vungCon," +
                "tp.maBuuCuc,tp.tenBuuTa,tp.loaiCanhBao, cast(tp.ngayTao as string))" +
                " from TonThuBill tp " +
                " where (:chiNhanh is null or :chiNhanh = '' or tp.chiNhanh = :chiNhanh)" +
                " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCuc = :maBuuCuc)" +
//                " and tp.status = 1" +
                " and tp.version = :version" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and (:loaiCanhBao is null or :loaiCanhBao = '' or tp.loaiCanhBao = :loaiCanhBao)" +
                " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
                " and (:buuTa is null or :buuTa = '' or tp.tenBuuTa = :buuTa)" +
                " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
                " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
                " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
                " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
                " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        Page<TonThuBillDashboarResponseDTO> tonThuChiTiet(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("vungCon") String vungCon,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("loaiCanhBao") String loaiCanhBao,
                @Param("buuTa") String buuTa,
                @Param("ca") String ca,
                Pageable page,
                String maDoiTac,
                String loaiHH,
                String khDacThuGui,
                String khDacThuNhan,
                String maKhGui,
                Long version
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuBillDashboarResponseDTO(" +
                "tp.maPhieuGui, tp.trangThai, tp.tinhKHGui, tp.vungCon, " +
                "tp.maBuuCuc,tp.tenBuuTa,tp.loaiCanhBao, cast(tp.ngayTao as string))" +
                " from TonThuBill tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanh IN (:chiNhanh)) " +
                " AND (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc)) " +
//                " and tp.status = 1" +
                " and tp.version = :version" +
                " and tp.ca = :ca" +
                " and (:trangThai is null or :trangThai = '' or tp.trangThai = :trangThai)" +
                " and (:loaiCanhBao is null or :loaiCanhBao = '' or tp.loaiCanhBao = :loaiCanhBao)" +
                " and (:vungCon is null or :vungCon = '' or tp.vungCon = :vungCon)" +
                " and (:buuTa is null or :buuTa = '' or tp.tenBuuTa = :buuTa)" +
                " and (:maDoiTac is null or :maDoiTac = '' or tp.maDoiTac = :maDoiTac)" +
                " and (:loaiHH is null or :loaiHH = '' or tp.loaiHH = :loaiHH)" +
                " and (:khDacThuGui is null or :khDacThuGui = '' or tp.khDacThuGui = :khDacThuGui)" +
                " and (:khDacThuNhan is null or :khDacThuNhan = '' or tp.khDacThuNhan = :khDacThuNhan)" +
                " and (:maKhGui is null or :maKhGui = '' or tp.maKhGui = :maKhGui)" +
                " and tp.ngayBaoCao = :ngayBaoCao")
        Page<TonThuBillDashboarResponseDTO> tonThuChiTietNoAdmin(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("vungCon") String vungCon,
                @Param("maBuuCuc") List<String> maBuuCuc,
                @Param("trangThai") String trangThai,
                @Param("loaiCanhBao") String loaiCanhBao,
                @Param("buuTa") String buuTa,
                @Param("ca") String ca,
                Pageable page,
                String maDoiTac,
                String loaiHH,
                String khDacThuGui,
                String khDacThuNhan,
                String maKhGui,
                Long version
        );

        @Query("SELECT distinct tp.tenBuuTa" +
                " from TonThuBill tp " +
                " where  tp.tinhKHGui = :chiNhanh " +
                " AND tp.maBuuCuc = :maBuuCuc " +
//                " and tp.status = 1" +
                " and tp.version = :version" +
                " and tp.ca = :ca" +
                " and tp.ngayBaoCao = :ngayBaoCao" +
                " order by tp.tenBuuTa")
        List<String> listBuuTaTonThu(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") String chiNhanh,
                @Param("maBuuCuc") String maBuuCuc,
                @Param("ca") String ca,
                Long version
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuBillResponseDTO(" +
                "tp.ngayBaoCao, tp.maPhieuGui, tp.trangThai, tp.maDoiTac, tp.khVIP, cast(tp.ngayTao as string ), " +
                "tp.ngayDuyet,tp.ngayHenThu,tp.tinhKHGui,tp.tenKHGui,tp.diaChiKHGui," +
                "tp.tinhKHNhan,tp.maBuuCuc,tp.chiNhanh,tp.maDVViettel,tp.tongTien,tp.buuTaNhan," +
                "tp.tenBuuTa,tp.tgTon,tp.ngayTon,tp.loaiCanhBao, tp.nhomDV, tp.timeTinhToan, tp.khDacThuGui, tp.khDacThuNhan, tp.maKhGui,tp.idPhuongXaNhan,tp.tenPhuongXaNhan)" +
                " from TonThuBill tp " +
                " where tp.maPhieuGui = :maPhieuGui" +
                " and tp.status = 1" +
                " and tp.ca = :ca")
        TonThuBillResponseDTO findBill(
                @Param("maPhieuGui") String maPhieuGui,
                @Param("ca") String ca
        );


//        @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.SanLuongTonBuuTa(" +
//                " sum(case when loaiCanhBao in ('DO_1','DO_2','DO_3') then 1 else 0 end)," +
//                "sum(case when loaiCanhBao in ('XANH', 'VANG') then 1 else 0 end)) " +
//                "from TonThuBill " +
//                "where userId = :buuTa " +
//                "and ca = :ca " +
////                "and status = 1"
//                "and version = :version"
//        )
//        SanLuongTonBuuTa findTonThuBuuTa(
//                @Param("buuTa") String buuTa,
//                @Param("ca") String ca
//                , Long version
//        );

//        @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BillDetailThu(tonBill.maPhieuGui, tonBill.ngayTao, tonBill.ngayHenThu) " +
//                "from TonThuBill as tonBill " +
//                "where tonBill.loaiCanhBao in ('DO_1','DO_2','DO_3') " +
//                "and tonBill.userId = :buuTa " +
//                "and tonBill.ca = :ca " +
////                "and tonBill.status = 1"
//                "and tonBill.version = :version"
//        )
//        Page<BillDetailThu> findAllBillQuaHanThuBuuTa(
//                @Param("buuTa") String buuTa,
//                @Param("ca") String ca,
//                Pageable pageable
//        );

//        @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BillDetailThu(tonBill.maPhieuGui, tonBill.ngayTao, tonBill.ngayHenThu) " +
//                "from TonThuBill as tonBill " +
//                "where tonBill.loaiCanhBao in ('XANH', 'VANG') " +
//                "and tonBill.userId = :buuTa " +
//                "and tonBill.ca = :ca " +
////                "and tonBill.status = 1"
//                "and tonBill.version = :version"
//        )
//        Page<BillDetailThu> findAllBillDenHanThuBuuTa(
//                @Param("buuTa") String buuTa,
//                @Param("ca") String ca,
//                Pageable pageable,
//                Long version
//        );
}
