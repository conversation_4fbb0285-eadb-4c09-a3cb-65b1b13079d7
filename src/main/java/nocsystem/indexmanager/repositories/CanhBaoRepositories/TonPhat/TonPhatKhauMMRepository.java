package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMLoaiTonDashDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatKhauMM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonPhatKhauMMRepository extends PagingAndSortingRepository<TonPhatKhauMM, Long> {
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatKhauMMResponseDTO> findTonPhatKhauMMTCT(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and (tp.tinhPhat in :tinhPhat) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatKhauMMResponseDTO> findTonPhatKhauMMTCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatKhauMMResponseDTO> findTonPhatKhauMMCN(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatKhauMMResponseDTO> findTonPhatKhauMMCN1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where (tp.tinhPhat in :tinhPhat)  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <>'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatKhauMMResponseDTO> findTonPhatKhauMMBC(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauMMResponseDTO> findTonPhatKhauMMTCTSum(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.tinhPhat in :tinhPhat) " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauMMResponseDTO> findTonPhatKhauMMTCTSum1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauMMResponseDTO> findTonPhatKhauMMCNSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauMMResponseDTO> findTonPhatKhauMMCNSum1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO (" +
            " tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.loaiCanhBao, tp.dGMocMM, tp.maBuuCuc, tp.buuTaPhat, " +
            " tp.sLTPNhanh, tp.sLTPCham, tp.sLTPHoaToc, tp.tongSL )" +
            " from TonPhatKhauMM tp " +
            " where (tp.tinhPhat in :tinhPhat)  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <>'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.dGMocMM in :dGMocMM) " +
            " and tp.dGMocMM <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauMMResponseDTO> findTonPhatKhauMMBCSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO (" +
            " tp.tinhPhat, sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.dgMocMM in :dGMocMM)" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.status = 1" +
            " group by tp.tinhPhat")
    List<TonPhatKhauMMMainDashResDTO> findTonPhatKhauMMDashMainTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO (" +
            " tp.tinhPhat, sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat in :tinhPhat" +
            " and tp.maBuuCuc = 'ALL'" +
            " and (tp.dgMocMM in :dGMocMM)" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.tinhPhat")
    List<TonPhatKhauMMMainDashResDTO> findTonPhatKhauMMDashMainTCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO (" +
            " tp.tinhPhat, sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and (tp.dgMocMM in :dGMocMM)" +
            " and tp.status = 1" +
            " group by tp.tinhPhat")
    List<TonPhatKhauMMMainDashResDTO> findTonPhatKhauMMDashMainCN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO (" +
            " tp.tinhPhat, sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.dgMocMM in :dGMocMM)" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " group by tp.tinhPhat")
    List<TonPhatKhauMMMainDashResDTO> findTonPhatKhauMMDashMainCN1(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") List<String> dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO (" +
            " tp.maBuuCuc, sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = :maBuuCuc" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and (tp.dgMocMM in :dGMocMM)" +
            " and tp.status = 1" +
            " group by tp.maBuuCuc")
    List<TonPhatKhauMMMainDashResDTO> findTonPhatKhauMMDashMainBC(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") List<String> dGMocMM
    );


    // Loại tồn dashboard
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMLoaiTonDashDTO (" +
            "  tp.loaiTon, tp.nhomDV, " +
            " sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL'" +
            " and (:dgMocMM is null or :dgMocMM = '' or tp.dgMocMM = :dgMocMM)" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.loaiTon, tp.nhomDV")
    List<TonPhatKhauMMLoaiTonDashDTO> findTonPhatKhauMMLoaiTonTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            @Param("loaiTon") String loaiTon,
            @Param("dgMocMM") String dgMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMLoaiTonDashDTO (" +
            " tp.loaiTon, tp.nhomDV, sum(tp.tongSL)) " +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat in :tinhPhat" +
            " and (:dgMocMM is null or :dgMocMM = '' or tp.dgMocMM = :dgMocMM)" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by  tp.loaiTon, tp.nhomDV")
    List<TonPhatKhauMMLoaiTonDashDTO> findTonPhatKhauMMLoaiTonTCTNoAdmin(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            @Param("loaiTon") String loaiTon,
            @Param("dgMocMM") String dgMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMLoaiTonDashDTO (" +
            "  tp.loaiTon, tp.nhomDV, sum(tp.tongSL)) " +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and (:dgMocMM is null or :dgMocMM = '' or tp.dgMocMM = :dgMocMM)" +
            " group by tp.loaiTon, tp.nhomDV")
    List<TonPhatKhauMMLoaiTonDashDTO> findTonPhatKhauMMLoaiTonCN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            @Param("loaiTon") String loaiTon,
            @Param("dgMocMM") String dgMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMLoaiTonDashDTO (" +
            "  tp.loaiTon, tp.nhomDV, sum(tp.tongSL)) " +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and (:dgMocMM is null or :dgMocMM = '' or tp.dgMocMM = :dgMocMM)" +
            " group by tp.loaiTon, tp.nhomDV")
    List<TonPhatKhauMMLoaiTonDashDTO> findTonPhatKhauMMLoaiTonCNNoAdmin(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dgMocMM") String dgMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMLoaiTonDashDTO (" +
            "  tp.loaiTon, tp.nhomDV, sum(tp.tongSL)) " +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = :maBuuCuc" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
//            " and tp.loaiTon = :loaiTon" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and (:dgMocMM is null or :dgMocMM = '' or tp.dgMocMM = :dgMocMM)" +
            " group by tp.loaiTon, tp.nhomDV")
    List<TonPhatKhauMMLoaiTonDashDTO> findTonPhatKhauMMLoaiTonBC(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            @Param("loaiTon") String loaiTon,
            @Param("dgMocMM") String dgMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO (" +
            " tp.tinhPhat, sum(case when tp.nhomDV = 'DV_CHAM' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'DV_NHANH' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'HOA_TOC' then tp.tongSL else 0 end), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL'" +
            " and (:dGMocMM is null or :dGMocMM = '' or tp.dgMocMM = :dGMocMM)" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.tinhPhat" +
            " order by tp.tinhPhat")
    List<TonPhatKhauMMTop15DashResDTO> findTonPhatKhauMMTop15TCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") String dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO (" +
            " tp.tinhPhat, sum(case when tp.nhomDV = 'DV_CHAM' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'DV_NHANH' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'HOA_TOC' then tp.tongSL else 0 end), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat in :tinhPhat" +
            " and tp.maBuuCuc in :buuCuc" +
            " and (:dGMocMM is null or :dGMocMM = '' or tp.dgMocMM = :dGMocMM)" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.tinhPhat" +
            " order by tp.tinhPhat")
    List<TonPhatKhauMMTop15DashResDTO> findTonPhatKhauMMTop15TCTNoAdmin(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("buuCuc") List<String> buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") String dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO (" +
            " tp.maBuuCuc, sum(case when tp.nhomDV = 'DV_CHAM' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'DV_NHANH' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'HOA_TOC' then tp.tongSL else 0 end), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and (:dGMocMM is null or :dGMocMM = '' or tp.dgMocMM = :dGMocMM)" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maBuuCuc" +
            " order by tp.maBuuCuc")
    List<TonPhatKhauMMTop15DashResDTO> findTonPhatKhauMMTop15CN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") String dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO (" +
            " tp.maBuuCuc, sum(case when tp.nhomDV = 'DV_CHAM' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'DV_NHANH' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'HOA_TOC' then tp.tongSL else 0 end), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and (:dGMocMM is null or :dGMocMM = '' or tp.dgMocMM = :dGMocMM)" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maBuuCuc" +
            " order by tp.maBuuCuc")
    List<TonPhatKhauMMTop15DashResDTO> findTonPhatKhauMMTop15CNNoAdmin(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") String dGMocMM
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO (" +
            " tp.buuTaPhat, sum(case when tp.nhomDV = 'DV_CHAM' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'DV_NHANH' then tp.tongSL else 0 end), " +
            " sum(case when tp.nhomDV = 'HOA_TOC' then tp.tongSL else 0 end), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and (:dGMocMM is null or :dGMocMM = '' or tp.dgMocMM = :dGMocMM)" +
            " and tp.maBuuCuc = :maBuuCuc" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.dgMocMM <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTaPhat" +
            " order by tp.buuTaPhat")
    List<TonPhatKhauMMTop15DashResDTO> findTonPhatKhauMMTop15BC(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("dGMocMM") String dGMocMM
    );

}
