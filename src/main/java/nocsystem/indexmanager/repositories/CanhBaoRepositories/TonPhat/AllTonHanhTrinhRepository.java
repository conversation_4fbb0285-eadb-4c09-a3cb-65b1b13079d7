package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.canhbao.TonPhat.TonHanhTrinhKey;
import nocsystem.indexmanager.models.canhbao.TonThu.AllTonHanhTrinh;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface AllTonHanhTrinhRepository extends PagingAndSortingRepository<AllTonHanhTrinh, TonHanhTrinhKey> {

    @Query("SELECT sum(tp.tongSl) FROM AllTonHanhTrinh tp " +
            "WHERE tp.ngayBaoCao = :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tp.tinhNhan = :maChiNhanh) " +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCucGoc = :maBuuCuc) " +
            "AND tp.status = 1")
    Long tongTonHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);

    @Query("select tp.updateAt from AllTonHanhTrinh tp where tp.ngayBaoCao  = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );

//    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonDto(" +
//            "tnm.tinhNhan, tnm.maBuuCucGoc, tnm.tongSl, tnm.type) " +
//            "FROM AllTonHanhTrinh tnm " +
//            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
//            " and (:maChiNhanh is null or :maChiNhanh = '' or tnm.tinhNhan = :maChiNhanh)" +
//            " and (:maBuuCuc is null or :maBuuCuc = '' or tnm.maBuuCucGoc = :maBuuCuc) " +
//            "AND tnm.status = 1")
//    List<TongHopTonDto> tonHanhTrinhTheoBuuCuc(
//            LocalDate ngayBaoCao,
//            String maChiNhanh,
//            String maBuuCuc
//    );

    @Query("select tp.maBuuCucGoc " +
            "from AllTonHanhTrinh tp " +
            "where tp.ngayBaoCao  = :ngayBaoCao")
    List<String> getDistinctBuuCuc(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );


//    public List<?> queryForMovies() {
//        EntityManagerFactory entityManagerFactory = Persistence.createEntityManagerFactory("persistence");
//        EntityManager em = entityManagerFactory.createEntityManager();
//        List<?> movies = em.createQuery("select d.tinh_nhan, d.ma_buucuc_goc, sum(case when d.type = 1 then d.tong_sl else 0 end) as hanhtrinh," +
//                        "sum(case when d.type = 2 then d.tong_sl else 0 end) as nhapmay," +
//                        "sum(case when d.type = 3 then d.tong_sl else 0 end) as chuapcp" +
//                        "from ((select *from all_ton_nhap_may_chua_ket_noi a) " +
//                        "union all (select * from all_ton_chua_pcp b) " +
//                        "union all (select *from all_ton_hanh_trinh_cuoi_thu c)) d" +
//                        "where d.ngay_baocao = '2023-07-15'" +
//                        "group by d.tinh_nhan, d.ma_buucuc_goc")
//                .setParameter(1, "English")
//                .getResultList();
//        return movies;
//    }

//    @Query("SELECT COALESCE (A.tinhNhan ,B.tinhNhan,C.tinhNhan ) as TINH_NHAN " +
//            ",COALESCE(A.maBuuCucGoc,B.maBuuCucGoc,C.maBuuCucGoc) as MA_BC " +
//            ", A.tongSL as tongsl_hanhtrinhcuoithu, B.tongSL as tongsl_chuapcp,C.tongSL as tongsl_nhapmay" +
//            "FROM (select * from AllTonHanhTrinh where ngayBaoCao = :ngayBaoCao) as A" +
//            "         FULL OUTER JOIN (select * from AllTonChuaPCP where ngayBaoCao = :ngayBaoCao) as B  ON A.maBuuCucGoc = B.maBuuCucGoc" +
//            "         FULL OUTER JOIN (select * from allTonNhapMay where ngayBaoCao = :ngayBaoCao) as C  ON A.maBuuCucGoc = C.maBuuCucGoc")
//    public Page<TongHopTonResponse> findResult(
//            @Param("ngayBaoCao") LocalDate ngayBaoCao,
//            Pageable page
//    );

}
