package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonHanhTrinh;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonHanhTrinhKey;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonHanhTrinhRepository extends PagingAndSortingRepository<TonHanhTrinh, TonHanhTrinhKey> {

    @Query("SELECT count(tp.maPhieuGui) FROM TonHanhTrinh tp " +
            "WHERE tp.ngayBaoCao = :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tp.tinhNhan = :maChiNhanh) " +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCucGoc = :maBuuCuc) " +
            "AND tp.status = 1")
    Long tongTonHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);


    //query tổng cty
    @Query("  select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            " TO_CHAR(dbcn.ngayBaoCao, 'YYYY-MM-DD'),dbcn.maPhieuGui,dbcn.tinhNhan,dbcn.huyenNhan," +
            "dbcn.tenHuyenNhan,dbcn.tinhPhat,dbcn.huyenPhat," +
            "dbcn.tenHuyenPhat,dbcn.maDvViettel,dbcn.maBuuCucGoc," +
            "dbcn.timeTacDong,dbcn.trangThai,dbcn.maBuuCucHt,dbcn.chiNhanhHt,dbcn.maDoiTac," +
            "dbcn.maKhGui,dbcn.maBuuCucPhat,dbcn.ngayGuiBP," +
            "dbcn.tienCOD, dbcn.nguoiNhapMay, dbcn.ngayNhapMay, dbcn.loaiHH, dbcn.tienCuoc) " +
            "from TonHanhTrinh dbcn " +
            "WHERE dbcn.ngayBaoCao = :ngayBaoCao " +
            "AND (:maChiNhanh IS NULL OR :maChiNhanh = '' OR dbcn.tinhNhan = :maChiNhanh ) " +
            "AND (:maBuuCuc IS NULL OR :maBuuCuc = '' OR dbcn.maBuuCucGoc = :maBuuCuc)" +
            " and (:trangThai is null or :trangThai = '' or dbcn.trangThai = :trangThai) " +
            "and dbcn.status = 1 "
    )
    List<DashTonHanhTrinhDto> getDashBoardHanhTrinh(
            LocalDate ngayBaoCao,
            String maChiNhanh,
            String maBuuCuc,
            String trangThai
    );

    //tổng cty không admin
    @Query("  select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            " TO_CHAR(dbcn.ngayBaoCao, 'YYYY-MM-DD'),dbcn.maPhieuGui,dbcn.tinhNhan,dbcn.huyenNhan," +
            "dbcn.tenHuyenNhan,dbcn.tinhPhat,dbcn.huyenPhat," +
            "dbcn.tenHuyenPhat,dbcn.maDvViettel,dbcn.maBuuCucGoc," +
            "dbcn.timeTacDong,dbcn.trangThai,dbcn.maBuuCucHt,dbcn.chiNhanhHt,dbcn.maDoiTac," +
            "dbcn.maKhGui,dbcn.maBuuCucPhat,dbcn.ngayGuiBP," +
            "dbcn.tienCOD, dbcn.nguoiNhapMay, dbcn.ngayNhapMay, dbcn.loaiHH, dbcn.tienCuoc) " +
            "from TonHanhTrinh dbcn " +
            "WHERE dbcn.ngayBaoCao = :ngayBaoCao " +
            "AND dbcn.tinhNhan in :maChiNhanh " +
            "AND  dbcn.maBuuCucGoc in :maBuuCuc " +
            " and (:trangThai is null or :trangThai = '' or dbcn.trangThai = :trangThai) " +
            "and dbcn.status = 1"
    )
    List<DashTonHanhTrinhDto> getDashBoardHanhTrinhNoAdmin(
            LocalDate ngayBaoCao,
            List<String> maChiNhanh,
            List<String> maBuuCuc,
            String trangThai
    );

    //tổng cty không admin
    @Query("  select new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto(" +
            "dbcn.maPhieuGui,dbcn.maBuuCucGoc," +
            "dbcn.maDvViettel,dbcn.trangThai,dbcn.maBuuCucHt," +
            "dbcn.tinhNhan,dbcn.buuTaPhat,dbcn.loaiCanhBao," +
            "dbcn.nhomDV) " +
            "from TonHanhTrinh dbcn " +
            "WHERE dbcn.maPhieuGui = :maPhieuGui " +
            "and dbcn.status = 1"
    )
    DashTonHanhTrinhDto getDetailHanhTrinh(
            String maPhieuGui
    );

}
