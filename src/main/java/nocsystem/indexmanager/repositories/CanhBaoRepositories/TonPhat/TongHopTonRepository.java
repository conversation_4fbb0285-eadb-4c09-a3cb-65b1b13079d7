package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonResponse;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonSumResponse;
import nocsystem.indexmanager.models.canhbao.TonThu.TongHopTon;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TongHopTonRepository extends JpaRepository<TongHopTon, Long> {


    @Query( value = "select sum(a.hanhtrinh) as tonHanhTrinh, sum(a.chuapcp) as tonChuaPCP, sum(a.nhapmay) as tonNhapMay from" +
            "(SELECT COALESCE (<PERSON><PERSON>tin<PERSON>_<PERSON> ,<PERSON><PERSON>chi_nhanh_ht,<PERSON><PERSON>tinh_nhan ) as chi<PERSON><PERSON>h,COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht,C.ma_buucuc_goc) as buuCuc" +
            "                  , A.tong_sl as hanhtrinh, B.tong_sl as chuapcp, C.tong_sl as nhapmay" +
            "                    FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = ?1 and status = 1) as A" +
            "                           FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = ?1 and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
            "                         FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl" +
            " from all_ton_nhap_may_chua_ket_noi where ngay_baocao = ?1 and status = 1 group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc) a ",
            nativeQuery = true
    )
    TongHopTonSumResponse tongHopTonCNSum(
            LocalDate ngayBaoCao
    );

    @Query( value = "select sum(a.HanhTrinh) as tonHanhTrinh, sum(a.ChuaPCP) as tonChuaPCP, sum(a.NhapMay) as tonNhapMay from" +
            "(SELECT COALESCE (A.tinh_nhan ,B.chi_nhanh_ht,C.tinh_nhan ) as chiNhanh" +
            "                 ,COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht,C.ma_buucuc_goc) as buuCuc" +
            "                 , A.tong_sl as HanhTrinh, B.tong_sl as ChuaPCP,C.tong_sl as NhapMay" +
            "                  FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR tinh_nhan = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_goc = ?3 ) and status = 1) as A" +
            "                           FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR chi_nhanh_ht = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_ht = ?3 ) and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
            "                        FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl " +
            "from all_ton_nhap_may_chua_ket_noi where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR tinh_nhan = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_goc = ?3 ) and status = 1 " +
            "group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc) a ",
            nativeQuery = true
    )
    TongHopTonSumResponse tongHopTonBCSum(
            LocalDate ngayBaoCao,
            String chiNhanh,
            String buuCuc
    );


    @Query( value = "SELECT COALESCE (D.chi_nhanh ,C.tinh_nhan ) as chiNhanh, " +
            "COALESCE(D.buu_cuc,C.ma_buucuc_goc) as buuCuc, " +
            "D.hanh_trinh as tonHanhTrinh, D.chua_pcp as tonChuaPCP,C.tong_sl as tonNhapMay from ( " +
            "SELECT COALESCE (A.tinh_nhan ,B.chi_nhanh_ht ) as chi_nhanh, " +
            "COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht) as buu_cuc, " +
            "A.tong_sl as hanh_trinh, B.tong_sl as chua_pcp " +
            "FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR tinh_nhan = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_goc = ?3) and status = 1) as A " +
            "FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR chi_nhanh_ht = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_ht = ?3) and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht) as D " +
            "FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl " +
            "from all_ton_nhap_may_chua_ket_noi where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR tinh_nhan = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_goc = ?3) and status = 1 " +
            "group by tinh_nhan, ma_buucuc_goc) as C  ON D.buu_cuc = C.ma_buucuc_goc",
            countQuery = "SELECT count(COALESCE (A.ma_buucuc_goc ,B.ma_buucuc_ht,C.ma_buucuc_goc ))" +
                    "         FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR tinh_nhan = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_goc = ?3 )and status = 1) as A" +
                    "                 FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = ?1 and (?2 IS NULL OR ?2 = '' OR chi_nhanh_ht = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_ht = ?3 ) and status = 1) as  B  ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
                    "                FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl" +
                    " from all_ton_nhap_may_chua_ket_noi where ngay_baocao = ?1 and " +
                    "(?2 IS NULL OR ?2 = '' OR tinh_nhan = ?2) and (?3 IS NULL OR ?3 = '' OR ma_buucuc_goc = ?3) and status = 1" +
                    " group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc ",
            nativeQuery = true
    )
    Page<TongHopTonResponse> tongHopTonBC(
            LocalDate ngayBaoCao,
            String chiNhanh,
            String buuCuc,
            Pageable page
    );

    @Query( value = "select E.chiNhanh, sum(E.hanhtrinh) as tonHanhTrinh, sum(E.chuapcp) as tonChuaPCP, sum(E.nhapmay) as  tonNhapMay from" +
            "(SELECT COALESCE (D.chi_nhanh ,C.tinh_nhan ) as chiNhanh, " +
            "COALESCE(D.buu_cuc,C.ma_buucuc_goc) as buuCuc, " +
            "D.hanh_trinh as hanhtrinh, D.chua_pcp as chuapcp,C.tong_sl as nhapmay from (" +
            "SELECT COALESCE (A.tinh_nhan ,B.chi_nhanh_ht ) as chi_nhanh, " +
            "COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht) as buu_cuc," +
            "A.tong_sl as hanh_trinh, B.tong_sl as chua_pcp " +
            "FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = ?1  and status = 1) as A " +
            "FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = ?1  and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht) as D " +
            "FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl from all_ton_nhap_may_chua_ket_noi where ngay_baocao = ?1 and status = 1 " +
            "group by tinh_nhan, ma_buucuc_goc) as C  ON D.buu_cuc = C.ma_buucuc_goc) E " +
            " group by chiNhanh" +
            " order by chiNhanh",
            countQuery = "SELECT count(distinct tinhNhan) from (select COALESCE (A.tinh_nhan ,B.chi_nhanh_ht,C.tinh_nhan ) as tinhNhan" +
                    "         FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = ?1 and status = 1) as A" +
                    "                 FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = ?1 and status = 1) as  B  ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
                    "                FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl from all_ton_nhap_may_chua_ket_noi where ngay_baocao = ?1 and status = 1 " +
                    "group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc ) D",
            nativeQuery = true
    )
    Page<TongHopTonResponse> tongHopTonCN(
            LocalDate ngayBaoCao,
            Pageable page
    );

    @Query( value = "SELECT COALESCE (D.chi_nhanh ,C.tinh_nhan ) as chiNhanh, " +
            "COALESCE(D.buu_cuc,C.ma_buucuc_goc) as buuCuc, " +
            "D.hanh_trinh as tonHanhTrinh, D.chua_pcp as tonChuaPCP,C.tong_sl as tonNhapMay from ( " +
            "SELECT COALESCE (A.tinh_nhan ,B.chi_nhanh_ht ) as chi_nhanh, " +
            "COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht) as buu_cuc, " +
            "A.tong_sl as hanh_trinh, B.tong_sl as chua_pcp " +
            "FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = :ngayBaoCao and tinh_nhan in :chiNhanh and ma_buucuc_goc in :buuCuc and status = 1) as A " +
            "FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = :ngayBaoCao and chi_nhanh_ht in :chiNhanh and  ma_buucuc_ht in :buuCuc and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht) as D " +
            "FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl from all_ton_nhap_may_chua_ket_noi where ngay_baocao = :ngayBaoCao and tinh_nhan in :chiNhanh and ma_buucuc_goc in :buuCuc and status = 1 " +
            "group by tinh_nhan, ma_buucuc_goc) as C  ON D.buu_cuc = C.ma_buucuc_goc",
            countQuery = "SELECT count(COALESCE (A.ma_buucuc_goc ,B.ma_buucuc_ht,C.ma_buucuc_goc ))" +
                    "         FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and (ma_buucuc_goc IN :buuCuc) and status = 1) as A" +
                    "                 FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = :ngayBaoCao and (chi_nhanh_ht IN :chiNhanh) and (ma_buucuc_ht IN :buuCuc) and status = 1) as  B  ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
                    "                FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl from all_ton_nhap_may_chua_ket_noi where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and (ma_buucuc_goc IN :buuCuc) and status = 1 " +
                    "group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc ",
            nativeQuery = true
    )
    Page<TongHopTonResponse> tongHopTonBCNoAdmin(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            List<String> buuCuc,
            Pageable page
    );

    @Query( value = "select E.chiNhanh, sum(E.hanhtrinh) as tonHanhTrinh, sum(E.chuapcp) as tonChuaPCP, sum(E.nhapmay) as  tonNhapMay from" +
            "(SELECT COALESCE (D.chi_nhanh ,C.tinh_nhan ) as chiNhanh, " +
            "COALESCE(D.buu_cuc,C.ma_buucuc_goc) as buuCuc, " +
            "D.hanh_trinh as hanhtrinh, D.chua_pcp as chuapcp,C.tong_sl as nhapmay from (" +
            "SELECT COALESCE (A.tinh_nhan ,B.chi_nhanh_ht ) as chi_nhanh, " +
            "COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht) as buu_cuc," +
            "A.tong_sl as hanh_trinh, B.tong_sl as chua_pcp " +
            "FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh)  and status = 1) as A " +
            "FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = :ngayBaoCao and (chi_nhanh_ht IN :chiNhanh)  and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht) as D " +
            "FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl from all_ton_nhap_may_chua_ket_noi where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and status = 1 " +
            "group by tinh_nhan, ma_buucuc_goc) as C  ON D.buu_cuc = C.ma_buucuc_goc) E " +
            " group by chiNhanh" +
            " order by chiNhanh",
            countQuery = "SELECT count(distinct tinhNhan) from (select COALESCE (A.tinh_nhan ,B.chi_nhanh_ht,C.tinh_nhan ) as tinhNhan" +
                    "         FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and status = 1) as A" +
                    "                 FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = :ngayBaoCao and (chi_nhanh_ht IN :chiNhanh) and status = 1) as  B  ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
                    "                FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl from all_ton_nhap_may_chua_ket_noi where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and status = 1 " +
                    "group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc ) D",
            nativeQuery = true
    )
    Page<TongHopTonResponse> tongHopTonCNNoAdmin(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            Pageable page
    );

    @Query( value = "select sum(D.hanhtrinh) as tonHanhTrinh, sum(D.chuapcp) as tonChuaPCP, sum(D.nhapmay) as tonNhapMay from " +
            "(SELECT COALESCE (A.tinh_nhan ,B.chi_nhanh_ht,C.tinh_nhan ) as chiNhanh" +
            " ,COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht,C.ma_buucuc_goc) as buuCuc" +
            " , A.tong_sl as hanhtrinh, B.tong_sl as chuapcp,C.tong_sl as nhapmay" +
            " FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and (ma_buucuc_goc IN :buuCuc) and status = 1) as A" +
            " FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = :ngayBaoCao and (chi_nhanh_ht IN :chiNhanh) and (ma_buucuc_ht IN :buuCuc) and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
            " FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl from all_ton_nhap_may_chua_ket_noi where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and (ma_buucuc_goc IN :buuCuc) and status = 1 " +
            "group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc ) D",
            nativeQuery = true
    )
    TongHopTonSumResponse tongHopTonBCNoAdminSum(
            LocalDate ngayBaoCao,
            List<String> chiNhanh,
            List<String> buuCuc
    );

    @Query( value = "select sum(D.hanhtrinh) as tonHanhTrinh, sum(D.chuapcp) as tonChuaPCP, sum(D.nhapmay) as tonNhapMay from" +
            "(SELECT COALESCE (A.tinh_nhan ,B.chi_nhanh_ht,C.tinh_nhan ) as chiNhanh" +
            " ,COALESCE(A.ma_buucuc_goc,B.ma_buucuc_ht,C.ma_buucuc_goc) as buuCuc" +
            " , A.tong_sl as hanhtrinh, B.tong_sl as chuapcp,C.tong_sl as nhapmay" +
            " FROM (select * from all_ton_hanh_trinh_cuoi_thu where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and status = 1) as A" +
            " FULL OUTER JOIN  (select * from all_ton_chua_pcp where ngay_baocao = :ngayBaoCao and (chi_nhanh_ht IN :chiNhanh) and status = 1) as  B ON A.ma_buucuc_goc = B.ma_buucuc_ht" +
            " FULL OUTER JOIN (select tinh_nhan, ma_buucuc_goc , sum(tong_sl) as tong_sl" +
            " from all_ton_nhap_may_chua_ket_noi where ngay_baocao = :ngayBaoCao and (tinh_nhan IN :chiNhanh) and status = 1 " +
            "group by tinh_nhan, ma_buucuc_goc) as C  ON A.ma_buucuc_goc = C.ma_buucuc_goc) D ",
            nativeQuery = true
    )
    TongHopTonSumResponse tongHopTonCNNoAdminSum(
            LocalDate ngayBaoCao,
            List<String> chiNhanh
    );
}
