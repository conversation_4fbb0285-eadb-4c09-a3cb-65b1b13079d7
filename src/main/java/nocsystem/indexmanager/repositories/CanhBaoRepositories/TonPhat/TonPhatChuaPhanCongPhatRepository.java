package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatChuaPhanCongPhat;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatChuaPhanCongPhatKey;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonPhatChuaPhanCongPhatRepository extends PagingAndSortingRepository<TonPhatChuaPhanCongPhat, TonPhatChuaPhanCongPhatKey> {

    @Query("SELECT count(tp.maPhieuGui) FROM TonPhatChuaPhanCongPhat tp " +
            "WHERE tp.ngayBaoCao = :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tp.chiNhanhHt = :maChiNhanh) " +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCucHt = :maBuuCuc) " +
//            "AND tp.maPhieuGui = 'ALL' " +
            "AND tp.status = 1")
    Long tongTonPhatChuaPhanCongPhat(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);


}
