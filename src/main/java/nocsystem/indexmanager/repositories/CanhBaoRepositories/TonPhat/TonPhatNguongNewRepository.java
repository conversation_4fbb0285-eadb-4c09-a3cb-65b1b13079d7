package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BieuDoTonResult;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatNguongNew;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonPhatNguongNewRepository extends PagingAndSortingRepository<TonPhatNguongNew, Long> {

    @Query("SELECT sum(tp.tongSL) FROM TonPhat tp " +
            "WHERE tp.ngayBaoCao = :ngayBaoCao " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or tp.chiNhanh = :maChiNhanh) " +
            "AND (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCuc = :maBuuCuc) " +
            "AND tp.version = :version ")
    Long tongTonPhat(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc,long version);

    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BieuDoTonResult(tp.loaiCanhBao,sum(tp.tongSL)) FROM TonPhat tp " +
            "WHERE tp.ngayBaoCao = :ngayBaoCao " +
            "AND (:maChiNhanh is null or :maChiNhanh = '' or tp.chiNhanh = :maChiNhanh) " +
            "AND (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCuc = :maBuuCuc) " +
            "AND tp.version = :version" +
            " group by tp.loaiCanhBao")
    List<BieuDoTonResult> bieuDoNguongTonPhat(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc,long version);

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " AND (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
//            " and tp.nguongTon <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.nguongTon")
    Page<TonPhatNguongNewResponseDTO> findAllTonPhatNguongTCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " AND (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
//            " and tp.nguongTon <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " AND (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc)) " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.nguongTon")
    Page<TonPhatNguongNewResponseDTO> findAllTonPhatNguongTCTNoAdmin(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maBuuCuc") List<String> maBuuCuc,
            Pageable pageable
    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND  tp.tinhPhat <> 'ALL'" +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
//            " and tp.nguongTon <> 'ALL'" +
//            " AND (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.nguongTon")
    Page<TonPhatNguongNewResponseDTO> findAllTonPhatNguongCN(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND  tp.tinhPhat <> 'ALL'" +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.maBuuCuc in :maBuuCuc " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.nguongTon")
    Page<TonPhatNguongNewResponseDTO> findAllTonPhatNguongCN2(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maBuuCuc") List<String> maBuuCuc,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " AND (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
//            " and tp.nguongTon <> 'ALL'" +
            " AND (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat <> 'ALL' " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.nguongTon")
    Page<TonPhatNguongNewResponseDTO> findAllTonPhatNguongBC(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " AND (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
//            " and tp.nguongTon <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongNewResponseDTO> findAllTonPhatNguongTCTSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " AND (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
//            " and tp.nguongTon <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " AND (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc)) " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.nguongTon")
    List<TonPhatNguongNewResponseDTO> findAllTonPhatNguongTCTSumNoAdmin(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maBuuCuc") List<String> maBuuCuc
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND  tp.tinhPhat <> 'ALL'" +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongNewResponseDTO> findAllTonPhatNguongCNSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND  tp.tinhPhat <> 'ALL'" +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon))" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.maBuuCuc in :maBuuCuc " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongNewResponseDTO> findAllTonPhatNguongCNSum2(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("maBuuCuc") List<String> maBuuCuc
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.nguongTon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.dvNhanh, tp.dvCham, tp.dvHoaToc, tp.tongSL )" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " AND (COALESCE(:tinhPhat, NULL) IS NULL OR tp.tinhPhat IN (:tinhPhat)) " +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.loaiTon IN (:loaiTon)) " +
            " and tp.loaiTon <> 'ALL'" +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.nguongTon IN (:nguongTon)) " +
//            " and tp.nguongTon <> 'ALL'" +
            " AND (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCuc IN (:maBuuCuc)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat <> 'ALL' " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongNewResponseDTO> findAllTonPhatNguongBCSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("nguongTon") List<String> nguongTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query(value = "select tp.time_tinh_toan from tonphat_tonghop_v2 tp where tp.version = :version and tp.ngay_baocao  = :ngayBaoCao limit 1", nativeQuery = true)
    String getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("version") long version
    );
}
