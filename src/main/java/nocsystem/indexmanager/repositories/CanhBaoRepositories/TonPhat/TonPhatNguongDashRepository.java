package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.*;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatNguongNew;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonPhatNguongDashRepository extends PagingAndSortingRepository<TonPhatNguongNew, Long> {

    //Dash chính cho Tổng cty(admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongMainDashResDTO(" +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end), " +
            "(sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end) + sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end) + " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)), " +
            "sum(tp.tongSL))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.nguongTon <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongMainDashResDTO>  TonPhatNguong789MainDashTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash chính cho Tổng cty(không admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongMainDashResDTO(" +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            " (sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end) + sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end) +" +
            " sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)), sum(tp.tongSL))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat in :tinhPhat" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.nguongTon <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongMainDashResDTO> TonPhatNguong789MainDashTCTNoAdmin(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash chính cho chi nhánh(admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongMainDashResDTO(" +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            " (sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end) + sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end) +" +
            " sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)), sum(tp.tongSL))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.nguongTon <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maBuuCuc")
    List<TonPhatNguongMainDashResDTO> TonPhatNguong789MainDashCN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash chính cho chi nhánh(không admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongMainDashResDTO(" +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            " (sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end) + sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end) +" +
            " sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)), sum(tp.tongSL))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.nguongTon <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maBuuCuc")
    List<TonPhatNguongMainDashResDTO> TonPhatNguong789MainDashCNNoAdmin(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash chính cho bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongMainDashResDTO(" +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            " (sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end) + sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end) +" +
            " sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)), sum(tp.tongSL))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = :maBuuCuc" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.nguongTon <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.maBuuCuc")
    List<TonPhatNguongMainDashResDTO> TonPhatNguong789MainDashBC(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash loại tồn Tổng cty(admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongLoaiTonDashDTO(" +
            "tp.loaiTon, sum(case when tp.nguongTon  = 'DO_7' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.dvCham  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvNhanh  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvHoaToc  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            "sum(tp.dvNhanh), sum(tp.dvCham),sum(tp.dvHoaToc))" +
            " from TonPhatNguongNew tp " +
            " where tp.nguongTon <> 'ALL'" +
            " and tp.tinhPhat <> 'ALL' " +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiTon <> 'ALL'" +
            " group by tp.loaiTon")
    List<TonPhatNguongLoaiTonDashDTO> findAllTonPhatNguong789DashLoaiTon(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash loại tồn Tổng cty(không admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongLoaiTonDashDTO(" +
            "tp.loaiTon, sum(case when tp.nguongTon  = 'DO_7' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.dvCham  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvNhanh  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvHoaToc  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            "sum(tp.dvNhanh), sum(tp.dvCham),sum(tp.dvHoaToc))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.nguongTon <> 'ALL' " +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.status = 1" +
            " group by tp.loaiTon")
    List<TonPhatNguongLoaiTonDashDTO> findAllTonPhatNguong789DashLoaiTonNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc
    );

    //Dash loại tồn Chi nhánh(admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongLoaiTonDashDTO(" +
            "tp.loaiTon, sum(case when tp.nguongTon  = 'DO_7' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.dvCham  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvNhanh  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvHoaToc  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            "sum(tp.dvNhanh), sum(tp.dvCham),sum(tp.dvHoaToc))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat = :tinhPhat " +
            " and tp.nguongTon <> 'ALL' " +
            " and (tp.maBuuCuc <> 'ALL')" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.status = 1" +
            " group by tp.loaiTon")
    List<TonPhatNguongLoaiTonDashDTO> findAllTonPhatNguong789DashLoaiTonCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("tinhPhat") String tinhPhat
    );

    //Dash loại tồn Chi nhánh(không admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongLoaiTonDashDTO(" +
            "tp.loaiTon, sum(case when tp.nguongTon  = 'DO_7' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.dvCham  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvNhanh  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvHoaToc  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            "sum(tp.dvNhanh), sum(tp.dvCham),sum(tp.dvHoaToc))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat = :tinhPhat " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.maBuuCuc in :maBuuCuc)" +
            " and tp.nguongTon <> 'ALL' " +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.loaiTon")
    List<TonPhatNguongLoaiTonDashDTO> findAllTonPhatNguong789DashLoaiTonCNNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc
    );

    //Dash loại tồn Bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongLoaiTonDashDTO(" +
            "tp.loaiTon, sum(case when tp.nguongTon  = 'DO_7' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_7' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_7' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.dvCham  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_8' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_8' then tp.tongSL  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvNhanh  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.dvCham  else 0 end)," +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvHoaToc  else 0 end),sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end)," +
            "sum(tp.dvNhanh), sum(tp.dvCham),sum(tp.dvHoaToc))" +
            " from TonPhatNguongNew tp " +
            " where tp.tinhPhat = :tinhPhat " +
            " and tp.loaiTon <> 'ALL' " +
            " and tp.nguongTon <> 'ALL' " +
            " and tp.maBuuCuc = :maBuuCuc" +
            " and tp.ngayBaoCao = :ngayBaoCao"+
            " and tp.status = 1" +
            " group by tp.loaiTon")
    List<TonPhatNguongLoaiTonDashDTO> findAllTonPhatNguong789DashLoaiTonBC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") String maBuuCuc
    );

    //Dash ngưỡng 789 chi nhánh HNI-HCM
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongHNIHCMDTO(" +
            "tp.maBuuCuc, sum(case when tp.nguongTon  = 'DO_9' then tp.dvNhanh  else 0 end), sum(case when tp.nguongTon  = 'DO_9' then tp.dvCham  else 0 end), " +
            "sum(case when tp.nguongTon  = 'DO_9' then tp.dvHoaToc  else 0 end), sum(case when tp.nguongTon  = 'DO_9' then tp.tongSL  else 0 end), sum(tp.tongSL)) "+
            " from TonPhatNguongNew tp " +
            " where tp.loaiTon = 'ALL' " +
            " and tp.nguongTon <> 'ALL'" +
            " and tp.maBuuCuc in :listBuuCuc" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " group by tp.maBuuCuc")
    List<TonPhatNguongHNIHCMDTO> findAllTonPhatNguong789DashHNIHCM(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listBuuCuc") List<String> listBuuCuc
    );

    //Dash Top 15 ngưỡng 789 Bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N789DashResDTO(" +
            " t.buuTaPhat, sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end), sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), " +
            " (sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end) + sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end) +" +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end)), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.tinhPhat = :chiNhanh" +
            " and  t.ngayBaoCao = :ngayBaoCao " +
            " and t.buuTaPhat <> 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.maBuuCuc = :buuCuc" +
            " and t.status = 1" +
            " group by t.buuTaPhat")
    List<TonPhatNguongTop15N789DashResDTO> Top15N789BC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc
    );

    //Dash Top 15 ngưỡng 789 Chi nhánh(Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N789DashResDTO(" +
            " t.maBuuCuc, sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end), sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), " +
            " (sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end) + sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end) +" +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end)), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.tinhPhat = :chiNhanh" +
            " and  t.ngayBaoCao = :ngayBaoCao " +
            " and t.loaiTon <> 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.buuTaPhat <> 'ALL'" +
            " and t.maBuuCuc <> 'ALL'" +
            " and t.status = 1" +
            " group by t.maBuuCuc")
    List<TonPhatNguongTop15N789DashResDTO> Top15N789CN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh
    );

    //Dash Top 15 ngưỡng 789 Chi nhánh(không Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N789DashResDTO(" +
            " t.maBuuCuc, sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end), sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), " +
            " (sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end) + sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end) +" +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end)), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.tinhPhat = :chiNhanh" +
            " and  t.ngayBaoCao = :ngayBaoCao " +
            " and t.loaiTon <> 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.buuTaPhat <> 'ALL'" +
            " and t.maBuuCuc in :maBuuCuc" +
            " and t.status = 1" +
            " group by t.maBuuCuc")
    List<TonPhatNguongTop15N789DashResDTO> Top15N789CNNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc
    );

    //Dash Top 15 ngưỡng 789 Tổng cty(Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N789DashResDTO(" +
            " t.tinhPhat, sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end), sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), " +
            " (sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end) + sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end) +" +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end)), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.ngayBaoCao = :ngayBaoCao " +
            " and t.buuTaPhat ='ALL'" +
            " and t.maBuuCuc = 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.tinhPhat <> 'ALL'" +
            " and t.status = 1" +
            " group by t.tinhPhat")
    List<TonPhatNguongTop15N789DashResDTO> Top15N789TCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash Top 15 ngưỡng 789 Tổng cty (không Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N789DashResDTO(" +
            " t.tinhPhat, sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end), sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), " +
            " (sum(case when t.nguongTon  = 'DO_7' then t.tongSL  else 0 end) + sum(case when t.nguongTon  = 'DO_8' then t.tongSL  else 0 end) +" +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end)), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.ngayBaoCao = :ngayBaoCao " +
            " and t.buuTaPhat ='ALL'" +
            " and t.maBuuCuc in :maBuuCuc" +
            " and t.nguongTon <> 'ALL'" +
            " and t.tinhPhat in :chiNhanh " +
            " and t.status = 1" +
            " group by t.tinhPhat")
    List<TonPhatNguongTop15N789DashResDTO> Top15N789TCTNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc
    );

    //Dash ngưỡng 9 Top 15 Bưu cục
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N9DashResDTO(" +
            " t.buuTaPhat, sum(case when t.nguongTon  = 'DO_9' then t.dvNhanh  else 0 end), " +
            " sum(case when t.nguongTon  = 'DO_9' then t.dvCham  else 0 end), sum(case when t.nguongTon  = 'DO_9' then t.dvHoaToc  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.tinhPhat = :chiNhanh" +
            " and  t.ngayBaoCao = :ngayBaoCao " +
            " and t.buuTaPhat <> 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.maBuuCuc = :buuCuc" +
            " and t.status = 1" +
            " group by t.buuTaPhat")
    List<TonPhatNguongTop15N9DashResDTO> Top15N9BC(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc
    );

    //Dash ngưỡng 9 Top 15 chi nhánh(Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N9DashResDTO(" +
            " t.maBuuCuc, sum(case when t.nguongTon  = 'DO_9' then t.dvNhanh  else 0 end), " +
            " sum(case when t.nguongTon  = 'DO_9' then t.dvCham  else 0 end), sum(case when t.nguongTon  = 'DO_9' then t.dvHoaToc  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.tinhPhat = :chiNhanh" +
            " and  t.ngayBaoCao = :ngayBaoCao " +
            " and t.loaiTon <> 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.buuTaPhat <> 'ALL'" +
            " and t.maBuuCuc <> 'ALL'" +
            " and t.status = 1" +
            " group by t.maBuuCuc")
    List<TonPhatNguongTop15N9DashResDTO> Top15N9CN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh
    );

    //Dash ngưỡng 9 Top 15 chi nhánh(không Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N9DashResDTO(" +
            " t.maBuuCuc, sum(case when t.nguongTon  = 'DO_9' then t.dvNhanh  else 0 end), " +
            " sum(case when t.nguongTon  = 'DO_9' then t.dvCham  else 0 end), sum(case when t.nguongTon  = 'DO_9' then t.dvHoaToc  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.tinhPhat = :chiNhanh" +
            " and  t.ngayBaoCao = :ngayBaoCao " +
            " and t.loaiTon <> 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.buuTaPhat <> 'ALL'" +
            " and t.maBuuCuc in :maBuuCuc" +
            " and t.status = 1" +
            " group by t.maBuuCuc")
    List<TonPhatNguongTop15N9DashResDTO> Top15N9CNNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc
    );

    //Dash ngưỡng 9 Top 15 Tổng cty(Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N9DashResDTO(" +
            " t.tinhPhat, sum(case when t.nguongTon  = 'DO_9' then t.dvNhanh  else 0 end), " +
            " sum(case when t.nguongTon  = 'DO_9' then t.dvCham  else 0 end), sum(case when t.nguongTon  = 'DO_9' then t.dvHoaToc  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.ngayBaoCao = :ngayBaoCao " +
            " and t.buuTaPhat ='ALL'" +
            " and t.maBuuCuc = 'ALL'" +
            " and t.nguongTon <> 'ALL'" +
            " and t.tinhPhat <> 'ALL'" +
            " and t.status = 1" +
            " group by t.tinhPhat")
    List<TonPhatNguongTop15N9DashResDTO> Top15N9TCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    //Dash ngưỡng 9 Top 15 Tổng cty(không Admin)
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongTop15N9DashResDTO(" +
            " t.tinhPhat, sum(case when t.nguongTon  = 'DO_9' then t.dvNhanh  else 0 end), " +
            " sum(case when t.nguongTon  = 'DO_9' then t.dvCham  else 0 end), sum(case when t.nguongTon  = 'DO_9' then t.dvHoaToc  else 0 end)," +
            " sum(case when t.nguongTon  = 'DO_9' then t.tongSL  else 0 end), sum(t.tongSL))" +
            " from TonPhatNguongNew t" +
            " where t.ngayBaoCao = :ngayBaoCao " +
            " and t.buuTaPhat ='ALL'" +
            " and t.maBuuCuc in :maBuuCuc" +
            " and t.nguongTon <> 'ALL'" +
            " and t.tinhPhat in :chiNhanh " +
            " and t.status = 1" +
            " group by t.tinhPhat")
    List<TonPhatNguongTop15N9DashResDTO> Top15N9TCTNoAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> chiNhanh,
            @Param("maBuuCuc") List<String> maBuuCuc
    );
}
