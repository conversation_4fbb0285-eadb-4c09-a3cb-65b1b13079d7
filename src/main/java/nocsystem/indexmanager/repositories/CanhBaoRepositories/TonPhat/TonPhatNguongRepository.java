package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatNguong789;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonPhatNguongRepository extends PagingAndSortingRepository<TonPhatNguong789, Long> {

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ng<PERSON><PERSON><PERSON><PERSON><PERSON>, tp.tinh<PERSON><PERSON>, tp.loai<PERSON>on, tp.loai<PERSON>on, tp.ma<PERSON>uuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon ) " +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatNguongResponseDTO> findAllTonPhatNguong789TCT(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and tp.tinhPhat in :tinhPhat" +
            " and (tp.loaiTon in :loaiTon ) " +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatNguongResponseDTO> findAllTonPhatNguong789TCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("select distinct loaiTon from TonPhatNguong789 ")
    List<String> findDistinctByLoaiTon();


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and tp.loaiTon in :loaiTon " +
            " and tp.loaiTon <> 'ALL' " +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatNguongResponseDTO> findAllTonPhatNguong789CN(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and tp.loaiTon in :loaiTon " +
            " and tp.loaiTon <> 'ALL' " +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc in :maBuuCuc " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatNguongResponseDTO> findAllTonPhatNguong789CN1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat in :tinhPhat  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.loaiTon in :loaiTon " +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.maBuuCuc in :maBuuCuc " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL' " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    Page<TonPhatNguongResponseDTO> findAllTonPhatNguong789BC(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and tp.loaiTon in :loaiTon " +
            //" and( tp.loaiTon IN :#{(#tp.loaiTon == null || #tp.loaiTon.isEmpty()) ? java.util.Collections.singletonList(#tp.loaiTon) : #tp.loaiTon})"+
            " and tp.loaiTon <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongResponseDTO> findTonPhatNguongTCTSum(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and tp.loaiTon in :loaiTon " +
            " and tp.tinhPhat in :tinhPhat " +
            //" and( tp.loaiTon IN :#{(#tp.loaiTon == null || #tp.loaiTon.isEmpty()) ? java.util.Collections.singletonList(#tp.loaiTon) : #tp.loaiTon})"+
            " and tp.loaiTon <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongResponseDTO> findTonPhatNguongTCTSum1(
            @Param("loaiTon") List<String> loaiTon,
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and tp.loaiTon in :loaiTon " +
            " and tp.loaiTon <> 'ALL' " +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongResponseDTO> findTonPhatNguongCNSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and tp.loaiTon in :loaiTon " +
            " and tp.maBuuCuc in :maBuuCuc " +
            " and tp.loaiTon <> 'ALL' " +
            " and tp.loaiDon = 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongResponseDTO> findTonPhatNguongCNSum1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiDon, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.sln7Nhanh, tp.sln7Cham, tp.sln7HoaToc, tp.sln7TongSL, " +
            "tp.sln8Nhanh, tp.sln8Cham, tp.sln8HoaToc, tp.sln8TongSL, " +
            "tp.sln9Nhanh, tp.sln9Cham, tp.sln9HoaToc, tp.sln9TongSL, tp.tongSLN789, tp.tiLeN789,tp.tongSL  )" +
            " from TonPhatNguong789 tp " +
            " where tp.tinhPhat in :tinhPhat  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and tp.loaiDon = 'ALL'" +
            " and tp.loaiTon in :loaiTon " +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.maBuuCuc in :maBuuCuc " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL' " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongResponseDTO> findTonPhatNguongBCSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

}
