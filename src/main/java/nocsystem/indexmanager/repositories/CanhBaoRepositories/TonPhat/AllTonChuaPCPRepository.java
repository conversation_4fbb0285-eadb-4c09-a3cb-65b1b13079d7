package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatChuaPhanCongPhatKey;
import nocsystem.indexmanager.models.canhbao.TonThu.AllChuaPCP;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface AllTonChuaPCPRepository extends PagingAndSortingRepository<AllChuaPCP, TonPhatChuaPhanCongPhatKey> {

    @Query("SELECT sum(tp.tongSl) FROM AllChuaPCP tp " +
            "WHERE tp.ngayBaoCao = :ngayBaoCao " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or tp.chiNhanhHT = :maChiNhanh) " +
            " and (:maBuuCuc is null or :maBuuCuc = '' or tp.maBuuCucHT = :maBuuCuc) " +
            "AND tp.status = 1")
    Long tongTonChuaPCP(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);


    @Query("select tp.updateAt from AllChuaPCP tp where tp.ngayBaoCao  = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );

//    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonDto(" +
//            "tnm.chiNhanhHT, tnm.maBuuCucHT, tnm.tongSl) " +
//            "FROM AllChuaPCP tnm " +
//            "WHERE tnm.ngayBaoCao = :ngayBaoCao " +
//            " and (:maChiNhanh is null or :maChiNhanh = '' or tnm.chiNhanhHT = :maChiNhanh)" +
//            " and (:maBuuCuc is null or :maBuuCuc = '' or tnm.maBuuCucHT = :maBuuCuc) " +
//            "AND tnm.status = 1")
//    List<TongHopTonDto> tonChuaPCPTheoBuuCuc(
//            LocalDate ngayBaoCao,
//            String maChiNhanh,
//            String maBuuCuc
//    );

    @Query("select tp.maBuuCucHT " +
            "from AllChuaPCP tp " +
            "where tp.ngayBaoCao  = :ngayBaoCao")
    List<String> getDistinctBuuCuc(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );
}
