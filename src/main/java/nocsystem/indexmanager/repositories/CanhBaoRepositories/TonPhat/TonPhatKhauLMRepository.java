package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatKhauLM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonPhatKhauLMRepository extends PagingAndSortingRepository<TonPhatKhauLM, Long> {
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
//            " and tp.dgMocMM in :loiKhau" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu))" +
//            " and tp.nhomDV <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauLMTCT(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.tinhPhat in :tinhPhat) " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauLMTCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat) " +
            " and tp.tinhPhat <>'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauLMCN(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat) " +
            " and tp.tinhPhat <>'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauLMCN1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat)  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauLMBC(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauLMSumTCT(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.tinhPhat in :tinhPhat) " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauLMSumTCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat) " +
            " and tp.tinhPhat <>'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauLMSumCN(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat) " +
            " and tp.tinhPhat <>'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauLMSumCN1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO(" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat)  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM IN (:loiKhau))" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauLMSumBC(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            @Param("loiKhau") List<String> loiKhau
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.tinhPhat, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.nhomDV = 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'OK'" +
            " and tp.loaiTon = 'ALL'" +
            " group by tp.tinhPhat")
    List<TonPhatKhauLMTop8ResDTO> findTonPhatKhauLMTop8TCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );
// ngocnt92

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto(" +
            "tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, " +
            " tp.maBuuCuc, tp.buuTaPhat ,tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat  NOT IN :tinhPhat " +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')" +
            " and tp.loaiCanhBao in :loaiCanhBao ")
    List<TonPhatKhauLMTop10ResponeDto> findNTonPhatKhauLMTop8TCT(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.tinhPhat, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat in :tinhPhat" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.loaiTon = 'ALL'" +
            " and tp.dgMocMM = 'OK'" +
            " group by tp.tinhPhat")
    List<TonPhatKhauLMTop8ResDTO> findTonPhatKhauLMTop8TCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    // ngocnt
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto(" +
            "tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, " +
            " tp.maBuuCuc, tp.buuTaPhat ,tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat =:tinhPhat" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')" +
            " and tp.loaiCanhBao in :loaiCanhBao")
    List<TonPhatKhauLMTop10ResponeDto> findNTonPhatKhauLMTop8TCT1(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.maBuuCuc, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'OK'" +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by  tp.maBuuCuc")
    List<TonPhatKhauLMTop8ResDTO> findTonPhatKhauLMTop8CN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.maBuuCuc, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.nhomDV <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'OK'" +
            " group by tp.maBuuCuc")
    List<TonPhatKhauLMTop8ResDTO> findTonPhatKhauLMTop8CN1(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    // ngocnt92
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto(" +
            "tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, " +
            " tp.maBuuCuc, tp.buuTaPhat ,tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc in :maBuuCuc" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao " +
            " and tp.loaiCanhBao in :listNguong " +
            " and tp.nhomDV <> 'ALL' " +
            " and tp.status = 1 " +
            " and tp.dgMocMM in ('OK','LOI_MM')" )
    List<TonPhatKhauLMTop10ResponeDto> findNTonPhatKhauLMTop8CN1(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listNguong") List<String> listNguong

    );


    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.tinhPhat, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.loaiCanhBao in :loaiCanhBao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'OK'" +
            " group by tp.tinhPhat")
    List<TonPhatKhauLMTop8ResDTO> findTop10NotiTGD(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    // ngocnt92
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto(" +
            "tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, " +
            " tp.maBuuCuc, tp.buuTaPhat ,tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.loaiCanhBao in :loaiCanhBao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')"  )
    List<TonPhatKhauLMTop10ResponeDto> findNTop10NotiTGD(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.maBuuCuc, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.loaiCanhBao in :loaiCanhBao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'OK'" +
            " group by  tp.maBuuCuc")
    List<TonPhatKhauLMTop8ResDTO> findTop10NotiCN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    //ngocnt92
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto(" +
            "tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, " +
            " tp.maBuuCuc, tp.buuTaPhat ,tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.loaiCanhBao in :loaiCanhBao" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiTon <> 'ALL'" +
            " and tp.status = 1 " +
            " and tp.nhomDV <> 'ALL' " +
            " and tp.dgMocMM in ('OK','LOI_MM')" )
    List<TonPhatKhauLMTop10ResponeDto> findNTop10NotiCN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.buuTaPhat, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = :maBuuCuc" +
            " and tp.loaiCanhBao in :loaiCanhBao" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'OK'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " group by tp.buuTaPhat")
    List<TonPhatKhauLMTop8ResDTO> findTop10NotiBC(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    // ngocnt92
    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto(" +
            "tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, " +
            " tp.maBuuCuc, tp.buuTaPhat ,tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = :maBuuCuc" +
            " and tp.loaiCanhBao in :loaiCanhBao" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')" +
            " and tp.ngayBaoCao = :ngayBaoCao" )
    List<TonPhatKhauLMTop10ResponeDto> findNTop10NotiBC(
            @Param("tinhPhat") String tinhPhat,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauMMTCT(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and (tp.tinhPhat in :tinhPhat) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauMMTCT1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauMMCN(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauMMCN1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat)  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <>'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " order by tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao")
    Page<TonPhatKhauLMResponseDTO> findTonPhatKhauMMBC(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauMMTCTSum(
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.tinhPhat in :tinhPhat) " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauMMTCTSum1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauMMCNSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where tp.tinhPhat in :tinhPhat " +
            " and tp.tinhPhat <> 'ALL' " +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.maBuuCuc <> 'ALL' " +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauMMCNSum1(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO (" +
            "tp.ngayBaoCao, tp.tinhPhat, tp.loaiTon, tp.loaiCanhBao, tp.maBuuCuc, tp.buuTaPhat, " +
            "tp.lMVang, tp.lMDo1, tp.lMDo2, tp.lMDo3, " +
            "tp.lMDo4, tp.lMDo5, tp.lMDo6, tp.lMDo7, " +
            "tp.lMDo8, tp.lMDo9, tp.tongSL )" +
            " from TonPhatKhauLM tp " +
            " where (tp.tinhPhat in :tinhPhat)  " +
            " and tp.tinhPhat <> 'ALL'" +
            " and (tp.loaiTon in :loaiTon) " +
            " and tp.loaiTon <> 'ALL'" +
            " and (tp.maBuuCuc in :maBuuCuc) " +
            " and tp.maBuuCuc <> 'ALL'" +
            " and tp.buuTaPhat <>'ALL' " +
            " and (tp.loaiCanhBao in :loaiCanhBao) " +
            " and tp.loaiCanhBao <> 'ALL'" +
            " and tp.status = 1" +
            " and tp.dgMocMM = 'LOI_MM'" +
            " AND (COALESCE(:loaiDichVu, NULL) IS NULL OR tp.nhomDV IN (:loaiDichVu)) " +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatKhauLMResponseDTO> findTonPhatKhauMMBCSum(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("loaiTon") List<String> loaiTon,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao,
            @Param("loaiDichVu") List<String> loaiDichVu
    );

    @Query("select tp.timeTinhToan from TonPhatKhauLM tp where tp.ngayBaoCao = :ngayBaoCao")
    Page<String> getTimeTinhToan(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable page
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat <> 'ALL'" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.loaiCanhBao in :loaiCanhBao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')")
    TonPhatKhauLMTop8ResDTO findTonPhatSMSTCT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("loaiCanhBao") List<String> loaiCanhBao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.tinhPhat, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')" +
            " and tp.loaiCanhBao in ('DO_7', 'DO_8', 'DO_9')" +
            " group by tp.tinhPhat")
    TonPhatKhauLMTop8ResDTO findTonPhatSMSCN(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.tinhPhat, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.tinhPhat = :tinhPhat" +
            " and tp.maBuuCuc = 'ALL'" +
            " and tp.buuTaPhat = 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')" +
            " group by tp.tinhPhat")
    TonPhatKhauLMTop8ResDTO findTonPhatSMSCNAll(
            @Param("tinhPhat") String tinhPhat,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.maBuuCuc, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.maBuuCuc = :buuCuc" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')" +
            " and tp.loaiCanhBao in ('DO_7', 'DO_8', 'DO_9')" +
            " group by tp.maBuuCuc")
    TonPhatKhauLMTop8ResDTO findTonPhatSMSBC(
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO(" +
            "tp.maBuuCuc, sum(tp.lMVang), sum(tp.lMDo1), sum(tp.lMDo2), sum(tp.lMDo3), " +
            " sum(tp.lMDo4), sum(tp.lMDo5), sum(tp.lMDo6), sum(tp.lMDo7), " +
            " sum(tp.lMDo8), sum(tp.lMDo9), sum(tp.tongSL) )" +
            " from TonPhatKhauLM tp " +
            " where  tp.maBuuCuc = :buuCuc" +
            " and tp.buuTaPhat <> 'ALL'" +
            " and tp.ngayBaoCao = :ngayBaoCao" +
            " and tp.status = 1" +
            " and tp.dgMocMM in ('OK','LOI_MM')" +
            " group by tp.maBuuCuc")
    TonPhatKhauLMTop8ResDTO findTonPhatSMSBCAll(
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

}
