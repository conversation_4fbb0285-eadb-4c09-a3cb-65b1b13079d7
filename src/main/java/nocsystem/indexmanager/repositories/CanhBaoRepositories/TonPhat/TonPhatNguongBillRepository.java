package nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongBillResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BillDetailPhat;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.SanLuongTonBuuTa;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatNguongBill;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface TonPhatNguongBillRepository extends PagingAndSortingRepository<TonPhatNguongBill, Long> {

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongBillResponseDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.huyenNhan, tp.tenHuyenNhan, tp.ttktFrom, " +
            "tp.tinhPhat,tp.huyenPhat,tp.tenHuyenPhat,tp.ttktTo,tp.maDvViettel," +
            "tp.maBuuCucGoc,tp.timeTacDong,tp.trangThai,tp.maBuuCucHT,tp.chiNhanhHT,tp.maDoiTac," +
            "tp.maKHGui,tp.maBuuCucPhat,cast(tp.timePCP as string),tp.timeGachBP, tp.ngayguiBP,tp.danhGia,tp.loaiPG," +
            "tp.lanPhat,tp.tgConPhat,tp.buuTaPhat,tp.tienCOD,tp.khauFM,tp.khauMM," +
            "tp.khauLM,tp.tgQuyDinh,tp.tgTTLuyKe,tp.tgChenhLech,tp.dgMocMM,tp.isChecked," +
            "tp.loaiPCP, tp.loaiCanhBao, tp.dgMocLM, tp.nhomDV, tp.timeTinhToan )" +
            " from TonPhatNguongBill tp " +
            " where tp.tinhPhat <> 'ALL' " +
            " AND (COALESCE(:tinhPhat, NULL) IS NULL OR tp.chiNhanhHT IN (:tinhPhat)) " +
            " AND (COALESCE(:loaiTon, NULL) IS NULL OR tp.isChecked IN (:loaiTon)) " +
            " AND (COALESCE(:nguongTon, NULL) IS NULL OR tp.loaiCanhBao IN (:nguongTon)) " +
            " AND (COALESCE(:dichVu, NULL) IS NULL OR tp.nhomDV IN (:dichVu)) " +
            " AND (COALESCE(:maBuuCuc, NULL) IS NULL OR tp.maBuuCucHT IN (:maBuuCuc)) " +
            " AND (COALESCE(:loiKhau, NULL) IS NULL OR tp.dgMocMM = (:loiKhau)) " +
            " and tp.status = 1" +
            " and tp.ngayBaoCao = :ngayBaoCao")
    List<TonPhatNguongBillResponseDTO> findAllTonPhatBill(
            @Param("tinhPhat") List<String> tinhPhat,
            @Param("maBuuCuc") List<String> maBuuCuc,
            @Param("loaiTon") List<String> loaiTon,
            @Param("nguongTon") List<String> nguongTon,
            @Param("dichVu") List<String> dichVu,
            @Param("loiKhau") List<String> loiKhau,
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongBillResponseDTO(" +
            "tp.ngayBaoCao, tp.maPhieuGui, tp.tinhNhan, tp.huyenNhan, tp.tenHuyenNhan, tp.ttktFrom, " +
            "tp.tinhPhat,tp.huyenPhat,tp.tenHuyenPhat,tp.ttktTo,tp.maDvViettel," +
            "tp.maBuuCucGoc,tp.timeTacDong,tp.trangThai,tp.maBuuCucHT,tp.chiNhanhHT,tp.maDoiTac," +
            "tp.maKHGui,tp.maBuuCucPhat,cast(tp.timePCP as string),tp.timeGachBP, tp.ngayguiBP,tp.danhGia,tp.loaiPG," +
            "tp.lanPhat,tp.tgConPhat,tp.buuTaPhat,tp.tienCOD,tp.khauFM,tp.khauMM," +
            "tp.khauLM,tp.tgQuyDinh,tp.tgTTLuyKe,tp.tgChenhLech,tp.dgMocMM,tp.isChecked," +
            "tp.loaiPCP, tp.loaiCanhBao, tp.dgMocLM, tp.nhomDV, tp.timeTinhToan )" +
            " from TonPhatNguongBill tp " +
            " where tp.status = 1" +
            " and tp.maPhieuGui = :maPhieuGui")
    TonPhatNguongBillResponseDTO findTonPhatBill(
            @Param("maPhieuGui") String maPhieuGui
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.SanLuongTonBuuTa(" +
            " sum(case when loaiCanhBao in ('DO_1','DO_2','DO_3','DO_4','DO_5','DO_6','DO_7','DO_8','DO_9') then 1 else 0 end)," +
            "sum(case when loaiCanhBao in ('VANG') then 1 else 0 end)) " +
            "from TonPhatNguongBill " +
            " where userId = :buuTa " +
            "and status = 1")
    SanLuongTonBuuTa findTonPhatBuuTa(
            @Param("buuTa") String buuTa
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BillDetailPhat(tp.maPhieuGui, tp.timePCP, tp.ngayguiBP) from TonPhatNguongBill tp " +
            " where tp.loaiCanhBao in ('VANG') " +
            " and tp.userId = :buuTa " +
            " and tp.status = 1 ")
    Page<BillDetailPhat> findAllBillDenHanPhatBuuTa(
            @Param("buuTa") String buuTa,
            Pageable pageable
    );

    @Query("SELECT new nocsystem.indexmanager.models.Response.DashBoardChiNhanh.BillDetailPhat(tp.maPhieuGui, tp.timePCP, tp.ngayguiBP) from TonPhatNguongBill tp " +
            "where tp.loaiCanhBao in ('DO_1','DO_2','DO_3','DO_4','DO_5','DO_6','DO_7','DO_8','DO_9') " +
            " and tp.userId = :buuTa " +
            "and tp.status = 1")
    Page<BillDetailPhat> findAllBillQuaHanPhatBuuTa(
            @Param("buuTa") String buuTa,
            Pageable pageable
    );
}
