package nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO;
import nocsystem.indexmanager.models.canhbao.TrungTam.KhoLienVung6h;
import nocsystem.indexmanager.models.canhbao.TrungTam.ListBuuCucChiNhanh;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public interface ListBCCNRepository extends PagingAndSortingRepository<ListBuuCucChiNhanh, Long> {

        @Query("select distinct (t.chiNhanh), t.tenChiNhanh from ListBuuCucChiNhanh t ")
        List<String[]> findTenChiNhanh(
        );

        @Query("select distinct (t.maBuuCuc), t.tenBuuCuc from ListBuuCucChiNhanh t ")
        List<String[]> findTenBuuCuc(
        );
}
