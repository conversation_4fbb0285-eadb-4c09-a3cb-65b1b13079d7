package nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO;
import nocsystem.indexmanager.models.canhbao.TrungTam.TonGiaoNhanLXLOG;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonGiaoNhanLXLOGRepository extends PagingAndSortingRepository<TonGiaoNhanLXLOG, Long> {
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh <> 'ALL' " +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.mocTon = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGTCT(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh <> 'ALL' " +
                " and tgn.chiNhanh in :chiNhanh" +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.mocTon = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGTCT1(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("chiNhanh") List<String> chiNhanh,
                Pageable pageable
        );

        @Query("select distinct t.chiNhanh from TonGiaoNhanLXLOG t " +
                "where t.ngayBaoCao = :ngayBaoCao " +
                "and t.chiNhanh <> 'ALL'" +
                "order by t.chiNhanh")
        List<String> findDistinctChiNhanh(
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("select distinct t.buuCuc from TonGiaoNhanLXLOG t " +
                "where t.chiNhanh in :chiNhanh " +
                "and t.ngayBaoCao = :ngayBaoCao " +
                "and t.buuCuc <> 'ALL'" +
                "order by t.buuCuc")
        List<String> findDistinctBCByCN(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGCN(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc in :buuCuc" +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGCN1(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where (tgn.chiNhanh in :chiNhanh) " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and (tgn.buuCuc in :buuCuc) " +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.ngayTaoDon <> 'ALL'" +
                " and tgn.maPhieuGui <> 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:mocTon, NULL) IS NULL OR tgn.mocTon IN (:mocTon)) " +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGBC(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("mocTon") List<String> mocTon,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh <> 'ALL' " +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " and tgn.mocTon = 'ALL' " +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGTCTSum(
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh <> 'ALL'" +
                " and tgn.chiNhanh in :chiNhanh" +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " and tgn.mocTon = 'ALL' " +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGTCTSum1(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("chiNhanh") List<String> chiNhanh
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGCNSum(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.ngayTaoDon = 'ALL'" +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.buuCuc in :buuCuc" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGCNSum1(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.loaiVung, tgn.nhomDV, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua0h, tgn.qua2h, tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from TonGiaoNhanLXLOG tgn " +
                " where (tgn.chiNhanh in :chiNhanh) " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and (tgn.buuCuc in :buuCuc) " +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.ngayTaoDon <> 'ALL'" +
                " and tgn.maPhieuGui <> 'ALL'" +
                " and tgn.status = 1" +
                " AND (COALESCE(:mocTon, NULL) IS NULL OR tgn.mocTon IN (:mocTon)) " +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tgn.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tgn.loaiVung IN (:loaiVung)) " +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<TonGiaoNhanLXLOGResponseDTO> findTonGiaoNhanLXLOGBCSum(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("mocTon") List<String> mocTon,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO(" +
                " t.chiNhanh, sum(t.tong), sum(t.qua0h), sum(t.qua2h), sum(t.qua6h), sum(t.qua12h), sum(t.qua24h), sum(t.qua48h)," +
                " sum(t.qua72h), sum(t.qua96h), sum(t.qua120h))" +
                " from TonGiaoNhanLXLOG t" +
                " where t.ngayBaoCao = :ngayBaoCao" +
                " and t.chiNhanh = 'ALL'" +
                " and t.mocTon = 'ALL'" +
                " group by t.chiNhanh")
        TTKTNotiResponseDTO findDataTGD(
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );
// ngocnt92
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO(" +
                " t.chiNhanh, sum(t.tong), sum(t.qua0h), sum(t.qua2h), sum(t.qua6h), sum(t.qua12h), sum(t.qua24h), sum(t.qua48h)," +
                " sum(t.qua72h), sum(t.qua96h), sum(t.qua120h))" +
                " from TonGiaoNhanLXLOG t" +
                " where t.ngayBaoCao = :ngayBaoCao" +
                " and t.chiNhanh = 'ALL'" +
                " and t.mocTon in :listNguong" +
                " group by t.chiNhanh")
        TTKTNotiResponseDTO findNDataTGD(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("listNguong") List<String> listNguong
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO(" +
                " t.buuCuc, sum(t.tong), sum(t.qua0h), sum(t.qua2h), sum(t.qua6h), sum(t.qua12h), sum(t.qua24h), sum(t.qua48h)," +
                " sum(t.qua72h), sum(t.qua96h), sum(t.qua120h))" +
                " from TonGiaoNhanLXLOG t" +
                " where t.ngayBaoCao = :ngayBaoCao" +
                " and t.chiNhanh <> 'ALL'" +
                " and t.mocTon in :mocTon" +
                " group by t.buuCuc")
        List<TTKTNotiResponseDTO> findDataCN(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("mocTon") List<String> mocTon
        );
// ngocnt92
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO(" +
                " t.chiNhanh, sum(t.tong), sum(t.qua0h), sum(t.qua2h), sum(t.qua6h), sum(t.qua12h), sum(t.qua24h), sum(t.qua48h)," +
                " sum(t.qua72h), sum(t.qua96h), sum(t.qua120h))" +
                " from TonGiaoNhanLXLOG t" +
                " where t.ngayBaoCao = :ngayBaoCao" +
                " and t.chiNhanh <> 'ALL'" +
                " and t.mocTon in :mocTon" +
                " and t.nhomDV <> 'ALL'" +
                " and t.loaiVung <> 'ALL' " +
                " group by t.chiNhanh")
        List<TTKTNotiResponseDTO> findNDataCN(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("mocTon") List<String> mocTon
        );

        @Query("select tp.timeTinhToan from TonGiaoNhanLXLOG tp where tp.ngayBaoCao = :ngayBaoCao")
        Page<String> getTimeTinhToan(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable page
        );
}
