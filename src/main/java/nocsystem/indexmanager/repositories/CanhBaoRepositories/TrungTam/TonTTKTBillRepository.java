package nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam;


import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonTTKTBillResponseDTO;
import nocsystem.indexmanager.models.canhbao.TrungTam.TonTTKTBill;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TonTTKTBillRepository extends PagingAndSortingRepository<TonTTKTBill, Long> {
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonTTKTBillResponseDTO(" +
                "tp.ngay<PERSON><PERSON><PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON>, tp.huy<PERSON><PERSON><PERSON>, tp.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tp.ttkt<PERSON>rom, " +
                "tp.tin<PERSON><PERSON><PERSON>,tp.huy<PERSON><PERSON><PERSON>,tp.ten<PERSON><PERSON><PERSON><PERSON><PERSON>,tp.ttktTo,tp.maDvViettel," +
                "tp.maBuuCucGoc,tp.timeTacDong,tp.trangThai,tp.maBuuCucHT,tp.chiNhanhHT,tp.maDoiTac," +
                "tp.maKHGui,tp.maBuuCucPhat,tp.timePCP,tp.timeGachBP, tp.ngayguiBP,tp.danhGia,tp.loaiPG," +
                "tp.lanPhat,tp.tgConPhat,tp.buuTaPhat,tp.tienCOD,tp.khauFM,tp.khauMM," +
                "tp.khauLM,tp.tgQuyDinh,tp.tgTTLuyKe,tp.tgChenhLech,tp.loaiVung, tp.nhomDV, tp.isChecked, tp.loaiCanhBao, tp.mocTonXe, tp.timeTinhToan)" +
                " from TonTTKTBill tp " +
                " where (COALESCE(:chiNhanh, NULL) IS NULL OR tp.chiNhanhHT IN (:chiNhanh)) " +
                " AND (COALESCE(:buuCuc, NULL) IS NULL OR tp.maBuuCucHT IN (:buuCuc)) " +
                " and tp.status = 1" +
                " AND (COALESCE(:nhomDV, NULL) IS NULL OR tp.nhomDV IN (:nhomDV)) " +
                " AND (COALESCE(:loaiVung, NULL) IS NULL OR tp.loaiVung IN (:loaiVung)) " +
                " AND (COALESCE(:mocTonXe, NULL) IS NULL OR tp.mocTonXe IN (:mocTonXe)) " +
                " and tp.ngayBaoCao = :ngayBaoCao")
        List<TonTTKTBillResponseDTO> findTonTTKTBill(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("nhomDV") List<String> nhomDV,
                @Param("loaiVung") List<String> loaiVung,
                @Param("mocTonXe") List<String> mocTonXe
        );
}
