package nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam;

import net.bytebuddy.asm.Advice;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO;
import nocsystem.indexmanager.models.canhbao.TrungTam.KhoLienVung6h;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface KhoLienVung6hRepository extends PagingAndSortingRepository<KhoLienVung6h, Long> {
        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngay<PERSON><PERSON><PERSON><PERSON>, tgn.chi<PERSON><PERSON><PERSON>, tgn.buu<PERSON><PERSON>, tgn.ma<PERSON>, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<KhoLienVung6hResponseDTO> findKhoLienVung6hTCT(
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh in :chiNhanh" +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<KhoLienVung6hResponseDTO> findKhoLienVung6hTCT1(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<KhoLienVung6hResponseDTO> findKhoLienVung6hCN(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc in :buuCuc" +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<KhoLienVung6hResponseDTO> findKhoLienVung6hCN1(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                Pageable pageable
        );

        @Query("select distinct t.chiNhanh from KhoLienVung6h t " +
                "where t.ngayBaoCao = :ngayBaoCao " +
                "and t.chiNhanh <> 'ALL'")
        List<String> findDistinctChiNhanh(
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("select distinct t.buuCuc from KhoLienVung6h t " +
                "where t.chiNhanh in :chiNhanh " +
                "and t.buuCuc <> 'ALL'" +
                "and t.ngayBaoCao = :ngayBaoCao")
        List<String> findDistinctBCByCN(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where (tgn.chiNhanh in :chiNhanh) " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and (tgn.buuCuc in :buuCuc) " +
                " and tgn.buuCuc <> 'ALL'" +
                " and (tgn.mocTon in :mocTon) " +
                " and tgn.maPhieuGui <> 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        Page<KhoLienVung6hResponseDTO> findKhoLienVung6hBC(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("mocTon") List<String> mocTon,
                Pageable pageable
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<KhoLienVung6hResponseDTO> findKhoLienVung6hTCTSum(
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh in :chiNhanh" +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc = 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<KhoLienVung6hResponseDTO> findKhoLienVung6hTCTSum1(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<KhoLienVung6hResponseDTO> findKhoLienVung6hCNSum(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );

        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where tgn.chiNhanh in :chiNhanh " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and tgn.buuCuc in :buuCuc" +
                " and tgn.buuCuc <> 'ALL'" +
                " and tgn.maPhieuGui = 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<KhoLienVung6hResponseDTO> findKhoLienVung6hCNSum1(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("ngayBaoCao") LocalDate ngayBaoCao
        );


        @Query("SELECT new nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO(" +
                " tgn.ngayBaoCao, tgn.chiNhanh, tgn.buuCuc, tgn.maPhieuGui, tgn.ngayTaoDon, tgn.mocTon, " +
                " tgn.qua6h, tgn.qua12h, tgn.qua24h, tgn.qua48h, tgn.qua72h, tgn.qua96h, tgn.qua120h, tgn.tong)" +
                " from KhoLienVung6h tgn " +
                " where (tgn.chiNhanh in :chiNhanh) " +
                " and tgn.chiNhanh <> 'ALL' " +
                " and (tgn.buuCuc in :buuCuc) " +
                " and tgn.buuCuc <> 'ALL'" +
                " and (tgn.mocTon in :mocTon) " +
                " and tgn.maPhieuGui <> 'ALL'" +
                " and tgn.ngayBaoCao = :ngayBaoCao")
        List<KhoLienVung6hResponseDTO> findKhoLienVung6hBCSum(
                @Param("chiNhanh") List<String> chiNhanh,
                @Param("buuCuc") List<String> buuCuc,
                @Param("ngayBaoCao") LocalDate ngayBaoCao,
                @Param("mocTon") List<String> mocTon
        );
}
