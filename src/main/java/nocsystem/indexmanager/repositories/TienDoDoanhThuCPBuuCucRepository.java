package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuJPADto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.CSKDTienDoDoanhThuCPOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPCNV2ResDto;
import nocsystem.indexmanager.models.TienDoDoanhThuChuyenPhat.TienDoDoanhThuCPBuuCuc;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPBCV1ResDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuCPBuuCucRepository extends JpaRepository<TienDoDoanhThuCPBuuCuc, Long> {
    @Query("SELECT dtcpbc.maBuuCuc as maBuuCuc, dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.loaiDichVu as loaiDichVu, " +
            "dtcpbc.thucHien as thucHien, dtcpbc.keHoach as keHoach, dtcpbc.tlht as tlht, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.ttThang as ttThang, dtcpbc.ttTbnThang as ttTbnThang, dtcpbc.ttNam as ttNam " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc WHERE dtcpbc.maBuuCuc = :maBC AND dtcpbc.maChiNhanh = :maCN " +
            "AND dtcpbc.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpbc.loaiDichVu = :loaiDichVu) " +
            "AND dtcpbc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtcpbc.maChiNhanh asc ")
    Page<TienDoDoanhThuCPBCV1ResDto> findTienDoDoanhThuBuuCucAndChiNhanh(
            @Param("maBC") String maBC,
            @Param("maCN") String maCN,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcpbc.maBuuCuc as maBuuCuc, dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.loaiDichVu as loaiDichVu, " +
            "dtcpbc.thucHien as thucHien, dtcpbc.keHoach as keHoach, dtcpbc.tlht as tlht, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.ttThang as ttThang, dtcpbc.ttTbnThang as ttTbnThang, dtcpbc.ttNam as ttNam " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE dtcpbc.maBuuCuc = :maBC " +
            "AND dtcpbc.maChiNhanh = :maCN " +
            "AND dtcpbc.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpbc.loaiDichVu = :loaiDichVu) " +
            "order by dtcpbc.maChiNhanh asc ")
    Page<TienDoDoanhThuCPBCV1ResDto> findTienDoDoanhThuBuuCucAndChiNhanhV2(
            @Param("maBC") String maBC,
            @Param("maCN") String maCN,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT dtcpbc.maBuuCuc as maBuuCuc, dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.loaiDichVu as loaiDichVu, " +
            "dtcpbc.thucHien as thucHien, dtcpbc.keHoach as keHoach, dtcpbc.tlht as tlht, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.ttThang as ttThang, dtcpbc.ttTbnThang as ttTbnThang, dtcpbc.ttNam as ttNam " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE dtcpbc.ngayBaoCao = :toTime " +
            "AND dtcpbc.maChiNhanh = :maCN " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpbc.loaiDichVu = :loaiDichVu) " +
            "AND dtcpbc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtcpbc.maChiNhanh asc, dtcpbc.loaiDichVu asc ")
    Page<TienDoDoanhThuCPBCV1ResDto> findTienDoDoanhThuFollowTime(
            @Param("maCN") String maCN,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcpbc.maBuuCuc as maBuuCuc, dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.loaiDichVu as loaiDichVu, " +
            "dtcpbc.thucHien as thucHien, dtcpbc.keHoach as keHoach, dtcpbc.tlht as tlht, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.ttThang as ttThang, dtcpbc.ttTbnThang as ttTbnThang, dtcpbc.ttNam as ttNam " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE dtcpbc.ngayBaoCao = :toTime " +
            "AND dtcpbc.maChiNhanh = :maCN " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpbc.loaiDichVu = :loaiDichVu) " +
            "AND dtcpbc.maBuuCuc IN :listBuuCucVeriable " +
            "order by dtcpbc.maChiNhanh asc, dtcpbc.loaiDichVu asc ")
    Page<TienDoDoanhThuCPBCV1ResDto> findTienDoDoanhThuFollowTimeV2(
            @Param("maCN") String maCN,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT  dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.keHoach as keHoach, dtcpbc.thucHien as thucHien, " +
            "dtcpbc.cungKyNgay as cungKyNgay, dtcpbc.cungKyThang as cungKyThang, dtcpbc.cungKyNam as cungKyNam, " +
            "dtcpbc.thangTruoc as thangTruoc, dtcpbc.namTruoc as namTruoc, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.loaiDichVu as loaiDichVu, dtcpbc.tlht as tlHoanThanh, dtcpbc.ttThang as ttThang, dtcpbc.ttNam as ttNam, " +
            "dtcpbc.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE ( dtcpbc.maBuuCuc = :maBC OR :maBC is null OR :maBC = '' )" +
            "AND (dtcpbc.maChiNhanh = :maCN OR :maCN is null OR :maCN = '')" +
            "AND dtcpbc.ngayBaoCao = :toTime ")
    List<CSKDTienDoDoanhThuCPOverViewDto> findTienDoDoanhThuBuuCucOverView(
            @Param("maCN") String maCN,
            @Param("maBC") String maBC,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT  dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.keHoach as keHoach, dtcpbc.thucHien as thucHien, " +
            "dtcpbc.cungKyNgay as cungKyNgay, dtcpbc.cungKyThang as cungKyThang, dtcpbc.cungKyNam as cungKyNam, " +
            "dtcpbc.thangTruoc as thangTruoc, dtcpbc.namTruoc as namTruoc, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.loaiDichVu as loaiDichVu, dtcpbc.tlht as tlHoanThanh, dtcpbc.ttThang as ttThang, dtcpbc.ttNam as ttNam, " +
            "dtcpbc.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE ( dtcpbc.maBuuCuc = :maBC OR :maBC is null OR :maBC = '' )" +
            "AND (dtcpbc.maChiNhanh = :maCN OR :maCN is null OR :maCN = '') " +
            "AND dtcpbc.ngayBaoCao = :toTime " +
            "AND dtcpbc.maBuuCuc IN :listBuuCucVeriable")
    List<CSKDTienDoDoanhThuCPOverViewDto> findTienDoDoanhThuBuuCucOverViewV2(
            @Param("maCN") String maCN,
            @Param("maBC") String maBC,
            @Param("toTime") LocalDate toTime,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT  dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.keHoach as keHoach, dtcpbc.thucHien as thucHien, " +
            "dtcpbc.cungKyNgay as cungKyNgay, dtcpbc.cungKyThang as cungKyThang, dtcpbc.cungKyNam as cungKyNam, " +
            "dtcpbc.thangTruoc as thangTruoc, dtcpbc.namTruoc as namTruoc, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.loaiDichVu as loaiDichVu, dtcpbc.tlht as tlht, dtcpbc.ttThang as ttThang, dtcpbc.ttNam as ttNam, " +
            "dtcpbc.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE (:maBC is null OR :maBC = '' OR dtcpbc.maBuuCuc = :maBC) " +
            "AND (:maCN is null OR :maCN = '' OR dtcpbc.maChiNhanh = :maCN) " +
            "AND dtcpbc.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu is null OR :loaiDichVu = '' OR dtcpbc.loaiDichVu = :loaiDichVu) ")
    Slice<CSKDTienDoDoanhThuCPOverViewDto> findListTienDoDTCPBC(
            @Param("maCN") String maCN,
            @Param("maBC") String maBC,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT  dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.keHoach as keHoach, dtcpbc.thucHien as thucHien, " +
            "dtcpbc.cungKyNgay as cungKyNgay, dtcpbc.cungKyThang as cungKyThang, dtcpbc.cungKyNam as cungKyNam, " +
            "dtcpbc.thangTruoc as thangTruoc, dtcpbc.namTruoc as namTruoc, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.loaiDichVu as loaiDichVu, dtcpbc.tlht as tlht, dtcpbc.ttThang as ttThang, dtcpbc.ttNam as ttNam, " +
            "dtcpbc.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE (:maBC is null OR :maBC = '' OR dtcpbc.maBuuCuc = :maBC) " +
            "AND (:maCN is null OR :maCN = '' OR dtcpbc.maChiNhanh = :maCN) " +
            "AND dtcpbc.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu is null OR :loaiDichVu = '' OR dtcpbc.loaiDichVu = :loaiDichVu) " +
            "AND dtcpbc.maBuuCuc IN :listBuuCucVeriable ")
    Slice<CSKDTienDoDoanhThuCPOverViewDto> findListTienDoDTCPBCV2(
            @Param("maCN") String maCN,
            @Param("maBC") String maBC,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT  dtcpbc.maChiNhanh as maChiNhanh, dtcpbc.keHoach as keHoach, dtcpbc.thucHien as thucHien, " +
            "dtcpbc.cungKyNgay as cungKyNgay, dtcpbc.cungKyThang as cungKyThang, dtcpbc.cungKyNam as cungKyNam, " +
            "dtcpbc.thangTruoc as thangTruoc, dtcpbc.namTruoc as namTruoc, dtcpbc.tienDo as tienDo, " +
            "dtcpbc.loaiDichVu as loaiDichVu, dtcpbc.tlht as tlht, dtcpbc.ttThang as ttThang, dtcpbc.ttNam as ttNam, " +
            "dtcpbc.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpbc " +
            "WHERE dtcpbc.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu is null OR :loaiDichVu = '' OR dtcpbc.loaiDichVu = :loaiDichVu) " +
            "AND dtcpbc.maBuuCuc IN :listBuuCucVeriable ")
    Page<TienDoDoanhThuCPCNV2ResDto> findListTienDoDTCPBCDetail(
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT  dtcpcn.loaiDichVu as loaiDichVu, dtcpcn.tiLeTangTruongNgay as tiLeTangTruongNgay, " +
            "dtcpcn.ngayBaoCao as ngayBaoCao " +
            "FROM TienDoDoanhThuCPBuuCuc dtcpcn " +
            "WHERE dtcpcn.ngayBaoCao <= :ngayKetThuc " +
            "AND dtcpcn.ngayBaoCao >= :ngayBatDau " +
            "AND dtcpcn.maChiNhanh = :maChiNhanh " +
            "AND dtcpcn.maBuuCuc = :maBuuCuc ")
    List<BieuDoTtTienDoDoanhThuJPADto> bieuDoTtTienDoDoanhThu(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc
    );
}
