package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTon;
import nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTonDTO;
import nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTonKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.List;

public interface BieuDoXuTheTonRepository extends JpaRepository<BieuDoXuTheTon, BieuDoXuTheTonKey> {
    @Query(value = "SELECT new nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTonDTO(t.ngayBaoCao, t.loaiTon, t.tongSl) " +
            " FROM BieuDoXuTheTon t " +
            " WHERE t.ngayBaoCao >= :startDate " +
            " AND t.ngayBaoCao <= :endDate and t.maChiNhanh = 'ALL' and t.maBuuCuc = 'ALL' ")
    List<BieuDoXuTheTonDTO> getBieuDoXuTheTongTon(LocalDate startDate, LocalDate endDate);

    @Query("SELECT new nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTonDTO(t.ngayBaoCao, t.loaiTon, t.tongSl) " +
            " FROM BieuDoXuTheTon t " +
            " WHERE t.ngayBaoCao >= :startDate " +
            " AND t.ngayBaoCao <= :endDate and t.maChiNhanh = :maChiNhanh and t.maBuuCuc = :maBuuCuc and t.maChiNhanh <> 'ALL' AND t.maBuuCuc <> 'ALL' ")
    List<BieuDoXuTheTonDTO> getBieuDoXuTheTongTonByBuuCuc(LocalDate startDate, LocalDate endDate, String maChiNhanh, String maBuuCuc);

    @Query("SELECT new nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTonDTO(t.ngayBaoCao, t.loaiTon, t.tongSl) " +
            " FROM BieuDoXuTheTon t " +
            " WHERE t.ngayBaoCao >= :startDate " +
            " AND t.ngayBaoCao <= :endDate and t.maChiNhanh = :maChiNhanh and t.maBuuCuc = 'ALL' and t.maChiNhanh <> 'ALL' ")
    List<BieuDoXuTheTonDTO> getBieuDoXuTheTongTonByChiNhanh(LocalDate startDate, LocalDate endDate, String maChiNhanh);
}
