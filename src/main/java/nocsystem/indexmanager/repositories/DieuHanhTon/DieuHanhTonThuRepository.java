package nocsystem.indexmanager.repositories.DieuHanhTon;

import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonThu;
import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonThuKey;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DieuHanhTonThuRepository extends JpaRepository<DieuHanhTonThu, DieuHanhTonThuKey> {
    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse( " +
            "t.chiNhanh, " +
            "sum(t.thuTH1d), sum(t.thuTH2d), sum(t.thuTH3d), sum(t.thuTHL3d), " +
            "sum(t.thuTH1d + t.thuTH2d + t.thuTH3d + t.thuTHL3d) as thuTHTong, " +
            "sum(t.thuDH6h), sum(t.thuDH12h), sum(t.thuDH18h), sum(t.thuDH24h), " +
            "sum(t.thuDH6h + t.thuDH12h + t.thuDH18h + t.thuDH24h) as thuDHTong, " +
            "sum(t.thuQH1d), sum(t.thuQH2d), sum(t.thuQH3d), sum(t.thuQH4d), sum(t.thuQH5d), sum(t.thuQHL5d), " +
            "sum(t.thuQH1d + t.thuQH2d + t.thuQH3d + t.thuQH4d + t.thuQH5d + t.thuQHL5d) as thuQHTong) " +
            "from DieuHanhTonThu t " +
            "where t.thoiGian = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or t.chiNhanh = :chiNhanh) " +
            "and (:maDoiTac is null or :maDoiTac = '' or t.doiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or t.chiNhanh in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or t.buuCuc in (:buuCucSSO)) " +
            "and t.status = 1 " +
            "group by t.chiNhanh " +
            "order by thuQHTong desc")
    Page<DieuHanhTonThuResponse> getPhaiThuDHTTongQuanTheoCN(LocalDate ngayBaoCao, String chiNhanh, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac, Pageable pageable);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse( " +
            "t.chiNhanh, t.buuCuc, " +
            "sum(t.thuTH1d), sum(t.thuTH2d), sum(t.thuTH3d), sum(t.thuTHL3d), " +
            "sum(t.thuTH1d + t.thuTH2d + t.thuTH3d + t.thuTHL3d) as thuTHTong, " +
            "sum(t.thuDH6h), sum(t.thuDH12h), sum(t.thuDH18h), sum(t.thuDH24h), " +
            "sum(t.thuDH6h + t.thuDH12h + t.thuDH18h + t.thuDH24h) as thuDHTong, " +
            "sum(t.thuQH1d), sum(t.thuQH2d), sum(t.thuQH3d), sum(t.thuQH4d), sum(t.thuQH5d), sum(t.thuQHL5d), " +
            "sum(t.thuQH1d + t.thuQH2d + t.thuQH3d + t.thuQH4d + t.thuQH5d + t.thuQHL5d) as thuQHTong) " +
            "from DieuHanhTonThu t " +
            "where t.thoiGian = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or t.chiNhanh = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or t.buuCuc = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or t.doiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or t.chiNhanh in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or t.buuCuc in (:buuCucSSO)) " +
            "and t.status = 1 " +
            "group by t.chiNhanh, t.buuCuc " +
            "order by thuQHTong desc")
    Page<DieuHanhTonThuResponse> getPhaiThuDHTTongQuanTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac, Pageable pageable);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse( " +
            "t.chiNhanh, " +
            "sum(t.thuTH1d), sum(t.thuTH2d), sum(t.thuTH3d), sum(t.thuTHL3d), " +
            "sum(t.thuTH1d + t.thuTH2d + t.thuTH3d + t.thuTHL3d) as thuTHTong, " +
            "sum(t.thuDH6h), sum(t.thuDH12h), sum(t.thuDH18h), sum(t.thuDH24h), " +
            "sum(t.thuDH6h + t.thuDH12h + t.thuDH18h + t.thuDH24h) as thuDHTong, " +
            "sum(t.thuQH1d), sum(t.thuQH2d), sum(t.thuQH3d), sum(t.thuQH4d), sum(t.thuQH5d), sum(t.thuQHL5d), " +
            "sum(t.thuQH1d + t.thuQH2d + t.thuQH3d + t.thuQH4d + t.thuQH5d + t.thuQHL5d) as thuQHTong) " +
            "from DieuHanhTonThu t " +
            "where t.thoiGian = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or t.chiNhanh = :chiNhanh) " +
            "and (:maDoiTac is null or :maDoiTac = '' or t.doiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or t.chiNhanh in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or t.buuCuc in (:buuCucSSO)) " +
            "and t.status = 1 " +
            "group by t.chiNhanh")
    List<DieuHanhTonThuResponse> getPhaiThuDHTChiTietTheoCN(LocalDate ngayBaoCao, String chiNhanh, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse( " +
            "t.chiNhanh, t.buuCuc, " +
            "sum(t.thuTH1d), sum(t.thuTH2d), sum(t.thuTH3d), sum(t.thuTHL3d), " +
            "sum(t.thuTH1d + t.thuTH2d + t.thuTH3d + t.thuTHL3d) as thuTHTong, " +
            "sum(t.thuDH6h), sum(t.thuDH12h), sum(t.thuDH18h), sum(t.thuDH24h), " +
            "sum(t.thuDH6h + t.thuDH12h + t.thuDH18h + t.thuDH24h) as thuDHTong, " +
            "sum(t.thuQH1d), sum(t.thuQH2d), sum(t.thuQH3d), sum(t.thuQH4d), sum(t.thuQH5d), sum(t.thuQHL5d), " +
            "sum(t.thuQH1d + t.thuQH2d + t.thuQH3d + t.thuQH4d + t.thuQH5d + t.thuQHL5d) as thuQHTong) " +
            "from DieuHanhTonThu t " +
            "where t.thoiGian = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or t.chiNhanh = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or t.buuCuc = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or t.doiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or t.chiNhanh in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or t.buuCuc in (:buuCucSSO)) " +
            "and t.status = 1 " +
            "group by t.chiNhanh, t.buuCuc")
    List<DieuHanhTonThuResponse> getPhaiThuDHTChiTietTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse( " +
            "t.chiNhanh, t.buuCuc, t.tuyenBuuTa, " +
            "sum(t.thuTH1d), sum(t.thuTH2d), sum(t.thuTH3d), sum(t.thuTHL3d), " +
            "sum(t.thuTH1d + t.thuTH2d + t.thuTH3d + t.thuTHL3d) as thuTHTong, " +
            "sum(t.thuDH6h), sum(t.thuDH12h), sum(t.thuDH18h), sum(t.thuDH24h), " +
            "sum(t.thuDH6h + t.thuDH12h + t.thuDH18h + t.thuDH24h) as thuDHTong, " +
            "sum(t.thuQH1d), sum(t.thuQH2d), sum(t.thuQH3d), sum(t.thuQH4d), sum(t.thuQH5d), sum(t.thuQHL5d), " +
            "sum(t.thuQH1d + t.thuQH2d + t.thuQH3d + t.thuQH4d + t.thuQH5d + t.thuQHL5d) as thuQHTong) " +
            "from DieuHanhTonThu t " +
            "where t.thoiGian = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or t.chiNhanh = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or t.buuCuc = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or t.doiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or t.chiNhanh in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or t.buuCuc in (:buuCucSSO)) " +
            "and t.status = 1 " +
            "group by t.chiNhanh, t.buuCuc, t.tuyenBuuTa")
    List<DieuHanhTonThuResponse> getPhaiThuDHTChiTietTheoCNBCT(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse( " +
            "sum(t.thuTH1d), sum(t.thuTH2d), sum(t.thuTH3d), sum(t.thuTHL3d), " +
            "sum(t.thuTH1d + t.thuTH2d + t.thuTH3d + t.thuTHL3d) as thuTHTong, " +
            "sum(t.thuDH6h), sum(t.thuDH12h), sum(t.thuDH18h), sum(t.thuDH24h), " +
            "sum(t.thuDH6h + t.thuDH12h + t.thuDH18h + t.thuDH24h) as thuDHTong, " +
            "sum(t.thuQH1d), sum(t.thuQH2d), sum(t.thuQH3d), sum(t.thuQH4d), sum(t.thuQH5d), sum(t.thuQHL5d), " +
            "sum(t.thuQH1d + t.thuQH2d + t.thuQH3d + t.thuQH4d + t.thuQH5d + t.thuQHL5d) as thuQHTong) " +
            "from DieuHanhTonThu t " +
            "where t.thoiGian = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or t.chiNhanh = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or t.buuCuc = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or t.doiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or t.chiNhanh in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or t.buuCuc in (:buuCucSSO)) " +
            "and t.status = 1")
    DieuHanhTonThuResponse getPhaiThuDHTTong(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

}
