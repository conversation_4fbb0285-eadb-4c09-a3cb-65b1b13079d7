package nocsystem.indexmanager.repositories.DieuHanhTon;

import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonHoanPhat;
import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonHoanPhatKey;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DieuHanhTonHoanPhatRepository extends JpaRepository<DieuHanhTonHoanPhat, DieuHanhTonHoanPhatKey> {
    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse( " +
            "hp.cnPhat, " +
            "sum(hp.phatTH1d), sum(hp.phatTH2d), sum(hp.phatTH3d), sum(hp.phatTHL3d), " +
            "sum(hp.phatTH1d + hp.phatTH2d + hp.phatTH3d + hp.phatTHL3d) as phatTHTong, " +
            "sum(hp.phatDH6h), sum(hp.phatDH12h), sum(hp.phatDH18h), sum(hp.phatDH24h), " +
            "sum(hp.phatDH6h + hp.phatDH12h + hp.phatDH18h + hp.phatDH24h) as phatDHTong, " +
            "sum(hp.phatQH1d), sum(hp.phatQH2d), sum(hp.phatQH3d), sum(hp.phatQH4d), sum(hp.phatQH5d), sum(hp.phatQH6d), sum(hp.phatQH7d), sum(hp.phatQHL7d), " +
            "sum(hp.phatQH1d + hp.phatQH2d + hp.phatQH3d + hp.phatQH4d + hp.phatQH5d + hp.phatQH6d + hp.phatQH7d + hp.phatQHL7d) as phatQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 1 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat " +
            "order by phatQHTong desc")
    Page<DieuHanhTonPhatResponse> getPhaiPhatDHTTongQuanTheoCN(LocalDate ngayBaoCao, String chiNhanh, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac, Pageable pageable);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse( " +
            "hp.cnPhat, hp.maBuuCucPhat, " +
            "sum(hp.phatTH1d), sum(hp.phatTH2d), sum(hp.phatTH3d), sum(hp.phatTHL3d), " +
            "sum(hp.phatTH1d + hp.phatTH2d + hp.phatTH3d + hp.phatTHL3d) as phatTHTong, " +
            "sum(hp.phatDH6h), sum(hp.phatDH12h), sum(hp.phatDH18h), sum(hp.phatDH24h), " +
            "sum(hp.phatDH6h + hp.phatDH12h + hp.phatDH18h + hp.phatDH24h) as phatDHTong, " +
            "sum(hp.phatQH1d), sum(hp.phatQH2d), sum(hp.phatQH3d), sum(hp.phatQH4d), sum(hp.phatQH5d), sum(hp.phatQH6d), sum(hp.phatQH7d), sum(hp.phatQHL7d), " +
            "sum(hp.phatQH1d + hp.phatQH2d + hp.phatQH3d + hp.phatQH4d + hp.phatQH5d + hp.phatQH6d + hp.phatQH7d + hp.phatQHL7d) as phatQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 2 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat, hp.maBuuCucPhat " +
            "order by phatQHTong desc")
    Page<DieuHanhTonPhatResponse> getPhaiPhatDHTTongQuanTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac, Pageable pageable);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse( " +
            "hp.cnPhat, " +
            "sum(hp.hoanTH1d), sum(hp.hoanTH2d), sum(hp.hoanTH3d), sum(hp.hoanTHL3d), " +
            "sum(hp.hoanTH1d + hp.hoanTH2d + hp.hoanTH3d + hp.hoanTHL3d) as hoanTHTong, " +
            "sum(hp.hoanDH6h), sum(hp.hoanDH12h), sum(hp.hoanDH18h), sum(hp.hoanDH24h), " +
            "sum(hp.hoanDH6h + hp.hoanDH12h + hp.hoanDH18h + hp.hoanDH24h) as hoanDHTong, " +
            "sum(hp.hoanQH1d), sum(hp.hoanQH2d), sum(hp.hoanQH3d), sum(hp.hoanQH4d), sum(hp.hoanQH5d), sum(hp.hoanQH6d), sum(hp.hoanQH7d), sum(hp.hoanQHL7d), " +
            "sum(hp.hoanQH1d + hp.hoanQH2d + hp.hoanQH3d + hp.hoanQH4d + hp.hoanQH5d + hp.hoanQH6d + hp.hoanQH7d + hp.hoanQHL7d) as hoanQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 1 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat " +
            "order by hoanQHTong desc")
    Page<DieuHanhTonHoanResponse> getPhaiHoanDHTTongQuanTheoCN(LocalDate ngayBaoCao, String chiNhanh, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac, Pageable pageable);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse( " +
            "hp.cnPhat, hp.maBuuCucPhat, " +
            "sum(hp.hoanTH1d), sum(hp.hoanTH2d), sum(hp.hoanTH3d), sum(hp.hoanTHL3d), " +
            "sum(hp.hoanTH1d + hp.hoanTH2d + hp.hoanTH3d + hp.hoanTHL3d) as hoanTHTong, " +
            "sum(hp.hoanDH6h), sum(hp.hoanDH12h), sum(hp.hoanDH18h), sum(hp.hoanDH24h), " +
            "sum(hp.hoanDH6h + hp.hoanDH12h + hp.hoanDH18h + hp.hoanDH24h) as hoanDHTong, " +
            "sum(hp.hoanQH1d), sum(hp.hoanQH2d), sum(hp.hoanQH3d), sum(hp.hoanQH4d), sum(hp.hoanQH5d), sum(hp.hoanQH6d), sum(hp.hoanQH7d), sum(hp.hoanQHL7d), " +
            "sum(hp.hoanQH1d + hp.hoanQH2d + hp.hoanQH3d + hp.hoanQH4d + hp.hoanQH5d + hp.hoanQH6d + hp.hoanQH7d + hp.hoanQHL7d) as hoanQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 2 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat, hp.maBuuCucPhat " +
            "order by hoanQHTong desc")
    Page<DieuHanhTonHoanResponse> getPhaiHoanDHTTongQuanTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac, Pageable pageable);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse( " +
            "hp.cnPhat, " +
            "sum(hp.phatTH1d), sum(hp.phatTH2d), sum(hp.phatTH3d), sum(hp.phatTHL3d), " +
            "sum(hp.phatTH1d + hp.phatTH2d + hp.phatTH3d + hp.phatTHL3d) as phatTHTong, " +
            "sum(hp.phatDH6h), sum(hp.phatDH12h), sum(hp.phatDH18h), sum(hp.phatDH24h), " +
            "sum(hp.phatDH6h + hp.phatDH12h + hp.phatDH18h + hp.phatDH24h) as phatDHTong, " +
            "sum(hp.phatQH1d), sum(hp.phatQH2d), sum(hp.phatQH3d), sum(hp.phatQH4d), sum(hp.phatQH5d), sum(hp.phatQH6d), sum(hp.phatQH7d), sum(hp.phatQHL7d), " +
            "sum(hp.phatQH1d + hp.phatQH2d + hp.phatQH3d + hp.phatQH4d + hp.phatQH5d + hp.phatQH6d + hp.phatQH7d + hp.phatQHL7d) as phatQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 1 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat")
    List<DieuHanhTonPhatResponse> getPhaiPhatDHTChiTietTheoCN(LocalDate ngayBaoCao, String chiNhanh, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse( " +
            "hp.cnPhat, hp.maBuuCucPhat, " +
            "sum(hp.phatTH1d), sum(hp.phatTH2d), sum(hp.phatTH3d), sum(hp.phatTHL3d), " +
            "sum(hp.phatTH1d + hp.phatTH2d + hp.phatTH3d + hp.phatTHL3d) as phatTHTong, " +
            "sum(hp.phatDH6h), sum(hp.phatDH12h), sum(hp.phatDH18h), sum(hp.phatDH24h), " +
            "sum(hp.phatDH6h + hp.phatDH12h + hp.phatDH18h + hp.phatDH24h) as phatDHTong, " +
            "sum(hp.phatQH1d), sum(hp.phatQH2d), sum(hp.phatQH3d), sum(hp.phatQH4d), sum(hp.phatQH5d), sum(hp.phatQH6d), sum(hp.phatQH7d), sum(hp.phatQHL7d), " +
            "sum(hp.phatQH1d + hp.phatQH2d + hp.phatQH3d + hp.phatQH4d + hp.phatQH5d + hp.phatQH6d + hp.phatQH7d + hp.phatQHL7d) as phatQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 2 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat, hp.maBuuCucPhat")
    List<DieuHanhTonPhatResponse> getPhaiPhatDHTChiTietTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse( " +
            "hp.cnPhat, hp.maBuuCucPhat, hp.tuyenBuuTa, " +
            "sum(hp.phatTH1d), sum(hp.phatTH2d), sum(hp.phatTH3d), sum(hp.phatTHL3d), " +
            "sum(hp.phatTH1d + hp.phatTH2d + hp.phatTH3d + hp.phatTHL3d) as phatTHTong, " +
            "sum(hp.phatDH6h), sum(hp.phatDH12h), sum(hp.phatDH18h), sum(hp.phatDH24h), " +
            "sum(hp.phatDH6h + hp.phatDH12h + hp.phatDH18h + hp.phatDH24h) as phatDHTong, " +
            "sum(hp.phatQH1d), sum(hp.phatQH2d), sum(hp.phatQH3d), sum(hp.phatQH4d), sum(hp.phatQH5d), sum(hp.phatQH6d), sum(hp.phatQH7d), sum(hp.phatQHL7d), " +
            "sum(hp.phatQH1d + hp.phatQH2d + hp.phatQH3d + hp.phatQH4d + hp.phatQH5d + hp.phatQH6d + hp.phatQH7d + hp.phatQHL7d) as phatQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 4 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat, hp.maBuuCucPhat, hp.tuyenBuuTa")
    List<DieuHanhTonPhatResponse> getPhaiPhatDHTChiTietTheoCNBCT(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse( " +
            "hp.cnPhat, " +
            "sum(hp.hoanTH1d), sum(hp.hoanTH2d), sum(hp.hoanTH3d), sum(hp.hoanTHL3d), " +
            "sum(hp.hoanTH1d + hp.hoanTH2d + hp.hoanTH3d + hp.hoanTHL3d) as hoanTHTong, " +
            "sum(hp.hoanDH6h), sum(hp.hoanDH12h), sum(hp.hoanDH18h), sum(hp.hoanDH24h), " +
            "sum(hp.hoanDH6h + hp.hoanDH12h + hp.hoanDH18h + hp.hoanDH24h) as hoanDHTong, " +
            "sum(hp.hoanQH1d), sum(hp.hoanQH2d), sum(hp.hoanQH3d), sum(hp.hoanQH4d), sum(hp.hoanQH5d), sum(hp.hoanQH6d), sum(hp.hoanQH7d), sum(hp.hoanQHL7d), " +
            "sum(hp.hoanQH1d + hp.hoanQH2d + hp.hoanQH3d + hp.hoanQH4d + hp.hoanQH5d + hp.hoanQH6d + hp.hoanQH7d + hp.hoanQHL7d) as hoanQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 1 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat")
    List<DieuHanhTonHoanResponse> getPhaiHoanDHTChiTietTheoCN(LocalDate ngayBaoCao, String chiNhanh, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse( " +
            "hp.cnPhat, hp.maBuuCucPhat, " +
            "sum(hp.hoanTH1d), sum(hp.hoanTH2d), sum(hp.hoanTH3d), sum(hp.hoanTHL3d), " +
            "sum(hp.hoanTH1d + hp.hoanTH2d + hp.hoanTH3d + hp.hoanTHL3d) as hoanTHTong, " +
            "sum(hp.hoanDH6h), sum(hp.hoanDH12h), sum(hp.hoanDH18h), sum(hp.hoanDH24h), " +
            "sum(hp.hoanDH6h + hp.hoanDH12h + hp.hoanDH18h + hp.hoanDH24h) as hoanDHTong, " +
            "sum(hp.hoanQH1d), sum(hp.hoanQH2d), sum(hp.hoanQH3d), sum(hp.hoanQH4d), sum(hp.hoanQH5d), sum(hp.hoanQH6d), sum(hp.hoanQH7d), sum(hp.hoanQHL7d), " +
            "sum(hp.hoanQH1d + hp.hoanQH2d + hp.hoanQH3d + hp.hoanQH4d + hp.hoanQH5d + hp.hoanQH6d + hp.hoanQH7d + hp.hoanQHL7d) as hoanQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 2 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat, hp.maBuuCucPhat")
    List<DieuHanhTonHoanResponse> getPhaiHoanDHTChiTietTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse( " +
            "hp.cnPhat, hp.maBuuCucPhat, hp.tuyenBuuTa, " +
            "sum(hp.hoanTH1d), sum(hp.hoanTH2d), sum(hp.hoanTH3d), sum(hp.hoanTHL3d), " +
            "sum(hp.hoanTH1d + hp.hoanTH2d + hp.hoanTH3d + hp.hoanTHL3d) as hoanTHTong, " +
            "sum(hp.hoanDH6h), sum(hp.hoanDH12h), sum(hp.hoanDH18h), sum(hp.hoanDH24h), " +
            "sum(hp.hoanDH6h + hp.hoanDH12h + hp.hoanDH18h + hp.hoanDH24h) as hoanDHTong, " +
            "sum(hp.hoanQH1d), sum(hp.hoanQH2d), sum(hp.hoanQH3d), sum(hp.hoanQH4d), sum(hp.hoanQH5d), sum(hp.hoanQH6d), sum(hp.hoanQH7d), sum(hp.hoanQHL7d), " +
            "sum(hp.hoanQH1d + hp.hoanQH2d + hp.hoanQH3d + hp.hoanQH4d + hp.hoanQH5d + hp.hoanQH6d + hp.hoanQH7d + hp.hoanQHL7d) as hoanQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 4 " +
            "and hp.status = 1 " +
            "group by hp.cnPhat, hp.maBuuCucPhat, hp.tuyenBuuTa")
    List<DieuHanhTonHoanResponse> getPhaiHoanDHTChiTietTheoCNBCT(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse( " +
            "sum(hp.phatTH1d), sum(hp.phatTH2d), sum(hp.phatTH3d), sum(hp.phatTHL3d), " +
            "sum(hp.phatTH1d + hp.phatTH2d + hp.phatTH3d + hp.phatTHL3d) as phatTHTong, " +
            "sum(hp.phatDH6h), sum(hp.phatDH12h), sum(hp.phatDH18h), sum(hp.phatDH24h), " +
            "sum(hp.phatDH6h + hp.phatDH12h + hp.phatDH18h + hp.phatDH24h) as phatDHTong, " +
            "sum(hp.phatQH1d), sum(hp.phatQH2d), sum(hp.phatQH3d), sum(hp.phatQH4d), sum(hp.phatQH5d), sum(hp.phatQH6d), sum(hp.phatQH7d), sum(hp.phatQHL7d), " +
            "sum(hp.phatQH1d + hp.phatQH2d + hp.phatQH3d + hp.phatQH4d + hp.phatQH5d + hp.phatQH6d + hp.phatQH7d + hp.phatQHL7d) as phatQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 4 " +
            "and hp.status = 1")
    DieuHanhTonPhatResponse getPhaiPhatDHTTong(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse( " +
            "sum(hp.hoanTH1d), sum(hp.hoanTH2d), sum(hp.hoanTH3d), sum(hp.hoanTHL3d), " +
            "sum(hp.hoanTH1d + hp.hoanTH2d + hp.hoanTH3d + hp.hoanTHL3d) as hoanTHTong, " +
            "sum(hp.hoanDH6h), sum(hp.hoanDH12h), sum(hp.hoanDH18h), sum(hp.hoanDH24h), " +
            "sum(hp.hoanDH6h + hp.hoanDH12h + hp.hoanDH18h + hp.hoanDH24h) as hoanDHTong, " +
            "sum(hp.hoanQH1d), sum(hp.hoanQH2d), sum(hp.hoanQH3d), sum(hp.hoanQH4d), sum(hp.hoanQH5d), sum(hp.hoanQH6d), sum(hp.hoanQH7d), sum(hp.hoanQHL7d), " +
            "sum(hp.hoanQH1d + hp.hoanQH2d + hp.hoanQH3d + hp.hoanQH4d + hp.hoanQH5d + hp.hoanQH6d + hp.hoanQH7d + hp.hoanQHL7d) as hoanQHTong) " +
            "from DieuHanhTonHoanPhat hp " +
            "where hp.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or hp.cnPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or hp.maBuuCucPhat = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or hp.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or hp.cnPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or hp.maBuuCucPhat in (:buuCucSSO)) " +
            "and hp.loaiBaoCao = 4 " +
            "and hp.status = 1")
    DieuHanhTonHoanResponse getPhaiHoanDHTTong(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

}
