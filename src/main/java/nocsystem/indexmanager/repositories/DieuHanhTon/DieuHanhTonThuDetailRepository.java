package nocsystem.indexmanager.repositories.DieuHanhTon;

import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonThuChiTiet;
import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonThuChiTietKey;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuDetailResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DieuHanhTonThuDetailRepository extends JpaRepository<DieuHanhTonThuChiTiet, DieuHanhTonThuChiTietKey> {

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuDetailResponse(" +
            "d.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d.th<PERSON><PERSON><PERSON><PERSON><PERSON>, " +
            "to_char(d.time<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 'YYYY-MM-<PERSON> HH24:MI:SS'), d.tinhNhan, d.maKHGui, d.tinhPhat, " +
            "d.maBuuCucGoc, d.maDVViettel, d.tongCuoc, to_char(d.updateTime, 'YYYY-MM-DD HH24:MI:SS'), d.thoiGian," +
            "d.danhGiaDon," +
            "d.thoiGianTon) " +
            "from DieuHanhTonThuChiTiet d " +
            "where d.thoiGian = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or d.tinhNhan = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or d.maBuuCucGoc = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or d.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or d.tinhNhan in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or d.maBuuCucGoc in (:buuCucSSO)) " +
            "and d.typePB = 'PHAI_THU' " +
            "and d.status = 1")
    List<DieuHanhTonThuDetailResponse> getPhaiThuDHTChiTietTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

}