package nocsystem.indexmanager.repositories.DieuHanhTon;

import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonHoanPhatChiTiet;
import nocsystem.indexmanager.models.DieuHanhTon.DieuHanhTonHoanPhatChiTietKey;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanDetailResponse;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatDetailResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DieuHanhTonHoanPhatDetailRepository extends JpaRepository<DieuHanhTonHoanPhatChiTiet, DieuHanhTonHoanPhatChiTietKey> {

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatDetailResponse(" +
            "d.<PERSON>, d.tinh<PERSON>han, d.huyenNhan, d.tinhPhat, d.huyenPhat, d.maBuuCucPhatThucTe, d.maDVViettel, d.maBuuCucGoc, " +
            "d.maTrangThai, d.maBuuCucHT, d.maChiNhanhHT, d.maDoiTac, d.maKHGui, d.maBuuCucPhat, d.timePCP, " +
            "d.timeGachBP, d.timeGachBP2, d.timeGachBP3, d.ngayGuiBP, d.soLanGiao, d.tuyenBuuTa, d.tienCOD, " +
            "d.tgQuyDinhPhat, d.tgChenhLechPhat, d.ngayBaoCao, d.noiKho, d.typeGiao,d.danhGiaTon, d.thoiGianTon) " +
            "from DieuHanhTonHoanPhatChiTiet d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or d.tinhPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or d.maBuuCucPhatThucTe = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or d.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or d.tinhPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or d.maBuuCucPhatThucTe in (:buuCucSSO)) " +
            "and d.typePB = 'PHAI_PHAT' " +
            "and d.status = 1 ")
    List<DieuHanhTonPhatDetailResponse> getPhaiPhatDHTChiTietTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

    @Query("select new nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanDetailResponse(" +
            "d.maPhieuGui, d.tinhNhan, d.huyenNhan, d.tinhPhat, d.huyenPhat, d.maBuuCucPhatThucTe, d.maDVViettel, d.maBuuCucGoc, " +
            "d.maTrangThai, d.maBuuCucHT, d.maChiNhanhHT, d.maDoiTac, d.maKHGui, d.maBuuCucPhat, d.timePCP, " +
            "d.timeGachBP, d.timeGachBP2, d.timeGachBP3, d.ngayGuiBP, d.soLanGiao, d.tuyenBuuTa, d.tienCOD, " +
            "d.tgQuyDinhPhat, d.tgChenhLechPhat, d.ngayBaoCao, d.noiKho, d.typeGiao, d.danhGiaTon, d.thoiGianTon) " +
            "from DieuHanhTonHoanPhatChiTiet d " +
            "where d.ngayBaoCao = :ngayBaoCao " +
            "and (:chiNhanh is null or :chiNhanh = '' or d.tinhPhat = :chiNhanh) " +
            "and (:buuCuc is null or :buuCuc = '' or d.maBuuCucPhatThucTe = :buuCuc) " +
            "and (:maDoiTac is null or :maDoiTac = '' or d.maDoiTac = :maDoiTac) " +
            "and (coalesce(:chiNhanhSSO, null) is null or d.tinhPhat in (:chiNhanhSSO)) " +
            "and (coalesce(:buuCucSSO, null) is null or d.maBuuCucPhatThucTe in (:buuCucSSO)) " +
            "and d.typePB = 'PHAI_HOAN' " +
            "and d.status = 1")
    List<DieuHanhTonHoanDetailResponse> getPhaiHoanDHTChiTietTheoCNBC(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String maDoiTac);

}