package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TongTienDoLuyKeTCTCNResDto;
import nocsystem.indexmanager.models.ThongKeLuyKe.TienDoLuyKeTCTCN;
import nocsystem.indexmanager.models.ThongKeLuyKe.TongTienDoLuyKeTCTCN;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TongTienDoLuyKeTCTBuuCucRepository extends JpaRepository<TienDoLuyKeTCTCN, Long> {

    @Query("SELECT TongTDLuyKeTCTBC.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCTBC.mien as mien, TongTDLuyKeTCTBC.tlht as tlht, TongTDLuyKeTCTBC.chiNhanh as chiNhanh,TongTDLuyKeTCTBC.tienDo as tienDo, " +
            "TongTDLuyKeTCTBC.ttThang as ttThang, TongTDLuyKeTCTBC.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCTBC.ttNam as ttNam, TongTDLuyKeTCTBC.ttTbnNam as ttTbnNam, TongTDLuyKeTCTBC.khthang as keHoach," +
            "TongTDLuyKeTCTBC.lkthang as luyKeThang, TongTDLuyKeTCTBC.danhgia as danhgia, TongTDLuyKeTCTBC.tienDoNgay as tienDoNgay, " +
            "TongTDLuyKeTCTBC.tiLeHoanThanhNgay as tiLeHoanThanhNgay, TongTDLuyKeTCTBC.keHoachThangDenNgay as keHoachThangDenNgay, " +
            "TongTDLuyKeTCTBC.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN TongTDLuyKeTCTBC " +
            "WHERE TongTDLuyKeTCTBC.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeTCTBC.chiNhanh = :chiNhanh")
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeTCTBC(
            @Param("chiNhanh") String chiNhanh,
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT TongTDLuyKeTCTBC.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCTBC.mien as mien, TongTDLuyKeTCTBC.tlht as tlht, TongTDLuyKeTCTBC.chiNhanh as chiNhanh,TongTDLuyKeTCTBC.tienDo as tienDo, " +
            "TongTDLuyKeTCTBC.ttThang as ttThang, TongTDLuyKeTCTBC.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCTBC.ttNam as ttNam, TongTDLuyKeTCTBC.ttTbnNam as ttTbnNam, TongTDLuyKeTCTBC.khthang as keHoach," +
            "TongTDLuyKeTCTBC.lkthang as luyKeThang, TongTDLuyKeTCTBC.danhgia as danhgia, TongTDLuyKeTCTBC.tienDoNgay as tienDoNgay, " +
            "TongTDLuyKeTCTBC.tiLeHoanThanhNgay as tiLeHoanThanhNgay, TongTDLuyKeTCTBC.keHoachThangDenNgay as keHoachThangDenNgay, " +
            "TongTDLuyKeTCTBC.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN TongTDLuyKeTCTBC " +
            "WHERE TongTDLuyKeTCTBC.ngayBaoCao = :ngayBaoCao " +
            "AND TongTDLuyKeTCTBC.chiNhanh = :chiNhanh " +
            "AND TongTDLuyKeTCTBC.chiNhanh IN :listChiNhanhVeriable ")
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeTCTBCV2(
            @Param("chiNhanh") String chiNhanh,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable);

    @Query("SELECT TongTDLuyKeTCTBC.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCTBC.mien as mien, TongTDLuyKeTCTBC.tlht as tlht, TongTDLuyKeTCTBC.chiNhanh as chiNhanh,TongTDLuyKeTCTBC.tienDo as tienDo, " +
            "TongTDLuyKeTCTBC.ttThang as ttThang, TongTDLuyKeTCTBC.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCTBC.ttNam as ttNam, TongTDLuyKeTCTBC.ttTbnNam as ttTbnNam, TongTDLuyKeTCTBC.khthang as keHoach," +
            "TongTDLuyKeTCTBC.lkthang as luyKeThang, TongTDLuyKeTCTBC.danhgia as danhgia, TongTDLuyKeTCTBC.tienDoNgay as tienDoNgay, " +
            "TongTDLuyKeTCTBC.tiLeHoanThanhNgay as tiLeHoanThanhNgay, TongTDLuyKeTCTBC.keHoachThangDenNgay as keHoachThangDenNgay, " +
            "TongTDLuyKeTCTBC.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTBuuCuc TongTDLuyKeTCTBC " +
            "WHERE TongTDLuyKeTCTBC.ngayBaoCao = :ngayBaoCao " +
            "AND ( :chiNhanh is null OR :chiNhanh = '' OR TongTDLuyKeTCTBC.chiNhanh = :chiNhanh ) " +
            "AND ( :buuCuc is null OR :buuCuc = '' OR TongTDLuyKeTCTBC.buuCuc = :buuCuc ) " +
            "AND TongTDLuyKeTCTBC.buuCuc IN :listBuuCucVeriable ")
    List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeTCTRoleBuuCuc(
            @Param("chiNhanh") String chiNhanh,
            @Param("buuCuc") String buuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable);
}
