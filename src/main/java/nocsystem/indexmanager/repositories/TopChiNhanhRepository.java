package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.TopChiNhanh.TopChiNhanh;
import nocsystem.indexmanager.models.TopChiNhanh.TopChiNhanhKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TopChiNhanhRepository extends JpaRepository<TopChiNhanh, TopChiNhanhKey> {
    @Query(value =
            "SELECT * FROM ( " +
                    "    SELECT *, ROW_NUMBER() OVER(PARTITION BY type ORDER BY case when (:filterSort) = 'ascend' then tl_hoanthanh END ASC, case when (:filterSort) = 'descend' then tl_hoanthanh end desc ) as rn " +
                    "    FROM top_chinhanh_cldv " +
                    "    WHERE ngay_baocao = :ngayBaoCao and is_luyke = :luyKe and ma_bc = 'N/A' " +
                    "    AND ma_cn NOT IN ('CNTCT', 'FFM', 'LOG', 'TTKDCP') " +
                    ") as ranked " +
                    "WHERE rn <= 10 " +
                    "ORDER BY case when (:filterSort) = 'ascend' then tl_hoanthanh END ASC, case when (:filterSort) = 'descend' then tl_hoanthanh END DESC ",
            nativeQuery = true)
    List<TopChiNhanh> findTop10ChiNhanh(LocalDate ngayBaoCao, String filterSort, Integer luyKe);

    @Query(value =
            "SELECT * FROM ( " +
                    "    SELECT *, ROW_NUMBER() OVER(PARTITION BY type ORDER BY case when (:filterSort) = 'ascend' then tl_hoanthanh END ASC, case when (:filterSort) = 'descend' then tl_hoanthanh end desc ) as rn " +
                    "    FROM top_chinhanh_cldv " +
                    "    WHERE ngay_baocao = :ngayBaoCao and ma_cn = :maChiNhanh  and is_luyke = :luyKe and ma_bc <> 'N/A' " +
                    "    AND ma_cn NOT IN ('CNTCT', 'FFM', 'LOG', 'TTKDCP') " +
                    ") as ranked " +
                    "WHERE rn <= 10 " +
                    "ORDER BY case when (:filterSort) = 'ascend' then tl_hoanthanh END ASC, case when (:filterSort) = 'descend' then tl_hoanthanh END DESC ",
            nativeQuery = true)
    List<TopChiNhanh> findTop10BuuCuc(LocalDate ngayBaoCao, String maChiNhanh, String filterSort, Integer luyKe);
}
