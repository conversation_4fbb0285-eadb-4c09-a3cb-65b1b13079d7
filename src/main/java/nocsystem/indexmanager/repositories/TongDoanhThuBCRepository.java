package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.TongDoanhThu.*;
import nocsystem.indexmanager.models.TongDoanhThu.TongDoanhThuBC;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TongDoanhThuBCRepository extends PagingAndSortingRepository<TongDoanhThuBC, Long> {
    @Query("SELECT tdt_bc.maBuuCuc as maBuuCuc, tdt_bc.ngayBaoCao as ngayBaoCao, tdt_bc.maChiNhanh as maChi<PERSON>hanh, " +
            "tdt_bc.tongDoanhThu as tong<PERSON><PERSON>h<PERSON>hu, tdt_bc.tlht as tlht, tdt_bc.ttThang as ttThang, " +
            "tdt_bc.ttNam as ttNam, tdt_bc.ttTbnThang as ttTbnThang, tdt_bc.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuBC tdt_bc " +
            "WHERE tdt_bc.maBuuCuc = :maBC " +
            "AND tdt_bc.maChiNhanh = :maCN " +
            "AND tdt_bc.ngayBaoCao = :toTime " +
            "AND tdt_bc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by tdt_bc.maChiNhanh asc ")
    Page<TongDoanhThuBCV1ResDto> findTongDoanhThuBCAndCN(
            @Param("maBC") String maBC,
            @Param("maCN") String maCN,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
            );

    @Query("SELECT tdt_bc.maBuuCuc as maBuuCuc, tdt_bc.ngayBaoCao as ngayBaoCao, tdt_bc.maChiNhanh as maChiNhanh, " +
            "tdt_bc.tongDoanhThu as tongDoanhThu, tdt_bc.tlht as tlht, tdt_bc.ttThang as ttThang, " +
            "tdt_bc.ttNam as ttNam, tdt_bc.ttTbnThang as ttTbnThang, tdt_bc.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuBC tdt_bc " +
            "WHERE tdt_bc.ngayBaoCao = :toTime " +
            "AND tdt_bc.maChiNhanh = :maCN " +
            "AND tdt_bc.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by tdt_bc.maChiNhanh asc ")
    Page<TongDoanhThuBCV1ResDto> findTongDoanhThuBCAndCNAll(
            @Param("maCN") String maCN,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT tdt_bc.maBuuCuc as maBuuCuc, tdt_bc.ngayBaoCao as ngayBaoCao, tdt_bc.maChiNhanh as maChiNhanh, " +
            "tdt_bc.tongDoanhThu as tongDoanhThu, tdt_bc.tlht as tlht, tdt_bc.ttThang as ttThang, " +
            "tdt_bc.ttNam as ttNam, tdt_bc.ttTbnThang as ttTbnThang, tdt_bc.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuBC tdt_bc " +
            "WHERE tdt_bc.ngayBaoCao = :toTime " +
            "AND tdt_bc.maChiNhanh = :maCN " +
            "AND (tdt_bc.maBuuCuc IN :listBuuCucVeriable ) " +
            "order by tdt_bc.maChiNhanh asc ")
    Page<TongDoanhThuBCV1ResDto> findTongDoanhThuBCAndCNAllV2(
            @Param("maCN") String maCN,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT tdt_bc.maBuuCuc as maBuuCuc, tdt_bc.ngayBaoCao as ngayBaoCao, tdt_bc.maChiNhanh as maChiNhanh, " +
            "tdt_bc.tongDoanhThu as tongDoanhThu, tdt_bc.tlht as tlht, tdt_bc.ttThang as ttThang, " +
            "tdt_bc.ttNam as ttNam, tdt_bc.ttTbnThang as ttTbnThang, tdt_bc.ttTbnNam as ttTbnNam " +
            "FROM TongDoanhThuBC tdt_bc " +
            "WHERE tdt_bc.maBuuCuc = :maBC " +
            "AND tdt_bc.maChiNhanh = :maCN " +
            "AND tdt_bc.ngayBaoCao = :toTime")
    CSKDTongDoanhThuOverViewDto findTongDoanhThuBC(
            @Param("maCN") String maCN,
            @Param("maBC") String maBC,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.tongDoanhThu as tongDoanhThu, " +
            "dtbc.keHoach as keHoach, dtbc.tlht as tlht, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay " +
            "FROM TongDoanhThuBC dtbc " +
            "WHERE (:maChiNhanh is null OR :maChiNhanh = '' OR dtbc.maChiNhanh = :maChiNhanh ) " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR dtbc.maBuuCuc = :maBuuCuc)" +
            "AND dtbc.ngayBaoCao = :toTime ")
    Slice<TongDoanhThuDetailScreenDto> findListDoanhThuBCOfCN(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.tongDoanhThu as tongDoanhThu, " +
            "dtbc.keHoach as keHoach, dtbc.tlht as tlht, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay " +
            "FROM TongDoanhThuBC dtbc " +
            "WHERE (:maChiNhanh is null OR :maChiNhanh = '' OR dtbc.maChiNhanh = :maChiNhanh ) " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR dtbc.maBuuCuc = :maBuuCuc)" +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    Page<TongDoanhThuCNV1ResDto> findListDoanhThuBCOfCNV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );

    @Query("SELECT dtbc.maBuuCuc as maBuuCuc, dtbc.maChiNhanh as maChiNhanh, dtbc.tongDoanhThu as tongDoanhThu, " +
            "dtbc.keHoach as keHoach, dtbc.tlht as tlht, " +
            "dtbc.ttThang as ttThang, dtbc.ttTbnThang as ttTbnThang, dtbc.ttNam as ttNam, dtbc.ttTbnNam as ttTbnNam, " +
            "dtbc.thangTruoc as thangTruoc, dtbc.cungKyThang as cungKyThang, dtbc.namTruoc as namTruoc, " +
            "dtbc.cungKyNam as cungKyNam, dtbc.cungKyNgay as cungKyNgay " +
            "FROM TongDoanhThuBC dtbc " +
            "WHERE (:maChiNhanh is null OR :maChiNhanh = '' OR dtbc.maChiNhanh = :maChiNhanh ) " +
            "AND (:maBuuCuc is null OR :maBuuCuc = '' OR dtbc.maBuuCuc = :maBuuCuc)" +
            "AND dtbc.ngayBaoCao = :toTime " +
            "AND dtbc.maBuuCuc IN :listBuuCucVeriable ")
    Page<TongDoanhThuBCV1ResDto> findListDoanhThuBCOfCNV3(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listBuuCucVeriable") List<String> listBuuCucVeriable
    );
}
