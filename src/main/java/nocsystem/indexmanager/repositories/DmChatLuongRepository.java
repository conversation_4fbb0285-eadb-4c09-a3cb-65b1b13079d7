package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.DmChatLuong.DmChatLuongKho;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.DmChatLuongBuuTaResDto;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.DmChatLuongKhoResDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DmChatLuongRepository extends JpaRepository<DmChatLuongKho, Integer> {

    @Query("SELECT p.tlDonLayNhanDungGio as tlDonLayNhanDungGio, " +
            "p.tlDonLayTC as tlDonLayTC, " +
            "p.tlGiaoDungGio as tlGiaoDungGio, " +
            "p.tlGiaoTC as tlGiaoTC, " +
            "p.tlTon<PERSON>han as tlTon<PERSON>han, " +
            "p.tlTonTTBcNhan as tlTonTTBcNhan, " +
            "p.tlTonGiao as tlTonGiao, " +
            "p.tlKhaiThacKPI3hTTKT as tlKhaiThacKPI3hTTKT, " +
            "p.tlTonPCNhan as tlTonPCNhan,"+
            "p.tgKhaiThacTBTTKT as tgKhaiThacTBTTKT,"+
            "p.tenTinh as tenTinh, " +
            "p.maTinh as maTinh, " +
            "p.maBuuCuc as maBuuCuc, " +
            "p.tbChatLuong as tbChatLuong, " +
            "p.ngayBaoCao as ngayBaoCao " +
            "FROM DmChatLuongKho p WHERE " +
            " (:maTinh is null or :maTinh = '' or p.maTinh = :maTinh) " +
            " and (:maBuuCuc is null or :maBuuCuc = '' or p.maBuuCuc = :maBuuCuc) and p.ngayBaoCao = :ngayBaoCao")
    public List<DmChatLuongKhoResDto> searchKho(
            @Param("maTinh") String maTinh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT p.maNhanVien as maNhanVien, p.maBuuCuc as maBuuCuc, p.ngayBaoCao as ngayBaoCao," +
            "p.maChiNhanh as maChiNhanh, p.tlNhanTC as tlNhanTC, p.tlNhanDG as tlNhanDG, " +
            "p.tlTonLay as tlTonLay, p.tlGiaoTC as tlGiaoTC, p.tlGiaoTDG as tlGiaoTDG, p.tbChatLuong as tbChatLuong FROM DmChatLuongBuuTa p where" +
            " (:maNhanVien is null or :maNhanVien = '' or p.maNhanVien = :maNhanVien) " +
            " and (:maChiNhanh is null or :maChiNhanh = '' or p.maChiNhanh = :maChiNhanh) " +
            " and (:maBuuCuc is null or :maBuuCuc = '' or p.maBuuCuc = :maBuuCuc) and p.ngayBaoCao = :ngayBaoCao")
    public List<DmChatLuongBuuTaResDto> searchBuuTa(
            @Param("maNhanVien") String maNhanVien,
            @Param("maChiNhanh") String maChiNhanh,
            @Param("maBuuCuc") String maBuuCuc,
            @Param("ngayBaoCao") LocalDate ngayBaoCao);
}
