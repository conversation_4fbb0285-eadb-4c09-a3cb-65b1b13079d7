package nocsystem.indexmanager.repositories.baoCaoTon;

import nocsystem.indexmanager.common.StringCommon;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.excel_helper.ExportExcelResulSet;
import nocsystem.indexmanager.excel_helper.ZipHelper;
import nocsystem.indexmanager.exception.ObjectException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.sql.*;
import java.sql.Date;
import java.util.*;

@Component
public class TonPhatJdbc extends AbstractDao {
    private static final Logger log = LoggerFactory.getLogger(TonPhatJdbc.class);

    @Autowired
    private DataSource dataSource;
    private final String MA_CHISO = "ton_phat";

    public final Integer LIMIT_SIZE = 300000;
    public static String getSqlTonPhat(String list_chinhanh_ht, String list_ma_buu_cuc_ht) {

        return "SELECT " +
                "d.ma_phieugui, TO_CHAR(d.ngay_baocao, 'YYYY-MM-dd') as ngay_baocao, d.tinh_nhan, d.huyen_nhan, d.ten_huyen_nhan, d.ttkt_from, d.tinh_phat, " +
                "d.huyen_phat, d.ten_huyen_phat, d.ttkt_to, d.ma_dv_viettel, d.ma_buucuc_goc, d.time_tac_dong, d.trang_thai, " +
                "d.ma_buucuc_ht, d.chi_nhanh_ht, d.ma_doitac, d.ma_khgui, d.ma_buucuc_phat, d.time_pcp, d.time_gach_bp, " +
                "d.ngay_gui_bp, d.danh_gia, d.loai_pg, d.lan_phat, d.tg_con_phat, d.buu_ta_phat, d.tien_cod, d.khau_fm, " +
                "d.khau_mm, d.khau_lm, d.tg_quydinh, d.tg_tt_luyke, d.tg_chenhlech, d.dg_moc_mm, d.is_checked, " +
                "d.loai_pcp, d.loai_canh_bao, d.dg_moc_lm, d.nhom_dv, d.version, d.time_tinh_toan, d.trong_luong, d.tien_cuoc, d.loai_hh, " +
                "(case when d.kh_dacthu_gui = '1' then 'SVIP1, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when d.kh_dacthu_gui = '2' then 'SVIP2, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when d.kh_dacthu_gui = '3' then 'VIP-NG, KH đối ngoại, ngoại giao' " +
                "when d.kh_dacthu_gui = '4' then 'VIP-KHL, KH thân thiết doanh thu lớn' " +
                "when d.kh_dacthu_gui = '5' then 'VIP-KHHT, KH hệ thống, sàn TMĐT' " +
                "when d.kh_dacthu_gui = '6' then 'ĐT-MXH, KH đặc thù - Báo chí, mạng xã hội' " +
                "when d.kh_dacthu_gui = '7' then 'ĐT-KN, KH đặc thù, khiếu nại' " +
                "else kh_dacthu_gui end) as kh_dacthu_gui, " +
                "(case when d.kh_dacthu_nhan = '1' then 'SVIP1, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when d.kh_dacthu_nhan = '2' then 'SVIP2, Ban lãnh đạo Tập đoàn, KH vị thế' " +
                "when d.kh_dacthu_nhan = '3' then 'VIP-NG, KH đối ngoại, ngoại giao' " +
                "when d.kh_dacthu_nhan = '4' then 'VIP-KHL, KH thân thiết doanh thu lớn' " +
                "when d.kh_dacthu_nhan = '5' then 'VIP-KHHT, KH hệ thống, sàn TMĐT' " +
                "when d.kh_dacthu_nhan = '6' then 'ĐT-MXH, KH đặc thù - Báo chí, mạng xã hội' " +
                "when d.kh_dacthu_nhan = '7' then 'ĐT-KN, KH đặc thù, khiếu nại' " +
                "else kh_dacthu_nhan end) as kh_dacthu_nhan, " +
                "d.id_phuongxa_phat, d.ten_phuongxa_phat, d.ma_dv_cong_them, d.tg_ton_pcp_cuoi_cung, d.time_pcp_dau_tien, d.loai_don " +
                "FROM tonphat_chitiet_v2 d " +
                "WHERE d.ngay_baocao = ? " +
                "AND (? IS NULL OR d.chi_nhanh_ht in (" + list_chinhanh_ht + " ))" +
                "AND (? IS NULL OR d.ma_buucuc_ht in (" + list_ma_buu_cuc_ht + " ))" +
                "AND (? IS NULL OR d.buu_ta_phat = ?) " +
                "AND (? IS NULL OR d.is_checked = ?) " +
                "AND (? IS NULL OR d.dg_moc_lm = ?) " +
                "AND (? IS NULL OR d.nhom_dv = ?) " +
                "AND (? IS NULL OR d.vung_con = ?) " +
                "AND (? IS NULL OR d.trang_thai = ?)"+
                "AND (? IS NULL OR d.ma_doitac = ?) " +
                "AND (? IS NULL OR d.loai_hh = ?) " +
                "AND (? IS NULL OR d.kh_dacthu_gui = ?) " +
                "AND (? IS NULL OR d.kh_dacthu_nhan = ?) " +
                "AND d.version = ? ";


    }

    public void setStatementMapValue(List<String> listFilter, PreparedStatement stmt, Integer index) throws SQLException {
        if (listFilter == null || listFilter.isEmpty()) return ;
        for (String s : listFilter) {

            if (s == null || s.isEmpty()) {
                stmt.setString(index++, null);
                stmt.setString(index++, "LaGiCungDuoc");
            } else {
                stmt.setString(index++, "#NULL");
                stmt.setString(index++, s);
            }
        }
    }

    public List<String> listHeader(){
        return  Arrays.asList(
                "STT",
                "MA_PHIEUGUI",
                "TINH_NHAN",
                "HUYEN_NHAN",
                "TEN_HUYEN_NHAN",
                "TTKT_FROM",
                "TINH_PHAT",
                "HUYEN_PHAT",
                "TEN_HUYEN_PHAT",
                "TTKT_TO",
                "MA_DV_VIETTEL",
                "MA_BUUCUC_GOC",
                "TIME_TAC_DONG",
                "TRANG_THAI",
                "MA_BUUCUC_HT",
                "CHI_NHANH_HT",
                "MA_DOITAC",
                "MA_KHGUI",
                "MA_BUUCUC_PHAT",
                "TIME_PCP",
                "NGAY_GUI_BP",
                "LOAI_PG",
                "LAN_PHAT",
                "BUU_TA_PHAT",
                "TIEN_COD",
                "TG_QUYDINH",
                "TG_TT_LUYKE",
                "TG_CHENHLECH",
                "IS_CHECKED",
                "LOAI_PCP",
                "NGAY_BAOCAO",
                "LOAI_CANH_BAO",
                "DG_MOC_LM",
                "NHOM_DV",
                "TRONG_LUONG",
                "TIEN_CUOC",
                "LOAI_HH",
                "kh_dacthu_gui",
                "kh_dacthu_nhan",
                "ID_PHUONGXA_PHAT",
                "TEN_PHUONGXA_PHAT",
                "MA_DV_CONG_THEM",
                "TG_TON_PCP_CUOI_CUNG",
                "TIME_PCP_DAU_TIEN",
                "loai_don"
        );

    }
    public void setParamInStatement(PreparedStatement stmt,
                                    String ngayBaoCao,
                                    String list_chinhanh_ht,
                                    String list_ma_buu_cuc_ht,
                                    String tuyenBuuTaPhat,
                                    String nguongTon,
                                    String loaiCanhBao,
                                    String loaiDichVu,
                                    String vungCon,
                                    String trangThai,
                                    String maDoiTac,
                                    String loaiHH,
                                    String khDacThuGui,
                                    String khDacThuNhan,
                                    long version
    ) throws SQLException {

        int index = 1;
        stmt.setDate(index++, Date.valueOf(ngayBaoCao));
        if (list_chinhanh_ht == null || list_chinhanh_ht.isEmpty()) {
            stmt.setString(index++, null);
        } else {
            stmt.setString(index++, "#NULL");
        }

        if (list_ma_buu_cuc_ht == null || list_ma_buu_cuc_ht.isEmpty()) {
            stmt.setString(index++, null);
        } else {
            stmt.setString(index++, "#NULL");
        }
        List<String> listFilter = new ArrayList<>(Arrays.asList(
                tuyenBuuTaPhat,
                nguongTon,
                loaiCanhBao,
                loaiDichVu,
                vungCon,
                trangThai,
                maDoiTac,
                loaiHH,
                khDacThuGui,
                khDacThuNhan
        ));

        setStatementMapValue(listFilter, stmt, index);

        //TODO : tai sao lai index + 20 vi ham setStatementMapValue co 20 tham so duoc set nhung vi index thoat khoi ham se ko thay doi => phai cong them 20
        stmt.setLong(index + 20, version);
    }

    public ResponseEntity<?> exportExcelChiTietTonPhat(
            HttpServletResponse response,
            String ngayBaoCao,
            List<String> chiNhanhPhat,
            List<String> buuCucPhat,
            String tuyenBuuTaPhat,
            String nguongTon,
            String loaiCanhBao,
            String loaiDichVu,
            String vungCon,
            String trangThai,
            String maDoiTac,
            String loaiHH,
            String khDacThuGui,
            String khDacThuNhan,
            long version
    ) {

        if (ngayBaoCao != null){
            String list_chinhanh_ht = null;
            String list_ma_buu_cuc_ht = null;
            if (!(chiNhanhPhat == null || chiNhanhPhat.isEmpty())) {
                list_chinhanh_ht = StringCommon.convertListToStringForQueryIn(chiNhanhPhat);
            }
            if (!(buuCucPhat == null || buuCucPhat.isEmpty())) {
                list_ma_buu_cuc_ht = StringCommon.convertListToStringForQueryIn(buuCucPhat);
            }

            String sql = getSqlTonPhat(list_chinhanh_ht, list_ma_buu_cuc_ht);

            Connection conn = null;
            PreparedStatement stmt = null;
            ResultSet rs = null;

            int limit = LIMIT_SIZE;

            int numRow = 0;

            response.setContentType("application/vnd.ms.excel");
            String headerKey = "Content-Disposition";
            String headerValue = "attachment; filename=chiTietBuuGuiTonPhat.xlsx";
            response.setHeader(headerKey, headerValue);

            try {
                conn = dataSource.getConnection();
                stmt = conn.prepareStatement(sql);
                setParamInStatement(
                        stmt,
                        ngayBaoCao,
                        list_chinhanh_ht,
                        list_ma_buu_cuc_ht,
                        tuyenBuuTaPhat,
                        nguongTon,
                        loaiCanhBao,
                        loaiDichVu,
                        vungCon,
                        trangThai,
                        maDoiTac,
                        loaiHH,
                        khDacThuGui,
                        khDacThuNhan,
                        version
                );
                rs = stmt.executeQuery();

                Map<String, String> dictHeader = new HashMap<>();
                for(String s : listHeader()){
                    dictHeader.put(s, s);
                }
                ExportExcelResulSet exportExcelResulSet = new ExportExcelResulSet(conn, stmt, rs, dictHeader, listHeader(), listHeader(), numRow, "sheet_1");

                exportExcelResulSet.writeDataToStream(limit);

                List<ByteArrayOutputStream> outputStreamList = exportExcelResulSet.outputStreamList;
                if((outputStreamList == null || outputStreamList.isEmpty()) ){
                    outputStreamList = new ArrayList<>();
                }

                ZipHelper zipHelper = new ZipHelper();
                byte[] zippedBytes = zipHelper.zipStreams(outputStreamList, MA_CHISO);

                log.info(" sau khi byte[] zippedBytes = zipStreams(outputStreamList, tenBaoCao) ");
                zipHelper.returnZipFile(response, zippedBytes, MA_CHISO);

                zippedBytes = null;
                outputStreamList.clear();
                exportExcelResulSet  = null;
                dictHeader = null;

            }catch (Exception e) {

                e.printStackTrace();
                eLogger.error("Error from excel_export_zip_not_thread, reason: {}", e);
                throw new ObjectException(" export excel ton phat fail ");
            } finally {
                releaseConnect(conn, stmt, rs);
                System.gc();
            }
        }
        return ResponseEntity.ok().build();
    }
}
