package nocsystem.indexmanager.repositories.baocao_taichinh.logic;


import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.repositories.baocao_taichinh.logic.query.BaoCaoTaiChinhSql;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoTaiChinhResponseDb;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Service
public class BaoCaoTaiChinhRepoImpl extends AbstractDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;


    public List<BaoCaoTaiChinhResponseDb> getDataFromFilter(BaoCaoTaiChinhBody body, String sql) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, Integer.parseInt(body.filterThang + body.filterNam));
            rs = stmt.executeQuery();

            List<BaoCaoTaiChinhResponseDb> list = new ArrayList<>();
            while (rs.next()) {
                BaoCaoTaiChinhResponseDb response = new BaoCaoTaiChinhResponseDb();
                response.setMaSo(rs.getString("ma_so"));

                if (body.loaiBaoCao == null || body.loaiBaoCao.isEmpty()) {
                    response.setGiaTriLoaiBaoCao(rs.getFloat("hop_nhat"));
                } else response.setGiaTriLoaiBaoCao(rs.getFloat(body.loaiBaoCao));
                response.setLoaiBang(rs.getString("loai_bang"));
                list.add(response);
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        } finally {
            releaseConnect(conn, stmt, rs);
        }
    }

    public BaoCaoTaiChinhResponseDb getDataFromMaSo(BaoCaoTaiChinhBody body, String maSo) {
        String sql = BaoCaoTaiChinhSql.getSqlTaiSanBinhQuan(body.loaiBaoCao, maSo);
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            stmt = conn.prepareStatement(sql);
            stmt.setInt(1, Integer.parseInt(body.filterThang + body.filterNam));
            rs = stmt.executeQuery();

            BaoCaoTaiChinhResponseDb response = new BaoCaoTaiChinhResponseDb();

            while (rs.next()) {
                response.setMaSo(rs.getString("ma_so"));
                if (body.loaiBaoCao == null || body.loaiBaoCao.equals("")) {
                    response.setGiaTriLoaiBaoCao(rs.getFloat("hop_nhat"));
                } else response.setGiaTriLoaiBaoCao(rs.getFloat(body.loaiBaoCao));
                response.setLoaiBang(rs.getString("loai_bang"));
                break;
            }
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return new BaoCaoTaiChinhResponseDb();
        } finally {
            releaseConnect(conn, stmt, rs);
        }
    }


    public List<BaoCaoTaiChinhResponseDb> getDataB02(BaoCaoTaiChinhBody body, String sql) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();

            List<BaoCaoTaiChinhResponseDb> list = new ArrayList<>();
            while (rs.next()) {
                BaoCaoTaiChinhResponseDb response = new BaoCaoTaiChinhResponseDb();
                response.setMaSo(rs.getString("ma_so"));

                if (body.loaiBaoCao == null || body.loaiBaoCao.isEmpty()) {
                    response.setGiaTriLoaiBaoCao(rs.getFloat("hop_nhat"));
                } else response.setGiaTriLoaiBaoCao(rs.getFloat(body.loaiBaoCao));
                response.setLoaiBang(rs.getString("loai_bang"));
                list.add(response);
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        } finally {
            releaseConnect(conn, stmt, rs);
        }
    }
}
