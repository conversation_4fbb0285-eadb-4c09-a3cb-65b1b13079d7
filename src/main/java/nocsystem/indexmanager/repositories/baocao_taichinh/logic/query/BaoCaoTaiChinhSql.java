package nocsystem.indexmanager.repositories.baocao_taichinh.logic.query;

public class BaoCaoTaiChinhSql {
    public static String getSqlFromLoaiBaoCao( String loaiBaoCao, String listMaSo) {
        if (loaiBaoCao == null || loaiBaoCao.isEmpty()) {
            loaiBaoCao = "hop_nhat";
        }

        return "select ma_so , " +  loaiBaoCao + " , loai_bang from bao_cao_tai_chinh where thang = ?" +
                " and ma_so in (" +  listMaSo  + " ) and loai_bang in ('B01' , 'B03' )";
    }

    public static String getSqlTaiSanBinhQuan( String loaiBaoCao, String maSo) {
        if (loaiBaoCao == null || loaiBaoCao.isEmpty()) {
            loaiBaoCao = "hop_nhat";
        }
        return "select ma_so , " +  loaiBaoCao + " , loai_bang from bao_cao_tai_chinh where thang = ?" +
                " and loai_bang in ('B01' , 'B03' ) and ma_so = '" + maSo + "' ";
    }


    public static String getSqlTableB02( String loaiBaoCao, String maSo, String thang) {
        if (loaiBaoCao == null || loaiBaoCao.isEmpty()) {
            loaiBaoCao = "hop_nhat";
        }
        return "select ma_so , " +  loaiBaoCao + " , loai_bang from bao_cao_tai_chinh where thang in ("  + thang +
                    " ) and ma_so in ( " + maSo + " ) and loai_bang = 'B02'";
    }


// todo chart

}
