package nocsystem.indexmanager.repositories.baocao_taichinh.logic;


import nocsystem.indexmanager.common.BodyCustom;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.repositories.baocao_taichinh.logic.query.KeHoachSql;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.response.KeHoach3NgayResponseDb;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@Component
public class KeHoachRepoImpl extends AbstractDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final Logger log = LoggerFactory.getLogger(BaoCaoTaiChinhRepoImpl.class);

    public List<KeHoach3NgayResponseDb> getDataFromFilter(BaoCaoTaiChinhBody body, String type){
        String sql = KeHoachSql.getSqlFromLoaiBaoCao(body.loaiBaoCao);
        log.info("sql ==== " + sql);
        BodyCustom custom =  new BodyCustom();
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try {
            conn = jdbcTemplate.getDataSource().getConnection();
            stmt = conn.prepareStatement(sql);
            if(type.equals("thang")){

                stmt.setInt(1, Integer.parseInt(body.filterThang + body.filterNam));
                stmt.setString(2, "Tháng");
            }
            else if(type.equals("quy")){
                stmt.setInt(1, Integer.parseInt(custom.getQuyHienTaiChoBangKeHoach(body.filterThang) + body.filterNam));
                stmt.setString(2, "Quý");
            }
            else {
                stmt.setInt(1, Integer.parseInt(body.filterNam));
                stmt.setString(2, "Năm");
            }

            rs = stmt.executeQuery();

            List<KeHoach3NgayResponseDb> list = new ArrayList<>();
            while (rs.next()) {
                KeHoach3NgayResponseDb response = new KeHoach3NgayResponseDb();
                response.setChiTieu(rs.getString("chi_tieu"));
                response.setTanSuat(rs.getString("tan_suat"));
                response.setKy(rs.getInt("ky"));

                if(body.loaiBaoCao == null || body.loaiBaoCao.equals("")){
                    response.setGiaTriLoaiBaoCao(rs.getFloat("hop_nhat"));
                }
                else response.setGiaTriLoaiBaoCao(rs.getFloat(body.loaiBaoCao));
                response.setLoaiBang(rs.getString("loai_bang"));

                list.add(response);
            }
            return list;
        } catch (Exception e) {
//            eLogger.error("Error when get data in table ke_hoach_bc_ngay3, reason: {}", e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        } finally {
            releaseConnect(conn, stmt, rs);
        }
    }
}
