package nocsystem.indexmanager.repositories.baocao_taichinh;

import nocsystem.indexmanager.models.baocao_taichinh.TaiChinhModel;
import nocsystem.indexmanager.models.baocao_taichinh.TaiChinhModelId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface BaoCaoTaiChinhRepo extends JpaRepository<TaiChinhModel, TaiChinhModelId> {

    @Query("SELECT t FROM TaiChinhModel t " +
            "WHERE t.ma_so IN :listMaSo " +
            "AND t.thang = :thang " +
            "AND t.loai_bang IN :loaiBangs")
    List<TaiChinhModel> findByMaSoAndNgayBaoCaoAndLoaiBangIn(
            @Param("listMaSo") List<String> listMaSo,
            @Param("thang") Integer thang,
            @Param("loaiBangs") List<String> loaiBangs);



}
