package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.*;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.ThongKeLuyKe.TienDoLuyKeTCTCN;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoLuyKeTCTChiNhanhRepository extends JpaRepository<TienDoLuyKeTCTCN, Long>, PagingAndSortingRepository<TienDoLuyKeTCTCN, Long> {

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as keHoach, " +
            "tdLuyKeTCT.lkthang as luyKeThang, tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<TienDoluyKeTCTCNResDto> findTienDoLuyKeTCTCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as keHoach, " +
            "tdLuyKeTCT.lkthang as luyKeThang, tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND (tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable) " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<TienDoluyKeTCTCNResDto> findTienDoLuyKeTCTCNV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'B' " +
            "AND tdLuyKeTCT.ngayBaoCao = :ngayBaoCao")
    Page<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeChiNhanhMB(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            Pageable pageable);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'B' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND tdLuyKeTCT.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeBCMB(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'B' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeBCMBV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'T' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND tdLuyKeTCT.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeCNMT(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'T' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND (tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable )" +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeCNMTV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable);

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'N' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND tdLuyKeTCT.chiNhanh NOT IN :exceptionChiNhanh " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<TinhTongLuyKeChiNhanhMNDto> listDetailLuyKeCNMienNam(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh);


    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'N' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND (tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable) " +
            "ORDER BY tdLuyKeTCT.chiNhanh asc ")
    Page<TinhTongLuyKeChiNhanhMNDto> listDetailLuyKeCNMienNamV2(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable);

    /*
     * Tính tổng lũy kế chi nhánh miền Bắc
     */
    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'B' " +
            "AND tdLuyKeTCT.chiNhanh = :chiNhanh " +
            "AND tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable ")
    TinhTongLuyKeChiNhanhMBDto listTongLuyKeMoiChiNhanhMB(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'B' " +
            "AND tdLuyKeTCT.chiNhanh = :chiNhanh ")
    TinhTongLuyKeChiNhanhMBDto listTongLuyKeMoiChiNhanhMBRoleAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh
    );

    /*
     * Tính tổng lũy kế chi nhánh miền Trung
     */
    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'T' " +
            "AND tdLuyKeTCT.chiNhanh = :chiNhanh " +
            "AND tdLuyKeTCT.chiNhanh IN :listChiNhanhVeriable ")
    TinhTongLuyKeChiNhanhMTDto listTongLuyKeMoiChiNhanhMienTrung(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );


    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'T' " +
            "AND tdLuyKeTCT.chiNhanh = :chiNhanh ")
    TinhTongLuyKeChiNhanhMTDto listTongLuyKeMoiChiNhanhMienTrungRoleAdin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh
    );

    /*
     * Tính tổng lũy kế chi nhánh miền Nam
     */
    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'N' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) " +
            "AND tdLuyKeTCT.chiNhanh in :listChiNhanhVeriable")
    TinhTongLuyKeChiNhanhMNDto listTongLuyKeMoiChiNhanhMienNam(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT tdLuyKeTCT.ngayBaoCao as ngayBaoCao, tdLuyKeTCT.chiNhanh as chiNhanh, " +
            "tdLuyKeTCT.mien as mien, tdLuyKeTCT.tlht as tlht, tdLuyKeTCT.tienDo as tienDo, " +
            "tdLuyKeTCT.ttThang as ttThang, tdLuyKeTCT.ttTbnThang as ttTbnThang," +
            "tdLuyKeTCT.ttNam as ttNam, tdLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "tdLuyKeTCT.danhgia as danhgia, tdLuyKeTCT.khthang as khthang, tdLuyKeTCT.lkthang as lkthang, " +
            "tdLuyKeTCT.tienDoNgay as tienDoNgay, tdLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "tdLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, tdLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TienDoLuyKeTCTCN tdLuyKeTCT " +
            "WHERE tdLuyKeTCT.ngayBaoCao = :ngayBaoCao " +
            "AND tdLuyKeTCT.mien = 'N' " +
            "AND (:chiNhanh is null OR :chiNhanh = '' OR tdLuyKeTCT.chiNhanh = :chiNhanh) ")
    TinhTongLuyKeChiNhanhMNDto listTongLuyKeMoiChiNhanhMienNamRuleAdmin(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") String chiNhanh
    );
}
