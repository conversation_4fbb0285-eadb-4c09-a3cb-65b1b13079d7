package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeLogistic.TinhTongTienDoLuyKeLogisticDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMNDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMTDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TongTienDoLuyKeTCTCNResDto;
import nocsystem.indexmanager.models.ThongKeLuyKe.TongTienDoLuyKeTCTCN;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TongTienDoLuyKeTCTChiNhanhRepository extends JpaRepository<TongTienDoLuyKeTCTCN, Long> {

    @Query("SELECT TongTDLuyKeTCT.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCT.mien as mien, TongTDLuyKeTCT.tlht as tlht, TongTDLuyKeTCT.tienDo as tienDo, " +
            "TongTDLuyKeTCT.ttThang as ttThang, TongTDLuyKeTCT.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCT.ttNam as ttNam, TongTDLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "TongTDLuyKeTCT.khthang as keHoach, TongTDLuyKeTCT.lkthang as luyKeThang," +
            "TongTDLuyKeTCT.danhgia as danhgia, TongTDLuyKeTCT.tienDoNgay as tienDoNgay, TongTDLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "TongTDLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, TongTDLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TongTienDoLuyKeTCTCN TongTDLuyKeTCT " +
            "WHERE TongTDLuyKeTCT.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeTCT.mien = 'TCT'")
    List<TongTienDoLuyKeTCTCNResDto> findTongTienDoLuyKeTCTCN(
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT TongTDLuyKeTCT.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCT.mien as mien, TongTDLuyKeTCT.tlht as tlht, TongTDLuyKeTCT.tienDo as tienDo, " +
            "TongTDLuyKeTCT.ttThang as ttThang, TongTDLuyKeTCT.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCT.ttNam as ttNam, TongTDLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "TongTDLuyKeTCT.danhgia as danhgia, TongTDLuyKeTCT.lkthang as lkthang, TongTDLuyKeTCT.khthang as khthang, " +
            "TongTDLuyKeTCT.tienDoNgay as tienDoNgay, TongTDLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "TongTDLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, TongTDLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TongTienDoLuyKeTCTCN TongTDLuyKeTCT " +
            "WHERE TongTDLuyKeTCT.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeTCT.mien = 'B'")
    TinhTongLuyKeChiNhanhMBDto findTongTienDoLuyKeMienBac(
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT TongTDLuyKeTCT.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCT.mien as mien, TongTDLuyKeTCT.tlht as tlht, TongTDLuyKeTCT.tienDo as tienDo, " +
            "TongTDLuyKeTCT.ttThang as ttThang, TongTDLuyKeTCT.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCT.ttNam as ttNam, TongTDLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "TongTDLuyKeTCT.danhgia as danhgia, TongTDLuyKeTCT.khthang as khthang, TongTDLuyKeTCT.lkthang as lkthang, " +
            "TongTDLuyKeTCT.tienDoNgay as tienDoNgay, TongTDLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "TongTDLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, TongTDLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TongTienDoLuyKeTCTCN TongTDLuyKeTCT " +
            "WHERE TongTDLuyKeTCT.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeTCT.mien = 'T'")
    TinhTongLuyKeChiNhanhMTDto findTongTienDoLuyKeMienTrung(
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT TongTDLuyKeTCT.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCT.mien as mien, TongTDLuyKeTCT.tlht as tlht, TongTDLuyKeTCT.tienDo as tienDo, " +
            "TongTDLuyKeTCT.ttThang as ttThang, TongTDLuyKeTCT.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCT.ttNam as ttNam, TongTDLuyKeTCT.ttTbnNam as ttTbnNam, " +
            "TongTDLuyKeTCT.danhgia as danhgia, TongTDLuyKeTCT.khthang as khthang, TongTDLuyKeTCT.lkthang as lkthang, " +
            "TongTDLuyKeTCT.tienDoNgay as tienDoNgay, TongTDLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "TongTDLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, TongTDLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TongTienDoLuyKeTCTCN TongTDLuyKeTCT " +
            "WHERE TongTDLuyKeTCT.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeTCT.mien = 'N'")
    TinhTongLuyKeChiNhanhMNDto findTongTienDoLuyKeMienNam(
            @Param("ngayBaoCao") LocalDate ngayBaoCao);

    @Query("SELECT TongTDLuyKeTCT.ngayBaoCao as ngayBaoCao," +
            "TongTDLuyKeTCT.mien as mien, TongTDLuyKeTCT.tlht as tlht, TongTDLuyKeTCT.tienDo as tienDo, " +
            "TongTDLuyKeTCT.ttThang as ttThang, TongTDLuyKeTCT.ttTbnThang as ttTbnThang," +
            "TongTDLuyKeTCT.ttNam as ttNam, TongTDLuyKeTCT.ttTbnNam as ttTbnNam, TongTDLuyKeTCT.danhgia as danhgia, " +
            "TongTDLuyKeTCT.khthang as keHoach, TongTDLuyKeTCT.lkthang as luyKeThang, " +
            "TongTDLuyKeTCT.tienDoNgay as tienDoNgay, TongTDLuyKeTCT.tiLeHoanThanhNgay as tiLeHoanThanhNgay, " +
            "TongTDLuyKeTCT.keHoachThangDenNgay as keHoachThangDenNgay, TongTDLuyKeTCT.thucHienNgay as thucHienNgay " +
            "FROM TongTienDoLuyKeTCTCN TongTDLuyKeTCT " +
            "WHERE TongTDLuyKeTCT.ngayBaoCao = :ngayBaoCao AND TongTDLuyKeTCT.mien = 'CTLT'")
    TinhTongTienDoLuyKeLogisticDto findTongTienDoLuyKeLogistic(
            @Param("ngayBaoCao") LocalDate ngayBaoCao);
}
