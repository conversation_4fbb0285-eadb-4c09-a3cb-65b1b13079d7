package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuJPADto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.CSKDTienDoDoanhThuCPOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;
import nocsystem.indexmanager.models.TienDoDoanhThuChuyenPhat.TienDoDoanhThuCPChiNhanh;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPCNV2ResDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuCPChiNhanhRepository extends JpaRepository<TienDoDoanhThuCPChiNhanh, Long>, PagingAndSortingRepository<TienDoDoanhThuCPChiNhanh, Long> {
    @Query("SELECT  dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.loaiDichVu as loaiDichVu " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn WHERE dtcpcn.maChiNhanh = :maChiNhanh " +
            "AND dtcpcn.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpcn.loaiDichVu = :loaiDichVu) " +
            "AND dtcpcn.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtcpcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCPCNV2ResDto> findTienDoDoanhThuBuuCucAndChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT  dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.loaiDichVu as loaiDichVu " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn WHERE dtcpcn.maChiNhanh = :maChiNhanh " +
            "AND dtcpcn.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpcn.loaiDichVu = :loaiDichVu) " +
            "AND dtcpcn.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtcpcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCPCNV2ResDto> findTienDoDoanhThuBuuCucAndChiNhanhV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.loaiDichVu as loaiDichVu, " +
            "dtcpcn.tlht as tlht, dtcpcn.tienDo as tienDo, dtcpcn.thucHien as thucHien, dtcpcn.keHoach as keHoach, " +
            "dtcpcn.ttThang as ttThang, dtcpcn.ttTbnThang as ttTbnThang, dtcpcn.ttNam as ttNam " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn WHERE dtcpcn.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpcn.loaiDichVu = :loaiDichVu) " +
            "AND dtcpcn.maChiNhanh NOT IN :exceptionChiNhanh " +
            "order by dtcpcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCPCNV2ResDto> findTienDoDoanhThuCPCNFollowTime(
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("exceptionChiNhanh") List<String> exceptionChiNhanh
    );

    @Query("SELECT dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.loaiDichVu as loaiDichVu, " +
            "dtcpcn.tlht as tlht, dtcpcn.tienDo as tienDo, dtcpcn.thucHien as thucHien, dtcpcn.keHoach as keHoach, " +
            "dtcpcn.ttThang as ttThang, dtcpcn.ttTbnThang as ttTbnThang, dtcpcn.ttNam as ttNam " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn WHERE dtcpcn.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpcn.loaiDichVu = :loaiDichVu) " +
            "AND (dtcpcn.maChiNhanh IN :listChiNhanhVeriable) " +
            "order by dtcpcn.maChiNhanh asc ")
    Page<TienDoDoanhThuCPCNV2ResDto> findTienDoDoanhThuCPCNFollowTimeV2(
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT dtcpcn.tlht as tlht, dtcpcn.maChiNhanh as maChiNhanh, " +
            "dtcpcn.tenChiNhanh as tenChiNhanh " +
            "FROM TienDoDoanhThuChiNhanh dtcpcn WHERE dtcpcn.ngayBaoCao = :ngayBaoCao and dtcpcn.nhomDoanhThu = 'DT-CP'")
    public List<TienDoDoanhThuCNResDto> findTienDoDoanhThuCPCNFollowTimeLuyKe(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT dtcpcn.tlht as tlht, dtcpcn.maChiNhanh as maChiNhanh, " +
            "dtcpcn.tenChiNhanh as tenChiNhanh " +
            "FROM TienDoDoanhThuChiNhanh dtcpcn " +
            "WHERE dtcpcn.ngayBaoCao = :ngayBaoCao and dtcpcn.nhomDoanhThu = 'DT-CP' " +
            "and dtcpcn.tenChiNhanh in :chiNhanh ")
    public List<TienDoDoanhThuCNResDto> findTienDoDoanhThuCPCNFollowTimeLuyKeSSO(
            @Param("ngayBaoCao") LocalDate ngayBaoCao,
            @Param("chiNhanh") List<String> listChiNhanh
    );

    @Query("SELECT  dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.keHoach as keHoach, dtcpcn.thucHien as thucHien, " +
            "dtcpcn.cungKyNgay as cungKyNgay, dtcpcn.cungKyThang as cungKyThang, dtcpcn.cungKyNam as cungKyNam, " +
            "dtcpcn.thangTruoc as thangTruoc, dtcpcn.namTruoc as namTruoc, dtcpcn.tienDo as tienDo, " +
            "dtcpcn.loaiDichVu as loaiDichVu, dtcpcn.tlht as tlHoanThanh, dtcpcn.ttThang as ttThang, dtcpcn.ttNam as ttNam, " +
            "dtcpcn.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn WHERE dtcpcn.ngayBaoCao = :toTime " +
            "AND (dtcpcn.maChiNhanh = :maChiNhanh OR :maChiNhanh is null OR :maChiNhanh = '') ")
    List<CSKDTienDoDoanhThuCPOverViewDto> findTienDoDoanhChiNhanhOverView(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime
    );

    @Query("SELECT  dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.keHoach as keHoach, dtcpcn.thucHien as thucHien, " +
            "dtcpcn.cungKyNgay as cungKyNgay, dtcpcn.cungKyThang as cungKyThang, dtcpcn.cungKyNam as cungKyNam, " +
            "dtcpcn.thangTruoc as thangTruoc, dtcpcn.namTruoc as namTruoc, dtcpcn.tienDo as tienDo, " +
            "dtcpcn.loaiDichVu as loaiDichVu, dtcpcn.tlht as tlHoanThanh, dtcpcn.ttThang as ttThang, dtcpcn.ttNam as ttNam, " +
            "dtcpcn.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn WHERE dtcpcn.ngayBaoCao = :toTime " +
            "AND (dtcpcn.maChiNhanh = :maChiNhanh OR :maChiNhanh is null OR :maChiNhanh = '') " +
            "AND dtcpcn.maChiNhanh IN :listChiNhanhVeriable")
    List<CSKDTienDoDoanhThuCPOverViewDto> findTienDoDoanhChiNhanhOverViewV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("toTime") LocalDate toTime,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    /**
     * Tính tổng tiến độ doanh thu CP chi nhánh
     *
     * @param maChiNhanh
     * @param loaiDichVu
     * @param toTime
     * @return
     */
    @Query("SELECT  dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.keHoach as keHoach, dtcpcn.thucHien as thucHien, " +
            "dtcpcn.cungKyNgay as cungKyNgay, dtcpcn.cungKyThang as cungKyThang, dtcpcn.cungKyNam as cungKyNam, " +
            "dtcpcn.thangTruoc as thangTruoc, dtcpcn.namTruoc as namTruoc, dtcpcn.tienDo as tienDo, " +
            "dtcpcn.loaiDichVu as loaiDichVu, dtcpcn.tlht as tlht, dtcpcn.ttThang as ttThang, dtcpcn.ttNam as ttNam, " +
            "dtcpcn.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtcpcn.maChiNhanh = :maChiNhanh) " +
            "AND dtcpcn.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpcn.loaiDichVu = :loaiDichVu) ")
    Slice<CSKDTienDoDoanhThuCPOverViewDto> findListTinhTongTDDTCPChiNhanh(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable
    );

    @Query("SELECT  dtcpcn.maChiNhanh as maChiNhanh, dtcpcn.keHoach as keHoach, dtcpcn.thucHien as thucHien, " +
            "dtcpcn.cungKyNgay as cungKyNgay, dtcpcn.cungKyThang as cungKyThang, dtcpcn.cungKyNam as cungKyNam, " +
            "dtcpcn.thangTruoc as thangTruoc, dtcpcn.namTruoc as namTruoc, dtcpcn.tienDo as tienDo, " +
            "dtcpcn.loaiDichVu as loaiDichVu, dtcpcn.tlht as tlht, dtcpcn.ttThang as ttThang, dtcpcn.ttNam as ttNam, " +
            "dtcpcn.ttTbnThang as ttTbnThang " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn " +
            "WHERE (:maChiNhanh = '' OR :maChiNhanh is null OR dtcpcn.maChiNhanh = :maChiNhanh) " +
            "AND dtcpcn.ngayBaoCao = :toTime " +
            "AND (:loaiDichVu = '' OR :loaiDichVu is null OR dtcpcn.loaiDichVu = :loaiDichVu) " +
            "AND dtcpcn.maChiNhanh IN :listChiNhanhVeriable")
    Slice<CSKDTienDoDoanhThuCPOverViewDto> findListTinhTongTDDTCPChiNhanhV2(
            @Param("maChiNhanh") String maChiNhanh,
            @Param("loaiDichVu") String loaiDichVu,
            @Param("toTime") LocalDate toTime,
            Pageable pageable,
            @Param("listChiNhanhVeriable") List<String> listChiNhanhVeriable
    );

    @Query("SELECT  dtcpcn.loaiDichVu as loaiDichVu, dtcpcn.tiLeTangTruongNgay as tiLeTangTruongNgay, dtcpcn.ngayBaoCao as ngayBaoCao " +
            "FROM TienDoDoanhThuCPChiNhanh dtcpcn " +
            "WHERE dtcpcn.ngayBaoCao <= :ngayKetThuc " +
            "AND dtcpcn.ngayBaoCao >= :ngayBatDau " +
            "AND dtcpcn.maChiNhanh = :maChiNhanh ")
    List<BieuDoTtTienDoDoanhThuJPADto> bieuDoTtTienDoDoanhThu(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc,
            @Param("maChiNhanh") String maChiNhanh
    );
}
