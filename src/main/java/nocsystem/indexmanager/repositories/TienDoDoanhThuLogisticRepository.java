package nocsystem.indexmanager.repositories;

import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuLogisticJPADto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticV1Dto;
import nocsystem.indexmanager.models.TienDoDoanhThuChuyenPhat.TienDoDoanhThuCP;
import nocsystem.indexmanager.models.TienDoDoanhThuLogistic.TienDoDoanhThuLogistic;
import nocsystem.indexmanager.models.TienDoDoanhThuLogistic.TienDoDoanhThuLogisticChiNhanh;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TienDoDoanhThuLogisticRepository extends JpaRepository<TienDoDoanhThuLogistic, Long> {
    @Query( "SELECT dtLogistic.ngayBaoCao as ngayBaoCao, dtLogistic.dichVu as dichVu, " +
            "dtLogistic.tlHoanThanh as tlHoanThanh, dtLogistic.tienDo as tienDo, dtLogistic.ttThang as ttThang, " +
            "dtLogistic.ttTbnThang as ttTbnThang, dtLogistic.ttNam as ttNam, dtLogistic.thucHien as thucHien, " +
            "dtLogistic.keHoach as keHoach, dtLogistic.cungKyNgay as cungKyNgay, dtLogistic.cungKyThang as cungKyThang, " +
            "dtLogistic.cungKyNam as cungKyNam, dtLogistic.thangTruoc as thangTruoc, dtLogistic.namTruoc as namTruoc " +
            "FROM TienDoDoanhThuLogistic dtLogistic " +
            "WHERE dtLogistic.ngayBaoCao = :ngayBaoCao ")
    public List<TienDoDoanhThuLogisticOverViewDto> findDoanhThuLogistic(
            @Param("ngayBaoCao") LocalDate ngayBaoCao
    );

    @Query("SELECT dtbc.tiLeTangTruongNgay as tiLeTangTruongNgay, dtbc.ngayBaoCao as ngayBaoCao, dtbc.dichVu as loaiDichVu " +
            "FROM TienDoDoanhThuLogistic dtbc " +
            "WHERE dtbc.ngayBaoCao >= :ngayBatDau AND dtbc.ngayBaoCao <= :ngayKetThuc ")
    List<BieuDoTtTienDoDoanhThuLogisticJPADto> dataBieuDoTienDoDoanhThuLogistic(
            @Param("ngayBatDau") LocalDate ngayBatDau,
            @Param("ngayKetThuc") LocalDate ngayKetThuc
    );
}
