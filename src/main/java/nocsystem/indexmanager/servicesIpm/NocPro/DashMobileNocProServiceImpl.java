package nocsystem.indexmanager.servicesIpm.NocPro;

import com.google.common.base.Strings;
import nocsystem.indexmanager.dao.NocPro.DashMobileNocProDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.NocPro.ChiTietHieuQuaKDResponse;
import nocsystem.indexmanager.models.Response.NocPro.TongQuanHieuQuaKDResponse;
import nocsystem.indexmanager.services.NocPro.DashMobileNocProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;


@Service
public class DashMobileNocProServiceImpl implements DashMobileNocProService {
    @Autowired
    DashMobileNocProDAO dashMobileNocProDAO;

    @Override
    public TongQuanHieuQuaKDResponse getDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, String vungCon, String nhomKhachHang, String nhomDichVu, List<String> mucTrongLuong, String dichVu) {

        TongQuanHieuQuaKDResponse tongQuanHieuQuaKDResponse = new TongQuanHieuQuaKDResponse();

        if (UserContext.getUserData().getIsRoleViewNocPro().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            List<String> listDichVu;
            if (Strings.isNullOrEmpty(nhomDichVu)) {
                listDichVu = Arrays.asList("EXP", "CPTN");
            } else {
                listDichVu = Collections.singletonList(nhomDichVu);
            }

            List<String> listTrongLuong;
            if (mucTrongLuong == null) {
                listTrongLuong = new ArrayList<>();
            } else {
                listTrongLuong = mucTrongLuong;
            }

            tongQuanHieuQuaKDResponse = dashMobileNocProDAO.getDashboard(ngayBaoCao, mien, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, vungCon, nhomKhachHang, listDichVu, listTrongLuong, dichVu);
        }

        return tongQuanHieuQuaKDResponse;
    }

    @Override
    public ListContentPageDto<ChiTietHieuQuaKDResponse> getDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, String vungCon, String nhomKhachHang, String nhomDichVu, List<String> mucTrongLuong, String dichVu, String order, String orderBy, Integer page, Integer pageSize) {
        if (UserContext.getUserData().getIsRoleViewNocPro().equalsIgnoreCase("true")) {
            List<ChiTietHieuQuaKDResponse> listData = new ArrayList<>();

            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            List<String> listDichVu;
            if (Strings.isNullOrEmpty(nhomDichVu)) {
                listDichVu = Arrays.asList("EXP", "CPTN");
            } else {
                listDichVu = Collections.singletonList(nhomDichVu);
            }

            List<String> listTrongLuong;
            if (mucTrongLuong == null) {
                listTrongLuong = new ArrayList<>();
            } else {
                listTrongLuong = mucTrongLuong;
            }

            listData = dashMobileNocProDAO.getDetailDashboard(ngayBaoCao, mien, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, vungCon, nhomKhachHang, listDichVu, listTrongLuong, dichVu);

            if (!Strings.isNullOrEmpty(order) && !Strings.isNullOrEmpty(orderBy)) {
                // Nếu tlht = N/A -> đẩy xuống cuối list
                switch (orderBy) {
                    case "tlhtDonGiaKg":
                        if (order.equalsIgnoreCase("asc")) {
                            Collections.sort(listData, (d1, d2) -> {
                                if (d1.getDonGiaKgResponse().getTlhtDonGia().equals("N/A") && d2.getDonGiaKgResponse().getTlhtDonGia().equals("N/A")) {
                                    return 0; // d1 same d2
                                } else if (d1.getDonGiaKgResponse().getTlhtDonGia().equals("N/A")) {
                                    return 1; // d2 before d1
                                } else if (d2.getDonGiaKgResponse().getTlhtDonGia().equals("N/A")) {
                                    return -1; // d1 before d2
                                } else {
                                    return Double.compare(Double.parseDouble(d1.getDonGiaKgResponse().getTlhtDonGia()), Double.parseDouble(d2.getDonGiaKgResponse().getTlhtDonGia()));
                                }
                            });
                        }
                        if (order.equalsIgnoreCase("desc")) {
                            Collections.sort(listData, (d1, d2) -> {
                                if (d1.getDonGiaKgResponse().getTlhtDonGia().equals("N/A") && d2.getDonGiaKgResponse().getTlhtDonGia().equals("N/A")) {
                                    return 0; // d1 same d2
                                } else if (d1.getDonGiaKgResponse().getTlhtDonGia().equals("N/A")) {
                                    return 1; // d2 before d1
                                } else if (d2.getDonGiaKgResponse().getTlhtDonGia().equals("N/A")) {
                                    return -1; // d1 before d2
                                } else {
                                    return Double.compare(Double.parseDouble(d2.getDonGiaKgResponse().getTlhtDonGia()), Double.parseDouble(d1.getDonGiaKgResponse().getTlhtDonGia()));
                                }
                            });
                        }
                        break;
                    case "tlhtDonGiaBill":
                        if (order.equalsIgnoreCase("asc")){
                            Collections.sort(listData, (d1, d2) -> {
                                if (d1.getDonGiaBillResponse().getTlhtDonGia().equals("N/A") && d2.getDonGiaBillResponse().getTlhtDonGia().equals("N/A")) {
                                    return 0; // d1 same d2
                                } else if (d1.getDonGiaBillResponse().getTlhtDonGia().equals("N/A")) {
                                    return 1; // d2 before d1
                                } else if (d2.getDonGiaBillResponse().getTlhtDonGia().equals("N/A")) {
                                    return -1; // d1 before d2
                                } else {
                                    return Double.compare(Double.parseDouble(d1.getDonGiaBillResponse().getTlhtDonGia()), Double.parseDouble(d2.getDonGiaBillResponse().getTlhtDonGia()));
                                }
                            });
                        }
                        if (order.equalsIgnoreCase("desc")) {
                            Collections.sort(listData, (d1, d2) -> {
                                if (d1.getDonGiaBillResponse().getTlhtDonGia().equals("N/A") && d2.getDonGiaBillResponse().getTlhtDonGia().equals("N/A")) {
                                    return 0; // d1 same d2
                                } else if (d1.getDonGiaBillResponse().getTlhtDonGia().equals("N/A")) {
                                    return 1; // d2 before d1
                                } else if (d2.getDonGiaBillResponse().getTlhtDonGia().equals("N/A")) {
                                    return -1; // d1 before d2
                                } else {
                                    return Double.compare(Double.parseDouble(d2.getDonGiaBillResponse().getTlhtDonGia()), Double.parseDouble(d1.getDonGiaBillResponse().getTlhtDonGia()));
                                }
                            });
                        }
                        break;
                    default:
                        break;
                }
            }

            Pageable pageable = PageRequest.of(page, pageSize);

            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), listData.size());

            if (start > listData.size())
                return new ListContentPageDto<>();

            List<ChiTietHieuQuaKDResponse> pageData = listData.subList(start, end);
            Page<ChiTietHieuQuaKDResponse> pageFinal = new PageImpl<>(pageData, pageable, listData.size());

            return new ListContentPageDto<>(pageFinal);
        }

        return new ListContentPageDto<>();
    }

    @Override
    public ChiTietHieuQuaKDResponse getTotalDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, String vungCon, String nhomKhachHang, String nhomDichVu, List<String> mucTrongLuong, String dichVu) {
        ChiTietHieuQuaKDResponse result = new ChiTietHieuQuaKDResponse();

        if (UserContext.getUserData().getIsRoleViewNocPro().equalsIgnoreCase("true")) {

            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            List<String> listDichVu;
            if (Strings.isNullOrEmpty(nhomDichVu)) {
                listDichVu = Arrays.asList("EXP", "CPTN");
            } else {
                listDichVu = Collections.singletonList(nhomDichVu);
            }

            List<String> listTrongLuong;
            if (mucTrongLuong == null) {
                listTrongLuong = new ArrayList<>();
            } else {
                listTrongLuong = mucTrongLuong;
            }

            result = dashMobileNocProDAO.getTotalDetailDashboard(ngayBaoCao, mien, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, vungCon, nhomKhachHang, listDichVu, listTrongLuong, dichVu);
        }

        return result;
    }
}
