package nocsystem.indexmanager.servicesIpm.KpiDayChuyenChiaChonTTKT5;

import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonBodyDto;
import nocsystem.indexmanager.models.Response.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonResponse;
import nocsystem.indexmanager.repositories.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonTTKT5Repository;
import nocsystem.indexmanager.services.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonTTKT5Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class KpiDayChuyenChiaChonTTKT5ServiceImpl extends AbstractDao implements KpiDayChuyenChiaChonTTKT5Service {

    @Autowired
    private KpiDayChuyenChiaChonTTKT5Repository kpiDayChuyenChiaChonTTKT5Repository;

    private boolean isViewKpiDayChuyenChiaChonTTKT5() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("LANH_DAO_TCT") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("LANH_DAO_TTVH") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("CHUYEN_QUAN_TCT_TTVH") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_LANHDAO_CONGTYLOG") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_CONGTY_LOG") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("CHUYEN_QUAN_TCT_CL") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("TCT_PCN");
    }

    @Override
    public Page<KpiDayChuyenChiaChonResponse> getListDetail(KpiDayChuyenChiaChonBodyDto body) {
        if (!isViewKpiDayChuyenChiaChonTTKT5()) {
            Pageable pageable = PageRequest.of(0, 1);
            Page<KpiDayChuyenChiaChonResponse> p = new PageImpl<>(new ArrayList<>(), pageable, 0);
            return p;
        }
        Page<KpiDayChuyenChiaChonResponse> pageImpl;
        LocalDate ngayBaoCao = LocalDate.parse(body.getNgayBaoCao());

        List<KpiDayChuyenChiaChonResponse> list = kpiDayChuyenChiaChonTTKT5Repository.listAllKpiDayChuyenChiaChon(ngayBaoCao);

        Pageable paging = PageRequest.of(body.getPageIndex(), body.getPageSize());

        int limitSize = paging.getPageSize();
        int offset = (int) paging.getOffset();
        int start = Math.min(offset, list.size());
        int end = Math.min((start + limitSize), list.size());

        if (list.isEmpty()) {
            Pageable pageable = PageRequest.of(0, 1);
            pageImpl = new PageImpl<>(new ArrayList<>(), pageable, 0);

        } else {
            pageImpl = new PageImpl<>(list.subList(start, end), paging, list.size());
        }
        return pageImpl;
    }
}

