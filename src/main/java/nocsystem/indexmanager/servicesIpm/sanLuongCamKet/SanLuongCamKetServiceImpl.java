package nocsystem.indexmanager.servicesIpm.sanLuongCamKet;

import com.google.common.base.Strings;
import nocsystem.indexmanager.dao.sanLuongCamKet.SanLuongCamKetDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatExcel;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.sanLuongCamKet.*;
import nocsystem.indexmanager.services.sanLuongCamKet.SanLuongCamKetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

@Service
public class SanLuongCamKetServiceImpl<T> implements SanLuongCamKetService {
    @Autowired
    SanLuongCamKetDAO sanLuongCamKetDAO;

    @Override
    public ListContentPageDto<SanLuongCamKet> getSanLuongCamKet(LocalDate ngayBaoCao, String vung, String chiNhanh, String vungCon, String buuCuc, String cusid, List<String> dichVu, String order, String orderBy, Integer page, Integer pageSize) {
        if(!UserContext.getUserData().getIsRoleViewSLCK().equalsIgnoreCase("true")) {
            return new ListContentPageDto<>();
        }

        List<SanLuongCamKet> listResponse = new ArrayList<>();

        List<String> chiNhanhSSO = new ArrayList<>();
        List<String> buuCucSSO = new ArrayList<>();

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
            chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
            chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
        }

        Map<String, List<SanLuongCamKet>> map = new HashMap<>();
        List<SanLuong> listSanLuong = sanLuongCamKetDAO.getSanLuong(ngayBaoCao, vung, chiNhanh, chiNhanhSSO, vungCon, buuCuc, buuCucSSO, cusid, dichVu);
        for(SanLuong sl: listSanLuong) {
            SanLuongCamKet slck = new SanLuongCamKet(
                    sl.getMaChiNhanh(),
                    sl.getCusid(),
                    sl.getMaKhachHang(),
                    sl.getMaDichVu(),
                    sl.getSanLuongCamKet(),
                    sl.getSanLuongGui(),
                    sl.getDoanhThu(),
                    null,
                    null,
                    null

            );
            if(map.containsKey(sl.getCusid() + sl.getMaDichVu())) {
                map.get(sl.getCusid() + sl.getMaDichVu()).add(slck);
            } else {
                map.put(sl.getCusid() + sl.getMaDichVu(), new ArrayList<>(List.of(slck)));
            }
        }

        List<Tyle> listTyle = sanLuongCamKetDAO.getTyLe(ngayBaoCao, vung, chiNhanh, chiNhanhSSO, vungCon, buuCuc, buuCucSSO, cusid, dichVu);
        for(Tyle tl: listTyle) {
            List<SanLuongCamKet> listSLCK = map.get(tl.getCusid() + tl.getMaDichVu());
            for(SanLuongCamKet slck: listSLCK) {
                slck.setTgCapNhatBangGia(tl.getTgCapNhatBangGia());
                slck.setTlSLCK(tl.getTlSLCK());
                slck.setTlSLCKDuKienThang(tl.getTlSLCKDuKienThang());
            }
        }

        listResponse.addAll(map.values().stream().flatMap(List::stream).collect(Collectors.toList()));

        if (!Strings.isNullOrEmpty(order) && !Strings.isNullOrEmpty(orderBy)) {
            switch (orderBy) {
                case "tlSLCK":
                    if (order.equalsIgnoreCase("asc"))
                        Collections.sort(listResponse, comparing(SanLuongCamKet::getTlSLCK, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (order.equalsIgnoreCase("desc"))
                        Collections.sort(listResponse, comparing(SanLuongCamKet::getTlSLCK, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlSLCKDKThang":
                    if (order.equalsIgnoreCase("asc"))
                        Collections.sort(listResponse, comparing(SanLuongCamKet::getTlSLCKDuKienThang, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (order.equalsIgnoreCase("desc"))
                        Collections.sort(listResponse, comparing(SanLuongCamKet::getTlSLCKDuKienThang, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
            }
        }

        Pageable pageable = PageRequest.of(page, pageSize);

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), listResponse.size());

        if (start > listResponse.size())
            return new ListContentPageDto<>();

        List<SanLuongCamKet> pageData = listResponse.subList(start, end);
        Page<SanLuongCamKet> pageFinal = new PageImpl<>(pageData, pageable, listResponse.size());

        return new ListContentPageDto<>(pageFinal);
    }

    @Override
    public void exportSanLuongCamKetExcel(LocalDate ngayBaoCao, String vung, String chiNhanh, String vungCon, String buuCuc, String cusid, List<String> dichVu, HttpServletResponse response) throws NoSuchFieldException, IllegalAccessException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=SLCK.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("maChiNhanh", "Mã chi nhánh");
        mapHeader.put("cusid", "Cusid");
        mapHeader.put("maKhachHang", "Mã khách hàng");
        mapHeader.put("maDichVu", "Mã dịch vụ");
        mapHeader.put("sanLuongCamKet", "Sản lượng cam kết");
        mapHeader.put("sanLuongGui", "Sản lượng gửi");
        mapHeader.put("doanhThu", "Doanh thu");
        mapHeader.put("tlSLCK", "Tỷ lệ % SLCK");
        mapHeader.put("tlSLCKDuKienThang", "Tỷ lệ % SLCK dự kiến tháng");
        mapHeader.put("tgCapNhatBangGia", "Thời gian cập nhật bảng giá");

        List<String> header = Arrays.asList(
                "stt",
                "maChiNhanh",
                "cusid",
                "maKhachHang",
                "maDichVu",
                "sanLuongCamKet",
                "sanLuongGui",
                "doanhThu",
                "tlSLCK",
                "tlSLCKDuKienThang",
                "tgCapNhatBangGia"
        );

        SanLuongCamKetExcelTemplate sanLuongCamKetExcelTemplate = new SanLuongCamKetExcelTemplate(
                findDataSLCKExcel(ngayBaoCao, vung, chiNhanh, vungCon, buuCuc, cusid, dichVu),
                mapHeader,
                header,
                "SLCK");
        try {
            sanLuongCamKetExcelTemplate.exportDataToExcel(response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<Map<String, Object>> findDataSLCKExcel(LocalDate ngayBaoCao, String vung, String chiNhanh, String vungCon, String buuCuc, String cusid, List<String> dichVu) throws NoSuchFieldException, IllegalAccessException {
        if(!UserContext.getUserData().getIsRoleViewSLCK().equalsIgnoreCase("true")) {
            return new ArrayList<>();
        }

        List<SanLuongCamKet> listResponse = new ArrayList<>();
        List<SanLuongCamKetExcel> listResponseExcel = new ArrayList<>();

        List<String> chiNhanhSSO = new ArrayList<>();
        List<String> buuCucSSO = new ArrayList<>();

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
            chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
            chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
        }

        Map<String, List<SanLuongCamKet>> map = new HashMap<>();
        List<SanLuong> listSanLuong = sanLuongCamKetDAO.getSanLuong(ngayBaoCao, vung, chiNhanh, chiNhanhSSO, vungCon, buuCuc, buuCucSSO, cusid, dichVu);
        for(SanLuong sl: listSanLuong) {
            SanLuongCamKet slck = new SanLuongCamKet(
                    sl.getMaChiNhanh(),
                    sl.getCusid(),
                    sl.getMaKhachHang(),
                    sl.getMaDichVu(),
                    sl.getSanLuongCamKet(),
                    sl.getSanLuongGui(),
                    sl.getDoanhThu(),
                    null,
                    null,
                    null
            );
            if(map.containsKey(sl.getCusid() + sl.getMaDichVu())) {
                map.get(sl.getCusid() + sl.getMaDichVu()).add(slck);
            } else {
                map.put(sl.getCusid() + sl.getMaDichVu(), new ArrayList<>(List.of(slck)));
            }
        }

        List<Tyle> listTyle = sanLuongCamKetDAO.getTyLe(ngayBaoCao, vung, chiNhanh, chiNhanhSSO, vungCon, buuCuc, buuCucSSO, cusid, dichVu);
        for(Tyle tl: listTyle) {
            List<SanLuongCamKet> listSLCK = map.get(tl.getCusid() + tl.getMaDichVu());
            for(SanLuongCamKet slck: listSLCK) {
                slck.setTgCapNhatBangGia(tl.getTgCapNhatBangGia());
                slck.setTlSLCK(tl.getTlSLCK());
                slck.setTlSLCKDuKienThang(tl.getTlSLCKDuKienThang());
            }
        }

        listResponse.addAll(map.values().stream().flatMap(List::stream).collect(Collectors.toList()));

        int stt = 1;
        for(SanLuongCamKet item: listResponse) {
            SanLuongCamKetExcel excel = new SanLuongCamKetExcel(
                    stt,
                    item.getMaChiNhanh(),
                    item.getCusid(),
                    item.getMaKhachHang(),
                    item.getMaDichVu(),
                    item.getSanLuongCamKet(),
                    item.getSanLuongGui(),
                    item.getDoanhThu(),
                    item.getTlSLCK(),
                    item.getTlSLCKDuKienThang(),
                    item.getTgCapNhatBangGia()
            );
            listResponseExcel.add(excel);
            stt++;
        }

        return tryTestDataSLCKExcel((List<T>) listResponseExcel);
    }


    public List<Map<String, Object>> tryTestDataSLCKExcel(List<T> list) {
        List<Map<String, Object>> total = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                SanLuongCamKetExcel x1 = (SanLuongCamKetExcel) x;
                total.add(x1.getObjectHashMap());
                i++;
            }
        }
        return total;
    }
}
