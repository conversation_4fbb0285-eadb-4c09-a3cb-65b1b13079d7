package nocsystem.indexmanager.servicesIpm.ChiSoKinhDoanh;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPCNDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuSMSDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.*;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOriginDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOverViewDto;
import nocsystem.indexmanager.repositories.*;
import nocsystem.indexmanager.services.ChiSoKinhDoanh.ChiSoKinDoanhService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;

@Component
public class ChiSoKinhDoanhIpml implements ChiSoKinDoanhService {
    @Autowired
    private RedisTemplate template;

    @Autowired
    private ChiSoKinhDoanhRepository chiSoKinhDoanhRepository;

    @Autowired
    private TongDoanhThuCNRepository tongDoanhThuCNRepo;

    @Autowired
    private TongDoanhThuBCRepository tongDoanhThuBCRepo;

    @Autowired
    private TienDoDoanhThuChiNhanhRepository tienDoDoanhThuChiNhanhRepo;

    @Autowired
    private TienDoDoanhThuBuuCucRepository tienDoDoanhThuBuuCucRepo;

    @Override
    public CSKDTongDoanhThuOriginDto findCSKDTongDoanhThu(LocalDate toTime, String maChiNhanh, String maBuuCuc) {
        List<CSKDTienDoDoanhThuV2Dto> cskdTongDoanhThu = this.findCSKDTienDoDoanhThu(maChiNhanh, maBuuCuc, toTime, false);
        if (cskdTongDoanhThu == null || cskdTongDoanhThu.isEmpty()) {
            return null;
        }

        CSKDTienDoDoanhThuV2Dto tongDoanhThu = new CSKDTienDoDoanhThuV2Dto();

        for (CSKDTienDoDoanhThuV2Dto cskdTongDoanhThuDetail : cskdTongDoanhThu) {
            if (cskdTongDoanhThuDetail.getNhomDt().equals("Tổng")) {
                tongDoanhThu = cskdTongDoanhThuDetail;
            }
        }

        CSKDTongDoanhThuOriginDto cskdTongDoanhThuResponse = this.convertFromDetailToTongDoanhThu(tongDoanhThu);

        return cskdTongDoanhThuResponse;
    }

    /**
     * Tính tiến độ doanh thu
     * TH1: nếu detail == true tức là sẽ search để lấy số liệu cho màn chi tiết
     * TH2: nếu detail == false tức là sẽ search để lấy số liệu cho màn hình tổng quan
     * Nguyên nhân: Do màn tổng quan phần logistic có liên quan đến doanh thu của Cty Log và một số loại doanh thu khác
     *
     * @param maChiNhanh
     * @param maBuuCuc
     * @param toTime
     * @param detail
     * @return
     */
    @Override
    public List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThu(String maChiNhanh, String maBuuCuc, LocalDate toTime, Boolean detail) {
//        if (detail == true) {
//            return this.findTotalTienDoDoanhThuOfAllBranch(maChiNhanh, maBuuCuc, toTime);
//        }

        if (!maChiNhanh.isEmpty() && maBuuCuc.isEmpty()) {
            return this.findCSKDTienDoDoanhThuTheoCN(maChiNhanh, toTime, maBuuCuc);
        } else if (!maBuuCuc.isEmpty() && !maChiNhanh.isEmpty()) {
            return this.findCSKDTienDoDoanhThuTheoBC(maChiNhanh, maBuuCuc, toTime);
        } else {
            if (!UserContext.getUserData().getIsAdmin().equals("true")) {
                return this.findCSKDTienDoDoanhThuTheoCN(maChiNhanh, toTime, maBuuCuc);
            }
            return this.findCSKDTienDoDoanhThu(toTime);
        }
    }

    //sonph13: process data cho SMS CSKD của ban tổng giám đốc
    @Override
    public CSKDTienDoDoanhThuSMSDto findCSKDTienDoDoanhThuSMS(String maChiNhanh, String maBuuCuc, LocalDate toTime, Boolean detail) {
        List<CSKDTienDoDoanhThuV2Dto> result;
        List<CSKDTienDoDoanhThuV2Dto> cungKyThang;
        List<CSKDTienDoDoanhThuV2Dto> cungKyNam;
        List<CSKDTienDoDoanhThuV2Dto> dataN1X1;
        if (!maChiNhanh.isEmpty() && maBuuCuc.isEmpty()) {
            result = findCSKDTienDoDoanhThuTheoCN(maChiNhanh, toTime, maBuuCuc);
            cungKyThang = findCSKDTienDoDoanhThuTheoCN(maChiNhanh, toTime.minusMonths(1), maBuuCuc);
            cungKyNam = findCSKDTienDoDoanhThuTheoCN(maChiNhanh, toTime.minusYears(1), maBuuCuc);
            dataN1X1 = findCSKDTienDoDoanhThuTheoCN(maChiNhanh, toTime.minusMonths(1).minusDays(1), maBuuCuc);
        } else if (!maBuuCuc.isEmpty() && !maChiNhanh.isEmpty()) {
            result = findCSKDTienDoDoanhThuTheoBC(maChiNhanh, maBuuCuc, toTime);
            cungKyThang = findCSKDTienDoDoanhThuTheoBC(maChiNhanh, maBuuCuc, toTime.minusMonths(1));
            cungKyNam = findCSKDTienDoDoanhThuTheoBC(maChiNhanh, maBuuCuc, toTime.minusYears(1));
            dataN1X1 = findCSKDTienDoDoanhThuTheoBC(maChiNhanh, maBuuCuc, toTime.minusMonths(1).minusDays(1));
        } else {
            result = findCSKDTienDoDoanhThu(toTime);
            cungKyThang = findCSKDTienDoDoanhThu(toTime.minusMonths(1));
            cungKyNam = findCSKDTienDoDoanhThu(toTime.minusYears(1));
            dataN1X1 = findCSKDTienDoDoanhThu(toTime.minusMonths(1).minusDays(1));
        }
        CSKDTienDoDoanhThuSMSDto response = new CSKDTienDoDoanhThuSMSDto();

        if (result == null || result.isEmpty()) {
            return null;
        }

        response = processDataForEachCN(result, cungKyThang, cungKyNam, dataN1X1);
        return  response;
    }

    //sonph13: process data cho SMS CSKD của chi nhánh
    @Override
    public List<CSKDTienDoDoanhThuSMSDto> findCSKDTienDoDoanhThuSMSCN(List<String> listChiNhanh, LocalDate toTime) {
        List<CSKDTienDoDoanhThuV2Dto> result = new ArrayList<>();
        List<CSKDTienDoDoanhThuV2Dto> cungKyThang;
        List<CSKDTienDoDoanhThuV2Dto> cungKyNam;
        List<CSKDTienDoDoanhThuV2Dto> dataN1X1;

        //get list cskd theo list chi nhánh
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNModel = tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverViewV2("", toTime, listChiNhanh);
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNCungKyThang = tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverViewV2("", toTime.minusMonths(1), listChiNhanh);
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNCungKyNam = tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverViewV2("", toTime.minusYears(1), listChiNhanh);
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNN1X1 = tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverViewV2("", toTime.minusMonths(1).minusDays(1), listChiNhanh);

        //chuyển list thành table để tiện cho việc search
        Table<String, String,CSKDTienDoDoanhThuOverViewDto> resultTable = convertToTable(tienDoDoanhThuCNModel);
        Table<String, String,CSKDTienDoDoanhThuOverViewDto> cungKyThangTable = convertToTable(tienDoDoanhThuCNCungKyThang);
        Table<String, String,CSKDTienDoDoanhThuOverViewDto> cungKyNamTable = convertToTable(tienDoDoanhThuCNCungKyNam);
        Table<String, String,CSKDTienDoDoanhThuOverViewDto> n1X1Table = convertToTable(tienDoDoanhThuCNN1X1);

        List<CSKDTienDoDoanhThuSMSDto> response = new ArrayList<>();
        for(String chiNhanh : listChiNhanh){
            result = findCSKDTienDoDoanhThuTheoCNSMS(resultTable, chiNhanh);
            cungKyThang = findCSKDTienDoDoanhThuTheoCNSMS(cungKyThangTable, chiNhanh);
            cungKyNam = findCSKDTienDoDoanhThuTheoCNSMS(cungKyNamTable, chiNhanh);
            dataN1X1 = findCSKDTienDoDoanhThuTheoCNSMS(n1X1Table, chiNhanh);
            if(result == null || result.isEmpty()){
                continue;
            }
            response.add(processDataForEachCN(result, cungKyThang, cungKyNam, dataN1X1));
        }
        return  response;
    }

    //convert từ list sang table
    private Table<String, String, CSKDTienDoDoanhThuOverViewDto> convertToTable(List<CSKDTienDoDoanhThuOverViewDto> listCSKD){
        Table<String, String, CSKDTienDoDoanhThuOverViewDto> table = HashBasedTable.create();
        for (CSKDTienDoDoanhThuOverViewDto cskd : listCSKD){
            table.put(cskd.getMaChiNhanh(), cskd.getNhomDt(), cskd);
        }
        return table;
    }

    private CSKDTienDoDoanhThuSMSDto processDataForEachCN(List<CSKDTienDoDoanhThuV2Dto> result, List<CSKDTienDoDoanhThuV2Dto> cungKyThang, List<CSKDTienDoDoanhThuV2Dto> cungKyNam, List<CSKDTienDoDoanhThuV2Dto> dataN1X1){
        Float cungKyThangTong = Float.valueOf(0f);
        Float cungKyThangCP = Float.valueOf(0f);
        Float cungKyThangLOG = Float.valueOf(0f);
        CSKDTienDoDoanhThuSMSDto response = new CSKDTienDoDoanhThuSMSDto();

        if(cungKyThang != null) {
            for (CSKDTienDoDoanhThuV2Dto cs : cungKyThang) {
                if (cs.getNhomDt().equals("Tổng"))
                    cungKyThangTong = cs.getThucHien();
                if (cs.getNhomDt().equals("DT-CP"))
                    cungKyThangCP = cs.getThucHien();
                if (cs.getNhomDt().equals("DT-LOGISTICS"))
                    cungKyThangLOG = cs.getThucHien();
            }
        }

        Float cungKyNamTong = Float.valueOf(0f);
        Float cungKyNamCP = Float.valueOf(0f);
        Float cungKyNamLOG = Float.valueOf(0f);

        if(cungKyNam != null) {
            for (CSKDTienDoDoanhThuV2Dto cs : cungKyNam) {
                if (cs.getNhomDt().equals("Tổng"))
                    cungKyNamTong = cs.getThucHien();
                if (cs.getNhomDt().equals("DT-CP"))
                    cungKyNamCP = cs.getThucHien();
                if (cs.getNhomDt().equals("DT-LOGISTICS"))
                    cungKyNamLOG = cs.getThucHien();
            }
        }

//        Float n1X1 = Float.valueOf(0f);
//        Float n1X1CP = Float.valueOf(0f);
        Float n1X1LOG = Float.valueOf(0f);

        if(dataN1X1 != null){
            for(CSKDTienDoDoanhThuV2Dto cs : dataN1X1){
//                if(cs.getNhomDt().equals("Tổng"))
//                    n1X1 = cs.getThucHien();
//                if(cs.getNhomDt().equals("DT-CP"))
//                    n1X1CP = cs.getThucHien();
                if(cs.getNhomDt().equals("DT-LOGISTICS"))
                    n1X1LOG = cs.getThucHien();
            }
        }

        for(CSKDTienDoDoanhThuV2Dto cs : result){
            response.setMaChiNhanh(cs.getMaChiNhanh());
            switch (cs.getNhomDt()){
                case "Tổng":
                    response.setKeHoach(cs.getKeHoach());
                    response.setThucHien(cs.getThucHien());
                    response.setTlHoanThanh(cs.getTlHoanThanh());
                    response.setTlDoanhThu(cs.getTlDoanhThu());
                    response.setTienDo(cs.getTienDo());
                    response.setTtThang(cs.getTtThang());
                    response.setTtTbnThang(cs.getTtTbnThang());
                    response.setTtNam(cs.getTtNam());
                    response.setTtTbnNam(cs.getTtTbnNam());
                    response.setThangTruoc(cs.getThangTruoc());
                    response.setNamTruoc(cs.getNamTruoc());
                    response.setCungKyNgay(cs.getCungKyNgay());
                    response.setCungKyThang(cs.getThucHien() - cungKyThangTong);
                    response.setCungKyNam(cs.getThucHien() - cungKyNamTong);
                    response.setTongDTNgay(cs.getTongDTNgay());
                    response.setTiLeHoanThanhNgay(cs.getTiLeHoanThanhNgay());
                    response.setTtCungKyNgay(cs.getTtCungKyNgay());
                    response.setTtCungKyNgayPercent(cs.getTtCungKyNgayPercent());
                    response.setTTTbnNgay(cs.getTTTbnNgay());
                    response.setTtTbnNgayPercent(cs.getTtCungKyNgayPercent());
                    break;
                case "DT-CP":
                    response.setKeHoachCP(cs.getKeHoach());
                    response.setThucHienCP(cs.getThucHien());
                    response.setTlHoanThanhCP(cs.getTlHoanThanh());
                    response.setTlDoanhThuCP(cs.getTlDoanhThu());
                    response.setTienDoCP(cs.getTienDo());
                    response.setTtThangCP(cs.getTtThang());
                    response.setTtTbnThangCP(cs.getTtTbnThang());
                    response.setTtNamCP(cs.getTtNam());
                    response.setTtTbnNamCP(cs.getTtTbnNam());
                    response.setThangTruocCP(cs.getThangTruoc());
                    response.setNamTruocCP(cs.getNamTruoc());
                    response.setCungKyNgayCP(cs.getCungKyNgay());
                    response.setCungKyThangCP(cs.getThucHien() - cungKyThangCP);
                    response.setCungKyNamCP(cs.getThucHien() - cungKyNamCP);
                    response.setTongDTNgayCP(cs.getTongDTNgay());
                    response.setTiLeHoanThanhNgayCP(cs.getTiLeHoanThanhNgay());
                    response.setTtCungKyNgayCP(cs.getTtCungKyNgay());
                    response.setTtCungKyNgayPercentCP(cs.getTtCungKyNgayPercent());
                    response.setTTTbnNgayCP(cs.getTTTbnNgay());
                    response.setTtTbnNgayPercentCP(cs.getTtCungKyNgayPercent());
                    break;
                case "DT-LOGISTICS":
                    response.setKeHoachLOG(cs.getKeHoach());
                    response.setThucHienLOG(cs.getThucHien());
                    response.setTlHoanThanhLOG(cs.getTlHoanThanh());
                    response.setTlDoanhThuLOG(cs.getTlDoanhThu());
                    response.setTienDoLOG(cs.getTienDo());
                    response.setTtThangLOG(cs.getTtThang());
                    response.setTtTbnThangLOG(cs.getTtTbnThang());
                    response.setTtNamLOG(cs.getTtNam());
                    response.setTtTbnNamLOG(cs.getTtTbnNam());
                    response.setThangTruocLOG(cs.getThangTruoc());
                    response.setNamTruocLOG(cs.getNamTruoc());
                    response.setCungKyNgayLOG(cs.getCungKyNgay());
                    response.setCungKyThangLOG(cungKyThangLOG - n1X1LOG);
                    response.setCungKyNamLOG(cs.getThucHien() - cungKyNamLOG);
                    response.setTongDTNgayLOG(cs.getTongDTNgay());
                    response.setTiLeHoanThanhNgayLOG(cs.getTiLeHoanThanhNgay());
                    response.setTtCungKyNgayLOG(cs.getTtCungKyNgay());
                    response.setTtCungKyNgayPercentLOG(cs.getTtCungKyNgayPercent());
                    response.setTTTbnNgayLOG(cs.getTTTbnNgay());
                    response.setTtTbnNgayPercentLOG(cs.getTtCungKyNgayPercent());
                    break;
                default:
                    break;
            }
        }
        return response;
    }

    @Override
    public TienDoDoanhThuCNV1ResDto findDoanhThuLogistic(LocalDate toTime) {
        String nhom_doanhthu = "DT-LOGISTIC";
        TienDoDoanhThuCNV1ResDto doanhThuLogisticCN = chiSoKinhDoanhRepository.findDoanhThuLogistic(toTime, nhom_doanhthu);

        if (doanhThuLogisticCN != null) {
            doanhThuLogisticCN.setNhomDoanhThu(nhom_doanhthu);
        } else {
            doanhThuLogisticCN = new TienDoDoanhThuCNV1ResDto();
        }
        doanhThuLogisticCN.setMaChiNhanh("Total");

        return doanhThuLogisticCN;
    }

    @Override
    public FindTotalTienDoDoanhThuCPCNDto findDoanhThuChuyenPhat(LocalDate toTime) {
        String loaiDichVu = "DT-CP";
        FindTotalTienDoDoanhThuCPCNDto doanhThuLogisticCN = chiSoKinhDoanhRepository.findDoanhThuChuyenPhat(toTime, loaiDichVu);

        if (doanhThuLogisticCN != null) {
            doanhThuLogisticCN.setLoaiDichVu(loaiDichVu);
        } else {
            doanhThuLogisticCN = new FindTotalTienDoDoanhThuCPCNDto();
        }

        doanhThuLogisticCN.setMaChiNhanh("Total");

        return doanhThuLogisticCN;
    }

    @Override
    public CSKDTienDoDoanhThuV2Dto findTongDoanhThuCuaChiNhanh(LocalDate toTime) {
        /* Tính tổng của bảng chi nhánh
         * Tính tổng của bảng tiến độ doanh thu của tất cả các chi nhánh (Trong bảng chi nhánh) theo ngày:
         * select * from cskd_tk_td_thu_cn where ngay_baocao : =ngay_baocao
         */

        CSKDTienDoDoanhThuV2Dto tienDoDoanhThu = chiSoKinhDoanhRepository.findTongDoanhThuCuaChiNhanh(toTime);
        if (tienDoDoanhThu != null) {
            tienDoDoanhThu.setMaChiNhanh("Total");
        }

        return tienDoDoanhThu;
    }

    private List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThu(LocalDate toTime) {
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuModel = chiSoKinhDoanhRepository.findCSKDTienDoDoanhThu(toTime);
        if (tienDoDoanhThuModel.isEmpty()) {
            return null;
        }

        List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuModel);
        CSKDTienDoDoanhThuV2Dto tongDoanhThu = this.tinhTongDoanhThu(tienDoDoanhThuModel, "");

        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDoDoanhThuModel);
    }

    private List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThuTheoCN(String maChiNhanh, LocalDate toTime, String maBuuCuc) {
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNModel = new ArrayList<>();
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tienDoDoanhThuCNModel =
                        tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverViewV2(maChiNhanh, toTime, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tienDoDoanhThuCNModel =
                        tienDoDoanhThuBuuCucRepo.findTienDoDoanhThuBCOverViewV2(maChiNhanh, toTime, UserContext.getUserData().getListBuuCucVeriable(), maBuuCuc);
            }
        } else {
            tienDoDoanhThuCNModel =
                    tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverView(maChiNhanh, toTime);
        }

        if (tienDoDoanhThuCNModel.isEmpty() || tienDoDoanhThuCNModel == null) {
            return null;
        }

        List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuCNModel);

        CSKDTienDoDoanhThuV2Dto tongDoanhThu = this.tinhTongDoanhThu(tienDoDoanhThuCNModel, maChiNhanh);
        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDoDoanhThuCNModel);
    }

    //sonph13 lấy cskd chi nhánh cho gửi sms cskd
    private List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThuTheoCNSMS(Table<String, String, CSKDTienDoDoanhThuOverViewDto> table, String chiNhanh) {

        if (table.get(chiNhanh, "DT-CP") == null && table.get(chiNhanh, "DT-LOGISTICS") == null) {
            return new ArrayList<>();
        }
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNModel = new ArrayList<>();
        tienDoDoanhThuCNModel.add(table.get(chiNhanh, "DT-CP"));
        tienDoDoanhThuCNModel.add(table.get(chiNhanh, "DT-LOGISTIC"));

        List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuCNModel);

        CSKDTienDoDoanhThuV2Dto tongDoanhThu = this.tinhTongDoanhThu(tienDoDoanhThuCNModel, chiNhanh);
        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDoDoanhThuCNModel);
    }
    //sonph13 end

    private List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThuTheoCNDetail(String maChiNhanh, LocalDate toTime, String maBuuCuc) {
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNModel = new ArrayList<>();
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tienDoDoanhThuCNModel =
                        tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverViewV2(maChiNhanh, toTime, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tienDoDoanhThuBuuCucRepo.findTienDoDoanhThuBCOverViewV2(maChiNhanh, toTime, UserContext.getUserData().getListChiNhanhVeriable(), maBuuCuc);
            }
        } else {
            tienDoDoanhThuCNModel =
                    tienDoDoanhThuChiNhanhRepo.findTienDoDoanhThuChiNhanhOverView(maChiNhanh, toTime);
        }

        List<CSKDTienDoDoanhThuOverViewDto> tienDoDTChuyenPhatList = new ArrayList<>();
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDTLogisticList = new ArrayList<>();
        CSKDTienDoDoanhThuV2Dto tongTienDoDTChuyenPhat = new CSKDTienDoDoanhThuV2Dto();
        CSKDTienDoDoanhThuV2Dto tongTienDoDTLogistic = new CSKDTienDoDoanhThuV2Dto();

        for (CSKDTienDoDoanhThuOverViewDto tienDoDoanhThuCNModelDt : tienDoDoanhThuCNModel) {
            if (tienDoDoanhThuCNModelDt.getNhomDt().equals("DT-CP")) {
                tienDoDTChuyenPhatList.add(tienDoDoanhThuCNModelDt);
            }
            if (tienDoDoanhThuCNModelDt.getNhomDt().equals("DT-LOGISTIC")) {
                tienDoDTLogisticList.add(tienDoDoanhThuCNModelDt);
            }
        }
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuCNModelConvert = new ArrayList<>();
        tongTienDoDTChuyenPhat = this.tinhTongDoanhThu(tienDoDTChuyenPhatList, maChiNhanh);
        tongTienDoDTLogistic = this.tinhTongDoanhThu(tienDoDTLogisticList, maChiNhanh);
        tongTienDoDTChuyenPhat.setNhomDt("DT-CP");
        tongTienDoDTLogistic.setNhomDt("DT-LOGISTIC");

        tienDoDoanhThuCNModelConvert.add(this.convertTongToChiTietModel(tongTienDoDTChuyenPhat));
        tienDoDoanhThuCNModelConvert.add(this.convertTongToChiTietModel(tongTienDoDTLogistic));
        List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuCNModelConvert);

        CSKDTienDoDoanhThuV2Dto tongDoanhThu = this.tinhTongDoanhThu(tienDoDoanhThuCNModelConvert, maChiNhanh);
        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDoDoanhThuCNModel);
    }

    private List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThuTheoBC(String maChiNhanh, String maBuuCuc, LocalDate toTime) {
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuBCModel =
                tienDoDoanhThuBuuCucRepo.findTienDoDoanhThuBCOverView(maChiNhanh, maBuuCuc, toTime);
        if (tienDoDoanhThuBCModel.isEmpty() || tienDoDoanhThuBCModel == null) {
            return null;
        }

        List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuBCModel);
        CSKDTienDoDoanhThuV2Dto tongDoanhThu = this.tinhTongDoanhThu(tienDoDoanhThuBCModel, maChiNhanh);
        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDoDoanhThuBCModel);
    }

    private List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThuTheoBCDetail(String maChiNhanh, String maBuuCuc, LocalDate toTime) {
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuBCModel =
                tienDoDoanhThuBuuCucRepo.findTienDoDoanhThuBCOverView(maChiNhanh, maBuuCuc, toTime);
        List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuBCModel);
        CSKDTienDoDoanhThuV2Dto tongDoanhThu = this.tinhTongDoanhThu(tienDoDoanhThuBCModel, maChiNhanh);
        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDoDoanhThuBCModel);
    }

    private List<CSKDTienDoDoanhThuV2Dto> convertModelToDomain(List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuModel) {
        List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = new ArrayList<>();
        for (CSKDTienDoDoanhThuOverViewDto tienDoDoanhThuDt : tienDoDoanhThuModel) {
            tienDoDoanhThu.add(this.mapModelToDomainOverView(tienDoDoanhThuDt));
        }

        return tienDoDoanhThu;
    }

    private CSKDTienDoDoanhThuOverViewDto convertTongToChiTietModel(CSKDTienDoDoanhThuV2Dto tienDoDoanhThuModel) {
        CSKDTienDoDoanhThuOverViewDto tienDoDoanhThuOverView = new CSKDTienDoDoanhThuOverViewDto();
        tienDoDoanhThuOverView.setMaChiNhanh(tienDoDoanhThuModel.getMaChiNhanh());
        tienDoDoanhThuOverView.setNhomDt(tienDoDoanhThuModel.getNhomDt());
        tienDoDoanhThuOverView.setKeHoach(tienDoDoanhThuModel.getKeHoach());
        tienDoDoanhThuOverView.setThucHien(tienDoDoanhThuModel.getThucHien());
        tienDoDoanhThuOverView.setCungKyNgay(tienDoDoanhThuModel.getCungKyNgay());
        tienDoDoanhThuOverView.setCungKyThang(tienDoDoanhThuModel.getCungKyThang());
        tienDoDoanhThuOverView.setCungKyNam(tienDoDoanhThuModel.getCungKyNam());
        tienDoDoanhThuOverView.setThangTruoc(tienDoDoanhThuModel.getThangTruoc());
        tienDoDoanhThuOverView.setNamTruoc(tienDoDoanhThuModel.getNamTruoc());
        tienDoDoanhThuOverView.setTlHoanThanh(tienDoDoanhThuModel.getTlHoanThanh());
        tienDoDoanhThuOverView.setTlDoanhThu(tienDoDoanhThuModel.getTlDoanhThu());
        tienDoDoanhThuOverView.setTienDo(tienDoDoanhThuModel.getTienDo());
        tienDoDoanhThuOverView.setTtThang(tienDoDoanhThuModel.getTtThang());
        tienDoDoanhThuOverView.setTtTbnThang(tienDoDoanhThuModel.getTtTbnThang());
        tienDoDoanhThuOverView.setTtNam(tienDoDoanhThuModel.getTtNam());
        tienDoDoanhThuOverView.setTtTbnNam(tienDoDoanhThuModel.getTtTbnNam());

        return tienDoDoanhThuOverView;
    }

    public CSKDTienDoDoanhThuV2Dto tinhTongDoanhThu(List<CSKDTienDoDoanhThuOverViewDto> tienDoDoanhThuModel,
                                                    String maChiNhanh) {
        Float thucHien = Float.valueOf(0);
        Float keHoach = Float.valueOf(0);
        Float ngayCungKy = Float.valueOf(0);
        Float thucHienCungKyThangTruoc = Float.valueOf(0);
        Float thangCungKy = Float.valueOf(0);
        Float tongTHTBNCungKyThangTruoc = Float.valueOf(0);
        Float tongThucHienTBNThangNay = Float.valueOf(0);
        Float thucHienCungKyNamTruoc = Float.valueOf(0);
        Float soNgayCKNamTruoc = Float.valueOf(0);
        Float tongTHTBNCungKyNamTruoc = Float.valueOf(0);
        Float thucHienNgay = Float.valueOf(0);
        Float thucHienNgayMM1 = Float.valueOf(0);
        Float ngayQuyDoiThang = Float.valueOf(0);
        Float heSoNgay = Float.valueOf(0);
        Float tienDo = Float.valueOf(0);
        int flag = 0;
        String chiNhanh = "";
        HashMap<String, Float> keHoachTongDT = new HashMap<>();

        for (CSKDTienDoDoanhThuOverViewDto tienDoDoanhThuDt : tienDoDoanhThuModel) {
            chiNhanh = tienDoDoanhThuDt.getMaChiNhanh();
            if (tienDoDoanhThuDt.getKeHoach() == 0) {
                flag = 1;
            }
            if (tienDoDoanhThuDt.getThucHien() != null) {
                thucHien += tienDoDoanhThuDt.getThucHien();
            }
            if (tienDoDoanhThuDt.getKeHoach() != null) {
                keHoach += tienDoDoanhThuDt.getKeHoach();
            }
            if (tienDoDoanhThuDt.getThangTruoc() != null) {
                thucHienCungKyThangTruoc += tienDoDoanhThuDt.getThangTruoc();
            }
            if (tienDoDoanhThuDt.getNamTruoc() != null) {
                thucHienCungKyNamTruoc += tienDoDoanhThuDt.getNamTruoc();
            }
            ngayCungKy = tienDoDoanhThuDt.getCungKyNgay();
            thangCungKy = tienDoDoanhThuDt.getCungKyThang();
            soNgayCKNamTruoc = tienDoDoanhThuDt.getCungKyNam();
            if (!maChiNhanh.isEmpty() && tienDoDoanhThuDt.getKeHoach() != null) {
                keHoachTongDT.put(tienDoDoanhThuDt.getNhomDt(), tienDoDoanhThuDt.getKeHoach());
            }
            if (tienDoDoanhThuDt.getThucHienNgay() != null) {
                thucHienNgay += tienDoDoanhThuDt.getThucHienNgay();
            }
            if (tienDoDoanhThuDt.getThucHienNgayTruocDo() != null) {
                thucHienNgayMM1 += tienDoDoanhThuDt.getThucHienNgayTruocDo();
            }
            if (ngayQuyDoiThang == 0 && tienDoDoanhThuDt.getNgayQuyDoiThang() != 0) {
                ngayQuyDoiThang = tienDoDoanhThuDt.getNgayQuyDoiThang();
            }
            if (heSoNgay == 0 && tienDoDoanhThuDt.getHsNgay() != 0) {
                heSoNgay = tienDoDoanhThuDt.getHsNgay();
            }
            if (tienDoDoanhThuDt.getTienDo() != null) {
                tienDo += tienDoDoanhThuDt.getTienDo();
            }


        }
        CSKDTienDoDoanhThuV2Dto tongDoanhThu = new CSKDTienDoDoanhThuV2Dto();

        /* Tổng doanh thu ngày N */
        tongDoanhThu.setTongDTNgay(thucHienNgay);

        /* Khi search theo bưu cục thì ke hoach sẽ lấy bằng kế hoạch của chi nhánh */
        if (!maChiNhanh.isEmpty() && !keHoachTongDT.isEmpty()) {
            keHoach = keHoachTongDT.values().stream().reduce((float) 0, Float::sum);
        }

        if (thucHien == 0 && (keHoach == null || keHoach == 0)) {
            return tongDoanhThu;
        }

        /** Tính Tổng Kế Hoạch */
        tongDoanhThu.setKeHoach(keHoach);

        /** Tính Tổng Thực Hiện */
        tongDoanhThu.setThucHien(thucHien);

        //TODO check lại công thức tính
        /** Tính Tổng Tỉ Lệ Hoàn Thành và Tỉ lệ hoàn thành ngày */
        if (keHoach != 0) {
            tongDoanhThu.setTlHoanThanh(100 * (thucHien / keHoach));
            if (ngayQuyDoiThang != 0 && heSoNgay != 0) {
                Float keHoachNgayN = (keHoach / ngayQuyDoiThang) * heSoNgay;
                tongDoanhThu.setTiLeHoanThanhNgay(100 * tongDoanhThu.getTongDTNgay() / keHoachNgayN);
            }
        } else {
            tongDoanhThu.setTlHoanThanh(null);
        }

        /** Tăng trưởng cùng kỳ ngày tính theo hiệu số tăng trưởng */
        tongDoanhThu.setTtCungKyNgay(thucHienNgay - thucHienNgayMM1);

        /** Tăng trưởng cùng kỳ ngày tính theo % tăng trưởng */
        if (thucHienNgayMM1 != 0) {
            tongDoanhThu.setTtCungKyNgayPercent(100 * tongDoanhThu.getTtCungKyNgay() / thucHienNgayMM1);
        }

        /** Tính tăng trưởng Trung Bình Ngày*/
        if (ngayQuyDoiThang != 0) {
            //B1: Tính Doanh thu trung bình ngày N tháng X
            Float dtTBNgayNThangX = (thucHien / ngayQuyDoiThang) * heSoNgay;
            Float dtTBNgay = thucHienNgay - dtTBNgayNThangX;
            tongDoanhThu.setTTTbnNgay(dtTBNgay);
            if (dtTBNgay != 0) {
                tongDoanhThu.setTtTbnNgayPercent(100 * tongDoanhThu.getTTTbnNgay() / dtTBNgay);
            }
        }

        /** Tính tiến độ */
        if (flag == 0 && ngayCungKy != 0) {
//            tongDoanhThu.setTienDo(thucHien - (keHoach * ngayQuyDoiThang / ngayCungKy));
//            tongDoanhThu.setTienDo(thucHien - keHoach);
            tongDoanhThu.setTienDo(tienDo);
        }

        /** Tính tăng trưởng tháng */
        if (thucHienCungKyThangTruoc != 0) {
            tongDoanhThu.setTtThang(100 * (thucHien - thucHienCungKyThangTruoc) / thucHienCungKyThangTruoc);
        }

        /** Tính TTTBN tháng */
        /* Tính tổng thực hiện TBN tháng này */
        if (ngayCungKy != 0) {
            tongThucHienTBNThangNay = thucHien / ngayCungKy;
        }

        /* Tính tổng thực hiện cùng kỳ tháng trước */
        if (thangCungKy != 0) {
            tongTHTBNCungKyThangTruoc = thucHienCungKyThangTruoc / thangCungKy;
        }

        if (tongTHTBNCungKyThangTruoc != 0) {
            tongDoanhThu.setTtTbnThang(
                    100 * (tongThucHienTBNThangNay - tongTHTBNCungKyThangTruoc) / tongTHTBNCungKyThangTruoc);
        }

        /** Tính TT Năm */
        if (thucHienCungKyNamTruoc != 0) {
            tongDoanhThu.setTtNam(100 * (thucHien - thucHienCungKyNamTruoc) / thucHienCungKyNamTruoc);
        }

        /** Tính TT TBN Năm */
        if (soNgayCKNamTruoc != 0) {
            /* Tổng thực hiện TBN tháng {tongThucHienTBNThangNay} */
            /* Tổng thực hiện cùng kỳ năm trước {thucHienCungKyNamTruoc} */
            /* Số ngày cùng kỳ năm trước {soNgayCKNamTruoc} */
            /* Tính tổng thực hiện cùng kỳ năm trước */
            tongTHTBNCungKyNamTruoc = thucHienCungKyNamTruoc / soNgayCKNamTruoc;
            if (tongTHTBNCungKyNamTruoc != 0) {
                tongDoanhThu.setTtTbnNam(
                        100 * (tongThucHienTBNThangNay - tongTHTBNCungKyNamTruoc) / tongTHTBNCungKyNamTruoc);
            }
        }

        tongDoanhThu.setMaChiNhanh(chiNhanh);
        tongDoanhThu.setThangTruoc(thucHienCungKyThangTruoc);
        tongDoanhThu.setNamTruoc(thucHienCungKyNamTruoc);
        tongDoanhThu.setCungKyNgay(ngayCungKy);
        tongDoanhThu.setCungKyThang(thangCungKy);
        tongDoanhThu.setCungKyNam(soNgayCKNamTruoc);

        return tongDoanhThu;
    }

    private CSKDTienDoDoanhThuV2Dto mapModelToDomainOverView(CSKDTienDoDoanhThuOverViewDto tienDoDoanhThuModel) {
        CSKDTienDoDoanhThuV2Dto tienDoDoanhThu = new CSKDTienDoDoanhThuV2Dto();

        tienDoDoanhThu.setMaChiNhanh(tienDoDoanhThuModel.getMaChiNhanh());
        tienDoDoanhThu.setNhomDt(tienDoDoanhThuModel.getNhomDt());
        tienDoDoanhThu.setKeHoach(tienDoDoanhThuModel.getKeHoach());
        tienDoDoanhThu.setThucHien(tienDoDoanhThuModel.getThucHien());
        tienDoDoanhThu.setTlHoanThanh(tienDoDoanhThuModel.getTlHoanThanh());
        tienDoDoanhThu.setTlDoanhThu(tienDoDoanhThuModel.getTlDoanhThu());
        tienDoDoanhThu.setTienDo(tienDoDoanhThuModel.getTienDo());
        tienDoDoanhThu.setTtThang(tienDoDoanhThuModel.getTtThang());
        tienDoDoanhThu.setTtTbnThang(tienDoDoanhThuModel.getTtTbnThang());
        tienDoDoanhThu.setTtNam(tienDoDoanhThuModel.getTtNam());
        tienDoDoanhThu.setTtTbnNam(tienDoDoanhThuModel.getTtTbnNam());
        tienDoDoanhThu.setNamTruoc(tienDoDoanhThuModel.getNamTruoc());
        tienDoDoanhThu.setThangTruoc(tienDoDoanhThuModel.getThangTruoc());
        tienDoDoanhThu.setCungKyNgay(tienDoDoanhThuModel.getCungKyNgay());
        tienDoDoanhThu.setCungKyThang(tienDoDoanhThuModel.getCungKyThang());
        tienDoDoanhThu.setCungKyNam(tienDoDoanhThuModel.getCungKyNam());

        return tienDoDoanhThu;
    }

    private List<CSKDTienDoDoanhThuV2Dto> processResponseTienDoDoanhThu(
            List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu,
            CSKDTienDoDoanhThuV2Dto tongDoanhThu,
            List<CSKDTienDoDoanhThuOverViewDto> listTienDoDoanhThuModel
    ) {
        CSKDTienDoDoanhThuV2Dto tienDoDoanhThuCP = new CSKDTienDoDoanhThuV2Dto();
        CSKDTienDoDoanhThuV2Dto tienDoDoanhThuLOGIS = new CSKDTienDoDoanhThuV2Dto();
        tienDoDoanhThuCP.setNhomDt("DT-CP");
        tienDoDoanhThuLOGIS.setNhomDt("DT-LOGISTICS");

        List<CSKDTienDoDoanhThuV2Dto> result = new ArrayList<>();
        List<CSKDTienDoDoanhThuOverViewDto> listCP = new ArrayList<>();
        List<CSKDTienDoDoanhThuOverViewDto> listLOGISTIC = new ArrayList<>();
        if (tongDoanhThu != null && (tienDoDoanhThu != null && !tienDoDoanhThu.isEmpty())) {
            for (CSKDTienDoDoanhThuOverViewDto doanhThuDt : listTienDoDoanhThuModel) {
                if (doanhThuDt.getNhomDt().equals("DT-CP")) {
                    listCP.add(doanhThuDt);
                }
                if (doanhThuDt.getNhomDt().equals("DT-LOGISTIC")) {
                    listLOGISTIC.add(doanhThuDt);
                }
            }
        } else {
            tongDoanhThu = new CSKDTienDoDoanhThuV2Dto();
        }

        CSKDTienDoDoanhThuV2Dto doanhThuCP = tinhTongDoanhThu(listCP, "");
        CSKDTienDoDoanhThuV2Dto doanhThuLogistic = tinhTongDoanhThu(listLOGISTIC, "");
        doanhThuCP.setNhomDt("DT-CP");
        doanhThuLogistic.setNhomDt("DT-LOGISTICS");

        tongDoanhThu.setNhomDt("Tổng");
        result.add(doanhThuCP);
        result.add(doanhThuLogistic);
        result.add(tongDoanhThu);
        return result;
    }

    private CSKDTongDoanhThuOriginDto convertCSKDTongDoanhThuOverViewtoOrigin(
            CSKDTongDoanhThuOverViewDto cskdTongDoanhThuOverView) {
        CSKDTongDoanhThuOriginDto cskdTongDoanhThu = new CSKDTongDoanhThuOriginDto();

        if (cskdTongDoanhThuOverView != null) {
            cskdTongDoanhThu.setTlDoanhThu(cskdTongDoanhThuOverView.getTongDoanhThu());
            cskdTongDoanhThu.setMaBuuCuc(cskdTongDoanhThuOverView.getMaBuuCuc());
            cskdTongDoanhThu.setMaChiNhanh(cskdTongDoanhThuOverView.getMaChiNhanh());
            cskdTongDoanhThu.setTtNam(cskdTongDoanhThuOverView.getTtNam());
            cskdTongDoanhThu.setTtThang(cskdTongDoanhThuOverView.getTtThang());
            cskdTongDoanhThu.setTlHoanThanh(cskdTongDoanhThuOverView.getTlHoanThanh());
            cskdTongDoanhThu.setTtTbnThang(cskdTongDoanhThuOverView.getTtTbnThang());
        }

        return cskdTongDoanhThu;
    }

    @Override
    public CSKDTienDoDoanhThuV2Dto findTongTienDoDTCN(String maChiNhanh, String loaiDichVu, String maBuuCuc, LocalDate to_time) {
        if (!maBuuCuc.isEmpty() || maBuuCuc != null) {

        }

        return null;
    }


    private CSKDTongDoanhThuOriginDto convertFromDetailToTongDoanhThu(CSKDTienDoDoanhThuV2Dto originTongDoanhThu) {
        CSKDTongDoanhThuOriginDto tongDoanhThu = new CSKDTongDoanhThuOriginDto();

        if (originTongDoanhThu != null) {
            tongDoanhThu.setMaBuuCuc(originTongDoanhThu.getMaBuuCuc());
            tongDoanhThu.setMaChiNhanh(originTongDoanhThu.getMaChiNhanh());
            tongDoanhThu.setTtNam(originTongDoanhThu.getTtNam());
            tongDoanhThu.setTtThang(originTongDoanhThu.getTtThang());
            tongDoanhThu.setTtTbnThang(originTongDoanhThu.getTtTbnThang());
            tongDoanhThu.setTtTbnNam(originTongDoanhThu.getTtTbnNam());
            tongDoanhThu.setTlHoanThanh(originTongDoanhThu.getTlHoanThanh());
            tongDoanhThu.setTlDoanhThu(originTongDoanhThu.getThucHien());
            tongDoanhThu.setTongDTNgay(originTongDoanhThu.getTongDTNgay());
            tongDoanhThu.setTiLeHoanThanhNgay(originTongDoanhThu.getTiLeHoanThanhNgay());
            tongDoanhThu.setTtCungKyNgay(originTongDoanhThu.getTtCungKyNgay());
            tongDoanhThu.setTtCungKyNgayPercent(originTongDoanhThu.getTtCungKyNgayPercent());
            tongDoanhThu.setTTTbnNgay(originTongDoanhThu.getTTTbnNgay());
            tongDoanhThu.setTtTbnNgayPercent(originTongDoanhThu.getTtTbnNgayPercent());
        } else {
            return null;
        }

        return tongDoanhThu;
    }
}
