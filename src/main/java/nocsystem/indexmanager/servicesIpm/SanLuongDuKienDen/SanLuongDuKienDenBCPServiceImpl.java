package nocsystem.indexmanager.servicesIpm.SanLuongDuKienDen;

import nocsystem.indexmanager.dao.SanLuongDuKienDen.SanLuongDuKienDenBCPDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.SanLuongDuKienDen.SanLuongDuKienDenBCPResponse;
import nocsystem.indexmanager.services.SanLuongDuKienDen.SanLuongDuKienDenBCPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class SanLuongDuKienDenBCPServiceImpl implements SanLuongDuKienDenBCPService {
    @Autowired
    SanLuongDuKienDenBCPDAO sanLuongDuKienDenBCPDAO;
    @Override
    public List<SanLuongDuKienDenBCPResponse> getBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanh, List<String> buuCuc, String orderBy, String sort, Integer pageNumber, Integer pageSize, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException {
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleLanhDaoCtyLog().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true")) {
            List<String> chiNhanhSso = new ArrayList<>();
            List<String> buuCucSso = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSso = UserContext.getUserData().getListChiNhanhVeriable();
            }

            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                chiNhanhSso = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSso = UserContext.getUserData().getListBuuCucVeriable();
            }
            return sanLuongDuKienDenBCPDAO.getBaoCaoSLDuKienBCP(ngayBaoCao, chiNhanhSso, buuCucSso, chiNhanh, buuCuc ,orderBy, sort, pageNumber, pageSize, hangDacThu, khDacThuGui, khDacThuNhan);
        }
        return new ArrayList<>();
    }

    @Override
    public Long getTotalRecordBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException {
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleLanhDaoCtyLog().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true")) {
            List<String> chiNhanhSso = new ArrayList<>();
            List<String> buuCucSso = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSso = UserContext.getUserData().getListChiNhanhVeriable();
            }

            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                chiNhanhSso = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSso = UserContext.getUserData().getListBuuCucVeriable();
            }
            return sanLuongDuKienDenBCPDAO.getTotalRecordBaoCaoSLDuKienBCP(ngayBaoCao, chiNhanhSso, buuCucSso, chiNhanh, buuCuc, hangDacThu, khDacThuGui, khDacThuNhan);
        }
        return 0L;
    }

    @Override
    public SanLuongDuKienDenBCPResponse getTotalBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException {
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleLanhDaoCtyLog().equalsIgnoreCase("true")
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true")) {
            List<String> chiNhanhSso = new ArrayList<>();
            List<String> buuCucSso = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSso = UserContext.getUserData().getListChiNhanhVeriable();
            }

            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                chiNhanhSso = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSso = UserContext.getUserData().getListBuuCucVeriable();
            }

            List<SanLuongDuKienDenBCPResponse> listData = sanLuongDuKienDenBCPDAO.getTotalBaoCaoSLDuKienBCP(ngayBaoCao, chiNhanhSso, buuCucSso, chiNhanh, buuCuc, hangDacThu, khDacThuGui, khDacThuNhan);
            Long tongTrongLuong = 0L;
            Long tongCaSangNgayN = 0L;
            Long tongCaChieuNgayN = 0L;
            Long tongCaToiNgayN = 0L;
            Long tongSLNgayN = 0L;
            Long tongCaSangNgayN1 = 0L;
            Long tongCaChieuNgayN1 = 0L;
            Long tongCaToiNgayN1 = 0L;
            Long tongSLNgayN1 = 0L;
            Long tongCaSangNgayN2 = 0L;
            Long tongCaChieuNgayN2 = 0L;
            Long tongCaToiNgayN2 = 0L;
            Long tongSLNgayN2 = 0L;
            Long tongSLDuKien = 0L;
            for (SanLuongDuKienDenBCPResponse data: listData) {
                tongTrongLuong += data.getTongTrongLuong();
                tongCaSangNgayN += data.getCaSangNgayN();
                tongCaChieuNgayN += data.getCaChieuNgayN();
                tongCaToiNgayN += data.getCaToiNgayN();
                tongSLNgayN += data.getTongSLNgayN();
                tongCaSangNgayN1 += data.getCaSangNgayN1();
                tongCaChieuNgayN1 += data.getCaChieuNgayN1();
                tongCaToiNgayN1 += data.getCaToiNgayN1();
                tongSLNgayN1 += data.getTongSLNgayN1();
                tongCaSangNgayN2 += data.getCaSangNgayN2();
                tongCaChieuNgayN2 += data.getCaChieuNgayN2();
                tongCaToiNgayN2 += data.getCaToiNgayN2();
                tongSLNgayN2 += data.getTongSLNgayN2();
                tongSLDuKien += data.getTongSLDuKien();
            }
            SanLuongDuKienDenBCPResponse result = new SanLuongDuKienDenBCPResponse();
            result.setDonVi("Tổng");
            result.setTongTrongLuong(tongTrongLuong);
            result.setCaSangNgayN(tongCaSangNgayN);
            result.setCaChieuNgayN(tongCaChieuNgayN);
            result.setCaToiNgayN(tongCaToiNgayN);
            result.setTongSLNgayN(tongSLNgayN);
            result.setCaSangNgayN1(tongCaSangNgayN1);
            result.setCaChieuNgayN1(tongCaChieuNgayN1);
            result.setCaToiNgayN1(tongCaToiNgayN1);
            result.setTongSLNgayN1(tongSLNgayN1);
            result.setCaSangNgayN2(tongCaSangNgayN2);
            result.setCaChieuNgayN2(tongCaChieuNgayN2);
            result.setCaToiNgayN2(tongCaToiNgayN2);
            result.setTongSLNgayN2(tongSLNgayN2);
            result.setTongSLDuKien(tongSLDuKien);

            return result;
        }
        return null;
    }
}
