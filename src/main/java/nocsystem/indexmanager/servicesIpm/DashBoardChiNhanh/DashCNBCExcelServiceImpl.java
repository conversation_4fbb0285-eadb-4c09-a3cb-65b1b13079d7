package nocsystem.indexmanager.servicesIpm.DashBoardChiNhanh;

import io.swagger.models.auth.In;
import nocsystem.indexmanager.dao.TonKhac.TonKhacDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DashTonHanhTrinhDto;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TonDvGiaTangExcelDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonHanhTrinhRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatChuaPhanCongPhatRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonChuaNhapMayRepository2;
import nocsystem.indexmanager.services.DashBoardChiNhanh.DashCNBCExcelService;
import nocsystem.indexmanager.services.DashBoardChiNhanh.ExportExcelTemplateCNBC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;

@Service
public class DashCNBCExcelServiceImpl<T> implements DashCNBCExcelService {

    @Autowired
    TonHanhTrinhRepository dashTonHanhTrinhExcelRepo;

    @Autowired
    TonChuaNhapMayRepository2 dashTonNhapMayExcelRepo;

    @Autowired
    TonPhatChuaPhanCongPhatRepository dashTonChuaPCPExcelRepo;

    @Autowired
    TonKhacDAO tonKhacDAO;

    @Override
    public List<Map<String, Object>> findAllRecordsHanhTrinh(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String loaiBaoCao, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao, List<Integer> trangThaiList, String loaiDon, String khDacThuGui, String khDacThuNhan) throws IllegalAccessException, SQLException {
        List<DashTonHanhTrinhDto> dashTonExcelDtos = new ArrayList<>();
        List<String> listBuuCuc = new ArrayList<>();
        List<String> listChiNhanh = new ArrayList<>();

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase("TRUE")
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("TRUE")) {
            if (!buuCuc.equals(""))
                listBuuCuc = List.of(buuCuc);
            if (!chiNhanh.equals(""))
                listChiNhanh = List.of(chiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("TRUE")) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listChiNhanh = mapCNBC.get("cn");
            listBuuCuc = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBuuCuc.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && buuCuc.equals("")) {
                listBuuCuc.add("CHUA_XAC_DINH");
            }
        }


        switch (loaiBaoCao) {
            case "TonHanhTrinh":
                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_TRANG_THAI_CHI_TIET", loaiHang, loaiCanhBao, 0, trangThaiList, loaiDon, khDacThuGui, khDacThuNhan);
                break;
            case "TonKienCham":
                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAP_MAY_CHUA_KET_NOI", loaiHang, "", 1);
                break;
            case "TonChuaPCP":
                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_CHUA_PCP_CHI_TIET", loaiHang, "", 0);
                break;
            case "TonNhapMay":
                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAP_MAY_CHUA_KET_NOI", loaiHang, "", 0);
                break;
            case "TonChuaCoHanhTrinh":
                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_CHUA_CO_HANH_TRINH_CHI_TIET", loaiHang, loaiCanhBao, 0);
                break;
            case "TonNhanBanGiao":
//                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAN_BAN_GIAO", loaiHang, "", 0);
                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAN_BAN_GIAO", loaiHang, loaiCanhBao, 0);
                break;
            default:
                break;
        }
        return tryTest((List<T>) dashTonExcelDtos);
    }

    @Override
    public List<Map<String, Object>> findAllRecordsHanhTrinh(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String loaiBaoCao, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao) throws IllegalAccessException, SQLException {
        return findAllRecordsHanhTrinh(ngayBaoCao, chiNhanh, buuCuc, loaiBaoCao, trangThai, vungCon, maDoiTac, loaiHang, loaiCanhBao, null, "all", "0", "0");
    }

    @Override
    public List<Map<String, Object>> excelDvGiaTang(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String loaiBaoCao, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao) throws IllegalAccessException, SQLException {
        List<TonDvGiaTangExcelDto> dashTonExcelDtos = new ArrayList<>();
        List<String> listBuuCuc = new ArrayList<>();
        List<String> listChiNhanh = new ArrayList<>();

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase("TRUE")
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("TRUE")) {
            if (!buuCuc.equals(""))
                listBuuCuc = List.of(buuCuc);
            if (!chiNhanh.equals(""))
                listChiNhanh = List.of(chiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("TRUE")) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listChiNhanh = mapCNBC.get("cn");
            listBuuCuc = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBuuCuc.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && buuCuc.equals("")) {
                listBuuCuc.add("CHUA_XAC_DINH");
            }
        }

        dashTonExcelDtos = tonKhacDAO.getDetailExcelTonDvGiaTang(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, loaiHang);

        return tryTestDvGiaTang((List<T>) dashTonExcelDtos);
    }

    public List<Map<String, Object>> tryTest(List<T> canIOTList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!canIOTList.isEmpty()) {
            int i = 0;
            for (T x : canIOTList) {
                DashTonHanhTrinhDto x1 = (DashTonHanhTrinhDto) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public List<Map<String, Object>> tryTestDvGiaTang(List<T> canIOTList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!canIOTList.isEmpty()) {
            int i = 0;
            for (T x : canIOTList) {
                TonDvGiaTangExcelDto x1 = (TonDvGiaTangExcelDto) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public void findAllRecordsHanhTrinhExcel(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String loaiBaoCao, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao, List<Integer> trangThaiList, String loaiDon, String khDacThuGui, String khDacThuNhan) throws IllegalAccessException, SQLException, IOException {
        List<String> listBuuCuc = new ArrayList<>();
        List<String> listChiNhanh = new ArrayList<>();

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase("TRUE")
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("TRUE")) {
            if (!buuCuc.equals(""))
                listBuuCuc = List.of(buuCuc);
            if (!chiNhanh.equals(""))
                listChiNhanh = List.of(chiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("TRUE")) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listChiNhanh = mapCNBC.get("cn");
            listBuuCuc = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBuuCuc.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && buuCuc.equals("")) {
                listBuuCuc.add("CHUA_XAC_DINH");
            }
        }

        switch (loaiBaoCao) {
            case "TonHanhTrinh":
                tonKhacDAO.exportExcelTonHanhTrinh(response, ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_TRANG_THAI_CHI_TIET", loaiHang, loaiCanhBao, 0, trangThaiList, loaiDon, khDacThuGui, khDacThuNhan, loaiBaoCao);
                break;
            case "TonKienCham":
                tonKhacDAO.exportExcelTonHanhTrinh(response, ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAP_MAY_CHUA_KET_NOI", loaiHang, "", 1, null, "all", "0", "0", loaiBaoCao);
                break;
            case "TonChuaPCP":
                tonKhacDAO.exportExcelTonHanhTrinh(response, ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_CHUA_PCP_CHI_TIET", loaiHang, "", 0, null, "all", "0", "0", loaiBaoCao);
                break;
            case "TonNhapMay":
                tonKhacDAO.exportExcelTonHanhTrinh(response, ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAP_MAY_CHUA_KET_NOI", loaiHang, "", 0, null, "all", "0", "0", loaiBaoCao);
                break;
            case "TonChuaCoHanhTrinh":
                tonKhacDAO.exportExcelTonHanhTrinh(response, ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_CHUA_CO_HANH_TRINH_CHI_TIET", loaiHang, loaiCanhBao, 0, null, "all", "0", "0", loaiBaoCao);
                break;
            case "TonNhanBanGiao":
//                dashTonExcelDtos = tonKhacDAO.getDetailExcel(ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAN_BAN_GIAO", loaiHang, "", 0);
                tonKhacDAO.exportExcelTonHanhTrinh(response, ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, "TON_NHAN_BAN_GIAO", loaiHang, loaiCanhBao, 0, null, "all", "0", "0", loaiBaoCao);
                break;
            default:
                break;
        }
    }


    @Override
    public void exportExcelTonKhacToiUu(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang, List<Integer> trangThaiList, String loaiDon, String khDacThuGui, String khDacThuNhan, String loaiCanhBao)
            throws IllegalAccessException, IOException, SQLException {
        findAllRecordsHanhTrinhExcel(response, ngayBaoCao, chiNhanh, buuCuc, fileName, trangThai, vungCon, maDoiTac, loaiHang, loaiCanhBao, trangThaiList, loaiDon, khDacThuGui, khDacThuNhan);

    }

    @Override
    public void exportExcelHanhTrinh(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang, List<Integer> trangThaiList, String loaiDon, String khDacThuGui, String khDacThuNhan, String loaiCanhBao)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã DV Viettel");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("maBuuCucHt", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHt", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKhGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayGuiBP", "Ngày Gửi BP");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");
        mapHeader.put("khDacThuGui", "KH đặc thù gửi");
        mapHeader.put("khDacThuNhan", "KH đặc thù nhận");
        mapHeader.put("loaiCanhBao", "Loại cảnh báo");
        mapHeader.put("maDvCongThem", "Mã dịch vụ cộng thêm");

        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHt",
                "chiNhanhHt",
                "maDoiTac",
                "maKhGui",
                "maBuuCucPhat",
                "ngayGuiBP",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "loaiHH",
                "tienCuoc",
                "khDacThuGui",
                "khDacThuNhan",
                "loaiCanhBao",
                "maDvCongThem"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                findAllRecordsHanhTrinh(ngayBaoCao, chiNhanh, buuCuc, fileName, trangThai, vungCon, maDoiTac, loaiHang, loaiCanhBao, trangThaiList, loaiDon, khDacThuGui, khDacThuNhan),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    @Override
    public void exportExcelHanhTrinh(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang)
            throws IllegalAccessException, IOException, SQLException {
        exportExcelHanhTrinh(response, ngayBaoCao, chiNhanh, buuCuc, trangThai, vungCon, maDoiTac, fileName, loaiHang, null, "all", "0", "0", null);
    }

    @Override
    public void exportExcelKienCham(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã DV Viettel");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("maBuuCucHt", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHt", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKhGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayGuiBP", "Ngày Gửi BP");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");

        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHt",
                "chiNhanhHt",
                "maDoiTac",
                "maKhGui",
                "maBuuCucPhat",
                "ngayGuiBP",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "loaiHH",
                "tienCuoc"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                findAllRecordsHanhTrinh(ngayBaoCao, chiNhanh, buuCuc, fileName, trangThai, vungCon, maDoiTac, loaiHang, ""),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    @Override
    public void exportExcelChuaPCP(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String loaiHang)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=TonChuaPCP.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã DV Viettel");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("maBuuCucHt", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHt", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKhGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayGuiBP", "Ngày Gửi BP");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("trongLuong", "Trọng Lượng");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");
        mapHeader.put("maDvCongThem", "Mã dịch vụ cộng thêm");
        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHt",
                "chiNhanhHt",
                "maDoiTac",
                "maKhGui",
                "maBuuCucPhat",
                "ngayGuiBP",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "trongLuong",
                "loaiHH",
                "tienCuoc",
                "maDvCongThem"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                findAllRecordsHanhTrinh(ngayBaoCao, chiNhanh, buuCuc, "TonChuaPCP", trangThai, vungCon, maDoiTac, loaiHang, ""),
                mapHeader,
                header,
                "TonChuaPCP"
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    @Override
    public void exportExcelChuaCoHanhTrinh(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=TonChuaCoHanhTrinh.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã DV Viettel");
        mapHeader.put("maDvCongThem", "Mã DV Cộng Thêm");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("maBuuCucHt", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHt", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKhGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayGuiBP", "Ngày Gửi BP");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("trongLuong", "Trọng Lượng");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");
        mapHeader.put("loaiCanhBao", "Cấp độ cảnh báo");
        mapHeader.put("nguoiTacDong", "Người tác động");
        mapHeader.put("buuTaPhat", "Bưu tá phát");
        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maDvCongThem",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHt",
                "chiNhanhHt",
                "maDoiTac",
                "maKhGui",
                "maBuuCucPhat",
                "ngayGuiBP",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "trongLuong",
                "loaiHH",
                "tienCuoc",
                "loaiCanhBao",
                "nguoiTacDong",
                "buuTaPhat"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                findAllRecordsHanhTrinh(ngayBaoCao, chiNhanh, buuCuc, "TonChuaCoHanhTrinh", trangThai, vungCon, maDoiTac, loaiHang, loaiCanhBao),
                mapHeader,
                header,
                "TonChuaCoHanhTrinh"
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    @Override
    public void exportExcelNhanBanGiao(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã DV Viettel");
        mapHeader.put("maDvCongThem", "Mã DV Cộng Thêm");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("maBuuCucHt", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHt", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKhGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayGuiBP", "Ngày Gửi BP");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");

        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maDvCongThem",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHt",
                "chiNhanhHt",
                "maDoiTac",
                "maKhGui",
                "maBuuCucPhat",
                "ngayGuiBP",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "loaiHH",
                "tienCuoc"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                findAllRecordsHanhTrinh(ngayBaoCao, chiNhanh, buuCuc, fileName, trangThai, vungCon, maDoiTac, loaiHang, ""),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }


    @Override
    public void exportExcelNhanBanGiao(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang, String loaiCanhBao)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã DV Viettel");
        mapHeader.put("maDvCongThem", "Mã DV Cộng Thêm");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("maBuuCucHt", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHt", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKhGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayGuiBP", "Ngày Gửi BP");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");
        mapHeader.put("loaiCanhBao", "Loại cảnh báo");

        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maDvCongThem",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHt",
                "chiNhanhHt",
                "maDoiTac",
                "maKhGui",
                "maBuuCucPhat",
                "ngayGuiBP",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "loaiHH",
                "tienCuoc",
                "loaiCanhBao"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                findAllRecordsHanhTrinh(ngayBaoCao, chiNhanh, buuCuc, fileName, trangThai, vungCon, maDoiTac, loaiHang, loaiCanhBao),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }


    @Override
    public void exportExcelDvGiaTangToiUu(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang)
            throws IllegalAccessException, IOException, SQLException {

        List<TonDvGiaTangExcelDto> dashTonExcelDtos = new ArrayList<>();
        List<String> listBuuCuc = new ArrayList<>();
        List<String> listChiNhanh = new ArrayList<>();

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase("TRUE")
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("TRUE")) {
            if (!buuCuc.equals(""))
                listBuuCuc = List.of(buuCuc);
            if (!chiNhanh.equals(""))
                listChiNhanh = List.of(chiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("TRUE") || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("TRUE")) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listChiNhanh = mapCNBC.get("cn");
            listBuuCuc = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBuuCuc.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && buuCuc.equals("")) {
                listBuuCuc.add("CHUA_XAC_DINH");
            }
        }
        tonKhacDAO.exportExcelTonDvGiaTangToiUu(response, ngayBaoCao, vungCon, listChiNhanh, listBuuCuc, maDoiTac, trangThai, loaiHang);

    }

    @Override
    public void exportExcelDvGiaTang(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename= ton_dv_gia_tang.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã DV Viettel");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("buuCucHT", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHT", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDvGiaTang", "Dịch vụ gia tăng");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKHGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayGuiBP", "Ngày Gửi BP");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("trongLuong", "Trọng lượng");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");

        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "buuCucHT",
                "chiNhanhHT",
                "maDvGiaTang",
                "maDoiTac",
                "maKHGui",
                "maBuuCucPhat",
                "ngayGuiBP",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "trongLuong",
                "loaiHH",
                "tienCuoc"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                excelDvGiaTang(ngayBaoCao, chiNhanh, buuCuc, fileName, trangThai, vungCon, maDoiTac, loaiHang, ""),
                mapHeader,
                header,
                "DvGiaTang"
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    //lấy list chi nhánh, bưu cục cần để query theo filter + SSO
    public Map<String, List<String>> checkListCNBC(String chiNhanh, String buuCuc) {
        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (chiNhanh.equals("")) {
            listCN = UserContext.getUserData().getListChiNhanhVeriable();
        } else {
            listCN.add(chiNhanh);
        }
        if (buuCuc.equals("")) {
            listBC = UserContext.getUserData().getListBuuCucVeriable();
        } else {
            listBC.add(buuCuc);
        }
        Map<String, List<String>> mapCNBC = new HashMap<>();
        mapCNBC.put("cn", listCN);
        mapCNBC.put("bc", listBC);
        return mapCNBC;
    }
}
