package nocsystem.indexmanager.servicesIpm.DashBoardChiNhanh;

import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.constants.DashChiNhanhMobileConstants;
import nocsystem.indexmanager.dao.TonKhac.TonKhacDAO;
import nocsystem.indexmanager.dao.TonNhapMay.TonNhapMayDAO;
import nocsystem.indexmanager.entity.ChiSoVersion;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTon;
import nocsystem.indexmanager.models.BieuDoXuTheTon.BieuDoXuTheTonDTO;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.*;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.BieuDoXuTheTonRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.*;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.*;
import nocsystem.indexmanager.services.ChiSoVersion.ChiSoVersionPostgresRepo;
import nocsystem.indexmanager.services.DashBoardChiNhanh.DBChiNhanhBuuCucService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Comparator.comparing;

@Slf4j
@Service
public class DBChiNhanhBuuCucServiceImp implements DBChiNhanhBuuCucService {
    private static final Logger logger = LoggerFactory.getLogger(DBChiNhanhBuuCucServiceImp.class);
    private final String THOI_GIAN_TON_THU_CA1 = "CA1";
    private final String THOI_GIAN_TON_THU_CA2 = "CA2";
    private String ca;
    private final TonPhatNguongNewRepository tonPhatNguongNewRepository;
    private final TonThuTheoKHRepository tonThuTheoKHRepository;
    private final TonPhatChuaPhanCongPhatRepository tonPhatChuaPhanCongPhatRepository;
    private final TonHanhTrinhRepository tonHanhTrinhRepository;

    private final TonChuaNhapMayRepository2 tonChuaNhapMayRepository2;
    private final TonChuaNhapMayRepository tonChuaNhapMayRepository;


    private final AllTonChuaNhapMayRepository allTonChuaNhapMayRepository;
    private final AllTonChuaPCPRepository allTonPhatChuaPhanCongPhatRepository;
    private final AllTonHanhTrinhRepository allTonHanhTrinhRepository;
    private final ChiSoVersionPostgresRepo chiSoVersionRepository;
    private final TonNhapMayDAO tonNhapMayDAO;
    private final TonPhatNguongBillRepository tonPhatNguongBillRepository;
    private final TonThuBillRepository tonThuBillRepository;
    private final BieuDoXuTheTonRepository bieuDoXuTheTonRepository;

    private static final String FALSE = "false";
    private static final String TRUE = "true";
    private final String TON_THU = "TON_THU";
    private final String TON_PHAT = "TON_PHAT";
    private final String TON_NHAN_BG = "TON_NHAN_BG";

    @Autowired
    TonKhacDAO tonKhacDAO;


    public DBChiNhanhBuuCucServiceImp(TonPhatNguongNewRepository tonPhatNguongNewRepository, TonThuTheoKHRepository tonThuTheoKHRepository, TonPhatChuaPhanCongPhatRepository tonPhatChuaPhanCongPhatRepository, TonHanhTrinhRepository tonHanhTrinhRepository, TonChuaNhapMayRepository2 tonChuaNhapMayRepository2, TonChuaNhapMayRepository tonChuaNhapMayRepository, AllTonChuaNhapMayRepository allTonChuaNhapMayRepository, AllTonChuaPCPRepository allTonPhatChuaPhanCongPhatRepository, AllTonHanhTrinhRepository allTonHanhTrinhRepository, TongHopTonRepository tongHopTonRepository, TonNhapMayDAO tonNhapMayDAO, TonPhatNguongBillRepository tonPhatNguongBillRepository, TonThuBillRepository tonThuBillRepository, BieuDoXuTheTonRepository bieuDoXuTheTonRepository,ChiSoVersionPostgresRepo chiSoVersionRepository) {
        this.tonPhatNguongNewRepository = tonPhatNguongNewRepository;
        this.tonThuTheoKHRepository = tonThuTheoKHRepository;
        this.tonPhatChuaPhanCongPhatRepository = tonPhatChuaPhanCongPhatRepository;
        this.tonHanhTrinhRepository = tonHanhTrinhRepository;
        this.tonChuaNhapMayRepository2 = tonChuaNhapMayRepository2;
        this.tonChuaNhapMayRepository = tonChuaNhapMayRepository;
        this.allTonChuaNhapMayRepository = allTonChuaNhapMayRepository;
        this.allTonPhatChuaPhanCongPhatRepository = allTonPhatChuaPhanCongPhatRepository;
        this.allTonHanhTrinhRepository = allTonHanhTrinhRepository;
        this.tonNhapMayDAO = tonNhapMayDAO;
        this.tonPhatNguongBillRepository = tonPhatNguongBillRepository;
        this.tonThuBillRepository = tonThuBillRepository;
        this.bieuDoXuTheTonRepository = bieuDoXuTheTonRepository;
        this.chiSoVersionRepository = chiSoVersionRepository;
        thoiGianCa();
    }

    public void thoiGianCa() {
        Calendar currentTime = Calendar.getInstance();
        int hour = currentTime.get(Calendar.HOUR_OF_DAY);
        // CA2 thời gian từ 12pm-18pm ngày N,ngoài thời gian trên là CA1
        if (hour > 12 && hour < 18) {
            this.ca = THOI_GIAN_TON_THU_CA2;
        } else {
            this.ca = THOI_GIAN_TON_THU_CA1;
        }
    }

    private String getCa() {
        String ca;
        Calendar rightNow = Calendar.getInstance();
        int hour = rightNow.get(Calendar.HOUR_OF_DAY);

        if (hour < 18 && hour > 12) {
            ca = "CA2";
        } else ca = "CA1";
        return ca;
    }

    DieuHanhTonTongResponse chiSoTongTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) throws SQLException {

        long versionTonPhat = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaoCao, "ton_phat")
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        DieuHanhTonTongResponse dieuHanhTonTongResponse = new DieuHanhTonTongResponse();

        Long versionTonThu = getChiSoVersion(ngayBaoCao, "ton_thu");
        //Lấy tồn thu theo chia CA1,CA2
        Long tongTonThu = null;
        if(versionTonThu != null)
            tongTonThu = tonThuTheoKHRepository.tongTonThu(ngayBaoCao, maChiNhanh, maBuuCuc, getCa(), versionTonThu);
        // Lấy tồn phát
        Long tongTonPhat = tonPhatNguongNewRepository.tongTonPhat(ngayBaoCao, maChiNhanh, maBuuCuc,versionTonPhat);
        //Lấy tồn phát chưa phân công phát
        DashboardTongTonDto tongTonPhatChuaPhanCongPhat = tonKhacDAO.getTongTon(ngayBaoCao, maChiNhanh, maBuuCuc, "TON_CHUA_PCP_TONG_HOP");
        //Lấy tồn hành trình
        DashboardTongTonDto tongTonHanhTrinh = tonKhacDAO.getTongTon(ngayBaoCao, maChiNhanh, maBuuCuc, "TON_TRANG_THAI_TONG_HOP");
        //Lấy tồn nhập máy
        DashboardTongTonDto tonNhapMay = tonKhacDAO.getTongTon(ngayBaoCao, maChiNhanh, maBuuCuc, "ALL_TON_NHAP_MAY_CHUA_KET_NOI");
        //tồn chưa có hành trình
        DashboardTongTonDto tonChuaCoHanhTrinh = tonKhacDAO.getTongTon(ngayBaoCao, maChiNhanh, maBuuCuc, "TON_CHUA_CO_HANH_TRINH_TONG_HOP");
        //tồn nhận bàn giao
        DashboardTongTonDto tonNhanBanGiao = tonKhacDAO.getTongTon(ngayBaoCao, maChiNhanh, maBuuCuc, "ALL_TON_NHAN_BAN_GIAO");
        dieuHanhTonTongResponse.setTonThu(tongTonThu);
        dieuHanhTonTongResponse.setTonPhat(tongTonPhat);
        dieuHanhTonTongResponse.setTonPhatChuaPhanCong(tongTonPhatChuaPhanCongPhat.getSl());
        dieuHanhTonTongResponse.setTonThuCuoiDon(tongTonHanhTrinh.getSl());
        dieuHanhTonTongResponse.setTonNhapMayChuaKetNoi(tonNhapMay.getSl());
        dieuHanhTonTongResponse.setTonChuaCoHanhTrinh(tonChuaCoHanhTrinh.getSl());
        dieuHanhTonTongResponse.setTonNhanBanGiao(tonNhanBanGiao.getSl());
        dieuHanhTonTongResponse.setThuUpdateAt(tonThuTheoKHRepository.getTimeTinhToan(versionTonThu,ngayBaoCao));
        dieuHanhTonTongResponse.setPhatUpdateAt(tonPhatNguongNewRepository.getTimeTinhToan(ngayBaoCao,versionTonPhat));
        dieuHanhTonTongResponse.setChuaPCPUpdateAt(tongTonPhatChuaPhanCongPhat.getUpdatedAt());
        dieuHanhTonTongResponse.setCuoiDonUpdateAt(tongTonHanhTrinh.getUpdatedAt());
        dieuHanhTonTongResponse.setNhapMayUpdateAt(tonNhapMay.getUpdatedAt());
        dieuHanhTonTongResponse.setChuaCoHanhTrinhUpdateAt(tonChuaCoHanhTrinh.getUpdatedAt());
        dieuHanhTonTongResponse.setNhanBanGiaoUpdateAt(tonNhanBanGiao.getUpdatedAt());
        return dieuHanhTonTongResponse;
    }

    @Override
    public DieuHanhTonTongResponse dashBoardTongTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) throws SQLException {
        DieuHanhTonTongResponse dieuHanhTonTongResponse = new DieuHanhTonTongResponse();
//        System.out.println("CA LÀM VIỆC: " + ca);

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE))
            return dieuHanhTonTongResponse;

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)) {
            if (UserContext.getUserData().getListChiNhanhVeriable().contains(maChiNhanh)) {
                dieuHanhTonTongResponse = chiSoTongTon(ngayBaoCao, maChiNhanh, maBuuCuc);
            }
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            if (UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc)) {
                dieuHanhTonTongResponse = chiSoTongTon(ngayBaoCao, maChiNhanh, maBuuCuc);
            }
        }
        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            dieuHanhTonTongResponse = chiSoTongTon(ngayBaoCao, maChiNhanh, maBuuCuc);
        }
        //Sét giá trị cho object response
//        dieuHanhTonTongResponse.setTonThu(tongTonThu);
//        dieuHanhTonTongResponse.setTonPhat(tongTonPhat);
//        dieuHanhTonTongResponse.setTonPhatChuaPhanCong(tongTonPhatChuaPhanCongPhat);
//        dieuHanhTonTongResponse.setTonThuCuoiDon(tongTonHanhTrinh);
//        dieuHanhTonTongResponse.setTonNhapMayChuaKetNoi(tonNhapMay);
        return dieuHanhTonTongResponse;
    }

    public Long getChiSoVersion(LocalDate ngayBaoCao, String maChiSo) {
        Optional<ChiSoVersion> chiSoVersionEntity = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaoCao, maChiSo);
        return chiSoVersionEntity.map(ChiSoVersion::getVersion).orElse(0L);
    }


    @Override
    public List<BieuDoTonTongResponse> bieuDoTongTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) {
        List<BieuDoTonTongResponse> bieuDoTonTongResponses = new ArrayList<>();
        List<BieuDoTonResult> bieuDoTonThu = new ArrayList<>();
        List<BieuDoTonResult> bieuDoNguongTonPhat = new ArrayList<>();

        long versionTonPhat = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaoCao, "ton_phat")
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        Long versionTonThu = getChiSoVersion(ngayBaoCao, "ton_thu");

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE))
            return bieuDoTonTongResponses;

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)) {
            if (UserContext.getUserData().getListChiNhanhVeriable().contains(maChiNhanh)) {
                if(versionTonThu != null)
                    bieuDoTonThu = tonThuTheoKHRepository.bieuDoTonThu(ngayBaoCao, maChiNhanh, maBuuCuc, getCa(), versionTonThu);
                bieuDoNguongTonPhat = tonPhatNguongNewRepository.bieuDoNguongTonPhat(ngayBaoCao, maChiNhanh, maBuuCuc,versionTonPhat);
            }
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            if (UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc)) {
                if(versionTonThu != null)
                    bieuDoTonThu = tonThuTheoKHRepository.bieuDoTonThu(ngayBaoCao, maChiNhanh, maBuuCuc, getCa(), versionTonThu);
                bieuDoNguongTonPhat = tonPhatNguongNewRepository.bieuDoNguongTonPhat(ngayBaoCao, maChiNhanh, maBuuCuc,versionTonPhat);
            }
        }
        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if(versionTonThu != null)
                bieuDoTonThu = tonThuTheoKHRepository.bieuDoTonThu(ngayBaoCao, maChiNhanh, maBuuCuc, getCa(), versionTonThu);
            bieuDoNguongTonPhat = tonPhatNguongNewRepository.bieuDoNguongTonPhat(ngayBaoCao, maChiNhanh, maBuuCuc,versionTonPhat);
        }
        BieuDoTonTongResponse tonThu = new BieuDoTonTongResponse();
        tonThu.setLoaiTon("THU");
        BieuDoTonTongResponse tonPhat = new BieuDoTonTongResponse();
        tonPhat.setLoaiTon("PHAT");
        bieuDoTonThu.forEach(thu -> {
            switch (thu.getNguongTon()) {
                case "XANH":
                    tonThu.setXanh(thu.getSl());
                    break;
                case "VANG":
                    tonThu.setVang(thu.getSl());
                    break;
                case "DO_1":
                    tonThu.setDo1(thu.getSl());
                    break;
                case "DO_2":
                    tonThu.setDo2(thu.getSl());
                    break;
                case "DO_3":
                    tonThu.setDo3(thu.getSl());
                    break;
                default:
                    break;
            }
        });
        bieuDoNguongTonPhat.forEach(phat -> {
            switch (phat.getNguongTon()) {
                case "XANH":
                    tonPhat.setXanh(phat.getSl());
                    break;
                case "VANG":
                    tonPhat.setVang(phat.getSl());
                    break;
                case "DO_1":
                    tonPhat.setDo1(phat.getSl());
                    break;
                case "DO_2":
                    tonPhat.setDo2(phat.getSl());
                    break;
                case "DO_3":
                    tonPhat.setDo3(phat.getSl());
                    break;
                case "DO_4":
                    tonPhat.setDo4(phat.getSl());
                    break;
                case "DO_5":
                    tonPhat.setDo5(phat.getSl());
                    break;
                case "DO_6":
                    tonPhat.setDo6(phat.getSl());
                    break;
                case "DO_7":
                    tonPhat.setDo7(phat.getSl());
                    break;
                case "DO_8":
                    tonPhat.setDo8(phat.getSl());
                    break;
                case "DO_9":
                    tonPhat.setDo9(phat.getSl());
                    break;
                default:
                    break;
            }
        });
        bieuDoTonTongResponses.add(tonThu);
        bieuDoTonTongResponses.add(tonPhat);
        return bieuDoTonTongResponses;
    }

    @Override
    public List<TonNhapMayChuaKetNoiResponse> bieuDoTonChuaNhapMay(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) throws SQLException {
        LocalDate ngayBatDau = ngayBaoCao.minusDays(7);
        List<TonNhapMayChuaKetNoiResponse> tonNhapMayChuaKetNoiResponses = new ArrayList<>();
        List<TonNhapMayChuaKetNoiResponse> tonNhapMayN1 = new ArrayList<>();
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return tonNhapMayChuaKetNoiResponses;
        }
        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)) {
            if (UserContext.getUserData().getListChiNhanhVeriable().contains(maChiNhanh)) {
                tonNhapMayChuaKetNoiResponses = tonNhapMayDAO.getBieuDoTonNhapMay(ngayBaoCao, ngayBatDau, maChiNhanh, maBuuCuc);
                tonNhapMayN1 = tonNhapMayDAO.getBieuDoTonNhapMay(ngayBaoCao.minusDays(1), ngayBaoCao.minusDays(8), maChiNhanh, maBuuCuc);
            }
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            if (UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc)) {
                tonNhapMayChuaKetNoiResponses = tonNhapMayDAO.getBieuDoTonNhapMay(ngayBaoCao, ngayBatDau, maChiNhanh, maBuuCuc);
                tonNhapMayN1 = tonNhapMayDAO.getBieuDoTonNhapMay(ngayBaoCao.minusDays(1), ngayBaoCao.minusDays(8), maChiNhanh, maBuuCuc);
            }
        }
        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            tonNhapMayChuaKetNoiResponses = tonNhapMayDAO.getBieuDoTonNhapMay(ngayBaoCao, ngayBatDau, maChiNhanh, maBuuCuc);
            tonNhapMayN1 = tonNhapMayDAO.getBieuDoTonNhapMay(ngayBaoCao.minusDays(1), ngayBaoCao.minusDays(8), maChiNhanh, maBuuCuc);
        }

        Map<LocalDate, TonNhapMayChuaKetNoiResponse> mapN1 = new HashMap<>();
        Map<LocalDate, TonNhapMayChuaKetNoiResponse> mapN = new HashMap<>();

        for (TonNhapMayChuaKetNoiResponse n1 : tonNhapMayN1) {
            mapN1.put(n1.getNgayBaoCao(), n1);
        }

        for (TonNhapMayChuaKetNoiResponse n : tonNhapMayChuaKetNoiResponses) {
            mapN.put(n.getNgayBaoCao(), n);
        }

        //lấy list ngày từ ngày N-7 đến ngày N
        Stream<LocalDate> dates = ngayBaoCao.minusDays(7).datesUntil(ngayBaoCao.plusDays(1));
        List<LocalDate> listDate = dates.collect(Collectors.toList());

        List<TonNhapMayChuaKetNoiResponse> finalList = new ArrayList<>();

        for (LocalDate date : listDate) {
            //nếu ngày N có data
            if (mapN.containsKey(date)) {
                TonNhapMayChuaKetNoiResponse n = mapN.get(date);
                TonNhapMayChuaKetNoiResponse n1 = new TonNhapMayChuaKetNoiResponse();
                Long n1SlChuaKetNoi = Long.valueOf(0);

                if (mapN1.containsKey(n.getNgayBaoCao().minusDays(1))) {
                    n1 = mapN1.get(n.getNgayBaoCao().minusDays(1));
                    n1SlChuaKetNoi = n1.getSlTon() == null ? 0 : n1.getSlTon();
                }

                //Sl phải kết nối = sl nhận thành công N + sl chưa kết nối N-1
                Long slPhaiKetNoi = n.getTongSl() == null ? n1SlChuaKetNoi : n1SlChuaKetNoi + n.getTongSl();
                n.setTongSl(slPhaiKetNoi);
                if (slPhaiKetNoi == null || slPhaiKetNoi == 0) {
                    n.setTlTon(null);
                } else {
                    n.setTlTon(((float) n.getSlTon() / (float) slPhaiKetNoi) * 100);
                }
                finalList.add(n);
            }
            //nếu k có data thì check ngày trước có data không
            else {
                if (mapN1.containsKey(date.minusDays(1))) {
                    TonNhapMayChuaKetNoiResponse n = new TonNhapMayChuaKetNoiResponse();
                    TonNhapMayChuaKetNoiResponse n1 = mapN1.get(date.minusDays(1));

                    n.setTongSl(n1.getSlTon());
                    n.setNgayBaoCao(date);
                    n.setTlTon(0F);
                    n.setSlTon(0L);
                    finalList.add(n);
                }
            }
        }
        return finalList;
    }

    @Override
    public ListContentPageDto<TonHanhTrinhListingResponse> tonHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan, String loaiCanhBao) throws SQLException{
        Page<TonHanhTrinhListingResponse> tonListingResponses;
        List<TonHanhTrinhListingResponse> listResult = new ArrayList<>();
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_TRANG_THAI_CHI_TIET", loaiHang, loaiCanhBao, 0, trangThaiList,loaiDon,khDacThuGui,khDacThuNhan);
        listResult = tonKhacDAO.getDetailTrangThai(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang, trangThaiList,loaiDon,khDacThuGui,khDacThuNhan, loaiCanhBao);
        Pageable pageable = PageRequest.of(pageNo, pageSize);
        tonListingResponses = new PageImpl<>(listResult, pageable, total);

        return new ListContentPageDto<>(tonListingResponses,total);
    }
    @Override
    public ListContentPageDto<TonHanhTrinhListingResponse> tonHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException{
        return tonHanhTrinh(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, pageNo, pageSize, vungCon, doiTac, loaiHang, null,"all","0","0", null);
    }

    @Override
    public ListContentPageDto<TonChuaPCPListingResponse> tonChuaPCP(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException {
        Page<TonChuaPCPListingResponse> tonListingResponses = Page.empty();
        List<TonChuaPCPListingResponse> listResult = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();


        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }


        listResult = tonKhacDAO.getDetailChuaPCP(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang);

        //get tổng số bản ghi
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_CHUA_PCP_CHI_TIET", loaiHang, "", 0);

        tonListingResponses = new PageImpl<TonChuaPCPListingResponse>(listResult, PageRequest.of(pageNo, pageSize), total);

        return new ListContentPageDto<>(tonListingResponses);
    }

    @Override
    public ListContentPageDto<TonChuaPCPListingResponse> tonChuaCoHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang, String loaiCanhBao) throws SQLException {
        Page<TonChuaPCPListingResponse> tonListingResponses = Page.empty();
        List<TonChuaPCPListingResponse> listResult = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();


        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }


        listResult = tonKhacDAO.getDetailHanhTrinh(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang, loaiCanhBao);

        //get tổng số bản ghi
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_CHUA_CO_HANH_TRINH_CHI_TIET", loaiHang, loaiCanhBao, 0);

        tonListingResponses = new PageImpl<TonChuaPCPListingResponse>(listResult, PageRequest.of(pageNo, pageSize), total);

        return new ListContentPageDto<>(tonListingResponses);
    }

    @Override
    public ListContentPageDto<TonChuaPCPListingResponse> tonNhanBanGiao(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException {
        Page<TonChuaPCPListingResponse> tonListingResponses = Page.empty();
        List<TonChuaPCPListingResponse> listResult = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();


        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE) && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            return new ListContentPageDto<>();
        }


        listResult = tonKhacDAO.getDetailNhanBanGiao(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang);

        //get tổng số bản ghi
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_NHAN_BAN_GIAO", loaiHang, "", 0);

        tonListingResponses = new PageImpl<TonChuaPCPListingResponse>(listResult, PageRequest.of(pageNo, pageSize), total);

        return new ListContentPageDto<>(tonListingResponses);
    }

    @Override
    public ListContentPageDto<TonChuaPCPListingResponse> tonNhanBanGiao(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang, String loaiCanhBao) throws SQLException {
        Page<TonChuaPCPListingResponse> tonListingResponses = Page.empty();
        List<TonChuaPCPListingResponse> listResult = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();


        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }


        listResult = tonKhacDAO.getDetailNhanBanGiao(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang, loaiCanhBao);

        //get tổng số bản ghi
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_NHAN_BAN_GIAO", loaiHang, loaiCanhBao, 0);

        tonListingResponses = new PageImpl<TonChuaPCPListingResponse>(listResult, PageRequest.of(pageNo, pageSize), total);

        return new ListContentPageDto<>(tonListingResponses);
    }


    @Override
    public ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMay(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException {
        Page<TonHanhTrinhListingResponse> tonListingResponses = Page.empty();
        List<TonHanhTrinhListingResponse> listResult = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }

        listResult = tonKhacDAO.getDetailChuaNhapMay(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang, 0);

        //get tổng số bản ghi
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_NHAP_MAY_CHUA_KET_NOI", loaiHang, "", 0);

        tonListingResponses = new PageImpl<TonHanhTrinhListingResponse>(listResult, PageRequest.of(pageNo, pageSize), total);

        return new ListContentPageDto<>(tonListingResponses);
    }

    @Override
    public ListContentPageDto<TonHanhTrinhListingResponse> tonKienCham(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException {
        Page<TonHanhTrinhListingResponse> tonListingResponses = Page.empty();
        List<TonHanhTrinhListingResponse> listResult = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE)) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }

        listResult = tonKhacDAO.getDetailChuaNhapMay(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang, 1);

        //get tổng số bản ghi
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_NHAP_MAY_CHUA_KET_NOI", loaiHang, "", 1);

        tonListingResponses = new PageImpl<TonHanhTrinhListingResponse>(listResult, PageRequest.of(pageNo, pageSize), total);

        return new ListContentPageDto<>(tonListingResponses);
    }

    @Override
    public ListContentPageDto<TonDvGiaTangListingResponse> tonDvGiaTang(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException {
        Page<TonDvGiaTangListingResponse> tonListingResponses = Page.empty();
        List<TonDvGiaTangListingResponse> listResult = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();


        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }


        listResult = tonKhacDAO.getDetailDvGiaTang(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, pageNo, pageSize, loaiHang);

        //get tổng số bản ghi
        Long total = tonKhacDAO.getTotalDetail(ngayBaoCao, vungCon, listCN, listBC, doiTac, trangThai, "TON_DV_GIA_TANG_CHI_TIET_V2", loaiHang, "", 0);

        tonListingResponses = new PageImpl<TonDvGiaTangListingResponse>(listResult, PageRequest.of(pageNo, pageSize), total);

        return new ListContentPageDto<>(tonListingResponses, total);
    }

    @Override
    public TonDetailBillResponse tonNhapMayBill(String maPhieuGui) throws SQLException {
        return tonKhacDAO.getDetailMaPhieuGui(maPhieuGui, "TON_NHAP_MAY_CHUA_KET_NOI");
    }

    @Override
    public TonDetailBillResponse tonHanhTrinhBill(String maPhieuGui) throws SQLException {
        return tonKhacDAO.getDetailMaPhieuGui(maPhieuGui, "TON_TRANG_THAI_CHI_TIET");
    }

    @Override
    public TonDetailBillResponse tonChuaPcpBill(String maPhieuGui) throws SQLException {
        return tonKhacDAO.getDetailMaPhieuGui(maPhieuGui, "TON_CHUA_PCP_CHI_TIET");
    }

    @Override
    public ListContentPageDto<TongHopTonDto> tonghopTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String sort, String sortBy, String loaiHang) throws SQLException {
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return new ListContentPageDto<>();
        }

        List<TongHopTonDto> tonNhapMay = new ArrayList<>();
        List<TongHopTonDto> tonChuaPCP = new ArrayList<>();
        List<TongHopTonDto> tonTrangThai = new ArrayList<>();
        List<TongHopTonDto> tonChuaCoHanhTrinh= new ArrayList<>();
        List<TongHopTonDto> tonNhanBanGiao = new ArrayList<>();
        List<TongHopTonDto> tonDvGiaTang = new ArrayList<>();

        Pageable page = PageRequest.of(pageNo, pageSize);

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)) {

            if (!maChiNhanh.equals("") && !UserContext.getUserData().getListChiNhanhVeriable().contains(maChiNhanh)) {
                return new ListContentPageDto<>();
            }
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            if (!maBuuCuc.equals("") && !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc)) {
                return new ListContentPageDto<>();
            }
        }

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals("")){
                listCN = List.of(maChiNhanh);
            }


        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        logger.info("begin query ton ");

        CompletableFuture<List<TongHopTonDto>> futureTonNhapMay = tonKhacDAO.getListChuaNhapMay(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> Collections.emptyList());

        CompletableFuture<List<TongHopTonDto>> futureTonChuaPCP = tonKhacDAO.getListChuaPCP(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> Collections.emptyList());

        CompletableFuture<List<TongHopTonDto>> futureTonTrangThai = tonKhacDAO.getListTrangThai(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> Collections.emptyList());

        CompletableFuture<List<TongHopTonDto>> futureTonChuaCoHanhTrinh = tonKhacDAO.getListTonChuaCoHanhTrinh(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> Collections.emptyList());

        CompletableFuture<List<TongHopTonDto>> futureTonNhanBanGiao = tonKhacDAO.getListTonNhanBanGiao(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> Collections.emptyList());

        CompletableFuture<List<TongHopTonDto>> futureTonDvGiaTang = tonKhacDAO.getListTonDvGiaTang(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> Collections.emptyList());


        CompletableFuture.allOf(futureTonNhapMay, futureTonChuaPCP, futureTonTrangThai, futureTonChuaCoHanhTrinh, futureTonNhanBanGiao, futureTonDvGiaTang).join();

        try {
            tonNhapMay = futureTonNhapMay.get();
            tonChuaPCP = futureTonChuaPCP.get();
            tonTrangThai = futureTonTrangThai.get();
            tonChuaCoHanhTrinh = futureTonChuaCoHanhTrinh.get();
            tonNhanBanGiao = futureTonNhanBanGiao.get();
            tonDvGiaTang = futureTonDvGiaTang.get();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        logger.info("end get list ton ");

        Map<String, TongHopTonDto> mapNhapMay;
        Map<String, TongHopTonDto> mapChuaPCP;
        Map<String, TongHopTonDto> mapTrangThai;
        Map<String, TongHopTonDto> mapChuaCoHanhTrinh;
        Map<String, TongHopTonDto> mapNhanBanGiao;
        Map<String, TongHopTonDto> mapDvGiaTang;

        if(maChiNhanh.equals("")) {
            mapChuaPCP = tonChuaPCP.stream().collect(Collectors.toMap(TongHopTonDto::getChiNhanh, Function.identity()));
            mapNhapMay = tonNhapMay.stream().collect(Collectors.toMap(TongHopTonDto::getChiNhanh, Function.identity()));
            mapTrangThai = tonTrangThai.stream().collect(Collectors.toMap(TongHopTonDto::getChiNhanh, Function.identity()));
            mapChuaCoHanhTrinh = tonChuaCoHanhTrinh.stream().collect(Collectors.toMap(TongHopTonDto::getChiNhanh, Function.identity()));
            mapNhanBanGiao = tonNhanBanGiao.stream().collect(Collectors.toMap(TongHopTonDto::getChiNhanh, Function.identity()));
            mapDvGiaTang = tonDvGiaTang.stream().collect(Collectors.toMap(TongHopTonDto::getChiNhanh, Function.identity()));
        }
        else {
            mapChuaPCP = tonChuaPCP.stream().collect(Collectors.toMap(dto -> dto.getBuuCuc() + dto.getVungCon(), Function.identity()));


            mapNhapMay = tonNhapMay.stream().collect(Collectors.toMap(dto -> dto.getBuuCuc() + dto.getVungCon(), Function.identity()));
            mapTrangThai = tonTrangThai.stream().collect(Collectors.toMap(dto -> dto.getBuuCuc() + dto.getVungCon(), Function.identity()));
            mapChuaCoHanhTrinh = tonChuaCoHanhTrinh.stream().collect(Collectors.toMap(dto -> dto.getBuuCuc() + dto.getVungCon(), Function.identity()));
            mapNhanBanGiao = tonNhanBanGiao.stream().collect(Collectors.toMap(dto -> dto.getBuuCuc() + dto.getVungCon(), Function.identity()));
            mapDvGiaTang = tonDvGiaTang.stream().collect(Collectors.toMap(dto -> dto.getBuuCuc() + dto.getVungCon(), Function.identity()));
        }

        //tạo set chứa tất cả chi nhánh/bưu cục của tất cả tồn
        Set<String> allKeySet = Stream.of(mapChuaPCP.keySet(), mapNhapMay.keySet(), mapTrangThai.keySet(), mapChuaCoHanhTrinh.keySet(), mapNhanBanGiao.keySet(), mapDvGiaTang.keySet()).flatMap(Collection::stream).collect(Collectors.toSet());
        List<String> allKeyList = new ArrayList<>(allKeySet);

        List<TongHopTonDto> finalList = new ArrayList<>();

        for (String key : allKeyList) {
            TongHopTonDto ton = new TongHopTonDto();

            if (maChiNhanh.equals("")) {
                ton.setChiNhanh(key);
            }
            //set số tồn nhập máy
            if (mapNhapMay.containsKey(key)) {
                ton.setTonNhapMay(mapNhapMay.get(key).getTonNhapMay());
                ton.setTonKienCham(mapNhapMay.get(key).getTonKienCham());
                ton.setChiNhanh(mapNhapMay.get(key).getChiNhanh());
                ton.setVungCon(mapNhapMay.get(key).getVungCon());
                ton.setBuuCuc(mapNhapMay.get(key).getBuuCuc());
            }

            //set số tồn chưa pcp
            if (mapChuaPCP.containsKey(key)) {
                ton.setTonChuaPCP(mapChuaPCP.get(key).getTonChuaPCP());
                ton.setChiNhanh(mapChuaPCP.get(key).getChiNhanh());
                ton.setVungCon(mapChuaPCP.get(key).getVungCon());
                ton.setBuuCuc(mapChuaPCP.get(key).getBuuCuc());
            }

            //set số tồn trạng thái
            if (mapTrangThai.containsKey(key)) {
                ton.setTonHanhTrinh(mapTrangThai.get(key).getTonHanhTrinh());
                ton.setChiNhanh(mapTrangThai.get(key).getChiNhanh());
                ton.setVungCon(mapTrangThai.get(key).getVungCon());
                ton.setBuuCuc(mapTrangThai.get(key).getBuuCuc());
            }

            //set số tồn chưa có hành trình
            if (mapChuaCoHanhTrinh.containsKey(key)) {
                ton.setTonChuaCoHanhTrinh(mapChuaCoHanhTrinh.get(key).getTonChuaCoHanhTrinh());
                ton.setChiNhanh(mapChuaCoHanhTrinh.get(key).getChiNhanh());
                ton.setVungCon(mapChuaCoHanhTrinh.get(key).getVungCon());
                ton.setBuuCuc(mapChuaCoHanhTrinh.get(key).getBuuCuc());
            }

            //set số tồn nhận bàn giao
            if (mapNhanBanGiao.containsKey(key)) {
                ton.setTonNhanBanGiao(mapNhanBanGiao.get(key).getTonNhanBanGiao());
                ton.setChiNhanh(mapNhanBanGiao.get(key).getChiNhanh());
                ton.setVungCon(mapNhanBanGiao.get(key).getVungCon());
                ton.setBuuCuc(mapNhanBanGiao.get(key).getBuuCuc());
            }

            //set số tồn dịch vụ gia tăng
            if (mapDvGiaTang.containsKey(key)) {
                ton.setTonDvGiaTang(mapDvGiaTang.get(key).getTonDvGiaTang());
                ton.setChiNhanh(mapDvGiaTang.get(key).getChiNhanh());
                ton.setVungCon(mapDvGiaTang.get(key).getVungCon());
                ton.setBuuCuc(mapDvGiaTang.get(key).getBuuCuc());
            }
            //add vào final list
            finalList.add(ton);
        }
//        Collections.sort(finalList, comparing(TongHopTonDto::getTonNhapMay));


        if (!sort.equals(""))
            switch (sortBy.toLowerCase(Locale.ROOT)) {
                case "ton_nhap_may":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonNhapMay, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonNhapMay, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_kien_cham":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonKienCham, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonKienCham, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_trang_thai":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonHanhTrinh, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonHanhTrinh, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_nhan_ban_giao":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonNhanBanGiao, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonNhanBanGiao, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_chua_pcp":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonChuaPCP, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonChuaPCP, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_chua_co_hanh_trinh":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonChuaCoHanhTrinh, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonChuaCoHanhTrinh, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_dich_vu_gia_tang":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonDvGiaTang, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDto::getTonDvGiaTang, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                default:
                    break;
            }
        //tạo page từ list
        int start = (int) page.getOffset();
        int end = Math.min((start + page.getPageSize()), finalList.size());
        if (start > finalList.size())
            return new ListContentPageDto<>();
        List<TongHopTonDto> pageData = finalList.subList(start, end);
        Page<TongHopTonDto> pageFinal = new PageImpl<>(pageData, page, finalList.size());

        return new ListContentPageDto<TongHopTonDto>(pageFinal);
    }

    @Override
    public ListContentPageDto<TongHopTonDtoNoVungCon> tonghopTonMobile(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String sort, String sortBy) throws SQLException {
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE) && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            return new ListContentPageDto<>();
        }

        List<TongHopTonDtoNoVungCon> tonNhapMay = new ArrayList<>();
        List<TongHopTonDtoNoVungCon> tonChuaPCP = new ArrayList<>();
        List<TongHopTonDtoNoVungCon> tonTrangThai = new ArrayList<>();

        Pageable page = PageRequest.of(pageNo, pageSize);

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)) {
            if (!maChiNhanh.equals("") && !UserContext.getUserData().getListChiNhanhVeriable().contains(maChiNhanh)) {
                return new ListContentPageDto<>();
            }
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            if (!maBuuCuc.equals("") && !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc)) {
                return new ListContentPageDto<>();
            }
        }

        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);

        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        tonNhapMay = tonKhacDAO.getListChuaNhapMayMobile(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh);
        tonChuaPCP = tonKhacDAO.getListChuaPCPMobile(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh);
        tonTrangThai = tonKhacDAO.getListTrangThaiMobile(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh);


        Map<String, TongHopTonDtoNoVungCon> mapNhapMay;

        Map<String, TongHopTonDtoNoVungCon> mapChuaPCP;

        Map<String, TongHopTonDtoNoVungCon> mapTrangThai;

        if (maChiNhanh.equals("")) {
            mapChuaPCP = tonChuaPCP.stream().collect(Collectors.toMap(TongHopTonDtoNoVungCon::getChiNhanh, Function.identity()));
            mapNhapMay = tonNhapMay.stream().collect(Collectors.toMap(TongHopTonDtoNoVungCon::getChiNhanh, Function.identity()));
            mapTrangThai = tonTrangThai.stream().collect(Collectors.toMap(TongHopTonDtoNoVungCon::getChiNhanh, Function.identity()));
        } else {
            mapChuaPCP = tonChuaPCP.stream().collect(Collectors.toMap(TongHopTonDtoNoVungCon::getBuuCuc, Function.identity()));
            mapNhapMay = tonNhapMay.stream().collect(Collectors.toMap(TongHopTonDtoNoVungCon::getBuuCuc, Function.identity()));
            mapTrangThai = tonTrangThai.stream().collect(Collectors.toMap(TongHopTonDtoNoVungCon::getBuuCuc, Function.identity()));
        }

        Set<String> allKeySet = Stream.of(mapNhapMay.keySet(), mapChuaPCP.keySet(), mapTrangThai.keySet()).flatMap(Collection::stream).collect(Collectors.toSet());
        List<String> allKeyList = new ArrayList<>(allKeySet);

        List<TongHopTonDtoNoVungCon> finalList = new ArrayList<>();

        for (String key : allKeyList) {
            TongHopTonDtoNoVungCon ton = new TongHopTonDtoNoVungCon();

            if (maChiNhanh.equals("")) {
                ton.setChiNhanh(key);
            }
            //set số tồn nhập máy
            if (mapNhapMay.containsKey(key)) {
                ton.setTonNhapMay(mapNhapMay.get(key).getTonNhapMay());
                ton.setChiNhanh(mapNhapMay.get(key).getChiNhanh());
                ton.setBuuCuc(mapNhapMay.get(key).getBuuCuc());
            }

            //set số tồn chưa pcp
            if (mapChuaPCP.containsKey(key)) {
                ton.setTonChuaPCP(mapChuaPCP.get(key).getTonChuaPCP());
                ton.setChiNhanh(mapChuaPCP.get(key).getChiNhanh());
                ton.setBuuCuc(mapChuaPCP.get(key).getBuuCuc());
            }

            //set số tồn trạng thái
            if (mapTrangThai.containsKey(key)) {
                ton.setTonHanhTrinh(mapTrangThai.get(key).getTonHanhTrinh());
                ton.setChiNhanh(mapTrangThai.get(key).getChiNhanh());
                ton.setBuuCuc(mapTrangThai.get(key).getBuuCuc());
            }
            //add vào final list
            finalList.add(ton);
        }

        if (!sort.equals(""))
            switch (sortBy.toLowerCase(Locale.ROOT)) {
                case "ton_nhap_may":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDtoNoVungCon::getTonNhapMay, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDtoNoVungCon::getTonNhapMay, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_trang_thai":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDtoNoVungCon::getTonHanhTrinh, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDtoNoVungCon::getTonHanhTrinh, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ton_nhan_ban_giao":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(finalList, comparing(TongHopTonDtoNoVungCon::getTonChuaPCP, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(finalList, comparing(TongHopTonDtoNoVungCon::getTonChuaPCP, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                default:
                    break;
            }


        //tạo page từ list
        int start = (int) page.getOffset();
        int end = Math.min((start + page.getPageSize()), finalList.size());
        if (start > finalList.size())
            return new ListContentPageDto<>();
        List<TongHopTonDtoNoVungCon> pageData = finalList.subList(start, end);
        Page<TongHopTonDtoNoVungCon> pageFinal = new PageImpl<>(pageData, page, finalList.size());

        return new ListContentPageDto<TongHopTonDtoNoVungCon>(pageFinal);
    }

    @Override
    public TongHopTonDto tonghopTonSum(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String vungCon, String doiTac, String loaiHang) throws SQLException {
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(FALSE) && UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(FALSE)
                && UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(FALSE)
                && FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                && UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(FALSE)) {
            return null;
        }

        TongHopTonDto tonNhapMay = new TongHopTonDto();
        TongHopTonDto tonChuaPCP = new TongHopTonDto();
        TongHopTonDto tonTrangThai = new TongHopTonDto();
        TongHopTonDto tonChuaCoHanhTrinh = new TongHopTonDto();
        TongHopTonDto tonNhanBanGiao = new TongHopTonDto();
        TongHopTonDto tonDvGiaTang = new TongHopTonDto();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        TongHopTonDto summary = new TongHopTonDto();

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)) {
            if (!maChiNhanh.equals("") && !UserContext.getUserData().getListChiNhanhVeriable().contains(maChiNhanh)) {
                return summary;
            }
        }
        if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            if (!maBuuCuc.equals("") && !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc)) {
                return summary;
            }
        }
        if (UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (!maBuuCuc.equals(""))
                listBC = List.of(maBuuCuc);
            if (!maChiNhanh.equals(""))
                listCN = List.of(maChiNhanh);
        } else if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && maBuuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        CompletableFuture<TongHopTonDto> futureTonNhapMay = tonKhacDAO.getSumChuaNhapMay(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> new TongHopTonDto());

        CompletableFuture<TongHopTonDto> futureTonChuaPCP = tonKhacDAO.getSumChuaPCP(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> new TongHopTonDto());

        CompletableFuture<TongHopTonDto> futureTonTrangThai = tonKhacDAO.getSumTrangThai(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> new TongHopTonDto());

        CompletableFuture<TongHopTonDto> futureTonChuaCoHanhTrinh = tonKhacDAO.getSumTonChuaCoHanhTrinh(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> new TongHopTonDto());

        CompletableFuture<TongHopTonDto> futureTonNhanBanGiao = tonKhacDAO.getSumTonNhanBanGiao(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> new TongHopTonDto());

        CompletableFuture<TongHopTonDto> futureTonDvGiaTang = tonKhacDAO.getSumDvGiaTang(ngayBaoCao, vungCon, listCN, listBC, doiTac, maChiNhanh, loaiHang)
                .exceptionally(e -> new TongHopTonDto());

        CompletableFuture.allOf(futureTonNhapMay, futureTonChuaPCP, futureTonTrangThai, futureTonChuaCoHanhTrinh, futureTonNhanBanGiao, futureTonDvGiaTang).join();

        try {
            tonNhapMay = futureTonNhapMay.get();
            tonChuaPCP = futureTonChuaPCP.get();
            tonTrangThai = futureTonTrangThai.get();
            tonChuaCoHanhTrinh = futureTonChuaCoHanhTrinh.get();
            tonNhanBanGiao = futureTonNhanBanGiao.get();
            tonDvGiaTang = futureTonDvGiaTang.get();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        summary.setTonChuaPCP(tonChuaPCP.getTonChuaPCP());
        summary.setTonNhapMay(tonNhapMay.getTonNhapMay());
        summary.setTonHanhTrinh(tonTrangThai.getTonHanhTrinh());
        summary.setTonChuaCoHanhTrinh(tonChuaCoHanhTrinh.getTonChuaCoHanhTrinh());
        summary.setTonNhanBanGiao(tonNhanBanGiao.getTonNhanBanGiao());
        summary.setTonKienCham(tonNhapMay.getTonKienCham());
        summary.setTonDvGiaTang(tonDvGiaTang.getTonDvGiaTang());

        return summary;
    }

    //lấy list chi nhánh, bưu cục cần để query theo filter + SSO
    public Map<String, List<String>> checkListCNBC(String chiNhanh, String buuCuc) {
        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (chiNhanh.equals("")) {
            listCN = UserContext.getUserData().getListChiNhanhVeriable();
        } else {
            listCN.add(chiNhanh);
        }
        if (buuCuc.equals("")) {
            listBC = UserContext.getUserData().getListBuuCucVeriable();
        } else {
            listBC.add(buuCuc);
        }
        Map<String, List<String>> mapCNBC = new HashMap<>();
        mapCNBC.put("cn", listCN);
        mapCNBC.put("bc", listBC);
        return mapCNBC;
    }

    @Override
    public List<String> getMaDoiTac(LocalDate ngayBaoCao) throws SQLException {
        return tonKhacDAO.getAllMaDoiTac(ngayBaoCao);
    }


    @Override
    public SanLuongTonBuuTa getTonPhatBuuTa(String buuTa){
        SanLuongTonBuuTa tonghop = null;
        tonghop = tonPhatNguongBillRepository.findTonPhatBuuTa(buuTa);
        return tonghop;
    }
//    @Override
//    public SanLuongTonBuuTa getTonThuBuuTa(String buuTa){
//        SanLuongTonBuuTa tonghop = null;
//        tonghop = tonThuBillRepository.findTonThuBuuTa(buuTa, getCa());
//        return tonghop;
//    }
//    @Override
//    public Page<BillDetailThu> getListBillTonThuBuuTa(String buuTa, String trangThai, Integer pageIndex, Integer pageSize,  String orderBy){
//        Page<BillDetailThu> listBill = null;
//        Sort sort = null;
//        Pageable page = null;
//        switch (orderBy) {
//            case "asc":
//                sort = Sort.by(Sort.Direction.ASC, "ngayTao");
//                break;
////            case "desc":
////                sort = Sort.by(Sort.Direction.DESC, "ngayTao");
////                break;
//            default:
//                sort = Sort.by(Sort.Direction.DESC, "ngayTao");
//                break;
//        }
//        page = PageRequest.of(pageIndex, pageSize, sort);
//
//        if (trangThai.equals(DashChiNhanhMobileConstants.QuaHanTHU)) {
//            listBill = tonThuBillRepository.findAllBillQuaHanThuBuuTa(buuTa, getCa(), page);
//        } else if (trangThai.equals(DashChiNhanhMobileConstants.DenHanTHU)) {
//            listBill = tonThuBillRepository.findAllBillDenHanThuBuuTa(buuTa, getCa(), page);
//        }
//        return listBill;
//    }

    @Override
    public Page<BillDetailPhat> getListBillTonPhatBuuTa(String buuTa, String trangThai, Integer pageIndex, Integer pageSize, String orderBy) {
        Page<BillDetailPhat> listBill = null;
        Pageable page;
        Sort sort = null;
        switch (orderBy) {
            case "asc":
                sort = Sort.by(Sort.Direction.ASC, "timePCP");
                break;
//            case "desc":
//                sort = Sort.by(Sort.Direction.DESC, "timePCP");
//                break;
            default:
                sort = Sort.by(Sort.Direction.DESC, "timePCP");
                break;
        }
        page = PageRequest.of(pageIndex, pageSize, sort);
        if (trangThai.equals(DashChiNhanhMobileConstants.DenHanPhat)) {
            listBill = tonPhatNguongBillRepository.findAllBillDenHanPhatBuuTa(buuTa, page);
        } else if (trangThai.equals(DashChiNhanhMobileConstants.QuaHanPhat)) {
            listBill = tonPhatNguongBillRepository.findAllBillQuaHanPhatBuuTa(buuTa, page);
        }
        return listBill;
    }

    @Override
    public BieuDoXuTheTonResponse getBieuDoXuTheTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) {
        BieuDoXuTheTonResponse bieuDoXuTheTonResponses = new BieuDoXuTheTonResponse();
        if (FALSE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) &&
                FALSE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) &&
                FALSE.equalsIgnoreCase(UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc()) &&
                FALSE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog()))
            return bieuDoXuTheTonResponses;
        LocalDate ngayBatDau = ngayBaoCao.minusDays(7);
        Stream<LocalDate> dates = ngayBaoCao.minusDays(7).datesUntil(ngayBaoCao.plusDays(1));
        List<LocalDate> listDate = dates.collect(Collectors.toList());
        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) && UserContext.getUserData().getListChiNhanhVeriable().contains(maChiNhanh)) {
            calculateTotalInventoryForEachInventoryType(listDate, ngayBatDau, ngayBaoCao, maChiNhanh, maBuuCuc, bieuDoXuTheTonResponses);
        }
        if ((TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) && UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc)) {
            calculateTotalInventoryForEachInventoryType(listDate, ngayBatDau, ngayBaoCao, maChiNhanh, maBuuCuc, bieuDoXuTheTonResponses);
        }
        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc())) {
            calculateTotalInventoryForEachInventoryType(listDate, ngayBatDau, ngayBaoCao, maChiNhanh, maBuuCuc, bieuDoXuTheTonResponses);
        }
        return bieuDoXuTheTonResponses;
    }

    private void calculateTotalInventoryForEachInventoryType(List<LocalDate> listDate, LocalDate ngayBatDau, LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, BieuDoXuTheTonResponse bieuDoXuTheTonResponses) {
        List<TonResult> tonThuResult = new ArrayList<>();
        List<TonResult> tonPhatResult = new ArrayList<>();
        List<TonResult> tonNhanBanGiaoResult = new ArrayList<>();
        List<BieuDoXuTheTonDTO> bieuDoXuTheTonList = new ArrayList<>();
        if (ObjectUtils.isEmpty(maChiNhanh)) {
            bieuDoXuTheTonList = bieuDoXuTheTonRepository.getBieuDoXuTheTongTon(ngayBatDau, ngayBaoCao);
        } else if (ObjectUtils.isEmpty(maBuuCuc)) {
            bieuDoXuTheTonList = bieuDoXuTheTonRepository.getBieuDoXuTheTongTonByChiNhanh(ngayBatDau, ngayBaoCao, maChiNhanh);
        } else {
            bieuDoXuTheTonList = bieuDoXuTheTonRepository.getBieuDoXuTheTongTonByBuuCuc(ngayBatDau, ngayBaoCao, maChiNhanh, maBuuCuc);
        }
        groupDataByInventoryType(bieuDoXuTheTonList, tonThuResult, TON_THU);
        groupDataByInventoryType(bieuDoXuTheTonList, tonPhatResult, TON_PHAT);
        groupDataByInventoryType(bieuDoXuTheTonList, tonNhanBanGiaoResult, TON_NHAN_BG);
        Map<LocalDate, TonResult> tonThuMap = tonThuResult.stream().collect(Collectors.toMap(TonResult::getNgayBaoCao, Function.identity(), (existing, replacement) -> replacement));
        Map<LocalDate, TonResult> tonPhatMap = tonPhatResult.stream().collect(Collectors.toMap(TonResult::getNgayBaoCao, Function.identity(), (existing, replacement) -> replacement));
        Map<LocalDate, TonResult> tonNhanBanGiaoMap = tonNhanBanGiaoResult.stream().collect(Collectors.toMap(TonResult::getNgayBaoCao, Function.identity(), (existing, replacement) -> replacement));
        tonThuResult = convertInventoryData(tonThuMap, listDate);
        tonPhatResult = convertInventoryData(tonPhatMap, listDate);
        tonNhanBanGiaoResult = convertInventoryData(tonNhanBanGiaoMap, listDate);
        bieuDoXuTheTonResponses.setTonThuResult(tonThuResult);
        bieuDoXuTheTonResponses.setTonNhanBanGiaoResult(tonNhanBanGiaoResult);
        bieuDoXuTheTonResponses.setTonPhatResult(tonPhatResult);
    }

    private List<TonResult> convertInventoryData(Map<LocalDate, TonResult> tonMap, List<LocalDate> listDate) {
        return listDate.stream()
                .map(date -> new TonResult(date, tonMap.containsKey(date) ? tonMap.get(date).getTongSl() : null))
                .collect(Collectors.toList());
    }

    private void groupDataByInventoryType(List<BieuDoXuTheTonDTO> bieuDoXuTheTonList, List<TonResult> tonThuResult, String inventoryType) {
        bieuDoXuTheTonList.stream()
                .filter(item -> inventoryType.equalsIgnoreCase(item.getLoaiTon()))
                .map(t -> new TonResult(t.getNgayBaoCao(), t.getTongSl()))
                .forEach(tonThuResult::add);
    }
}
