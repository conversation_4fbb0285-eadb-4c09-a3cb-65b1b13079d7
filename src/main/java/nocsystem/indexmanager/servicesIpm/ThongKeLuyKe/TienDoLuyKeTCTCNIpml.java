package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeTCTBuuCucRepository;
import nocsystem.indexmanager.repositories.TienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TienDoluyKeTCTCNService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TienDoLuyKeTCTCNIpml implements TienDoluyKeTCTCNService {

    @Autowired
    private TienDoLuyKeTCTChiNhanhRepository tienDoLuyKeTCTChiNhanhRepository;

    @Autowired
    private TienDoLuyKeTCTBuuCucRepository tienDoLuyKeBuuCucRepo;

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    @Override
    public CustomizeDataPage<TienDoluyKeTCTCNResDto> findTienDoLuyKeTCTCN(String maChiNhanh, String maBuuCuc, LocalDate ngayBaoCao, int pageIndex, int pageSize) {
        Pageable paging = PageRequest.of(pageIndex, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            Page<TienDoluyKeTCTCNResDto> page;
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                page = tienDoLuyKeTCTChiNhanhRepository.findTienDoLuyKeTCTCNV2(
                        ngayBaoCao, paging, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                page = tienDoLuyKeBuuCucRepo.findTienDoLuyKeTheoKeHoach(
                        maChiNhanh, maBuuCuc, paging, ngayBaoCao, UserContext.getUserData().getListBuuCucVeriable());
            }
            return this.convertPaginateDataBC(page);
        } else {
            Page<TienDoluyKeTCTCNResDto> page = tienDoLuyKeTCTChiNhanhRepository.findTienDoLuyKeTCTCN(
                    ngayBaoCao, paging, this.exceptionChiNhanh);
            return this.convertPaginateDataBC(page);
        }
    }

    private CustomizeDataPage<TienDoluyKeTCTCNResDto> convertPaginateDataBC(Page<TienDoluyKeTCTCNResDto> pageResult) {
        ListContentPageDto<TienDoluyKeTCTCNResDto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoluyKeTCTCNResDto> tienDoDoanhThuCNV1ResDtos = new ArrayList<>();
        if (pageResult.getContent().isEmpty()) {
            tienDoDoanhThuCNV1ResDtos.add(new TienDoluyKeTCTCNResDto());
        } else {
            tienDoDoanhThuCNV1ResDtos = pageResult.getContent();
        }

        CustomizeDataPage<TienDoluyKeTCTCNResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoDoanhThuCNV1ResDtos);

        return customizeDataPage;
    }
}
