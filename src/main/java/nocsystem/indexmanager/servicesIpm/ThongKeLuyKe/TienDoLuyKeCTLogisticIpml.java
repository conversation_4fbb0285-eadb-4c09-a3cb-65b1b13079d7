package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeLogistic.TinhTongTienDoLuyKeLogisticDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TienDoLuyKeLogisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public class TienDoLuyKeCTLogisticIpml implements TienDoLuyKeLogisticService {
    @Autowired
    private TongTienDoLuyKeTCTChiNhanhRepository tongLuyKeRepos;

    @Override
    public TinhTongTienDoLuyKeLogisticDto totalLuyKeCTLogistic(LocalDate toTime, String maChiNhanh) {
        /*
         * Xét 2 trường hợp nếu ko truyền lên mã chi nhánh thì trả về all chi nhánh
         * Trường hợp còn lại trả về bưu cục của chi nhánh
         */
        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            TinhTongTienDoLuyKeLogisticDto tongLuyKeMB = tongLuyKeRepos.findTongTienDoLuyKeLogistic(toTime);

            if (tongLuyKeMB != null && UserContext.getUserData().getIsAdmin().equals("true")) {
                tongLuyKeMB.setChiNhanh("Total");
            } else {
                tongLuyKeMB = new TinhTongTienDoLuyKeLogisticDto();
            }

//            if (tongLuyKeMB == null) {
//                tongLuyKeMB = new TinhTongTienDoLuyKeLogisticDto();
//            }

            return tongLuyKeMB;
        }

        /*
         * Hiện tại chỉ show dữ liệu trên màn DashBoard chưa cần show trên màn chi tiết, sau nếu có sẽ bổ sung sau
         */
        return null;
    }
}
