package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.ListDetailLuyKeBuuCucMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.ListDetailLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMNDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMTDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeDetailRepository;
import nocsystem.indexmanager.repositories.TienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TienDoLuyKeMienNamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TienDoLuyKeMienNamServiceIpml implements TienDoLuyKeMienNamService {
    @Autowired
    private TienDoLuyKeTCTChiNhanhRepository luyKeChiNhanhRepo;

    @Autowired
    private TongTienDoLuyKeTCTChiNhanhRepository tongLuyKeRepos;

    @Autowired
    private TienDoLuyKeDetailRepository luyKeDetailRepo;

    @Autowired
    private TongTienDoLuyKeCNHaNoiAndHCMIpml tongTienDoLuyKeIpml;

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    @Override
    public CustomizeDataPage<TinhTongLuyKeChiNhanhMNDto> listDetailLuyKeMN(LocalDate to_time, String ma_chinhanh, String ma_buucuc, int pageIndex, int pageSize) {
        /*
         * Xét 2 trường hợp nếu ko truyền lên mã chi nhánh thì trả về all chi nhánh
         * Trường hợp còn lại trả về bưu cục của chi nhánh
         */
        if (pageIndex > 0) pageIndex--;

        Pageable paging = PageRequest.of(pageIndex, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if ((ma_chinhanh.isEmpty()) && ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Page<TinhTongLuyKeChiNhanhMNDto> result =
                        luyKeChiNhanhRepo.listDetailLuyKeCNMienNamV2(to_time, ma_chinhanh, paging,
                                UserContext.getUserData().getListChiNhanhVeriable());
                return this.listDetailLuyKeMienNam(result);
            }

            if ((ma_chinhanh.isEmpty()) && !ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Page<TinhTongLuyKeChiNhanhMNDto> result =
                        luyKeDetailRepo.findTienDoLuyKeManTongCtyForBuuCuc(to_time, ma_chinhanh, ma_buucuc, paging,
                                UserContext.getUserData().getListBuuCucVeriable());
                return this.listDetailLuyKeMienNam(result);
            }

            Page<TinhTongLuyKeChiNhanhMNDto> result =
                    luyKeDetailRepo.findTienDoLuyKeBCMienNamV2(to_time, ma_chinhanh, ma_buucuc, paging,
                            UserContext.getUserData().getListBuuCucVeriable());
            return this.listDetailLuyKeMienNam(result);
        }

        if (ma_chinhanh.isEmpty() || ma_chinhanh == null) {
            Page<TinhTongLuyKeChiNhanhMNDto> result =
                    luyKeChiNhanhRepo.listDetailLuyKeCNMienNam(to_time, ma_chinhanh, paging, this.exceptionChiNhanh);
            return this.listDetailLuyKeMienNam(result);
        }

        Page<TinhTongLuyKeChiNhanhMNDto> result =
                luyKeDetailRepo.findTienDoLuyKeBCMienNam(to_time, ma_chinhanh, ma_buucuc, paging, this.exceptionChiNhanh);
        return this.listDetailLuyKeMienNam(result);
    }

    @Override
    public TinhTongLuyKeChiNhanhMNDto totalLuyKeCNBCMienNam(LocalDate toTime, String maChiNhanh, String buuCuc) {
        /*
         * Xét 2 trường hợp nếu ko truyền lên mã chi nhánh thì trả về all chi nhánh
         * Trường hợp còn lại trả về bưu cục của chi nhánh
         */
        TinhTongLuyKeChiNhanhMNDto tongLuyKeBuuCuc = new TinhTongLuyKeChiNhanhMNDto();
        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            TinhTongLuyKeChiNhanhMNDto tongLuyKeMB = new TinhTongLuyKeChiNhanhMNDto();
            if (UserContext.getUserData().getIsAdmin().equals("true")) {
                tongLuyKeMB = tongLuyKeRepos.findTongTienDoLuyKeMienNam(toTime);
            }

            if (!UserContext.getUserData().getIsAdmin().equals("true")
                    && ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                List<TienDoluyKeTCTCNResDto> listMultiBranch =
                        luyKeDetailRepo.findTienDoLuyKeBCMienNamMultiBranch(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListChiNhanhVeriable());
                TienDoluyKeTCTCNResDto tongLuyKe = tongTienDoLuyKeIpml.tinhTongLuyKe(listMultiBranch);
                tongLuyKeMB = convertModelToTinhTongLuyKeChiNhanh(tongLuyKe);
            }

            if (!UserContext.getUserData().getIsAdmin().equals("true")
                    && !ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                List<TienDoluyKeTCTCNResDto> listMultiBranch =
                        luyKeDetailRepo.findTienDoLuyKeBCMienNamForBuuCuc(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListBuuCucVeriable());
                TienDoluyKeTCTCNResDto tongLuyKe = tongTienDoLuyKeIpml.tinhTongLuyKe(listMultiBranch);
                tongLuyKeMB = convertModelToTinhTongLuyKeChiNhanh(tongLuyKe);
            }

            //TODO tạm bỏ check isAdmin
            //if (tongLuyKeMB != null && ListVariableLocation.isAdmin.equals("true"))
            if (tongLuyKeMB != null) {
                tongLuyKeMB.setChiNhanh("Total");
            } else {
                tongLuyKeMB = new TinhTongLuyKeChiNhanhMNDto();
            }
            return tongLuyKeMB;
        }

        if (UserContext.getUserData().getIsAdmin().equals("true")) {
            tongLuyKeBuuCuc
                    = luyKeChiNhanhRepo.listTongLuyKeMoiChiNhanhMienNamRuleAdmin(toTime, maChiNhanh);
        } else {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tongLuyKeBuuCuc
                        = luyKeChiNhanhRepo.listTongLuyKeMoiChiNhanhMienNam(toTime, maChiNhanh, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tongLuyKeBuuCuc
                        = luyKeDetailRepo.findLuyKeBCMienNamOnlyOne(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListBuuCucVeriable());
            }
        }

        if (tongLuyKeBuuCuc != null) {
            tongLuyKeBuuCuc.setBuuCuc("Total");
        } else {
            tongLuyKeBuuCuc = new TinhTongLuyKeChiNhanhMNDto();
        }

        return tongLuyKeBuuCuc;
    }

    private CustomizeDataPage<TinhTongLuyKeChiNhanhMNDto> listDetailLuyKeMienNam(
            Page<TinhTongLuyKeChiNhanhMNDto> pageResult) {
        ListContentPageDto<TinhTongLuyKeChiNhanhMNDto> customPage =
                new ListContentPageDto<>(pageResult);

        List<TinhTongLuyKeChiNhanhMNDto> customContentPage = new ArrayList<>();
        if (pageResult.getContent().isEmpty()) {
            customContentPage.add(new TinhTongLuyKeChiNhanhMNDto());
        } else {
            customContentPage = pageResult.getContent();
        }

        CustomizeDataPage<TinhTongLuyKeChiNhanhMNDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) customPage.getTotal());
        customizeDataPage.setOffset((int) customPage.getOffset());
        customizeDataPage.setLimit(customPage.getLimit());
        customizeDataPage.setContent(customContentPage);

        return customizeDataPage;
    }

    private TinhTongLuyKeChiNhanhMNDto convertModelToTinhTongLuyKeChiNhanh(TienDoluyKeTCTCNResDto tongLuyKe) {
        TinhTongLuyKeChiNhanhMNDto tinhTongLuyKeChiNhanhMNDto = new TinhTongLuyKeChiNhanhMNDto();
        tinhTongLuyKeChiNhanhMNDto.setChiNhanh(tongLuyKe.getChiNhanh());
        tinhTongLuyKeChiNhanhMNDto.setKhthang(tongLuyKe.getKeHoach());
        tinhTongLuyKeChiNhanhMNDto.setKeHoachThangDenNgay(tongLuyKe.getKeHoachThangDenNgay());
        tinhTongLuyKeChiNhanhMNDto.setLkthang(tongLuyKe.getLuyKeThang());
        tinhTongLuyKeChiNhanhMNDto.setTlht(tongLuyKe.getTlht());
        tinhTongLuyKeChiNhanhMNDto.setTiLeHoanThanhNgay(tongLuyKe.getTiLeHoanThanhNgay());
        tinhTongLuyKeChiNhanhMNDto.setTienDo(tongLuyKe.getTienDo());
        tinhTongLuyKeChiNhanhMNDto.setTienDoNgay(tongLuyKe.getTienDoNgay());
        tinhTongLuyKeChiNhanhMNDto.setTtThang(tongLuyKe.getTtThang());
        tinhTongLuyKeChiNhanhMNDto.setTtTbnThang(tongLuyKe.getTtTbnThang());
        tinhTongLuyKeChiNhanhMNDto.setTtNam(tongLuyKe.getTtNam());
        tinhTongLuyKeChiNhanhMNDto.setTtTbnNam(tongLuyKe.getTtTbnNam());
        //Phần đánh giá set tạm thời giá trị = 0 rồi tính lại sau
        tinhTongLuyKeChiNhanhMNDto.setDanhgia(0);
        tinhTongLuyKeChiNhanhMNDto.setTiLeHoanThanhThang(tongLuyKe.getTlht());
        tinhTongLuyKeChiNhanhMNDto.setLkthang(tongLuyKe.getLuyKeThang());
        tinhTongLuyKeChiNhanhMNDto.setKhthang(tongLuyKe.getKeHoach());
        tinhTongLuyKeChiNhanhMNDto.setThucHienNgay(tongLuyKe.getThucHienNgay());

        return tinhTongLuyKeChiNhanhMNDto;
    }
}
