package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMNDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TongTienDoLuyKeTCTCNResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeDetailRepository;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TongTienDoluyKeTCTCNService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TongTienDoLuyKeTCTCNIpml implements TongTienDoluyKeTCTCNService {

    @Autowired
    private TongTienDoLuyKeTCTChiNhanhRepository tienDoLuyKeTCTChiNhanhRepository;

    @Autowired
    private TienDoLuyKeDetailRepository luyKeDetailRepo;

    @Autowired
    private TongTienDoLuyKeCNHaNoiAndHCMIpml tongTienDoLuyKeIpml;

    @Override
    public List<TongTienDoLuyKeTCTCNResDto> findTongTienDoLuyKeTCTCN(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) {
        List<TongTienDoLuyKeTCTCNResDto> tongTDLuyKe = new ArrayList<>();
        if (UserContext.getUserData().getIsAdmin().equals("true")) {
            tongTDLuyKe = tienDoLuyKeTCTChiNhanhRepository.findTongTienDoLuyKeTCTCN(ngayBaoCao);
        } else {
            List<TienDoluyKeTCTCNResDto> listMultiBranch;
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                listMultiBranch = luyKeDetailRepo.findTienDoLuyKeManTongCtyMultiBranch(
                        ngayBaoCao, maChiNhanh, maBuuCuc, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                listMultiBranch = luyKeDetailRepo.findTienDoLuyKeKeHoachTongCtyBC(
                        ngayBaoCao, maChiNhanh, maBuuCuc, UserContext.getUserData().getListBuuCucVeriable());
            }

            TienDoluyKeTCTCNResDto tongLuyKe = tongTienDoLuyKeIpml.tinhTongLuyKe(listMultiBranch);
            TongTienDoLuyKeTCTCNResDto convertTongLuyKe = convertModelToTinhTongLuyKeChiNhanh(tongLuyKe);
            tongTDLuyKe.add(convertTongLuyKe);
        }

        if (tongTDLuyKe.isEmpty()) {
            tongTDLuyKe = new ArrayList<>();
        }

        return tongTDLuyKe;
    }

    private TongTienDoLuyKeTCTCNResDto convertModelToTinhTongLuyKeChiNhanh(TienDoluyKeTCTCNResDto tongLuyKe) {
        TongTienDoLuyKeTCTCNResDto tinhTongLuyKeChiNhanh = new TongTienDoLuyKeTCTCNResDto();
        tinhTongLuyKeChiNhanh.setKeHoach(tongLuyKe.getKeHoach());
        tinhTongLuyKeChiNhanh.setKeHoachThangDenNgay(tongLuyKe.getKeHoachThangDenNgay());
        tinhTongLuyKeChiNhanh.setLuyKeThang(tongLuyKe.getLuyKeThang());
        tinhTongLuyKeChiNhanh.setTlht(tongLuyKe.getTlht());
        tinhTongLuyKeChiNhanh.setTiLeHoanThanhNgay(tongLuyKe.getTiLeHoanThanhNgay());
        tinhTongLuyKeChiNhanh.setTienDo(tongLuyKe.getTienDo());
        tinhTongLuyKeChiNhanh.setTienDoNgay(tongLuyKe.getTienDoNgay());
        tinhTongLuyKeChiNhanh.setTtThang(tongLuyKe.getTtThang());
        tinhTongLuyKeChiNhanh.setTtTbnThang(tongLuyKe.getTtTbnThang());
        tinhTongLuyKeChiNhanh.setTtNam(tongLuyKe.getTtNam());
        tinhTongLuyKeChiNhanh.setTtTbnNam(tongLuyKe.getTtTbnNam());
        //Phần đánh giá set tạm thời giá trị = 0 rồi tính lại sau
        tinhTongLuyKeChiNhanh.setDanhgia(0);

        return tinhTongLuyKeChiNhanh;
    }
}
