package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTBCResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeTCTBuuCucRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TienDoluyKeTCTBuuCucService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TienDoLuyKeTCTBCIpml implements TienDoluyKeTCTBuuCucService {

    @Autowired
    private TienDoLuyKeTCTBuuCucRepository tienDoLuyKeTCTBuuCucRepository;

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    @Override
    public CustomizeDataPage<TienDoluyKeTCTBCResDto> findTienDoLuyKeTCTBC(String chiNhanh, String buuCuc, LocalDate ngayBaoCao, int pageIndex, int pageSize) {
        Pageable paging = PageRequest.of(pageIndex, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            Page<TienDoluyKeTCTBCResDto> page = tienDoLuyKeTCTBuuCucRepository.findTienDoLuyKeTCTBCV2(
                    chiNhanh, buuCuc, ngayBaoCao, paging, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginateDataBC(page);
        } else {
            Page<TienDoluyKeTCTBCResDto> page = tienDoLuyKeTCTBuuCucRepository.findTienDoLuyKeTCTBC(
                    chiNhanh, buuCuc, ngayBaoCao, paging, this.exceptionChiNhanh);
            return this.convertPaginateDataBC(page);
        }
    }

    private CustomizeDataPage<TienDoluyKeTCTBCResDto> convertPaginateDataBC(Page<TienDoluyKeTCTBCResDto> pageResult) {
        ListContentPageDto<TienDoluyKeTCTBCResDto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoluyKeTCTBCResDto> tienDoluyKeTCTBCResDto = new ArrayList<>();
        if (pageResult.getContent().isEmpty()) {
            tienDoluyKeTCTBCResDto.add(new TienDoluyKeTCTBCResDto());
        } else {
            tienDoluyKeTCTBCResDto = pageResult.getContent();
        }

        CustomizeDataPage<TienDoluyKeTCTBCResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoluyKeTCTBCResDto);

        return customizeDataPage;
    }
}
