package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeTCTBuuCucRepository;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TongTienDoluyKeTCTBCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TongTienDoLuyKeTCTBCIpml implements TongTienDoluyKeTCTBCService {

    @Autowired
    private TongTienDoLuyKeTCTBuuCucRepository tongTienDoLuyKeTCTBuuCucRepository;

    @Override
    public List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeTCTBC(String maChiNhanh, String maBuuCuc, LocalDate ngayBaoCao) {
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            List<TienDoluyKeTCTCNResDto> tienDoLKTCTBuuCuc = new ArrayList<>();
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tienDoLKTCTBuuCuc =
                        tongTienDoLuyKeTCTBuuCucRepository.findTongTienDoLuyKeTCTBCV2(maChiNhanh, ngayBaoCao, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tienDoLKTCTBuuCuc =
                        tongTienDoLuyKeTCTBuuCucRepository.findTongTienDoLuyKeTCTRoleBuuCuc(maChiNhanh, maBuuCuc, ngayBaoCao, UserContext.getUserData().getListBuuCucVeriable());
            }

            if (tienDoLKTCTBuuCuc.isEmpty()) {
                tienDoLKTCTBuuCuc = new ArrayList<>();
            }

            return tienDoLKTCTBuuCuc;
        }

        List<TienDoluyKeTCTCNResDto> tienDoLKTCTBuuCuc =
                tongTienDoLuyKeTCTBuuCucRepository.findTongTienDoLuyKeTCTBC(maChiNhanh, ngayBaoCao);
        if (tienDoLKTCTBuuCuc.isEmpty()) {
            tienDoLKTCTBuuCuc = new ArrayList<>();
        }

        return tienDoLKTCTBuuCuc;
    }
}
