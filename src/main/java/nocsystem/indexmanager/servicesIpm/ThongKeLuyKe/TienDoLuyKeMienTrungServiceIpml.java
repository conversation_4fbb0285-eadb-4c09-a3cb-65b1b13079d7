package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.ListDetailLuyKeBuuCucMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.ListDetailLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMTDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeDetailRepository;
import nocsystem.indexmanager.repositories.TienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TienDoLuyKeMienTrungService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TienDoLuyKeMienTrungServiceIpml implements TienDoLuyKeMienTrungService {
    @Autowired
    private TongTienDoLuyKeTCTChiNhanhRepository tongLuyKeRepos;

    @Autowired
    private TienDoLuyKeTCTChiNhanhRepository luyKeChiNhanhRepo;

    @Autowired
    private TienDoLuyKeDetailRepository luyKeDetailRepo;

    @Autowired
    private TongTienDoLuyKeCNHaNoiAndHCMIpml tongTienDoLuyKeIpml;

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    @Override
    public TinhTongLuyKeChiNhanhMTDto totalLuyKeCNBCMienTrung(LocalDate toTime, String maChiNhanh, String buuCuc) {
        /*
         * Xét 2 trường hợp nếu ko truyền lên mã chi nhánh thì trả về all chi nhánh
         * Trường hợp còn lại trả về bưu cục của chi nhánh
         */
        TinhTongLuyKeChiNhanhMTDto tongLuyKeBuuCuc = new TinhTongLuyKeChiNhanhMTDto();

        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            TinhTongLuyKeChiNhanhMTDto tongLuyKeMB = new TinhTongLuyKeChiNhanhMTDto();

            if (UserContext.getUserData().getIsAdmin().equals("true")) {
                tongLuyKeMB = tongLuyKeRepos.findTongTienDoLuyKeMienTrung(toTime);
            }

            if (!UserContext.getUserData().getIsAdmin().equals("true") && ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                List<TienDoluyKeTCTCNResDto> listMultiBranch =
                        luyKeDetailRepo.findTienDoLuyKeBCMienNamMultiBranch(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListChiNhanhVeriable());
                TienDoluyKeTCTCNResDto tongLuyKe = tongTienDoLuyKeIpml.tinhTongLuyKe(listMultiBranch);
                tongLuyKeMB = convertModelToTinhTongLuyKeChiNhanh(tongLuyKe);
            }

            if (!UserContext.getUserData().getIsAdmin().equals("true") && !ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                List<TienDoluyKeTCTCNResDto> listMultiBranch =
                        luyKeDetailRepo.findTienDoLuyKeBCMienTrungDetail(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListBuuCucVeriable());
                TienDoluyKeTCTCNResDto tongLuyKe = tongTienDoLuyKeIpml.tinhTongLuyKe(listMultiBranch);
                tongLuyKeMB = convertModelToTinhTongLuyKeChiNhanh(tongLuyKe);
            }

            if (tongLuyKeMB != null) {
                tongLuyKeMB.setChiNhanh("Total");
            } else {
                tongLuyKeMB = new TinhTongLuyKeChiNhanhMTDto();
            }
            return tongLuyKeMB;
        }

        if (UserContext.getUserData().getIsAdmin().equals("true")) {
            tongLuyKeBuuCuc
                    = luyKeChiNhanhRepo.listTongLuyKeMoiChiNhanhMienTrungRoleAdin(toTime, maChiNhanh);
        } else {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tongLuyKeBuuCuc
                        = luyKeChiNhanhRepo.listTongLuyKeMoiChiNhanhMienTrung(toTime, maChiNhanh, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tongLuyKeBuuCuc
                        = luyKeDetailRepo.findLuyKeBCMienTrungOnlyOne(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListBuuCucVeriable());
            }
        }

        if (tongLuyKeBuuCuc != null) {
            tongLuyKeBuuCuc.setBuuCuc("Total");
        }

        if (tongLuyKeBuuCuc == null) {
            tongLuyKeBuuCuc = new TinhTongLuyKeChiNhanhMTDto();
        }

        return tongLuyKeBuuCuc;
    }

    @Override
    public CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeMT(LocalDate to_time, String ma_chinhanh, String ma_buucuc, int pageIndex, int pageSize) {
        /*
         * Xét 2 trường hợp nếu ko truyền lên mã chi nhánh thì trả về all chi nhánh
         * Trường hợp còn lại trả về bưu cục của chi nhánh
         */
        if (pageIndex > 0) pageIndex--;

        Pageable paging = PageRequest.of(pageIndex, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ma_chinhanh.isEmpty() && ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Page<ListDetailLuyKeChiNhanhMBDto> result =
                        luyKeChiNhanhRepo.listDetailLuyKeCNMTV2(to_time, ma_chinhanh, paging, UserContext.getUserData().getListChiNhanhVeriable());
                return this.listDetailLuyKeMT(result);
            }

            if (ma_chinhanh.isEmpty() && !ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Page<ListDetailLuyKeChiNhanhMBDto> result =
                        luyKeDetailRepo.findTienDoLuyKeBCMienTrungDetailV2(to_time, ma_chinhanh, ma_buucuc, paging, UserContext.getUserData().getListBuuCucVeriable());
                return this.listDetailLuyKeMT(result);
            }

            Page<ListDetailLuyKeChiNhanhMBDto> result = luyKeDetailRepo.findTienDoLuyKeBCMienTrungV2(
                    to_time, ma_chinhanh, ma_buucuc, paging, UserContext.getUserData().getListBuuCucVeriable());
            return this.listDetailLuyKeMT(result);
        }

        if (ma_chinhanh.isEmpty() || ma_chinhanh == null) {
            Page<ListDetailLuyKeChiNhanhMBDto> result =
                    luyKeChiNhanhRepo.listDetailLuyKeCNMT(to_time, ma_chinhanh, paging, this.exceptionChiNhanh);
            return this.listDetailLuyKeMT(result);
        }

        Page<ListDetailLuyKeChiNhanhMBDto> result = luyKeDetailRepo.findTienDoLuyKeBCMienTrung(
                to_time, ma_chinhanh, ma_buucuc, paging, this.exceptionChiNhanh);
        return this.listDetailLuyKeMT(result);
    }

    private CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeMT(
            Page<ListDetailLuyKeChiNhanhMBDto> pageResult) {
        ListContentPageDto<ListDetailLuyKeChiNhanhMBDto> customPage =
                new ListContentPageDto<>(pageResult);

        List<ListDetailLuyKeChiNhanhMBDto> customContentPage = new ArrayList<>();
        if (pageResult.getContent().isEmpty()) {
            customContentPage.add(new ListDetailLuyKeChiNhanhMBDto());
        } else {
            customContentPage = pageResult.getContent();
        }

        CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) customPage.getTotal());
        customizeDataPage.setOffset((int) customPage.getOffset());
        customizeDataPage.setLimit(customPage.getLimit());
        customizeDataPage.setContent(customContentPage);

        return customizeDataPage;
    }

    private TinhTongLuyKeChiNhanhMTDto convertModelToTinhTongLuyKeChiNhanh(TienDoluyKeTCTCNResDto tongLuyKe) {
        TinhTongLuyKeChiNhanhMTDto tinhTongLuyKeChiNhanhMTDto = new TinhTongLuyKeChiNhanhMTDto();
        tinhTongLuyKeChiNhanhMTDto.setChiNhanh(tongLuyKe.getChiNhanh());
        tinhTongLuyKeChiNhanhMTDto.setKhthang(tongLuyKe.getKeHoach());
        tinhTongLuyKeChiNhanhMTDto.setKeHoachThangDenNgay(tongLuyKe.getKeHoachThangDenNgay());
        tinhTongLuyKeChiNhanhMTDto.setLkthang(tongLuyKe.getLuyKeThang());
        tinhTongLuyKeChiNhanhMTDto.setTlht(tongLuyKe.getTlht());
        tinhTongLuyKeChiNhanhMTDto.setTiLeHoanThanhNgay(tongLuyKe.getTiLeHoanThanhNgay());
        tinhTongLuyKeChiNhanhMTDto.setTienDo(tongLuyKe.getTienDo());
        tinhTongLuyKeChiNhanhMTDto.setTienDoNgay(tongLuyKe.getTienDoNgay());
        tinhTongLuyKeChiNhanhMTDto.setTtThang(tongLuyKe.getTtThang());
        tinhTongLuyKeChiNhanhMTDto.setTtTbnThang(tongLuyKe.getTtTbnThang());
        tinhTongLuyKeChiNhanhMTDto.setTtNam(tongLuyKe.getTtNam());
        tinhTongLuyKeChiNhanhMTDto.setTtTbnNam(tongLuyKe.getTtTbnNam());
        //Phần đánh giá set tạm thời giá trị = 0 rồi tính lại sau
        tinhTongLuyKeChiNhanhMTDto.setDanhgia(0);

        return tinhTongLuyKeChiNhanhMTDto;
    }
}
