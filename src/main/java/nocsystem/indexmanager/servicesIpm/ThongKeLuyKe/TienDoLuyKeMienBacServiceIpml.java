package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.*;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeCNHaNoiAndHCMResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeDetailRepository;
import nocsystem.indexmanager.repositories.TienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeTCTChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TienDoLuyKeMienBacService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TienDoLuyKeMienBacServiceIpml implements TienDoLuyKeMienBacService {
    @Autowired
    private TienDoLuyKeTCTChiNhanhRepository luyKeChiNhanhRepo;

    @Autowired
    private TienDoLuyKeDetailRepository luyKeDetailRepo;

    @Autowired
    private TongTienDoLuyKeTCTChiNhanhRepository tongLuyKeRepos;

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    @Autowired
    private TongTienDoLuyKeCNHaNoiAndHCMIpml tongTienDoLuyKeIpml;

    @Override
    public CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> findListDetailLuyKeMB(LocalDate toTime, String maChiNhanh, String maBuuCuc, int pageIndex, int pageSize) {
        if (pageIndex > 0) pageIndex--;

        Pageable paging = PageRequest.of(pageIndex, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (maChiNhanh.isEmpty() && ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Page<ListDetailLuyKeChiNhanhMBDto> result = luyKeChiNhanhRepo.listDetailLuyKeBCMBV2(toTime, maChiNhanh, paging, UserContext.getUserData().getListChiNhanhVeriable());
                return this.convertPaginatefindListDetailLuyKeMB(result);
            }

            if (maChiNhanh.isEmpty() && !ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Page<ListDetailLuyKeChiNhanhMBDto> result = luyKeDetailRepo.findTienDoLuyKeBCMienBacDetailV2(toTime, maChiNhanh, maBuuCuc, UserContext.getUserData().getListBuuCucVeriable(), paging);
                return this.convertPaginatefindListDetailLuyKeMB(result);
            }

            Page<ListDetailLuyKeChiNhanhMBDto> result = luyKeDetailRepo.findTienDoLuyKeCNMienBacV2(toTime, maChiNhanh, maBuuCuc, paging, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginatefindListDetailLuyKeMB(result);
        }

        if (maChiNhanh.isEmpty() || maChiNhanh == null) {
            Page<ListDetailLuyKeChiNhanhMBDto> result = luyKeChiNhanhRepo.listDetailLuyKeBCMB(toTime, maChiNhanh, paging, this.exceptionChiNhanh);
            return this.convertPaginatefindListDetailLuyKeMB(result);
        }

        Page<ListDetailLuyKeChiNhanhMBDto> result = luyKeDetailRepo.findTienDoLuyKeCNMienBac(toTime, maChiNhanh, maBuuCuc, paging, this.exceptionChiNhanh);
        return this.convertPaginatefindListDetailLuyKeMB(result);
    }

    @Override
    public TinhTongLuyKeChiNhanhMBDto totalLuyKeCNBCMienBac(LocalDate toTime, String maChiNhanh, String buuCuc) {
        /*
         * Xét 2 trường hợp nếu ko truyền lên mã chi nhánh thì trả về all chi nhánh
         * Trường hợp còn lại trả về bưu cục của chi nhánh
         */
        TinhTongLuyKeChiNhanhMBDto tongLuyKeBuuCucThuocChiNhanh = new TinhTongLuyKeChiNhanhMBDto();

        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            TinhTongLuyKeChiNhanhMBDto tongLuyKeMB = new TinhTongLuyKeChiNhanhMBDto();
            if (UserContext.getUserData().getIsAdmin().equals("true")) {
                tongLuyKeMB = tongLuyKeRepos.findTongTienDoLuyKeMienBac(toTime);
            }

            if (!UserContext.getUserData().getIsAdmin().equals("true") && ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                List<TienDoluyKeTCTCNResDto> listMultiBranch = luyKeDetailRepo.findTienDoLuyKeBCMienTrungMultiBranch(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListChiNhanhVeriable());
                TienDoluyKeTCTCNResDto tongLuyKe = tongTienDoLuyKeIpml.tinhTongLuyKe(listMultiBranch);
                tongLuyKeMB = convertModelToTinhTongLuyKeChiNhanh(tongLuyKe);
            }

            if (!UserContext.getUserData().getIsAdmin().equals("true") && !ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                List<TienDoluyKeTCTCNResDto> listMultiBranch = luyKeDetailRepo.findTienDoLuyKeBCMienBacDetail(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListBuuCucVeriable());
                TienDoluyKeTCTCNResDto tongLuyKe = tongTienDoLuyKeIpml.tinhTongLuyKe(listMultiBranch);
                tongLuyKeMB = convertModelToTinhTongLuyKeChiNhanh(tongLuyKe);
            }

//            if (tongLuyKeMB != null && ListVariableLocation.isAdmin.equals("true")) {
            if (tongLuyKeMB != null) {
                tongLuyKeMB.setChiNhanh("Total");
            } else {
                tongLuyKeMB = new TinhTongLuyKeChiNhanhMBDto();
            }
            return tongLuyKeMB;
        }

        if(UserContext.getUserData().getIsAdmin().equals("true")) {
            tongLuyKeBuuCucThuocChiNhanh = luyKeChiNhanhRepo.listTongLuyKeMoiChiNhanhMBRoleAdmin(toTime, maChiNhanh);
        } else {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tongLuyKeBuuCucThuocChiNhanh = luyKeChiNhanhRepo.listTongLuyKeMoiChiNhanhMB(toTime, maChiNhanh, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tongLuyKeBuuCucThuocChiNhanh
                        = luyKeDetailRepo.findLuyKeBCMienBacOnlyOne(toTime, maChiNhanh, buuCuc, UserContext.getUserData().getListBuuCucVeriable());
            }
        }

        if (tongLuyKeBuuCucThuocChiNhanh != null) {
            tongLuyKeBuuCucThuocChiNhanh.setBuuCuc("Total");
        }

        if (tongLuyKeBuuCucThuocChiNhanh == null) {
            tongLuyKeBuuCucThuocChiNhanh = new TinhTongLuyKeChiNhanhMBDto();
        }

        return tongLuyKeBuuCucThuocChiNhanh;
    }

    private CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> convertPaginatefindListDetailLuyKeMB(Page<ListDetailLuyKeChiNhanhMBDto> pageResult) {
        ListContentPageDto<ListDetailLuyKeChiNhanhMBDto> customPage = new ListContentPageDto<>(pageResult);

        List<ListDetailLuyKeChiNhanhMBDto> customContentPage = new ArrayList<>();
        if (pageResult.getContent().isEmpty()) {
            customContentPage.add(new ListDetailLuyKeChiNhanhMBDto());
        } else {
            customContentPage = pageResult.getContent();
        }

        CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) customPage.getTotal());
        customizeDataPage.setOffset((int) customPage.getOffset());
        customizeDataPage.setLimit(customPage.getLimit());
        customizeDataPage.setContent(customContentPage);

        return customizeDataPage;
    }

    private TinhTongLuyKeChiNhanhMBDto convertModelToTinhTongLuyKeChiNhanh(TienDoluyKeTCTCNResDto tongLuyKe) {
        TinhTongLuyKeChiNhanhMBDto tinhTongLuyKeChiNhanhMBDto = new TinhTongLuyKeChiNhanhMBDto();
        tinhTongLuyKeChiNhanhMBDto.setChiNhanh(tongLuyKe.getChiNhanh());
        tinhTongLuyKeChiNhanhMBDto.setKhthang(tongLuyKe.getKeHoach());
        tinhTongLuyKeChiNhanhMBDto.setKeHoachThangDenNgay(tongLuyKe.getKeHoachThangDenNgay());
        tinhTongLuyKeChiNhanhMBDto.setLkthang(tongLuyKe.getLuyKeThang());
        tinhTongLuyKeChiNhanhMBDto.setTlht(tongLuyKe.getTlht());
        tinhTongLuyKeChiNhanhMBDto.setTiLeHoanThanhNgay(tongLuyKe.getTiLeHoanThanhNgay());
        tinhTongLuyKeChiNhanhMBDto.setTienDo(tongLuyKe.getTienDo());
        tinhTongLuyKeChiNhanhMBDto.setTienDoNgay(tongLuyKe.getTienDoNgay());
        tinhTongLuyKeChiNhanhMBDto.setTtThang(tongLuyKe.getTtThang());
        tinhTongLuyKeChiNhanhMBDto.setTtTbnThang(tongLuyKe.getTtTbnThang());
        tinhTongLuyKeChiNhanhMBDto.setTtNam(tongLuyKe.getTtNam());
        tinhTongLuyKeChiNhanhMBDto.setTtTbnNam(tongLuyKe.getTtTbnNam());
        tinhTongLuyKeChiNhanhMBDto.setThucHienNgay(tongLuyKe.getThucHienNgay());
        //Phần đánh giá set tạm thời giá trị = 0 rồi tính lại sau
        tinhTongLuyKeChiNhanhMBDto.setDanhgia(0);

        return tinhTongLuyKeChiNhanhMBDto;
    }
}
