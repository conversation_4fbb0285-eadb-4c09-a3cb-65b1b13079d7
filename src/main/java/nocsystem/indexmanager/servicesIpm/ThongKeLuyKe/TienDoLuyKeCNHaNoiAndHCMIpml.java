package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeCNHaNoiAndHCMResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTBCResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeCNHaNoiAndHCMRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TienDoluyKeCNHaNoiAndHCMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class TienDoLuyKeCNHaNoiAndHCMIpml implements TienDoluyKeCNHaNoiAndHCMService {

    @Autowired
    private TienDoLuyKeCNHaNoiAndHCMRepository tienDoLuyKeCNHaNoiAndHCMRepository;

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    @Override
    public CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHaNoi(LocalDate ngayBaoCao, String buuCuc, int pageIndex, int pageSize) {
        Pageable paging = PageRequest.of(pageIndex, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            Page<TienDoluyKeCNHaNoiAndHCMResDto> page = tienDoLuyKeCNHaNoiAndHCMRepository.findTienDoLuyKeCNHaNoiV2(
                    ngayBaoCao, buuCuc, paging, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginateDatafindTienDoLuyKeCNHaNoi(page);
        }

        Page<TienDoluyKeCNHaNoiAndHCMResDto> page = tienDoLuyKeCNHaNoiAndHCMRepository.findTienDoLuyKeCNHaNoi(
                ngayBaoCao, buuCuc, paging);
        return this.convertPaginateDatafindTienDoLuyKeCNHaNoi(page);
    }

    @Override
    public CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHCM(LocalDate ngayBaoCao, String buuCuc, int pageIndex, int pageSize) {
        Pageable paging = PageRequest.of(pageIndex, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            Page<TienDoluyKeCNHaNoiAndHCMResDto> page = tienDoLuyKeCNHaNoiAndHCMRepository.findTienDoLuyKeCNHCMV2(
                    ngayBaoCao, buuCuc, paging, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginateDatafindTienDoLuyKeCNHaNoi(page);
        }

        Page<TienDoluyKeCNHaNoiAndHCMResDto> page = tienDoLuyKeCNHaNoiAndHCMRepository.findTienDoLuyKeCNHCM(
                ngayBaoCao, buuCuc, paging);
        return this.convertPaginateDatafindTienDoLuyKeCNHaNoi(page);
    }

    private CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> convertPaginateDatafindTienDoLuyKeCNHaNoi(
            Page<TienDoluyKeCNHaNoiAndHCMResDto> pageResult) {
        ListContentPageDto<TienDoluyKeCNHaNoiAndHCMResDto> customPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoluyKeCNHaNoiAndHCMResDto> customContentPage = new ArrayList<>();
        if (pageResult.getContent().isEmpty()) {
            customContentPage.add(new TienDoluyKeCNHaNoiAndHCMResDto());
        } else {
            customContentPage = pageResult.getContent();
        }

        CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) customPage.getTotal());
        customizeDataPage.setOffset((int) customPage.getOffset());
        customizeDataPage.setLimit(customPage.getLimit());
        customizeDataPage.setContent(customContentPage);

        return customizeDataPage;
    }
}
