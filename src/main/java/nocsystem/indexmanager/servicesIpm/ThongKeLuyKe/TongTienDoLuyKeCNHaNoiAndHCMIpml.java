package nocsystem.indexmanager.servicesIpm.ThongKeLuyKe;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TongTienDoLuyKeTCTCNResDto;
import nocsystem.indexmanager.repositories.TienDoLuyKeTCTBuuCucRepository;
import nocsystem.indexmanager.repositories.TongTienDoLuyKeCNHaNoiAndHCMRepository;
import nocsystem.indexmanager.services.ThongKeLuyKe.TongTienDoLuyKeCNHaNoiAndHCMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

@Service
public class TongTienDoLuyKeCNHaNoiAndHCMIpml implements TongTienDoLuyKeCNHaNoiAndHCMService {

    @Autowired
    private TongTienDoLuyKeCNHaNoiAndHCMRepository tongTienDoLuyKeCNHaNoiAndHCMRepository;

    @Autowired
    private TienDoLuyKeTCTBuuCucRepository tienDoLuyKeTCTBuuCucRepo;

    @Override
    public List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHaNoi(LocalDate ngayBaoCao, String chiNhanh, String maBuuCuc) {
        List<TienDoluyKeTCTCNResDto> tongTDLuyKeCNHaNoi = new ArrayList<>();
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tongTDLuyKeCNHaNoi =
                        tongTienDoLuyKeCNHaNoiAndHCMRepository.findTongTienDoLuyKeCNHaNoiV2(ngayBaoCao, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tongTDLuyKeCNHaNoi =
                        tienDoLuyKeTCTBuuCucRepo.findTienDoLuyKeTCTBCChiNhanhHN(maBuuCuc, ngayBaoCao, UserContext.getUserData().getListBuuCucVeriable());
            }
        } else {
            tongTDLuyKeCNHaNoi =
                    tongTienDoLuyKeCNHaNoiAndHCMRepository.findTongTienDoLuyKeCNHaNoi(ngayBaoCao);
        }

        if (tongTDLuyKeCNHaNoi.isEmpty()) {
            tongTDLuyKeCNHaNoi = new ArrayList<>();
        }

        return tongTDLuyKeCNHaNoi;
    }

    public TienDoluyKeTCTCNResDto tinhTongLuyKe(List<TienDoluyKeTCTCNResDto> listAllTienDoLuyKe) {
        Float lkThang = Float.valueOf(0);
        Float khThang = Float.valueOf(0);
        Float khDenNgay = Float.valueOf(0);
        Float thangTruoc = Float.valueOf(0);
        Float namTruoc = Float.valueOf(0);
        Float cungKyNgay = Float.valueOf(0);
        Float cungKyThang = Float.valueOf(0);
        Float cungKyNam = Float.valueOf(0);
        Float thucHienNgay = Float.valueOf(0);
        Float ngayQuyDoiThang = Float.valueOf(0);
        Float heSoNgay = Float.valueOf(0);
        TienDoluyKeTCTCNResDto tong = new TienDoluyKeTCTCNResDto();
        Set<Float> keHoachChiNhanh = new HashSet<>();

        for (TienDoluyKeTCTCNResDto tienDoluyKeTCTCNResDto : listAllTienDoLuyKe) {
            if (tienDoluyKeTCTCNResDto.getKeHoach() != null) {
                keHoachChiNhanh.add(tienDoluyKeTCTCNResDto.getKeHoach());
            }
            khDenNgay = tienDoluyKeTCTCNResDto.getKeHoachThangDenNgay() == null ? 0 : tienDoluyKeTCTCNResDto.getKeHoachThangDenNgay();
            cungKyNgay = tienDoluyKeTCTCNResDto.getCungKyNgay() == null ? 0 : tienDoluyKeTCTCNResDto.getCungKyNgay();
            cungKyThang = tienDoluyKeTCTCNResDto.getCungKyThang() == null ? 0 : tienDoluyKeTCTCNResDto.getCungKyThang();
            cungKyNam = tienDoluyKeTCTCNResDto.getCungKyNam() == null ? 0 : tienDoluyKeTCTCNResDto.getCungKyNam();
            ngayQuyDoiThang = tienDoluyKeTCTCNResDto.getNgayQuyDoiThang() == null ? 0 : tienDoluyKeTCTCNResDto.getNgayQuyDoiThang();
            heSoNgay = tienDoluyKeTCTCNResDto.getHeSoNgay() == null ? 0 : tienDoluyKeTCTCNResDto.getHeSoNgay();
            Float lkThangItem = tienDoluyKeTCTCNResDto.getLuyKeThang() == null ? 0 : tienDoluyKeTCTCNResDto.getLuyKeThang();
            lkThang += lkThangItem;

            Float namTruocItem = tienDoluyKeTCTCNResDto.getNamTruoc() == null ? 0 : tienDoluyKeTCTCNResDto.getNamTruoc();
            namTruoc += namTruocItem;

            Float thucHienNgayItem = tienDoluyKeTCTCNResDto.getThucHienNgay() == null ? 0 : tienDoluyKeTCTCNResDto.getThucHienNgay();
            thucHienNgay += thucHienNgayItem;

            Float thangTruocItem = tienDoluyKeTCTCNResDto.getThangTruoc() == null ? 0 : tienDoluyKeTCTCNResDto.getThangTruoc();
            thangTruoc += thangTruocItem;
        }

        Float sumAllKeHoach = keHoachChiNhanh.stream().reduce((float) 0, Float::sum);
        khThang = sumAllKeHoach;
        tong.setKeHoach(sumAllKeHoach);
        tong.setLuyKeThang(lkThang);

        if (ngayQuyDoiThang != 0) {
            tong.setKeHoachThangDenNgay((khThang / ngayQuyDoiThang) * cungKyNgay);
        }
        if (khThang != 0) {
            /*Tính tỉ lệ hoàn thành tháng */
            Float tiLeHoanThanhThang = lkThang * 100 / khThang;
            tong.setTlht(tiLeHoanThanhThang);
        }

        if (thangTruoc != 0)
            tong.setTtThang((lkThang / thangTruoc - 1) * 100);

        if (cungKyNgay != 0 && cungKyThang != 0) {
            Float trungBinhNgayThangNay = thucHienNgay / cungKyThang;
            Float trungBinhNgayThangTruoc = lkThang / cungKyNgay;
            tong.setTtTbnThang(((trungBinhNgayThangNay / trungBinhNgayThangTruoc) - 1) * 100);
        }

        if (namTruoc != 0)
            tong.setTtNam((lkThang / namTruoc - 1) * 100);

        if (cungKyNgay != 0 && cungKyNam != 0) {
            Float trungBinhNgayNamNay = lkThang / cungKyNgay;
            Float trungBinhNgayNamTruoc = namTruoc / cungKyNam;
            if(trungBinhNgayNamTruoc != 0) {
                tong.setTtTbnNam(((trungBinhNgayNamNay / trungBinhNgayNamTruoc) - 1) * 100);
            }
        }

        /*Tính tiến độ đến ngày = Lũy kế - Kế hoạch đến ngày */
        tong.setTienDo(lkThang - khDenNgay);

        /*Tính tỉ lệ hoàn thành ngày = Lũy kế tháng / kế hoạch đến ngày */
        if (khDenNgay != 0) {
            tong.setTiLeHoanThanhNgay(lkThang * 100 / khDenNgay);
        }

        /*TÍnh tiến độ ngày = Thực hiện ngày (a) - kế hoạch ngày (b)
         * Trong đó:
         * + (a) được lấy từ cột th_ngay
         * + (b) = kế hoaạch tháng * hs / ngày quy đổi tháng */
        if (ngayQuyDoiThang != 0) {
            Float keHoachNgay = khThang * heSoNgay / ngayQuyDoiThang;
            tong.setTienDoNgay(thucHienNgay - keHoachNgay);
        }

        /* Gán Thực hiện ngày */
        tong.setThucHienNgay(thucHienNgay);

        return tong;
    }

    @Override
    public List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHCM(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) {
        List<TienDoluyKeTCTCNResDto> tongTDLKCNHCM = new ArrayList<>();
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (maChiNhanh.isEmpty() && ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tongTDLKCNHCM =
                        tienDoLuyKeTCTBuuCucRepo.findTienDoLuyKeTCTBCDetail(maChiNhanh, maBuuCuc, ngayBaoCao, UserContext.getUserData().getListChiNhanhVeriable());
            }

            if (maChiNhanh.isEmpty() && !ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tongTDLKCNHCM =
                        tienDoLuyKeTCTBuuCucRepo.findTienDoLuyKeTCTBC("HCM", maBuuCuc, ngayBaoCao, UserContext.getUserData().getListBuuCucVeriable());
            }
        } else {
            tongTDLKCNHCM =
                    tongTienDoLuyKeCNHaNoiAndHCMRepository.findTongTienDoLuyKeCNHCM(ngayBaoCao);
        }

        if (tongTDLKCNHCM.isEmpty()) {
            tongTDLKCNHCM = new ArrayList<>();
        }

        return tongTDLKCNHCM;
    }
}
