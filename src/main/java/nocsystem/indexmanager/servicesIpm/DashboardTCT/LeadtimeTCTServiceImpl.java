package nocsystem.indexmanager.servicesIpm.DashboardTCT;

import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.DashboardTCT.leadtime.*;
import nocsystem.indexmanager.repositories.DashboardTCT.leadtime.LeadtimeNgayRepository;
import nocsystem.indexmanager.repositories.DashboardTCT.leadtime.LeadtimeThangRepository;
import nocsystem.indexmanager.services.DashboardTCT.LeadtimeTCTService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;


@Service
public class LeadtimeTCTServiceImpl implements LeadtimeTCTService {
    @Autowired
    LeadtimeNgayRepository leadtimeNgayRepository;

    @Autowired
    LeadtimeThangRepository leadtimeThangRepository;

    @Override
    public DashTCTLeadtimeResponse getDashTCTLeadtime(LocalDate ngayBaoCao, String chiN<PERSON>h, <PERSON> buuCuc, Integer luyKe) {
        DashTCTLeadtimeResponse response = new DashTCTLeadtimeResponse();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            LocalDate ngayBaoCaoN1 = ngayBaoCao.minusMonths(1);
            Date ngayBaoCaoNew = Date.valueOf(ngayBaoCao);
            Date ngayBaoCaoNewN1 = Date.valueOf(ngayBaoCaoN1);
            LeadtimeDto data;
            LeadtimeDto dataN1;

            if (luyKe == 0) {
                if (chiNhanh != null && chiNhanh.isEmpty()) {
                    data = leadtimeNgayRepository.getLeadtimeNgayAll(ngayBaoCaoNew);
                    dataN1 = leadtimeNgayRepository.getLeadtimeNgayAll(ngayBaoCaoNewN1);
                } else {
                    if (buuCuc != null && buuCuc.isEmpty()) {
                        data = leadtimeNgayRepository.getLeadtimeNgayCN(ngayBaoCaoNew, chiNhanh);
                        dataN1 = leadtimeNgayRepository.getLeadtimeNgayCN(ngayBaoCaoNewN1, chiNhanh);
                    } else {
                        data = leadtimeNgayRepository.getLeadtimeNgayCNBC(ngayBaoCaoNew, chiNhanh, buuCuc);
                        dataN1 = leadtimeNgayRepository.getLeadtimeNgayCNBC(ngayBaoCaoNewN1, chiNhanh, buuCuc);
                    }
                }
            } else {
                if (chiNhanh != null && chiNhanh.isEmpty()) {
                    data = leadtimeThangRepository.getLeadtimeThangAll(ngayBaoCaoNew);
                    dataN1 = leadtimeThangRepository.getLeadtimeThangAll(ngayBaoCaoNewN1);
                } else {
                    if (buuCuc != null && buuCuc.isEmpty()) {
                        data = leadtimeThangRepository.getLeadtimeThangCN(ngayBaoCaoNew, chiNhanh);
                        dataN1 = leadtimeThangRepository.getLeadtimeThangCN(ngayBaoCaoNewN1, chiNhanh);
                    } else {
                        data = leadtimeThangRepository.getLeadtimeThangCNBC(ngayBaoCaoNew, chiNhanh, buuCuc);
                        dataN1 = leadtimeThangRepository.getLeadtimeThangCNBC(ngayBaoCaoNewN1, chiNhanh, buuCuc);
                    }
                }
            }

            //thoi gian tt all
            LeadtimeTGAllResponse leadtimeTGAllResponse = new LeadtimeTGAllResponse();
            if (data != null) {
                leadtimeTGAllResponse.setTgTtAll(data.getTgTtAll() != null ? data.getTgTtAll() : null);
                leadtimeTGAllResponse.setTgFmAll(data.getTgFmAll() != null ? data.getTgFmAll() : null);
                leadtimeTGAllResponse.setTgMmAll(data.getTgMmAll() != null ? data.getTgMmAll() : null);
                leadtimeTGAllResponse.setTgLmAll(data.getTgLmAll() != null ? data.getTgLmAll() : null);
                leadtimeTGAllResponse.setTgNoiTinhAll(data.getTgNoiTinhAll() != null ? data.getTgNoiTinhAll() : null);
                leadtimeTGAllResponse.setTgNoiMienAll(data.getTgNoiMienAll() != null ? data.getTgNoiMienAll() : null);
                leadtimeTGAllResponse.setTgLienMienAll(data.getTgLienMienAll() != null ? data.getTgLienMienAll() : null);
            }
            if (dataN1 != null) {
                leadtimeTGAllResponse.setTgTtN1All(dataN1.getTgTtAll() != null ? dataN1.getTgTtAll() : null);
                leadtimeTGAllResponse.setTgFmN1All(dataN1.getTgFmAll() != null ? dataN1.getTgFmAll() : null);
                leadtimeTGAllResponse.setTgMmN1All(dataN1.getTgMmAll() != null ? dataN1.getTgMmAll() : null);
                leadtimeTGAllResponse.setTgLmN1All(dataN1.getTgLmAll() != null ? dataN1.getTgLmAll() : null);
                leadtimeTGAllResponse.setTgNoiTinhN1All(dataN1.getTgNoiTinhAll() != null ? dataN1.getTgNoiTinhAll() : null);
                leadtimeTGAllResponse.setTgNoiMienN1All(dataN1.getTgNoiMienAll() != null ? dataN1.getTgNoiMienAll() : null);
                leadtimeTGAllResponse.setTgLienMienN1All(dataN1.getTgLienMienAll() != null ? dataN1.getTgLienMienAll() : null);
            }
            if (data != null && dataN1 != null) {
                leadtimeTGAllResponse.setTgTtTangTruongAll(
                        (data.getTgTtAll() != null && dataN1.getTgTtAll() != null) ?
                                lamTron(data.getTgTtAll() - dataN1.getTgTtAll())
                                : null
                );
                leadtimeTGAllResponse.setTgFmTangTruongAll(
                        (data.getTgFmAll() != null && dataN1.getTgFmAll() != null) ?
                                lamTron(data.getTgFmAll() - dataN1.getTgFmAll())
                                : null
                );
                leadtimeTGAllResponse.setTgMmTangTruongAll(
                        (data.getTgMmAll() != null && dataN1.getTgMmAll() != null) ?
                                lamTron(data.getTgMmAll() - dataN1.getTgMmAll())
                                : null
                );
                leadtimeTGAllResponse.setTgLmTangTruongAll(
                        (data.getTgLmAll() != null && dataN1.getTgLmAll() != null) ?
                                lamTron(data.getTgLmAll() - dataN1.getTgLmAll())
                                : null
                );
                leadtimeTGAllResponse.setTgNoiTinhTangTruongAll(
                        (data.getTgNoiTinhAll() != null && dataN1.getTgNoiTinhAll() != null) ?
                                lamTron(data.getTgNoiTinhAll() - dataN1.getTgNoiTinhAll())
                                : null
                );
                leadtimeTGAllResponse.setTgNoiMienTangTruongAll(
                        (data.getTgNoiMienAll() != null && dataN1.getTgNoiMienAll() != null) ?
                                lamTron(data.getTgNoiMienAll() - dataN1.getTgNoiMienAll())
                                : null
                );
                leadtimeTGAllResponse.setTgLienMienTangTruongAll(
                        (data.getTgLienMienAll() != null && dataN1.getTgLienMienAll() != null) ?
                                lamTron(data.getTgLienMienAll() - dataN1.getTgLienMienAll())
                                : null
                );
            }
            response.setLeadtimeTGAll(leadtimeTGAllResponse);


            //thoi gian tt nhanh
            LeadtimeTGNhanhResponse leadtimeTGNhanhResponse = new LeadtimeTGNhanhResponse();
            if (data != null) {
                leadtimeTGNhanhResponse.setTgTtNhanh(data.getTgTtNhanh() != null ? data.getTgTtNhanh() : null);
                leadtimeTGNhanhResponse.setTgFmNhanh(data.getTgFmNhanh() != null ? data.getTgFmNhanh() : null);
                leadtimeTGNhanhResponse.setTgMmNhanh(data.getTgMmNhanh() != null ? data.getTgMmNhanh() : null);
                leadtimeTGNhanhResponse.setTgLmNhanh(data.getTgLmNhanh() != null ? data.getTgLmNhanh() : null);
                leadtimeTGNhanhResponse.setTgNoiTinhNhanh(data.getTgNoiTinhNhanh() != null ? data.getTgNoiTinhNhanh() : null);
                leadtimeTGNhanhResponse.setTgNoiMienNhanh(data.getTgNoiMienNhanh() != null ? data.getTgNoiMienNhanh() : null);
                leadtimeTGNhanhResponse.setTgLienMienNhanh(data.getTgLienMienNhanh() != null ? data.getTgLienMienNhanh() : null);
            }
            if (dataN1 != null) {
                leadtimeTGNhanhResponse.setTgTtN1Nhanh(dataN1.getTgTtNhanh() != null ? dataN1.getTgTtNhanh() : null);
                leadtimeTGNhanhResponse.setTgFmN1Nhanh(dataN1.getTgFmNhanh() != null ? dataN1.getTgFmNhanh() : null);
                leadtimeTGNhanhResponse.setTgMmN1Nhanh(dataN1.getTgMmNhanh() != null ? dataN1.getTgMmNhanh() : null);
                leadtimeTGNhanhResponse.setTgLmN1Nhanh(dataN1.getTgLmNhanh() != null ? dataN1.getTgLmNhanh() : null);
                leadtimeTGNhanhResponse.setTgNoiTinhN1Nhanh(dataN1.getTgNoiTinhNhanh() != null ? dataN1.getTgNoiTinhNhanh() : null);
                leadtimeTGNhanhResponse.setTgNoiMienN1Nhanh(dataN1.getTgNoiMienNhanh() != null ? dataN1.getTgNoiMienNhanh() : null);
                leadtimeTGNhanhResponse.setTgLienMienN1Nhanh(dataN1.getTgLienMienNhanh() != null ? dataN1.getTgLienMienNhanh() : null);
            }
            if (data != null && dataN1 != null) {
                leadtimeTGNhanhResponse.setTgTtTangTruongNhanh(
                        (data.getTgTtNhanh() != null && dataN1.getTgTtNhanh() != null) ?
                                lamTron(data.getTgTtNhanh() - dataN1.getTgTtNhanh())
                                : null
                );
                leadtimeTGNhanhResponse.setTgFmTangTruongNhanh(
                        (data.getTgFmNhanh() != null && dataN1.getTgFmNhanh() != null) ?
                                lamTron(data.getTgFmNhanh() - dataN1.getTgFmNhanh())
                                : null
                );
                leadtimeTGNhanhResponse.setTgMmTangTruongNhanh(
                        (data.getTgMmNhanh() != null && dataN1.getTgMmNhanh() != null) ?
                                lamTron(data.getTgMmNhanh() - dataN1.getTgMmNhanh())
                                : null
                );
                leadtimeTGNhanhResponse.setTgLmTangTruongNhanh(
                        (data.getTgLmNhanh() != null && dataN1.getTgLmNhanh() != null) ?
                                lamTron(data.getTgLmNhanh() - dataN1.getTgLmNhanh())
                                : null
                );
                leadtimeTGNhanhResponse.setTgNoiTinhTangTruongNhanh(
                        (data.getTgNoiTinhNhanh() != null && dataN1.getTgNoiTinhNhanh() != null) ?
                                lamTron(data.getTgNoiTinhNhanh() - dataN1.getTgNoiTinhNhanh())
                                : null
                );
                leadtimeTGNhanhResponse.setTgNoiMienTangTruongNhanh(
                        (data.getTgNoiMienNhanh() != null && dataN1.getTgNoiMienNhanh() != null) ?
                                lamTron(data.getTgNoiMienNhanh() - dataN1.getTgNoiMienNhanh())
                                : null
                );
                leadtimeTGNhanhResponse.setTgLienMienTangTruongNhanh(
                        (data.getTgLienMienNhanh() != null && dataN1.getTgLienMienNhanh() != null) ?
                                lamTron(data.getTgLienMienNhanh() - dataN1.getTgLienMienNhanh())
                                : null
                );
            }
            response.setLeadtimeTGNhanh(leadtimeTGNhanhResponse);


            //thoi gian tt tiet kiem
            LeadtimeTGTKResponse leadtimeTGTKResponse = new LeadtimeTGTKResponse();
            if (data != null) {
                leadtimeTGTKResponse.setTgTtTK(data.getTgTtTK() != null ? data.getTgTtTK() : null);
                leadtimeTGTKResponse.setTgFmTK(data.getTgFmTK() != null ? data.getTgFmTK() : null);
                leadtimeTGTKResponse.setTgMmTK(data.getTgMmTK() != null ? data.getTgMmTK() : null);
                leadtimeTGTKResponse.setTgLmTK(data.getTgLmTK() != null ? data.getTgLmTK() : null);
                leadtimeTGTKResponse.setTgNoiTinhTK(data.getTgNoiTinhTK() != null ? data.getTgNoiTinhTK() : null);
                leadtimeTGTKResponse.setTgNoiMienTK(data.getTgNoiMienTK() != null ? data.getTgNoiMienTK() : null);
                leadtimeTGTKResponse.setTgLienMienTK(data.getTgLienMienTK() != null ? data.getTgLienMienTK() : null);

            }
            if (dataN1 != null) {
                leadtimeTGTKResponse.setTgTtN1TK(dataN1.getTgTtTK() != null ? dataN1.getTgTtTK() : null);
                leadtimeTGTKResponse.setTgFmN1TK(dataN1.getTgFmTK() != null ? dataN1.getTgFmTK() : null);
                leadtimeTGTKResponse.setTgMmN1TK(dataN1.getTgMmTK() != null ? dataN1.getTgMmTK() : null);
                leadtimeTGTKResponse.setTgLmN1TK(dataN1.getTgLmTK() != null ? dataN1.getTgLmTK() : null);
                leadtimeTGTKResponse.setTgNoiTinhN1TK(dataN1.getTgNoiTinhTK() != null ? dataN1.getTgNoiTinhTK() : null);
                leadtimeTGTKResponse.setTgNoiMienN1TK(dataN1.getTgNoiMienTK() != null ? dataN1.getTgNoiMienTK() : null);
                leadtimeTGTKResponse.setTgLienMienN1TK(dataN1.getTgLienMienTK() != null ? dataN1.getTgLienMienTK() : null);
            }
            if (data != null && dataN1 != null) {
                leadtimeTGTKResponse.setTgTtTangTruongTK(
                        (data.getTgTtTK() != null && dataN1.getTgTtTK() != null) ?
                                lamTron(data.getTgTtTK() - dataN1.getTgTtTK())
                                : null
                );
                leadtimeTGTKResponse.setTgFmTangTruongTK(
                        (data.getTgFmTK() != null && dataN1.getTgFmTK() != null) ?
                                lamTron(data.getTgFmTK() - dataN1.getTgFmTK())
                                : null
                );
                leadtimeTGTKResponse.setTgMmTangTruongTK(
                        (data.getTgMmTK() != null && dataN1.getTgMmTK() != null) ?
                                lamTron(data.getTgMmTK() - dataN1.getTgMmTK())
                                : null
                );
                leadtimeTGTKResponse.setTgLmTangTruongTK(
                        (data.getTgLmTK() != null && dataN1.getTgLmTK() != null) ?
                                lamTron(data.getTgLmTK() - dataN1.getTgLmTK())
                                : null
                );
                leadtimeTGTKResponse.setTgNoiTinhTangTruongTK(
                        (data.getTgNoiTinhTK() != null && dataN1.getTgNoiTinhTK() != null) ?
                                lamTron(data.getTgNoiTinhTK() - dataN1.getTgNoiTinhTK())
                                : null
                );
                leadtimeTGTKResponse.setTgNoiMienTangTruongTK(
                        (data.getTgNoiMienTK() != null && dataN1.getTgNoiMienTK() != null) ?
                                lamTron(data.getTgNoiMienTK() - dataN1.getTgNoiMienTK())
                                : null
                );
                leadtimeTGTKResponse.setTgLienMienTangTruongTK(
                        (data.getTgLienMienTK() != null && dataN1.getTgLienMienTK() != null) ?
                                lamTron(data.getTgLienMienTK() - dataN1.getTgLienMienTK())
                                : null
                );
            }
            response.setLeadtimeTGTK(leadtimeTGTKResponse);


            //thoi gian tt kien tiet kiem
            LeadtimeTGKienTKResponse leadtimeTGKienTKResponse = new LeadtimeTGKienTKResponse();
            if (data != null) {
                leadtimeTGKienTKResponse.setTgTtKienTK(data.getTgTtKienTK() != null ? data.getTgTtKienTK() : null);
                leadtimeTGKienTKResponse.setTgFmKienTK(data.getTgFmKienTK() != null ? data.getTgFmKienTK() : null);
                leadtimeTGKienTKResponse.setTgMmKienTK(data.getTgMmKienTK() != null ? data.getTgMmKienTK() : null);
                leadtimeTGKienTKResponse.setTgLmKienTK(data.getTgLmKienTK() != null ? data.getTgLmKienTK() : null);
                leadtimeTGKienTKResponse.setTgNoiTinhKienTK(data.getTgNoiTinhKienTK() != null ? data.getTgNoiTinhKienTK() : null);
                leadtimeTGKienTKResponse.setTgNoiMienKienTK(data.getTgNoiMienKienTK() != null ? data.getTgNoiMienKienTK() : null);
                leadtimeTGKienTKResponse.setTgLienMienKienTK(data.getTgLienMienKienTK() != null ? data.getTgLienMienKienTK() : null);
            }
            if (dataN1 != null) {
                leadtimeTGKienTKResponse.setTgTtN1KienTK(dataN1.getTgTtKienTK() != null ? dataN1.getTgTtKienTK() : null);
                leadtimeTGKienTKResponse.setTgFmN1KienTK(dataN1.getTgFmKienTK() != null ? dataN1.getTgFmKienTK() : null);
                leadtimeTGKienTKResponse.setTgMmN1KienTK(dataN1.getTgMmKienTK() != null ? dataN1.getTgMmKienTK() : null);
                leadtimeTGKienTKResponse.setTgLmN1KienTK(dataN1.getTgLmKienTK() != null ? dataN1.getTgLmKienTK() : null);
                leadtimeTGKienTKResponse.setTgNoiTinhN1KienTK(dataN1.getTgNoiTinhKienTK() != null ? dataN1.getTgNoiTinhKienTK() : null);
                leadtimeTGKienTKResponse.setTgNoiMienN1KienTK(dataN1.getTgNoiMienKienTK() != null ? dataN1.getTgNoiMienKienTK() : null);
                leadtimeTGKienTKResponse.setTgLienMienN1KienTK(dataN1.getTgLienMienKienTK() != null ? dataN1.getTgLienMienKienTK() : null);
            }
            if (data != null && dataN1 != null) {
                leadtimeTGKienTKResponse.setTgTtTangTruongKienTK(
                        (data.getTgTtKienTK() != null && dataN1.getTgTtKienTK() != null) ?
                                lamTron(data.getTgTtKienTK() - dataN1.getTgTtKienTK())
                                : null
                );
                leadtimeTGKienTKResponse.setTgFmTangTruongKienTK(
                        (data.getTgFmKienTK() != null && dataN1.getTgFmKienTK() != null) ?
                                lamTron(data.getTgFmKienTK() - dataN1.getTgFmKienTK())
                                : null
                );
                leadtimeTGKienTKResponse.setTgMmTangTruongKienTK(
                        (data.getTgMmKienTK() != null && dataN1.getTgMmKienTK() != null) ?
                                lamTron(data.getTgMmKienTK() - dataN1.getTgMmKienTK())
                                : null
                );
                leadtimeTGKienTKResponse.setTgLmTangTruongKienTK(
                        (data.getTgLmKienTK() != null && dataN1.getTgLmKienTK() != null) ?
                                lamTron(data.getTgLmKienTK() - dataN1.getTgLmKienTK())
                                : null
                );
                leadtimeTGKienTKResponse.setTgNoiTinhTangTruongKienTK(
                        (data.getTgNoiTinhKienTK() != null && dataN1.getTgNoiTinhKienTK() != null) ?
                                lamTron(data.getTgNoiTinhKienTK() - dataN1.getTgNoiTinhKienTK())
                                : null
                );
                leadtimeTGKienTKResponse.setTgNoiMienTangTruongKienTK(
                        (data.getTgNoiMienKienTK() != null && dataN1.getTgNoiMienKienTK() != null) ?
                                lamTron(data.getTgNoiMienKienTK() - dataN1.getTgNoiMienKienTK())
                                : null
                );
                leadtimeTGKienTKResponse.setTgLienMienTangTruongKienTK(
                        (data.getTgLienMienKienTK() != null && dataN1.getTgLienMienKienTK() != null) ?
                                lamTron(data.getTgLienMienKienTK() - dataN1.getTgLienMienKienTK())
                                : null
                );
            }
            response.setLeadtimeTGKienTK(leadtimeTGKienTKResponse);


            //thoi gian tt tach kien tiet kiem
            LeadtimeTGTachKienTKResponse leadtimeTGTachKienTKResponse = new LeadtimeTGTachKienTKResponse();
            if (data != null) {
                leadtimeTGTachKienTKResponse.setTgTtTachKienTK(data.getTgTtTachKienTK() != null ? data.getTgTtTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgFmTachKienTK(data.getTgFmTachKienTK() != null ? data.getTgFmTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgMmTachKienTK(data.getTgMmTachKienTK() != null ? data.getTgMmTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgLmTachKienTK(data.getTgLmTachKienTK() != null ? data.getTgLmTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgNoiTinhTachKienTK(data.getTgNoiTinhTachKienTK() != null ? data.getTgNoiTinhTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgNoiMienTachKienTK(data.getTgNoiMienTachKienTK() != null ? data.getTgNoiMienTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgLienMienTachKienTK(data.getTgLienMienTachKienTK() != null ? data.getTgLienMienTachKienTK() : null);
            }
            if (dataN1 != null) {
                leadtimeTGTachKienTKResponse.setTgTtN1TachKienTK(dataN1.getTgTtTachKienTK() != null ? dataN1.getTgTtTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgFmN1TachKienTK(dataN1.getTgFmTachKienTK() != null ? dataN1.getTgFmTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgMmN1TachKienTK(dataN1.getTgMmTachKienTK() != null ? dataN1.getTgMmTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgLmN1TachKienTK(dataN1.getTgLmTachKienTK() != null ? dataN1.getTgLmTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgNoiTinhN1TachKienTK(dataN1.getTgNoiTinhTachKienTK() != null ? dataN1.getTgNoiTinhTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgNoiMienN1TachKienTK(dataN1.getTgNoiMienTachKienTK() != null ? dataN1.getTgNoiMienTachKienTK() : null);
                leadtimeTGTachKienTKResponse.setTgLienMienN1TachKienTK(dataN1.getTgLienMienTachKienTK() != null ? dataN1.getTgLienMienTachKienTK() : null);
            }
            if (data != null && dataN1 != null) {
                leadtimeTGTachKienTKResponse.setTgTtTangTruongTachKienTK(
                        (data.getTgTtTachKienTK() != null && dataN1.getTgTtTachKienTK() != null) ?
                                lamTron(data.getTgTtTachKienTK() - dataN1.getTgTtTachKienTK())
                                : null
                );
                leadtimeTGTachKienTKResponse.setTgFmTangTruongTachKienTK(
                        (data.getTgFmTachKienTK() != null && dataN1.getTgFmTachKienTK() != null) ?
                                lamTron(data.getTgFmTachKienTK() - dataN1.getTgFmTachKienTK())
                                : null
                );
                leadtimeTGTachKienTKResponse.setTgMmTangTruongTachKienTK(
                        (data.getTgMmTachKienTK() != null && dataN1.getTgMmTachKienTK() != null) ?
                                lamTron(data.getTgMmTachKienTK() - dataN1.getTgMmTachKienTK())
                                : null
                );
                leadtimeTGTachKienTKResponse.setTgLmTangTruongTachKienTK(
                        (data.getTgLmTachKienTK() != null && dataN1.getTgLmTachKienTK() != null) ?
                                lamTron(data.getTgLmTachKienTK() - dataN1.getTgLmTachKienTK())
                                : null
                );
                leadtimeTGTachKienTKResponse.setTgNoiTinhTangTruongTachKienTK(
                        (data.getTgNoiTinhTachKienTK() != null && dataN1.getTgNoiTinhTachKienTK() != null) ?
                                lamTron(data.getTgNoiTinhTachKienTK() - dataN1.getTgNoiTinhTachKienTK())
                                : null
                );
                leadtimeTGTachKienTKResponse.setTgNoiMienTangTruongTachKienTK(
                        (data.getTgNoiMienTachKienTK() != null && dataN1.getTgNoiMienTachKienTK() != null) ?
                                lamTron(data.getTgNoiMienTachKienTK() - dataN1.getTgNoiMienTachKienTK())
                                : null
                );
                leadtimeTGTachKienTKResponse.setTgLienMienTangTruongTachKienTK(
                        (data.getTgLienMienTachKienTK() != null && dataN1.getTgLienMienTachKienTK() != null) ?
                                lamTron(data.getTgLienMienTachKienTK() - dataN1.getTgLienMienTachKienTK())
                                : null
                );
            }
            response.setLeadtimeTGTachKienTK(leadtimeTGTachKienTKResponse);

        }
        return response;
    }

    @Override
    public List<LeadtimeChartTTAllResponse> getLeadtimeChartTTAll(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        List<LeadtimeChartTTAllResponse> listData = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            LocalDate ngayDauTien = ngayBaoCao.withDayOfMonth(1);
            Date ngayDauTienNew = Date.valueOf(ngayDauTien);
            Date ngayBaoCaoNew = Date.valueOf(ngayBaoCao);

            if (chiNhanh != null && chiNhanh.isEmpty()) {
                listData = leadtimeNgayRepository.getLeadtimeChartTTAllNgayAll(ngayDauTienNew, ngayBaoCaoNew);
            } else {
                if (buuCuc != null && buuCuc.isEmpty()) {
                    listData = leadtimeNgayRepository.getLeadtimeChartTTAllNgayCN(ngayDauTienNew, ngayBaoCaoNew, chiNhanh);
                } else {
                    listData = leadtimeNgayRepository.getLeadtimeChartTTAllNgayCNBC(ngayDauTienNew, ngayBaoCaoNew, chiNhanh, buuCuc);
                }
            }

            List<LeadtimeChartTTAllResponse> listDataNull = new ArrayList<>();
            LeadtimeChartTTAllResponse dataNull;
            int dayOfNgayBaoCao = ngayBaoCao.getDayOfMonth();
            int monthOfNgayBaoCao = ngayBaoCao.getMonthValue();
            int yearOfNgayBaoCao = ngayBaoCao.getYear();
            for (int i = 1; i <= dayOfNgayBaoCao; i++) {
                LocalDate localDate = LocalDate.of(yearOfNgayBaoCao, monthOfNgayBaoCao, i);
                dataNull = new LeadtimeChartTTAllResponse(Date.valueOf(localDate), null, null, null, null);
                listDataNull.add(dataNull);
            }

            if (!listData.isEmpty()) {
                for (int i = 0; i < listDataNull.size(); i++) {
                    LeadtimeChartTTAllResponse itemNull = listDataNull.get(i);
                    boolean check = false;
                    for (int j = 0; j < listData.size(); j++) {
                        LeadtimeChartTTAllResponse item = listData.get(j);
                        if (itemNull.getNgayBaoCao().equals(item.getNgayBaoCao())) {
                            check = true;
                        }
                    }
                    if (!check) {
                        listData.add(i, itemNull);
                    }
                }
            }
        }
        return listData;
    }

    @Override
    public List<LeadtimeChartTTTachKienTKResponse> getLeadtimeChartTTTachKienTK(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        List<LeadtimeChartTTTachKienTKResponse> listData = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            LocalDate ngayDauTien = ngayBaoCao.withDayOfMonth(1);
            Date ngayDauTienNew = Date.valueOf(ngayDauTien);
            Date ngayBaoCaoNew = Date.valueOf(ngayBaoCao);

            if (chiNhanh != null && chiNhanh.isEmpty()) {
                listData = leadtimeNgayRepository.getLeadtimeChartTTTachKienTKNgayAll(ngayDauTienNew, ngayBaoCaoNew);
            } else {
                if (buuCuc != null && buuCuc.isEmpty()) {
                    listData = leadtimeNgayRepository.getLeadtimeChartTTTachKienTKNgayCN(ngayDauTienNew, ngayBaoCaoNew, chiNhanh);
                } else {
                    listData = leadtimeNgayRepository.getLeadtimeChartTTTachKienTKNgayCNBC(ngayDauTienNew, ngayBaoCaoNew, chiNhanh, buuCuc);
                }
            }

            List<LeadtimeChartTTTachKienTKResponse> listDataNull = new ArrayList<>();
            LeadtimeChartTTTachKienTKResponse dataNull;
            int dayOfNgayBaoCao = ngayBaoCao.getDayOfMonth();
            int monthOfNgayBaoCao = ngayBaoCao.getMonthValue();
            int yearOfNgayBaoCao = ngayBaoCao.getYear();
            for (int i = 1; i <= dayOfNgayBaoCao; i++) {
                LocalDate localDate = LocalDate.of(yearOfNgayBaoCao, monthOfNgayBaoCao, i);
                dataNull = new LeadtimeChartTTTachKienTKResponse(Date.valueOf(localDate), null, null, null, null);
                listDataNull.add(dataNull);
            }

            if (!listData.isEmpty()) {
                for (int i = 0; i < listDataNull.size(); i++) {
                    LeadtimeChartTTTachKienTKResponse itemNull = listDataNull.get(i);
                    boolean check = false;
                    for (int j = 0; j < listData.size(); j++) {
                        LeadtimeChartTTTachKienTKResponse item = listData.get(j);
                        if (itemNull.getNgayBaoCao().equals(item.getNgayBaoCao())) {
                            check = true;
                        }
                    }
                    if (!check) {
                        listData.add(i, itemNull);
                    }
                }
            }
        }
        return listData;
    }

    private double lamTron(double item) {
        return (double) Math.round(item * 100) / 100;
    }
}
