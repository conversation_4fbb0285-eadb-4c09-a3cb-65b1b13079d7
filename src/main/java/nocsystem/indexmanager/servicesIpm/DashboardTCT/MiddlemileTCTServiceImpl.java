package nocsystem.indexmanager.servicesIpm.DashboardTCT;

import nocsystem.indexmanager.dao.DashboardTCT.MiddlemileDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.*;
import nocsystem.indexmanager.repositories.DashboardTCT.leadtime.LeadtimeNgayRepository;
import nocsystem.indexmanager.repositories.DashboardTCT.leadtime.LeadtimeThangRepository;
import nocsystem.indexmanager.services.DashboardTCT.MiddlemileTCTService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class MiddlemileTCTServiceImpl implements MiddlemileTCTService {
    private static final Logger logger = LoggerFactory.getLogger(FirstmileTCTServiceImpl.class);

    @Autowired
    MiddlemileDAO middlemileDAO;

    @Autowired
    LeadtimeNgayRepository leadtimeNgayRepository;

    @Autowired
    LeadtimeThangRepository leadtimeThangRepository;

    @Override
    public KhaiThacResponse getKhaiThac(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe) {
        KhaiThacResponse response = new KhaiThacResponse(new ArrayList<TTKTDto>(), new TTKTTongHopResponse());
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            LocalDate ngayBaoCaoN1 = ngayBaoCao.minusMonths(1);

            List<TTKTDto> listTTKT = middlemileDAO.getTTKT(ngayBaoCao, luyKe, chiNhanh, buuCuc);
            Long soBPKhaiThac = middlemileDAO.getSoBPKhaiThac(ngayBaoCao, luyKe, chiNhanh, buuCuc);
            Long slTaiKienNhap = middlemileDAO.getSLTaiKienNhap(ngayBaoCao, luyKe, chiNhanh, buuCuc);
            Double tgKhaiThac = middlemileDAO.getTGKhaiThac(ngayBaoCao, luyKe, chiNhanh, buuCuc);
            Double tgKhaiThacN1 = middlemileDAO.getTGKhaiThac(ngayBaoCaoN1, luyKe, chiNhanh, buuCuc);

            if (!listTTKT.isEmpty()) {
                TTKTDto ttkt1 = new TTKTDto("TTKT1", null);
                TTKTDto ttkt2 = new TTKTDto("TTKT2", null);
                TTKTDto ttkt3 = new TTKTDto("TTKT3", null);
                TTKTDto ttkt4 = new TTKTDto("TTKT4", null);
                TTKTDto ttkt5 = new TTKTDto("TTKT5", null);

                List<TTKTDto> listTTKTNull = new ArrayList<>();
                listTTKTNull.add(ttkt1);
                listTTKTNull.add(ttkt2);
                listTTKTNull.add(ttkt3);
                listTTKTNull.add(ttkt4);
                listTTKTNull.add(ttkt5);

                for (int i = 0; i < listTTKTNull.size(); i++) {
                    TTKTDto itemNull = listTTKTNull.get(i);
                    boolean check = false;
                    for (int j = 0; j < listTTKT.size(); j++) {
                        TTKTDto item = listTTKT.get(j);
                        if (itemNull.getTtkt().equals(item.getTtkt())) {
                            check = true;
                        }
                    }
                    if (!check) {
                        listTTKT.add(i, itemNull);
                    }
                }
            }

            Double tangTruong = null;
            if (tgKhaiThac != null && tgKhaiThacN1 != null) {
                tangTruong = (double) Math.round((tgKhaiThac - tgKhaiThacN1) * 100) / 100;
            }

            TTKTTongHopResponse ttktTongHopResponse = new TTKTTongHopResponse();
            ttktTongHopResponse.setSoBuuPhamKhaiThac(soBPKhaiThac);
            ttktTongHopResponse.setSlTaiKienNhap(slTaiKienNhap);
            ttktTongHopResponse.setTgKhaiThac(tgKhaiThac);
            ttktTongHopResponse.setTgKhaiThacN1(tgKhaiThacN1);
            ttktTongHopResponse.setTangTruong(tangTruong);

            response.setListTTKT(listTTKT);
            response.setKhaiThac(ttktTongHopResponse);

            logger.info("list khai thac response ", response);
        }
        return response;
    }

    @Override
    public KetNoiResponse getKetNoi(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe) {
        KetNoiResponse response = new KetNoiResponse();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            if (!chiNhanh.equals("") || !buuCuc.equals("")) {
                return response;
            }

            SLDiemKetNoiDto slDiemKetNoi = middlemileDAO.getSLDiemKetNoi(ngayBaoCao, luyKe);
            TLKetNoiDto tlKetNoi = middlemileDAO.getTLKetNoi(ngayBaoCao, luyKe, null);
            TLKetNoiDto tlKetNoiN1 = middlemileDAO.getTLKetNoi(ngayBaoCao.minusMonths(1), luyKe, null);
            TLKetNoiDto tlKetNoiChieuDi = middlemileDAO.getTLKetNoi(ngayBaoCao, luyKe, 1);
            TLKetNoiDto tlKetNoiChieuVe = middlemileDAO.getTLKetNoi(ngayBaoCao, luyKe, 2);

            Double tlKetNoiDuTangTruong = null;
            if (tlKetNoi.getTlKetNoiDu() != null && tlKetNoiN1.getTlKetNoiDu() != null) {
                tlKetNoiDuTangTruong = (double) Math.round((tlKetNoi.getTlKetNoiDu() - tlKetNoiN1.getTlKetNoiDu()) * 100) / 100;
            }
            Double tlKetNoiDGTangTruong = null;
            if (tlKetNoi.getTlKetNoiDG() != null && tlKetNoiN1.getTlKetNoiDG() != null) {
                tlKetNoiDGTangTruong = (double) Math.round((tlKetNoi.getTlKetNoiDG() - tlKetNoiN1.getTlKetNoiDG()) * 100) / 100;
            }

            response.setSlDiemCamKet(slDiemKetNoi.getSlDiemCamKet());
            response.setSlDiemKetNoiTT(slDiemKetNoi.getSlDiemKetNoiTT());
            response.setSlDiemKetNoiDG(slDiemKetNoi.getSlDiemKetNoiDG());
            response.setTlKetNoiDu(tlKetNoi.getTlKetNoiDu());
            response.setTlKetNoiDuN1(tlKetNoiN1.getTlKetNoiDu());
            response.setTlKetNoiDuTangTruong(tlKetNoiDuTangTruong);
            response.setTlKetNoiDuChieuDi(tlKetNoiChieuDi.getTlKetNoiDu());
            response.setTlKetNoiDuChieuVe(tlKetNoiChieuVe.getTlKetNoiDu());
            response.setTlKetNoiDG(tlKetNoi.getTlKetNoiDG());
            response.setTlKetNoiDGN1(tlKetNoiN1.getTlKetNoiDG());
            response.setTlKetNoiDGTangTruong(tlKetNoiDGTangTruong);
            response.setTlKetNoiDGChieuDi(tlKetNoiChieuDi.getTlKetNoiDG());
            response.setTlKetNoiDGChieuVe(tlKetNoiChieuVe.getTlKetNoiDG());

            logger.info("list ket noi response ", response);
        }

        return response;
    }

    @Override
    public HieuQuaXeResponse getHieuQuaXe(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe) {
        HieuQuaXeResponse response = new HieuQuaXeResponse();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            if (!chiNhanh.equals("") || !buuCuc.equals("")) {
                return response;
            }

            Double noiTinhNoiMien = middlemileDAO.getHieuQuaXe(ngayBaoCao, luyKe, Arrays.asList("NM", "NT"), null);
            Double noiTinhNoiMienN1 = middlemileDAO.getHieuQuaXe(ngayBaoCao.minusMonths(1), luyKe, Arrays.asList("NM", "NT"), null);
            Double noiTinhNoiMienChieuDi = middlemileDAO.getHieuQuaXe(ngayBaoCao, luyKe, Arrays.asList("NM", "NT"), 1);
            Double noiTinhNoiMienChieuVe = middlemileDAO.getHieuQuaXe(ngayBaoCao, luyKe, Arrays.asList("NM", "NT"), 2);
            Double lienMien = middlemileDAO.getHieuQuaXe(ngayBaoCao, luyKe, Arrays.asList("LM"), null);
            Double lienMienN1 = middlemileDAO.getHieuQuaXe(ngayBaoCao.minusMonths(1), luyKe, Arrays.asList("LM"), null);
            Double lienMienChieuDi = middlemileDAO.getHieuQuaXe(ngayBaoCao, luyKe, Arrays.asList("LM"), 1);

            Double noiTinhNoiMienTangTruong = null;
            if (noiTinhNoiMien != null && noiTinhNoiMienN1 != null) {
                noiTinhNoiMienTangTruong = noiTinhNoiMien - noiTinhNoiMienN1;
            }

            Double lienMienTangTruong = null;
            if (lienMien != null && lienMienN1 != null) {
                lienMienTangTruong = lienMien - lienMienN1;
            }

            response.setNoiTinhNoiMien(noiTinhNoiMien);
            response.setNoiTinhNoiMienN1(noiTinhNoiMienN1);
            response.setNoiTinhNoiMienTangTruong(noiTinhNoiMienTangTruong);
            response.setNoiTinhNoiMienChieuDi(noiTinhNoiMienChieuDi);
            response.setNoiTinhNoiMienChieuVe(noiTinhNoiMienChieuVe);
            response.setLienMien(lienMien);
            response.setLienMienN1(lienMienN1);
            response.setLienMienTangTruong(lienMienTangTruong);
            response.setLienMienChieuDi(lienMienChieuDi);

            logger.info("list hieu qua xe response ", response);
        }

        return response;
    }

    @Override
    public MiddleMileResponse getMiddleMile(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe) {
        MiddleMileResponse response = new MiddleMileResponse();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            LocalDate ngayBaoCaoN1 = ngayBaoCao.minusMonths(1);
            Date ngayBaoCaoNew = Date.valueOf(ngayBaoCao);
            Date ngayBaoCaoNewN1 = Date.valueOf(ngayBaoCaoN1);
            MiddleMileDto middleMile;
            MiddleMileDto middleMileN1;

            if (luyKe == 0) {
                if (chiNhanh != null && chiNhanh.isEmpty()) {
                    middleMile = leadtimeNgayRepository.getMiddleMileNgayAll(ngayBaoCaoNew);
                    middleMileN1 = leadtimeNgayRepository.getMiddleMileNgayAll(ngayBaoCaoNewN1);
                } else {
                    if (buuCuc != null && buuCuc.isEmpty()) {
                        middleMile = leadtimeNgayRepository.getMiddleMileNgayCN(ngayBaoCaoNew, chiNhanh);
                        middleMileN1 = leadtimeNgayRepository.getMiddleMileNgayCN(ngayBaoCaoNewN1, chiNhanh);
                    } else {
                        middleMile = leadtimeNgayRepository.getMiddleMileNgayCNBC(ngayBaoCaoNew, chiNhanh, buuCuc);
                        middleMileN1 = leadtimeNgayRepository.getMiddleMileNgayCNBC(ngayBaoCaoNewN1, chiNhanh, buuCuc);
                    }
                }
            } else {
                if (chiNhanh != null && chiNhanh.isEmpty()) {
                    middleMile = leadtimeThangRepository.getMiddleMileThangAll(ngayBaoCaoNew);
                    middleMileN1 = leadtimeThangRepository.getMiddleMileThangAll(ngayBaoCaoNewN1);
                } else {
                    if (buuCuc != null && buuCuc.isEmpty()) {
                        middleMile = leadtimeThangRepository.getMiddleMileThangCN(ngayBaoCaoNew, chiNhanh);
                        middleMileN1 = leadtimeThangRepository.getMiddleMileThangCN(ngayBaoCaoNewN1, chiNhanh);
                    } else {
                        middleMile = leadtimeThangRepository.getMiddleMileThangCNBC(ngayBaoCaoNew, chiNhanh, buuCuc);
                        middleMileN1 = leadtimeThangRepository.getMiddleMileThangCNBC(ngayBaoCaoNewN1, chiNhanh, buuCuc);
                    }
                }
            }

            if (middleMile != null) {
                response.setMiddleMileAll(middleMile.getMiddleMileAll() != null ? middleMile.getMiddleMileAll() : null);
                response.setMiddleMileLienMien(middleMile.getMiddleMileLienMien() != null ? middleMile.getMiddleMileLienMien() : null);
                response.setMiddleMileNoiMien(middleMile.getMiddleMileNoiMien() != null ? middleMile.getMiddleMileNoiMien() : null);
            }
            if (middleMileN1 != null) {
                response.setMiddleMileAllN1(middleMileN1.getMiddleMileAll() != null ? middleMileN1.getMiddleMileAll() : null);
                response.setMiddleMileLienMienN1(middleMileN1.getMiddleMileLienMien() != null ? middleMileN1.getMiddleMileLienMien() : null);
                response.setMiddleMileNoiMienN1(middleMileN1.getMiddleMileNoiMien() != null ? middleMileN1.getMiddleMileNoiMien() : null);
            }
            if (middleMile != null && middleMileN1 != null) {
                response.setMiddleMileAllTangTruong(
                        (middleMile.getMiddleMileAll() != null && middleMileN1.getMiddleMileAll() != null) ?
                                lamTron(middleMile.getMiddleMileAll() - middleMileN1.getMiddleMileAll())
                                : null
                );
                response.setMiddleMileLienMienTangTruong(
                        (middleMile.getMiddleMileLienMien() != null && middleMileN1.getMiddleMileLienMien() != null) ?
                                lamTron(middleMile.getMiddleMileLienMien() - middleMileN1.getMiddleMileLienMien())
                                : null
                );
                response.setMiddleMileNoiMienTangTruong(
                        (middleMile.getMiddleMileNoiMien() != null && middleMileN1.getMiddleMileNoiMien() != null) ?
                                lamTron(middleMile.getMiddleMileNoiMien() - middleMileN1.getMiddleMileNoiMien())
                                : null
                );
            }

            logger.info("list middle mile response ", response);
        }

        return response;
    }

    private double lamTron(double item) {
        return (double) Math.round(item * 100) / 100;
    }
}
