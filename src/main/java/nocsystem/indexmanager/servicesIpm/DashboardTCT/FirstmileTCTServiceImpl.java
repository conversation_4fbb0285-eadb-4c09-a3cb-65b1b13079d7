package nocsystem.indexmanager.servicesIpm.DashboardTCT;

import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashFirstmileTLThuTCDto;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto;
import nocsystem.indexmanager.repositories.DashboardTCT.firstmile.FirstmileNgayRepository;
import nocsystem.indexmanager.repositories.DashboardTCT.firstmile.FirstmileThangRepository;
import nocsystem.indexmanager.services.DashboardTCT.FirstmileTCTService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class FirstmileTCTServiceImpl implements FirstmileTCTService {
    private static final Logger logger = LoggerFactory.getLogger(FirstmileTCTServiceImpl.class);

    @Autowired
    FirstmileNgayRepository firstmileNgayRepository;

    @Autowired
    FirstmileThangRepository firstmileThangRepository;

    @Override
    public List<DashTCTFirstmileDto> getFirstmile(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe) {
        List<DashTCTFirstmileDto> listData = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            Date ngayBaoCaoNew = Date.valueOf(ngayBaoCao);

            logger.info("ngay bao cao " + ngayBaoCao + "chi nhanh " + chiNhanh + "buu cuc " + buuCuc + "luy ke " + luyKe);

            if (luyKe == 0) {
                if (chiNhanh.isEmpty()) {
                    listData = firstmileNgayRepository.getListFirstmileAllNgay(ngayBaoCaoNew);
                } else {
                    if (buuCuc.isEmpty()) {
                        listData = firstmileNgayRepository.getListFirstmileCNNgay(ngayBaoCaoNew, chiNhanh);
                    } else {
                        listData = firstmileNgayRepository.getListFirstmileCNBCNgay(ngayBaoCaoNew, chiNhanh, buuCuc);
                    }
                }
            } else {
                if (chiNhanh.isEmpty()) {
                    listData = firstmileThangRepository.getListFirstmileAllThang(ngayBaoCaoNew);
                } else {
                    if (buuCuc.isEmpty()) {
                        listData = firstmileThangRepository.getListFirstmileCNThang(ngayBaoCaoNew, chiNhanh);
                    } else {
                        listData = firstmileThangRepository.getListFirstmileCNBCThang(ngayBaoCaoNew, chiNhanh, buuCuc);
                    }
                }
            }
            logger.info("list data size before" + listData.size());

            DashTCTFirstmileDto slThuNull = new DashTCTFirstmileDto("1", 0, "Sản lượng thu");
            DashTCTFirstmileDto slNhapDoanhThuNull = new DashTCTFirstmileDto("2", 0, "Sản lượng nhập doanh thu");
            DashTCTFirstmileDto tlThuTCNull = new DashTCTFirstmileDto("3", 1, "Tỷ lệ thu thành công");
            DashTCTFirstmileDto tlThuDungGioNull = new DashTCTFirstmileDto("4", 1, "Tỷ lệ thu đúng giờ");

            List<DashTCTFirstmileDto> listDataNull = new ArrayList<>();
            listDataNull.add(slThuNull);
            listDataNull.add(slNhapDoanhThuNull);
            listDataNull.add(tlThuTCNull);
            listDataNull.add(tlThuDungGioNull);

            logger.info("list data size after" + listData.size());

            if (listData.isEmpty()) {
                return listDataNull;
            } else {
                for (int i = 0; i < listDataNull.size(); i++) {
                    DashTCTFirstmileDto itemNull = listDataNull.get(i);
                    boolean check = false;
                    for (int j = 0; j < listData.size(); j++) {
                        DashTCTFirstmileDto item = listData.get(j);
                        if (itemNull.getStt().equals(item.getStt())) {
                            check = true;
                        }
                    }
                    if (!check) {
                        listData.add(i, itemNull);
                    }
                }
                logger.info("list data size after" + listData.size());
                return listData;
            }
        }
        return listData;
    }

    @Override
    public List<DashFirstmileTLThuTCDto> getDashFirstmileTlThuTCNgay(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        List<DashFirstmileTLThuTCDto> listData = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewDashTCT().equalsIgnoreCase("true")) {
            LocalDate firstDayOfMonth = ngayBaoCao.withDayOfMonth(1);
            Date ngayBaoCaoNew = Date.valueOf(ngayBaoCao);
            Date firstDayOfMonthNew = Date.valueOf(firstDayOfMonth);

            logger.info("ngay bao cao " + ngayBaoCao + "chi nhanh " + chiNhanh + "buu cuc " + buuCuc);

            if (chiNhanh.isEmpty()) {
                listData = firstmileNgayRepository.getListDashFirstmileTLThuTCAllNgay(firstDayOfMonthNew, ngayBaoCaoNew, "3");
            } else {
                if (buuCuc.isEmpty()) {
                    listData = firstmileNgayRepository.getListDashFirstmileTLThuTCCNNgay(firstDayOfMonthNew, ngayBaoCaoNew, "3", chiNhanh);
                } else {
                    listData = firstmileNgayRepository.getListDashFirstmileTLThuTCCNBCNgay(firstDayOfMonthNew, ngayBaoCaoNew, "3", chiNhanh, buuCuc);
                }
            }
            logger.info("list data size " + listData.size());

            DashFirstmileTLThuTCDto dataNull;
            List<DashFirstmileTLThuTCDto> listDataNull = new ArrayList<>();
            int dayOfNgayBaoCao = ngayBaoCao.getDayOfMonth();
            int monthOfNgayBaoCao = ngayBaoCao.getMonthValue();
            int yearOfNgayBaoCao = ngayBaoCao.getYear();
            for (int i = 1; i <= dayOfNgayBaoCao; i++) {
                LocalDate localDate = LocalDate.of(yearOfNgayBaoCao, monthOfNgayBaoCao, i);
                dataNull = new DashFirstmileTLThuTCDto(Date.valueOf(localDate), null);
                listDataNull.add(dataNull);
            }

            if (!listData.isEmpty()) {
                for (int i = 0; i < listDataNull.size(); i++) {
                    DashFirstmileTLThuTCDto itemNull = listDataNull.get(i);
                    boolean check = false;
                    for (int j = 0; j < listData.size(); j++) {
                        DashFirstmileTLThuTCDto item = listData.get(j);
                        if (itemNull.getNgayBaoCao().equals(item.getNgayBaoCao())) {
                            check = true;
                        }
                    }
                    if (!check) {
                        listData.add(i, itemNull);
                    }
                }
                logger.info("list data size after" + listData.size());
            }
            return listData;
        }
        return listData;
    }
}
