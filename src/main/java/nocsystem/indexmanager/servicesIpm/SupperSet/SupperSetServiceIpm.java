package nocsystem.indexmanager.servicesIpm.SupperSet;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SupperSet.GenerateConnection;
import nocsystem.indexmanager.exception.BadRequestException;
import nocsystem.indexmanager.exception.InternalServerException;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.SupperSet.SecurityLoginDto;
import nocsystem.indexmanager.models.SupperSet.SuperSetAccessTokenResponse;
import nocsystem.indexmanager.models.SupperSet.SuperSetLogin;
import nocsystem.indexmanager.request.SupperSetReq;
import nocsystem.indexmanager.services.SupperSet.SupperSetService;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.net.URLDecoder;
import java.sql.*;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SupperSetServiceIpm implements SupperSetService {
    @Autowired
    private GenerateConnection generateConnection;
    @Autowired
    private RedisTemplate redisTemplate;
    private List<String> listAvailableDB = List.of("noc_index_manager", "noc_base", "noc_network_monitoring", "noc_quality");
    private String keyUserSupperSet = "SUPPER_SET_" + ListVariableLocation.maNhanVien;
    private int maxRow = 100000;
    private int maxExecutionTime = 30;
    private int timeOut = 60;

    private int timeOutToTal = 60 * 60;

    private String defaultColumnChiNhanh = "ma_chinhanh";
    private String defaultColumnBuuCuc = "ma_buucuc";

    private int limitForCount = 1000000;

    @Value("${superset-server.domain}")
    private String domainSuperset;

    @Value("${superset-server.username}")
    private String username;

    @Value("${superset-server.password}")
    private String password;

    @Value("${superset-server.provider}")
    private String provider;

    public void checkInjectionOfContext(String sqlContext, String dbName) {
        if (!listAvailableDB.contains(dbName)) {
            throw new BadRequestException("Không tồn tại DB !");
        }

        String queryToUpperCase = sqlContext.toUpperCase();
        List<String> listInjection = List.of("DROP TABLE", "DROP DATABASE", "UPDATE TABLE", "DELETE TABLE", "ALTER TABLE", "ALTER DATABASE");

        for (int injectInt = 0; injectInt < listInjection.size(); injectInt++) {
            if (queryToUpperCase.contains(listInjection.get(injectInt))) {
                throw new BadRequestException("Dữ liệu gửi tới server không hợp lệ .");
            }
        }

        if (!queryToUpperCase.contains("WHERE")) {
            throw new BadRequestException("Not permit Select without where ");
        }
    }

    @Override
    public List<Map<String, Object>> getDataOfQueryBuilder(SupperSetReq supperSetReq) throws SQLException {
        String sqlContext = supperSetReq.getSql();
        String dbName = supperSetReq.getDbName();
//        ResultSet rs = null;
        Connection connection = null;
        List<Map<String, Object>> result;
        String keyUserSupperSetCS = Base64.getEncoder().encodeToString((keyUserSupperSet + sqlContext + supperSetReq.getLimit() + supperSetReq.getOffset()).getBytes());

        /*
         * Trường hợp supper-set không truyền lên limit và offset thì ưu tiên lấy dữ liệu trong Redis */
//        List<Map<String, Object>> totalRows = this.getCacheResultSet(keyUserSupperSetCS);
//        if (totalRows != null) {
//            System.out.println("Check Data In Cache ==========> OK");
//            return totalRows;
//        }

        try {
            /* Step 1: check injection */
            StopWatch controllConnect = new StopWatch();
            this.checkInjectionOfContext(sqlContext, dbName);

            /* Step 2: Create a statement using connection object */
            controllConnect.start("controllConnect");
            String sqlDecode = URLDecoder.decode(sqlContext, "UTF-8");
            String configDBName = dbName.replace('_', '-');
            connection = generateConnection.createConnection(configDBName);
            controllConnect.stop();

            /* Step 3: Check excusion plan*/
            /*
            this.checkExcutionOfQuery(sqlDecode, connection);
            */

            /* Step 4: Gán data theo phân quyên*/
            String schemaFollowPermission = this.getSchemaFollowPermission(sqlDecode, connection);

            StopWatch checkExcuse = new StopWatch();
            checkExcuse.start("checkExcuse");
            /* Step 5: Get Data and Performance excute query */
            log.info(controllConnect.getLastTaskInfo().getTaskName() + ": " + controllConnect.getLastTaskInfo().getTimeMillis());
            if (supperSetReq.getLimit() == 1000000) {
                result = this.getCountTotalPage(sqlDecode, connection, schemaFollowPermission, keyUserSupperSetCS, supperSetReq);
            } else {
                result = this.getDataFromDB(connection, schemaFollowPermission, keyUserSupperSetCS, supperSetReq);
            }
            redisTemplate.opsForValue().set(keyUserSupperSetCS, result, timeOut, TimeUnit.SECONDS);

            checkExcuse.stop();
            log.info("Thời gian thực thi code =============> " + checkExcuse.getLastTaskInfo().getTaskName()
                    + ": " + checkExcuse.getLastTaskInfo().getTimeMillis() + " (ms)");

            return result;
        } catch (Exception exception) {
            throw new InternalServerException(exception.getMessage());
        } finally {
//            try {
////                if (rs != null) {
////                    rs.close();
////                }
//                if (connection != null) {
//                    connection.close();
//                }
//            } catch (SQLException ex) {
//                throw new InternalServerException(ex.getMessage());
//            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    public List<Map<String, Object>> getCountTotalPage(String sqlDecode, Connection connection,
                                                       String schemaFollowPermission, String keyUserSupperSetCS,
                                                       SupperSetReq supperSetReq) throws Exception {
        try {
            String keyUserSupperSetPermission = Base64.getEncoder().encodeToString((keyUserSupperSet + schemaFollowPermission).getBytes());
            List<Map<String, Object>> cacheResultSet = (List<Map<String, Object>>) redisTemplate.opsForValue().get(keyUserSupperSetPermission);
            if (cacheResultSet != null && cacheResultSet.size() > 0) {
                return cacheResultSet;
            }

            String tableName = this.getTableName(sqlDecode);
            ResultSetMetaData result = this.queryToGetColumn(tableName, connection);
            List<String> columns = new ArrayList<>();
            int columnCount = result.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String column = result.getColumnName(i);
                columns.add(column);
            }

            if (columns.isEmpty()) {
                return new ArrayList<>();
            }

            StopWatch countDataWatch = new StopWatch();
            countDataWatch.start("countDataWatch");
            String sprate = sqlDecode.substring(sqlDecode.indexOf("FROM") + 5);
            String completeSql = "SELECT count(1) FROM " + sprate;

            try (PreparedStatement preparedStatement = connection.prepareStatement(completeSql)) {
                ResultSet explainResult = preparedStatement.executeQuery();

                countDataWatch.stop();
                log.info("=========> Superset-count-sql " + countDataWatch.getLastTaskInfo().getTaskName() + ": " +
                        countDataWatch.getLastTaskInfo().getTimeMillis() + " (ms)\n" +
                        " =========> " + completeSql);

                //Get total row from DB
                int number = 0;
                while (explainResult.next()) {
                    number = ((Long) explainResult.getObject("count")).intValue();
                }

                List<Map<String, Object>> totalRows = new ArrayList<>();
                //Add data to first-row
                Map<String, Object> elementRow = new HashMap<>();
                for (int thuTuCot = 0; thuTuCot < columns.size(); thuTuCot++) {
                    String column = columns.get(thuTuCot);
                    elementRow.put(column, number);
                }

                totalRows.add(elementRow);
                redisTemplate.opsForValue().set(keyUserSupperSetPermission, totalRows, timeOutToTal, TimeUnit.SECONDS);
                return totalRows;
            }
        } catch (Exception exception) {
            connection.close();
            throw new Exception(exception.getMessage());
        } finally {
            connection.close();
        }
    }

    public List<Map<String, Object>> getCacheResultSet(String keyUserSupperSet) {
        List<Map<String, Object>> cacheResultSet = (List<Map<String, Object>>) redisTemplate.opsForValue().get(keyUserSupperSet);

        return cacheResultSet;
    }

    public void checkExcutionOfQuery(String sqlDecode, Connection connection) throws Exception {
        try {
            String sqlContextEplan = "EXPLAIN ANALYZE " + sqlDecode;

            try (PreparedStatement preparedStatementExplan = connection.prepareStatement(sqlContextEplan)) {
                log.info("Query =========> " + sqlContextEplan);

                // Step 3: Execute the query or update query
                ResultSet explainResult = preparedStatementExplan.executeQuery();
                String resultAnalize = "";
                String ExecutionTime = "";

                while (explainResult.next()) {
                    String query_plan = (String) explainResult.getObject("QUERY PLAN");
                    if (query_plan.contains("Scan")) {
                        resultAnalize = query_plan;
                    } else if (query_plan.contains("Execution Time")) {
                        ExecutionTime = query_plan;
                    }
                }

                log.info("resultAnalize: =====> " + resultAnalize);
                log.info("ExecutionTime: =====> " + ExecutionTime);

                String resultAnalizeCS = resultAnalize;
                resultAnalizeCS = resultAnalizeCS.substring(resultAnalizeCS.indexOf("rows=") + 5);
                resultAnalizeCS = resultAnalizeCS.substring(resultAnalizeCS.indexOf("rows=") + 5);
                resultAnalizeCS = resultAnalizeCS.substring(0, resultAnalizeCS.indexOf(" loops"));

                String executionTimeCS = ExecutionTime;
                executionTimeCS = executionTimeCS.substring(executionTimeCS.indexOf("Execution Time") + 16);
                executionTimeCS = executionTimeCS.substring(0, executionTimeCS.indexOf(" ms"));

//        if (Float.parseFloat(resultAnalizeCS) > maxRow) {
//            connection.close();
//            throw new InternalServerException("Dữ liệu > " + maxRow + ", vui lòng sử dụng phân trang.");
//        }

                if (Float.parseFloat(executionTimeCS) / 1000 > maxExecutionTime) {
                    connection.close();
                    throw new InternalServerException("Thời gian thực thi quá lớn: " + executionTimeCS);
                }
            }
        } catch (Exception exception) {
            connection.close();
            throw new Exception(exception.getMessage());
        } finally {
            connection.close();
        }
    }

    private String getTableName(String sqlContext) {
        String sqlContextUpperCase = sqlContext.toUpperCase();
        sqlContextUpperCase = sqlContextUpperCase.substring(sqlContextUpperCase.indexOf("FROM") + 4);
        sqlContextUpperCase = sqlContextUpperCase.substring(0, sqlContextUpperCase.indexOf("WHERE"));
        sqlContextUpperCase.replaceAll("\\s+", "");
        String tableName = sqlContextUpperCase.replaceAll("\\s+", "").toLowerCase();

        return tableName;
    }

    private String getSchemaFollowPermission(String sqlContext, Connection connection) throws Exception {
        try {
            /* Nếu không phải rule admin thì sẽ cần check phân quyền */
            if (!UserContext.getUserData().getIsAdmin().equals("true")) {
                String tableName = this.getTableName(sqlContext);
                ResultSetMetaData metadata = this.queryToGetColumn(tableName, connection);
                int columnCount = metadata.getColumnCount();
                String labelMaChiNhanh = "";
                String labelMaBuuCuc = "";
                for (int i = 1; i <= columnCount; i++) {
                    String column = metadata.getColumnName(i);
                    if (column.equals(defaultColumnChiNhanh)) {
                        labelMaChiNhanh = column;
                    } else if (column.equals(defaultColumnBuuCuc)) {
                        labelMaBuuCuc = column;
                    }
                }

                if (labelMaChiNhanh.isEmpty() && labelMaBuuCuc.isEmpty()) {
                    connection.close();
                    throw new InternalServerException("Not Safe to make query execution.");
                }

                String permissionQuery = "";
                List<String> listChiNhanh = UserContext.getUserData().getListChiNhanhVeriable();
                String listChiNhanhConvert = "( '" + String.join("','", listChiNhanh) + "' )";
                if (sqlContext.toUpperCase().contains("WHERE")) {
                    permissionQuery = " AND " + defaultColumnChiNhanh + " IN " + listChiNhanhConvert;
                } else {
                    permissionQuery = " WHERE " + defaultColumnChiNhanh + " IN " + listChiNhanhConvert;
                }
                return sqlContext + permissionQuery;
            }

            return sqlContext;
        } catch (Exception exception) {
            throw new Exception(exception.getMessage());
        }
    }

    public ResultSetMetaData queryToGetColumn(String tableName, Connection connection) throws Exception {
        try {
            String sqlContextEplan = "select * from " + tableName + " where false limit 1";

            try (PreparedStatement preparedStatementExplan = connection.prepareStatement(sqlContextEplan)) {
                log.info("Query =========> " + sqlContextEplan);

                // Step 3: Execute the query or update query
                ResultSet explainResult = preparedStatementExplan.executeQuery();
                ResultSetMetaData metadata = explainResult.getMetaData();

                return metadata;
            }


        } catch (Exception exception) {
            throw new Exception(exception.getMessage());
        } finally {
            connection.close();
        }
    }

    public List<Map<String, Object>> getDataFromDB(Connection connection, String sqlDecode, String keyUserSupperSetCS, SupperSetReq supperSetReq) throws Exception {
        List<Map<String, Object>> totalRows = new ArrayList<>();

        if (supperSetReq.getLimit() != 0) {
            totalRows = getDataWithoutBatchSize(connection, sqlDecode, supperSetReq);
        } else {
            totalRows = getDataWithBatchSize(connection, sqlDecode, supperSetReq);
        }

        log.info("Finish All ==============>");
        connection.close();
        redisTemplate.opsForValue().set(keyUserSupperSetCS, totalRows, timeOut, TimeUnit.SECONDS);

        return totalRows;
    }

    public List<Map<String, Object>> getDataWithoutBatchSize(Connection connection, String sqlDecode, SupperSetReq supperSetReq) throws Exception {
        try {
            List<Map<String, Object>> totalRows = new ArrayList<>();
            StopWatch controllExcuse = new StopWatch();
            int offset = supperSetReq.getOffset();
            int limit = supperSetReq.getLimit() >= 5000 ? 5000 : supperSetReq.getLimit();

            controllExcuse.start("executeQuery");
            String sqlContextPerformance = sqlDecode + " OFFSET " + offset + " LIMIT " + limit;

            try (PreparedStatement preparedStatement = connection.prepareStatement(sqlContextPerformance)) {
                log.info("Query =========> " + sqlContextPerformance);

                // Step 3: Execute the query or update query
                ResultSet rs = preparedStatement.executeQuery();
                controllExcuse.stop();
                log.info("Get Data =========> Ok");
                log.info("getDataWithoutBatchSize ======> " + controllExcuse.getLastTaskInfo().getTaskName() + ": " + controllExcuse.getLastTaskInfo().getTimeMillis());

                StopWatch mapData = new StopWatch();
                mapData.start("mapData");
                totalRows = mapDataToReturnResponse(rs);
                mapData.stop();
                log.info("mapData ======> " + mapData.getLastTaskInfo().getTaskName() + ": " + mapData.getLastTaskInfo().getTimeMillis());

                return totalRows;
            }
        } catch (Exception exception) {
            connection.close();
            throw new Exception(exception.getMessage());
        } finally {
            connection.close();
        }
    }

    public List<Map<String, Object>> mapDataToReturnResponse(ResultSet rs) throws SQLException {
        ResultSetMetaData rsmd = rs.getMetaData();
        int numberColumn = rs.getMetaData().getColumnCount();
        List<String> columnName = new ArrayList<>();
        List<Map<String, Object>> totalRows = new ArrayList<>();

        /* Lấy ra columnName của query đang xử lý */
        if (numberColumn > 0 && columnName.isEmpty()) {
            for (int i = 1; i <= numberColumn; i++) {
                columnName.add(rsmd.getColumnName(i));
            }
        }

        /* Xử lý dữ liệu dể map được giá trị của các cột tương ứng */
        log.info("Propcess logic ============> Đang xử lý dữ liệu ");
        while (rs.next()) {
            Map<String, Object> elementRow = new HashMap<>();
            for (int thuTuCot = 0; thuTuCot < numberColumn; thuTuCot++) {
                String column = columnName.get(thuTuCot);
                elementRow.put(column, rs.getObject(column));
            }
            totalRows.add(elementRow);
        }

        return totalRows;
    }

    public List<Map<String, Object>> getDataWithBatchSize(Connection connection, String sqlDecode, SupperSetReq supperSetReq) throws Exception {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        StopWatch controllExcuse = new StopWatch();
        int max = 1000000;
        int limit = 10000;
        totalRows = new ArrayList<>();

        try {
            for (int offset = 0; offset <= max; offset += limit) {
                controllExcuse.start("executeQuery");
                String sqlContextPerformance = sqlDecode + " OFFSET " + offset + " LIMIT " + limit;
                try (PreparedStatement preparedStatement = connection.prepareStatement(sqlContextPerformance)) {
                    log.info("Query =========> " + sqlContextPerformance);

                    // Step 3: Execute the query or update query
                    ResultSet rs = preparedStatement.executeQuery();
                    /* Nếu không có dữ liệu thì đóng kết nối và thoát khỏi vòng lắp */
                    if (!rs.next()) {
                        connection.close();
                        break;
                    }

                    controllExcuse.stop();
                    log.info("Get Data =========> Ok");
                    totalRows = mapDataToReturnResponse(rs);
                }
            }

            return totalRows;
        } catch (Exception exception) {
            connection.close();
            throw new Exception(exception.getMessage());
        } finally {
            connection.close();
        }

    }

    @Override
    public SimpleAPIResponse securityLogin() throws IOException {
        try {
            SuperSetLogin superSetLogin = new SuperSetLogin();
            superSetLogin.setUsername(username);
            superSetLogin.setPassword(password);
            superSetLogin.setProvider(provider);
            String loginParam = new Gson().toJson(superSetLogin);

            OkHttpClient client = new OkHttpClient();
            RequestBody body = RequestBody.create(
                    MediaType.parse("application/json"), loginParam);

            log.info("Domain ==========> " + domainSuperset + "/api/v1/security/login");

            domainSuperset = "http://192.168.12.110:30900";
            Request request = new Request.Builder()
                    .url(domainSuperset + "/api/v1/security/login")
                    .post(body)
                    .build();

            Call call = client.newCall(request);
            Response response = call.execute();
            String response_body_string = response.body().string();

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            SecurityLoginDto luykeResponse = objectMapper.readValue(response_body_string, SecurityLoginDto.class);
            String accessToken = luykeResponse.getAccessToken();

            SuperSetAccessTokenResponse superSetAccessTokenResponse = new SuperSetAccessTokenResponse();
            superSetAccessTokenResponse.setAccessToken(accessToken);

            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            simpleAPIResponse.setData(superSetAccessTokenResponse);
            return simpleAPIResponse;
        } catch (Exception exception) {
            throw new InternalServerException(exception.getMessage());
        }
    }
}
