package nocsystem.indexmanager.servicesIpm.danhgia_nguonluc;

import lombok.RequiredArgsConstructor;
import nocsystem.indexmanager.config.ListContentPageDto;
import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.dao.danhgia_nguonluc.TinhTrangNhanSuDAO;
import nocsystem.indexmanager.dao.danhgia_nguonluc.TongQuanBuuCucDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.*;
import nocsystem.indexmanager.models.Response.danhgianguonluc.DanhSachBcTinhTrangNhanSu;
import nocsystem.indexmanager.models.Response.danhgianguonluc.TienDoXLTonVaDuBaoKLHang3NgayRes;
import nocsystem.indexmanager.request.DanhGiaNguonLucRequest;
import nocsystem.indexmanager.services.danhgia_nguonluc.TongQuanNguonLucService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class TongQuanNguonLucServiceImpl implements TongQuanNguonLucService {
    private final TongQuanBuuCucDAO tongQuanBuuCucDAO;
    private static final String TRUE = "TRUE";

    private final TinhTrangNhanSuDAO tinhTrangNhanSuDAO;

    @Override
    public TongQuanBuuCucResp getTongQuanBuuCuc(DanhGiaNguonLucRequest request) {
        List<String> listChiNhanhSSO = null;
        List<String> listBuuCucSSO = null;
        String chiNhanh = ObjectUtils.isEmpty(request.getChiNhanh()) ? null : request.getChiNhanh();
        String buuCuc = ObjectUtils.isEmpty(request.getBuuCuc()) ? null : request.getBuuCuc();
        TongQuanBuuCucResp tongQuanBuuCucResp = new TongQuanBuuCucResp();
        if (!isAllowViewDashNguonLuc()) {
            return tongQuanBuuCucResp;
        }
        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh())) {
            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
        }
        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc())) {
            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
            listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
        }
        tongQuanBuuCucResp = tongQuanBuuCucDAO.getTongQuanBuuCuc(request.getNgayBaoCao(), chiNhanh, buuCuc, listChiNhanhSSO, listBuuCucSSO);
        return tongQuanBuuCucResp;
    }

    private boolean isAllowViewDashNguonLuc() {
        return TRUE.equalsIgnoreCase(UserContext.getUserData().getIsViewDashNguonLuc()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc());
    }

    @Override
    public SimpleAPIResponseWithSum getThongTinDuBaoChuyenCap(DanhGiaNguonLucRequest request) {
        List<String> listChiNhanhSSO = new ArrayList<>();
        List<String> listBuuCucSSO = new ArrayList<>();
        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();
        List<DubaoChuyenCapResponse> fullList = new ArrayList<>();
        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh())) {
            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
            listBuuCucSSO = new ArrayList<>();
        }
        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc())) {
            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
            listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
        }
        if (isAllowViewDashNguonLuc()) {
            fullList = tongQuanBuuCucDAO.getThongTinDuBaoChuyenCap(request.getNgayBaoCao(), request.getChiNhanh(), request.getBuuCuc(), listChiNhanhSSO, listBuuCucSSO);
        }

        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), fullList.size());

        if (start >= fullList.size()) {
            response.setData(new ListContentPageDto<>(Page.empty(pageable)));
            response.setSummary(fullList.size());
            return response;
        }

        List<DubaoChuyenCapResponse> pageData = fullList.subList(start, end);
        Page<DubaoChuyenCapResponse> pageResult = new PageImpl<>(pageData, pageable, fullList.size());

        response.setData(new ListContentPageDto<>(pageResult));
        response.setSummary(fullList.size());
        return response;
    }

    @Override
    public SimpleAPIResponseWithSum getTTNhanSuDuBaoNhuCau(DanhGiaNguonLucRequest request) {
        List<String> listChiNhanhSSO = new ArrayList<>();
        List<String> listBuuCucSSO = new ArrayList<>();

        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();
        List<DanhSachBcTinhTrangNhanSu> listData = new ArrayList<>();

        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh())) {
            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
            listBuuCucSSO = new ArrayList<>();
        }
        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc())) {
            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
            listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
        }
        System.out.println("list chi nhanh, buu cuc sso: " + listChiNhanhSSO + "__" + listBuuCucSSO);
        if (isAllowViewDashNguonLuc()) {
            listData = tinhTrangNhanSuDAO.getTTNhanSuDuBaoNhuCau(request.getNgayBaoCao(), request.getChiNhanh(), request.getBuuCuc(), listChiNhanhSSO, listBuuCucSSO);
        }

        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), listData.size());

        if (start >= listData.size()) {
            response.setData(new ListContentPageDto<>(Page.empty(pageable)));
            response.setSummary(listData.size());
            return response;
        }

        List<DanhSachBcTinhTrangNhanSu> pageData = listData.subList(start, end);
        Page<DanhSachBcTinhTrangNhanSu> pageResult = new PageImpl<>(pageData, pageable, listData.size());

        response.setData(new ListContentPageDto<>(pageResult));
        response.setSummary(listData.size());
        return response;
    }

    @Override
    public TienDoXLTonVaDuBaoKLHang3NgayRes getTienDoXuLyTonDongNhomHang(LocalDate ngayBaoCao, String chiNhanh, String buuCuc) {
        return tinhTrangNhanSuDAO.getTienDoXuLyTonDongNhomHang(ngayBaoCao, chiNhanh, buuCuc);
    }

    @Override
    public ChiTietSuCoResp getChiTietSuCo(DanhGiaNguonLucRequest request) {
        ChiTietSuCoResp chiTietSuCoResp = new ChiTietSuCoResp();
        if (!isAllowViewDashNguonLuc()) {
            return chiTietSuCoResp;
        }
        String chiNhanh = ObjectUtils.isEmpty(request.getChiNhanh()) ? null : request.getChiNhanh();
        String buuCuc = ObjectUtils.isEmpty(request.getBuuCuc()) ? null : request.getBuuCuc();
        chiTietSuCoResp = tongQuanBuuCucDAO.getChiTietSuCo(request.getNgayBaoCao(), chiNhanh, buuCuc);
        return chiTietSuCoResp;
    }

    @Override
    public ThongTinDieuChuyenResp getThongTinDieuChuyenHoTro(DanhGiaNguonLucRequest request) {
        ThongTinDieuChuyenResp resp = new ThongTinDieuChuyenResp();
        if (!isAllowViewDashNguonLuc()) {
            return resp;
        }
        resp = tongQuanBuuCucDAO.getThongTinDieuChuyenHoTro(request.getNgayBaoCao(), request.getChiNhanh(), request.getBuuCuc());
        return resp;
    }

    @Override
    public BaoCaoChiTietSuCoResp baoCaoSoSanhChiSo(DanhGiaNguonLucRequest request) {
        BaoCaoChiTietSuCoResp resp = new BaoCaoChiTietSuCoResp();
        if (!isAllowViewDashNguonLuc()) {
            return resp;
        }
        resp = tongQuanBuuCucDAO.baoCaoSoSanhChiSo(request.getNgayBaoCao(), request.getChiNhanh(), request.getBuuCuc());
        return resp;
    }

    @Override
    public ChiSoChuyenCapResp chiSoChuyenCapSuCo(DanhGiaNguonLucRequest request) {
        ChiSoChuyenCapResp resp = new ChiSoChuyenCapResp();
        if (!isAllowViewDashNguonLuc()) {
            return resp;
        }
        resp = tongQuanBuuCucDAO.chiSoChuyenCapSuCo(request.getNgayBaoCao(), request.getChiNhanh(), request.getBuuCuc());
        return resp;
    }
}
