package nocsystem.indexmanager.servicesIpm.danhgia_nguonluc;

import lombok.RequiredArgsConstructor;
import nocsystem.indexmanager.config.ListContentPageDto;
import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.dao.danhgia_nguonluc.HieuQuaXeDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.hieuquaxe.*;
import nocsystem.indexmanager.request.hieuquaxe.DanhSachXeChuyRequest;
import nocsystem.indexmanager.request.hieuquaxe.DuBaoHQTXeRequest;
import nocsystem.indexmanager.request.hieuquaxe.TongQuanHQXRequest;
import nocsystem.indexmanager.services.danhgia_nguonluc.HieuQuaXeService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class HieuQuaXeServiceImpl implements HieuQuaXeService {
    private final HieuQuaXeDAO hieuQuaXeDAO;

    private static final String TRUE = "TRUE";

    private boolean isAllowViewDashNguonLuc() {
        return TRUE.equalsIgnoreCase(UserContext.getUserData().getIsViewDashNguonLuc()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc());
    }

    @Override
    public TongQuanHQXRes tongQuanHQX(TongQuanHQXRequest request) {
        List<String> listChiNhanhSSO = null;
        List<String> listBuuCucSSO = null;
        String chiNhanh = ObjectUtils.isEmpty(request.getChiNhanh()) ? null : request.getChiNhanh();
        String dvc = ObjectUtils.isEmpty(request.getDvc()) ? null : request.getDvc();
        TongQuanHQXRes resp = new TongQuanHQXRes();
//        if (!isAllowViewDashNguonLuc()) {
//            return resp;
//        }
//        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
//                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh())) {
//            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
//        }
//        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
//                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc())) {
//            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
//            listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
//        }
        resp = hieuQuaXeDAO.tongQuanHQX(request.getNgayBaoCao(), chiNhanh, dvc, listChiNhanhSSO, listBuuCucSSO);
        return resp;
    }

    @Override
    public CanhBaoHQXTheoChieuRes canhBaoHQXChieu(TongQuanHQXRequest request) {
        List<String> listChiNhanhSSO = null;
        List<String> listBuuCucSSO = null;
        String chiNhanh = ObjectUtils.isEmpty(request.getChiNhanh()) ? null : request.getChiNhanh();
        String buuCuc = ObjectUtils.isEmpty(request.getDvc()) ? null : request.getDvc();

        CanhBaoHQXTheoChieuRes resp = new CanhBaoHQXTheoChieuRes();
        List<CanhBaoHQX> canhBaoHQXRes = new ArrayList<>();
//        if (!isAllowViewDashNguonLuc()) {
//            return resp;
//        }
//        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
//                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh())) {
//            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
//        }
//        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
//                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc())) {
//            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
//            listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
//        }

        resp = hieuQuaXeDAO.canhBaoHQX(request.getNgayBaoCao(), chiNhanh, buuCuc, listChiNhanhSSO, listBuuCucSSO);

        return resp;
    }

    @Override
    public SimpleAPIResponseWithSum danhSachXeChuy(DanhSachXeChuyRequest request) {
        List<String> listChiNhanhSSO = null;
        List<String> listBuuCucSSO = null;

        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();

        String chiNhanh = ObjectUtils.isEmpty(request.getChiNhanh()) ? null : request.getChiNhanh();
        String buuCuc = ObjectUtils.isEmpty(request.getDvc()) ? null : request.getDvc();

        DanhSachXeChuyRes resp = new DanhSachXeChuyRes();
        List<DanhSachXeChuY> fullList = new ArrayList<>();
//        if (!isAllowViewDashNguonLuc()) {
//            return resp;
//        }
//        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoChiNhanh()) ||
//                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvChiNhanh())) {
//            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
//        }
//        if (TRUE.equalsIgnoreCase(UserContext.getUserData().getIsLanhDaoBuuCuc()) ||
//                TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCbnvBuuCuc())) {
//            listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
//            listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
//        }

        fullList = hieuQuaXeDAO.danhSachXeCanChuY(request.getKhungGio(), request.getNgayBaoCao(),request.getType(), request.getChieu(), chiNhanh, buuCuc, listChiNhanhSSO, listBuuCucSSO);

        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), fullList.size());

        if (start >= fullList.size()) {
            response.setData(new ListContentPageDto<>(Page.empty(pageable)));
            response.setSummary(fullList.size());
            return response;
        }
        List<DanhSachXeChuY> pageData = fullList.subList(start, end);
        Page<DanhSachXeChuY> pageResult = new PageImpl<>(pageData, pageable, fullList.size());

        response.setData(new ListContentPageDto<>(pageResult));
        response.setSummary(fullList.size());

        return response;
    }


    @Override
    public DuBaoHQTXeRes duBaoHieuQuaTuyenXe(DuBaoHQTXeRequest request) {
        List<String> listChiNhanhSSO = null;
        List<String> listBuuCucSSO = null;

        String chiNhanh = ObjectUtils.isEmpty(request.getChiNhanh()) ? null : request.getChiNhanh();
        String buuCuc = ObjectUtils.isEmpty(request.getDvc()) ? null : request.getDvc();

        DuBaoHQTXeRes resp = new DuBaoHQTXeRes();
        List<DuBaoHieuQuaTuyen> listData = hieuQuaXeDAO.duBaoHieuQuaTuyenXe(request.getNgayBaoCao(), request.getMaChuyenxe(), chiNhanh, buuCuc, listChiNhanhSSO, listBuuCucSSO);

        resp.setDuBaoHieuQuaTuyens(listData);
        return resp;
    }

    @Override
    public SimpleAPIResponseWithSum topCoHoiBanHang(DuBaoHQTXeRequest request) {
        List<String> listChiNhanhSSO = null;
        List<String> listBuuCucSSO = null;
        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();

        String chiNhanh = ObjectUtils.isEmpty(request.getChiNhanh()) ? null : request.getChiNhanh();
        String buuCuc = ObjectUtils.isEmpty(request.getDvc()) ? null : request.getDvc();
        TopCoHoiBanHangRes resp = new TopCoHoiBanHangRes();

        List<TopCoHoiBanHang> data = new ArrayList<>();
        data = hieuQuaXeDAO.topCoHoiBanHang(request.getNgayBaoCao(), request.getMaChuyenxe(), chiNhanh, buuCuc, listChiNhanhSSO, listBuuCucSSO);

        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), data.size());

        if (start >= data.size()) {
            response.setData(new ListContentPageDto<>(Page.empty(pageable)));
            response.setSummary(data.size());
            return response;
        }

        List<TopCoHoiBanHang> pageData = data.subList(start, end);
        Page<TopCoHoiBanHang> pageResult = new PageImpl<>(pageData, pageable, data.size());

        response.setData(new ListContentPageDto<>(pageResult));
        response.setSummary(data.size());

        return response;
    }
}
