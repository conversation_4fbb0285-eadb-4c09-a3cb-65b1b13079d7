package nocsystem.indexmanager.servicesIpm.DashBuuCucServiceImpl;

import nocsystem.indexmanager.entity.ChiSoVersion;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.DashBuuCuc.*;
import nocsystem.indexmanager.repositories.DashBuuCucRepository;
import nocsystem.indexmanager.repositories.baoCaoTon.TonPhatJdbc;
import nocsystem.indexmanager.services.ChiSoVersion.ChiSoVersionPostgresRepo;
import nocsystem.indexmanager.services.DashBuuCucService.DashBuuCucTonPhatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;

@Service
public class DashBuuCucTonPhatServiceImpl<T> implements DashBuuCucTonPhatService {

    @Autowired
    TonPhatJdbc tonPhatJdbc;
    @Autowired
    private DashBuuCucRepository dashBuuCucRepository;
    @Autowired
    private ChiSoVersionPostgresRepo chiSoVersionRepository;
    private final String TRUE = "true";
    private final String MA_CHISO = "ton_phat";
    @Override
    public ListContentPageCustomDto<DashBuuCucTonPhatDisplayDto, DashboardTongTonPhatdto> findAllByRoleTCT(DashTonPhatParam tonPhatParam) {
        List<DashBuuCucTonPhatDisplayDto> tonPhatDisplayDtos = new ArrayList<>();
        Page<DashBuuCucTonPhatDisplayDto> tonPhatDisplayDtoPage = new PageImpl<>(tonPhatDisplayDtos);
        ListContentPageCustomDto<DashBuuCucTonPhatDisplayDto, DashboardTongTonPhatdto> content = new ListContentPageCustomDto<>();
        DashboardTongTonPhatdto tongTonPhatdto = new DashboardTongTonPhatdto();
        List<String> chiNhanhList = new ArrayList<>();
        List<String> buuCucList = new ArrayList<>();
        List<String> chiNhanhChon = tonPhatParam.getChiNhanhPhat();
        List<String> buuCucChon = tonPhatParam.getBuuCuc();

        LocalDate ngayBaocao = LocalDate.parse(tonPhatParam.getNgayBaoCao());
        long version = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaocao, MA_CHISO)
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (chiNhanhChon.isEmpty()) {
                chiNhanhList = new ArrayList<>();
            } else {
                chiNhanhList = chiNhanhChon;
            }
            if (buuCucChon.isEmpty()) {
                buuCucList = new ArrayList<>();
            } else {
                buuCucList = buuCucChon;
            }
        }
        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            chiNhanhList = UserContext.getUserData().getListChiNhanhVeriable();
            buuCucList = UserContext.getUserData().getListBuuCucVeriable();
            if (!chiNhanhChon.isEmpty()) {
                chiNhanhList.retainAll(chiNhanhChon);
            } else {
                chiNhanhList = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (!buuCucChon.isEmpty()) {
                buuCucList.retainAll(buuCucChon);
                if(buuCucChon.get(0).equalsIgnoreCase("CHUA_XAC_DINH"))
                    buuCucList = List.of("CHUA_XAC_DINH");
            } else {
                buuCucList = UserContext.getUserData().getListBuuCucVeriable();
                if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE")) {
                    buuCucList = new ArrayList<>();
                }
            }
        }

        //pagable logic
        int pageNumber = tonPhatParam.getPage() > 0 ? tonPhatParam.getPage() - 1 : 0;
        int pageSize = tonPhatParam.getPageSize();
        Pageable page;
        switch (tonPhatParam.getOrder().toLowerCase(Locale.ROOT)) {
            case "asc":
                page = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, tonPhatParam.getOrderBy().toLowerCase(Locale.ROOT)));
                break;
            case "desc":
                page = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC, tonPhatParam.getOrderBy().toLowerCase(Locale.ROOT)));
                break;
            default:
                page = PageRequest.of(pageNumber, pageSize);
                break;
        }

        boolean isAdminOrLeader = UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
                || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE);


        if (isAdminOrLeader) {
            String nguongTon = tonPhatParam.getNguongTon();
            String mauLuaChon = tonPhatParam.getMauLuaChon();
            String loaiDichVu = tonPhatParam.getLoaiDichVu();
            String tuyenBuuTa = tonPhatParam.getTuyenBuuTa();
            String vungCon = tonPhatParam.getVungCon();
            String maDoiTac = tonPhatParam.getMaDoiTac();
            String loaiHH = tonPhatParam.getLoaiHH();
            String khDacThuGui = tonPhatParam.getKhDacThuGui();
            String khDacThuNhan = tonPhatParam.getKhDacThuNhan();
            String trangThai = tonPhatParam.getTrangThai();

            if (chiNhanhChon.isEmpty() && buuCucChon.isEmpty()) {
                tonPhatDisplayDtoPage = dashBuuCucRepository.findAllNew(ngayBaocao, chiNhanhList, buuCucList, nguongTon,
                        mauLuaChon, loaiDichVu, tuyenBuuTa, trangThai, vungCon, page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, version);
            } else if (!chiNhanhChon.isEmpty() && buuCucChon.isEmpty()) {
                tonPhatDisplayDtoPage = dashBuuCucRepository.findAllNew1(ngayBaocao, chiNhanhList, buuCucList, nguongTon,
                        mauLuaChon, loaiDichVu, tuyenBuuTa, trangThai, vungCon, page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, version);
            } else if (!chiNhanhChon.isEmpty() && !buuCucChon.isEmpty() && tuyenBuuTa == null) {
                tonPhatDisplayDtoPage = dashBuuCucRepository.findAllNew2(ngayBaocao, chiNhanhList, buuCucList, nguongTon,
                        mauLuaChon, loaiDichVu, tuyenBuuTa, trangThai, vungCon, page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, version);
            } else {
                tonPhatDisplayDtoPage = dashBuuCucRepository.findAllNew3(ngayBaocao, chiNhanhList, buuCucList, nguongTon,
                        mauLuaChon, loaiDichVu, tuyenBuuTa, trangThai, vungCon, page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, version);
            }

            DashBuuCucTonPhatDisplayDto sumDisplayDto = dashBuuCucRepository.findSumNew(ngayBaocao, chiNhanhList, buuCucList, nguongTon,
                    mauLuaChon, loaiDichVu, tuyenBuuTa, trangThai, vungCon, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, version);

            tongTonPhatdto = new DashboardTongTonPhatdto("Tổng", sumDisplayDto.getVang(), sumDisplayDto.getDo1(), sumDisplayDto.getDo2(),
                    sumDisplayDto.getDo3(), sumDisplayDto.getDo4(), sumDisplayDto.getDo5(), sumDisplayDto.getDo6(),
                    sumDisplayDto.getDo7(), sumDisplayDto.getDo8(), sumDisplayDto.getDo9(), sumDisplayDto.getChuaXacDinh(),
                    sumDisplayDto.getTong());
        }
        content = new ListContentPageCustomDto<>(tonPhatDisplayDtoPage.getTotalElements(), tonPhatDisplayDtoPage.getNumber(), tonPhatDisplayDtoPage.getSize(), tonPhatDisplayDtoPage.getContent(), tongTonPhatdto);
        return content;
    }

    @Override
    public List<String> findAllListBuuTa(DashTonPhatParam tonPhatParam) {
        LocalDate ngayBaoCao = LocalDate.parse(tonPhatParam.getNgayBaoCao());
        List<String> chiNhanh = tonPhatParam.getChiNhanhPhat();
        List<String> buuCuc = tonPhatParam.getBuuCuc();
        String nguongTon = tonPhatParam.getNguongTon();
        String mauLuaChon = tonPhatParam.getMauLuaChon();
        String trangThai = tonPhatParam.getTrangThai();

        long version = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaoCao, MA_CHISO)
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        return dashBuuCucRepository.findAllTuyenBuuTa(ngayBaoCao, chiNhanh, buuCuc, nguongTon, mauLuaChon, tonPhatParam.getLoaiDichVu(), tonPhatParam.getTuyenBuuTa(), trangThai,version);
    }


    @Override
    public Page<DashBuuCucTonPhatChiTietDisplayDto> findAll1(DashTonPhatParam tonPhatParam) {
        List<DashBuuCucTonPhatChiTietDisplayDto> tonPhatDetailDisplayDtos = new ArrayList<>();
        Page<DashBuuCucTonPhatChiTietDisplayDto> tonPhatDetailDisplayDtoPage = new PageImpl<>(tonPhatDetailDisplayDtos);

        List<String> chiNhanhList = new ArrayList<>();
        List<String> buuCucList = new ArrayList<>();
        List<String> chiNhanhChon = tonPhatParam.getChiNhanhPhat();
        List<String> buuCucChon = tonPhatParam.getBuuCuc();

        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (chiNhanhChon.isEmpty()) {
                chiNhanhList = new ArrayList<>();
            } else {
                chiNhanhList = chiNhanhChon;
            }
            if (buuCucChon.isEmpty()) {
                buuCucList = new ArrayList<>();
            } else {
                buuCucList = buuCucChon;
            }
        }

        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())) {
            chiNhanhList = UserContext.getUserData().getListChiNhanhVeriable();
            buuCucList = UserContext.getUserData().getListBuuCucVeriable();
            if (!chiNhanhChon.isEmpty()) {
                chiNhanhList.retainAll(chiNhanhChon);
            }
            if (!buuCucChon.isEmpty()) {
                buuCucList.retainAll(buuCucChon);
                if(buuCucChon.get(0).equalsIgnoreCase("CHUA_XAC_DINH"))
                    buuCucList = List.of("CHUA_XAC_DINH");
            }
            else if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE")) {
                buuCucList = new ArrayList<>();
            }
        }
        long version = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(LocalDate.parse(tonPhatParam.getNgayBaoCao()), MA_CHISO)
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        int pageNumber = tonPhatParam.getPage() > 0 ? tonPhatParam.getPage() - 1 : 0;
        int pageSize = tonPhatParam.getPageSize();
        Pageable pageable = null;
        pageable = PageRequest.of(pageNumber, pageSize);
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
                || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            tonPhatDetailDisplayDtoPage = dashBuuCucRepository.findAll1(LocalDate.parse(tonPhatParam.getNgayBaoCao()), chiNhanhList, buuCucList, tonPhatParam.getTuyenBuuTa(),
                    tonPhatParam.getNguongTon(), tonPhatParam.getMauLuaChon(), tonPhatParam.getLoaiDichVu(), tonPhatParam.getTrangThai(), tonPhatParam.getVungCon(),
                    pageable, tonPhatParam.getMaDoiTac(), tonPhatParam.getLoaiHH(), tonPhatParam.getKhDacThuGui(), tonPhatParam.getKhDacThuNhan(),version
            );
        }
        return tonPhatDetailDisplayDtoPage;
    }

    @Override
    public List<Map<String, Object>> findAllList(DashTonPhatParam tonPhatParam) throws IllegalAccessException {

        List<DashBuuCucTonPhatChiTietExcelDto> tonPhatDetailDisplayDtos = new ArrayList<>();

        List<String> chiNhanhList = new ArrayList<>();
        List<String> buuCucList = new ArrayList<>();
        List<String> chiNhanhChon = tonPhatParam.getChiNhanhPhat();
        List<String> buuCucChon = tonPhatParam.getBuuCuc();

        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
            || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (chiNhanhChon.isEmpty()) {
                chiNhanhList = new ArrayList<>();
            } else {
                chiNhanhList = chiNhanhChon;
            }
            if (buuCucChon.isEmpty()) {
                buuCucList = new ArrayList<>();
            } else {
                buuCucList = buuCucChon;
            }
        }
        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase(TRUE)) {
            chiNhanhList = UserContext.getUserData().getListChiNhanhVeriable();
            buuCucList = UserContext.getUserData().getListBuuCucVeriable();
            if (!chiNhanhChon.isEmpty()) {
                chiNhanhList.retainAll(chiNhanhChon);
            }
            if (!buuCucChon.isEmpty()) {
                buuCucList.retainAll(buuCucChon);
                if(buuCucChon.get(0).equalsIgnoreCase("CHUA_XAC_DINH"))
                    buuCucList = List.of("CHUA_XAC_DINH");
            }
            else if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE")) {
                buuCucList = new ArrayList<>();
            }
        }

        long version = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(LocalDate.parse(tonPhatParam.getNgayBaoCao()), MA_CHISO)
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
                || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            tonPhatDetailDisplayDtos = dashBuuCucRepository.findAllListExportExcel(LocalDate.parse(tonPhatParam.getNgayBaoCao()), chiNhanhList, buuCucList, tonPhatParam.getTuyenBuuTa(),
                    tonPhatParam.getNguongTon(), tonPhatParam.getMauLuaChon(), tonPhatParam.getLoaiDichVu(), tonPhatParam.getVungCon(),
                    tonPhatParam.getTrangThai(), tonPhatParam.getMaDoiTac(), tonPhatParam.getLoaiHH(), tonPhatParam.getKhDacThuGui(), tonPhatParam.getKhDacThuNhan(),version
            );
        }
        return tryTest((List<T>) tonPhatDetailDisplayDtos);
    }


    public void findAllListToiUuExcel(HttpServletResponse response, DashTonPhatParam tonPhatParam) throws SQLException {


        List<String> chiNhanhList = new ArrayList<>();
        List<String> buuCucList = new ArrayList<>();
        List<String> chiNhanhChon = tonPhatParam.getChiNhanhPhat();
        List<String> buuCucChon = tonPhatParam.getBuuCuc();

        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            if (chiNhanhChon.isEmpty()) {
                chiNhanhList = new ArrayList<>();
            } else {
                chiNhanhList = chiNhanhChon;
            }
            if (buuCucChon.isEmpty()) {
                buuCucList = new ArrayList<>();
            } else {
                buuCucList = buuCucChon;
            }
        }
        if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase(TRUE)) {
            chiNhanhList = UserContext.getUserData().getListChiNhanhVeriable();
            buuCucList = UserContext.getUserData().getListBuuCucVeriable();
            if (!chiNhanhChon.isEmpty()) {
                chiNhanhList.retainAll(chiNhanhChon);
            }
            if (!buuCucChon.isEmpty()) {
                buuCucList.retainAll(buuCucChon);
                if(buuCucChon.get(0).equalsIgnoreCase("CHUA_XAC_DINH"))
                    buuCucList = List.of("CHUA_XAC_DINH");
            }
            else if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE")) {
                buuCucList = new ArrayList<>();
            }
        }

        long version = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(LocalDate.parse(tonPhatParam.getNgayBaoCao()), MA_CHISO)
                .map(ChiSoVersion::getVersion)
                .orElse(0L);
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase(TRUE)
                || UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase(TRUE)
                || TRUE.equalsIgnoreCase(UserContext.getUserData().getIsRoleCtyLog())
                || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase(TRUE)) {
            tonPhatJdbc.exportExcelChiTietTonPhat(response, tonPhatParam.getNgayBaoCao(), chiNhanhList, buuCucList, tonPhatParam.getTuyenBuuTa(),
                    tonPhatParam.getNguongTon(), tonPhatParam.getMauLuaChon(), tonPhatParam.getLoaiDichVu(), tonPhatParam.getVungCon(),
                    tonPhatParam.getTrangThai(), tonPhatParam.getMaDoiTac(), tonPhatParam.getLoaiHH(), tonPhatParam.getKhDacThuGui(), tonPhatParam.getKhDacThuNhan(),version
            );
        }
    }


    @Override
    public void exportExcelChiTietTonPhat(HttpServletResponse response, DashTonPhatParam tonPhatParam) throws IOException, IllegalAccessException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=chiTietBuuGuiTonPhat.xlsx";
        response.setHeader(headerKey, headerValue);

        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("STT", "STT");
        mapHeader.put("maPhieuGui", "MA_PHIEUGUI");
        mapHeader.put("tinhNhan", "TINH_NHAN");
        mapHeader.put("huyenNhan", "HUYEN_NHAN");
        mapHeader.put("tenHuyenNhan", "TEN_HUYEN_NHAN");
        mapHeader.put("ttktFrom", "TTKT_FROM");
        mapHeader.put("chiNhanhPhat", "TINH_PHAT");
        mapHeader.put("huyenPhat", "HUYEN_PHAT");
        mapHeader.put("tenHuyenPhat", "TEN_HUYEN_PHAT");
        mapHeader.put("ttktTo", "TTKT_TO");
        mapHeader.put("maDvViettel", "MA_DV_VIETTEL");
        mapHeader.put("maBuuCucGoc", "MA_BUUCUC_GOC");
        mapHeader.put("timeTacDong", "TIME_TAC_DONG");
        mapHeader.put("trangThai", "TRANG_THAI");
        mapHeader.put("maBuuCucHT", "MA_BUUCUC_HT");
        mapHeader.put("chiNhanhHT", "CHI_NHANH_HT");
        mapHeader.put("maDoiTac", "MA_DOITAC");
        mapHeader.put("maKHGui", "MA_KHGUI");
        mapHeader.put("buuCucPhat", "MA_BUUCUC_PHAT");
        mapHeader.put("timePCP", "TIME_PCP");
        mapHeader.put("ngayguiBP", "NGAY_GUI_BP");
        mapHeader.put("loaiPG", "LOAI_PG");
        mapHeader.put("lanPhat", "LAN_PHAT");
        mapHeader.put("tuyenBuuTaPhat", "BUU_TA_PHAT");
        mapHeader.put("tienCOD", "TIEN_COD");
        mapHeader.put("tgQuyDinh", "TG_QUYDINH");
        mapHeader.put("tgTTLuyKe", "TG_TT_LUYKE");
        mapHeader.put("tgChenhLech", "TG_CHENHLECH");
        mapHeader.put("nguongTon", "IS_CHECKED");
        mapHeader.put("loaiPCP", "LOAI_PCP");
        mapHeader.put("ngayBaoCao", "NGAY_BAOCAO");
        mapHeader.put("loaiCanhBao", "LOAI_CANH_BAO");
        mapHeader.put("dgMocLM", "DG_MOC_LM");
        mapHeader.put("loaiDichVu", "NHOM_DV");

        mapHeader.put("trongLuong", "TRONG_LUONG");
        mapHeader.put("tienCuoc", "TIEN_CUOC");
        mapHeader.put("loaiHangHoa", "HANG_HOA");
        mapHeader.put("khDacThuGui", "KH_DAC_THU_GUI");
        mapHeader.put("khDacThuNhan", "KH_DAC_THU_NHAN");
        mapHeader.put("idPhuongXaPhat", "ID_PHUONGXA_PHAT");
        mapHeader.put("tenPhuongXaPhat", "TEN_PHUONGXA_PHAT");
        mapHeader.put("maDichVuCongThem", "MA_DV_CONG_THEM");
        mapHeader.put("tgTonPcpCuoiCung", "TG_TON_PCP_CUOI_CUNG");
        mapHeader.put("timePcpDauTien", "TIME_PCP_DAU_TIEN");
        mapHeader.put("loaiDon", "loai_don");



        List<String> header1 = Arrays.asList(
                "STT",
                "MA_PHIEUGUI",
                "TINH_NHAN",
                "HUYEN_NHAN",
                "TEN_HUYEN_NHAN",
                "TTKT_FROM",
                "TINH_PHAT",
                "HUYEN_PHAT",
                "TEN_HUYEN_PHAT",
                "TTKT_TO",
                "MA_DV_VIETTEL",
                "MA_BUUCUC_GOC",
                "TIME_TAC_DONG",
                "TRANG_THAI",
                "MA_BUUCUC_HT",
                "CHI_NHANH_HT",
                "MA_DOITAC",
                "MA_KHGUI",
                "MA_BUUCUC_PHAT",
                "TIME_PCP",
                "NGAY_GUI_BP",
                "LOAI_PG",
                "LAN_PHAT",
                "BUU_TA_PHAT",
                "TIEN_COD",
                "TG_QUYDINH",
                "TG_TT_LUYKE",
                "TG_CHENHLECH",
                "IS_CHECKED",
                "LOAI_PCP",
                "NGAY_BAOCAO",
                "LOAI_CANH_BAO",
                "DG_MOC_LM",
                "NHOM_DV",
                "TRONG_LUONG",
                "TIEN_CUOC",
                "HANG_HOA",
                "KH_DAC_THU_GUI",
                "KH_DAC_THU_NHAN",
                "ID_PHUONGXA_PHAT",
                "TEN_PHUONGXA_PHAT",
                "MA_DV_CONG_THEM",
                "TG_TON_PCP_CUOI_CUNG",
                "TIME_PCP_DAU_TIEN",
                "loai_don"
                );

        List<String> header2 = Arrays.asList(
                "STT",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "ttktFrom",
                "chiNhanhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "ttktTo",
                "maDvViettel",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHT",
                "chiNhanhHT",
                "maDoiTac",
                "maKHGui",
                "buuCucPhat",
                "timePCP",
                "ngayguiBP",
                "loaiPG",
                "lanPhat",
                "tuyenBuuTaPhat",
                "tienCOD",
                "tgQuyDinh",
                "tgTTLuyKe",
                "tgChenhLech",
                "nguongTon",
                "loaiPCP",
                "ngayBaoCao",
                "loaiCanhBao",
                "dgMocLM",
                "loaiDichVu",
                "trongLuong",
                "tienCuoc",
                "loaiHangHoa",
                "khDacThuGui",
                "khDacThuNhan",
                "idPhuongXaPhat",
                "tenPhuongXaPhat",
                "maDichVuCongThem",
                "tgTonPcpCuoiCung",
                "timePcpDauTien",
                "loaiDon"
        );


        ExportExcelTemplateBuuCucTonPhat logTemplate = new ExportExcelTemplateBuuCucTonPhat(
                findAllList(tonPhatParam),
                mapHeader,
                header1,
                header2,
                "ChiTietBuuGuiTonPhat"
        );
        logTemplate.exportDataToExcel(response);
    }

    public List<Map<String, Object>> tryTest(List<T> logSanLuongChiaTayList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();

        if (!logSanLuongChiaTayList.isEmpty()) {
            int i = 0;
            for (T x : logSanLuongChiaTayList) {
                DashBuuCucTonPhatChiTietExcelDto x1 = (DashBuuCucTonPhatChiTietExcelDto) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }
}
