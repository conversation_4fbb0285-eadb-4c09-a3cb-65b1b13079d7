package nocsystem.indexmanager.servicesIpm.TopChiNhanh;

import nocsystem.indexmanager.constants.TopChiNhanhConstant;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.TopChiNhanh.TopChiNhanhResp;
import nocsystem.indexmanager.models.TopChiNhanh.TopChiNhanh;
import nocsystem.indexmanager.models.TopChiNhanh.TopChiNhanhDTO;
import nocsystem.indexmanager.repositories.TopChiNhanhRepository;
import nocsystem.indexmanager.request.TopChiNhanhRequest;
import nocsystem.indexmanager.services.TopChiNhanh.TopChiNhanhService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TopChiNhanhServiceImpl implements TopChiNhanhService {
    private final TopChiNhanhRepository topChiNhanhRepository;
    private final static String TRUE = "true";
    private final static String FILTER_BY_ASC = "ascend";

    public TopChiNhanhServiceImpl(TopChiNhanhRepository topChiNhanhRepository) {
        this.topChiNhanhRepository = topChiNhanhRepository;
    }

    @Override
    public TopChiNhanhResp getTop10ChiNhanhBuuCuc(TopChiNhanhRequest request) {
        TopChiNhanhResp resp = new TopChiNhanhResp();
        if(TRUE.equalsIgnoreCase(UserContext.getUserData().getIsPermissionViewTopChiNhanhCLDV())) {
            resp = processGetTopChiNhanhBuuCuc(request);
        }
        return resp;
    }

    private TopChiNhanhResp processGetTopChiNhanhBuuCuc(TopChiNhanhRequest request) {
        List<TopChiNhanh> topChiNhanhList = new ArrayList<>();
        if (ObjectUtils.isEmpty(request.getChiNhanh())) {
            topChiNhanhList = topChiNhanhRepository.findTop10ChiNhanh(request.getNgayBaoCao(), ObjectUtils.isNotEmpty(request.getFilterSort()) ? request.getFilterSort() : FILTER_BY_ASC, request.getLuyKe());
        } else if(ObjectUtils.isEmpty(request.getBuuCuc())) {
            topChiNhanhList = topChiNhanhRepository.findTop10BuuCuc(request.getNgayBaoCao(), request.getChiNhanh(), ObjectUtils.isNotEmpty(request.getFilterSort()) ? request.getFilterSort() : FILTER_BY_ASC, request.getLuyKe());
        }
        if(CollectionUtils.isEmpty(topChiNhanhList)) {
            return null;
        }
        List<TopChiNhanhDTO> topChiNhanhDTOList = toDTOs(topChiNhanhList);
        return convertTopChiNhanhListToResponse(topChiNhanhDTOList);
    }

    private TopChiNhanhResp convertTopChiNhanhListToResponse(List<TopChiNhanhDTO> topChiNhanhDTOList) {
        TopChiNhanhResp resp = new TopChiNhanhResp();
        resp.setThuThanhCongList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.THU_TC));
        resp.setThuThanhCongDGList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.THU_TC_DG));
        resp.setPhatThanhCongList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.PHAT_TC));
        resp.setPhatThanhCongDGList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.PHAT_TC_DG));
        resp.setHoanList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.HOAN));
        resp.setNoiTinhList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.NOI_TINH));
//        resp.setFirstMileList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.TT_FM));
//        resp.setLastMileList(getListTopChiNhanhBuuCucByType(topChiNhanhDTOList, TopChiNhanhConstant.TT_LM));
        return resp;
    }

    private List<TopChiNhanhDTO> getListTopChiNhanhBuuCucByType(List<TopChiNhanhDTO> topChiNhanhDTOList, String type) {
        return topChiNhanhDTOList.stream().filter(item -> type.equalsIgnoreCase(item.getType())).collect(Collectors.toList());
    }

    private List<TopChiNhanhDTO> toDTOs(List<TopChiNhanh> topChiNhanhList) {
        if(CollectionUtils.isEmpty(topChiNhanhList)){
            return null;
        }
        List<TopChiNhanhDTO> topChiNhanhDTOList = new ArrayList<>();
        topChiNhanhList.forEach(topChiNhanh -> {
            topChiNhanhDTOList.add(toDTO(topChiNhanh));
        });
        return topChiNhanhDTOList;
    }

    private TopChiNhanhDTO toDTO(TopChiNhanh topChiNhanh) {
        if(ObjectUtils.isEmpty(topChiNhanh)) {
            return null;
        }
        TopChiNhanhDTO topChiNhanhDTO = new TopChiNhanhDTO();
        topChiNhanhDTO.setNgayBaoCao(topChiNhanh.getNgayBaoCao());
        topChiNhanhDTO.setMaChiNhanh(topChiNhanh.getMaChiNhanh());
        topChiNhanhDTO.setMaBuuCuc(topChiNhanh.getMaBuuCuc());
        topChiNhanhDTO.setTlThucHien(roundToTwoDecimalPlaces(topChiNhanh.getTlThucHien()));
        topChiNhanhDTO.setTlHoanThanh(roundToTwoDecimalPlaces(topChiNhanh.getTlHoanThanh()));
        topChiNhanhDTO.setKpi(roundToTwoDecimalPlaces(topChiNhanh.getKpi()));
        topChiNhanhDTO.setThoiGianToanTrinh(roundToTwoDecimalPlaces(topChiNhanh.getThoiGianToanTrinh()));
        topChiNhanhDTO.setType(topChiNhanh.getType());
        topChiNhanhDTO.setLuyKe(topChiNhanh.getLuyKe());
        return topChiNhanhDTO;
    }
    public static Float roundToTwoDecimalPlaces(Float number) {
        if(ObjectUtils.isEmpty(number)) {
            return null;
        }
        DecimalFormat df = new DecimalFormat("#.##");
        String formattedNumber = df.format(number);
        return Float.parseFloat(formattedNumber);
    }
}
