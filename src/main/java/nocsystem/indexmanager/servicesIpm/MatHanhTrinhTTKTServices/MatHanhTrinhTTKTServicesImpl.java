package nocsystem.indexmanager.servicesIpm.MatHanhTrinhTTKTServices;

import nocsystem.indexmanager.common.FindProperty;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.MatHanhTrinhDetailBodyDto;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.MatHanhTrinhListBodyDto;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.ResponseMatHanhTrinhDto;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.ResponseMatHanhTrinhDetailDto;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.repositories.MatHanhTrinhTTKT.MatHanhTrinhTTKTRepository;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.ResponseMatHanhTrinhTTKTList;
import nocsystem.indexmanager.services.MatHanhTrinhTTKT.MatHanhTrinhTTKTServices;
import org.apache.hadoop.util.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class MatHanhTrinhTTKTServicesImpl<T> extends AbstractDao implements MatHanhTrinhTTKTServices {
    public List<T> getDataSort(List<T> l, String propertySort, String typeSort, FindProperty<T> findProperty) throws NoSuchMethodException {
        if (l.isEmpty()) {
            return new ArrayList<>();
        }
        return findProperty.getDataSort(l, propertySort, typeSort, findProperty.getObjectType());
    }

    public String convertListParamsToString(List<String> listString) {
        if (listString == null || listString.isEmpty()) {
            return null;
        }

        String result = listString.stream().collect(Collectors.joining("', '", "('", "')"));
        return result;
    }

    public String convertListParamsNumberToString(List<Integer> listNumber) {
        if (listNumber == null || listNumber.isEmpty()) {
            return null;
        }
        String result = "(" + StringUtils.join(", ", listNumber) + ")";
        return result;
    }

    public List<ResponseMatHanhTrinhDto> sortList(List<ResponseMatHanhTrinhDto> l, String typeSort, String sortBy) throws NoSuchMethodException {
        if (l == null || l.isEmpty()) {
            return new ArrayList<>();
        }

        if (!(typeSort == null || typeSort.equals(""))) {
            if (!(sortBy == null || sortBy.equals(""))) {
                FindProperty<ResponseMatHanhTrinhDto> cache = new FindProperty<>(ResponseMatHanhTrinhDto.class);
                MatHanhTrinhTTKTServicesImpl<ResponseMatHanhTrinhDto> d = new MatHanhTrinhTTKTServicesImpl<>();
                l = d.getDataSort(l, sortBy, typeSort, cache);
            }
        }
        return l;
    }

    public Long getVersionMax(String tenBaoCao, LocalDate ngayBaoCao) {

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlMax = "";
        if (ngayBaoCao != null) {
            sqlMax = "SELECT max(version) as version from chiso_version where ma_chiso = ?";
        } else return null;
        Long maxVersion = null;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            stmt.setString(1, tenBaoCao);

            rs = stmt.executeQuery();
            if (rs.next()) {
                maxVersion = rs.getLong("version");
            }
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        return maxVersion;
    }

    private boolean isViewMatHanhTrinhTTKT() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("LANH_DAO_TCT") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("LANH_DAO_TTVH") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("CHUYEN_QUAN_TCT_TTVH") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_LANHDAO_CONGTYLOG") ||
                UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_CONGTY_LOG");
    }

    @Override
    public ResponseMatHanhTrinhTTKTList getList(MatHanhTrinhListBodyDto body) {
        if (!isViewMatHanhTrinhTTKT()) {
            return new ResponseMatHanhTrinhTTKTList();
        }
        LocalDate localDate = LocalDateTime.now().toLocalDate();
        Long maxVerSionHbase = getVersionMax("nguy_co_mat_hanh_trinh", localDate);

        String sqlForMatHanhTrinhList = MatHanhTrinhTTKTRepository.getQueryMatHanhTrinhList(convertListParamsToString(body.chiNhanhPhat),
                convertListParamsToString(body.loaiHang), convertListParamsToString(body.hangGiaTri),
                convertListParamsToString(body.nhomDichVu), convertListParamsToString(body.loaiKhachHang), maxVerSionHbase);


        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {


            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlForMatHanhTrinhList);
            rs = stmt.executeQuery();
            List<ResponseMatHanhTrinhDto> resList = new ArrayList<>();
            Long sumDuoi1h = 0L;
            Long sumDuoi2h = 0L;
            Long sumTren2h = 0L;
            Long sum = 0L;
            ResponseMatHanhTrinhTTKTList resSum = new ResponseMatHanhTrinhTTKTList();

            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm dd/MM/yyyy");

            while (rs.next()) {
                ResponseMatHanhTrinhDto tempRes = new ResponseMatHanhTrinhDto();
                tempRes.setMaTTKT(rs.getString("ma_buucuc"));

                tempRes.setDuoi1h(rs.getLong("duoi1h"));
                tempRes.setDuoi2h(rs.getLong("duoi2h"));
                tempRes.setTren2h(rs.getLong("tren2h"));
                tempRes.setTong(tempRes.getDuoi1h() + tempRes.getDuoi2h() + tempRes.getTren2h());
                Date date = new Date(rs.getLong("updated_at"));
                tempRes.setNgayUpdate(sdf.format(date));


                resList.add(tempRes);
                sumDuoi1h += tempRes.getDuoi1h();
                sumDuoi2h += tempRes.getDuoi2h();
                sumTren2h += tempRes.getTren2h();
                sum += tempRes.getTong();
            }

            ResponseMatHanhTrinhDto tempRes = new ResponseMatHanhTrinhDto();
            tempRes.setMaTTKT("Tong");
            tempRes.setDuoi1h(sumDuoi1h);
            tempRes.setDuoi2h(sumDuoi2h);
            tempRes.setTren2h(sumTren2h);
            tempRes.setTong(sum);


            resSum.setList(sortList(resList, body.typeSort, body.sortBy));
            resSum.setSum(tempRes);

            return resSum;
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

    }

    @Override
    public Page<ResponseMatHanhTrinhDetailDto> getListDetail(MatHanhTrinhDetailBodyDto body) throws SQLException {

        LocalDate localDate = LocalDateTime.now().toLocalDate();
        Long maxVerSionHbase = getVersionMax("nguy_co_mat_hanh_trinh", localDate);


        if (!isViewMatHanhTrinhTTKT() || (maxVerSionHbase == null)) {
            Pageable pageable = PageRequest.of(0, 1);
            Page<ResponseMatHanhTrinhDetailDto> p = new PageImpl<>(new ArrayList<>(), pageable, 0);
            return p;
        }
        String maBuuCucConvert = "'" + body.tenTtkt + "'";

        String sqlForMatHanhTrinhDonDetail = MatHanhTrinhTTKTRepository.getQueryMatHanhTrinhListDetail(maxVerSionHbase, body.mocTon, maBuuCucConvert,
                convertListParamsToString(body.chiNhanhPhat),
                convertListParamsToString(body.loaiHang), convertListParamsToString(body.hangGiaTri),
                convertListParamsToString(body.nhomDichVu), convertListParamsToString(body.loaiKhachHang));

        Connection conn = null;
        PreparedStatement stmt = null;

        ResultSet rs = null;

        try {

            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlForMatHanhTrinhDonDetail);
            rs = stmt.executeQuery();

            List<ResponseMatHanhTrinhDetailDto> resListDetail = new ArrayList<>();
            while (rs.next()) {
                ResponseMatHanhTrinhDetailDto tempResDetail = new ResponseMatHanhTrinhDetailDto();
                tempResDetail.setMaPhieuGui(rs.getString("ma_phieugui"));
                tempResDetail.setMaTai(rs.getString("ma_tai"));
                tempResDetail.setDonHoan(rs.getString("don_hoan"));
                tempResDetail.setMa_buucuc_nhan(rs.getString("ma_buucuc_nhan"));
                tempResDetail.setChi_nhanh_nhan(rs.getString("chi_nhanh_nhan"));
                tempResDetail.setMa_buucuc_phat(rs.getString("ma_buucuc_phat"));
                tempResDetail.setChi_nhanh_phat(rs.getString("chi_nhanh_phat"));
                tempResDetail.setNoiDungHang(rs.getString("noi_dung_hang"));
                tempResDetail.setKhoiLuong(rs.getLong("khoi_luong_hang"));
                tempResDetail.setNhomDichVu(rs.getString("nhom_dv"));
                tempResDetail.setTienThuHo(rs.getLong("tien_thu_ho"));
                tempResDetail.setHangGiaTriCao(rs.getString("hang_gia_tri_cao"));
                tempResDetail.setPhanLoaiKhachHang(rs.getString("phan_loai_kh"));
                tempResDetail.setThoiGianNhan(rs.getLong("thoi_gian_nhan"));
                tempResDetail.setThoiGianTon(rs.getLong("thoi_gian_ton") * 60 * 1000);
                tempResDetail.setMocTon(rs.getString("moc_ton"));
                resListDetail.add(tempResDetail);
            }

            int pageIndex = body.pageIndex;
            int pageSize = body.pageSize;

            Pageable paging = PageRequest.of(pageIndex, pageSize);

            int limitSize = paging.getPageSize();
            int offset = (int) paging.getOffset();
            int start = Math.min(offset, resListDetail.size());
            int end = Math.min((start + limitSize), resListDetail.size());

            List<ResponseMatHanhTrinhDetailDto> dataListTmp = resListDetail.subList(start, end);

            Page<ResponseMatHanhTrinhDetailDto> page = new PageImpl<>(dataListTmp, paging, resListDetail.size());

            return page;
        } catch (Exception e) {
            e.printStackTrace();
            eLogger.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
    }

}
