package nocsystem.indexmanager.servicesIpm.ChatLuongSanLuong;

import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.*;
import nocsystem.indexmanager.repositories.DmSanLuongRepository;
import nocsystem.indexmanager.services.ChatLuongSanLuong.DmSanLuongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class DmSanLuongIpml implements DmSanLuongService {

    @Autowired
    private DmSanLuongRepository dmSanLuongRepository;

    @Override
    public List<DmSanLuongKhoResDto> searchKho(String maTinh, String maBuuTa, LocalDate ngayBaoCao) {
        return dmSanLuongRepository.searchKho(maTinh, maBuuTa,ngayBaoCao);
    }

    @Override
    public List<DmSanLuongBuuTaResDto> searchBuuTa(String maNhanVien, String maChinhNhanh, String maBuuCuc,LocalDate ngayBaoCao) {
        return dmSanLuongRepository.searchBuuTa(maNhanVien, maChinhNhanh, maBuuCuc,ngayBaoCao);
    }

    @Override
    public List<LkSanLuongBuuTaResDto> searchBuuTaLk(String maNhanVien, String maChinhNhanh, String maBuuCuc, LocalDate ngayBaoCao) {
        return dmSanLuongRepository.searchBuuTaLk(maNhanVien, maChinhNhanh, maBuuCuc,ngayBaoCao);
    }

    @Override
    public List<LkSanLuongKhoResDto> searchKhoLk(String maTinh, String maBuuCuc, LocalDate ngayBaoCao) {
        return dmSanLuongRepository.searchKhoLk(maTinh, maBuuCuc,ngayBaoCao);
    }

    @Override
    public List<LkSanLuongChiNhanhResDto> searchChiNhanhLk(String maTinh, LocalDate ngayBaoCao) {
        return dmSanLuongRepository.searchChiNhanhLk(maTinh,ngayBaoCao);
    }
    @Override
    public List<DmSanLuongChiNhanhTResDto> searchKhoChiNhanhTheoNgay(String maTinh, LocalDate ngayBaoCao) {
        return dmSanLuongRepository.searchKhoChiNhanhTheoNgay(maTinh,ngayBaoCao);
    }
}
