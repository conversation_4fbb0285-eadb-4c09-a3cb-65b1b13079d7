package nocsystem.indexmanager.servicesIpm.ChatLuongSanLuong;

import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.DmChatLuongBuuTaResDto;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.DmChatLuongKhoResDto;
import nocsystem.indexmanager.repositories.DmChatLuongRepository;
import nocsystem.indexmanager.services.ChatLuongSanLuong.DmChatLuongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

@Service
public class DmChatLuongIpml implements DmChatLuongService {

    @Autowired
    private DmChatLuongRepository dmChatLuongRepository;

    @Override
    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    public List<DmChatLuongKhoResDto> searchKho(String maTinh, String maBuuTa, LocalDate ngayBaoCao) {
        return dmChatLuongRepository.searchKho(maTinh, maBuuTa, ngayBaoCao);
    }

    @Override
    public List<DmChatLuongBuuTaResDto> searchBuuTa(String maNhanVien, String maChiNhanh, String maBuuCuc,LocalDate ngayBaoCao) {
        return dmChatLuongRepository.searchBuuTa(maNhanVien, maChiNhanh, maBuuCuc,ngayBaoCao);
    }
}
