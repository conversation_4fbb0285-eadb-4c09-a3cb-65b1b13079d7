package nocsystem.indexmanager.servicesIpm.ChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong.*;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.repositories.ChiSoKinhDoanhRepository;
import nocsystem.indexmanager.request.RequestToken;
import nocsystem.indexmanager.services.ChiTieuChatLuong.ThongKeChiTieuChatLuongService;
import okhttp3.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static nocsystem.indexmanager.config.DateTimeUltis.DateToLong;
import static nocsystem.indexmanager.config.DateTimeUltis.StringToLocalDate;
import static nocsystem.indexmanager.config.ProcessStringUtils.decodeString;

@Service
public class ThongKeChiTieuChatLuongServiceIpm implements ThongKeChiTieuChatLuongService {
    @Autowired
    private ChiSoKinhDoanhRepository chiSoKinhDoanhRepo;

    @Value("${quality-server.domain}")
    private String domain;


    private static final String prefixText = "reportNoc";

    private static final String preUrl = "https://dev-noc-pc.viettelpost.vn/report";

    public static final Long expireTime = 86400000L * 3;

    @Override
    public ChiTieuChatLuongDto thongKeChiTieuChatLuong(LocalDate ngayBaoCao) throws IOException {
        ChiTieuKinhDoanhDto tongDoanhThu = chiSoKinhDoanhRepo.findThongKeChiTieuChatLuong(ngayBaoCao);
        ChiTieuChatLuongDto res = new ChiTieuChatLuongDto();
        if (tongDoanhThu != null) {
            List<CSKDTienDoDoanhThuV2Dto> tienDoDoanhThu = chiSoKinhDoanhRepo.findTienDoDoanhThuTheoNgay(ngayBaoCao);
            for (CSKDTienDoDoanhThuV2Dto tienDoDoanhThuDetail : tienDoDoanhThu) {
                if (tienDoDoanhThuDetail.getNhomDt().equals("DT-CP")) {
                    tongDoanhThu.setThucHienCP(tienDoDoanhThuDetail.getThucHien());
                }
            }
            res.setChiTieuKinhDoanh(tongDoanhThu);
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDate = ngayBaoCao.format(formatter);
//        String startDate = "2022-10-24";
        /*
         * Lấy dữ liệu từ service noc-quantity
         * */

        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("text/plain");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url(domain + "/api/v1/report/public/bao-cao-tong-hop?startDate=" + startDate)
                .build();
        Response response = client.newCall(request).execute();

        if (response.code() == 200) {
            String response_body_string = response.body().string();

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            SimpleAPIResponse luykeResponse = objectMapper.readValue(response_body_string, SimpleAPIResponse.class);
            LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) luykeResponse.getData();

            BaoCaoBcclTongQuanResponse baoCaoBcclTongQuanResponse =
                    objectMapper.convertValue(data.get("baoCaoBcclTongQuanResponse"), BaoCaoBcclTongQuanResponse.class);

            BaoCaoBcclThoiGianToanTrinhDichVuDto thoiGianToanTrinhDichVuDto =
                    objectMapper.convertValue(data.get("baoCaoBcclThoiGianToanTrinhDichVu"),
                            BaoCaoBcclThoiGianToanTrinhDichVuDto.class);

            List<Top15ChiNhanhCaoNhatDto> top15ChiNhanhCaoNhatDtos =
                    objectMapper.convertValue(data.get("top15ChiNhanhCaoNhat"),
                            new TypeReference<List<Top15ChiNhanhCaoNhatDto>>() {
                            });

            List<Top15ChiNhanhThapNhatDto> top15ChiNhanhThapNhatDtos =
                    objectMapper.convertValue(data.get("top15ChiNhanhThapNhat"),
                            new TypeReference<List<Top15ChiNhanhThapNhatDto>>() {
                            });

            if (baoCaoBcclTongQuanResponse != null) {
                /*
                 * Tinh toan chi tieu giao
                 */
                ChiTieuGiaoDto chiTieuGiaoDto = new ChiTieuGiaoDto();
                chiTieuGiaoDto.setTong_sl_giao(baoCaoBcclTongQuanResponse.getTong_sl_giao());
                chiTieuGiaoDto.setSan_luong_ton_giao_ngay((Integer) data.get("tongTon"));
                chiTieuGiaoDto.setLm_ty_le_gtc_tong(baoCaoBcclTongQuanResponse.getLm_ty_le_gtc_tong());
                chiTieuGiaoDto.setTl_ptc_landau((Double) data.get("tl_ptc_landau"));
                chiTieuGiaoDto.setSan_luong_ton_giao_ngay(baoCaoBcclTongQuanResponse.getTong_sl_gtc());

                /*
                 * Tinh toan chi tieu nhan
                 */
                ChiTieuNhanDto chiTieuNhanDto = new ChiTieuNhanDto();
                chiTieuNhanDto.setTong_san_luong_don_tmdt(baoCaoBcclTongQuanResponse.getTong_san_luong_don_tmdt());
                chiTieuNhanDto.setSl_don_ntc(baoCaoBcclTongQuanResponse.getSl_don_ntc());
                chiTieuNhanDto.setFm_ty_le_ntc(baoCaoBcclTongQuanResponse.getFm_ty_le_ntc());

                /*
                 * Ty le dung KPI toan trinh ngay
                 */
                TLDungKPIToanTrinhNgayDto tlDungKPIToanTrinhNgayDto = new TLDungKPIToanTrinhNgayDto();
                tlDungKPIToanTrinhNgayDto.setTong(baoCaoBcclTongQuanResponse.getTl_dung_tt());
                tlDungKPIToanTrinhNgayDto.setTl_dung_tt_noi_tinh(baoCaoBcclTongQuanResponse.getTl_dung_tt_noi_tinh());
                tlDungKPIToanTrinhNgayDto.setTl_dung_tt_noi_mien(baoCaoBcclTongQuanResponse.getTl_dung_tt_noi_mien());
                tlDungKPIToanTrinhNgayDto.setTl_dung_tt_lien_mien_bo(baoCaoBcclTongQuanResponse.getTl_dung_tt_lien_mien_bo());
                tlDungKPIToanTrinhNgayDto.setTl_dung_tt_lien_mien_bay(baoCaoBcclTongQuanResponse.getTl_dung_tt_lien_mien_bay());
                tlDungKPIToanTrinhNgayDto.setTl_dung_tt_noi_tinh_hni_hcm(baoCaoBcclTongQuanResponse.getTl_dung_tt_noi_tinh_hni_hcm());

                /*
                 * Thoi gian toan trinh trung binh ngay
                 */
                ThoiGianToanTrinhTBNgayDto thoiGianToanTrinhTBNgayDto = new ThoiGianToanTrinhTBNgayDto();
                thoiGianToanTrinhTBNgayDto.setTgtb_toan_trinh_noi_tinh(baoCaoBcclTongQuanResponse.getTgtb_toan_trinh_noi_tinh());
                thoiGianToanTrinhTBNgayDto.setTgtb_toan_trinh_noi_tinh_hni_hcm(baoCaoBcclTongQuanResponse.getTgtb_toan_trinh_noi_tinh_hni_hcm());
                thoiGianToanTrinhTBNgayDto.setTgtb_toan_trinh_noi_mien(baoCaoBcclTongQuanResponse.getTgtb_toan_trinh_noi_mien());
                thoiGianToanTrinhTBNgayDto.setTgtb_lien_mien_bo(baoCaoBcclTongQuanResponse.getTgtb_cham_lien_mien());
                thoiGianToanTrinhTBNgayDto.setTgtb_lien_mien_bay(baoCaoBcclTongQuanResponse.getTgtb_nhanh_lien_mien());
                thoiGianToanTrinhTBNgayDto.setTong((Double) data.get("tg_toantrinh_tb"));

                /*
                 * Set gia tri tra ve
                 */
                res.setTlDungKPIToanTrinhNgayDto(tlDungKPIToanTrinhNgayDto);
                res.setThoiGianToanTrinhTBNgayDto(thoiGianToanTrinhTBNgayDto);
                res.setChiTieuGiaoDto(chiTieuGiaoDto);
                res.setChiTieuNhanDto(chiTieuNhanDto);
            }

            if (!top15ChiNhanhCaoNhatDtos.isEmpty()) {
                res.setTop15ChiNhanhCaoNhatDto(top15ChiNhanhCaoNhatDtos);
            }
            return res;
        } else {
            throw new IOException("Có lỗi xảy ra");
        }
    }

    @Override
    public ChiTieuChatLuongDto verifyAndGetInfo(String typeReport, String exportDate, String notiDate, String checkSum) throws Exception {
        // Check time expire
        if (!checkExpireTime(notiDate)) {
            throw new Exception("Expired Time");
        }

        // Check Valid Token
        String encodeText = hashMD5(prefixText + exportDate + notiDate + typeReport);
        if (!verifyCheckSum(encodeText, checkSum)) {
            throw new Exception("Token invalid");
        }

        // Token is Valid -> Call to ThongKeChiTieuChatLuong func
        LocalDate localDateExportDate = StringToLocalDate(exportDate);
        ChiTieuChatLuongDto chiTieuChatLuongDto = thongKeChiTieuChatLuong(localDateExportDate);
        chiTieuChatLuongDto.setNotiDate(notiDate);
        chiTieuChatLuongDto.setExportDate(exportDate);

        return chiTieuChatLuongDto;

    }

    public boolean checkExpireTime(String notiDate) {

        Date dateNow = Calendar.getInstance().getTime();
        Long nowLong = DateToLong(dateNow);

        Long notiDateLong = DateToLong(notiDate);

        return (nowLong - notiDateLong) < expireTime;

    }

    public boolean verifyCheckSum(String hashText, String checkSum) {
        return hashText.equals(checkSum);
    }

    public String hashMD5(String text) {
        return DigestUtils
                .md5Hex(text).toUpperCase();
    }

}
