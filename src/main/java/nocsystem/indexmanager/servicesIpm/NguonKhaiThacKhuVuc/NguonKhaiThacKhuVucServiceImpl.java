package nocsystem.indexmanager.servicesIpm.NguonKhaiThacKhuVuc;

import nocsystem.indexmanager.dao.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucExcel;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucRequestBody;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucResponse;
import nocsystem.indexmanager.services.DashBoardChiNhanh.ExportExcelTemplateCNBC;
import nocsystem.indexmanager.services.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;

@Service
public class NguonKhaiThacKhuVucServiceImpl<T> implements NguonKhaiThacKhuVucService {
    @Autowired
    NguonKhaiThacKhuVucDAO nguonKhaiThacKhuVucDAO;

    @Override
    public List<NguonKhaiThacKhuVucResponse> nguonKhaiThacKhuVuc(NguonKhaiThacKhuVucRequestBody requestBody) throws SQLException {
        List<NguonKhaiThacKhuVucResponse> listResult = new ArrayList<>();
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            listResult = nguonKhaiThacKhuVucDAO.nguonKhaiThacKhuVuc(
                    requestBody.getNgayBaoCao(),
                    requestBody.getLuyKe(),
                    requestBody.getTtktCha(),
                    requestBody.getTtktQuetNhan(),
                    buuCucSSO,
                    requestBody.getMaDichVu(),
                    requestBody.getPage(),
                    requestBody.getPageSize()
            );
        }
        return listResult;
    }

    @Override
    public Long nguonKhaiThacKhuVucTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> maDichVu) throws SQLException {
        Long total = 0L;
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }
            total = nguonKhaiThacKhuVucDAO.nguonKhaiThacKhuVucTotalRecord(ngayBaoCao, luyKe, ttktCha, ttktQuetNhan, buuCucSSO, maDichVu);
        }
        return total;
    }

    @Override
    public NguonKhaiThacKhuVucResponse nguonKhaiThacKhuVucTong(NguonKhaiThacKhuVucRequestBody requestBody) throws SQLException {
        NguonKhaiThacKhuVucResponse result = new NguonKhaiThacKhuVucResponse();
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            result = nguonKhaiThacKhuVucDAO.nguonKhaiThacKhuVucTong(
                    requestBody.getNgayBaoCao(),
                    requestBody.getLuyKe(),
                    requestBody.getTtktCha(),
                    requestBody.getTtktQuetNhan(),
                    buuCucSSO,
                    requestBody.getMaDichVu()
            );
        }
        return result;
    }

    @Override
    public void nguonKhaiThacKhuVucExcel(HttpServletResponse response, NguonKhaiThacKhuVucRequestBody requestBody) throws IOException, IllegalAccessException, SQLException {
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            response.setContentType("application/vnd.ms.excel");
            String headerKey = "Content-Disposition";
            String headerValue = "attachment; filename=NguonKhaiThacKhuVuc.xlsx";
            response.setHeader(headerKey, headerValue);
            Map<String, String> mapHeader = new HashMap<>();
            mapHeader.put("ttktCha", "TTKT Cha");
            mapHeader.put("ttktQuetNhan", "TTKT Quét Nhận");
            mapHeader.put("slNho", "Sản Lượng <= 2kg");
            mapHeader.put("tlNho", "Trọng Lượng <= 2kg (gram)");
            mapHeader.put("nguonSlNho", "Nguồn SL <= 2kg (đồng)");
            mapHeader.put("nguonTlNho", "Nguồn TL <= 2kg (đồng)");
            mapHeader.put("tongNguonNho", "Tổng Nguồn <= 2kg (đồng)");
            mapHeader.put("slLon", "Sản Lượng > 2kg");
            mapHeader.put("tlLon", "Trọng Lượng > 2kg (gram)");
            mapHeader.put("nguonSlLon", "Nguồn SL > 2kg (đồng)");
            mapHeader.put("nguonTlLon", "Nguồn TL > 2kg (đồng)");
            mapHeader.put("tongNguonLon", "Tổng Nguồn > 2kg (đồng)");
            mapHeader.put("tongSl", "Tổng Sản Lượng");
            mapHeader.put("tongTl", "Tổng Trọng Lượng (gram)");
            mapHeader.put("tongTlTinhNguon", "Tổng trọng lượng tính nguồn (gram)");
            mapHeader.put("tongNguonSl", "Tổng Nguồn SL (đồng)");
            mapHeader.put("tongNguonTl", "Tổng Nguồn TL (đồng)");
            mapHeader.put("tongNguonTruocKpi", "Tổng Nguồn Trước KPI (đồng)");
            mapHeader.put("tongNguonSauKpi", "Tổng Nguồn Sau KPI (đồng)");

            List<String> header;
            header = Arrays.asList(
                    "ttktCha",
                    "ttktQuetNhan",
                    "slNho",
                    "tlNho",
                    "nguonSlNho",
                    "nguonTlNho",
                    "tongNguonNho",
                    "slLon",
                    "tlLon",
                    "nguonSlLon",
                    "nguonTlLon",
                    "tongNguonLon",
                    "tongSl",
                    "tongTl",
                    "tongTlTinhNguon",
                    "tongNguonSl",
                    "tongNguonTl",
                    "tongNguonTruocKpi",
                    "tongNguonSauKpi"
            );

            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                    exportExcelNguonKhaiThacKhuVuc(requestBody.getNgayBaoCao(), requestBody.getLuyKe(), requestBody.getTtktCha(), requestBody.getTtktQuetNhan(), buuCucSSO, requestBody.getMaDichVu()),
                    mapHeader,
                    header,
                    "NguonKhaiThacKhuVuc"
            );
            tonExcelTemplate.exportDataToExcel(response);
        }
    }

    private List<Map<String, Object>> exportExcelNguonKhaiThacKhuVuc(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> buuCucSSO, List<String> maDichVu) throws SQLException{
        List<NguonKhaiThacKhuVucExcel> nguonKhaiThacKhuVucExcel = new ArrayList<>();
        nguonKhaiThacKhuVucExcel = nguonKhaiThacKhuVucDAO.exportExcelNguonKhaiThacKhuVuc(ngayBaoCao, luyKe, ttktCha, ttktQuetNhan, buuCucSSO, maDichVu);
        return tryTest((List<T>) nguonKhaiThacKhuVucExcel);
    }

    public List<Map<String, Object>> tryTest(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                NguonKhaiThacKhuVucExcel x1 = (NguonKhaiThacKhuVucExcel) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }
}
