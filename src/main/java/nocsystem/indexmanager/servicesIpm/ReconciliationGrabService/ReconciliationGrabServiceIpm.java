package nocsystem.indexmanager.servicesIpm.ReconciliationGrabService;

import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.config.ArrayUtils;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SupperSet.GenerateConnection;
import nocsystem.indexmanager.exception.BadDataInputExcelException;
import nocsystem.indexmanager.exception.BadRequestException;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Grab.GrabDoiSoat;
import nocsystem.indexmanager.models.Grab.GrabReconciliation;
import nocsystem.indexmanager.models.Grab.ImportExelReconciliation;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.*;
import nocsystem.indexmanager.repositories.Grab.GrabDoiSoatRepository;
import nocsystem.indexmanager.repositories.Grab.ImportExelReconciliationRepository;
import nocsystem.indexmanager.repositories.Grab.TongHopDoiSoatGrapRepository;
import nocsystem.indexmanager.request.FilterDetailReconciliationReq;
import nocsystem.indexmanager.request.FilterGrabFinancialStatementsReq;
import nocsystem.indexmanager.services.ReconciliationGrab.ReconciliationGrabService;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class ReconciliationGrabServiceIpm<T> implements ReconciliationGrabService {
    @Value("${storage.file.directory}")
    private String storageFileDirectory;

    private int skip = 0;

    private HashMap<Integer, String> detailRow = new HashMap<>();

    private int hasError = 0;

    private int checkingRow = 0;

    private int ALLOW_EMPTY = 20;

    private int nullData = 0;

    @Autowired
    private RedisTemplate redisTemplate;

    private final int TIME_OUT = 3600;

    private final int TYPE_CORRECT = 1;

    private final int TYPE_ERROR = 0;

    private final String PREFIX_RECONCILIATION_REDIS = "PREFIX_RECONCILIATION_REDIS";

    private final String PREFIX_GRAB_MVD_POSITION_REDIS = "GRAB_MVD_POSITION_";

    private final Short DEFAULT_SYNC_DATA = 0;

    private final Short DONE_UPDATE = 1;

    private final int INDEX_MA_VAN_DON = 1;

    private static final long MAX_FILE_SIZE = 25L * 1024 * 1024; // 25MB

    private static final int NEED_TO_DOWNLOAD_FILE = 1;

    private List<String> listTitle = List.of("STT", "Mã vận đơn", "Trạng thái", "Tổng tiền", "Cước chiều đi", "Phụ phí", "Chiết khấu");

    @Autowired
    private ImportExelReconciliationRepository importExelReconciliationRepo;

    @Autowired
    private GrabDoiSoatRepository grabDoiSoatRepository;

    public ReconciliationGrabServiceIpm(ImportExelReconciliationRepository importExelReconciliationRepo) {
        this.importExelReconciliationRepo = importExelReconciliationRepo;
    }

    @Autowired
    private GenerateConnection generateConnection;

    @Autowired
    private TongHopDoiSoatGrapRepository tongHopDoiSoatGrapRepo;

    @Override
    public List<HistoryImportExcelGrabDto> reconciliationUploadHistory(String fileName, short typeUpload) throws Exception {
        try {
            if (UserContext.getUserData().getJwtInformGlobal() == null) {
                throw new Exception();
            }
            String userName = UserContext.getUserData().getJwtInformGlobal().getSub();
            List<HistoryImportExcelGrabDto> reconciliation = importExelReconciliationRepo.historyUploadFileGrab(userName, typeUpload, fileName);
            return reconciliation;
        } catch (Exception exception) {
            throw new Exception(exception.getMessage());
        }
    }

    @Override
    public SimpleAPIResponse reconciliationUploadSuccess(String fileName, short typeUpload) throws Exception {
        try {
            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();

            if (UserContext.getUserData().getJwtInformGlobal() == null) {
                simpleAPIResponse.setError(HttpStatus.UNPROCESSABLE_ENTITY.value());
                simpleAPIResponse.setMessage("Lỗi xử lý với token");
            }
            String userName = UserContext.getUserData().getJwtInformGlobal().getSub();
            List<HistoryImportExcelGrabDto> historyUpload = importExelReconciliationRepo.reconciliationUploadSuccess(userName, typeUpload, fileName);
            HistoryImportExcelGrabDto reconciliation = new HistoryImportExcelGrabDto();

            if (historyUpload.size() > 0) {
                reconciliation = historyUpload.get(0);
            }

            simpleAPIResponse.setData(reconciliation);
            return simpleAPIResponse;
        } catch (Exception exception) {
            throw new Exception(exception.getMessage());
        }
    }

    @Override
    public SimpleAPIResponse reconciliationCalculate(int fileId) throws Exception {
        try {
            ImportExelReconciliation importExel = importExelReconciliationRepo.getImportFileInformById(fileId);
            if (importExel == null) {
                throw new InternalError("Không tìm thấy file_id :" + fileId);
            }

            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            String keyGrabReconciliationRedis = PREFIX_RECONCILIATION_REDIS + "_" + fileId;
            List<GrabReconciliation> cacheResultSet = (List<GrabReconciliation>) redisTemplate.opsForValue().get(keyGrabReconciliationRedis);

            if (cacheResultSet == null) {
                cacheResultSet = processToSaveDataBaseV2(fileId);
            }

            if (cacheResultSet.size() > 0) {
                //Xử lý logic đối soát
                List<String> allMaVanDon = new ArrayList<>();
                for (GrabReconciliation cacheResultSetItem : cacheResultSet) {
                    allMaVanDon.add(cacheResultSetItem.getMaVanDon());
                }

                List<GrabDoiSoat> lístGrabDoiSoatV2 = grabDoiSoatRepository.getGrabReconciliationByFileIdV2(allMaVanDon);
                if (reconciliationGrabV2(lístGrabDoiSoatV2, cacheResultSet, fileId)) {
                    //Update lại dữ liệu bảng import_exel_reconciliation đánh dấu file excel đã được đối soát
                    importExel.setDataSync(DONE_UPDATE);
                    importExel.setUpdatedAt(LocalDateTime.now());
                    importExelReconciliationRepo.save(importExel);

                    simpleAPIResponse.setMessage("Tiến trình đối soát thành công");
                    simpleAPIResponse.setError(0);
                    return simpleAPIResponse;
                }
            }

            simpleAPIResponse.setMessage("Có lỗi trong quá trình đối soát");
            simpleAPIResponse.setError(HttpStatus.INTERNAL_SERVER_ERROR.value());
            return simpleAPIResponse;
        } catch (Exception exception) {
            throw new InternalError(exception.getMessage());
        }
    }

    @Transactional(rollbackFor = {Exception.class, Throwable.class})
    public Boolean reconciliationGrabV2(List<GrabDoiSoat> lístGrabDoiSoat, List<GrabReconciliation> cacheResultSet, int fileId) throws Exception {
        try {
            HashMap<String, GrabReconciliation> reconciliationFromGrab = new HashMap<>();
            List<String> maDonFromGrabList = new ArrayList<>();
            List<String> vtpDonList = new ArrayList<>();

            List<ReconciliationRepo> reconciliationRepos = new ArrayList<>();
            for (GrabReconciliation reconciliationItem : cacheResultSet) {
                reconciliationFromGrab.put(reconciliationItem.getMaVanDon(), reconciliationItem);
                maDonFromGrabList.add(reconciliationItem.getMaVanDon());
            }

            for (GrabDoiSoat vtpDoiSoat : lístGrabDoiSoat) {
                GrabReconciliation grabReconciliation = reconciliationFromGrab.get(vtpDoiSoat.getMaVanDon());
//            ReconciliationRepo grabReconciliationItem = new ReconciliationRepo().builder().grabChieuKhau(dfsdf).grabPhuPhi().build();
                ReconciliationRepo grabReconciliationItem = new ReconciliationRepo();
                grabReconciliationItem.setMaVanDon(vtpDoiSoat.getMaVanDon());
                grabReconciliationItem.setOrderId(vtpDoiSoat.getOrderId());
                grabReconciliationItem.setKhoangCach(vtpDoiSoat.getKhoangCach());
                grabReconciliationItem.setMaChiNhanhGoc(vtpDoiSoat.getMaChiNhanhGoc());
                grabReconciliationItem.setTenBuuCucGoc(vtpDoiSoat.getTenChiNhanhGoc());
                grabReconciliationItem.setMaKhachHangGoc(vtpDoiSoat.getMaKhachHangGoc());
                grabReconciliationItem.setTenKhachHangGoc(vtpDoiSoat.getTenKhachHangGoc());
                grabReconciliationItem.setMaChiNhanhPhat(vtpDoiSoat.getMaChiNhanhPhat());
                grabReconciliationItem.setTenChiNhanhPhat(vtpDoiSoat.getTenChiNhanhPhat());
                grabReconciliationItem.setMaBuuCucPhat(vtpDoiSoat.getMaBuuCucPhat());
                grabReconciliationItem.setTenBuuCucPhat(vtpDoiSoat.getTenBuuCucPhat());
                grabReconciliationItem.setTenKhachHangNhan(vtpDoiSoat.getTenKhachHangNhan());
                grabReconciliationItem.setZone(vtpDoiSoat.getVung());
                grabReconciliationItem.setMaDichVuVietTel(vtpDoiSoat.getMaDichVuVietTel());
                grabReconciliationItem.setTrongLuong(vtpDoiSoat.getTrongLuong());
                grabReconciliationItem.setVtpTrangThaiCuoiCung(vtpDoiSoat.getVtpTrangThaiCuoiCung());
                grabReconciliationItem.setVtpTongTien(vtpDoiSoat.getVtpTongTien());
                grabReconciliationItem.setVtpCuocChieuDi(vtpDoiSoat.getVtpCuocChieuDi());
                grabReconciliationItem.setVtpPhuPhi(vtpDoiSoat.getVtpPhuPhi());
                grabReconciliationItem.setVtpChietKhau(vtpDoiSoat.getVtpChietKhau());
                grabReconciliationItem.setGrabTrangThai(vtpDoiSoat.getGrabTrangThai());
                grabReconciliationItem.setGrabTongTien(vtpDoiSoat.getGrabTongTien());
                grabReconciliationItem.setGrabCuocChieuDi(vtpDoiSoat.getGrabCuocChieuDi());
                grabReconciliationItem.setGrabPhuPhi(vtpDoiSoat.getGrabPhuPhi());
                grabReconciliationItem.setGrabChieuKhau(vtpDoiSoat.getGrabChieuKhau());

                BigDecimal chenhLechTongTien = null;
                BigDecimal chenhLechCuocChieuDi = null;
                BigDecimal chenhLechPhuPhi = null;
                BigDecimal chenhLechChietKhau = null;

                /* Tính chênh lệch đối soát giữa VTP và đối soát Grab import excel*/
                if (grabReconciliation != null) {
                    if (vtpDoiSoat.getVtpTongTien() != null && grabReconciliation.getTongTien() != null) {
                        chenhLechTongTien = vtpDoiSoat.getVtpTongTien().subtract(new BigDecimal(String.valueOf(grabReconciliation.getTongTien())));
                        vtpDoiSoat.setChenhLechTongTien(chenhLechTongTien);
                    }
                    if (vtpDoiSoat.getVtpCuocChieuDi() != null && grabReconciliation.getCuocChuyenDi() != null) {
                        chenhLechCuocChieuDi = vtpDoiSoat.getVtpCuocChieuDi().subtract(new BigDecimal(String.valueOf(grabReconciliation.getCuocChuyenDi())));
                        vtpDoiSoat.setChenhLechCuocChieuDi(chenhLechCuocChieuDi);
                    }
                    if (vtpDoiSoat.getVtpPhuPhi() != null && grabReconciliation.getPhuPhi() != null) {
                        chenhLechPhuPhi = vtpDoiSoat.getVtpPhuPhi().subtract(new BigDecimal(String.valueOf(grabReconciliation.getPhuPhi())));
                        vtpDoiSoat.setChenhLechPhuPhi(chenhLechPhuPhi);
                    }
                    if (vtpDoiSoat.getVtpChietKhau() != null && grabReconciliation.getChietKhau() != null) {
                        chenhLechChietKhau = vtpDoiSoat.getVtpChietKhau().subtract(new BigDecimal(String.valueOf(grabReconciliation.getChietKhau())));
                        vtpDoiSoat.setChenhLechChietKhau(chenhLechChietKhau);
                    }

                    /*Add thêm dữ liệu vào bảng đối soát*/
                    vtpDoiSoat.setGrabTrangThai(grabReconciliation.getTrangThai());
                    vtpDoiSoat.setGrabTongTien(grabReconciliation.getTongTien());
                    vtpDoiSoat.setGrabCuocChieuDi(grabReconciliation.getCuocChuyenDi());
                    vtpDoiSoat.setGrabPhuPhi(grabReconciliation.getPhuPhi());
                    vtpDoiSoat.setGrabChieuKhau(grabReconciliation.getChietKhau());
                }

                vtpDoiSoat.setFileId(fileId);
                grabReconciliationItem.setChenhLechTongTien(chenhLechTongTien);
                grabReconciliationItem.setChenhLechCuocChieuDi(chenhLechCuocChieuDi);
                grabReconciliationItem.setChenhLechPhuPhi(chenhLechPhuPhi);
                grabReconciliationItem.setChenhLechChietKhau(chenhLechChietKhau);
                grabReconciliationItem.setGhiChu(vtpDoiSoat.getGhiChu());

                vtpDonList.add(vtpDoiSoat.getMaVanDon());

                if (grabReconciliation != null) {
                    reconciliationRepos.add(grabReconciliationItem);
                    grabDoiSoatRepository.save(vtpDoiSoat);
                }
            }

            for (String element : maDonFromGrabList) {
                if (!vtpDonList.contains(element) && reconciliationFromGrab.get(element) != null) {
                    GrabDoiSoat newData = new GrabDoiSoat();
                    GrabReconciliation grabReconciliation = reconciliationFromGrab.get(element);
                    newData.setMaVanDon(grabReconciliation.getMaVanDon());
                    newData.setGrabTrangThai(grabReconciliation.getTrangThai());
                    newData.setGrabTongTien(grabReconciliation.getTongTien());
                    newData.setGrabCuocChieuDi(grabReconciliation.getCuocChuyenDi());
                    newData.setGrabPhuPhi(grabReconciliation.getPhuPhi());
                    newData.setGrabChieuKhau(grabReconciliation.getChietKhau());
                    grabDoiSoatRepository.save(newData);
                }
            }

            return true;
        } catch (Exception exception) {
            throw new Exception(exception.getMessage());
        }
    }

    @Transactional(rollbackFor = {Exception.class, Throwable.class})
    @Override
    public SimpleAPIResponse ReconciliationFinancial(MultipartFile file) throws Exception {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();

        try {
            // Alternatively, check file type based on file extension
            String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
            if (!originalFilename.endsWith(".xls") && !originalFilename.endsWith(".xlsx")) {
                throw new BadDataInputExcelException("Chỉ cho phép upload file excel");
            }

            if (file.getSize() > MAX_FILE_SIZE) {
                throw new BadDataInputExcelException("Dung lượng file upload không > 25MB");
            }

            this.skip = 0;
            this.detailRow = new HashMap<>();
            this.hasError = 0;
            this.checkingRow = 0;

            if (file.isEmpty()) {
                throw new BadRequestException("Please select a file to upload.");
            }

            LocalDateTime timeStamp = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy-MM-dd H:mm:ss");
            String formattedDateTime = timeStamp.format(formatter);

            // Load the uploaded file into a Workbook object
            Workbook workbook = getProperVersionOfExcel(file);
            // Get the first sheet of the workbook
            Sheet sheet = workbook.getSheetAt(0);
            Row firstRow = sheet.getRow(0);

            // Set a new font size for the row (increase by 2 points)
            Font newFont = workbook.createFont();
            List<String> maVanDonErrors = new ArrayList<>();
            ProcessCellOfExcelResponse processCellOfExcelResponse = this.processCellOfExcel(sheet, timeStamp, maVanDonErrors);
            List<GrabReconciliation> grabReconciliationList = processCellOfExcelResponse.getGrabReconciliationList();
            int totalColumn = processCellOfExcelResponse.getTotalColumn();

            List<String> maVanDonList = new ArrayList<>();
            if (grabReconciliationList.size() > 0) {
                for (GrabReconciliation grabReconciliation : grabReconciliationList) {
                    maVanDonList.add(grabReconciliation.getMaVanDon());
                }
            }

            if (maVanDonList.size() > 0) {
                List<GrabDoiSoat> grabDoiSoats = grabDoiSoatRepository.getGrabReconciliationByFileIdV2(maVanDonList);
                List<String> listExistVTPMVD = new ArrayList<>();
                for (GrabDoiSoat grabDoiSoat : grabDoiSoats) {
                    listExistVTPMVD.add(grabDoiSoat.getMaVanDon());
                }

                if (maVanDonList.size() != grabDoiSoats.size()) {
                    for (String maVanDonCheck : maVanDonList) {
                        if (!listExistVTPMVD.contains(maVanDonCheck)) {
                            maVanDonErrors.add(maVanDonCheck);
                        }
                    }
                }
            }

            if (maVanDonErrors.size() > 0) {
                this.hasError = 1;
                this.processToSetCellOfExcel(sheet, maVanDonErrors);
            }

            String storagePage = "";
            String changeFileName = "";
            String path = "";
            // Add thêm cột ghi chú nếu phát hiện ra file có lỗi
            if (this.hasError == 1) {
                if (totalColumn < 8) {
                    Cell newCell = firstRow.createCell(firstRow.getLastCellNum());
                    newCell.setCellValue("GHI CHÚ");
                }

                // Generate a unique filename
                String modifiedFilename = "E" + "_" + originalFilename;

                // Save the modified file to the directory
                storagePage = storageFileDirectory + "/error-files/" + modifiedFilename;
                try (FileOutputStream outputStream = new FileOutputStream(storagePage)) {
                    workbook.write(outputStream);
                }

                // Close the workbook
                workbook.close();
            } else {
                changeFileName = originalFilename;
                path = storageFileDirectory + "/correct-files/";
                storagePage = path + changeFileName;

                /* save data */
                String fileName = StringUtils.cleanPath(changeFileName);
                // Copy the file to the upload directory
                Path uploadPath = Path.of(path);
                Files.createDirectories(uploadPath);
                Path filePathSave = uploadPath.resolve(fileName);
                Files.copy(file.getInputStream(), filePathSave, StandardCopyOption.REPLACE_EXISTING);
                /* end save data */
            }

            /* Save information of file to DB */
            Short typeUpload = (short) (this.hasError == 1 ? TYPE_CORRECT : TYPE_ERROR);
            ImportExelReconciliation importReconciliation = new ImportExelReconciliation();
            importReconciliation.setFileName(file.getOriginalFilename());
            importReconciliation.setDirectory(storagePage);
            importReconciliation.setTypeUpload(typeUpload);
            importReconciliation.setCreatedBy(UserContext.getUserData().getJwtInformGlobal().getSub());
            importReconciliation.setCreatedAt(timeStamp);
            importReconciliation.setDataSync(DEFAULT_SYNC_DATA);
            importExelReconciliationRepo.save(importReconciliation);

            String resultOfProcess = "";

            // Nếu không có lỗi sẽ tiến hành lưu lại file vào thư mục /correct-file/ và
            // lưu dữ liệu file excel vào DB theo cơ chế bất đồng bộ để giảm tải cho hệ thống và trải nghiệm người dùng
            if (this.hasError == 0) {
                String keyGrabReconciliationRedis = PREFIX_RECONCILIATION_REDIS + "_" + String.valueOf(importReconciliation.getId());
                processToCacheRedis(grabReconciliationList, keyGrabReconciliationRedis);
                SimpleAPIResponse simpleResponse = this.reconciliationCalculate(importReconciliation.getId());
                if (simpleResponse.getError() != 1) {
                    /* save data */
                    String fileName = StringUtils.cleanPath(changeFileName);
                    // Copy the file to the upload directory
                    Path uploadPath = Path.of(path);
                    Files.createDirectories(uploadPath);
                    Path filePathSave = uploadPath.resolve(fileName);
                    Files.copy(file.getInputStream(), filePathSave, StandardCopyOption.REPLACE_EXISTING);
                    /* end save data */
                }

                simpleResponse.setMessage("Upload file thành công.");
                return simpleResponse;
            } else {
                resultOfProcess = "Có lỗi trong quá trình xử lý. Vui lòng check lại file.";
                simpleAPIResponse.setMessage(resultOfProcess);
                simpleAPIResponse.setError(NEED_TO_DOWNLOAD_FILE);
            }

            this.resetStaticFlag();
            // Save data to DataBase
            return simpleAPIResponse;
        } catch (BadDataInputExcelException e) {
            this.resetStaticFlag();
            simpleAPIResponse.setError(HttpStatus.UNPROCESSABLE_ENTITY.value());
            simpleAPIResponse.setMessage(e.getMessage());
            return simpleAPIResponse;
        }
    }

    public Workbook getProperVersionOfExcel(MultipartFile file) {
        try {
            // Create a temporary file
            File tempFile = File.createTempFile("temp", null);
            Path tempFilePath = tempFile.toPath();

            // Save the multipart file to the temporary file
            Files.copy(file.getInputStream(), tempFilePath, StandardCopyOption.REPLACE_EXISTING);

            // Determine the file format using Apache POI's FileMagic class
            FileMagic fileMagic = FileMagic.valueOf(tempFile);

            if (fileMagic == FileMagic.OOXML) {
                return new XSSFWorkbook(file.getInputStream());
            } else {
                throw new BadDataInputExcelException("");
            }
        } catch (BadDataInputExcelException | IOException exception) {
            throw new BadDataInputExcelException("Version excel không phù hợp. Vui lòng tải file mẫu và tạo dữ liệu !");
        }
    }

    private void resetStaticFlag() {
        this.skip = 0;
        this.detailRow = new HashMap<>();
        this.hasError = 0;
        this.checkingRow = 0;
        this.nullData = 0;
    }

    public ProcessCellOfExcelResponse processCellOfExcel(Sheet sheet, LocalDateTime timeStamp, List<String> maVanDonErrors) {
        try {
            // Iterate through each row
            Iterator<Row> rowIterator = sheet.iterator();

            if (isRowEmpty(sheet.getRow(1)) || isRowEmpty(sheet.getRow(0))) {
                // Row is empty, do something
                throw new BadDataInputExcelException("Sai định dạng file mẫu. Vui lòng check lại.");
            }

            List<GrabReconciliation> grabReconciliationList = new ArrayList<>();
            List<String> listMaVanDonExcels = new ArrayList<>();
            int totalColumn = 0;

            while (rowIterator.hasNext()) {
                if (this.skip == 1) {
                    break;
                }
                String errorInformAtCell = "";
                Row rowElement = rowIterator.next();
                int rowNumber = rowElement.getRowNum();

                //Check định dạng file
                if (rowNumber == 0) {
                    int cellRowCheckNum = rowElement.getPhysicalNumberOfCells();
                    totalColumn = cellRowCheckNum;
                    for (int i = 0; i < 7; i++) {
                        this.detailRow.put(i, rowElement.getCell(i).getStringCellValue());
                        String titleElmInFile = rowElement.getCell(i).getStringCellValue().replaceAll("\\s+", "");
                        String titleCompare = listTitle.get(i).replaceAll("\\s+", "");
                        if (!titleElmInFile.equals(titleCompare)) {
                            throw new BadDataInputExcelException("Định dạng tiêu đề file không phù hợp, yêu cầu: " + listTitle.toString()
                                    + ". Vui lòng tải lại file mẫu!");
                        }
                    }
                }

                if (rowNumber > 0) {
                    String valueInsert = validateInputData(rowElement, listMaVanDonExcels, sheet);

                    // Nếu số cột == 8 tức là có thêm cột Ghi chú, sau khi upload lại file thì cần check lại nếu cell
                    // không còn lỗi thì Set Cell = BLANK.
                    if (this.detailRow.size() >= 7) {
                        // Set the value at a specific cell position
                        Cell cell = rowElement.createCell(7);
                        String noteCellValue = valueInsert.equals("") ? "" : valueInsert;
                        cell.setCellValue(noteCellValue);
                    } else {
                        if (!valueInsert.equals("")) {
                            // Add a new cell to each row
                            Cell newCell = rowElement.createCell(rowElement.getLastCellNum());
                            newCell.setCellValue(valueInsert);
                        }
                    }

                    /*
                     * Tạm thời dữ liệu sẽ được lưu vào Redis để sau này sẽ dùng hàm processToSaveDataBase() để lưu dữ liệu
                     * Trường hợp nếu dữ liệu lỗi sẽ xóa toàn bộ dữ liệu trong Redis và dừng toàn bộ quá trình
                     * */
                    if (this.skip != 1 && this.hasError != 1 && this.nullData != 1) {
                        grabReconciliationList = addDataForGrabReconciliation(rowElement, timeStamp, grabReconciliationList);
                        //Thêm danh sách mã đã đươc nhập trên excel-sheet
                        listMaVanDonExcels.add(getValueOfMaVanDon(rowElement));
                    }

                    this.nullData = 0;
                }

                this.checkingRow += 1;
            }

            ProcessCellOfExcelResponse processCellOfExcelResponse = new ProcessCellOfExcelResponse();
            processCellOfExcelResponse.setGrabReconciliationList(grabReconciliationList);
            processCellOfExcelResponse.setTotalColumn(totalColumn);

            return processCellOfExcelResponse;
        } catch (BadDataInputExcelException badRequestException) {
            throw new BadDataInputExcelException(badRequestException.getMessage());
        }
    }

    public Sheet processToSetCellOfExcel(Sheet sheet, List<String> maVanDonErrors) throws Exception {
        if (maVanDonErrors.size() > 0) {
            for (String maVanDon : maVanDonErrors) {
                String keyRedisOfMaVanDonPosition = PREFIX_GRAB_MVD_POSITION_REDIS + maVanDon;
                String positionOfMaVanDon = (String) redisTemplate.opsForValue().get(keyRedisOfMaVanDonPosition);
                List<String> position = List.of(positionOfMaVanDon.split("_"));
                if (position.size() < 2) {
                    throw new Exception("Có lỗi xử lý excel");
                }
                int numRow = Integer.parseInt(position.get(0));

                Row rowError = sheet.getRow(numRow);
                Cell cellError = rowError.createCell(7);
                String ghiChuCollValue = cellError.getStringCellValue();
                String maVanDonError = "Mã đơn không tồn tại trên hệ thống VTP";
                String valueInsert = ghiChuCollValue.equals("") ? maVanDonError : " && " + maVanDonError;
                cellError.setCellValue(valueInsert);
            }
        }

        return sheet;
    }

    @Override
    public ResponseEntity<Resource> ReconciliationFinancialDownload(String fileName, String typeUpload) throws Exception {
        String userName = UserContext.getUserData().getJwtInformGlobal().getSub();
        List<ImportExcelGrabDto> reconciliationList = importExelReconciliationRepo.dowloadUploadFileGrab(fileName, Short.valueOf(typeUpload), userName);

        if (reconciliationList.size() == 0) {
            return null;
        }

        //Lấy ra phần tử đầu tiên (Trong trường hợp này có thể có nhiều file trùng tên và cùng type nên cần lấy ra bản
        // ghi mới nhất với type và username cần lọc)
        ImportExcelGrabDto reconciliation = reconciliationList.get(0);
        String reDirectory = reconciliation.getDirectory();

        // Create a Resource object representing the file
        Resource resource = new FileSystemResource(reDirectory);

        // Set the content type
        String contentType = "application/octet-stream";

        // Return a ResponseEntity with the file contents and headers
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + reconciliation.getFileName() + "\"")
                .body(resource);
    }

    private void applyBorders(Cell cell) {
        Workbook workbook = cell.getSheet().getWorkbook();
        CellStyle cellStyle = cell.getCellStyle();

        // Apply borders to all sides
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);

        // Set border color to black
        short blackColorIndex = IndexedColors.BLACK.getIndex();
        cellStyle.setTopBorderColor(blackColorIndex);
        cellStyle.setBottomBorderColor(blackColorIndex);
        cellStyle.setLeftBorderColor(blackColorIndex);
        cellStyle.setRightBorderColor(blackColorIndex);

        // Update the cell style
        cell.setCellStyle(cellStyle);
    }

    private String validateInputData(Row checkedRow, List<String> listMaVanDonExcels, Sheet sheet) {
        // Check giá trị ở 7 cột mặc định
        int cellRowCheckNum = 7;
        String note = "";
        int countBlank = 0;

        if (isRowEmpty(checkedRow)) {
            if (checkStopProcessCondition(sheet, this.checkingRow)) {
                // Row is empty, do something
                this.skip = 1;
                return note;
            }

            this.nullData = 1;
//            this.hasError = 1;
            return note;
        }

        for (int cot = 0; cot < cellRowCheckNum; cot++) {
            String checkColumn = this.detailRow.get(cot);
            if (checkedRow.getCell(cot) == null) {
                note += "Cột: {" + checkColumn + "} đang để trống && ";
                countBlank += 1;
            } else {
                // Check khoảng trắng trong Cell
                if (checkRowWithFullSpaceInCell(checkedRow.getCell(cot))) {
                    note += "Cột: {" + checkColumn + "} đang để trống && ";
                    continue;
                }

                if (!checkLengthOfCell(checkedRow.getCell(cot))) {
                    note += "Cột: {" + checkColumn + "} độ dài cần <= 15 kí tự ";
                    continue;
                }

                // Đối với cột thứ 2 thì cần check type là NUMBER OR STRING
                List<CellType> typeMaVanDon = List.of(CellType.NUMERIC, CellType.STRING);
                HashMap<Integer, CellType> defineType = checkTypeOfDataFollowColumn();
                if (defineType.get(cot) != null && cot != INDEX_MA_VAN_DON && !(defineType.get(cot) == checkedRow.getCell(cot).getCellType())
//                        ((cot != INDEX_MA_VAN_DON && !(defineType.get(cot) == checkedRow.getCell(cot).getCellType()))
//                                || (cot == INDEX_MA_VAN_DON && !(typeMaVanDon.contains(checkedRow.getCell(cot).getCellType()))))
                ) {
                    note += "Cột: {" + checkColumn + "} đang sai định dạng && ";
                }

                /* Lưu lại vị trí của position trên file excel để add message lỗi */
                if (cot == INDEX_MA_VAN_DON) {
                    String maVanDon = getValueOfMaVanDon(checkedRow);
                    String positionOfMVDCell = checkedRow.getRowNum() + "_" + cot;
                    String keyRedisOfMaVanDonPosition = PREFIX_GRAB_MVD_POSITION_REDIS + maVanDon;
                    redisTemplate.opsForValue().set(keyRedisOfMaVanDonPosition, positionOfMVDCell, TIME_OUT, TimeUnit.SECONDS);
                    if (listMaVanDonExcels.contains(getValueOfMaVanDon(checkedRow))) {
                        note += "Mã vận đơn " + maVanDon + " bị lặp trên file excel && ";
                    }
                }
            }
        }

//        if (countBlank == 7) {
//            note = "";
//            this.skip = 1;
//        }

        /* Nếu check file có lỗi sẽ cắt chuỗi && sau cùng và đánh dấu hasError để thêm cột ghi chú */
        if (!note.equals("")) {
            note = note.substring(0, note.length() - 3);
            this.hasError = 1;
        }

        /* Remove && at the end of note */
        return note;
    }

    public Boolean checkLengthOfCell(Cell cell) {
        String cellValue = "";
        if (cell.getCellType() == CellType.STRING) {
            cellValue = cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            double numericCellValue = cell.getNumericCellValue();
            DecimalFormat decimalFormat = new DecimalFormat("#");
            cellValue = decimalFormat.format(numericCellValue);
        }

        if (cellValue.length() > 15) {
            return false;
        }

        return true;
    }

    public Boolean checkStopProcessCondition(Sheet sheet, int indexOfCurrentRow) {
        System.out.println("Trống tại vị trí: " + indexOfCurrentRow);
        short numberBlank = 0;
        for (int step = 1; step <= ALLOW_EMPTY; step++) {
            Row rawCheck = sheet.getRow(indexOfCurrentRow + step);
            if (isRowEmpty(rawCheck)) {
                numberBlank += 1;
            }
        }
        if (numberBlank == 4) {
            return true;
        }

        return false;
    }

    private static boolean isRowEmpty(Row row) {
        // Không có Object row thì trả về cột không có dữ liệu
        if (row == null) {
            return true;
        }
        for (Cell cell : row) {
            if (cell.getCellType() != CellType.BLANK) {
                if (checkRowWithFullSpaceInCell(cell)) {
                    break;
                }
                return false;
            }
        }
        return true;
    }

    private static boolean checkRowWithFullSpaceInCell(Cell cell) {
        String cellValue = "";
        if (cell.getCellType() == CellType.STRING) {
            cellValue = cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.FORMULA) {
            cellValue = cell.getCellFormula();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            cellValue = String.valueOf(cell.getNumericCellValue());
        }
        String cellValueAfterRegex = cellValue.replaceAll("\\s", "");
        if (cellValueAfterRegex.equals("")) {
            return true;
        }

        return false;
    }

    private HashMap<Integer, CellType> checkTypeOfDataFollowColumn() {
        HashMap<Integer, CellType> defineType = new HashMap<>();
        defineType.put(0, CellType.NUMERIC);// STT
//        defineType.put(1, CellType.NUMERIC);// MÃ VẬN ĐƠN
        defineType.put(2, CellType.NUMERIC);// TRẠNG THÁI
        defineType.put(3, CellType.NUMERIC);// TỔNG TIỀN
        defineType.put(4, CellType.NUMERIC);// CƯỚC CHUYẾN ĐI
        defineType.put(5, CellType.NUMERIC);// PHỤ PHÍ
        defineType.put(6, CellType.NUMERIC);// CHIẾT KHẤU

        return defineType;
    }

    private void processToCacheRedis(List<GrabReconciliation> grabReconciliation, String grabReconciliationRedisKey) throws Exception {
        try {
            redisTemplate.opsForValue().set(grabReconciliationRedisKey, grabReconciliation, TIME_OUT, TimeUnit.SECONDS);
        } catch (Exception exception) {
            throw new Exception(exception);
        }
    }

    @Transactional(rollbackFor = {Exception.class, Throwable.class})
    public String processToSaveDataBase() throws Exception {
        List<HistoryImportExcelGrabDto> notSync = importExelReconciliationRepo.getNotSaveDataFile((short) DEFAULT_SYNC_DATA, (short) 0);
        LocalDateTime timeStamp = LocalDateTime.now();

        Connection connection = generateConnection.createConnection("noc-index-manager");
        try {
            for (HistoryImportExcelGrabDto notSyncItem : notSync) {
                String keyGrabReconciliationRedis = PREFIX_RECONCILIATION_REDIS + "_" + notSyncItem.getId();
                List<GrabReconciliation> grabReconciliationList = (List<GrabReconciliation>) redisTemplate.opsForValue().get(keyGrabReconciliationRedis);
                if (grabReconciliationList == null) {
                    grabReconciliationList = getDataFromExcelFile(notSyncItem);
                }

                if (grabReconciliationList == null || grabReconciliationList.size() == 0) {
                    break;
                }

                log.info("=============> START INSERT DATA TO grab_reconciliation with file_id: " + notSyncItem.getId());
                saveDataToImportExcelTable(connection, grabReconciliationList, notSyncItem);

                /* Update lại giá trị dataSync của ImportExelReconciliation từ 0 -> 1 sau khi đồng bộ xong*/
                log.info("=============> START INSERT DATA TO import_exel_reconciliation with file_id: " + notSyncItem.getId());
                updateDataForImportExcel(notSyncItem);
            }
        } catch (Exception exception) {
            connection.close();
            throw new Exception(exception);
        } finally {
            connection.close();
        }

        return null;
    }

    @Override
    public SimpleAPIResponse filterDetailReconciliation(FilterDetailReconciliationReq filterDetailReconciliationReq) throws ParseException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        LocalDateTime ngayPhatThanhCongTo = LocalDateTime.of(filterDetailReconciliationReq.getNgayPhatThanhCongTo(), LocalTime.MAX);
        LocalDateTime ngayPhatThanhCongFrom = filterDetailReconciliationReq.getNgayPhatThanhCongFrom().atStartOfDay();

        int pageParam = filterDetailReconciliationReq.getPage();
        int page = (pageParam > 0) ? (pageParam - 1) : pageParam;
        Pageable paging = PageRequest.of(page, filterDetailReconciliationReq.getSize());

        Page<GrabDoiSoat> grabDoiSoats = grabDoiSoatRepository.getFilterReconciliation(
                ngayPhatThanhCongFrom,
                ngayPhatThanhCongTo,
                filterDetailReconciliationReq.getMaChiNhanhGui(),
                filterDetailReconciliationReq.getMaChiNhanhNhan(),
                filterDetailReconciliationReq.getMaVanDon(),
                paging
        );

        ListContentPageDto<GrabDoiSoat> pageAbleDoiSoat = new ListContentPageDto<>(grabDoiSoats);
        CustomizeDataPage<GrabDoiSoat> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setContent(grabDoiSoats.getContent());
        customizeDataPage.setLimit(filterDetailReconciliationReq.getSize());
        customizeDataPage.setOffset((int) pageAbleDoiSoat.getOffset());
        customizeDataPage.setTotal((int) pageAbleDoiSoat.getTotal());

        simpleAPIResponse.setData(customizeDataPage);
        return simpleAPIResponse;
    }

    @Override
    public SimpleAPIResponse filterGrabFinancialStatements(FilterGrabFinancialStatementsReq filterGrabFinancialStatementsReq) {
        int pageParam = filterGrabFinancialStatementsReq.getPage();
        int page = (pageParam > 0) ? (pageParam - 1) : pageParam;
        Pageable paging = PageRequest.of(page, filterGrabFinancialStatementsReq.getSize());

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        LocalDateTime ngayPhatThanhCongTo = LocalDateTime.of(filterGrabFinancialStatementsReq.getNgayPhatThanhCongTo(), LocalTime.MAX);
        Page<TongHopDoiSoatGrapResponse> tongHopDoiSoatGrabResponses = grabDoiSoatRepository.getFilterReconciliationV3(
                filterGrabFinancialStatementsReq.getNgayPhatThanhCongFrom(),
                ngayPhatThanhCongTo,
                filterGrabFinancialStatementsReq.getChiChanh(),
                filterGrabFinancialStatementsReq.getBuuCuc(),
                paging);

        ListContentPageDto<TongHopDoiSoatGrapResponse> pageAbleDoiSoat = new ListContentPageDto<>(tongHopDoiSoatGrabResponses);
        CustomizeDataPage<TongHopDoiSoatGrapResponse> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setContent(tongHopDoiSoatGrabResponses.getContent());
        customizeDataPage.setLimit(filterGrabFinancialStatementsReq.getSize());
        customizeDataPage.setOffset((int) pageAbleDoiSoat.getOffset());
        customizeDataPage.setTotal((int) pageAbleDoiSoat.getTotal());

        simpleAPIResponse.setData(customizeDataPage);

        return simpleAPIResponse;
    }

    @Override
    public ResponseEntity<byte[]> exportExcelDetailReconciliation(FilterGrabFinancialStatementsReq filterGrabFinancialStatementsReq) {
        LocalDateTime ngayPhatThanhCongTo = LocalDateTime.of(filterGrabFinancialStatementsReq.getNgayPhatThanhCongTo(), LocalTime.MAX);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        String fromSearch = filterGrabFinancialStatementsReq.getNgayPhatThanhCongFrom().format(formatter);
        String toSearch = filterGrabFinancialStatementsReq.getNgayPhatThanhCongTo().format(formatter);

        List<TongHopDoiSoatGrapResponse> tongHopDoiSoatGrabResponses = grabDoiSoatRepository.getFilterReconciliationTotal(
                filterGrabFinancialStatementsReq.getNgayPhatThanhCongFrom(),
                ngayPhatThanhCongTo,
                filterGrabFinancialStatementsReq.getChiChanh(),
                filterGrabFinancialStatementsReq.getBuuCuc());

        Workbook workbook = new SXSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        CellStyle centerCellStyle = workbook.createCellStyle();
        centerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        centerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        centerCellStyle.setBorderTop(BorderStyle.THIN);
        centerCellStyle.setBorderBottom(BorderStyle.THIN);
        centerCellStyle.setBorderLeft(BorderStyle.THIN);
        centerCellStyle.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 14); // Set the font size
        font.setBold(true); // Set bold style
        font.setFontName("Times New Roman");
        centerCellStyle.setFont(font);

        Font fontDetail = workbook.createFont();
        font.setFontHeightInPoints((short) 12); // Set the font size
        font.setFontName("Times New Roman");
        centerCellStyle.setFont(font);

        CellStyle cellStyleDetail = workbook.createCellStyle();
        cellStyleDetail.setBorderTop(BorderStyle.THIN);
        cellStyleDetail.setBorderBottom(BorderStyle.THIN);
        cellStyleDetail.setBorderLeft(BorderStyle.THIN);
        cellStyleDetail.setBorderRight(BorderStyle.THIN);
        cellStyleDetail.setFont(fontDetail);

        CellStyle cellStyleMoney = cellStyleDetail;
        cellStyleMoney.setAlignment(HorizontalAlignment.RIGHT);
        cellStyleMoney.setVerticalAlignment(VerticalAlignment.CENTER);

        sheet.setColumnWidth(0, 5 * 256);
        int widthInUnits = 15;
        for (int columnIndex = 1; columnIndex <= 17; columnIndex++) {
            sheet.setColumnWidth(columnIndex, widthInUnits * 256);
        }

        CellRangeAddress cellRangeAddress = new CellRangeAddress(1, 2, 0, 17);
        sheet.addMergedRegion(cellRangeAddress);

        Row title = sheet.createRow(1);

        Cell mergedCell = title.createCell(0);
        mergedCell.setCellValue("DỮ LIỆU ĐỐI SOÁT TỔNG HỢP\n" +
                " Từ " + fromSearch + " đến " + toSearch);
        mergedCell.setCellStyle(centerCellStyle);
        setBordersForCell(cellRangeAddress, sheet);

        /* Header STT */
        CellRangeAddress headerSTT = new CellRangeAddress(3, 4, 0, 0);
        sheet.addMergedRegion(headerSTT);

        /* Header Mã chi nhánh */
        CellRangeAddress headerMaChiNhanh = new CellRangeAddress(3, 4, 1, 1);
        sheet.addMergedRegion(headerMaChiNhanh);

        /* Header chi nhánh */
        CellRangeAddress headerChiNhanh = new CellRangeAddress(3, 4, 2, 2);
        sheet.addMergedRegion(headerChiNhanh);

        /* Header mã bưu cục */
        CellRangeAddress headerMaBuuCuc = new CellRangeAddress(3, 4, 3, 3);
        sheet.addMergedRegion(headerMaBuuCuc);

        /* Header tên bưu cục */
        CellRangeAddress headerTenBuuCuc = new CellRangeAddress(3, 4, 4, 4);
        sheet.addMergedRegion(headerTenBuuCuc);

        /* Header zone VTP */
        CellRangeAddress zoneVTP = new CellRangeAddress(3, 3, 5, 9);
        sheet.addMergedRegion(zoneVTP);

        /* Header zone GRAB */
        CellRangeAddress zoneGrab = new CellRangeAddress(3, 3, 10, 14);
        sheet.addMergedRegion(zoneGrab);

        /* Header zone CHÊNH LỆCH */
        CellRangeAddress zoneChenhLech = new CellRangeAddress(3, 3, 15, 17);
        sheet.addMergedRegion(zoneChenhLech);

        /* Add row to set data for title */
        Row titleRow = sheet.createRow(3);

        Row vtpRoleDetail = sheet.createRow(4);

        // Set the height of the cell in twips (1 twip = 1/20th of a point)
        int cellHeightInPoints = 30; // Specify the desired height in points
        short cellHeightInTwips = (short) (cellHeightInPoints * 20);
        titleRow.setHeight(cellHeightInTwips);
        vtpRoleDetail.setHeight(cellHeightInTwips);

        /*---------Style Header*/
        CellStyle titleCellStyle = workbook.createCellStyle();
        titleCellStyle.setAlignment(HorizontalAlignment.CENTER);
        titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font tieltFont = workbook.createFont();
        tieltFont.setFontHeightInPoints((short) 11); // Set the font size
        tieltFont.setBold(true); // Set bold style
        tieltFont.setFontName("Times New Roman");
        titleCellStyle.setFont(tieltFont);

        /*---------Add Value ----------*/

        Cell sttHeaderCell = titleRow.createCell(0);
        sttHeaderCell.setCellValue("STT");
        setBordersForCell(headerSTT, sheet);
        sttHeaderCell.setCellStyle(centerCellStyle);

        Cell maChiNhanhHeaderCell = titleRow.createCell(1);
        maChiNhanhHeaderCell.setCellValue("MÃ CHI NHÁNH");
        setBordersForCell(headerMaChiNhanh, sheet);
        maChiNhanhHeaderCell.setCellStyle(centerCellStyle);

        Cell chiNhanhHeaderCell = titleRow.createCell(2);
        chiNhanhHeaderCell.setCellValue("CHI NHÁNH");
        setBordersForCell(headerChiNhanh, sheet);
        chiNhanhHeaderCell.setCellStyle(centerCellStyle);

        Cell maBuuCucHeaderCell = titleRow.createCell(3);
        maBuuCucHeaderCell.setCellValue("MÃ BƯU CỤC");
        setBordersForCell(headerMaBuuCuc, sheet);
        maBuuCucHeaderCell.setCellStyle(centerCellStyle);

        Cell tenBuuCucHeaderCell = titleRow.createCell(4);
        tenBuuCucHeaderCell.setCellValue("BƯU CỤC");
        setBordersForCell(headerTenBuuCuc, sheet);
        tenBuuCucHeaderCell.setCellStyle(centerCellStyle);

        Cell zoneHeaderCell = titleRow.createCell(5);
        zoneHeaderCell.setCellValue("VTP");
        setBordersForCell(zoneVTP, sheet);
        zoneHeaderCell.setCellStyle(centerCellStyle);

        Cell vtpDetailZoneHead = vtpRoleDetail.createCell(5);
        vtpDetailZoneHead.setCellValue("SỐ LƯỢNG ĐƠN");
        vtpDetailZoneHead.setCellStyle(centerCellStyle);

        Cell vtpTongTienHead = vtpRoleDetail.createCell(6);
        vtpTongTienHead.setCellValue("TỔNG TIỀN");
        vtpTongTienHead.setCellStyle(centerCellStyle);

        Cell vtpCuocHead = vtpRoleDetail.createCell(7);
        vtpCuocHead.setCellValue("CƯỚC CHIỀU ĐI");
        vtpCuocHead.setCellStyle(centerCellStyle);

        Cell vtpPhuPhiHead = vtpRoleDetail.createCell(8);
        vtpPhuPhiHead.setCellValue("PHỤ PHÍ");
        vtpPhuPhiHead.setCellStyle(centerCellStyle);

        Cell vtpChietKhauHead = vtpRoleDetail.createCell(9);
        vtpChietKhauHead.setCellValue("CHIẾT KHẤU");
        vtpChietKhauHead.setCellStyle(centerCellStyle);

        Cell grabZoneHeader = titleRow.createCell(10);
        grabZoneHeader.setCellValue("GRAB");
        setBordersForCell(zoneGrab, sheet);
        grabZoneHeader.setCellStyle(centerCellStyle);

        /* Add title detail for GRAB */
        Cell grabSoLuongDon = vtpRoleDetail.createCell(10);
        grabSoLuongDon.setCellValue("SỐ LƯỢNG ĐƠN");
        grabSoLuongDon.setCellStyle(centerCellStyle);

        Cell grabTongTien = vtpRoleDetail.createCell(11);
        grabTongTien.setCellValue("TỔNG TIỀN");
        grabTongTien.setCellStyle(centerCellStyle);

        Cell grabCuocChieuDi = vtpRoleDetail.createCell(12);
        grabCuocChieuDi.setCellValue("CƯỚC CHIỀU ĐI");
        grabCuocChieuDi.setCellStyle(centerCellStyle);

        Cell grabPhuPhi = vtpRoleDetail.createCell(13);
        grabPhuPhi.setCellValue("PHỤ PHÍ");
        grabPhuPhi.setCellStyle(centerCellStyle);

        Cell grabChietKhau = vtpRoleDetail.createCell(14);
        grabChietKhau.setCellValue("CHIẾT KHẤU");
        grabChietKhau.setCellStyle(centerCellStyle);

        Cell chenhLechZoneHeader = titleRow.createCell(15);
        chenhLechZoneHeader.setCellValue("CHÊNH LỆCH");
        setBordersForCell(zoneChenhLech, sheet);
        chenhLechZoneHeader.setCellStyle(centerCellStyle);

        /* ADD detail CHÊNH LỆCH */
        Cell chenhLechSoLuongDon = vtpRoleDetail.createCell(15);
        chenhLechSoLuongDon.setCellValue("SỐ LƯỢNG ĐƠN");
        chenhLechSoLuongDon.setCellStyle(centerCellStyle);

        Cell chenhLechTongTien = vtpRoleDetail.createCell(16);
        chenhLechTongTien.setCellValue("TỔNG TIỀN");
        chenhLechTongTien.setCellStyle(centerCellStyle);

        Cell chenhLechChietKhau = vtpRoleDetail.createCell(17);
        chenhLechChietKhau.setCellValue("CHIẾT KHẤU");
        chenhLechChietKhau.setCellStyle(centerCellStyle);

        // Create a currency format
        CellStyle currencyStyle = workbook.createCellStyle();
        currencyStyle.setDataFormat((short) 7); // Currency format code

        int stt = 1;
        for (TongHopDoiSoatGrapResponse tongHopDoiSoat : tongHopDoiSoatGrabResponses) {
            Row elementRow = sheet.createRow(stt + 4);
            int indexRow = stt + 4;

            Cell sttCell = elementRow.createCell(0);
            sttCell.setCellValue(stt);
            sttCell.setCellStyle(cellStyleDetail);

            Cell maChiNhanhCell = elementRow.createCell(1);
            maChiNhanhCell.setCellValue(tongHopDoiSoat.getMaChiNhanh());
            maChiNhanhCell.setCellStyle(cellStyleDetail);

            Cell chiNhanhCell = elementRow.createCell(2);
            chiNhanhCell.setCellValue(tongHopDoiSoat.getChiNhanh());
            chiNhanhCell.setCellStyle(cellStyleDetail);

            Cell maBuuCucCell = elementRow.createCell(3);
            maBuuCucCell.setCellValue(tongHopDoiSoat.getMaBuuCuc());
            maBuuCucCell.setCellStyle(cellStyleDetail);

            Cell buuCucCell = elementRow.createCell(4);
            buuCucCell.setCellValue(tongHopDoiSoat.getBuuCuc());
            buuCucCell.setCellStyle(cellStyleDetail);

            /*------------------- VTP --------------*/
            String vtpTongTien = roundingMode(tongHopDoiSoat.getVtpTongTien());
            String vtpCuocChieuDi = roundingMode(tongHopDoiSoat.getVtpCuocChieuDi());
            String vtpPhuPhi = roundingMode(tongHopDoiSoat.getVtpPhuPhi());
            String vtpChietKhau = roundingMode(tongHopDoiSoat.getVtpChietKhau());

            Cell vtpSoLuongDon = elementRow.createCell(5);
            vtpSoLuongDon.setCellValue(tongHopDoiSoat.getVtpSoLuongDon());
            vtpSoLuongDon.setCellStyle(cellStyleDetail);

            Cell vtpTongTienCell = elementRow.createCell(6);
            vtpTongTienCell.setCellValue(vtpTongTien);
            vtpTongTienCell.setCellStyle(cellStyleMoney);

            Cell vtpCuocChieuDiCell = elementRow.createCell(7);
            vtpCuocChieuDiCell.setCellValue(vtpCuocChieuDi);
            vtpCuocChieuDiCell.setCellStyle(cellStyleMoney);

            Cell vtpPhuPhiCell = elementRow.createCell(8);
            vtpPhuPhiCell.setCellValue(vtpPhuPhi);
            vtpPhuPhiCell.setCellStyle(cellStyleMoney);

            Cell vtpChietKhauCell = elementRow.createCell(9);
            vtpChietKhauCell.setCellValue(vtpChietKhau);
            vtpChietKhauCell.setCellStyle(cellStyleMoney);

            /*------------------- GRAB --------------*/
            String grabSoLuongDonData = tongHopDoiSoat.getGrabSoLuongDon() == null ? "" : String.valueOf(tongHopDoiSoat.getGrabSoLuongDon());
            String grabTongTienEport = roundingMode(tongHopDoiSoat.getGrabTongTien());
            String grabCuocChieuDiExport = roundingMode(tongHopDoiSoat.getGrabCuocChieuDi());
            String grabPhuPhiExport = roundingMode(tongHopDoiSoat.getGrabPhuPhi());
            String grabChietKhauExport = roundingMode(tongHopDoiSoat.getGrabChieuKhau());

            Cell grabSoLuongDonCell = elementRow.createCell(10);
            grabSoLuongDonCell.setCellValue(grabSoLuongDonData);
            grabSoLuongDonCell.setCellStyle(cellStyleDetail);

            Cell grabTongTienCell = elementRow.createCell(11);
            grabTongTienCell.setCellValue(grabTongTienEport);
            grabTongTienCell.setCellStyle(cellStyleMoney);

            Cell grabCuocChieuDiCell = elementRow.createCell(12);
            grabCuocChieuDiCell.setCellValue(grabCuocChieuDiExport);
            grabCuocChieuDiCell.setCellStyle(cellStyleMoney);

            Cell grabPhuPhiCell = elementRow.createCell(13);
            grabPhuPhiCell.setCellValue(grabPhuPhiExport);
            grabPhuPhiCell.setCellStyle(cellStyleMoney);

            Cell grabChietKhauCell = elementRow.createCell(14);
            grabChietKhauCell.setCellValue(grabChietKhauExport);
            grabChietKhauCell.setCellStyle(cellStyleMoney);

            String chenhLechTongTienExport = roundingMode(tongHopDoiSoat.getChenhLechTongTien());
            String chenhLechChietKhauExport = roundingMode(tongHopDoiSoat.getChenhLechChietKhau());

            /*------------------- CHÊNH LỆCH --------------*/
            Cell chenhLechSoLuongDonCell = elementRow.createCell(15);
            chenhLechSoLuongDonCell.setCellValue(tongHopDoiSoat.getChenhlechSoLuongDon());
            chenhLechSoLuongDonCell.setCellStyle(cellStyleMoney);

            Cell chenhLechTongTienCell = elementRow.createCell(16);
            chenhLechTongTienCell.setCellValue(chenhLechTongTienExport);
            chenhLechTongTienCell.setCellStyle(cellStyleMoney);

            Cell chenhLechChietKhauCell = elementRow.createCell(17);
            chenhLechChietKhauCell.setCellValue(chenhLechChietKhauExport);
            chenhLechChietKhauCell.setCellStyle(cellStyleMoney);

            stt += 1;
        }

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            byte[] excelContent = outputStream.toByteArray();

            String fileName = "doi_soat_tong_hop(" + fromSearch + "_" + toSearch + ").xlsx";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);

            return new ResponseEntity<>(excelContent, headers, HttpStatus.OK);
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public void setBordersForCell(CellRangeAddress mergedRegion, Sheet sheet) {
        RegionUtil.setBorderTop(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, mergedRegion, sheet);
    }

    private String roundingMode(BigDecimal input) {
        return input == null ? "" : String.valueOf(input.setScale(0, RoundingMode.HALF_UP));
    }

    public List<GrabReconciliation> processToSaveDataBaseV2(Integer fileId) throws Exception {
        List<HistoryImportExcelGrabDto> notSync = importExelReconciliationRepo.getNotSaveDataFileV2(fileId, (short) 0);

        try {
            List<GrabReconciliation> grabReconciliationList = new ArrayList<>();
            for (HistoryImportExcelGrabDto notSyncItem : notSync) {
                String keyGrabReconciliationRedis = PREFIX_RECONCILIATION_REDIS + "_" + notSyncItem.getId();
                grabReconciliationList = (List<GrabReconciliation>) redisTemplate.opsForValue().get(keyGrabReconciliationRedis);
                if (grabReconciliationList == null) {
                    grabReconciliationList = getDataFromExcelFile(notSyncItem);
                }
            }

            return grabReconciliationList;
        } catch (Exception exception) {
            throw new Exception(exception.getMessage());
        }
    }

    public void saveDataToImportExcelTable(
            Connection connection,
            List<GrabReconciliation> grabReconciliationList,
            HistoryImportExcelGrabDto notSyncItem
    ) throws Exception {
        try {
            List<List<GrabReconciliation>> chunksOfGrabRecon = ArrayUtils.chunk(grabReconciliationList, 1000);
            for (List<GrabReconciliation> chunksOfGrabReconElement : chunksOfGrabRecon) {
                String completeSql = "INSERT INTO grab_reconciliation " +
                        "(ma_van_don, trang_thai, tong_tien, cuoc_chuyen_di, phu_phi, chiet_khau, file_id, created_at) " +
                        "VALUES ";

                for (GrabReconciliation reconciliation : chunksOfGrabReconElement) {
                    String insertItemReconci = "(";
                    insertItemReconci += "'" + reconciliation.getMaVanDon() + "',";
                    insertItemReconci += "'" + reconciliation.getTrangThai() + "',";
                    insertItemReconci += "" + reconciliation.getTongTien() + ",";
                    insertItemReconci += "" + reconciliation.getCuocChuyenDi() + ",";
                    insertItemReconci += "" + reconciliation.getPhuPhi() + ",";
                    insertItemReconci += "'" + 5000 + "',";
                    insertItemReconci += "" + notSyncItem.getId() + ",";
                    insertItemReconci += "'" + reconciliation.getCreatedAt().toString() + "'";
                    insertItemReconci += "),";
                    completeSql += insertItemReconci;
                }
                completeSql = completeSql.trim();
                completeSql = completeSql.substring(0, completeSql.length() - 1);
                completeSql += ";";

                log.info("=============> INSERTED DATA TO grab_reconciliation SQL(" + grabReconciliationList.size() + ")");
                try (PreparedStatement preparedStatement = connection.prepareStatement(completeSql)) {
                    preparedStatement.executeUpdate();
                }
                connection.close();
            }
        } catch (Exception e) {
            connection.close();
            throw new Exception(e);
        } finally {
            connection.close();
        }
    }

    public void updateDataForImportExcel(HistoryImportExcelGrabDto notSyncItem) {
        if (notSyncItem == null) {
            return;
        }
        ImportExelReconciliation reconciliation = new ImportExelReconciliation();
        reconciliation.setId(notSyncItem.getId());
        reconciliation.setFileName(notSyncItem.getFileName());
        reconciliation.setDirectory(notSyncItem.getDirectory());
        reconciliation.setCreatedBy(notSyncItem.getCreatedBy());
        reconciliation.setTypeUpload(notSyncItem.getTypeUpload());
        reconciliation.setCreatedAt(notSyncItem.getCreatedAt());
        reconciliation.setUpdatedAt(LocalDateTime.now());
        reconciliation.setDataSync(DONE_UPDATE);
        importExelReconciliationRepo.save(reconciliation);
    }

    public List<GrabReconciliation> getDataFromExcelFile(HistoryImportExcelGrabDto notSyncItem) {
        try (FileInputStream inputStream = new FileInputStream(notSyncItem.getDirectory());
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            // Get the first sheet of the workbook
            Sheet sheet = workbook.getSheetAt(0);

            // Iterate through each row
            Iterator<Row> rowIterator = sheet.iterator();

            List<GrabReconciliation> grabReconciliationList = new ArrayList<>();
            List<String> listMaVanDonExcels = new ArrayList<>();

            while (rowIterator.hasNext()) {
                if (this.skip == 1) {
                    break;
                }
                Row rowElement = rowIterator.next();
                int rowNumber = rowElement.getRowNum();

                if (rowNumber > 0) {
                    validateInputData(rowElement, listMaVanDonExcels, sheet);
                    /*
                     * Tạm thời dữ liệu sẽ được lưu vào Redis để sau này sẽ dùng hàm processToSaveDataBase() để lưu dữ liệu
                     * Trường hợp nếu dữ liệu lỗi sẽ xóa toàn bộ dữ liệu trong Redis và dừng toàn bộ quá trình
                     * */
                    if (this.skip != 1) {
                        grabReconciliationList = addDataForGrabReconciliation(rowElement, notSyncItem.getCreatedAt(), grabReconciliationList);
                    }
                }
            }

            return grabReconciliationList;
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    public List<GrabReconciliation> addDataForGrabReconciliation(Row rowElement, LocalDateTime timeCreate, List<GrabReconciliation> grabReconciliationList) {
        BigDecimal cellValue = BigDecimal.valueOf(rowElement.getCell(3).getNumericCellValue());
        GrabReconciliation grabReconciliationItem = new GrabReconciliation();
        grabReconciliationItem.setMaVanDon(getValueOfMaVanDon(rowElement));
        grabReconciliationItem.setTrangThai(new Integer((int) rowElement.getCell(2).getNumericCellValue()));
        grabReconciliationItem.setTongTien(cellValue);
        grabReconciliationItem.setCuocChuyenDi(new BigDecimal(rowElement.getCell(4).getNumericCellValue()));
        grabReconciliationItem.setPhuPhi(new BigDecimal(rowElement.getCell(5).getNumericCellValue()));
        grabReconciliationItem.setChietKhau(new BigDecimal(rowElement.getCell(6).getNumericCellValue()));
        grabReconciliationItem.setCreatedAt(timeCreate);
        grabReconciliationList.add(grabReconciliationItem);

        return grabReconciliationList;
    }

    private String getValueOfMaVanDon(Row rowElement) {
        String maVanDon = "";
        if (rowElement.getCell(INDEX_MA_VAN_DON).getCellType() == CellType.NUMERIC) {
            String original = String.valueOf(rowElement.getCell(INDEX_MA_VAN_DON));
            double decimalNumber = Double.parseDouble(original);
            DecimalFormat decimalFormat = new DecimalFormat("#");
            maVanDon = decimalFormat.format(decimalNumber);
        }
        if (rowElement.getCell(INDEX_MA_VAN_DON).getCellType() == CellType.STRING) {
            maVanDon = rowElement.getCell(INDEX_MA_VAN_DON).getStringCellValue();
        }
        return maVanDon;
    }

    // 2023-06-28 ngocnt add function for export excel detail grab
    @Override
    public List<Map<String, Object>> findAllRecords(FilterDetailReconciliationReq filterDetailReconciliationReq) throws IllegalAccessException {
        List<GrabDoiSoatChiTietExcelDisplay> chiTietExcelDisplays = new ArrayList<>();

        LocalDate ngayPhatThanhCongFrom = filterDetailReconciliationReq.getNgayPhatThanhCongFrom();
        LocalDateTime ngayPhatThanhCongTo = LocalDateTime.of(filterDetailReconciliationReq.getNgayPhatThanhCongTo(), LocalTime.MAX);
        List<String> tinhKhachHangGui = filterDetailReconciliationReq.getMaChiNhanhGui();
        List<String> tinhKhachHangNhan = filterDetailReconciliationReq.getMaChiNhanhNhan();
        String maVanDo = filterDetailReconciliationReq.getMaVanDon();
        chiTietExcelDisplays = grabDoiSoatRepository.findAllList(ngayPhatThanhCongFrom, ngayPhatThanhCongTo, tinhKhachHangGui, tinhKhachHangNhan, maVanDo);
        return tryTest((List<T>) chiTietExcelDisplays);
    }

    @Override
    public void exportExcelChiTietGrab(HttpServletResponse response, FilterDetailReconciliationReq filterDetailReconciliationReq) throws IOException, IllegalAccessException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=doi_soat_grab_chi_tiet.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("ngayNhapMay", "NGAY_NHAP_MAY\n(Date of Import)");
        mapHeader.put("ngayPhatThanhCong", "Ngày phát thành công\n(Delivered Date)");
        mapHeader.put("maVanDon", "MA_VANDON\n(Bill Code)");
        mapHeader.put("orderId", "Order ID");
        mapHeader.put("khoangCach", "Khoảng cách");

        mapHeader.put("maChiNhanhGoc", "MÃ CHI NHÁNH\n(BC gốc)");
        mapHeader.put("tenChiNhanhGoc", "TÊN CHI NHÁNH\n(BC gốc)");
        mapHeader.put("maBuuCucGoc", "Mã BƯU CỤC\n(BC gốc)");
        mapHeader.put("tenBuuCucGoc", "TÊN BƯU CỤC\n(BC gốc)");
        mapHeader.put("maKhachHangGoc", "Mã_KHGUI");
//        mapHeader.put("tenKhachHangGoc", "MÃ_CHI NHÁNH\n(BC phát)");


        mapHeader.put("maChiNhanhPhat", "MÃ CHI NHÁNH\n(BC phát)");
        mapHeader.put("tenChiNhanhPhat", "TÊN CHI NHÁNH\n(BC phát)");
        mapHeader.put("maBuuCucPhat", "MÃ BƯU CỤC\n(BC phát)");
        mapHeader.put("tenBuuCucPhat", "TÊN BƯU CỤC\n(BC phát)");
        mapHeader.put("tenKhachHangNhan", "KH nhận");


        mapHeader.put("vung", "ZONE");
        mapHeader.put("maDichVuVietTel", "MA_DV_VIETTEL\n(Service code)");
        mapHeader.put("trongLuong", "TRONG_LUONG\n(weight)");

        mapHeader.put("vtpTrangThaiCuoiCung", "TRANG_THAI CUỐI CÙNG\n(Status)");
        mapHeader.put("vtpTongTien", "TỔNG TIỀN");
        mapHeader.put("vtpCuocChieuDi", "CƯỚC CHIỀU ĐI\n(Freight)");
        mapHeader.put("vtpPhuPhi", "PHỤ PHÍ");
        mapHeader.put("vtpChietKhau", "CHIẾT KHẤU");

        mapHeader.put("grabTrangThai", "TRẠNG THÁI");
        mapHeader.put("grabTongTien", "TỔNG TIỀN");
        mapHeader.put("grabCuocChieuDi", "CƯỚC CHIỀU ĐI\n(Freight)");
        mapHeader.put("grabPhuPhi", "PHỤ PHÍ");
        mapHeader.put("grabChieuKhau", "CHIẾT KHẤU");

        mapHeader.put("chenhLechTongTien", "TỔNG TIỀN");
        mapHeader.put("chenhLechCuocChieuDi", "CƯỚC CHIỀU ĐI\n(Freight)");
        mapHeader.put("chenhLechPhuPhi", "PHỤ PHÍ");
        mapHeader.put("chenhLechChietKhau", "CHIẾT KHẤU");
        mapHeader.put("trangThai", "TRẠNG THÁI");

        mapHeader.put("ghiChu", "GHI CHÚ\n(Note)");

        List<String> header1 = Arrays.asList(
                "STT",
                "NGAY_NHAP_MAY\n(Date of Import)",
                "Ngày phát thành công\n(Delivered Date)",
                "MA_VANDON\n(Bill Code)",
                "Order ID",

                "GỐC",
                "GỐC",
                "GỐC",
                "GỐC",
                "GỐC",

                "PHÁT",
                "PHÁT",
                "PHÁT",
                "PHÁT",
                "PHÁT",


                "ZONE",
                "Khoảng cách",
                "MA_DV_VIETTEL\n(Service code)",
                "TRONG_LUONG\n(weight)",

                " PHẢI THU GRAB",
                " PHẢI THU GRAB",
                " PHẢI THU GRAB",
                " PHẢI THU GRAB",
                " PHẢI THU GRAB",

                "GRAB TRẢ KẾT QUẢ ĐỐI SOÁT",
                "GRAB TRẢ KẾT QUẢ ĐỐI SOÁT",
                "GRAB TRẢ KẾT QUẢ ĐỐI SOÁT",
                "GRAB TRẢ KẾT QUẢ ĐỐI SOÁT",
                "GRAB TRẢ KẾT QUẢ ĐỐI SOÁT",

                "CHÊNH LỆCH",
                "CHÊNH LỆCH",
                "CHÊNH LỆCH",
                "CHÊNH LỆCH",
                "CHÊNH LỆCH",

                "GHI CHÚ\n(Note)"

        );
        List<String> header2 = Arrays.asList(
                "stt",
                "ngayNhapMay",
                "ngayPhatThanhCong",
                "maVanDon",
                "orderId",


                "maChiNhanhGoc",
                "tenChiNhanhGoc",
                "maBuuCucGoc",
                "tenBuuCucGoc",
                "maKhachHangGoc",
//                "tenKhachHangGoc",


                "maChiNhanhPhat",
                "tenChiNhanhPhat",
                "maBuuCucPhat",
                "tenBuuCucPhat",
                "tenKhachHangNhan",


                "vung",
                "khoangCach",
                "maDichVuVietTel",
                "trongLuong",

                "vtpTrangThaiCuoiCung",
                "vtpTongTien",
                "vtpCuocChieuDi",
                "vtpPhuPhi",
                "vtpChietKhau",

                "grabTrangThai",
                "grabTongTien",
                "grabCuocChieuDi",
                "grabPhuPhi",
                "grabChieuKhau",

                "chenhLechTongTien",
                "chenhLechCuocChieuDi",
                "chenhLechPhuPhi",
                "chenhLechChietKhau",
                "trangThai",

                "ghiChu"
        );

        List<String> header3 = Arrays.asList(
                "BIÊN BẢN ĐỐI SOÁT \n(RECONCILIATION REPORT)",
                "",
                "",
                "",
                "",

                "",
                "",
                "",
                "",
                "",

                "",
                "",
                "",
                "",
                "",

                "",
                "",
                "",
                "",

                "",
                "",
                "",
                "",
                "",

                "",
                "",
                "",
                "",
                "",

                "",
                "",
                "",
                "",

                ""
        );


        ExportExcelTemplate logTemplate = new ExportExcelTemplate(
                findAllRecords(filterDetailReconciliationReq),
                mapHeader,
                header1,
                header2,
                header3,
                "BaoCaoChiTietGrab"
        );
        logTemplate.exportDataToExcel(response);
    }

    public List<Map<String, Object>> tryTest(List<T> logSanLuongChiaTayList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();

        if (!logSanLuongChiaTayList.isEmpty()) {
            int i = 0;
            for (T x : logSanLuongChiaTayList) {
                GrabDoiSoatChiTietExcelDisplay x1 = (GrabDoiSoatChiTietExcelDisplay) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }


}