package nocsystem.indexmanager.servicesIpm.LOG;

import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.dao.TonNhapMay.TonNhapMayDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonDto;
import nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.AllTonChuaNhapMayRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonChuaNhapMayRepository2;
import nocsystem.indexmanager.services.DashBoardChiNhanh.ExportExcelTemplateCNBC;
import nocsystem.indexmanager.services.LOG.TonChuaKetNoiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;

import static java.util.Comparator.comparing;

@Service
public class TonChuaKetNoiServiceImp<T> implements TonChuaKetNoiService {
    private static final Logger logger = LoggerFactory.getLogger(TonChuaKetNoiServiceImp.class);


    private final AllTonChuaNhapMayRepository allTonChuaNhapMayRepository;
    private final TonChuaNhapMayRepository2 tonChuaNhapMayRepositoryCT;
    private final TonNhapMayDAO tonNhapMayDAO;

    private static final String FALSE = "false";
    private static final String TRUE = "true";

    public TonChuaKetNoiServiceImp(AllTonChuaNhapMayRepository allTonChuaNhapMayRepository, TonChuaNhapMayRepository2 tonChuaNhapMayRepositoryCT, TonNhapMayDAO tonNhapMayDAO) {
        this.allTonChuaNhapMayRepository = allTonChuaNhapMayRepository;
        this.tonChuaNhapMayRepositoryCT = tonChuaNhapMayRepositoryCT;
        this.tonNhapMayDAO = tonNhapMayDAO;
    }

    private Boolean isAdmin(){
        if(UserContext.getUserData().getIsAdmin().equalsIgnoreCase(TRUE) || UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_CONGTY_LOG")
                || UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_LANHDAO_CONGTYLOG"))
            return true;
        else return false;
    }

    public SimpleAPIResponseWithSum tonChuaKetNoi(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer dichVu, String loaiDon, Integer page, Integer pageSize, String sort, String sortBy) throws SQLException {
        List<String> listCN = new ArrayList<>();
        List<String> listBC = new ArrayList<>();

        Pageable pageable  = PageRequest.of(page, pageSize);


        if(isAdmin()){
            if(!maChiNhanh.equalsIgnoreCase(""))
                listCN.add(maChiNhanh);
            if(!maBuuCuc.equalsIgnoreCase(""))
                listBC.add(maBuuCuc);
        }
        else {
            Map<String, List<String>> mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");

        }
        List<TonChuaKetNoiResponse> listResult;
        List<TonChuaKetNoiResponse> listResultN1 = new ArrayList<>();
        TonChuaKetNoiResponse sum = new TonChuaKetNoiResponse();
        TonChuaKetNoiResponse sumN1 = new TonChuaKetNoiResponse();
        Map<String, TonChuaKetNoiResponse> mapN1 = new HashMap<>();

        //lấy list tồn chưa nhập máy
        listResult = tonNhapMayDAO.getListChuaNhapMay(ngayBaoCao, loaiDon, listCN, listBC, dichVu, maChiNhanh);
        listResultN1 = tonNhapMayDAO.getListChuaNhapMay(ngayBaoCao.minusDays(1), loaiDon, listCN, listBC, dichVu, maChiNhanh);
        mapN1 = toMap(listResultN1, maChiNhanh);

        Long sumSlChuaKetNoi =  Long.valueOf(0);
        Long slPhaiKetNoiSum =  Long.valueOf(0);

        Float sumKlChuaKetNoi = Float.valueOf(0) ;
        Float klPhaiKetNoiSum = Float.valueOf(0);

        //tính số từng CN, BC
        for(TonChuaKetNoiResponse ton : listResult){
            TonChuaKetNoiResponse n1 = maChiNhanh.equals("") ? mapN1.get(ton.getChiNhanh()) : mapN1.get(ton.getMaBuuCucGoc());

            //Sl phải kết nối = sl nhận thành công N + sl chưa kết nối N-1
            Long n1SlChuaKetNoi = n1 == null || n1.getSlChuaKetNoi() == null ? 0 : n1.getSlChuaKetNoi();
            Long slChuaKetNoi = ton.getSlChuaKetNoi() == null ? 0 : ton.getSlChuaKetNoi();
            Long slPhaiKetNoi = ton.getSlPhaiKetNoi() == null ? n1SlChuaKetNoi : n1SlChuaKetNoi + ton.getSlPhaiKetNoi();

            Float n1KlChuaKetNoi = n1 == null || n1.getKlChuaKetNoi() == null ? 0 : n1.getKlChuaKetNoi();
            Float klChuaKetNoi = ton.getKlChuaKetNoi() == null ? 0 : ton.getKlChuaKetNoi();
            Float klPhaiKetNoi = ton.getKlPhaiKetNoi() == null ? n1KlChuaKetNoi : n1KlChuaKetNoi + ton.getKlPhaiKetNoi();

            ton.setKlPhaiKetNoi(klPhaiKetNoi);
            ton.setSlPhaiKetNoi(slPhaiKetNoi);
            ton.setTlSlChuaKetNoi(ratioCalculator(ton.getSlChuaKetNoi(), slPhaiKetNoi));
            ton.setTlKlChuaKetNoi(klRatioCalculator(ton.getKlChuaKetNoi(), klPhaiKetNoi));

            //tính tổng kl, sl theo từng chi nhánh, bưu cục
            sumSlChuaKetNoi += slChuaKetNoi;
            sumKlChuaKetNoi += klChuaKetNoi;
            slPhaiKetNoiSum += slPhaiKetNoi;
            klPhaiKetNoiSum += klPhaiKetNoi;

            mapN1.remove(maChiNhanh.equals("") ? ton.getChiNhanh() : ton.getMaBuuCucGoc());
        }

        //check trường hợp ngày N-1 có data nhưng ngày N không có thì vẫn hiển thị data sl, kl phải phát
        if(listResult.size() < listResultN1.size()){
            for (Map.Entry<String, TonChuaKetNoiResponse> entry : mapN1.entrySet())
            {
                TonChuaKetNoiResponse ton = new TonChuaKetNoiResponse();
                if(maChiNhanh.equals("")) {
                    ton.setChiNhanh(entry.getKey());
                }
                else {
                    ton.setChiNhanh(maChiNhanh);
                    ton.setMaBuuCucGoc(entry.getKey());
                }
                ton.setSlChuaKetNoi(0L);
                ton.setKlChuaKetNoi(0F);
                ton.setTlKlChuaKetNoi(0F);
                ton.setTlSlChuaKetNoi(0F);
                ton.setSlPhaiKetNoi(entry.getValue().getSlChuaKetNoi());
                slPhaiKetNoiSum += entry.getValue().getSlChuaKetNoi();
                ton.setKlPhaiKetNoi(entry.getValue().getKlChuaKetNoi());
                klPhaiKetNoiSum += entry.getValue().getKlChuaKetNoi();
                listResult.add(ton);
            }
        }

        sum.setKlChuaKetNoi(sumKlChuaKetNoi);
        sum.setSlChuaKetNoi(sumSlChuaKetNoi);
        sum.setKlPhaiKetNoi(klPhaiKetNoiSum);
        sum.setSlPhaiKetNoi(slPhaiKetNoiSum);
        sum.setTlSlChuaKetNoi(ratioCalculator(sumSlChuaKetNoi, slPhaiKetNoiSum));
        sum.setTlKlChuaKetNoi(klRatioCalculator(sumKlChuaKetNoi, klPhaiKetNoiSum));

        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();

        if (!sort.equals(""))
            switch (sortBy.toLowerCase(Locale.ROOT)) {
                case "slchuaketnoi":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getSlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getSlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slphaiketnoi":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getSlPhaiKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getSlPhaiKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tylesanluong":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getTlSlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getTlSlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "klchuaketnoi":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getKlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getKlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "klphaiketnoi":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getKlPhaiKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getKlPhaiKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tylekhoiluong":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getTlKlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(listResult, comparing(TonChuaKetNoiResponse::getTlKlChuaKetNoi, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                default:
                    break;
            }

        //sublist lấy số lượng theo pageSize
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), listResult.size());
        if(start > listResult.size())
            return response;
        List<TonChuaKetNoiResponse> pageData = listResult.subList(start, end);

        //tạo page từ list
        Page<TonChuaKetNoiResponse> pageResult = new PageImpl<>(pageData, pageable, listResult.size());

        ListContentPageDto pageContent = new ListContentPageDto<>(pageResult);

        //get thời gian tính toán
//        Page<String> time = allTonChuaNhapMayRepository.getTimeTinhToan(ngayBaoCao, Pageable.ofSize(1));
//        String timeTinhToan = null;
//        if (time.hasNext())
//            timeTinhToan = time.getContent().get(0);

        //set thời gian tính toán
        pageContent.setTime(tonNhapMayDAO.getTimeTinhToan(ngayBaoCao, loaiDon, listCN, listBC, dichVu));
        response.setData(pageContent);
        response.setSummary(sum);

        return response;
    }

    //tính tỷ lệ sản lượng
    public Float ratioCalculator(Long tu, Long mau){
        if(mau == null || mau == 0)
            return null;
        else if(tu == null)
            return 0f;
        else {
            return  ((float) tu / (float) mau) * 100;
        }
    }

    //tính tỷ lệ khối lượng
    public Float klRatioCalculator(Float mau, Float tu){
        if(tu == null || mau == null || tu == 0)
            return null;
        else {
            return  (mau/tu) * 100;
        }
    }

    public Map<String, TonChuaKetNoiResponse> toMap (List<TonChuaKetNoiResponse> ton, String maChiNhanh){
        Map<String, TonChuaKetNoiResponse> map = new HashMap<>();
        for(TonChuaKetNoiResponse res : ton){
            map.put(maChiNhanh.equals("") ? res.getChiNhanh() : res.getMaBuuCucGoc() , res);
        }
        return map;
    }

    //lấy list chi nhánh, bưu cục cần để query theo filter + SSO
    public Map<String, List<String>> checkListCNBC(String chiNhanh, String buuCuc){
        List<String> listBC;
        List<String> listCN;

        if (chiNhanh.equals("")) {
            listCN = UserContext.getUserData().getListChiNhanhVeriable();
        }
        else {
            listCN = List.of(chiNhanh);
        }
        if (buuCuc.equals("")) {
            listBC = UserContext.getUserData().getListBuuCucVeriable();
        }
        else{
            listBC = List.of(buuCuc);
        }
        Map<String, List<String>> mapCNBC = new HashMap<>();
        mapCNBC.put("cn", listCN);
        mapCNBC.put("bc", listBC);
        return mapCNBC;
    }

    public List<Map<String, Object>> tonChuaKetNoiExcel(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer dichVu, String loaiDon) throws SQLException {
        List<DashTonHanhTrinhDto> dashTonExcelDtos = new ArrayList<>();
        List<String> listCN = new ArrayList<>();
        List<String> listBC = new ArrayList<>();

        //lấy list CN, BC để query
        if(isAdmin()){
            if(!maChiNhanh.equalsIgnoreCase(""))
                listCN.add(maChiNhanh);
            if(!maBuuCuc.equalsIgnoreCase(""))
                listBC.add(maBuuCuc);
        }
        else {
            Map<String, List<String>> mapCNBC = checkListCNBC(maChiNhanh, maBuuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");

        }

        dashTonExcelDtos = tonNhapMayDAO.getExcelChuaNhapMay(ngayBaoCao, loaiDon, listCN, listBC, dichVu);
        return tryTest((List<T>) dashTonExcelDtos);
    }

    public List<Map<String, Object>> tryTest(List<T> ton) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!ton.isEmpty()) {
            int i = 0;
            for (T x : ton) {
                DashTonHanhTrinhDto x1 = (DashTonHanhTrinhDto) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    @Override
    public void exportExcelTonChuaKetNoi(HttpServletResponse response, LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer dichVu, String loaiDon)
            throws IllegalAccessException, IOException, SQLException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=TonChuaKetNoi.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("ngayBaoCao", "Ngày Báo Cáo");
        mapHeader.put("maPhieuGui", "Mã Phiếu Gửi");
        mapHeader.put("tinhNhan", "Tỉnh Nhận");
        mapHeader.put("huyenNhan", "Huyện Nhận");
        mapHeader.put("tenHuyenNhan", "Tên Huyện Nhận");
        mapHeader.put("tinhPhat", "Tỉnh Phát");
        mapHeader.put("huyenPhat", "Huyện Phát");
        mapHeader.put("tenHuyenPhat", "Tên Huyện Phát");
        mapHeader.put("maDvViettel", "Mã Dịch vụ");
        mapHeader.put("maDvCongThem", "Mã Dịch vụ cộng thêm");
        mapHeader.put("maBuuCucGoc", "Mã Bưu Cục Gốc");
        mapHeader.put("timeTacDong", "Time Tác Động");
        mapHeader.put("trangThai", "Trạng Thái");
        mapHeader.put("maBuuCucHt", "Bưu Cục Hiện Tại");
        mapHeader.put("chiNhanhHt", "Chi Nhánh Hiện Tại");
        mapHeader.put("maDoiTac", "Mã Đối Tác");
        mapHeader.put("maKhGui", "Mã KH Gửi");
        mapHeader.put("maBuuCucPhat", "Mã Bưu Cục Phát");
        mapHeader.put("ngayTaoDon", "Ngày Tạo Đơn");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("nguoiNhapMay", "Người Nhập Máy");
        mapHeader.put("ngayNhapMay", "Ngày Nhập Máy");
        mapHeader.put("trongLuong", "Trọng lượng");
        mapHeader.put("loaiHH", "Loại Hàng Hóa");
        mapHeader.put("tienCuoc", "Tiền Cước");

        List<String> header = Arrays.asList(
                "ngayBaoCao",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tenHuyenNhan",
                "tinhPhat",
                "huyenPhat",
                "tenHuyenPhat",
                "maDvViettel",
                "maDvCongThem",
                "maBuuCucGoc",
                "timeTacDong",
                "trangThai",
                "maBuuCucHt",
                "chiNhanhHt",
                "maDoiTac",
                "maKhGui",
                "maBuuCucPhat",
                "ngayTaoDon",
                "tienCOD",
                "nguoiNhapMay",
                "ngayNhapMay",
                "trongLuong",
                "loaiHH",
                "tienCuoc"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                tonChuaKetNoiExcel(ngayBaoCao, maChiNhanh, maBuuCuc, dichVu, loaiDon),
                mapHeader,
                header,
                "TonChuaKetNoi"
        );
        tonExcelTemplate.exportDataToExcel(response);
    }
}
