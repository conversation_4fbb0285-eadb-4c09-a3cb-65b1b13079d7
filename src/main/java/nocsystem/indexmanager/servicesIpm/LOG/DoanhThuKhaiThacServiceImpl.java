package nocsystem.indexmanager.servicesIpm.LOG;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import nocsystem.indexmanager.dao.LOG.DoanhThuKhaiThacDAO;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacExcel;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacRequestBody;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacResponse;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacTrinhKyExcel;
import nocsystem.indexmanager.services.DashBoardChiNhanh.ExportExcelTemplateCNBC;
import nocsystem.indexmanager.services.LOG.DoanhThuKhaiThacService;
import nocsystem.indexmanager.services.LOG.ExportExcelTrinhKyDoanhThu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class DoanhThuKhaiThacServiceImpl<T> implements DoanhThuKhaiThacService {
    @Autowired
    DoanhThuKhaiThacDAO doanhThuKhaiThacDAO;

    @Override
    public List<DoanhThuKhaiThacResponse> doanhThuKhaiThac(DoanhThuKhaiThacRequestBody doanhThuKhaiThacRequestBody) throws SQLException {
        List<DoanhThuKhaiThacResponse> listResult = new ArrayList<>();
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            listResult = doanhThuKhaiThacDAO.doanhThuKhaiThac(
                    doanhThuKhaiThacRequestBody.getNgayBaoCao(),
                    doanhThuKhaiThacRequestBody.getLuyKe(),
                    doanhThuKhaiThacRequestBody.getChiNhanh(),
                    doanhThuKhaiThacRequestBody.getBuuCuc(),
                    chiNhanhSSO,
                    buuCucSSO,
                    doanhThuKhaiThacRequestBody.getTtkt(),
                    doanhThuKhaiThacRequestBody.getTtktCha(),
                    doanhThuKhaiThacRequestBody.getMaDichVu(),
                    doanhThuKhaiThacRequestBody.getPage(),
                    doanhThuKhaiThacRequestBody.getPageSize()
            );
        }
        return listResult;
    }

    @Override
    public Long doanhThuKhaiThacTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException {
        Long total = 0L;
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            total = doanhThuKhaiThacDAO.doanhThuKhaiThacTotalRecord(ngayBaoCao, luyKe, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, ttkt, ttktCha, maDichVu);
        }
        return total;
    }

    @Override
    public DoanhThuKhaiThacResponse doanhThuKhaiThacTong(DoanhThuKhaiThacRequestBody doanhThuKhaiThacRequestBody) throws SQLException {
        DoanhThuKhaiThacResponse result = new DoanhThuKhaiThacResponse();
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            result = doanhThuKhaiThacDAO.doanhThuKhaiThacTong(
                    doanhThuKhaiThacRequestBody.getNgayBaoCao(),
                    doanhThuKhaiThacRequestBody.getLuyKe(),
                    doanhThuKhaiThacRequestBody.getChiNhanh(),
                    doanhThuKhaiThacRequestBody.getBuuCuc(),
                    chiNhanhSSO,
                    buuCucSSO,
                    doanhThuKhaiThacRequestBody.getTtkt(),
                    doanhThuKhaiThacRequestBody.getTtktCha(),
                    doanhThuKhaiThacRequestBody.getMaDichVu()
            );
        }
        return result;
    }

    @Override
    public void doanhThuKhaiThacExcel(HttpServletResponse response, DoanhThuKhaiThacRequestBody body) throws IllegalAccessException, IOException, SQLException {
        if (UserContext.getUserData().getIsViewDTKT().equals("true")) {
            response.setContentType("application/vnd.ms.excel");
            String headerKey = "Content-Disposition";
            String headerValue = "attachment; filename=DoanhThuKhaiThac.xlsx";
            response.setHeader(headerKey, headerValue);
            Map<String, String> mapHeader = new HashMap<>();
            mapHeader.put("chiNhanh", "Chi Nhánh Nhận");
            if (!body.getChiNhanh().equals("")) {
                mapHeader.put("buuCuc", "Bưu Cục Nhận");
            }
            mapHeader.put("slNho", "Sản Lượng <= 2kg (gram)");
            mapHeader.put("tlNho", "Trọng Lượng <= 2kg (gram)");
            mapHeader.put("doanhThuSlNho", "Doanh Thu SL <= 2kg (gram)");
            mapHeader.put("doanhThuTlNho", "Doanh Thu TL <= 2kg (đồng)");
            mapHeader.put("tongDoanhThuNho", "Tổng Doanh Thu <= 2kg (đồng)");
            mapHeader.put("slLon", "Sản Lượng > 2kg");
            mapHeader.put("tlLon", "Trọng Lượng > 2kg (gram)");
            mapHeader.put("doanhThuSlLon", "Doanh Thu SL > 2kg (đồng)");
            mapHeader.put("doanhThuTlLon", "Doanh Thu TL > 2kg (đồng)");
            mapHeader.put("tongDoanhThuLon", "Tổng Doanh Thu > 2kg (đồng)");
            mapHeader.put("tongSl", "Tổng Sản Lượng");
            mapHeader.put("tongTl", "Tổng Trọng Lượng (gram)");
            mapHeader.put("tongTlDTKT", "Tổng trọng lượng tính DTKT (gram)");
            mapHeader.put("tongDoanhThuSl", "Tổng Doanh Thu SL (đồng)");
            mapHeader.put("tongDoanhThuTl", "Tổng Doanh Thu TL (đồng)");
            mapHeader.put("tongDoanhThuTruocKpi", "Tổng Doanh Thu Trước KPI (đồng)");
            mapHeader.put("tongDoanhThuSauKpi", "Tổng Doanh Thu Sau KPI (đồng)");

            List<String> header;
            if (!body.getChiNhanh().equals("")) {
                header = Arrays.asList(
                        "chiNhanh",
                        "buuCuc",
                        "slNho",
                        "tlNho",
                        "doanhThuSlNho",
                        "doanhThuTlNho",
                        "tongDoanhThuNho",
                        "slLon",
                        "tlLon",
                        "doanhThuSlLon",
                        "doanhThuTlLon",
                        "tongDoanhThuLon",
                        "tongSl",
                        "tongTl",
                        "tongTlDTKT",
                        "tongDoanhThuSl",
                        "tongDoanhThuTl",
                        "tongDoanhThuTruocKpi",
                        "tongDoanhThuSauKpi"
                );
            } else {
                header = Arrays.asList(
                        "chiNhanh",
                        "slNho",
                        "tlNho",
                        "doanhThuSlNho",
                        "doanhThuTlNho",
                        "tongDoanhThuNho",
                        "slLon",
                        "tlLon",
                        "doanhThuSlLon",
                        "doanhThuTlLon",
                        "tongDoanhThuLon",
                        "tongSl",
                        "tongTl",
                        "tongTlDTKT",
                        "tongDoanhThuSl",
                        "tongDoanhThuTl",
                        "tongDoanhThuTruocKpi",
                        "tongDoanhThuSauKpi"
                );
            }

            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsRoleCtyLog().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                    exportExcelDoanhThuKhaiThac(body.getNgayBaoCao(), body.getLuyKe(), body.getChiNhanh(), body.getBuuCuc(), chiNhanhSSO, buuCucSSO, body.getTtkt(), body.getTtktCha(), body.getMaDichVu()),
                    mapHeader,
                    header,
                    "DoanhThuKhaiThac"
            );
            tonExcelTemplate.exportDataToExcel(response);
        }
    }

    @Override
    public void doanhThuKhaiThacExcelTrinhKy(HttpServletResponse response, DoanhThuKhaiThacRequestBody body) throws IllegalAccessException, IOException, SQLException {
        if (UserContext.getUserData().getIsTrinhKyDTKT().equals("true")) {
            ExportExcelTrinhKyDoanhThu excelTrinhKyDoanhThu = new ExportExcelTrinhKyDoanhThu(exportExcelTrinhKyDoanhThu(body.getNgayBaoCao(), body.getLuyKe()), body.getNgayBaoCao());
            excelTrinhKyDoanhThu.exportDataToExcel(response);
        }
    }



    private List<Map<String, Object>> exportExcelDoanhThuKhaiThac(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, List<String> chiNhanhSSO, List<String> buuCucSSO, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException{
        List<DoanhThuKhaiThacExcel> doanhThuKhaiThacExcels = new ArrayList<>();
        doanhThuKhaiThacExcels = doanhThuKhaiThacDAO.exportExcelDoanhThuKhaiThac(ngayBaoCao, luyKe, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, ttkt, ttktCha, maDichVu);
        return tryTest((List<T>) doanhThuKhaiThacExcels);
    }

    private List<DoanhThuKhaiThacTrinhKyExcel> exportExcelTrinhKyDoanhThu(LocalDate ngayBaoCao, Integer luyKe) throws SQLException{
        List<DoanhThuKhaiThacTrinhKyExcel> doanhThuKhaiThacExcels = new ArrayList<>();
        doanhThuKhaiThacExcels = doanhThuKhaiThacDAO.exportExcelTrinhKyDoanhThu(ngayBaoCao, luyKe);
        return doanhThuKhaiThacExcels;
    }

    public List<Map<String, Object>> tryTest(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                DoanhThuKhaiThacExcel x1 = (DoanhThuKhaiThacExcel) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }
}
