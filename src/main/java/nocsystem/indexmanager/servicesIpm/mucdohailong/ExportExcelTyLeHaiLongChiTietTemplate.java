package nocsystem.indexmanager.servicesIpm.mucdohailong;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class ExportExcelTyLeHaiLongChiTietTemplate {
    private final SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    List<Map<String, Object>> dataList;
    Map<String, String> mapHeader;
    List<String> headerRow1;
    List<String> headerRow2;
    String sheetName;

    public ExportExcelTyLeHaiLongChiTietTemplate(){
        workbook = new SXSSFWorkbook(1);

    }

    public ExportExcelTyLeHaiLongChiTietTemplate(
        List<Map<String, Object>> dataList,
        Map<String, String> mapHeader,
        List<String> headerRow1,
        List<String> headerRow2,
        String sheetName
    ) {
        this.dataList = dataList;
        this.mapHeader = mapHeader;
        this.headerRow1 = headerRow1;
        this.headerRow2 = headerRow2;
        this.sheetName = sheetName;
        workbook = new SXSSFWorkbook(1);
    }

    private void createCell(SXSSFRow row, int columnCount, Object value, CellStyle style) {
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof BigInteger) {
            cell.setCellValue(((BigInteger) value).longValue());
        }else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }


    private void createHeaderRowAll() {
        sheet = workbook.createSheet(sheetName);
        workbook.setCompressTempFiles(true);
        SXSSFRow row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        Map<String, Integer> mapHeader2 = new LinkedHashMap<>();
        for (String x : headerRow1) {
            if (!mapHeader2.containsKey(x)) {
                mapHeader2.put(x, 1);
            } else {
                mapHeader2.put(x, mapHeader2.get(x) + 1);
            }
        }

        int soCot = 0;
        for (String x : headerRow1) {
            createCell(row, soCot, x, style);
            soCot++;
        }
        SXSSFRow row1 = sheet.createRow(1);

        soCot = 0;
        for (String x : headerRow2) {
            createCell(row1, soCot, mapHeader.get(x), style);
            sheet.setColumnWidth(soCot, ((int) (mapHeader.get(x).length() * 1.5)) * 210);
            soCot++;
        }

        for (int j = 0; j < headerRow1.size(); j++) {
            if (mapHeader.get(headerRow2.get(j)).equals(headerRow1.get(j))) {
                sheet.addMergedRegion(new CellRangeAddress(0, 1, j, j));
            }
        }

        for (int i = 0; i < headerRow1.size(); i++) {
            if (mapHeader2.get(headerRow1.get(i)) > 1) {
                sheet.addMergedRegion(new CellRangeAddress(0, 0, i, i + mapHeader2.get(headerRow1.get(i)) - 1));
                mapHeader2.put(headerRow1.get(i), mapHeader2.get(headerRow1.get(i)) - mapHeader2.get(headerRow1.get(i)) + 1);
            }
        }
    }

    public void writeDataALL() throws IOException {
        int rownum = 2;
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        int countColumn = 0;
        if (dataList != null) {
            for (Map<String, Object> x : dataList) {
                SXSSFRow row = sheet.createRow(rownum);
                for (int i = 0; i < headerRow2.size(); i++) {
                    if (i == 0) {
                        createCell(row, i, ++countColumn, style);
                    } else {
                        if(x.get(headerRow2.get(i))==null){
                            createCell(row, i, "", style);
                        }else {
                            createCell(row, i, x.get(headerRow2.get(i)), style);
                        }
                    }
                }
                rownum++;
            }
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
        createHeaderRowAll();
        writeDataALL();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
            workbook.dispose();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            workbook.close();
        }
    }
}
