package nocsystem.indexmanager.servicesIpm.mucdohailong.mobile;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.controllers.mucdohailong.mobile.ChiNhanhVungMap;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.MucDoHaiLongOverview;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.TyLeKHDto;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.TyTrongNguyenNhanKHLMobileDto;
import nocsystem.indexmanager.services.mucdohailong.mobile.MucDoHaiLongMobileService;
import nocsystem.indexmanager.servicesIpm.mucdohailong.TyLeHaiLongServiceImpl;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class MucDoHaiLongMobileServiceImpl extends AbstractDao implements MucDoHaiLongMobileService {
    private final Logger log = LoggerFactory.getLogger(TyLeHaiLongServiceImpl.class);

    @Override
    public TyLeKHDto getTyLeKhachHang(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type, String name) {
        TyLeKHDto dto = new TyLeKHDto();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return dto;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";
        } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh);
        } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh, maBuuCuc);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, selectedVung, listChiNhanh, maChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, maBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc, maBuuCuc);
            }

        }

        String sql = "SELECT  SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_KHAUNHAN) AS TONG_DG_KHAUNHAN, SUM(TONG_DG_KHAUNHAN_HAILONG) AS TONG_DG_KHAUNHAN_HAILONG, " +
                "SUM(TONG_DG_KHAUGIAO) AS TONG_DG_KHAUGIAO, SUM(TONG_DG_KHAUGIAO_HAILONG) AS TONG_DG_KHAUGIAO_HAILONG, " +
                "SUM(TONG_KHAOSAT) AS TONG_KHAOSAT, SUM(TONG_PHANHOI) AS TONG_PHANHOI, SUM(TONG_KH_TU_DANHGIA) AS TONG_KH_TU_DANHGIA, " +
                "SUM(TONG_KS_KHAUGIAO) AS TONG_KS_KHAUGIAO, SUM(TONG_PH_KHAUGIAO) AS TONG_PH_KHAUGIAO, SUM(TONG_KH_TU_DANHGIA_KHAUGIAO) AS TONG_KH_TU_DANHGIA_KHAUGIAO, " +
                "SUM(TONG_KS_KHAUNHAN) AS TONG_KS_KHAUNHAN, SUM(TONG_PH_KHAUNHAN) AS TONG_PH_KHAUNHAN, SUM(TONG_KH_TU_DANHGIA_KHAUNHAN) AS TONG_KH_TU_DANHGIA_KHAUNHAN " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition;

        if (sql.contains("VUNG")) {
            sql += "GROUP BY VUNG";
            if (sql.contains("MA_CHINHANH")) {
                sql += ", MA_CHINHANH";
                if (sql.contains("MA_BUUCUC")) {
                    sql += ", MA_BUUCUC";
                }
            }
        }

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                switch (name) {
                    case "tyLeKHPhanHoi":
                        dto = new TyLeKHDto(
                                "tyLeKHPhanHoi",
                                rs.getLong("TONG_KHAOSAT"),
                                rs.getLong("TONG_PHANHOI"),
                                rs.getLong("TONG_KS_KHAUNHAN"),
                                rs.getLong("TONG_PH_KHAUNHAN"),
                                rs.getLong("TONG_KS_KHAUGIAO"),
                                rs.getLong("TONG_PH_KHAUGIAO"),
                                null, null, null, null, null, null
                        );
                        break;
                    case "tyLeKHHaiLong":
                        dto = new TyLeKHDto(
                                "tyLeKHHaiLong",
                                null, null, null, null, null, null,
                                rs.getLong("TONG_DG"),
                                rs.getLong("TONG_DG_HAILONG"),
                                rs.getLong("TONG_DG_KHAUNHAN"),
                                rs.getLong("TONG_DG_KHAUNHAN_HAILONG"),
                                rs.getLong("TONG_DG_KHAUGIAO"),
                                rs.getLong("TONG_DG_KHAUGIAO_HAILONG")
                        );
                        break;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return dto;
    }

    @Override
    public List<TyTrongNguyenNhanKHLMobileDto> getNguyenNhanKhongHaiLong(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type) {
        TyTrongNguyenNhanKHLMobileDto dto = new TyTrongNguyenNhanKHLMobileDto();
        List<TyTrongNguyenNhanKHLMobileDto> response = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return response;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";
        } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh);
        } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh, maBuuCuc);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, selectedVung, listChiNhanh, maChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, maBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc, maBuuCuc);
            }

        }

        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_K_HAILONG_NV) AS TONG_DG_K_HAILONG_NV, SUM(TONG_DG_K_HAILONG_CLPV) AS TONG_DG_K_HAILONG_CLPV, " +
                "SUM(TONG_DG_K_HAILONG_CLDV) AS TONG_DG_K_HAILONG_CLDV, SUM(TONG_DG_K_HAILONG_KN) AS TONG_DG_K_HAILONG_KN, " +
                "SUM(TONG_DG_K_HAILONG_LICN) AS TONG_DG_K_HAILONG_LICN, SUM(TONG_DG_K_HAILONG_KHAC) AS TONG_DG_K_HAILONG_KHAC " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                TyTrongNguyenNhanKHLMobileDto tyTrongTongQuan = new TyTrongNguyenNhanKHLMobileDto("tongQuan", rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG"), rs.getLong("TONG_DG"));
                TyTrongNguyenNhanKHLMobileDto tyTrongNV = new TyTrongNguyenNhanKHLMobileDto("nghiepVu", rs.getLong("TONG_DG_K_HAILONG_NV"), rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG"));
                TyTrongNguyenNhanKHLMobileDto tyTrongCLPV = new TyTrongNguyenNhanKHLMobileDto("chatLuongPhucVu", rs.getLong("TONG_DG_K_HAILONG_CLPV"), rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG"));
                TyTrongNguyenNhanKHLMobileDto tyTrongCLDV = new TyTrongNguyenNhanKHLMobileDto("chatLuongDichVu", rs.getLong("TONG_DG_K_HAILONG_CLDV"), rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG"));
                TyTrongNguyenNhanKHLMobileDto tyTrongKN = new TyTrongNguyenNhanKHLMobileDto("kiNang", rs.getLong("TONG_DG_K_HAILONG_KN"), rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG"));
                TyTrongNguyenNhanKHLMobileDto tyTrongLICN = new TyTrongNguyenNhanKHLMobileDto("loiIchCaNhan", rs.getLong("TONG_DG_K_HAILONG_LICN"), rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG"));
                TyTrongNguyenNhanKHLMobileDto tyTrongKhac = new TyTrongNguyenNhanKHLMobileDto("khac", rs.getLong("TONG_DG_K_HAILONG_KHAC"), rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG"));
                response.add(tyTrongTongQuan);
                response.add(tyTrongNV);
                response.add(tyTrongCLPV);
                response.add(tyTrongCLDV);
                response.add(tyTrongKN);
                response.add(tyTrongLICN);
                response.add(tyTrongKhac);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public List<TyTrongNguyenNhanKHLMobileDto> getSoLoiNghiemTrong(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type) {
        TyTrongNguyenNhanKHLMobileDto soLoiNghiemTrong = new TyTrongNguyenNhanKHLMobileDto("Số lỗi nghiêm trọng", 0L, 0L);
        List<TyTrongNguyenNhanKHLMobileDto> response = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return response;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";
        } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh);
        } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh, maBuuCuc);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, selectedVung, listChiNhanh, maChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, maBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc, maBuuCuc);
            }

        }

        Long tongDG = getTongDanhGiaCuaKH(ngayBaoCao, maChiNhanh, maBuuCuc, type, listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DANHGIA, SUM(LOI_THAIDO_PHUCVU_KEM) AS LOI_THAIDO_PHUCVU_KEM, SUM(LOI_GACH_TT_AO) AS LOI_GACH_TT_AO " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {

                soLoiNghiemTrong = new TyTrongNguyenNhanKHLMobileDto("tongQuan", rs.getLong("LOI_THAIDO_PHUCVU_KEM") + rs.getLong("LOI_GACH_TT_AO"), rs.getLong("TONG_DANHGIA"));
                TyTrongNguyenNhanKHLMobileDto tyTrongThaiDoKem = new TyTrongNguyenNhanKHLMobileDto("thaiDoKem", rs.getLong("LOI_THAIDO_PHUCVU_KEM"), rs.getLong("TONG_DANHGIA"));
                response.add(tyTrongThaiDoKem);
                TyTrongNguyenNhanKHLMobileDto tyTrongKHChuaNhanHang = new TyTrongNguyenNhanKHLMobileDto("chuaNhanHang", rs.getLong("LOI_GACH_TT_AO"), rs.getLong("TONG_DANHGIA"));
                response.add(tyTrongKHChuaNhanHang);
            }
            soLoiNghiemTrong.setTongDG(tongDG);
            soLoiNghiemTrong.setTyTrong(MucDoHaiLongUtils.getTyLeHaiLong(soLoiNghiemTrong.getTongDG(), soLoiNghiemTrong.getTongDGKhongHaiLong()));
            response.add(0, soLoiNghiemTrong);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public List<TyTrongNguyenNhanKHLMobileDto> getNguyenNhanKhongHaiLongDetails(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type, String name) {
        List<TyTrongNguyenNhanKHLMobileDto> response = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return response;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                sqlCondition += "NGAY_KH_PHANHOI >= " + convertedNgayDauThang + " AND NGAY_KH_PHANHOI <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_KH_PHANHOI = " + convertedNgayBaoCao + " ";
                break;
        }

        if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' ", maChiNhanh);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_CHINHANH in (%s) ", listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }

        if (maBuuCuc != null && !maBuuCuc.isEmpty()) {
            sqlCondition += String.format("AND MA_BUUCUC = '%s' ", maBuuCuc);
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_BUUCUC in (%s) ", listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }

        switch (name) {
            case "nghiepVu":
                sqlCondition += "AND PHANLOAI_DANHGIA = 'Nhóm hành vi về nghiệp vụ' ";
                break;
            case "chatLuongPhucVu":
                sqlCondition += "AND PHANLOAI_DANHGIA = 'Chất lượng phục vụ' ";
                break;
            case "chatLuongDichVu":
                sqlCondition += "AND PHANLOAI_DANHGIA = 'Chất lượng dịch vụ' ";
                break;
            case "kiNang":
                sqlCondition += "AND PHANLOAI_DANHGIA = 'Nhóm hành vi về kỹ năng' ";
                break;
            case "loiIchCaNhan":
                sqlCondition += "AND PHANLOAI_DANHGIA = 'Nhóm hành vi lợi ích cá nhân' ";
                break;
            case "khac":
                sqlCondition += "AND PHANLOAI_DANHGIA = 'Khác' ";
                break;
        }

        Long tongDGKhongHaiLong = getTongDanhGiaKhongHaiLongCuaKH(ngayBaoCao, maChiNhanh, maBuuCuc, type, listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT NGUYEN_NHAN_SAU_XM, count (NGUYEN_NHAN_SAU_XM) AS TONG_DG_KHONG_HAI_LONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_CHITIET WHERE " + sqlCondition +
                "AND ((rate in ('1','2','3') and trang_thai_xu_ly in ('Chốt', 'Chốt_Yêu cầu xác minh sai')) " +
                "or (rate in ('4','5') and trang_thai_xu_ly = 'Chốt') " +
                "or (ketqua_danhgia = 'Thông tin không đúng' and nguyen_nhan_sau_xm = 'Thái độ nhân viên kém' and trang_thai_xu_ly in ('Chốt', 'Chốt_Yêu cầu xác minh sai'))) " +
                "GROUP BY NGUYEN_NHAN_SAU_XM";
        System.out.println("sql " + sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                TyTrongNguyenNhanKHLMobileDto dto = new TyTrongNguyenNhanKHLMobileDto(rs.getString("NGUYEN_NHAN_SAU_XM"), rs.getLong("TONG_DG_KHONG_HAI_LONG"), tongDGKhongHaiLong);
                response.add(dto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public MucDoHaiLongOverview getTyLeKhachHangOverview(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type) {
        MucDoHaiLongOverview dto = new MucDoHaiLongOverview();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return dto;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlEntity = "";
        String sqlCondition = "";

        if (!Objects.isNull(maChiNhanh) && maChiNhanh.equals("CNTCT")) {
            return dto;
        } else {
            /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
            int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
            int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }

            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";
            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
                if (vungValue != null) {
                    sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
                }
                sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh);
            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
                if (vungValue != null) {
                    sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
                }
                sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh, maBuuCuc);
            }

            if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
                sqlCondition = new String("");
                switch (type) {
                    case "0":
                        sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                        break;
                    case "1":
                        sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                        break;
                }
                String listChiNhanh = listChiNhanhSSO.stream()
                        .map(giaTri -> "'" + giaTri + "'")
                        .collect(Collectors.joining(", "));

                // lay list vung tu list chi nhanh SSO
                String listVung = null;
                for (String x : listChiNhanhSSO) {
                    String vungValue = ChiNhanhVungMap.collection.get(x);
                    if (vungValue != null) {
                        listVung = String.join("', '", vungValue);
                    }
                }

                // dua list vung SSO vao query
                if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                    sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

                } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                    String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                    sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, selectedVung, listChiNhanh, maChiNhanh);

                } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                    String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                    sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                            listVung, selectedVung, listChiNhanh, maChiNhanh, maBuuCuc);
                }
            }

            if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
                sqlCondition = new String("");
                switch (type) {
                    case "0":
                        sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                        break;
                    case "1":
                        sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                        break;
                }
                String listChiNhanh = listChiNhanhSSO.stream()
                        .map(giaTri -> "'" + giaTri + "'")
                        .collect(Collectors.joining(", "));
                String listBuuCuc = listBuuCucSSO.stream()
                        .map(giaTri -> "'" + giaTri + "'")
                        .collect(Collectors.joining(", "));

                // lay list vung tu list chi nhanh SSO
                String listVung = null;
                for (String x : listChiNhanhSSO) {
                    String vungValue = ChiNhanhVungMap.collection.get(x);
                    if (vungValue != null) {
                        listVung = String.join("', '", vungValue);
                    }
                }

                // dua list vung SSO vao query
                if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                    sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

                } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                    String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                    sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ",
                            listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc);

                } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                    String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                    sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                            listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc, maBuuCuc);
                }
            }

            String sql = "SELECT " + sqlEntity + " SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                    "SUM(TONG_KHAOSAT) AS TONG_KHAOSAT, SUM(TONG_PHANHOI) AS TONG_PHANHOI, SUM(TONG_KH_TU_DANHGIA) AS TONG_KH_TU_DANHGIA, " +
                    "SUM(LOI_THAIDO_PHUCVU_KEM) AS LOI_THAIDO_PHUCVU_KEM, SUM(LOI_GACH_TT_AO) AS LOI_GACH_TT_AO " +
                    "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition;

            if (sql.contains("VUNG")) {
                sql += "GROUP BY VUNG";
                if (sql.contains("MA_CHINHANH")) {
                    sql += ", MA_CHINHANH";
                    if (sql.contains("MA_BUUCUC")) {
                        sql += ", MA_BUUCUC";
                    }
                }
            }

            try {
                conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
                stmt = conn.prepareStatement(sql);
                System.out.println("-----------------------------SQL{}: " + stmt.toString());
                rs = stmt.executeQuery();
                while (rs.next()) {
                    dto = new MucDoHaiLongOverview(
                            sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                            sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                            sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                            rs.getLong("TONG_KHAOSAT"),
                            rs.getLong("TONG_PHANHOI"),
                            rs.getLong("TONG_DG"),
                            rs.getLong("TONG_DG_HAILONG"),
                            rs.getLong("LOI_THAIDO_PHUCVU_KEM"),
                            rs.getLong("LOI_GACH_TT_AO")
                    );
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
                throw new RuntimeException(e);
            } finally {
                releaseConnect(conn, stmt, rs);
            }
            if (null == dto.getMaChiNhanh() && null == dto.getMaBuuCuc()) {
                dto.setMaChiNhanh(maChiNhanh);
                dto.setMaBuuCuc(maBuuCuc);
            }
            return dto;
        }
    }

    private Long getTongDanhGiaCuaKH(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long tongDG = 0L;
        String sqlCondition = "";
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";
        } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh);
        } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh, maBuuCuc);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, selectedVung, listChiNhanh, maChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, maBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc, maBuuCuc);
            }
        }

        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DG FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                tongDG = rs.getLong("TONG_DG");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tongDG;
    }

    private Long getTongDanhGiaKhongHaiLongCuaKH(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long tongDGKhongHaiLong = 0L;
        String sqlCondition = "";
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";
        } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh);
        } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
            String vungValue = ChiNhanhVungMap.collection.get(maChiNhanh);
            if (vungValue != null) {
                sqlCondition += String.format("AND VUNG = '%s' ", vungValue);
            }
            sqlCondition += String.format("AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ", maChiNhanh, maBuuCuc);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, selectedVung, listChiNhanh, maChiNhanh);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, maBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            // lay list vung tu list chi nhanh SSO
            String listVung = null;
            for (String x : listChiNhanhSSO) {
                String vungValue = ChiNhanhVungMap.collection.get(x);
                if (vungValue != null) {
                    listVung = String.join("', '", vungValue);
                }
            }

            // dua list vung SSO vao query
            if (StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                sqlCondition += String.format("AND VUNG in ('%s') AND MA_CHINHANH in (%s) AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc);

            } else if (!StringUtils.isEmpty(maChiNhanh) && !StringUtils.isEmpty(maBuuCuc)) {
                String selectedVung = ChiNhanhVungMap.collection.get(maChiNhanh);
                sqlCondition += String.format("AND VUNG in ('%s') AND VUNG = '%s' AND MA_CHINHANH in (%s) AND MA_CHINHANH = '%s' AND MA_BUUCUC in (%s) AND MA_BUUCUC = '%s' AND TUYEN_BUUTA = 'ALL' ",
                        listVung, selectedVung, listChiNhanh, maChiNhanh, listBuuCuc, maBuuCuc);
            }
        }

        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            rs = stmt.executeQuery();
            while (rs.next()) {
                tongDGKhongHaiLong = rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tongDGKhongHaiLong;
    }
}
