package nocsystem.indexmanager.servicesIpm.mucdohailong;

import nocsystem.indexmanager.config.DateTimeUltis;
import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.constants.MucDoHaiLongConstants;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.mucdohailong.*;
import nocsystem.indexmanager.models.mucdohailong.KpiMucDoHaiLong;
import nocsystem.indexmanager.repositories.mucdohailong.KpiMucDoHaiLongRepository;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRqExcel;
import nocsystem.indexmanager.services.mucdohailong.TyLeHaiLongService;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static nocsystem.indexmanager.config.DateTimeUltis.convertLongTimestampToMMDDYYYY;

@Service
public class TyLeHaiLongServiceImpl<T> extends AbstractDao implements TyLeHaiLongService {

    private final KpiMucDoHaiLongRepository kpiMucDoHaiLongRepository;
    private final Logger log = LoggerFactory.getLogger(TyLeHaiLongServiceImpl.class);

    private static Map<String, Double> monthKPIMap = new HashMap<>();

    public TyLeHaiLongServiceImpl(KpiMucDoHaiLongRepository kpiMucDoHaiLongRepository) {
        this.kpiMucDoHaiLongRepository = kpiMucDoHaiLongRepository;
    }

    @Override
    public List<TyLeHaiLongDashboardDto> getTyLeHaiLongDashBoard(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException {
        TyLeHaiLongDto dto = new TyLeHaiLongDto();
        List<TyLeHaiLongDashboardDto> response = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return response;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> sqlConditionMap = getSqlCondition(null, ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_KHAUNHAN) AS TONG_DG_KHAUNHAN, SUM(TONG_DG_KHAUNHAN_HAILONG) AS TONG_DG_KHAUNHAN_HAILONG, " +
                "SUM(TONG_DG_KHAUGIAO) AS TONG_DG_KHAUGIAO, SUM(TONG_DG_KHAUGIAO_HAILONG) AS TONG_DG_KHAUGIAO_HAILONG, " +
                "SUM(TONG_DG_KENHBT) AS TONG_DG_KENHBT, SUM(TONG_DG_KENHBT_HAILONG) AS TONG_DG_KENHBT_HAILONG, " +
                "SUM(TONG_DG_KENHBC) AS TONG_DG_KENHBC, SUM(TONG_DG_KENHBC_HAILONG) AS TONG_DG_KENHBC_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlConditionMap.get("sqlCondition");
        System.out.println("-----------------------------SQL{}: " + sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new TyLeHaiLongDto(
                        rs.getLong("TONG_DG"),
                        rs.getLong("TONG_DG_HAILONG"),
                        rs.getLong("TONG_DG_KHAUNHAN"),
                        rs.getLong("TONG_DG_KHAUNHAN_HAILONG"),
                        rs.getLong("TONG_DG_KHAUGIAO"),
                        rs.getLong("TONG_DG_KHAUGIAO_HAILONG"),
                        rs.getLong("TONG_DG_KENHBT"),
                        rs.getLong("TONG_DG_KENHBT_HAILONG"),
                        rs.getLong("TONG_DG_KENHBC"),
                        rs.getLong("TONG_DG_KENHBC_HAILONG"));
            }

            if (dto != null) {
                TyLeHaiLongDashboardDto tyLeTong = new TyLeHaiLongDashboardDto(dto, "Tong");
                TyLeHaiLongDashboardDto tyLeKhauGiao = new TyLeHaiLongDashboardDto(dto, "KhauGiao");
                TyLeHaiLongDashboardDto tyLeKhauNhan = new TyLeHaiLongDashboardDto(dto, "KhauNhan");
                TyLeHaiLongDashboardDto tyLeKenhBT = new TyLeHaiLongDashboardDto(dto, "KenhBT");
                TyLeHaiLongDashboardDto tyLeKenhBC = new TyLeHaiLongDashboardDto(dto, "KenhBC");

                List<Double> tyleCungKyList = getTyLeHaiLongCungKy(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
                if (!tyleCungKyList.isEmpty() && !Objects.isNull(tyleCungKyList)) {
                    if (!Objects.isNull(tyleCungKyList.get(0))) {
                        tyLeTong.setTangTruongThang(tyLeTong.getTyLeHaiLong() - tyleCungKyList.get(0).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(1))) {
                        tyLeKhauGiao.setTangTruongThang(tyLeKhauGiao.getTyLeHaiLong() - tyleCungKyList.get(1).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(2))) {
                        tyLeKhauNhan.setTangTruongThang(tyLeKhauNhan.getTyLeHaiLong() - tyleCungKyList.get(2).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(3))) {
                        tyLeKenhBT.setTangTruongThang(tyLeKenhBT.getTyLeHaiLong() - tyleCungKyList.get(3).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(4))) {
                        tyLeKenhBC.setTangTruongThang(tyLeKenhBC.getTyLeHaiLong() - tyleCungKyList.get(4).doubleValue());
                    }
                }


                List<KpiMucDoHaiLong> list = getKpiRecords(ngayBaoCao, vung, maChiNhanh);
                if (list != null && !list.isEmpty()) {
                    List<KpiMucDoHaiLong> listTong = list.stream().filter(e -> "Tổng".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKhauGiao = list.stream().filter(e -> "Khâu giao".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKhauNhan = list.stream().filter(e -> "Khâu nhận".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKenhBT = list.stream().filter(e -> "Kênh bưu tá".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKenhBC = list.stream().filter(e -> "Kênh bưu cục".equals(e.getTieuChi())).collect(Collectors.toList());
                    Double kpiTong = caculateAverageKpi(ngayBaoCao, listTong);
                    if (kpiTong != null && tyLeTong.getTyLeHaiLong() >= kpiTong) {
                        tyLeTong.setDanhGiaTLHL(true);
                    } else tyLeTong.setDanhGiaTLHL(false);
                    tyLeTong.setKpi(kpiTong);

                    Double kpiKhauGiao = caculateAverageKpi(ngayBaoCao, listKhauGiao);
                    if (kpiKhauGiao != null && tyLeKhauGiao.getTyLeHaiLong() >= kpiKhauGiao) {
                        tyLeKhauGiao.setDanhGiaTLHL(true);
                    } else tyLeKhauGiao.setDanhGiaTLHL(false);
                    tyLeKhauGiao.setKpi(kpiKhauGiao);

                    Double kpiKhauNhan = caculateAverageKpi(ngayBaoCao, listKhauNhan);
                    if (kpiKhauNhan != null && tyLeKhauNhan.getTyLeHaiLong() >= kpiKhauNhan) {
                        tyLeKhauNhan.setDanhGiaTLHL(true);
                    } else tyLeKhauNhan.setDanhGiaTLHL(false);
                    tyLeKhauNhan.setKpi(kpiKhauNhan);

                    Double kpiKenhBT = caculateAverageKpi(ngayBaoCao, listKenhBT);
                    if (kpiKenhBT != null && tyLeKenhBT.getTyLeHaiLong() >= kpiKenhBT) {
                        tyLeKenhBT.setDanhGiaTLHL(true);
                    } else tyLeKenhBT.setDanhGiaTLHL(false);
                    tyLeKenhBT.setKpi(kpiKenhBT);

                    Double kpiKenhBC = caculateAverageKpi(ngayBaoCao, listKenhBC);
                    if (kpiKenhBC != null && tyLeKenhBC.getTyLeHaiLong() >= kpiKenhBC) {
                        tyLeKenhBC.setDanhGiaTLHL(true);
                    } else tyLeKenhBC.setDanhGiaTLHL(false);
                    tyLeKenhBC.setKpi(kpiKenhBC);
                }
                response.add(tyLeTong);
                response.add(tyLeKhauGiao);
                response.add(tyLeKhauNhan);
                response.add(tyLeKenhBT);
                response.add(tyLeKenhBC);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public PageWithTotalNumber<TyLeHaiLongDto> getBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request) throws NoSuchFieldException, IllegalAccessException {
        PageWithTotalNumber<TyLeHaiLongDto> resultPage = null;
        TyLeHaiLongDto dto = new TyLeHaiLongDto();
        List<TyLeHaiLongDto> response = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        Double kpiTong = (double) 0;
        Double kpiKhauGiao = (double) 0;
        Double kpiKhauNhan = (double) 0;
        /** List param để tạo dòng Tổng trên bảng bảo cáo **/
        TyLeHaiLongDto totalRow = null;
        Long tongDG = 0L;
        Long tongDGHaiLong = 0L;
        Long tongDGKhauNhan = 0L;
        Long tongDGKhauNhanHaiLong = 0L;
        Long tongDGKhauGiao = 0L;
        Long tongDGKhauGiaoHaiLong = 0L;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlEntity = "VUNG, MA_CHINHANH";
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(request.getNgayBaoCao().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (request.getType()) {
            case "0":
                LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            sqlCondition += "AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            String listBuuCuc = String.join("', '", request.getMaBuuCuc());
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            String listBuuCuc = String.join("', '", request.getMaBuuCuc());
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (request.getType()) {
                case "0":
                    LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCuc = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCuc = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (request.getType()) {
                case "0":
                    LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }

            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCucFilter = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCucFilter = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s)AND TUYEN_BUUTA <> 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_KHAUNHAN) AS TONG_DG_KHAUNHAN, SUM(TONG_DG_KHAUNHAN_HAILONG) AS TONG_DG_KHAUNHAN_HAILONG, " +
                "SUM(TONG_DG_KHAUGIAO) AS TONG_DG_KHAUGIAO, SUM(TONG_DG_KHAUGIAO_HAILONG) AS TONG_DG_KHAUGIAO_HAILONG, " +
                "SUM(TONG_DG_KENHBT) AS TONG_DG_KENHBT, SUM(TONG_DG_KENHBT_HAILONG) AS TONG_DG_KENHBT_HAILONG, " +
                "SUM(TONG_DG_KENHBC) AS TONG_DG_KENHBC, SUM(TONG_DG_KENHBC_HAILONG) AS TONG_DG_KENHBC_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition + " AND TONG_DANHGIA <> 0 " +
                "GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new TyLeHaiLongDto(
                        rs.getString("VUNG"),
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("TONG_DG"),
                        rs.getLong("TONG_DG_HAILONG"),
                        rs.getLong("TONG_DG_KHAUNHAN"),
                        rs.getLong("TONG_DG_KHAUNHAN_HAILONG"),
                        rs.getLong("TONG_DG_KHAUGIAO"),
                        rs.getLong("TONG_DG_KHAUGIAO_HAILONG"));

                /** Caculate average KPI from database, filter via tieuChi **/
                List<KpiMucDoHaiLong> list = getKpiRecords(request.getNgayBaoCao(), request.getVung(), request.getMaChiNhanh());
                if (list != null && !list.isEmpty()) {
                    List<KpiMucDoHaiLong> listTong = list.stream().filter(e -> "Tổng".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKhauGiao = list.stream().filter(e -> "Khâu giao".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKhauNhan = list.stream().filter(e -> "Khâu nhận".equals(e.getTieuChi())).collect(Collectors.toList());
                    kpiTong = caculateAverageKpi(request.getNgayBaoCao(), listTong);
                    if (kpiTong != null && dto.getTyLeHL() >= kpiTong) {
                        dto.setDanhGiaTLHLTong(true);
                    } else dto.setDanhGiaTLHLTong(false);

                    kpiKhauGiao = caculateAverageKpi(request.getNgayBaoCao(), listKhauGiao);
                    if ( kpiKhauGiao != null && dto.getTyLeHLKhauGiao() >= kpiKhauGiao) {
                        dto.setDanhGiaTLHLKhauGiao(true);
                    } else dto.setDanhGiaTLHLKhauGiao(false);

                    kpiKhauNhan = caculateAverageKpi(request.getNgayBaoCao(), listKhauNhan);
                    if (kpiKhauNhan != null && dto.getTyLeHLKhauNhan() >= kpiKhauNhan) {
                        dto.setDanhGiaTLHLKhauNhan(true);
                    } else dto.setDanhGiaTLHLKhauNhan(false);
                }
                response.add(dto);
            }
            sortBaoCaoTongHopTable(request.getVung(), request.getMaChiNhanh(), request.getMaBuuCuc(), request.getSort(), request.getSortBy(), response);
            if (response.size() > 0) {
                for (TyLeHaiLongDto x : response) {
                    tongDG += x.getTongDG();
                    tongDGHaiLong += x.getTongDGHaiLong();

                    tongDGKhauGiao += x.getTongDGKhauGiao();
                    tongDGKhauGiaoHaiLong += x.getTongDGKhauGiaoHaiLong();

                    tongDGKhauNhan += x.getTongDGKhauNhan();
                    tongDGKhauNhanHaiLong += x.getTongDGKhauNhanHaiLong();
                }
                totalRow = new TyLeHaiLongDto(null, "TONG", null, null, tongDG, tongDGHaiLong, tongDGKhauNhan, tongDGKhauNhanHaiLong, tongDGKhauGiao, tongDGKhauGiaoHaiLong);

                if (totalRow.getTyLeHL() >= kpiTong) {
                    totalRow.setDanhGiaTLHLTong(true);
                } else totalRow.setDanhGiaTLHLTong(false);

                if (totalRow.getTyLeHLKhauGiao() >= kpiKhauGiao) {
                    totalRow.setDanhGiaTLHLKhauGiao(true);
                } else totalRow.setDanhGiaTLHLKhauGiao(false);

                if (totalRow.getTyLeHLKhauNhan() >= kpiKhauNhan) {
                    totalRow.setDanhGiaTLHLKhauNhan(true);
                } else totalRow.setDanhGiaTLHLKhauNhan(false);

            }

            int pageNumber = request.getPage();
            int pageSize = request.getSize();
            if (pageNumber > 0) pageNumber--;
            int start = (int) Math.min(pageNumber * pageSize, response.size());
            int end = (int) Math.min((pageNumber + 1) * pageSize, response.size());

            List<TyLeHaiLongDto> listTotal = new ArrayList<>(response); // this list to get true size of list when pagination
            List<TyLeHaiLongDto> sublist = response.subList(start, end);
            if (sublist.size() > 0) {
                sublist.add(totalRow);
            }
            resultPage = new PageWithTotalNumber<TyLeHaiLongDto>(sublist, PageRequest.of(pageNumber, pageSize), listTotal.size());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return resultPage;
    }

    @Override
    public void exportExcelBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request,
                                         HttpServletResponse response) throws IOException, NoSuchFieldException, IllegalAccessException {
        LocalDate ngayBaoCao = request.getNgayBaoCao();
        List<String> vung = request.getVung();
        List<String> maChiNhanh = request.getMaChiNhanh();
        List<String> maBuuCuc = request.getMaBuuCuc();
        String type = request.getType();
        String sort = request.getSort();
        String sortBy = request.getSortBy();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCtylehailong.xlsx";
        response.setHeader(headerKey, headerValue);

        Map<String, String> mapInfo = new HashMap<>();

        mapInfo.put("stt", "STT");
        mapInfo.put("vung", "Vùng");
        mapInfo.put("maChiNhanh", "Chi nhánh");
        if (!maChiNhanh.isEmpty()) {
            mapInfo.put("maBuuCuc", "Bưu cục");
            if (!maBuuCuc.isEmpty()) {
                mapInfo.put("maTuyen", "Tuyến");
            }
        }
        mapInfo.put("tongDGHaiLong", "Hài lòng [1]");
        mapInfo.put("tongDGKhongHaiLong", "Không hài lòng [2]");
        mapInfo.put("tongDG", "Tổng lượt đánh giá [3]=[1]+[2]");
        mapInfo.put("tyLeHL", "Tỷ lệ hài lòng [4]=[1]/[3]");
        mapInfo.put("danhGiaTLHLTong", "Đánh giá");

        mapInfo.put("tongDGKhauGiaoHaiLong", "Hài lòng [6]");
        mapInfo.put("tongDGKhauGiaoKhongHaiLong", "Không hài lòng [7]");
        mapInfo.put("tongDGKhauGiao", "Tổng lượt đánh giá [8]=[6]+[7]");
        mapInfo.put("tyLeHLKhauGiao", "Tỷ lệ hài lòng [9]=[6]/[8]");
        mapInfo.put("danhGiaTLHLKhauGiao", "Đánh giá");

        mapInfo.put("tongDGKhauNhanHaiLong", "Hài lòng [11]");
        mapInfo.put("tongDGKhauNhanKhongHaiLong", "Không hài lòng [12]");
        mapInfo.put("tongDGKhauNhan", "Tổng lượt đánh giá [13]=[11]+[12]");
        mapInfo.put("tyLeHLKhauNhan", "Tỷ lệ hài lòng [14]=[11]/[13]");
        mapInfo.put("danhGiaTLHLKhauNhan", "Đánh giá");

        List<String> firstHeaders = null;
        List<String> secondHeaders = null;

        if (!maChiNhanh.isEmpty()) {
            if (!maBuuCuc.isEmpty()) {
                firstHeaders = Arrays.asList(
                        "STT",
                        "Vùng",
                        "Chi nhánh",
                        "Bưu cục",
                        "Tuyến",

                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",

                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",

                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN"
                );

                secondHeaders = Arrays.asList(
                        "stt",
                        "vung",
                        "maChiNhanh",
                        "maBuuCuc",
                        "maTuyen",

                        "tongDGHaiLong",
                        "tongDGKhongHaiLong",
                        "tongDG",
                        "tyLeHL",
                        "danhGiaTLHLTong",

                        "tongDGKhauGiaoHaiLong",
                        "tongDGKhauGiaoKhongHaiLong",
                        "tongDGKhauGiao",
                        "tyLeHLKhauGiao",
                        "danhGiaTLHLKhauGiao",

                        "tongDGKhauNhanHaiLong",
                        "tongDGKhauNhanKhongHaiLong",
                        "tongDGKhauNhan",
                        "tyLeHLKhauNhan",
                        "danhGiaTLHLKhauNhan"
                );
            } else {
                firstHeaders = Arrays.asList(
                        "STT",
                        "Vùng",
                        "Chi nhánh",
                        "Bưu cục",

                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",
                        "TỶ LỆ HÀI LÒNG TỔNG",

                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                        "TỶ LỆ HÀI LÒNG KHÂU GIAO",

                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                        "TỶ LỆ HÀI LÒNG KHÂU NHẬN"
                );

                secondHeaders = Arrays.asList(
                        "stt",
                        "vung",
                        "maChiNhanh",
                        "maBuuCuc",

                        "tongDGHaiLong",
                        "tongDGKhongHaiLong",
                        "tongDG",
                        "tyLeHL",
                        "danhGiaTLHLTong",

                        "tongDGKhauGiaoHaiLong",
                        "tongDGKhauGiaoKhongHaiLong",
                        "tongDGKhauGiao",
                        "tyLeHLKhauGiao",
                        "danhGiaTLHLKhauGiao",

                        "tongDGKhauNhanHaiLong",
                        "tongDGKhauNhanKhongHaiLong",
                        "tongDGKhauNhan",
                        "tyLeHLKhauNhan",
                        "danhGiaTLHLKhauNhan"
                );
            }
        } else {
            firstHeaders = Arrays.asList(
                    "STT",
                    "Vùng",
                    "Chi nhánh",

                    "TỶ LỆ HÀI LÒNG TỔNG",
                    "TỶ LỆ HÀI LÒNG TỔNG",
                    "TỶ LỆ HÀI LÒNG TỔNG",
                    "TỶ LỆ HÀI LÒNG TỔNG",
                    "TỶ LỆ HÀI LÒNG TỔNG",

                    "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                    "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                    "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                    "TỶ LỆ HÀI LÒNG KHÂU GIAO",
                    "TỶ LỆ HÀI LÒNG KHÂU GIAO",

                    "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                    "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                    "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                    "TỶ LỆ HÀI LÒNG KHÂU NHẬN",
                    "TỶ LỆ HÀI LÒNG KHÂU NHẬN"
            );

            secondHeaders = Arrays.asList(
                    "stt",
                    "vung",
                    "maChiNhanh",

                    "tongDGHaiLong",
                    "tongDGKhongHaiLong",
                    "tongDG",
                    "tyLeHL",
                    "danhGiaTLHLTong",

                    "tongDGKhauGiaoHaiLong",
                    "tongDGKhauGiaoKhongHaiLong",
                    "tongDGKhauGiao",
                    "tyLeHLKhauGiao",
                    "danhGiaTLHLKhauGiao",

                    "tongDGKhauNhanHaiLong",
                    "tongDGKhauNhanKhongHaiLong",
                    "tongDGKhauNhan",
                    "tyLeHLKhauNhan",
                    "danhGiaTLHLKhauNhan"
            );
        }
        ExportExcelTyLeHaiLongTemplate template = new ExportExcelTyLeHaiLongTemplate(
                getDataTongHopToExport(ngayBaoCao, vung, maChiNhanh, maBuuCuc, type, sort, sortBy),
                mapInfo,
                firstHeaders,
                secondHeaders,
                "BaoCaoTyLeHaiLong"
        );
        try {
            template.exportDataToExcel(response);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public void exportExcelBaoCaoChiTiet(GetBaoCaoTyLeHaiLongRqExcel request, HttpServletResponse response) {
//        LocalDate ngayBaoCao = request.getNgayBaoCao();
        LocalDate ngayBatDau = request.getStartDate();
        LocalDate ngayKetThuc = request.getEndDate();
        List<String> vung = request.getVung();
        List<String> maChiNhanh = request.getMaChiNhanh();
        List<String> maBuuCuc = request.getMaBuuCuc();
        String type = request.getType();
        String sort = request.getSort();
        String sortBy = request.getSortBy();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCtylehailongchitet.xlsx";
        response.setHeader(headerKey, headerValue);

        Map<String, String> mapInfo = new HashMap<>();

        mapInfo.put("stt", "STT");
        mapInfo.put("ngayKhaoSat", "Ngày nhắn tin khảo sát");
        mapInfo.put("ngayKHPhanHoi", "Ngày KH phản hồi");
        mapInfo.put("kenhGuiKS", "Kênh gửi khảo sát");
        mapInfo.put("kenhPhanHoi", "Kênh phản hồi");
        mapInfo.put("maBillDanhGia", "Mã bill đánh giá");
        mapInfo.put("maTrangThai", "Mã trạng thái");
        mapInfo.put("thoiGianCapNhatTT", "Thời gian cập nhật trạng thái");
        mapInfo.put("lyDoPhatKTC", "Lý do đơn phát không thành công");
        mapInfo.put("maKH", "Mã khách hàng");
        mapInfo.put("maDoiTac", "Mã đối tác");
        mapInfo.put("nhomKH", "Nhóm KH");
        mapInfo.put("doiTuongKH", "Đối tượng KH");
        mapInfo.put("doiTuongKHDanhGia", "Đối tượng KH đánh giá");
        mapInfo.put("kqDanhGiaMDHL", "Kết quả đánh giá mức độ hài lòng của KH");
        mapInfo.put("kqKSDonGiaoThatBai", "Kết quả khảo sát đơn giao thất bại");
        mapInfo.put("nnKhongHL", "Nguyên nhân không hài lòng");
        mapInfo.put("nnKhongHLSauXacMinh", "Nguyên nhân không hài lòng sau xác minh");
        mapInfo.put("phanLoaiDG", "Phân loại đánh giá");
        mapInfo.put("maBuuTa", "Mã bưu tá");
        mapInfo.put("tenBuuTa", "Tên bưu tá");
        mapInfo.put("sdtBuuTa", "Số điện thoại bưu tá");
        mapInfo.put("chucDanh", "Chức danh");
        mapInfo.put("vung", "Vùng");
        mapInfo.put("chiNhanh", "Chi nhánh");
        mapInfo.put("buuCuc", "Bưu cục");
        mapInfo.put("khauDG", "Khâu đánh giá");
        mapInfo.put("yeuCauDvi", "Yêu cầu đơn vị");
        mapInfo.put("lyDoDviYCXacMinh", "Lý do đơn vị yêu cầu xác minh");
        mapInfo.put("ndXacMinhVoiKH", "Nội dung xác minh với khách hàng");
        mapInfo.put("trangThaiXuLy", "Trạng thái xử lý");
        mapInfo.put("nguoiXacMinh", "Người xác minh");
        mapInfo.put("maKhieuNaiPhatSinh", "Mã khiếu nại phát sinh");
        mapInfo.put("ngayXacMinh", "Ngày xác minh");
        mapInfo.put("chiTietNguyenNhanKHL", "Chi tiết nguyên nhân không hài lòng");
        mapInfo.put("quyHoachLoiTheoThe", "Quy hoạch lỗi theo thẻ");
        mapInfo.put("kenhBaoCao", "Kênh báo cáo");
        mapInfo.put("soLoi","Số lỗi");
        mapInfo.put("fmTGNhanTC", "TG nhận hàng thành công");
        mapInfo.put("fmTGLaiXeXacNhanBG", "Thời gian lái  xe xác nhận bàn giao");
        mapInfo.put("fmTongKhau", "Tổng khâu");
        mapInfo.put("fmChenhLechKPI", "Chênh lệch KPI");
        mapInfo.put("fmDanhGia", "Đánh giá");
        mapInfo.put("mmTGLaiXeXacNhanBG", "TG lái xe xác nhận bàn giao");
        mapInfo.put("mmTGBCPXacNhanBG", "TG BCP xác nhận bàn giao");
        mapInfo.put("mmTongKhau", "Tổng khâu");
        mapInfo.put("mmChenhLechKPI", "Chênh lệch KPI");
        mapInfo.put("mmDanhGia", "Đánh giá");
        mapInfo.put("lmTGBCPXacNhanBG", "TG BCP xác nhận bàn giao");
        mapInfo.put("lmTGPhatLanDau", "TG phát lần đầu");
        mapInfo.put("lmTongKhau", "Tổng khâu");
        mapInfo.put("lmChenhLechKPI", "Chênh lệch KPI");
        mapInfo.put("lmDanhGia", "Đánh giá");
        mapInfo.put("tgTTThucTe", "TG TT thực tế");
        mapInfo.put("tgChiTieuPhatLanDau", "TG chỉ tiêu phát lần đầu");
        mapInfo.put("chenhLechKPI", "Chênh lệch KPI");
        mapInfo.put("danhGia", "Đánh giá");

        List<String> firstHeaders = null;
        List<String> secondHeaders = null;

        firstHeaders = Arrays.asList(
                "STT",
                "Ngày nhắn tin khảo sát",
                "Ngày KH phản hồi",
                "Kênh gửi khảo sát",
                "Kênh phản hồi",
                "Mã bill đánh giá",
                "Mã trạng thái",
                "Thời gian cập nhật trạng thái",
                "Lý do đơn phát không thành công",
                "Mã khách hàng",
                "Mã đối tác",
                "Nhóm KH",
                "Đối tượng KH",
                "Đối tượng KH đánh giá",
                "Kết quả đánh giá mức độ hài lòng của KH",
                "Kết quả khảo sát đơn giao thất bại",
                "Nguyên nhân không hài lòng",
                "Nguyên nhân không hài lòng sau xác minh",
                "Phân loại đánh giá",
                "Mã bưu tá",
                "Tên bưu tá",
                "Số điện thoại bưu tá",
                "Chức danh",
                "Vùng",
                "Chi nhánh",
                "Bưu cục",
                "Khâu đánh giá",
                "Yêu cầu đơn vị",
                "Lý do đơn vị yêu cầu xác minh",
                "Nội dung xác minh với khách hàng",
                "Trạng thái xử lý",
                "Người xác minh",
                "Mã khiếu nại phát sinh",
                "Ngày xác minh",
                "Chi tiết nguyên nhân không hài lòng",
                "Quy hoạch lỗi theo thẻ",
                "Kênh báo cáo",
                "Số lỗi",

                "Khâu First mile",
                "Khâu First mile",
                "Khâu First mile",
                "Khâu First mile",
                "Khâu First mile",

                "Khâu Middle mile",
                "Khâu Middle mile",
                "Khâu Middle mile",
                "Khâu Middle mile",
                "Khâu Middle mile",

                "Khâu Last mile",
                "Khâu Last mile",
                "Khâu Last mile",
                "Khâu Last mile",
                "Khâu Last mile",

                "Toàn trình bưu gửi",
                "Toàn trình bưu gửi",
                "Toàn trình bưu gửi",
                "Toàn trình bưu gửi"
        );

        secondHeaders = Arrays.asList(
                "stt",
                "ngayKhaoSat",
                "ngayKHPhanHoi",
                "kenhGuiKS",
                "kenhPhanHoi",
                "maBillDanhGia",
                "maTrangThai",
                "thoiGianCapNhatTT",
                "lyDoPhatKTC",
                "maKH",
                "maDoiTac",
                "nhomKH",
                "doiTuongKH",
                "doiTuongKHDanhGia",
                "kqDanhGiaMDHL",
                "kqKSDonGiaoThatBai",
                "nnKhongHL",
                "nnKhongHLSauXacMinh",
                "phanLoaiDG",
                "maBuuTa",
                "tenBuuTa",
                "sdtBuuTa",
                "chucDanh",
                "vung",
                "chiNhanh",
                "buuCuc",
                "khauDG",
                "yeuCauDvi",
                "lyDoDviYCXacMinh",
                "ndXacMinhVoiKH",
                "trangThaiXuLy",
                "nguoiXacMinh",
                "maKhieuNaiPhatSinh",
                "ngayXacMinh",
                "chiTietNguyenNhanKHL",
                "quyHoachLoiTheoThe",
                "kenhBaoCao",
                "soLoi",

                "fmTGNhanTC",
                "fmTGLaiXeXacNhanBG",
                "fmTongKhau",
                "fmChenhLechKPI",
                "fmDanhGia",

                "mmTGLaiXeXacNhanBG",
                "mmTGBCPXacNhanBG",
                "mmTongKhau",
                "mmChenhLechKPI",
                "mmDanhGia",

                "lmTGBCPXacNhanBG",
                "lmTGPhatLanDau",
                "lmTongKhau",
                "lmChenhLechKPI",
                "lmDanhGia",

                "tgTTThucTe",
                "tgChiTieuPhatLanDau",
                "chenhLechKPI",
                "danhGia"
        );

        List<Map<String, Object>> resultList = getDataChiTietToExport(ngayBatDau, ngayKetThuc, vung, maChiNhanh, maBuuCuc, type, sort, sortBy);
        ExportExcelTyLeHaiLongChiTietTemplate template = new ExportExcelTyLeHaiLongChiTietTemplate(
                resultList,
                mapInfo,
                firstHeaders,
                secondHeaders,
                "BaoCaoTyLeHaiLongChiTiet"
        );
        try {
            template.exportDataToExcel(response);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<TopTenChiNhanhDto> getTopTenChiNhanhDashboard(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type) throws IllegalAccessException {
        TopTenChiNhanhDto dto = new TopTenChiNhanhDto();
        List<TopTenChiNhanhDto> resultList = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;

        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> sqlCondition = getChiNhanhSqlCondition("MA_CHINHANH", ngayBaoCao, vung, maChiNhanh, maBuuCuc,
                listChiNhanhSSO, listBuuCucSSO);
        String sqlEntity = sqlCondition.get("sqlEntity");

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                "GROUP BY " + sqlEntity + "";

        Double kpiTong = getKpiTong(ngayBaoCao, vung, maChiNhanh);
        log.error("-----------------------------SQL{}: " + sql.toString());
        try {
            List<TopTenChiNhanhDto> dtoList = new ArrayList<>();
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());

            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new TopTenChiNhanhDto(
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("TONG_DG"),
                        rs.getLong("TONG_DG_HAILONG"),
                        ngayBaoCao
                );
                dto.setKpi(kpiTong);
                dtoList.add(dto);
            }

            /** type = '0' - thấp nhất, type = '1' cao nhất **/
            switch (type) {
                case "0":
                    dtoList.sort(Comparator.comparing(TopTenChiNhanhDto::getTyLeHL, Comparator.nullsLast(Comparator.naturalOrder())));
                    resultList = dtoList.subList(0, Math.min(10, dtoList.size()));
                    break;
                case "1":
                    dtoList.sort(Comparator.comparing(TopTenChiNhanhDto::getTyLeHL, Comparator.nullsLast(Comparator.reverseOrder())));
                    resultList = dtoList.subList(0, Math.min(10, dtoList.size()));
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return resultList;
    }

    @Override
    public TyLeHaiLongWithKPIDashboardDto getTyLeHaiLongWithKPILuyKe(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IllegalAccessException {
        TyLeHaiLongWithKPIDashboardDto response = new TyLeHaiLongWithKPIDashboardDto();
        List<TyLeHaiLongWithKPIDashboardDto> finalResult = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return response;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> sqlConditionLK = getSqlCondition("MA_CHINHANH", ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);

        String sqlLuyKe = "SELECT SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_KENHBT) AS TONG_DG_KENHBT, SUM(TONG_DG_KENHBT_HAILONG) AS TONG_DG_KENHBT_HAILONG, " +
                "SUM(TONG_DG_KENHBC) AS TONG_DG_KENHBC, SUM(TONG_DG_KENHBC_HAILONG) AS TONG_DG_KENHBC_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlConditionLK.get("sqlCondition");

        Double kpiTong = getKpiTong(ngayBaoCao, vung, maChiNhanh);

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlLuyKe);
            System.out.println("-----------------------------SQL Luy ke{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                response = new TyLeHaiLongWithKPIDashboardDto(
                        ngayBaoCao,
                        "Lũy Kế",
                        rs.getLong("TONG_DG"),
                        rs.getLong("TONG_DG_HAILONG"),
                        rs.getLong("TONG_DG_KENHBT"),
                        rs.getLong("TONG_DG_KENHBT_HAILONG"),
                        rs.getLong("TONG_DG_KENHBC"),
                        rs.getLong("TONG_DG_KENHBC_HAILONG"));
                response.setKpi(kpiTong);
                if (response.getTyLeHL() > 0 && response.getKpi() > 0 && response.getTyLeHL() >= response.getKpi()) {
                    response.setDanhGiaTLHL(true);
                }
            }
            List<Double> tyleCungKyList = getTyLeHaiLongCungKy(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
            if (!tyleCungKyList.isEmpty() && !Objects.isNull(tyleCungKyList)) {
                if (!Objects.isNull(tyleCungKyList.get(0))) {
                    response.setTangTruongCungKy(response.getTyLeHL() - tyleCungKyList.get(0).doubleValue());
                } else if (null == tyleCungKyList.get(0)) {
                    response.setTangTruongCungKy(response.getTyLeHL());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public List<TyLeHaiLongWithKPIDashboardDto> getTyLeHaiLongWithKPITheoNgay(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IllegalAccessException, IOException {
        List<TyLeHaiLongWithKPIDashboardDto> theoNgayDto = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return theoNgayDto;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        Double kpiTong = getKpiTong(ngayBaoCao, vung, maChiNhanh);
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        theoNgayDto = getTLHLWithKPITheoNgay(ngayDauThang, ngayBaoCao, vung, maChiNhanh, maBuuCuc);

        Map<String, Double> tyLeCungKyTheoNgayList = getTyLeHaiLongCungKyTheoNgay(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
        for (int i = 0; i < theoNgayDto.size(); i++) {
            TyLeHaiLongWithKPIDashboardDto check = theoNgayDto.get(i);
            if (tyLeCungKyTheoNgayList.containsKey(check.getName())) {
                if (null == tyLeCungKyTheoNgayList.get(check.getName())) {
                    check.setTangTruongCungKy(check.getTyLeHL());
                } else {
                    check.setTangTruongCungKy(check.getTyLeHL() - tyLeCungKyTheoNgayList.get(check.getName()));
                }
            } else {
                check.setTangTruongCungKy(check.getTyLeHL());
            }
        }

        List<String> existedNameList = new ArrayList<>();
        for (int i = 0; i < theoNgayDto.size(); i++) {
            existedNameList.add(theoNgayDto.get(i).getName());
        }
        for (int i = 1; i <= ngayBaoCao.getDayOfMonth(); i++) {
            String name = "N" + String.format("%02d", i);
            if (!existedNameList.contains(name)) {
                theoNgayDto.add(new TyLeHaiLongWithKPIDashboardDto("N" + String.format("%02d", i), 0L, 0L, 0L, 0L, 0L, 0L, kpiTong));
                TyLeHaiLongWithKPIDashboardDto check = theoNgayDto.get(theoNgayDto.size() -1);
                if (tyLeCungKyTheoNgayList.containsKey(check.getName())) {
                    if (null == tyLeCungKyTheoNgayList.get(check.getName())) {
                        check.setTangTruongCungKy(check.getTyLeHL());
                    } else {
                        check.setTangTruongCungKy(check.getTyLeHL() - tyLeCungKyTheoNgayList.get(check.getName()));
                    }
                } else {
                    check.setTangTruongCungKy(check.getTyLeHL());
                }
            }
        }
        theoNgayDto.sort(Comparator.comparing(TyLeHaiLongWithKPIDashboardDto::getName, Comparator.nullsLast(Comparator.naturalOrder())));
        return theoNgayDto;
    }

    private List<TyLeHaiLongWithKPIDashboardDto> getTLHLWithKPITheoNgay(LocalDate ngayDauThang, LocalDate ngayBaoCao,
                                                                        List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IllegalAccessException {
        TyLeHaiLongWithKPIDashboardDto ngayDto = new TyLeHaiLongWithKPIDashboardDto();
        List<String> listChiNhanhSSO = null;
        List<String> listBuuCucSSO = null;
        List<TyLeHaiLongWithKPIDashboardDto> resultList = new ArrayList<>();
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return resultList;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        /** Biến connection để query TLHL theo ngày **/
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> sqlCondition = getSqlCondition(null, ngayBaoCao, vung, maChiNhanh, maBuuCuc,
                listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT NGAY_BAOCAO, SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_KENHBT) AS TONG_DG_KENHBT, SUM(TONG_DG_KENHBT_HAILONG) AS TONG_DG_KENHBT_HAILONG, " +
                "SUM(TONG_DG_KENHBC) AS TONG_DG_KENHBC, SUM(TONG_DG_KENHBC_HAILONG) AS TONG_DG_KENHBC_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                "GROUP BY NGAY_BAOCAO";

        Double kpiTong = getKpiTong(ngayBaoCao, vung, maChiNhanh);

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL Theo ngay{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                ngayDto = new TyLeHaiLongWithKPIDashboardDto(
                        ngayBaoCao,
                        "N" + String.valueOf(rs.getLong("NGAY_BAOCAO")).substring(6, 8),
                        rs.getLong("TONG_DG"),
                        rs.getLong("TONG_DG_HAILONG"),
                        rs.getLong("TONG_DG_KENHBT"),
                        rs.getLong("TONG_DG_KENHBT_HAILONG"),
                        rs.getLong("TONG_DG_KENHBC"),
                        rs.getLong("TONG_DG_KENHBC_HAILONG"));
                ngayDto.setKpi(kpiTong);
                if (ngayDto.getTyLeHL() > 0 && ngayDto.getKpi() > 0 && ngayDto.getTyLeHL() >= ngayDto.getKpi()) {
                    ngayDto.setDanhGiaTLHL(true);
                }
                resultList.add(ngayDto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return resultList;
    }

    @Override
    public ChiNhanhDatKPIDto getChiNhanhDatKPILuyKe(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException, IllegalAccessException {
        ChiNhanhDatKPIDto luyKeDto = new ChiNhanhDatKPIDto();
        ChiNhanhDatKPIDto luyKeCungKyDto = new ChiNhanhDatKPIDto();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        luyKeDto = getCNDatKPILuyKe(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
        luyKeCungKyDto = getCNDatKPILuyKeCungKy(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
        if (luyKeDto.getSoCNDatKPI() != null && luyKeCungKyDto.getSoCNDatKPI() != null) {
            luyKeDto.setSoSanhCungKy(luyKeDto.getSoCNDatKPI() - luyKeCungKyDto.getSoCNDatKPI());

        }
        return luyKeDto;
    }

    @Override
    public List<ChiNhanhDatKPIDto> getChiNhanhDatKPITheoNgay(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException, IllegalAccessException {
        List<ChiNhanhDatKPIDto> theoNgayDto = new ArrayList<>();
        List<ChiNhanhDatKPIDto> theoNgayCungKyDto = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;

        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return theoNgayDto;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        theoNgayDto = getCNDatKPITheoNgay(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
        theoNgayCungKyDto = getCNDatKPITheoNgayCungKy(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
        for (ChiNhanhDatKPIDto x : theoNgayDto) {
            for (ChiNhanhDatKPIDto y : theoNgayCungKyDto) {
                if (x.getName().equals(y.getName()) && x.getSoCNDatKPI() != null && y.getSoCNDatKPI() != null) {
                    x.setSoSanhCungKy(x.getSoCNDatKPI() - y.getSoCNDatKPI());
                }
            }
        }
        theoNgayDto.sort(Comparator.comparing(ChiNhanhDatKPIDto::getName, Comparator.nullsLast(Comparator.naturalOrder())));
        return theoNgayDto;
    }

    @Override
    public String getUpdatedTime(LocalDate ngayBaoCao) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long updateTime = null;
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        String sqlMax = "SELECT UPDATED_AT FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE NGAY_BAOCAO = " + convertedNgayBaoCao + " LIMIT 1";
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sqlMax);
            rs = stmt.executeQuery();
            if (rs.next()) {
                updateTime = rs.getLong("UPDATED_AT");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getDetailByPaging in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        if (Objects.isNull(updateTime) || updateTime == 0) {
            return null;
        } else {
            return DateTimeUltis.convertLongTimestampToString(updateTime);
        }
    }

    private List<Map<String, Object>> convertListToListHashMap(List<T> dataList) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!dataList.isEmpty()) {
            int i = 0;
            for (T data : dataList) {
                TyLeHaiLongExcelDto x = (TyLeHaiLongExcelDto) data;
                totalRows.add(x.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    private List<Map<String, Object>> convertListTLHLChiTietHashMap(List<T> dataList) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!dataList.isEmpty()) {
            int i = 0;
            for (T data : dataList) {
                TyLeHaiLongChiTietExcelDto x = (TyLeHaiLongChiTietExcelDto) data;
                totalRows.add(x.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    private List<Map<String, Object>> getDataTongHopToExport(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type,
                                                             String sort, String sortBy) throws IOException, NoSuchFieldException, IllegalAccessException {
        TyLeHaiLongExcelDto dto = new TyLeHaiLongExcelDto();
        List<TyLeHaiLongExcelDto> result = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        /** List param để tạo dòng Tổng trên bảng bảo cáo **/
        TyLeHaiLongExcelDto totalRow = null;
        Long tongDG = 0L;
        Long tongDGHaiLong = 0L;
        Long tongDGKhauNhan = 0L;
        Long tongDGKhauNhanHaiLong = 0L;
        Long tongDGKhauGiao = 0L;
        Long tongDGKhauGiaoHaiLong = 0L;

        /** KPI **/
        Double kpiTong = (double) 0;
        Double kpiKhauNhan = (double) 0;
        Double kpiKhauGiao = (double) 0;

        String sqlEntity = "VUNG, MA_CHINHANH";
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            sqlCondition += "AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }

            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s)AND TUYEN_BUUTA <> 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_KHAUNHAN) AS TONG_DG_KHAUNHAN, SUM(TONG_DG_KHAUNHAN_HAILONG) AS TONG_DG_KHAUNHAN_HAILONG, " +
                "SUM(TONG_DG_KHAUGIAO) AS TONG_DG_KHAUGIAO, SUM(TONG_DG_KHAUGIAO_HAILONG) AS TONG_DG_KHAUGIAO_HAILONG, " +
                "SUM(TONG_DG_KENHBT) AS TONG_DG_KENHBT, SUM(TONG_DG_KENHBT_HAILONG) AS TONG_DG_KENHBT_HAILONG, " +
                "SUM(TONG_DG_KENHBC) AS TONG_DG_KENHBC, SUM(TONG_DG_KENHBC_HAILONG) AS TONG_DG_KENHBC_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition + " AND TONG_DANHGIA <> 0 " +
                "GROUP BY " + sqlEntity;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                String danhGiaTLHLTong = null;
                String danhGiaTLHLKhauGiao = null;
                String danhGiaTLHLKhauNhan = null;
                List<KpiMucDoHaiLong> list = getKpiRecords(ngayBaoCao, vung, maChiNhanh);
                if (list != null && !list.isEmpty()) {
                    List<KpiMucDoHaiLong> listTong = list.stream().filter(e -> "Tổng".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKhauGiao = list.stream().filter(e -> "Khâu giao".equals(e.getTieuChi())).collect(Collectors.toList());
                    List<KpiMucDoHaiLong> listKhauNhan = list.stream().filter(e -> "Khâu nhận".equals(e.getTieuChi())).collect(Collectors.toList());
                    kpiTong = caculateAverageKpi(ngayBaoCao, listTong);
                    if (kpiTong != null && (double) rs.getLong("TONG_DG_HAILONG") / rs.getLong("TONG_DG") * 100 >= kpiTong) {
                        danhGiaTLHLTong = MucDoHaiLongConstants.KPI_PASS;
                    } else danhGiaTLHLTong = MucDoHaiLongConstants.KPI_FAIL;

                    kpiKhauGiao = caculateAverageKpi(ngayBaoCao, listKhauGiao);
                    if (kpiKhauGiao != null && (double) rs.getLong("TONG_DG_KHAUGIAO_HAILONG") / rs.getLong("TONG_DG_KHAUGIAO") * 100 >= kpiKhauGiao) {
                        danhGiaTLHLKhauGiao = MucDoHaiLongConstants.KPI_PASS;
                    } else danhGiaTLHLKhauGiao = MucDoHaiLongConstants.KPI_FAIL;

                    kpiKhauNhan = caculateAverageKpi(ngayBaoCao, listKhauNhan);
                    if (kpiKhauNhan != null && (double) rs.getLong("TONG_DG_KHAUNHAN_HAILONG") / rs.getLong("TONG_DG_KHAUNHAN") * 100 >= kpiKhauNhan) {
                        danhGiaTLHLKhauNhan = MucDoHaiLongConstants.KPI_PASS;
                    } else danhGiaTLHLKhauNhan = MucDoHaiLongConstants.KPI_FAIL;
                }

                dto = new TyLeHaiLongExcelDto(
                        sqlEntity.contains("VUNG") ? rs.getString("VUNG") : null,
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : "",
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("TONG_DG"),
                        rs.getLong("TONG_DG_HAILONG"),
                        danhGiaTLHLTong,
                        rs.getLong("TONG_DG_KHAUNHAN"),
                        rs.getLong("TONG_DG_KHAUNHAN_HAILONG"),
                        danhGiaTLHLKhauNhan,
                        rs.getLong("TONG_DG_KHAUGIAO"),
                        rs.getLong("TONG_DG_KHAUGIAO_HAILONG"),
                        danhGiaTLHLKhauGiao);
                result.add(dto);
            }
            sortBaoCaoTongHopTableExcel(vung, maChiNhanh, maBuuCuc, sort, sortBy, result);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        if (!result.isEmpty()) {
            for (TyLeHaiLongExcelDto x : result) {
                tongDG += x.getTongDG();
                tongDGHaiLong += x.getTongDGHaiLong();

                tongDGKhauGiao += x.getTongDGKhauGiao();
                tongDGKhauGiaoHaiLong += x.getTongDGKhauGiaoHaiLong();

                tongDGKhauNhan += x.getTongDGKhauNhan();
                tongDGKhauNhanHaiLong += x.getTongDGKhauNhanHaiLong();
            }

            String danhGiaTLHLTong = null;
            String danhGiaTLHLKhauGiao = null;
            String danhGiaTLHLKhauNhan = null;
            if (kpiTong != null && (double) tongDGHaiLong / tongDG * 100 >= kpiTong) {
                danhGiaTLHLTong = MucDoHaiLongConstants.KPI_PASS;
            } else danhGiaTLHLTong = MucDoHaiLongConstants.KPI_FAIL;

            if (kpiKhauGiao != null && (double) tongDGKhauGiaoHaiLong / tongDGKhauGiao * 100 >= kpiKhauGiao) {
                danhGiaTLHLKhauGiao = MucDoHaiLongConstants.KPI_PASS;
            } else danhGiaTLHLKhauGiao = MucDoHaiLongConstants.KPI_FAIL;

            if (kpiKhauNhan != null && (double) tongDGKhauNhanHaiLong / tongDGKhauNhan * 100 >= kpiKhauNhan) {
                danhGiaTLHLKhauNhan = MucDoHaiLongConstants.KPI_PASS;
            } else danhGiaTLHLKhauNhan = MucDoHaiLongConstants.KPI_FAIL;

            totalRow = new TyLeHaiLongExcelDto(null, "TONG", null, null,
                    tongDG, tongDGHaiLong, danhGiaTLHLTong,
                    tongDGKhauNhan, tongDGKhauNhanHaiLong, danhGiaTLHLKhauNhan,
                    tongDGKhauGiao, tongDGKhauGiaoHaiLong, danhGiaTLHLKhauGiao);

            result.add(totalRow);
            return convertListToListHashMap((List<T>) result);
        }
        return null;
    }

    private List<Map<String, Object>> getDataChiTietToExport(
            LocalDate ngayBatDau,
            LocalDate ngayKetThuc,
                                                             List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type,
                                                             String sort, String sortBy) {
        TyLeHaiLongChiTietExcelDto dto = new TyLeHaiLongChiTietExcelDto();
        List<TyLeHaiLongChiTietExcelDto> result = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
//        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
//        switch (type) {
//            case "0":
//                LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayBatDau.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                int convertedNgayChonKetThuc = Integer.parseInt(ngayKetThuc.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                log.info("convertedNgayDauThang ======================== "+ convertedNgayDauThang);
                log.info("convertedNgayChonKetThuc ======================== "+ convertedNgayChonKetThuc);

//                sqlCondition += "NGAY_KH_PHANHOI >= " + convertedNgayDauThang + " AND NGAY_KH_PHANHOI <= " + convertedNgayBaoCao + " ";
                sqlCondition += "NGAY_KH_PHANHOI >= " + convertedNgayDauThang + " AND NGAY_KH_PHANHOI <= " + convertedNgayChonKetThuc + " ";
//                break;
//            case "1":
//                sqlCondition += "NGAY_KH_PHANHOI = " + convertedNgayBaoCao + " ";
//                break;
//        }

        if (vung != null && !vung.isEmpty()) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') ", listVung);
        }
        if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') ", listChiNhanh);
        }
        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_CHINHANH in (%s) ", listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }
        if (maBuuCuc != null && !maBuuCuc.isEmpty()) {
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_BUUCUC IN ('%s')", listBuuCuc);
        }
        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_BUUCUC in (%s) ", listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }

        String sql = "SELECT NGAY_KHAOSAT, NGAY_KH_PHANHOI_UNIXTIME, KENH_KHAO_SAT, KENH_PHAN_HOI, MAVANDON, MA_TRANGTHAI, TG_CAPNHAT_TT, LY_DO, MA_KH, MADOITAC, " +
                "NHOM_KH, DOITUONG_KH, DOITUONG_KH_DANHGIA, RATE, KETQUA_DANHGIA, NGUYEN_NHAN, NGUYEN_NHAN_SAU_XM, PHANLOAI_DANHGIA, MA_BUUTA, TEN_BUUTA, SDT_BUUTA, " +
                "CHUC_DANH_BUUTA, VUNG, MA_CHINHANH, MA_BUUCUC, KHAU_DANHGIA, YEU_CAU_DON_VI, LY_DO_DON_VI, NOI_DUNG_XAC_MINH, TRANG_THAI_XU_LY, NGUOI_XACMINH, MA_KHIEUNAI, " +
                "NGAY_XAC_MINH, CHITIET_NGUYENNHAN, THE_PHAT, KENH_BAOCAO,  SO_LOI, " +
                "TG_NTC_FM, TG_LX_XNBG_FM, TONG_KHAU_FM, CHENH_LECH_KPI_FM, DANH_GIA_FM, " +
                "TG_LX_XNBG_MM, TG_BC_PHAT_XNBG_MM, TONG_KHAU_MM, CHENH_LECH_KPI_MM, DANH_GIA_MM, " +
                "TG_BC_PHAT_XNBG_LM, TG_PHAT_LAN_DAU_LM, TONG_KHAU_LM, CHENH_LECH_KPI_LM, DANH_GIA_LM, " +
                "TG_TT_THUCTE, TG_CHITIEU_PHAT_LANDAU, CHENH_LECH_KPI_TT, DANH_GIA_TT " +
                "FROM MUCDO_HAILONG_KHACHHANG_CHITIET WHERE " + sqlCondition;

        log.info("SQL ======================================= "+ sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new TyLeHaiLongChiTietExcelDto(
                        convertLongTimestampToMMDDYYYY(rs.getLong("NGAY_KHAOSAT")),
                        convertLongTimestampToMMDDYYYY(rs.getLong("NGAY_KH_PHANHOI_UNIXTIME")),
                        rs.getString("KENH_KHAO_SAT"),
                        rs.getString("KENH_PHAN_HOI"),
                        rs.getString("MAVANDON"),
                        rs.getString("MA_TRANGTHAI"),
                        rs.getLong("TG_CAPNHAT_TT") == 0 ? null : convertLongTimestampToMMDDYYYY(rs.getLong("TG_CAPNHAT_TT")),
                        rs.getString("LY_DO"),
                        rs.getString("MA_KH"),
                        rs.getString("MADOITAC"),
                        rs.getString("NHOM_KH"),
                        rs.getString("DOITUONG_KH"),
                        rs.getString("DOITUONG_KH_DANHGIA"),
                        rs.getString("RATE"),
                        rs.getString("KETQUA_DANHGIA"),
                        rs.getString("NGUYEN_NHAN"),
                        rs.getString("NGUYEN_NHAN_SAU_XM"),
                        rs.getString("PHANLOAI_DANHGIA"),
                        rs.getString("MA_BUUTA"),
                        rs.getString("TEN_BUUTA"),
                        rs.getString("SDT_BUUTA"),
                        rs.getString("CHUC_DANH_BUUTA"),
                        rs.getString("VUNG"),
                        rs.getString("MA_CHINHANH"),
                        rs.getString("MA_BUUCUC"),
                        rs.getString("KHAU_DANHGIA"),
                        rs.getString("YEU_CAU_DON_VI"),
                        rs.getString("LY_DO_DON_VI"),
                        rs.getString("NOI_DUNG_XAC_MINH"),
                        rs.getString("TRANG_THAI_XU_LY"),
                        rs.getString("NGUOI_XACMINH"),
                        rs.getString("MA_KHIEUNAI"),
                        rs.getLong("NGAY_XAC_MINH") == 0 ? null : convertLongTimestampToMMDDYYYY(rs.getLong("NGAY_XAC_MINH")),
                        rs.getString("CHITIET_NGUYENNHAN"),
                        rs.getString("THE_PHAT"),
                        rs.getString("KENH_BAOCAO"),
                        rs.getInt("SO_LOI"),
                        Objects.isNull(rs.getString("TG_NTC_FM")) ? null : rs.getString("TG_NTC_FM"),
                        Objects.isNull(rs.getString("TG_LX_XNBG_FM")) ? null : rs.getString("TG_LX_XNBG_FM"),
                        rs.getDouble("TONG_KHAU_FM"),
                        rs.getDouble("CHENH_LECH_KPI_FM"),
                        rs.getString("DANH_GIA_FM"),
                        Objects.isNull(rs.getString("TG_LX_XNBG_MM")) ? null : rs.getString("TG_LX_XNBG_MM"),
                        Objects.isNull(rs.getString("TG_BC_PHAT_XNBG_MM")) ? null : rs.getString("TG_BC_PHAT_XNBG_MM"),
                        rs.getDouble("TONG_KHAU_MM"),
                        rs.getDouble("CHENH_LECH_KPI_MM"),
                        rs.getString("DANH_GIA_MM"),
                        Objects.isNull(rs.getString("TG_BC_PHAT_XNBG_LM")) ? null : rs.getString("TG_BC_PHAT_XNBG_LM"),
                        Objects.isNull(rs.getString("TG_PHAT_LAN_DAU_LM")) ? null : rs.getString("TG_PHAT_LAN_DAU_LM"),
                        rs.getDouble("TONG_KHAU_LM"),
                        rs.getDouble("CHENH_LECH_KPI_LM"),
                        rs.getString("DANH_GIA_LM"),
                        rs.getDouble("TG_TT_THUCTE"),
                        rs.getDouble("TG_CHITIEU_PHAT_LANDAU"),
                        rs.getDouble("CHENH_LECH_KPI_TT"),
                        rs.getString("DANH_GIA_TT")
                );
                result.add(dto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return convertListTLHLChiTietHashMap((List<T>) result);
    }

    private List<Double> getTyLeHaiLongCungKy(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        List<Double> response = new ArrayList<>();
        LocalDate ngayThangTruoc = MucDoHaiLongUtils.getSamePeriodPreviousMonth(ngayBaoCao);
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> sqlConditionMap = getSqlCondition(null, ngayThangTruoc, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, " +
                "SUM(TONG_DG_KHAUNHAN) AS TONG_DG_KHAUNHAN, SUM(TONG_DG_KHAUNHAN_HAILONG) AS TONG_DG_KHAUNHAN_HAILONG, " +
                "SUM(TONG_DG_KHAUGIAO) AS TONG_DG_KHAUGIAO, SUM(TONG_DG_KHAUGIAO_HAILONG) AS TONG_DG_KHAUGIAO_HAILONG, " +
                "SUM(TONG_DG_KENHBT) AS TONG_DG_KENHBT, SUM(TONG_DG_KENHBT_HAILONG) AS TONG_DG_KENHBT_HAILONG, " +
                "SUM(TONG_DG_KENHBC) AS TONG_DG_KENHBC, SUM(TONG_DG_KENHBC_HAILONG) AS TONG_DG_KENHBC_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlConditionMap.get("sqlCondition");

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL getTyLeHaiLongCungKy {}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                Long tongDG = rs.getLong("TONG_DG");
                Long tongDGHaiLong = rs.getLong("TONG_DG_HAILONG");
                double tyLeHLTong = 0;
                if (tongDGHaiLong > 0 && tongDG > 0) {
                    tyLeHLTong = (double) tongDGHaiLong / tongDG * 100;
                }
                response.add(tyLeHLTong);

                Long tongDGKhauGiao = rs.getLong("TONG_DG_KHAUGIAO");
                Long tongDGKhauGiaoHaiLong = rs.getLong("TONG_DG_KHAUGIAO_HAILONG");
                double tyLeHLKhauGiao = 0;
                if (tongDGKhauGiaoHaiLong > 0 && tongDGKhauGiao > 0) {
                    tyLeHLKhauGiao = (double) tongDGKhauGiaoHaiLong / tongDGKhauGiao * 100;
                }
                response.add(tyLeHLKhauGiao);

                Long tongDGKhauNhan = rs.getLong("TONG_DG_KHAUNHAN");
                Long tongDGKhauNhanHaiLong = rs.getLong("TONG_DG_KHAUNHAN_HAILONG");
                double tyLeHLKhauNhan = 0;
                if (tongDGKhauNhanHaiLong > 0 && tongDGKhauNhan > 0) {
                    tyLeHLKhauNhan = (double) tongDGKhauNhanHaiLong / tongDGKhauNhan * 100;
                }
                response.add(tyLeHLKhauNhan);

                Long tongDGKenhBT = rs.getLong("TONG_DG_KENHBT");
                Long tongDGKenhBTHaiLong = rs.getLong("TONG_DG_HAILONG");
                double tyLeHLKenhBT = 0;
                if (tongDGKenhBTHaiLong > 0 && tongDGKenhBT > 0) {
                    tyLeHLKenhBT = (double) tongDGKenhBTHaiLong / tongDGKenhBT * 100;
                }
                response.add(tyLeHLKenhBT);

                Long tongDGKenhBC = rs.getLong("TONG_DG_KENHBC");
                Long tongDGKenhBCHaiLong = rs.getLong("TONG_DG_KENHBC_HAILONG");
                double tyLeHLKenhBC = 0;
                if (tongDGKenhBCHaiLong > 0 && tongDGKenhBC > 0) {
                    tyLeHLKenhBC = (double) tongDGKenhBCHaiLong / tongDGKenhBC * 100;
                }
                response.add(tyLeHLKenhBC);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private Map<String, Double> getTyLeHaiLongCungKyTheoNgay(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Map<String, Double> tyLeHLCungKyThangTruoc = new HashMap<>();

        LocalDate ngayThangTruoc = MucDoHaiLongUtils.getSamePeriodPreviousMonth(ngayBaoCao);
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> sqlCondition = getSqlCondition(null, ngayThangTruoc, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT NGAY_BAOCAO, SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                "GROUP BY NGAY_BAOCAO";
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL getTyLeHaiLongCungKy theo ngay {}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                Long tongDG = rs.getLong("TONG_DG");
                Long tongDGHaiLong = rs.getLong("TONG_DG_HAILONG");
                if (tongDGHaiLong > 0 && tongDG > 0) {
                    tyLeHLCungKyThangTruoc.put("N" + String.valueOf(rs.getLong("NGAY_BAOCAO")).substring(6, 8),
                            (double) tongDGHaiLong / tongDG * 100);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tyLeHLCungKyThangTruoc;
    }

    private ChiNhanhDatKPIDto getCNDatKPILuyKe(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc,
                                               List<String> listChiNhanhSSO, List<String> listBuuCucSSO) throws IllegalAccessException {
        ChiNhanhDatKPIDto response = new ChiNhanhDatKPIDto();
        Double kpiCurrentMonth = getKpiTong(ngayBaoCao, vung, maChiNhanh);

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> sqlCondition = getChiNhanhSqlCondition("MA_CHINHANH", ngayBaoCao, vung, maChiNhanh, maBuuCuc,
                listChiNhanhSSO, listBuuCucSSO);
        String sqlEntity = sqlCondition.get("sqlEntity");
        String sql = "SELECT " + sqlEntity + ", SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                "GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            int countCNKhongDatKPI = 0;
            int countCNDatKPI = 0;
            while (rs.next()) {
                Double tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(rs.getLong("TONG_DG"), rs.getLong("TONG_DG_HAILONG"));
                if (tyLeHL >= kpiCurrentMonth) {
                    countCNDatKPI++;
                } else {
                    countCNKhongDatKPI++;
                }
            }
            response = new ChiNhanhDatKPIDto("Lũy Kế", countCNDatKPI, countCNKhongDatKPI);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private ChiNhanhDatKPIDto getCNDatKPILuyKeCungKy(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc,
                                                     List<String> listChiNhanhSSO, List<String> listBuuCucSSO) throws IOException, IllegalAccessException {
        ChiNhanhDatKPIDto response = new ChiNhanhDatKPIDto();
        Double kpiCurrentMonth = getKpiTong(ngayBaoCao, vung, maChiNhanh);

        LocalDate ngayThangTruoc = MucDoHaiLongUtils.getSamePeriodPreviousMonth(ngayBaoCao);

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> sqlCondition = getChiNhanhSqlCondition("MA_CHINHANH", ngayThangTruoc, vung, maChiNhanh, maBuuCuc,
                listChiNhanhSSO, listBuuCucSSO);
        String sqlEntity = sqlCondition.get("sqlEntity");

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                "GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL getCNDatKPILuyKeCungKy{}: " + stmt.toString());
            rs = stmt.executeQuery();
            int countCNKhongDatKPI = 0;
            int countCNDatKPI = 0;
            while (rs.next()) {
                Double tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(rs.getLong("TONG_DG"), rs.getLong("TONG_DG_HAILONG"));
                if (tyLeHL >= kpiCurrentMonth) {
                    countCNDatKPI++;
                } else {
                    countCNKhongDatKPI++;
                }
            }
            response = new ChiNhanhDatKPIDto("LuyKeCungKy", countCNDatKPI, countCNKhongDatKPI);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private List<ChiNhanhDatKPIDto> getCNDatKPITheoNgay(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc,
                                                        List<String> listChiNhanhSSO, List<String> listBuuCucSSO) throws IllegalAccessException {
        List<TyLeHaiLongDto> tempList = new ArrayList<>();
        ChiNhanhDatKPIDto dto = new ChiNhanhDatKPIDto();
        List<ChiNhanhDatKPIDto> response = new ArrayList<>();
        HashMap<LocalDate, ChiNhanhDatKPIDto> hashMap = new HashMap<>();
        Double kpiCurrentMonth = getKpiTong(ngayBaoCao, vung, maChiNhanh);;

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        Map<String, String> sqlCondition = getChiNhanhSqlCondition("MA_CHINHANH", ngayBaoCao, vung, maChiNhanh, maBuuCuc,
                listChiNhanhSSO, listBuuCucSSO);
        String sqlEntity = sqlCondition.get("sqlEntity");

        String sql = "SELECT NGAY_BAOCAO, " + sqlEntity + ", SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                "GROUP BY " + sqlEntity + ", NGAY_BAOCAO " +
                "ORDER BY NGAY_BAOCAO DESC ";
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();

            while (!ngayDauThang.isAfter(ngayBaoCao)) { // Chỉ thêm các ngày từ ngày đầu tháng đến ngày hiện tại
                hashMap.put(ngayDauThang, null);
                ngayDauThang = ngayDauThang.plusDays(1); // Di chuyển ngày trở về trước
            }

            while (rs.next()) {
                Long date = rs.getLong("NGAY_BAOCAO");
                String dateStr = String.valueOf(date);
                TyLeHaiLongDto data = new TyLeHaiLongDto();
                data.setVung(sqlEntity.contains("VUNG") ? rs.getString("VUNG") : null);
                data.setMaChiNhanh(sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null);
                data.setMaBuuCuc(sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null);
                data.setMaTuyen(sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null);
                data.setNgayBaoCao(LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd")));
                data.setTongDG(rs.getLong("TONG_DG"));
                data.setTongDGHaiLong(rs.getLong("TONG_DG_HAILONG"));
                tempList.add(data);
            }

            for (Map.Entry<LocalDate, ChiNhanhDatKPIDto> entry : hashMap.entrySet()) {
                int countCNKhongDatKPI = 0;
                int countCNDatKPI = 0;
                LocalDate key = entry.getKey();
                for (int i = 0; i < tempList.size(); i++) {
                    TyLeHaiLongDto checkedObj = tempList.get(i);
                    if (key.equals(checkedObj.getNgayBaoCao())) {
                        Double tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(checkedObj.getTongDG(), checkedObj.getTongDGHaiLong());
                        if (tyLeHL >= kpiCurrentMonth) {
                            countCNDatKPI++;
                        } else {
                            countCNKhongDatKPI++;
                        }
                    }
                }
                dto = new ChiNhanhDatKPIDto("N" + String.format("%02d", key.getDayOfMonth()), countCNDatKPI, countCNKhongDatKPI);
                response.add(dto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private List<ChiNhanhDatKPIDto> getCNDatKPITheoNgayCungKy(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc,
                                                              List<String> listChiNhanhSSO, List<String> listBuuCucSSO) throws IOException, IllegalAccessException {
        List<TyLeHaiLongDto> tempList = new ArrayList<>();
        ChiNhanhDatKPIDto dto = new ChiNhanhDatKPIDto();
        List<ChiNhanhDatKPIDto> response = new ArrayList<>();
        HashMap<LocalDate, ChiNhanhDatKPIDto> hashMap = new HashMap<>();
        Double kpiCurrentMonth = getKpiTong(ngayBaoCao, vung, maChiNhanh);

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        LocalDate ngayThangTruoc = MucDoHaiLongUtils.getSamePeriodPreviousMonth(ngayBaoCao);

        LocalDate ngayDauThang = ngayThangTruoc.withDayOfMonth(1);
        Map<String, String> sqlCondition = getChiNhanhSqlCondition("MA_CHINHANH", ngayThangTruoc, vung, maChiNhanh, maBuuCuc,
                listChiNhanhSSO, listBuuCucSSO);
        String sqlEntity = sqlCondition.get("sqlEntity");

        String sql = "SELECT NGAY_BAOCAO, " + sqlEntity + ", SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                "GROUP BY " + sqlEntity + ", NGAY_BAOCAO " +
                "ORDER BY NGAY_BAOCAO DESC ";
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();

            while (!ngayDauThang.isAfter(ngayBaoCao)) { // Chỉ thêm các ngày từ ngày đầu tháng đến ngày hiện tại
                hashMap.put(ngayDauThang, null);
                ngayDauThang = ngayDauThang.plusDays(1); // Di chuyển ngày trở về trước
            }

            while (rs.next()) {
                Long date = rs.getLong("NGAY_BAOCAO");
                TyLeHaiLongDto data = new TyLeHaiLongDto();
                data.setVung(sqlEntity.contains("VUNG") ? rs.getString("VUNG") : null);
                data.setMaChiNhanh(sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null);
                data.setMaBuuCuc(sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null);
                data.setMaTuyen(sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null);
                data.setNgayBaoCao(LocalDate.parse(String.valueOf(date), DateTimeFormatter.ofPattern("yyyyMMdd")));
                data.setTongDG(rs.getLong("TONG_DG"));
                data.setTongDGHaiLong(rs.getLong("TONG_DG_HAILONG"));
                tempList.add(data);
            }

            for (Map.Entry<LocalDate, ChiNhanhDatKPIDto> entry : hashMap.entrySet()) {
                int countCNKhongDatKPI = 0;
                int countCNDatKPI = 0;
                LocalDate key = entry.getKey();
                for (int i = 0; i < tempList.size(); i++) {
                    TyLeHaiLongDto checkedObj = tempList.get(i);
                    if (key.equals(checkedObj.getNgayBaoCao())) {
                        Double tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(checkedObj.getTongDG(), checkedObj.getTongDGHaiLong());
                        if (tyLeHL >= kpiCurrentMonth) {
                            countCNDatKPI++;
                        } else {
                            countCNKhongDatKPI++;
                        }
                    }
                }
                dto = new ChiNhanhDatKPIDto("N" + String.format("%02d", key.getDayOfMonth()), countCNDatKPI, countCNKhongDatKPI);
                response.add(dto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    private Map<String, String> getSqlCondition(String sqlEntity, LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Map<String, String> result = new HashMap<>();
        String sqlCondition = "";
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int ngayDauThang = Integer.parseInt(ngayBaoCao.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        sqlCondition = "NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC";
        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s)AND TUYEN_BUUTA = 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
            }
//            sqlCondition = String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
        }

        result.put("sqlCondition", sqlCondition);
        result.put("sqlEntity", sqlEntity);
        return result;
    }

    private Map<String, String> getChiNhanhSqlCondition(String sqlEntity, LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Map<String, String> result = new HashMap<>();
        String sqlCondition = "";
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int ngayDauThang = Integer.parseInt(ngayBaoCao.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        sqlCondition = "NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            sqlCondition += "AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("MA_CHINHANH");
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("MA_CHINHANH");
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        result.put("sqlCondition", sqlCondition);
        result.put("sqlEntity", sqlEntity);
        return result;
    }

    private List<TyLeHaiLongDto> sortBaoCaoTongHopTable(List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String sort, String sortBy, List<TyLeHaiLongDto> response) {
        if (!response.isEmpty() || !Objects.isNull(response)) {
            if (sort == null || sort == null) {
                if (vung == null || vung.isEmpty()) {
                    response.sort(Comparator.comparing(TyLeHaiLongDto::getVung));
                } else {
                    if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                        response.sort(Comparator.comparing(TyLeHaiLongDto::getMaChiNhanh));
                    } else {
                        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
                            response.sort(Comparator.comparing(TyLeHaiLongDto::getMaBuuCuc));
                        } else {
                            response.sort(Comparator.comparing(TyLeHaiLongDto::getMaTuyen));
                        }
                    }
                }
            } else {
                switch (sortBy) {
                    case MucDoHaiLongConstants.TONG_DG_HAI_LONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHONG_HAI_LONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhongHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhongHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDG, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDG, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_HAI_LONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTyLeHL, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTyLeHL, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_HAI_LONG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauGiaoHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauGiaoHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHONG_HAI_LONG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauGiaoKhongHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauGiaoKhongHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_HAI_LONG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTyLeHLKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTyLeHLKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_HAI_LONG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauNhanHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauNhanHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHONG_HAI_LONG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauNhanKhongHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauNhanKhongHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTongDGKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_HAI_LONG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTyLeHLKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongDto::getTyLeHLKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                }
            }
        }
        return response;
    }

    private List<TyLeHaiLongExcelDto> sortBaoCaoTongHopTableExcel(List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String sort, String sortBy, List<TyLeHaiLongExcelDto> response) {
        if (!Objects.isNull(response) || !response.isEmpty()) {
            if (sort == null || sortBy == null) {
                if (vung.isEmpty()) {
                    response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getVung));
                } else {
                    if (maChiNhanh.isEmpty()) {
                        response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getMaChiNhanh));
                    } else {
                        if (maBuuCuc.isEmpty()) {
                            response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getMaBuuCuc));
                        } else {
                            response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getMaTuyen));
                        }
                    }
                }
            } else {
                switch (sortBy) {
                    case MucDoHaiLongConstants.TONG_DG_HAI_LONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHONG_HAI_LONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhongHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhongHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDG, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDG, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_HAI_LONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTyLeHL, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTyLeHL, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_HAI_LONG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauGiaoHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauGiaoHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHONG_HAI_LONG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauGiaoKhongHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauGiaoKhongHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_HAI_LONG_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTyLeHLKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTyLeHLKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_HAI_LONG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauNhanHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauNhanHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHONG_HAI_LONG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauNhanKhongHaiLong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauNhanKhongHaiLong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_DG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTongDGKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_HAI_LONG_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTyLeHLKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLeHaiLongExcelDto::getTyLeHLKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                }
            }
        }
        return response;
    }

    private List<KpiMucDoHaiLong> getKpiRecords(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh) {
        /** Caculate average KPI from database, filter via tieuChi **/
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
        String year = ngayBaoCao.format(formatter);
        List<KpiMucDoHaiLong> list = new ArrayList<>();
        if (vung == null || vung.isEmpty()) {
            if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                List<String> keyword = new ArrayList<>();
                keyword.add("TCT");
                list = kpiMucDoHaiLongRepository.findByKeysInput(keyword, keyword, year);
            } else {
                list = kpiMucDoHaiLongRepository.findByKeysInput(null, maChiNhanh, year);
            }
        } else {
            list = kpiMucDoHaiLongRepository.findByKeysInput(vung, maChiNhanh, year);
        }
        return list;
    }

    private Double caculateAverageKpi(LocalDate ngayBaoCao, List<KpiMucDoHaiLong> list) throws IllegalAccessException {
        Double kpiAverage = (double) 0;
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MM");
        String month = ngayBaoCao.format(monthFormatter);
        if (list.size() > 0) {
            for (KpiMucDoHaiLong x : list) {
                Class<KpiMucDoHaiLong> clazz = KpiMucDoHaiLong.class;
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if (field.getName().contains(month)) {
                        field.setAccessible(true);
                        Object value = field.get(x);
                        Double kpi = Double.parseDouble(String.valueOf(value));
                        kpiAverage += kpi;
                        break;
                    }
                }
            }
            kpiAverage = kpiAverage / list.size();
        }
        return kpiAverage;
    }

    private Double getKpiTong(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh) throws IllegalAccessException {
        Double kpiTong = (double) 0;
        List<KpiMucDoHaiLong> list = getKpiRecords(ngayBaoCao, vung, maChiNhanh);
        if (list != null && !list.isEmpty()) {
            List<KpiMucDoHaiLong> listTong = list.stream().filter(e -> "Tổng".equals(e.getTieuChi())).collect(Collectors.toList());
            kpiTong = caculateAverageKpi(ngayBaoCao, listTong);
        }
        return kpiTong;
    }

}
