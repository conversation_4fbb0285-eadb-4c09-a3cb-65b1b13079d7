package nocsystem.indexmanager.servicesIpm.mucdohailong;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.*;

public class ExportExcelTyLeHaiLongTemplate {
    private final SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    List<Map<String, Object>> dataList;
    Map<String, String> mapHeader1;
    List<String> headerRow1;
    List<String> headerRow2;
    String sheetName;

    public ExportExcelTyLeHaiLongTemplate(){
        workbook = new SXSSFWorkbook(1);

    }

    public ExportExcelTyLeHaiLongTemplate(
        List<Map<String, Object>> dataList,
        Map<String, String> mapHeader1,
        List<String> headerRow1,
        List<String> headerRow2,
        String sheetName
    ) {
        this.dataList = dataList;
        this.mapHeader1 = mapHeader1;
        this.headerRow1 = headerRow1;
        this.headerRow2 = headerRow2;
        this.sheetName = sheetName;
        workbook = new SXSSFWorkbook(1);
    }

    private void createCell(SXSSFRow row, int columnCount, Object value, CellStyle style) {
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof BigInteger) {
            cell.setCellValue(((BigInteger) value).longValue());
        }else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }


    private void createHeaderRowAll() {
        sheet = workbook.createSheet(sheetName);
        workbook.setCompressTempFiles(true);
        SXSSFRow row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        Map<String, Integer> mapHeader2 = new LinkedHashMap<>();
        for (String x : headerRow1) {
            if (!mapHeader2.containsKey(x)) {
                mapHeader2.put(x, 1);
            } else {
                mapHeader2.put(x, mapHeader2.get(x) + 1);
            }
        }

        int soCot = 0;
        for (String x : headerRow1) {
            createCell(row, soCot, x, style);
            soCot++;
        }
        SXSSFRow row1 = sheet.createRow(1);

        soCot = 0;
        for (String x : headerRow2) {
            createCell(row1, soCot, mapHeader1.get(x), style);
            sheet.setColumnWidth(soCot, ((int) (mapHeader1.get(x).length() * 1.5)) * 210);
            soCot++;
        }

        for (int j = 0; j < headerRow1.size(); j++) {
            if (mapHeader1.get(headerRow2.get(j)).equals(headerRow1.get(j))) {
                sheet.addMergedRegion(new CellRangeAddress(0, 1, j, j));
            }
        }

        for (int i = 0; i < headerRow1.size(); i++) {
            if (mapHeader2.get(headerRow1.get(i)) > 1) {
                sheet.addMergedRegion(new CellRangeAddress(0, 0, i, i + mapHeader2.get(headerRow1.get(i)) - 1));
                mapHeader2.put(headerRow1.get(i), mapHeader2.get(headerRow1.get(i)) - mapHeader2.get(headerRow1.get(i)) + 1);
            }
        }
    }

    public void writeDataALL() throws IOException {
        int rowNum = 3;
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        int countColumn = 0;
        if (dataList != null) {
            dataList.remove(dataList.size() - 1);
            for (Map<String, Object> x : dataList) {
                SXSSFRow row = sheet.createRow(rowNum);
                for (int i = 0; i < headerRow2.size(); i++) {
                    if (i == 0) {
                        createCell(row, i, ++countColumn, style);
                    } else {
                        if (headerRow2.get(i).equals("tyLeHL") || headerRow2.get(i).equals("tyLeHLKhauGiao") || headerRow2.get(i).equals("tyLeHLKhauNhan") ||
                                headerRow2.get(i).equals("tyLePhanHoi") || headerRow2.get(i).equals("tyLePhanHoiKhauGiao") || headerRow2.get(i).equals("tyLePhanHoiKhauNhan")) {
                            createCell(row, i, x.get(headerRow2.get(i)) + " %", style);
                        } else createCell(row, i, x.get(headerRow2.get(i)), style);
                    }
                }
                rowNum++;
            }
        }
    }

    public void createTotalRow() {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        CellStyle style1 = workbook.createCellStyle();
        style1.setBorderTop(BorderStyle.THIN);
        style1.setBorderRight(BorderStyle.THIN);
        style1.setBorderBottom(BorderStyle.THIN);
        style1.setBorderLeft(BorderStyle.THIN);
        style1.setAlignment(HorizontalAlignment.CENTER);
        style1.setVerticalAlignment(VerticalAlignment.CENTER);

        int countColumn = 2;
        SXSSFRow row1 = sheet.createRow(2);
        if (mapHeader1.containsKey("maChiNhanh")) {
            ++countColumn;
            if (mapHeader1.containsKey("maBuuCuc")) {
                ++countColumn;
                if (mapHeader1.containsKey("maTuyen")) {
                    ++countColumn;
                    createCell(row1, 0, "Tổng", style1);
                    createCell(row1, 1, "", style1);
                    createCell(row1, 2, "", style1);
                    createCell(row1, 3, "", style1);
                    createCell(row1, 4, "", style1);
                    sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 4));
                } else {
                    createCell(row1, 0, "Tổng", style1);
                    createCell(row1, 1, "", style1);
                    createCell(row1, 2, "", style1);
                    createCell(row1, 3, "", style1);
                    sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 3));
                }
            } else {
                createCell(row1, 0, "Tổng", style1);
                createCell(row1, 1, "", style1);
                createCell(row1, 2, "", style1);
                sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 2));
            }
        }
        if (dataList != null) {
            Map<String, Object> totalRowMap = dataList.get(dataList.size() - 1);
            for (int i = countColumn; i < headerRow2.size(); i++) {
                if (headerRow2.get(i).equals("tyLeHL") || headerRow2.get(i).equals("tyLeHLKhauGiao") || headerRow2.get(i).equals("tyLeHLKhauNhan") ||
                        headerRow2.get(i).equals("tyLePhanHoi") || headerRow2.get(i).equals("tyLePhanHoiKhauGiao") || headerRow2.get(i).equals("tyLePhanHoiKhauNhan")) {
                    createCell(row1, i, totalRowMap.get(headerRow2.get(i)) + " %", style);
                } else createCell(row1, i, totalRowMap.get(headerRow2.get(i)), style);
            }
        } else {
            for (int i = countColumn; i < headerRow2.size(); i++) {
                    createCell(row1, i, "", style);
            }
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
        createHeaderRowAll();
        createTotalRow();
        writeDataALL();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
            workbook.dispose();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            workbook.close();
        }
    }
}
