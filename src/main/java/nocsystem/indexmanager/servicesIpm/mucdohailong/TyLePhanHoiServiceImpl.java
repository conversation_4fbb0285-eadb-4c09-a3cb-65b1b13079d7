package nocsystem.indexmanager.servicesIpm.mucdohailong;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.constants.MucDoHaiLongConstants;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.mucdohailong.TyLePhanHoiDashboardDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyLePhanHoiDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyLePhanHoiExcelDto;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import nocsystem.indexmanager.services.mucdohailong.TyLePhanHoiService;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TyLePhanHoiServiceImpl<T> extends AbstractDao implements TyLePhanHoiService {

    private final Logger log = LoggerFactory.getLogger(TyLeHaiLongServiceImpl.class);

    @Override
    public List<TyLePhanHoiDashboardDto> getTyLePhanHoiDashBoard(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException {
        TyLePhanHoiDto dto = new TyLePhanHoiDto();
        List<TyLePhanHoiDashboardDto> tyLePhanHoiResult = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ checkl phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return tyLePhanHoiResult;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        Map<String, String> sqlCondition = getSqlCondition(null, ngayBaoCao, vung, maChiNhanh, maBuuCuc,
                listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT SUM(TONG_KHAOSAT) AS TONG_KHAOSAT, SUM(TONG_PHANHOI) AS TONG_PHANHOI, SUM(TONG_KH_TU_DANHGIA) AS TONG_KH_TU_DANHGIA, " +
                "SUM(TONG_KS_KHAUGIAO) AS TONG_KS_KHAUGIAO, SUM(TONG_PH_KHAUGIAO) AS TONG_PH_KHAUGIAO, SUM(TONG_KH_TU_DANHGIA_KHAUGIAO) AS TONG_KH_TU_DANHGIA_KHAUGIAO, " +
                "SUM(TONG_KS_KHAUNHAN) AS TONG_KS_KHAUNHAN, SUM(TONG_PH_KHAUNHAN) AS TONG_PH_KHAUNHAN, SUM(TONG_KH_TU_DANHGIA_KHAUNHAN) AS TONG_KH_TU_DANHGIA_KHAUNHAN, " +
                "SUM(TONG_KS_KENHBT) AS TONG_KS_KENHBT, SUM(TONG_PH_KENHBT) AS TONG_PH_KENHBT, SUM(TONG_KH_TU_DANHGIA_KENHBT) AS TONG_KH_TU_DANHGIA_KENHBT, " +
                "SUM(TONG_KS_KENHBC) AS TONG_KS_KENHBC, SUM(TONG_PH_KENHBC) AS TONG_PH_KENHBC, SUM(TONG_KH_TU_DANHGIA_KENHBC) AS TONG_KH_TU_DANHGIA_KENHBC " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition");

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new TyLePhanHoiDto(
                        rs.getLong("TONG_KHAOSAT"),
                        rs.getLong("TONG_PHANHOI"),
                        rs.getLong("TONG_KH_TU_DANHGIA"),
                        rs.getLong("TONG_KS_KHAUGIAO"),
                        rs.getLong("TONG_PH_KHAUGIAO"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KHAUGIAO"),
                        rs.getLong("TONG_KS_KHAUNHAN"),
                        rs.getLong("TONG_PH_KHAUNHAN"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KHAUNHAN"),
                        rs.getLong("TONG_KS_KENHBT"),
                        rs.getLong("TONG_PH_KENHBT"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KENHBT"),
                        rs.getLong("TONG_KS_KENHBC"),
                        rs.getLong("TONG_PH_KENHBC"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KENHBC"));
            }

            if (dto != null) {
                TyLePhanHoiDashboardDto tyLeTong = new TyLePhanHoiDashboardDto(dto, "Tong");
                TyLePhanHoiDashboardDto tyLeKhauGiao = new TyLePhanHoiDashboardDto(dto, "KhauGiao");
                TyLePhanHoiDashboardDto tyLeKhauNhan = new TyLePhanHoiDashboardDto(dto, "KhauNhan");
                TyLePhanHoiDashboardDto tyLeKenhBT = new TyLePhanHoiDashboardDto(dto, "KenhBT");
                TyLePhanHoiDashboardDto tyLeKenhBC = new TyLePhanHoiDashboardDto(dto, "KenhBC");

                List<Double> tyleCungKyList = getTyLePhanHoiCungKy(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
                if (!tyleCungKyList.isEmpty() && !Objects.isNull(tyleCungKyList)) {
                    if (!Objects.isNull(tyleCungKyList.get(0))) {
                        tyLeTong.setTangTruongThang(tyLeTong.getTyLePhanHoi() - tyleCungKyList.get(0).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(1))) {
                        tyLeKhauGiao.setTangTruongThang(tyLeKhauGiao.getTyLePhanHoi() - tyleCungKyList.get(1).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(2))) {
                        tyLeKhauNhan.setTangTruongThang(tyLeKhauNhan.getTyLePhanHoi() - tyleCungKyList.get(2).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(3))) {
                        tyLeKenhBT.setTangTruongThang(tyLeKenhBT.getTyLePhanHoi() - tyleCungKyList.get(3).doubleValue());
                    }
                    if (!Objects.isNull(tyleCungKyList.get(4))) {
                        tyLeKenhBC.setTangTruongThang(tyLeKenhBC.getTyLePhanHoi() - tyleCungKyList.get(4).doubleValue());
                    }
                }
                tyLePhanHoiResult.add(tyLeTong);
                tyLePhanHoiResult.add(tyLeKhauGiao);
                tyLePhanHoiResult.add(tyLeKhauNhan);
                tyLePhanHoiResult.add(tyLeKenhBT);
                tyLePhanHoiResult.add(tyLeKenhBC);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tyLePhanHoiResult;
    }

    @Override
    public PageWithTotalNumber<TyLePhanHoiDto> getBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request) {
        PageWithTotalNumber<TyLePhanHoiDto> resultPage = null;
        TyLePhanHoiDto dto = new TyLePhanHoiDto();
        List<TyLePhanHoiDto> dtoList = new ArrayList<>();
        TyLePhanHoiDto totalRow = null;
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;

        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlEntity = "VUNG, MA_CHINHANH";
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(request.getNgayBaoCao().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (request.getType()) {
            case "0":
                LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            sqlCondition += "AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            String listBuuCuc = String.join("', '", request.getMaBuuCuc());
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            String listBuuCuc = String.join("', '", request.getMaBuuCuc());
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (request.getType()) {
                case "0":
                    LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCuc = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCuc = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (request.getType()) {
                case "0":
                    LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }

            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCucFilter = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCucFilter = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s)AND TUYEN_BUUTA <> 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
//            sqlCondition = String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
        }

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_KHAOSAT) AS TONG_KHAOSAT, SUM(TONG_PHANHOI) AS TONG_PHANHOI, SUM(TONG_KH_TU_DANHGIA) AS TONG_KH_TU_DANHGIA, " +
                "SUM(TONG_KS_KHAUGIAO) AS TONG_KS_KHAUGIAO, SUM(TONG_PH_KHAUGIAO) AS TONG_PH_KHAUGIAO, SUM(TONG_KH_TU_DANHGIA_KHAUGIAO) AS TONG_KH_TU_DANHGIA_KHAUGIAO, " +
                "SUM(TONG_KS_KHAUNHAN) AS TONG_KS_KHAUNHAN, SUM(TONG_PH_KHAUNHAN) AS TONG_PH_KHAUNHAN, SUM(TONG_KH_TU_DANHGIA_KHAUNHAN) AS TONG_KH_TU_DANHGIA_KHAUNHAN, " +
                "SUM(TONG_KS_KENHBT) AS TONG_KS_KENHBT, SUM(TONG_PH_KENHBT) AS TONG_PH_KENHBT, SUM(TONG_KH_TU_DANHGIA_KENHBT) AS TONG_KH_TU_DANHGIA_KENHBT, " +
                "SUM(TONG_KS_KENHBC) AS TONG_KS_KENHBC, SUM(TONG_PH_KENHBC) AS TONG_PH_KENHBC, SUM(TONG_KH_TU_DANHGIA_KENHBC) AS TONG_KH_TU_DANHGIA_KENHBC " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition +
                "GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new TyLePhanHoiDto(
                        rs.getString("VUNG"),
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("TONG_KHAOSAT"),
                        rs.getLong("TONG_PHANHOI"),
                        rs.getLong("TONG_KH_TU_DANHGIA"),
                        rs.getLong("TONG_KS_KHAUGIAO"),
                        rs.getLong("TONG_PH_KHAUGIAO"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KHAUGIAO"),
                        rs.getLong("TONG_KS_KHAUNHAN"),
                        rs.getLong("TONG_PH_KHAUNHAN"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KHAUNHAN"));
                dtoList.add(dto);
            }
            sortBaoCaoTongHopTable(request.getVung(), request.getMaChiNhanh(), request.getMaBuuCuc(), request.getSort(), request.getSortBy(), dtoList);
            Long tongKhaoSat = 0L;
            Long tongPhanHoi = 0L;
            Long tongKHTuDanhGia = 0L;
            Long tongKhaoSatKhauGiao = 0L;
            Long tongPhanHoiKhauGiao = 0L;
            Long tongKHTuDanhGiaKhauGiao = 0L;
            Long tongKhaoSatKhauNhan = 0L;
            Long tongPhanHoiKhauNhan = 0L;
            Long tongKHTuDanhGiaKhauNhan = 0L;
            if (dtoList.size() > 0) {
                for (TyLePhanHoiDto x : dtoList) {
                    tongKhaoSat += x.getTongKhaoSat();
                    tongPhanHoi += x.getTongPhanHoi();
                    tongKHTuDanhGia += x.getTongKHTuDanhGia();

                    tongKhaoSatKhauGiao += x.getTongKhaoSatKhauGiao();
                    tongPhanHoiKhauGiao += x.getTongPhanHoiKhauGiao();
                    tongKHTuDanhGiaKhauGiao += x.getTongKHTuDanhGiaKhauGiao();

                    tongKhaoSatKhauNhan += x.getTongKhaoSatKhauNhan();
                    tongPhanHoiKhauNhan += x.getTongPhanHoiKhauNhan();
                    tongKHTuDanhGiaKhauNhan += x.getTongKHTuDanhGiaKhauNhan();
                }
                totalRow = new TyLePhanHoiDto("TONG",
                        tongKhaoSat, tongPhanHoi, tongKHTuDanhGia,
                        tongKhaoSatKhauGiao, tongPhanHoiKhauGiao, tongKHTuDanhGiaKhauGiao,
                        tongKhaoSatKhauNhan, tongPhanHoiKhauNhan, tongKHTuDanhGiaKhauNhan);
            }

            int pageNumber = request.getPage();
            int pageSize = request.getSize();
            if (pageNumber > 0) pageNumber--;
            int start = (int) Math.min(pageNumber * pageSize, dtoList.size());
            int end = (int) Math.min((pageNumber + 1) * pageSize, dtoList.size());

            List<TyLePhanHoiDto> listTotal = new ArrayList<>(dtoList); // this list to get true size of list when pagination
            List<TyLePhanHoiDto> sublist = dtoList.subList(start, end);
            if (sublist.size() > 0) {
                sublist.add(totalRow);
            }
            resultPage = new PageWithTotalNumber<TyLePhanHoiDto>(sublist, PageRequest.of(pageNumber, pageSize), listTotal.size());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getBaoCaoTongHop in class TyLePhanHoiServiceImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return resultPage;
    }

    @Override
    public void exportExcelBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request, HttpServletResponse response) {
        LocalDate ngayBaoCao = request.getNgayBaoCao();
        List<String> vung = request.getVung();
        List<String> maChiNhanh = request.getMaChiNhanh();
        List<String> maBuuCuc = request.getMaBuuCuc();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCtylephanhoi.xlsx";
        response.setHeader(headerKey, headerValue);

        Map<String, String> mapInfo = new HashMap<>();

        mapInfo.put("stt", "STT");
        mapInfo.put("vung", "Vùng");
        mapInfo.put("maChiNhanh", "Chi nhánh");
        if (!maChiNhanh.isEmpty()) {
            mapInfo.put("maBuuCuc", "Bưu cục");
            if (!maBuuCuc.isEmpty()) {
                mapInfo.put("maTuyen", "Tuyến");
            }
        }
        mapInfo.put("tongKhaoSat", "Tổng tin nhắn gửi khảo sát [1]");
        mapInfo.put("tongPhanHoi", "Tổng tin nhắn nhận được phản hồi [2]");
        mapInfo.put("tongKHTuDanhGia", "KH tự đánh giá qua app/web [3]");
        mapInfo.put("tyLePhanHoi", "Tỷ lệ phản hồi [4]=([2]+[3])/([1]+[3])");

        mapInfo.put("tongKhaoSatKhauGiao", "Tổng tin nhắn gửi khảo sát [5]");
        mapInfo.put("tongPhanHoiKhauGiao", "Tổng tin nhắn nhận được phản hồi [6]");
        mapInfo.put("tongKHTuDanhGiaKhauGiao", "KH tự đánh giá qua app/web [7]");
        mapInfo.put("tyLePhanHoiKhauGiao", "Tỷ lệ phản hồi [8]=([6]+[7])/([5]+[7])");

        mapInfo.put("tongKhaoSatKhauNhan", "Tổng tin nhắn gửi khảo sát [9]");
        mapInfo.put("tongPhanHoiKhauNhan", "Tổng tin nhắn nhận được phản hồi [10]");
        mapInfo.put("tongKHTuDanhGiaKhauNhan", "KH tự đánh giá qua app/web [11]");
        mapInfo.put("tyLePhanHoiKhauNhan", "Tỷ lệ phản hồi [12]=([10]+[11])/([9]+[11])");

        List<String> firstHeaders = null;
        List<String> secondHeaders = null;

        if (!maChiNhanh.isEmpty()) {
            if (!maBuuCuc.isEmpty()) {
                firstHeaders = Arrays.asList(
                        "STT",
                        "Vùng",
                        "Chi nhánh",
                        "Bưu cục",
                        "Tuyến",

                        "Tổng",
                        "Tổng",
                        "Tổng",
                        "Tổng",

                        "Khâu giao",
                        "Khâu giao",
                        "Khâu giao",
                        "Khâu giao",

                        "Khâu nhận",
                        "Khâu nhận",
                        "Khâu nhận",
                        "Khâu nhận"
                );

                secondHeaders = Arrays.asList(
                        "stt",
                        "vung",
                        "maChiNhanh",
                        "maBuuCuc",
                        "maTuyen",

                        "tongKhaoSat",
                        "tongPhanHoi",
                        "tongKHTuDanhGia",
                        "tyLePhanHoi",

                        "tongKhaoSatKhauGiao",
                        "tongPhanHoiKhauGiao",
                        "tongKHTuDanhGiaKhauGiao",
                        "tyLePhanHoiKhauGiao",

                        "tongKhaoSatKhauNhan",
                        "tongPhanHoiKhauNhan",
                        "tongKHTuDanhGiaKhauNhan",
                        "tyLePhanHoiKhauNhan"
                );
            } else {
                firstHeaders = Arrays.asList(
                        "STT",
                        "Vùng",
                        "Chi nhánh",
                        "Bưu cục",

                        "Tổng",
                        "Tổng",
                        "Tổng",
                        "Tổng",

                        "Khâu giao",
                        "Khâu giao",
                        "Khâu giao",
                        "Khâu giao",

                        "Khâu nhận",
                        "Khâu nhận",
                        "Khâu nhận",
                        "Khâu nhận"
                );

                secondHeaders = Arrays.asList(
                        "stt",
                        "vung",
                        "maChiNhanh",
                        "maBuuCuc",

                        "tongKhaoSat",
                        "tongPhanHoi",
                        "tongKHTuDanhGia",
                        "tyLePhanHoi",

                        "tongKhaoSatKhauGiao",
                        "tongPhanHoiKhauGiao",
                        "tongKHTuDanhGiaKhauGiao",
                        "tyLePhanHoiKhauGiao",

                        "tongKhaoSatKhauNhan",
                        "tongPhanHoiKhauNhan",
                        "tongKHTuDanhGiaKhauNhan",
                        "tyLePhanHoiKhauNhan"
                );
            }
        } else {
            firstHeaders = Arrays.asList(
                    "STT",
                    "Vùng",
                    "Chi nhánh",

                    "Tổng",
                    "Tổng",
                    "Tổng",
                    "Tổng",

                    "Khâu giao",
                    "Khâu giao",
                    "Khâu giao",
                    "Khâu giao",

                    "Khâu nhận",
                    "Khâu nhận",
                    "Khâu nhận",
                    "Khâu nhận"
            );

            secondHeaders = Arrays.asList(
                    "stt",
                    "vung",
                    "maChiNhanh",

                    "tongKhaoSat",
                    "tongPhanHoi",
                    "tongKHTuDanhGia",
                    "tyLePhanHoi",

                    "tongKhaoSatKhauGiao",
                    "tongPhanHoiKhauGiao",
                    "tongKHTuDanhGiaKhauGiao",
                    "tyLePhanHoiKhauGiao",

                    "tongKhaoSatKhauNhan",
                    "tongPhanHoiKhauNhan",
                    "tongKHTuDanhGiaKhauNhan",
                    "tyLePhanHoiKhauNhan"
            );
        }
        ExportExcelTyLeHaiLongTemplate template = new ExportExcelTyLeHaiLongTemplate(
                getDataTongHopToExport(ngayBaoCao, vung, maChiNhanh, maBuuCuc, request.getType(), request.getSort(), request.getSortBy()),
                mapInfo,
                firstHeaders,
                secondHeaders,
                "BaoCaoTyLePhanHoi"
        );
        try {
            template.exportDataToExcel(response);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }

    }

    private List<Double> getTyLePhanHoiCungKy(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        List<Double> response = new ArrayList<>();
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        LocalDate ngayThangTruoc = MucDoHaiLongUtils.getSamePeriodPreviousMonth(ngayBaoCao);

        Map<String, String> sqlCondition = getSqlCondition(null, ngayThangTruoc, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT SUM(TONG_KHAOSAT) AS TONG_KHAOSAT, SUM(TONG_PHANHOI) AS TONG_PHANHOI, SUM(TONG_KH_TU_DANHGIA) AS TONG_KH_TU_DANHGIA, " +
                "SUM(TONG_KS_KHAUGIAO) AS TONG_KS_KHAUGIAO, SUM(TONG_PH_KHAUGIAO) AS TONG_PH_KHAUGIAO, SUM(TONG_KH_TU_DANHGIA_KHAUGIAO) AS TONG_KH_TU_DANHGIA_KHAUGIAO, " +
                "SUM(TONG_KS_KHAUNHAN) AS TONG_KS_KHAUNHAN, SUM(TONG_PH_KHAUNHAN) AS TONG_PH_KHAUNHAN, SUM(TONG_KH_TU_DANHGIA_KHAUNHAN) AS TONG_KH_TU_DANHGIA_KHAUNHAN, " +
                "SUM(TONG_KS_KENHBT) AS TONG_KS_KENHBT, SUM(TONG_PH_KENHBT) AS TONG_PH_KENHBT, SUM(TONG_KH_TU_DANHGIA_KENHBT) AS TONG_KH_TU_DANHGIA_KENHBT, " +
                "SUM(TONG_KS_KENHBC) AS TONG_KS_KENHBC, SUM(TONG_PH_KENHBC) AS TONG_PH_KENHBC, SUM(TONG_KH_TU_DANHGIA_KENHBC) AS TONG_KH_TU_DANHGIA_KENHBC " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition");

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                Long tongPhanHoi = rs.getLong("TONG_PHANHOI");
                Long tongMauKhaoSat = rs.getLong("TONG_KHAOSAT");
                double tyLePhanHoiTong = 0;
                if (tongPhanHoi > 0 && tongMauKhaoSat > 0) {
                    tyLePhanHoiTong = (double) tongPhanHoi / tongMauKhaoSat * 100;
                }
                response.add(tyLePhanHoiTong);

                Long tongPhanHoiKhauGiao = rs.getLong("TONG_PH_KHAUGIAO");
                Long tongMauKhaoSatKhauGiao = rs.getLong("TONG_KS_KHAUGIAO");
                double tyLePhanHoiKhauGiao = 0;
                if (tongPhanHoiKhauGiao > 0 && tongMauKhaoSatKhauGiao > 0) {
                    tyLePhanHoiKhauGiao = (double) tongPhanHoiKhauGiao / tongMauKhaoSatKhauGiao * 100;
                }
                response.add(tyLePhanHoiKhauGiao);

                Long tongPhanHoiKhauNhan = rs.getLong("TONG_PH_KHAUNHAN");
                Long tongMauKhaoSatKhauNhan = rs.getLong("TONG_KS_KHAUNHAN");
                double tyLePhanHoiKhauNhan = 0;
                if (tongPhanHoiKhauNhan > 0 && tongMauKhaoSatKhauNhan > 0) {
                    tyLePhanHoiKhauNhan = (double) tongPhanHoiKhauNhan / tongMauKhaoSatKhauNhan * 100;
                }
                response.add(tyLePhanHoiKhauNhan);

                Long tongPhanHoiKenhBT = rs.getLong("TONG_PH_KENHBT");
                Long tongMauKhaoSatKenhBT = rs.getLong("TONG_KS_KENHBT");
                double tyLePhanHoiKenhBT = 0;
                if (tongPhanHoiKenhBT > 0 && tongMauKhaoSatKenhBT > 0) {
                    tyLePhanHoiKenhBT = (double) tongPhanHoiKenhBT / tongMauKhaoSatKenhBT * 100;
                }
                response.add(tyLePhanHoiKenhBT);

                Long tongPhanHoiKenhBC = rs.getLong("TONG_PH_KENHBC");
                Long tongMauKhaoSatKenhBC = rs.getLong("TONG_KS_KENHBC");
                double tyLePhanHoiKenhBC = 0;
                if (tongPhanHoiKenhBC > 0 && tongMauKhaoSatKenhBC > 0) {
                    tyLePhanHoiKenhBC = (double) tongPhanHoiKenhBC / tongMauKhaoSatKenhBC * 100;
                }
                response.add(tyLePhanHoiKenhBC);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }

        return response;
    }

    private List<Map<String, Object>> getDataTongHopToExport(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type,
                                                             String sort, String sortBy) {
        TyLePhanHoiExcelDto dto = new TyLePhanHoiExcelDto();
        List<TyLePhanHoiExcelDto> dtoList = new ArrayList<>();
        TyLePhanHoiExcelDto totalRow = null;
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
            }
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlEntity = "VUNG, MA_CHINHANH";
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            sqlCondition += "AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }

            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s)AND TUYEN_BUUTA <> 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
//            sqlCondition = String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
        }

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_KHAOSAT) AS TONG_KHAOSAT, SUM(TONG_PHANHOI) AS TONG_PHANHOI, SUM(TONG_KH_TU_DANHGIA) AS TONG_KH_TU_DANHGIA, " +
                "SUM(TONG_KS_KHAUGIAO) AS TONG_KS_KHAUGIAO, SUM(TONG_PH_KHAUGIAO) AS TONG_PH_KHAUGIAO, SUM(TONG_KH_TU_DANHGIA_KHAUGIAO) AS TONG_KH_TU_DANHGIA_KHAUGIAO, " +
                "SUM(TONG_KS_KHAUNHAN) AS TONG_KS_KHAUNHAN, SUM(TONG_PH_KHAUNHAN) AS TONG_PH_KHAUNHAN, SUM(TONG_KH_TU_DANHGIA_KHAUNHAN) AS TONG_KH_TU_DANHGIA_KHAUNHAN, " +
                "SUM(TONG_KS_KENHBT) AS TONG_KS_KENHBT, SUM(TONG_PH_KENHBT) AS TONG_PH_KENHBT, SUM(TONG_KH_TU_DANHGIA_KENHBT) AS TONG_KH_TU_DANHGIA_KENHBT, " +
                "SUM(TONG_KS_KENHBC) AS TONG_KS_KENHBC, SUM(TONG_PH_KENHBC) AS TONG_PH_KENHBC, SUM(TONG_KH_TU_DANHGIA_KENHBC) AS TONG_KH_TU_DANHGIA_KENHBC " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition +
                "GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new TyLePhanHoiExcelDto(
                        rs.getString("VUNG"),
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("TONG_KHAOSAT"),
                        rs.getLong("TONG_PHANHOI"),
                        rs.getLong("TONG_KH_TU_DANHGIA"),
                        rs.getLong("TONG_KS_KHAUGIAO"),
                        rs.getLong("TONG_PH_KHAUGIAO"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KHAUGIAO"),
                        rs.getLong("TONG_KS_KHAUNHAN"),
                        rs.getLong("TONG_PH_KHAUNHAN"),
                        rs.getLong("TONG_KH_TU_DANHGIA_KHAUNHAN")
                );
                dtoList.add(dto);
            }
            sortBaoCaoTongHopTableExcel(vung, maChiNhanh, maBuuCuc, sort, sortBy, dtoList);
            Long tongKhaoSat = 0L;
            Long tongPhanHoi = 0L;
            Long tongKHTuDanhGia = 0L;
            Long tongKhaoSatKhauGiao = 0L;
            Long tongPhanHoiKhauGiao = 0L;
            Long tongKHTuDanhGiaKhauGiao = 0L;
            Long tongKhaoSatKhauNhan = 0L;
            Long tongPhanHoiKhauNhan = 0L;
            Long tongKHTuDanhGiaKhauNhan = 0L;
            if (dtoList.size() > 0) {
                for (TyLePhanHoiExcelDto x : dtoList) {
                    tongKhaoSat += x.getTongKhaoSat();
                    tongPhanHoi += x.getTongPhanHoi();
                    tongKHTuDanhGia += x.getTongKHTuDanhGia();

                    tongKhaoSatKhauGiao += x.getTongKhaoSatKhauGiao();
                    tongPhanHoiKhauGiao += x.getTongPhanHoiKhauGiao();
                    tongKHTuDanhGiaKhauGiao += x.getTongKHTuDanhGiaKhauGiao();

                    tongKhaoSatKhauNhan += x.getTongKhaoSatKhauNhan();
                    tongPhanHoiKhauNhan += x.getTongPhanHoiKhauNhan();
                    tongKHTuDanhGiaKhauNhan += x.getTongKHTuDanhGiaKhauNhan();
                }
                totalRow = new TyLePhanHoiExcelDto("TONG",
                        tongKhaoSat, tongPhanHoi, tongKHTuDanhGia,
                        tongKhaoSatKhauGiao, tongPhanHoiKhauGiao, tongKHTuDanhGiaKhauGiao,
                        tongKhaoSatKhauNhan, tongPhanHoiKhauNhan, tongKHTuDanhGiaKhauNhan);
                dtoList.add(totalRow);
                return convertListMap((List<T>) dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getBaoCaoTongHop in class TyLePhanHoiServiceImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return null;
    }

    private List<Map<String, Object>> convertListMap(List<T> dataList) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!dataList.isEmpty()) {
            int i = 0;
            for (T data : dataList) {
                TyLePhanHoiExcelDto x = (TyLePhanHoiExcelDto) data;
                totalRows.add(x.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    private Map<String, String> getSqlCondition(String sqlEntity, LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Map<String, String> result = new HashMap<>();
        String sqlCondition = "";
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int ngayDauThang = Integer.parseInt(ngayBaoCao.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        sqlCondition = "NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC";
        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s)AND TUYEN_BUUTA = 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
            }
//            sqlCondition = String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
        }

        result.put("sqlCondition", sqlCondition);
        result.put("sqlEntity", sqlEntity);
        return result;
    }

    private List<TyLePhanHoiDto> sortBaoCaoTongHopTable(List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String sort, String sortBy, List<TyLePhanHoiDto> response) {
        if (!response.isEmpty() || !Objects.isNull(response)) {
            if (sort == null || sort == null) {
                if (vung == null || vung.isEmpty()) {
                    response.sort(Comparator.comparing(TyLePhanHoiDto::getVung));
                } else {
                    if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                        response.sort(Comparator.comparing(TyLePhanHoiDto::getMaChiNhanh));
                    } else {
                        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
                            response.sort(Comparator.comparing(TyLePhanHoiDto::getMaBuuCuc));
                        } else {
                            response.sort(Comparator.comparing(TyLePhanHoiDto::getMaTuyen));
                        }
                    }
                }
            } else {
                switch (sortBy) {
                    case MucDoHaiLongConstants.TONG_TN_KHAO_SAT:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKhaoSat, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKhaoSat, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_NHAN_PHAN_HOI:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongPhanHoi, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongPhanHoi, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KH_TU_DANH_GIA:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKHTuDanhGia, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKHTuDanhGia, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_PHAN_HOI:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTyLePhanHoi, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTyLePhanHoi, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_KHAO_SAT_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKhaoSatKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKhaoSatKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_NHAN_PHAN_HOI_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongPhanHoiKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongPhanHoiKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KH_TU_DANH_GIA_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKHTuDanhGiaKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKHTuDanhGiaKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_PHAN_HOI_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTyLePhanHoiKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTyLePhanHoiKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_KHAO_SAT_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKhaoSatKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKhaoSatKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_NHAN_PHAN_HOI_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongPhanHoiKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongPhanHoiKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KH_TU_DANH_GIA_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKHTuDanhGiaKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTongKHTuDanhGiaKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_PHAN_HOI_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTyLePhanHoiKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiDto::getTyLePhanHoiKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                }
            }
        }
        return response;
    }

    private List<TyLePhanHoiExcelDto> sortBaoCaoTongHopTableExcel(List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String sort, String sortBy, List<TyLePhanHoiExcelDto> response) {
        if (!response.isEmpty() || !Objects.isNull(response)) {
            if (sort == null || sort == null) {
                if (vung == null || vung.isEmpty()) {
                    response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getVung));
                } else {
                    if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                        response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getMaChiNhanh));
                    } else {
                        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
                            response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getMaBuuCuc));
                        } else {
                            response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getMaTuyen));
                        }
                    }
                }
            } else {
                switch (sortBy) {
                    case MucDoHaiLongConstants.TONG_TN_KHAO_SAT:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKhaoSat, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKhaoSat, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_NHAN_PHAN_HOI:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongPhanHoi, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongPhanHoi, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KH_TU_DANH_GIA:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKHTuDanhGia, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKHTuDanhGia, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_PHAN_HOI:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTyLePhanHoi, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTyLePhanHoi, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_KHAO_SAT_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKhaoSatKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKhaoSatKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_NHAN_PHAN_HOI_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongPhanHoiKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongPhanHoiKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KH_TU_DANH_GIA_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKHTuDanhGiaKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKHTuDanhGiaKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_PHAN_HOI_KHAU_GIAO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTyLePhanHoiKhauGiao, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTyLePhanHoiKhauGiao, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_KHAO_SAT_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKhaoSatKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKhaoSatKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_TN_NHAN_PHAN_HOI_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongPhanHoiKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongPhanHoiKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KH_TU_DANH_GIA_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKHTuDanhGiaKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTongKHTuDanhGiaKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TY_LE_PHAN_HOI_KHAU_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTyLePhanHoiKhauNhan, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(TyLePhanHoiExcelDto::getTyLePhanHoiKhauNhan, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                }
            }
        }
        return response;
    }
}
