package nocsystem.indexmanager.servicesIpm.mucdohailong;

import nocsystem.indexmanager.config.Hbase.PhoenixConnectionFactory;
import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.constants.MucDoHaiLongConstants;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.mucdohailong.*;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import nocsystem.indexmanager.services.mucdohailong.LoiViPhamService;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LoiViPhamServiceImpl<T> extends AbstractDao implements LoiViPhamService {

    private final Logger log = LoggerFactory.getLogger(TyLeHaiLongServiceImpl.class);

    @Override
    public PageWithTotalNumber<LoiViPhamDto> getBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request) throws NoSuchFieldException, IllegalAccessException {
        PageWithTotalNumber<LoiViPhamDto> resultPage = null;
        LoiViPhamDto dto = new LoiViPhamDto();
        List<LoiViPhamDto> dtoList = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** List param để tạo dòng Tổng trên bảng bảo cáo **/
        LoiViPhamDto totalRow = null;
        Long tongDGNV = 0L;
        Long tongDGCLPV = 0L;
        Long tongDGCLDV = 0L;
        Long tongDGKN = 0L;
        Long tongDGLICN = 0L;
        Long tongDGKhac = 0L;
        Long tongDGCacNguyenNhan = 0L;
        Long tongDGTDK = 0L;
        Long tongDGGTTA = 0L;
        Long tongDGLoiNghiemTrong = 0L;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlEntity = "VUNG, MA_CHINHANH";
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(request.getNgayBaoCao().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (request.getType()) {
            case "0":
                LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            sqlCondition += "AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
            String listVung = String.join("', '", request.getVung());
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            String listBuuCuc = String.join("', '", request.getMaBuuCuc());
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
            String listChiNhanh = String.join("', '", request.getMaChiNhanh());
            String listBuuCuc = String.join("', '", request.getMaBuuCuc());
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (request.getType()) {
                case "0":
                    LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCuc = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCuc = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (request.getType()) {
                case "0":
                    LocalDate ngayDauThang = request.getNgayBaoCao().withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }

            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((request.getVung() != null && !request.getVung().isEmpty()) &&
                    (Objects.isNull(request.getMaChiNhanh()) || request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (Objects.isNull(request.getMaBuuCuc()) || request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(request.getVung()) || request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCucFilter = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(request.getVung()) && !request.getVung().isEmpty()) &&
                    (!Objects.isNull(request.getMaChiNhanh()) && !request.getMaChiNhanh().isEmpty()) &&
                    (!Objects.isNull(request.getMaBuuCuc()) && !request.getMaBuuCuc().isEmpty())) {
                String listVung = String.join("', '", request.getVung());
                String listChiNhanhFilter = String.join("', '", request.getMaChiNhanh());
                String listBuuCucFilter = String.join("', '", request.getMaBuuCuc());
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_DG_K_HAILONG_NV) AS TONG_DG_K_HAILONG_NV, SUM(TONG_DG_K_HAILONG_CLPV) AS TONG_DG_K_HAILONG_CLPV," +
                "SUM(TONG_DG_K_HAILONG_CLDV) AS TONG_DG_K_HAILONG_CLDV, SUM(TONG_DG_K_HAILONG_KN) AS TONG_DG_K_HAILONG_KN, " +
                "SUM(TONG_DG_K_HAILONG_LICN) AS TONG_DG_K_HAILONG_LICN, SUM(TONG_DG_K_HAILONG_KHAC) AS TONG_DG_K_HAILONG_KHAC, " +
                "SUM(LOI_THAIDO_PHUCVU_KEM) AS LOI_THAIDO_PHUCVU_KEM, SUM(LOI_GACH_TT_AO) AS LOI_GACH_TT_AO, SUM(LOI_TONG) AS LOI_TONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition + " AND (TONG_DANHGIA - TONG_DG_HAILONG) <> 0" +
                "GROUP BY " + sqlEntity;
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new LoiViPhamDto(
                        sqlEntity.contains("VUNG") ? rs.getString("VUNG") : null,
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("TONG_DG_K_HAILONG_NV"),
                        rs.getLong("TONG_DG_K_HAILONG_CLPV"),
                        rs.getLong("TONG_DG_K_HAILONG_CLDV"),
                        rs.getLong("TONG_DG_K_HAILONG_KN"),
                        rs.getLong("TONG_DG_K_HAILONG_LICN"),
                        rs.getLong("TONG_DG_K_HAILONG_KHAC"),
                        rs.getLong("LOI_THAIDO_PHUCVU_KEM"),
                        rs.getLong("LOI_GACH_TT_AO"),
                        rs.getLong("LOI_TONG")
                );
                dtoList.add(dto);
            }
            sortBaoCaoTongHopTable(request.getVung(), request.getMaChiNhanh(), request.getMaBuuCuc(), request.getSort(), request.getSortBy(), dtoList);
            if (dtoList.size() > 0) {
                for (LoiViPhamDto x : dtoList) {
                    tongDGNV += x.getTongDanhGiaNghiepVu();
                    tongDGCLPV += x.getTongDanhGiaCLPV();
                    tongDGCLDV += x.getTongDanhGiaCLDV();
                    tongDGKN += x.getTongDanhGiaKiNang();
                    tongDGLICN += x.getTongDanhGiaLoiIchCN();
                    tongDGKhac += x.getTongDanhGiaKhac();
                    tongDGCacNguyenNhan += x.getTong();
                    tongDGTDK += x.getTongThaiDoKem();
                    tongDGGTTA += x.getTongGachTrangThaiAo();
                    tongDGLoiNghiemTrong += x.getTongLoiNghiemTrong();
                }
                totalRow = new LoiViPhamDto(null, "TONG", null, null, tongDGNV, tongDGCLPV, tongDGCLDV, tongDGKN, tongDGLICN, tongDGKhac, tongDGCacNguyenNhan,
                        tongDGTDK, tongDGGTTA, tongDGLoiNghiemTrong);

            }
            int pageNumber = request.getPage();
            int pageSize = request.getSize();
            if (pageNumber > 0) pageNumber--;
            int start = (int) Math.min(pageNumber * pageSize, dtoList.size());
            int end = (int) Math.min((pageNumber + 1) * pageSize, dtoList.size());

            List<LoiViPhamDto> listTotal = new ArrayList<>(dtoList); // this list to get true size of list when pagination
            List<LoiViPhamDto> sublist = dtoList.subList(start, end);
            if (sublist.size() > 0) {
                sublist.add(totalRow);
            }
            resultPage = new PageWithTotalNumber<LoiViPhamDto>(sublist, PageRequest.of(pageNumber, pageSize), listTotal.size());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return resultPage;
    }

    @Override
    public void exportExcelBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request, HttpServletResponse response) throws IOException, NoSuchFieldException, IllegalAccessException {
        LocalDate ngayBaoCao = request.getNgayBaoCao();
        List<String> vung = request.getVung();
        List<String> maChiNhanh = request.getMaChiNhanh();
        List<String> maBuuCuc = request.getMaBuuCuc();
        String type = request.getType();
        String sort = request.getSort();
        String sortBy = request.getSortBy();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCloiviphamnghiemtrong.xlsx";
        response.setHeader(headerKey, headerValue);

        Map<String, String> mapInfo = new HashMap<>();

        mapInfo.put("stt", "STT");
        mapInfo.put("vung", "Vùng");
        mapInfo.put("maChiNhanh", "Chi nhánh");
        if (!maChiNhanh.isEmpty()) {
            mapInfo.put("maBuuCuc", "Bưu cục");
            if (!maBuuCuc.isEmpty()) {
                mapInfo.put("maTuyen", "Tuyến");
            }
        }
        mapInfo.put("tongDanhGiaNghiepVu", "Nhóm hành vi về nghiệp vụ [1]");
        mapInfo.put("tongDanhGiaCLPV", "Chất lượng phục vụ [2]");
        mapInfo.put("tongDanhGiaCLDV", "Chất lượng dịch vụ [3]");
        mapInfo.put("tongDanhGiaKiNang", "Nhóm hành vi về kỹ năng [4]");
        mapInfo.put("tongDanhGiaLoiIchCN", "Nhóm hành vi lợi ích cá nhân [5]");
        mapInfo.put("tongDanhGiaKhac", "Khác [6]");
        mapInfo.put("tong", "Tổng [7]=[1]+[2]+[3]+[4]+[5]+[6]");
        mapInfo.put("tongThaiDoKem", "Thái độ nhân viên kém [8]");
        mapInfo.put("tongGachTrangThaiAo", " Gạch trạng thái ảo [9]");
        mapInfo.put("tongLoiNghiemTrong", "Tổng lỗi nghiêm trọng [10]=[8]+[9]");

        List<String> firstHeaders = null;
        List<String> secondHeaders = null;

        if (!maChiNhanh.isEmpty()) {
            if (!maBuuCuc.isEmpty()) {
                firstHeaders = Arrays.asList(
                        "STT",
                        "Vùng",
                        "Chi nhánh",
                        "Bưu cục",
                        "Tuyến",

                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",

                        "Lỗi nghiêm trọng",
                        "Lỗi nghiêm trọng",
                        "Lỗi nghiêm trọng"
                );

                secondHeaders = Arrays.asList(
                        "stt",
                        "vung",
                        "maChiNhanh",
                        "maBuuCuc",
                        "maTuyen",

                        "tongDanhGiaNghiepVu",
                        "tongDanhGiaCLPV",
                        "tongDanhGiaCLDV",
                        "tongDanhGiaKiNang",
                        "tongDanhGiaLoiIchCN",
                        "tongDanhGiaKhac",
                        "tong",

                        "tongThaiDoKem",
                        "tongGachTrangThaiAo",
                        "tongLoiNghiemTrong"
                );
            } else {
                firstHeaders = Arrays.asList(
                        "STT",
                        "Vùng",
                        "Chi nhánh",
                        "Bưu cục",

                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",
                        "Cơ cấu nguyên nhân không hài lòng",

                        "Lỗi nghiêm trọng",
                        "Lỗi nghiêm trọng",
                        "Lỗi nghiêm trọng"
                );

                secondHeaders = Arrays.asList(
                        "stt",
                        "vung",
                        "maChiNhanh",
                        "maBuuCuc",

                        "tongDanhGiaNghiepVu",
                        "tongDanhGiaCLPV",
                        "tongDanhGiaCLDV",
                        "tongDanhGiaKiNang",
                        "tongDanhGiaLoiIchCN",
                        "tongDanhGiaKhac",
                        "tong",

                        "tongThaiDoKem",
                        "tongGachTrangThaiAo",
                        "tongLoiNghiemTrong"
                );
            }
        } else {
            firstHeaders = Arrays.asList(
                    "STT",
                    "Vùng",
                    "Chi nhánh",

                    "Cơ cấu nguyên nhân không hài lòng",
                    "Cơ cấu nguyên nhân không hài lòng",
                    "Cơ cấu nguyên nhân không hài lòng",
                    "Cơ cấu nguyên nhân không hài lòng",
                    "Cơ cấu nguyên nhân không hài lòng",
                    "Cơ cấu nguyên nhân không hài lòng",
                    "Cơ cấu nguyên nhân không hài lòng",

                    "Lỗi nghiêm trọng",
                    "Lỗi nghiêm trọng",
                    "Lỗi nghiêm trọng"
            );

            secondHeaders = Arrays.asList(
                    "stt",
                    "vung",
                    "maChiNhanh",

                    "tongDanhGiaNghiepVu",
                    "tongDanhGiaCLPV",
                    "tongDanhGiaCLDV",
                    "tongDanhGiaKiNang",
                    "tongDanhGiaLoiIchCN",
                    "tongDanhGiaKhac",
                    "tong",

                    "tongThaiDoKem",
                    "tongGachTrangThaiAo",
                    "tongLoiNghiemTrong"
            );
        }
        ExportExcelTyLeHaiLongTemplate template = new ExportExcelTyLeHaiLongTemplate(
                getDataTongHopToExport(ngayBaoCao, vung, maChiNhanh, maBuuCuc, type, sort, sortBy),
                mapInfo,
                firstHeaders,
                secondHeaders,
                "BCloiviphamnghiemtrong"
        );
        try {
            template.exportDataToExcel(response);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<TyTrongNguyenNhanKHLDto> getTyTrongNguyenNhanKHL(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) {
        List<TyTrongNguyenNhanKHLDto> response = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map<String, String> sqlCondition = getSqlCondition(null, ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG, SUM(TONG_DG_K_HAILONG_NV) AS TONG_DG_K_HAILONG_NV, " +
                "SUM(TONG_DG_K_HAILONG_CLPV) AS TONG_DG_K_HAILONG_CLPV, SUM(TONG_DG_K_HAILONG_CLDV) AS TONG_DG_K_HAILONG_CLDV, " +
                "SUM(TONG_DG_K_HAILONG_KN) AS TONG_DG_K_HAILONG_KN, SUM(TONG_DG_K_HAILONG_LICN) AS TONG_DG_K_HAILONG_LICN, " +
                "SUM(TONG_DG_K_HAILONG_KHAC) AS TONG_DG_K_HAILONG_KHAC " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition");
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                TyTrongNguyenNhanKHLDto tyTrongNV = new TyTrongNguyenNhanKHLDto(
                        "Nhóm hành vi về nghiệp vụ",
                        rs.getLong("TONG_DG_K_HAILONG_NV"),
                        (rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG")),
                        rs.getLong("TONG_DG_K_HAILONG_NV"));
                TyTrongNguyenNhanKHLDto tyTrongCLPV = new TyTrongNguyenNhanKHLDto(
                        "Chất lượng phục vụ",
                        rs.getLong("TONG_DG_K_HAILONG_CLPV"),
                        (rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG")),
                        rs.getLong("TONG_DG_K_HAILONG_CLPV"));
                TyTrongNguyenNhanKHLDto tyTrongCLDV = new TyTrongNguyenNhanKHLDto(
                        "Chất lượng dịch vụ",
                        rs.getLong("TONG_DG_K_HAILONG_CLDV"),
                        (rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG")),
                        rs.getLong("TONG_DG_K_HAILONG_CLDV"));
                TyTrongNguyenNhanKHLDto tyTrongKN = new TyTrongNguyenNhanKHLDto(
                        "Nhóm hành vi về kĩ năng",
                        rs.getLong("TONG_DG_K_HAILONG_KN"),
                        (rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG")),
                        rs.getLong("TONG_DG_K_HAILONG_KN"));
                TyTrongNguyenNhanKHLDto tyTrongLICN = new TyTrongNguyenNhanKHLDto(
                        "Nhóm hành vi lợi ích cá nhân",
                        rs.getLong("TONG_DG_K_HAILONG_LICN"),
                        (rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG")),
                        rs.getLong("TONG_DG_K_HAILONG_LICN"));
                TyTrongNguyenNhanKHLDto tyTrongKhac = new TyTrongNguyenNhanKHLDto(
                        "Khác",
                        rs.getLong("TONG_DG_K_HAILONG_KHAC"),
                        (rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG")),
                        rs.getLong("TONG_DG_K_HAILONG_KHAC"));
                response.add(tyTrongNV);
                response.add(tyTrongCLPV);
                response.add(tyTrongCLDV);
                response.add(tyTrongKN);
                response.add(tyTrongLICN);
                response.add(tyTrongKhac);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;
    }

    @Override
    public List<TyTrongNguyenNhanKHLCaoNhatDto> getTopTyTrongCaoNhat(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) {
        List<TyTrongNguyenNhanKHLCaoNhatDto> response = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        String sqlCondition = "NGAY_KH_PHANHOI >= " + convertedNgayDauThang + " AND NGAY_KH_PHANHOI <= " + convertedNgayBaoCao + " ";

        if (vung != null && !vung.isEmpty()) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') ", listVung);
        }

        if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') ", listChiNhanh);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_CHINHANH in (%s) ", listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }

        if (maBuuCuc != null && !maBuuCuc.isEmpty()) {
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_BUUCUC IN ('%s') ", listBuuCuc);
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_BUUCUC in (%s) ", listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }

        Long tongDGKhongHL = getTongDanhGiaKhongHaiLongCuaKH(ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);

        String sql = "SELECT NGUYEN_NHAN_SAU_XM, count (*) AS TONG_DG_KHONG_HAI_LONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_CHITIET WHERE " + sqlCondition +
                "AND ((rate in ('1','2','3') and trang_thai_xu_ly in ('Chốt', 'Chốt_Yêu cầu xác minh sai')) " +
                "or (rate in ('4','5') and trang_thai_xu_ly = 'Chốt') " +
                "or (ketqua_danhgia = 'Thông tin không đúng' and nguyen_nhan_sau_xm = 'Thái độ nhân viên kém' and trang_thai_xu_ly in ('Chốt', 'Chốt_Yêu cầu xác minh sai'))) " +
                "AND NGUYEN_NHAN_SAU_XM IS NOT NULL " +
                "GROUP BY NGUYEN_NHAN_SAU_XM " +
                "ORDER BY TONG_DG_KHONG_HAI_LONG DESC " +
                "LIMIT 5";
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                TyTrongNguyenNhanKHLCaoNhatDto tyTrong = new TyTrongNguyenNhanKHLCaoNhatDto(rs.getString("NGUYEN_NHAN_SAU_XM"),
                        rs.getLong("TONG_DG_KHONG_HAI_LONG"),
                        tongDGKhongHL,
                        rs.getLong("TONG_DG_KHONG_HAI_LONG"));
                response.add(tyTrong);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        response = getTyTrongCaoNhatThangTruoc(response, ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        return response;
    }

    @Override
    public List<TopViPhamNhieuNhatDto> getTopViPhamNhieuNhat(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type) {
        List<TopViPhamNhieuNhatDto> topLuyKe = new ArrayList<>();
        List<TopViPhamNhieuNhatDto> topTrongNgay = new ArrayList<>();
        List<TopViPhamNhieuNhatDto> topLapLai = new ArrayList<>();

        topLuyKe = getTopViPhamNhieuNhatLuyKe(ngayBaoCao, vung, maChiNhanh, maBuuCuc, type);
        topTrongNgay = getTopViPhamNhieuNhatTrongNgay(ngayBaoCao, vung, maChiNhanh, maBuuCuc, type);
        topLapLai = getTopViPhamNhieuNhatLapLai(ngayBaoCao, vung, maChiNhanh, maBuuCuc, type);
        Map<String, TopViPhamNhieuNhatDto> map = convertListTopViPhamToMap(topLuyKe, topTrongNgay, topLapLai);
        List<TopViPhamNhieuNhatDto> convertedList = new ArrayList<>(map.values());
        convertedList.sort(Comparator.comparing(TopViPhamNhieuNhatDto::getTongDGLoiTrongNgay, Comparator.nullsLast(Comparator.reverseOrder())));
        return convertedList.subList(0, Math.min(10, convertedList.size()));
    }

    private Map<String, TopViPhamNhieuNhatDto> convertListTopViPhamToMap(List<TopViPhamNhieuNhatDto> topLuyKe, List<TopViPhamNhieuNhatDto> topTrongNgay, List<TopViPhamNhieuNhatDto> topLapLai) {
        Map<String, TopViPhamNhieuNhatDto> mapLuyKe = new HashMap<>();
        Map<String, TopViPhamNhieuNhatDto> mapLapLai = new HashMap<>();
        Map<String, TopViPhamNhieuNhatDto> mapResponse = new HashMap<>();

        if (topLuyKe != null && topLuyKe.size() > 0) {
            for (TopViPhamNhieuNhatDto luyKe : topLuyKe) {
                String key = luyKe.getMaChiNhanh() + luyKe.getMaBuuCuc() + luyKe.getMaTuyen();
                mapLuyKe.put(key, luyKe);
            }
        }

        if (topTrongNgay != null && topTrongNgay.size() > 0) {
            for (TopViPhamNhieuNhatDto trongNgay : topTrongNgay) {
                String key = trongNgay.getMaChiNhanh() + trongNgay.getMaBuuCuc() + trongNgay.getMaTuyen();
                if (mapLuyKe.containsKey(key)) {
                    mapLuyKe.get(key).setTongThaiDoKemTrongNgay(trongNgay.getTongThaiDoKemTrongNgay());
                    mapLuyKe.get(key).setTongGachTTAoTrongNgay(trongNgay.getTongGachTTAoTrongNgay());
                    mapLuyKe.get(key).setTongDGLoiTrongNgay(trongNgay.getTongDGLoiTrongNgay());
                    mapLuyKe.get(key).setTongThaiDoKemLapLai(sumTwoLongNum(mapLuyKe.get(key).getTongThaiDoKemLk(), trongNgay.getTongThaiDoKemTrongNgay()));
                    mapLuyKe.get(key).setTongGachTTAoLapLai(sumTwoLongNum(mapLuyKe.get(key).getTongGachTTAoLk(), trongNgay.getTongGachTTAoTrongNgay()));
                    mapLuyKe.get(key).setTongDGLoiLapLai(sumTwoLongNum(mapLuyKe.get(key).getTongDGLoiLk(), trongNgay.getTongDGLoiTrongNgay()));
                } else {
                    mapLuyKe.put(key, new TopViPhamNhieuNhatDto(trongNgay.getMaChiNhanh(), trongNgay.getMaBuuCuc(), trongNgay.getMaTuyen(),
                            null, null, null,
                            trongNgay.getTongThaiDoKemTrongNgay(), trongNgay.getTongGachTTAoTrongNgay(), trongNgay.getTongDGLoiTrongNgay(),
                            trongNgay.getTongThaiDoKemTrongNgay(), trongNgay.getTongGachTTAoTrongNgay(), trongNgay.getTongDGLoiTrongNgay()));
                }
            }
        }

        if (topLapLai != null && topLapLai.size() > 0) {
            for (TopViPhamNhieuNhatDto lapLai : topLapLai) {
                String key = lapLai.getMaChiNhanh() + lapLai.getMaBuuCuc() + lapLai.getMaTuyen();
                if (mapLuyKe.containsKey(key)) {
                    mapLuyKe.get(key).setTongThaiDoKemLapLai(lapLai.getTongThaiDoKemLapLai());
                    mapLuyKe.get(key).setTongGachTTAoLapLai(lapLai.getTongGachTTAoLapLai());
                    mapLuyKe.get(key).setTongDGLoiLapLai(lapLai.getTongDGLoiLapLai());
                }
            }
        }

        return mapLuyKe;
    }

    private List<Map<String, Object>> getDataTongHopToExport(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type,
                                                             String sort, String sortBy) {
        LoiViPhamExcelDto dto = new LoiViPhamExcelDto();
        List<LoiViPhamExcelDto> dtoList = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** List param để tạo dòng Tổng trên bảng bảo cáo **/
        LoiViPhamExcelDto totalRow = null;
        Long tongDGNV = 0L;
        Long tongDGCLPV = 0L;
        Long tongDGCLDV = 0L;
        Long tongDGKN = 0L;
        Long tongDGLICN = 0L;
        Long tongDGKhac = 0L;
        Long tongDGCacNguyenNhan = 0L;
        Long tongDGTDK = 0L;
        Long tongDGGTTA = 0L;
        Long tongDGLoiNghiemTrong = 0L;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        String sqlEntity = "VUNG, MA_CHINHANH";
        String sqlCondition = "";

        /** type = '0' - lũy kế tháng, type = '1' - lũy kế ngày **/
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        switch (type) {
            case "0":
                LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                break;
            case "1":
                sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                break;
        }

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            sqlCondition += "AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("VUNG, MA_CHINHANH");
            sqlCondition = new String("");
            switch (type) {
                case "0":
                    LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
                    int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                    sqlCondition += "NGAY_BAOCAO >= " + convertedNgayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";
                    break;
                case "1":
                    sqlCondition += "NGAY_BAOCAO = " + convertedNgayBaoCao + " ";
                    break;
            }

            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        String sql = "SELECT " + sqlEntity + ", SUM(TONG_DG_K_HAILONG_NV) AS TONG_DG_K_HAILONG_NV, SUM(TONG_DG_K_HAILONG_CLPV) AS TONG_DG_K_HAILONG_CLPV," +
                "SUM(TONG_DG_K_HAILONG_CLDV) AS TONG_DG_K_HAILONG_CLDV, SUM(TONG_DG_K_HAILONG_KN) AS TONG_DG_K_HAILONG_KN, " +
                "SUM(TONG_DG_K_HAILONG_LICN) AS TONG_DG_K_HAILONG_LICN, SUM(TONG_DG_K_HAILONG_KHAC) AS TONG_DG_K_HAILONG_KHAC, " +
                "SUM(LOI_THAIDO_PHUCVU_KEM) AS LOI_THAIDO_PHUCVU_KEM, SUM(LOI_GACH_TT_AO) AS LOI_GACH_TT_AO, SUM(LOI_TONG) AS LOI_TONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition + " AND (TONG_DANHGIA - TONG_DG_HAILONG) <> 0" +
                "GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                dto = new LoiViPhamExcelDto(
                        sqlEntity.contains("VUNG") ? rs.getString("VUNG") : null,
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("TONG_DG_K_HAILONG_NV"),
                        rs.getLong("TONG_DG_K_HAILONG_CLPV"),
                        rs.getLong("TONG_DG_K_HAILONG_CLDV"),
                        rs.getLong("TONG_DG_K_HAILONG_KN"),
                        rs.getLong("TONG_DG_K_HAILONG_LICN"),
                        rs.getLong("TONG_DG_K_HAILONG_KHAC"),
                        rs.getLong("LOI_THAIDO_PHUCVU_KEM"),
                        rs.getLong("LOI_GACH_TT_AO"),
                        rs.getLong("LOI_TONG")
                );
                dtoList.add(dto);
            }
            sortBaoCaoTongHopExcel(vung, maChiNhanh, maBuuCuc, sort, sortBy, dtoList);
            if (dtoList.size() > 0) {
                for (LoiViPhamExcelDto x : dtoList) {
                    tongDGNV += x.getTongDanhGiaNghiepVu();
                    tongDGCLPV += x.getTongDanhGiaCLPV();
                    tongDGCLDV += x.getTongDanhGiaCLDV();
                    tongDGKN += x.getTongDanhGiaKiNang();
                    tongDGLICN += x.getTongDanhGiaLoiIchCN();
                    tongDGKhac += x.getTongDanhGiaKhac();
                    tongDGCacNguyenNhan += x.getTong();
                    tongDGTDK += x.getTongThaiDoKem();
                    tongDGGTTA += x.getTongGachTrangThaiAo();
                    tongDGLoiNghiemTrong += x.getTongLoiNghiemTrong();
                }
                totalRow = new LoiViPhamExcelDto(null, "TONG", null, null, tongDGNV, tongDGCLPV, tongDGCLDV, tongDGKN, tongDGLICN, tongDGKhac, tongDGCacNguyenNhan,
                        tongDGTDK, tongDGGTTA, tongDGLoiNghiemTrong);

                dtoList.add(totalRow);
                return convertListToListHashMap((List<T>) dtoList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return null;
    }

    private List<Map<String, Object>> convertListToListHashMap(List<T> dataList) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!dataList.isEmpty()) {
            int i = 0;
            for (T data : dataList) {
                LoiViPhamExcelDto x = (LoiViPhamExcelDto) data;
                totalRows.add(x.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    private Long getTongDanhGiaKhongHaiLongCuaKH(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Long tongDGKhongHaiLong = 0L;

        Map<String, String> sqlCondition = getSqlCondition(null, ngayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);
        String sql = "SELECT SUM(TONG_DANHGIA) AS TONG_DG, SUM(TONG_DG_HAILONG) AS TONG_DG_HAILONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition");
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL query mau so{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                tongDGKhongHaiLong = rs.getLong("TONG_DG") - rs.getLong("TONG_DG_HAILONG");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return tongDGKhongHaiLong;
    }

    private List<TyTrongNguyenNhanKHLCaoNhatDto> getTyTrongCaoNhatThangTruoc(List<TyTrongNguyenNhanKHLCaoNhatDto> response,
                                                                             LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) {

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        LocalDate cungKyThangTruoc = MucDoHaiLongUtils.getSamePeriodPreviousMonth(ngayBaoCao);
        LocalDate ngayDauThang = cungKyThangTruoc.withDayOfMonth(1);

        int convertedCungKy = Integer.parseInt(cungKyThangTruoc.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        String sqlCondition = "NGAY_KH_PHANHOI >= " + convertedNgayDauThang + " AND NGAY_KH_PHANHOI <= " + convertedCungKy + " ";
        if (vung != null && !vung.isEmpty()) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') ", listVung);
        }

        if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') ", listChiNhanh);
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_CHINHANH in (%s) ", listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }

        if (maBuuCuc != null && !maBuuCuc.isEmpty()) {
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_BUUCUC IN ('%s') ", listBuuCuc);
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition += String.format("AND MA_BUUCUC in (%s) ", listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", ")));
        }

        Long tongDGKhongHLThangTruoc = getTongDanhGiaKhongHaiLongCuaKH(cungKyThangTruoc, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO);

        List<String> nguyenNhanList = new ArrayList<>();
        for (TyTrongNguyenNhanKHLCaoNhatDto x : response) {
            nguyenNhanList.add(x.getName());
        }
        if (nguyenNhanList != null && !nguyenNhanList.isEmpty()) {
            String nguyenNhan = String.join("', '", nguyenNhanList);
            sqlCondition += String.format(" AND NGUYEN_NHAN_SAU_XM IN ('%s') ", nguyenNhan);
        }

        String sql = "SELECT NGUYEN_NHAN_SAU_XM, count (NGUYEN_NHAN_SAU_XM) AS TONG_DG_KHONG_HAI_LONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_CHITIET WHERE " + sqlCondition +
                "AND ((rate in ('1','2','3') and trang_thai_xu_ly in ('Chốt', 'Chốt_Yêu cầu xác minh sai')) " +
                "or (rate in ('4','5') and trang_thai_xu_ly = 'Chốt') " +
                "or (ketqua_danhgia = 'Thông tin không đúng' and nguyen_nhan_sau_xm = 'Thái độ nhân viên kém' and trang_thai_xu_ly in ('Chốt', 'Chốt_Yêu cầu xác minh sai'))) " +
                "GROUP BY NGUYEN_NHAN_SAU_XM ORDER BY TONG_DG_KHONG_HAI_LONG DESC";

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                for (TyTrongNguyenNhanKHLCaoNhatDto x : response) {
                    if (rs.getString("NGUYEN_NHAN_SAU_XM").equals(x.getName())) {
                        x.setTyTrongThangTruoc(MucDoHaiLongUtils.getTyLeHaiLong(tongDGKhongHLThangTruoc, rs.getLong("TONG_DG_KHONG_HAI_LONG")));
                        x.setTongDGKhongHLThangTruoc(rs.getLong("TONG_DG_KHONG_HAI_LONG"));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return response;

    }

    private List<TopViPhamNhieuNhatDto> getTopViPhamNhieuNhatLuyKe(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type) {
        List<TopViPhamNhieuNhatDto> topLuyKe = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        LocalDate ngayTruoc = ngayBaoCao.minusDays(1);
        int convertedNgayTruoc = Integer.parseInt(ngayTruoc.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));


        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        String sqlEntity = "";
        Map<String, String> sqlCondition = getChiNhanhSqlCondition(sqlEntity, convertedNgayDauThang, convertedNgayTruoc, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO, type);
        sqlEntity = new String(sqlCondition.get("sqlEntity"));

        String sql = "SELECT " + sqlEntity + ", SUM(LOI_THAIDO_PHUCVU_KEM) AS LOI_THAIDO_PHUCVU_KEM, SUM(LOI_GACH_TT_AO) AS LOI_GACH_TT_AO, SUM(LOI_TONG) AS LOI_TONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                " GROUP BY " + sqlEntity;
        log.error("-----------------------------SQL getTopViPhamNhieuNhatLuyKe{}: " + sql);
        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL getTopViPhamNhieuNhatLuyKe{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                TopViPhamNhieuNhatDto dto = new TopViPhamNhieuNhatDto(
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        rs.getLong("LOI_THAIDO_PHUCVU_KEM"),
                        rs.getLong("LOI_GACH_TT_AO"),
                        rs.getLong("LOI_TONG"),
                        null,
                        null,
                        null,
                        null,
                        null,
                        null
                );
                topLuyKe.add(dto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return topLuyKe;
    }

    private List<TopViPhamNhieuNhatDto> getTopViPhamNhieuNhatTrongNgay(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type) {
        List<TopViPhamNhieuNhatDto> topTrongNgay = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        String sqlEntity = "";
        Map<String, String> sqlCondition = getChiNhanhSqlCondition(sqlEntity, convertedNgayBaoCao, convertedNgayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO, type);
        sqlEntity = sqlCondition.get("sqlEntity");

        String sql = "SELECT " + sqlEntity + ", SUM(LOI_THAIDO_PHUCVU_KEM) AS LOI_THAIDO_PHUCVU_KEM, SUM(LOI_GACH_TT_AO) AS LOI_GACH_TT_AO, SUM(LOI_TONG) AS LOI_TONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                " GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL getTopViPhamNhieuNhatTrongNgay{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                TopViPhamNhieuNhatDto dto = new TopViPhamNhieuNhatDto(
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        null,
                        null,
                        null,
                        rs.getLong("LOI_THAIDO_PHUCVU_KEM"),
                        rs.getLong("LOI_GACH_TT_AO"),
                        rs.getLong("LOI_TONG"),
                        null,
                        null,
                        null
                );
                topTrongNgay.add(dto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return topTrongNgay;
    }

    private List<TopViPhamNhieuNhatDto> getTopViPhamNhieuNhatLapLai(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type) {
        List<TopViPhamNhieuNhatDto> topLapLai = new ArrayList<>();
        List<String> listBuuCucSSO = null;
        List<String> listChiNhanhSSO = null;
        /** nhớ check phân quyền **/
        if (!(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleTctTtvhTtdvkh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true"))) {
            return null;
        } else {
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvChiNhanh().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = new ArrayList<>();
                System.out.println("listChiNhanhSSO: " + listChiNhanhSSO);
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true") ||
                    UserContext.getUserData().getIsRoleCbnvBuuCuc().equalsIgnoreCase("true")) {
                listChiNhanhSSO = UserContext.getUserData().getListTinhUserPhuTrach();
                listBuuCucSSO = UserContext.getUserData().getListBuuCucUserPhuTrach();
                System.out.println("listBuuCucSSO: " + listBuuCucSSO);
            }
        }

        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int convertedNgayDauThang = Integer.parseInt(ngayDauThang.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        String sqlEntity = "";
        Map<String, String> sqlCondition = getChiNhanhSqlCondition(sqlEntity, convertedNgayDauThang, convertedNgayBaoCao, vung, maChiNhanh, maBuuCuc, listChiNhanhSSO, listBuuCucSSO, type);
        sqlEntity = sqlCondition.get("sqlEntity");

        String sql = "SELECT " + sqlEntity + ", SUM(LOI_THAIDO_PHUCVU_KEM) AS LOI_THAIDO_PHUCVU_KEM, SUM(LOI_GACH_TT_AO) AS LOI_GACH_TT_AO, SUM(LOI_TONG) AS LOI_TONG " +
                "FROM MUCDO_HAILONG_KHACHHANG_TONGHOP WHERE " + sqlCondition.get("sqlCondition") +
                " GROUP BY " + sqlEntity;

        try {
            conn = PhoenixConnectionFactory.getInstance().getPhoenixConnection();
            stmt = conn.prepareStatement(sql);
            System.out.println("-----------------------------SQL getTopViPhamNhieuNhatLapLai{}: " + stmt.toString());
            rs = stmt.executeQuery();
            while (rs.next()) {
                TopViPhamNhieuNhatDto dto = new TopViPhamNhieuNhatDto(
                        sqlEntity.contains("MA_CHINHANH") ? rs.getString("MA_CHINHANH") : null,
                        sqlEntity.contains("MA_BUUCUC") ? rs.getString("MA_BUUCUC") : null,
                        sqlEntity.contains("TUYEN_BUUTA") ? rs.getString("TUYEN_BUUTA") : null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        rs.getLong("LOI_THAIDO_PHUCVU_KEM"),
                        rs.getLong("LOI_GACH_TT_AO"),
                        rs.getLong("LOI_TONG")
                );
                topLapLai.add(dto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Error when getSatisfactionRate in PhoenixDaoImpl, reason: {}", e.getMessage());
            throw new RuntimeException(e);
        } finally {
            releaseConnect(conn, stmt, rs);
        }
        return topLapLai;
    }

    private Map<String, String> getSqlCondition(String sqlEntity, LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, List<String> listChiNhanhSSO, List<String> listBuuCucSSO) {
        Map<String, String> result = new HashMap<>();
        String sqlCondition = "";
        int convertedNgayBaoCao = Integer.parseInt(ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        int ngayDauThang = Integer.parseInt(ngayBaoCao.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        sqlCondition = "NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ";

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            sqlCondition += "AND VUNG = 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            sqlCondition += String.format("AND VUNG IN ('%s') AND VUNG <> 'ALL' AND MA_CHINHANH = 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
            sqlEntity += ", MA_BUUCUC";

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
            sqlEntity += ", MA_BUUCUC";
        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC";
        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
            sqlEntity += ", MA_BUUCUC";
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
            }
        }

        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlCondition = new String("NGAY_BAOCAO >= " + ngayDauThang + " AND NGAY_BAOCAO <= " + convertedNgayBaoCao + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s)AND TUYEN_BUUTA = 'ALL' ",
                        listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
            }
//            sqlCondition = String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
        }

        result.put("sqlCondition", sqlCondition);
        result.put("sqlEntity", sqlEntity);
        return result;
    }

    private Map<String, String> getChiNhanhSqlCondition(String sqlEntity, int startDate, int endDate, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc,
                                                        List<String> listChiNhanhSSO, List<String> listBuuCucSSO, String type) {
        Map<String, String> result = new HashMap<>();
        String sqlCondition = "";
        sqlCondition += "NGAY_BAOCAO >= " + startDate + " AND NGAY_BAOCAO <= " + endDate + " ";

        if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            switch (type) {
                case "chiNhanh":
                    if (!sqlEntity.contains("MA_CHINHANH")) {
                        sqlEntity += "MA_CHINHANH";
                        sqlCondition +=  " AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ";
                    }
                    break;
                case "buuCuc":
                    if (!sqlEntity.contains("MA_BUUCUC")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                        sqlCondition += " AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ";
                    }
                    break;
                case "tuyen":
                    if (!sqlEntity.contains("TUYEN_BUUTA")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                        sqlCondition += " AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ";
                    }
                    break;
            }

        } else if ((vung != null && !vung.isEmpty()) &&
                (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            switch (type) {
                case "chiNhanh":
                    if (!sqlEntity.contains("MA_CHINHANH")) {
                        sqlEntity += "MA_CHINHANH";
                        sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);
                    }
                    break;
                case "buuCuc":
                    if (!sqlEntity.contains("MA_BUUCUC")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                        sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung);
                    }
                    break;
                case "tuyen":
                    if (!sqlEntity.contains("TUYEN_BUUTA")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                        sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ", listVung);
                    }
                    break;
            }

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            switch (type) {
                case "chiNhanh":
                    if (!sqlEntity.contains("MA_CHINHANH")) {
                        sqlEntity += "MA_CHINHANH";
                        sqlCondition +=  String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
                    }
                    break;
                case "buuCuc":
                    if (!sqlEntity.contains("MA_BUUCUC")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                        sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
                    }
                    break;
                case "tuyen":
                    if (!sqlEntity.contains("TUYEN_BUUTA")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                        sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh);
                    }
                    break;
            }

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            switch (type) {
                case "chiNhanh":
                    if (!sqlEntity.contains("MA_CHINHANH")) {
                        sqlEntity += "MA_CHINHANH";
                        sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
                    }
                    break;
                case "buuCuc":
                    if (!sqlEntity.contains("MA_BUUCUC")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                        sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);
                    }
                    break;
                case "tuyen":
                    if (!sqlEntity.contains("TUYEN_BUUTA")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                        sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh);
                    }
                    break;
            }

        } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listVung = String.join("', '", vung);
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            switch (type) {
                case "chiNhanh":
                    if (!sqlEntity.contains("MA_CHINHANH")) {
                        sqlEntity += "MA_CHINHANH";
                        sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL'", listVung, listChiNhanh, listBuuCuc);
                    }
                    break;
                case "buuCuc":
                    if (!sqlEntity.contains("MA_BUUCUC")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                        sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL'", listVung, listChiNhanh, listBuuCuc);
                    }
                    break;
                case "tuyen":
                    if (!sqlEntity.contains("TUYEN_BUUTA")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                        sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
                    }
                    break;
            }

        } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
            String listChiNhanh = String.join("', '", maChiNhanh);
            String listBuuCuc = String.join("', '", maBuuCuc);
            switch (type) {
                case "chiNhanh":
                    if (!sqlEntity.contains("MA_CHINHANH")) {
                        sqlEntity += "MA_CHINHANH";
                        sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL'", listChiNhanh, listBuuCuc);
                    }
                    break;
                case "buuCuc":
                    if (!sqlEntity.contains("MA_BUUCUC")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                        sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL'", listChiNhanh, listBuuCuc);
                    }
                    break;
                case "tuyen":
                    if (!sqlEntity.contains("TUYEN_BUUTA")) {
                        sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                        sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
                    }
                    break;
            }
        }

        if (listChiNhanhSSO != null && !listChiNhanhSSO.isEmpty()) {
            sqlEntity = new String("");
            sqlCondition = new String("NGAY_BAOCAO >= " + startDate + " AND NGAY_BAOCAO <= " + endDate + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format(" AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format(" AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format(" AND VUNG <> 'ALL' AND MA_CHINHANH <> 'ALL' AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh);
                        }
                        break;
                }

//                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format(" AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ",
                                    listVung, listChiNhanh);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format(" AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ",
                                    listVung, listChiNhanh);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format(" AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_CHINHANH <> 'ALL' AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ",
                                    listVung, listChiNhanh);
                        }
                        break;
                }
//                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ",
                                    listVung, listChiNhanhFilter, listChiNhanh);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ",
                                    listVung, listChiNhanhFilter, listChiNhanh);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ",
                                    listVung, listChiNhanhFilter, listChiNhanh);
                        }
                        break;
                }
//                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh);
//                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh);
                        }
                        break;
                }
//                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC = 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh);
//                sqlEntity += ", MA_BUUCUC";

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL'", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL'", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                }
//                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
//                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCuc = String.join("', '", maBuuCuc);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                }
//                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
//                sqlEntity += ", MA_BUUCUC, TUYEN_BUUTA";
            }
        }

        /** Role Lanh dao Buu cuc **/
        if (listBuuCucSSO != null && !listBuuCucSSO.isEmpty()) {
            sqlEntity = new String("");
            sqlCondition = new String("NGAY_BAOCAO >= " + startDate + " AND NGAY_BAOCAO <= " + endDate + " ");
            String listChiNhanh = listChiNhanhSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));
            String listBuuCuc = listBuuCucSSO.stream()
                    .map(giaTri -> "'" + giaTri + "'")
                    .collect(Collectors.joining(", "));

            if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format(" AND VUNG <> 'ALL' AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format(" AND VUNG <> 'ALL' AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format(" AND VUNG <> 'ALL' AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND MA_BUUCUC <> 'ALL' AND TUYEN_BUUTA <> 'ALL' ", listChiNhanh, listBuuCuc);
                        }
                        break;
                }
//                sqlCondition += String.format("AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanh, listBuuCuc);

            } else if ((vung != null && !vung.isEmpty()) &&
                    (Objects.isNull(maChiNhanh) || maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanh, listBuuCuc);
                        }
                        break;
                }
//                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanh, listBuuCuc);

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                }
//                sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (Objects.isNull(maBuuCuc) || maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC <> 'ALL' AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                }
//                sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);

            } else if ((Objects.isNull(vung) || vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC, TUYEN_BUUTA";
                            sqlCondition += String.format("AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ", listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                        }
                        break;
                }

            } else if ((!Objects.isNull(vung) && !vung.isEmpty()) &&
                    (!Objects.isNull(maChiNhanh) && !maChiNhanh.isEmpty()) &&
                    (!Objects.isNull(maBuuCuc) && !maBuuCuc.isEmpty())) {
                String listVung = String.join("', '", vung);
                String listChiNhanhFilter = String.join("', '", maChiNhanh);
                String listBuuCucFilter = String.join("', '", maBuuCuc);
                switch (type) {
                    case "chiNhanh":
                        if (!sqlEntity.contains("MA_CHINHANH")) {
                            sqlEntity += "MA_CHINHANH";
                            sqlCondition +=  String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ",
                                    listVung, listChiNhanhFilter, listChiNhanh, listBuuCuc);
                        }
                        break;
                    case "buuCuc":
                        if (!sqlEntity.contains("MA_BUUCUC")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA = 'ALL' ",
                                    listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                        }
                        break;
                    case "tuyen":
                        if (!sqlEntity.contains("TUYEN_BUUTA")) {
                            sqlEntity += "MA_CHINHANH, MA_BUUCUC , TUYEN_BUUTA";
                            sqlCondition += String.format("AND VUNG IN ('%s') AND MA_CHINHANH IN ('%s') AND MA_CHINHANH IN (%s) AND MA_BUUCUC IN ('%s') AND MA_BUUCUC IN (%s) AND TUYEN_BUUTA <> 'ALL' ",
                                    listVung, listChiNhanhFilter, listChiNhanh, listBuuCucFilter, listBuuCuc);
                        }
                        break;
                }
            }
        }

        result.put("sqlCondition", sqlCondition);
        result.put("sqlEntity", sqlEntity);
        return result;
    }

    private Long sumTwoLongNum(Long so1, Long so2) {
        Long result = null;
        if (so1 == null && so2 == null) return null;
        if (so1 == null) {
            so1 = 0L;
        }
        if (so2 == null) {
            so2 = 0L;
        }

        result = so1 + so2;

        return result;
    }

    private List<LoiViPhamDto> sortBaoCaoTongHopTable(List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String sort, String sortBy, List<LoiViPhamDto> response) {
        if (!response.isEmpty() || !Objects.isNull(response)) {
            if (sort == null || sort == null) {
                if (vung.isEmpty()) {
                    response.sort(Comparator.comparing(LoiViPhamDto::getVung));
                } else {
                    if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                        response.sort(Comparator.comparing(LoiViPhamDto::getMaChiNhanh));
                    } else {
                        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
                            response.sort(Comparator.comparing(LoiViPhamDto::getMaBuuCuc));
                        } else {
                            response.sort(Comparator.comparing(LoiViPhamDto::getMaTuyen));
                        }
                    }
                }
            } else {
                switch (sortBy) {
                    case MucDoHaiLongConstants.NHOM_HANH_VI_NGHIEP_VU:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaNghiepVu, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaNghiepVu, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.CHAT_LUONG_PHUC_VU:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaCLPV, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaCLPV, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.CHAT_LUONG_DICH_VU:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaCLDV, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaCLDV, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.NHOM_HANH_VI_KI_NANG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaKiNang, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaKiNang, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.NHOM_HANH_VI_LOI_ICH_CA_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaLoiIchCN, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaLoiIchCN, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KHAC:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaKhac, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongDanhGiaKhac, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_CAC_NGUYEN_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.THAI_DO_KEM:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongThaiDoKem, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongThaiDoKem, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.GACH_TT_AO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongGachTrangThaiAo, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongGachTrangThaiAo, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_LOI_NGHIEM_TRONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongLoiNghiemTrong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamDto::getTongLoiNghiemTrong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                }
            }
        }
        return response;
    }

    private List<LoiViPhamExcelDto> sortBaoCaoTongHopExcel(List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String sort, String sortBy, List<LoiViPhamExcelDto> response) {
        if (!response.isEmpty() || !Objects.isNull(response)) {
            if (sort == null || sort == null) {
                if (vung.isEmpty()) {
                    response.sort(Comparator.comparing(LoiViPhamExcelDto::getVung));
                } else {
                    if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                        response.sort(Comparator.comparing(LoiViPhamExcelDto::getMaChiNhanh));
                    } else {
                        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
                            response.sort(Comparator.comparing(LoiViPhamExcelDto::getMaBuuCuc));
                        } else {
                            response.sort(Comparator.comparing(LoiViPhamExcelDto::getMaTuyen));
                        }
                    }
                }
            } else {
                switch (sortBy) {
                    case MucDoHaiLongConstants.NHOM_HANH_VI_NGHIEP_VU:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaNghiepVu, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaNghiepVu, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.CHAT_LUONG_PHUC_VU:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaCLPV, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaCLPV, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.CHAT_LUONG_DICH_VU:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaCLDV, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaCLDV, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.NHOM_HANH_VI_KI_NANG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaKiNang, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaKiNang, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.NHOM_HANH_VI_LOI_ICH_CA_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaLoiIchCN, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaLoiIchCN, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.KHAC:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaKhac, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongDanhGiaKhac, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_CAC_NGUYEN_NHAN:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.THAI_DO_KEM:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongThaiDoKem, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongThaiDoKem, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.GACH_TT_AO:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongGachTrangThaiAo, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongGachTrangThaiAo, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case MucDoHaiLongConstants.TONG_LOI_NGHIEM_TRONG:
                        switch (sort) {
                            case MucDoHaiLongConstants.ASCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongLoiNghiemTrong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case MucDoHaiLongConstants.DESCENDING_ORDER:
                                response.sort(Comparator.comparing(LoiViPhamExcelDto::getTongLoiNghiemTrong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                }
            }
        }
        return response;
    }
}
