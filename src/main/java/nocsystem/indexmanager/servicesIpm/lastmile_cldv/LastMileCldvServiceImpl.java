package nocsystem.indexmanager.servicesIpm.lastmile_cldv;

import nocsystem.indexmanager.common.DateTimeUtil;
import nocsystem.indexmanager.controllers.lastmile_cldv.*;
import nocsystem.indexmanager.controllers.lastmile_cldv.LastmileCronDto;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.repositories.lastmile_cron.LastmileRepoNgayPostgres;
import nocsystem.indexmanager.repositories.lastmile_cron.LastmileRepoThangPostgres;
import nocsystem.indexmanager.services.lastmile_cldv.LastMileCronJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class LastMileCldvServiceImpl extends AbstractDao implements LastMileCronJob {

    @Autowired
    LastmileRepoNgayPostgres lastmileRepoNgayPostgres;

    @Autowired
    LastmileRepoThangPostgres lastmileRepoThangPostgres;

    @Override
    public LastmileCronDto getDatashow(LasmileBody body) {
        Date ngay = DateTimeUtil.stringToSqlDate(body.getNgayBaoCao());
        String maCn = body.chiNhanh;
        String maBc = body.buuCuc;
        if (maCn == null || maCn.isEmpty()) {
            maCn = "ALL";
            maBc = "ALL";
        } else {
            if (maBc == null || maBc.isEmpty()) {
                maBc = "ALL";
            }
        }
        if (body.luyKe == null || body.luyKe == 0) {
            return lastmileRepoNgayPostgres.getDataLmFromFilter(ngay, maCn, maBc);
        } else return lastmileRepoThangPostgres.getDataLmFromFilter(ngay, maCn, maBc);
    }

    public String getNgayDauThang(String input) {
        if (input == null || input.isEmpty()) return null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate ngay = LocalDate.parse(input, formatter);
        LocalDate ngayDauThang = ngay.withDayOfMonth(1);
        return ngayDauThang.format(formatter);
    }

    public String trimDay(String date) {
        if (date == null) return "3035-08-10"; // gia lap 1 ngay nao do con lau trong tuong lai
        return date.substring(0, 8);
    }

    public int getDaysInMonth(String dateString) {
        if (dateString == null || dateString.isEmpty()) return 0;
        String lastTwoChars = dateString.substring(dateString.length() - 2);
        return Integer.parseInt(lastTwoChars.startsWith("0") ? lastTwoChars.substring(1) : lastTwoChars);
    }


    public List<LastmileChartCronDto> getListRepsonseChart(List<LastmileChartCronDto> list, String ngayKpi) {
        if (list == null || list.isEmpty()) return new ArrayList<>();
        HashMap<String, LastmileChartCronDto> dict = new HashMap<>();
        int soNgay = getDaysInMonth(ngayKpi);

        for (int i = 1; i <= soNgay; i++) {
            String ngayTmp = trimDay(ngayKpi);
            LastmileChartCronDto dto = new LastmileChartCronDto();
            if (i < 10) {
                ngayTmp += "0" + i;
                dto.setNgayBaoCao(ngayTmp);
            } else {
                ngayTmp += i;
                dto.setNgayBaoCao(ngayTmp);
            }
            dict.put(ngayTmp, dto);
        }
        for (LastmileChartCronDto o : list) {
            String key = o.ngayBaoCao;
            if (dict.containsKey(key)) {
                dict.put(key, o);
            }
        }
        List<LastmileChartCronDto> listTmp = new ArrayList<>();
        for(int i = 1; i <= soNgay; i++){
            String ngayTmp = trimDay(ngayKpi);
            if (i < 10) {
                ngayTmp += "0" + i;
               if(dict.containsKey(ngayTmp)){
                   listTmp.add(dict.get(ngayTmp));
               }
            } else {
                ngayTmp += i;
                if(dict.containsKey(ngayTmp)){
                    listTmp.add(dict.get(ngayTmp));
                }
            }
        }
        return listTmp;

    }


    @Override
    public List<LastmileChartCronDto> getDataChart(LasmileBody body) {

        Date ngay = DateTimeUtil.stringToSqlDate(body.getNgayBaoCao());

        Date ngay_01 = DateTimeUtil.stringToSqlDate(getNgayDauThang(body.getNgayBaoCao()));
        String maCn = body.chiNhanh;
        String maBc = body.buuCuc;
        if (maCn == null || maCn.isEmpty()) {
            maCn = "ALL";
            maBc = "ALL";

        } else {
            if (maBc == null || maBc.isEmpty()) {
                maBc = "ALL";
            }
        }
        return getListRepsonseChart(lastmileRepoNgayPostgres.getDataChart(ngay, ngay_01, maCn, maBc), body.ngayBaoCao);
    }
}
