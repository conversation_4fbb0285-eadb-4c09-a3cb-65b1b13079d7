package nocsystem.indexmanager.servicesIpm.LogWebview;


import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.LogWebview.*;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh1DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinh2DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh.XeChoKhaiThacTinhAllXeDTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeKhaiThacTinhDetailXeDTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh1DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinh2DTO;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeDangKhaiThacTinh.XeDangKhaiThacTinhAllXeDTO;
import nocsystem.indexmanager.repositories.LogWebview.ChiTietXeRepository;
import nocsystem.indexmanager.repositories.LogWebview.SanLuongXeTinhRepository;
import nocsystem.indexmanager.services.LogWebview.ChiTietXeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Service
public class ChiTietXeServiceImpl implements ChiTietXeService {

    private static final Logger logger = LoggerFactory.getLogger(ChiTietXeServiceImpl.class);

    private final ChiTietXeRepository chiTietXeRepository;

    private final SanLuongXeTinhRepository sanLuongXeTinhRepository;



    public ChiTietXeServiceImpl(ChiTietXeRepository chiTietXeRepository, SanLuongXeTinhRepository sanLuongXeTinhRepository) {
        this.chiTietXeRepository = chiTietXeRepository;
        this.sanLuongXeTinhRepository = sanLuongXeTinhRepository;
    }

//    check admin
    private boolean isAdmin() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_CONGTY_LOG") || UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_LANHDAO_CONGTYLOG");
    }

    public ListContentPageDto<ChiTietXeDTO> chiTietXeTTKT5(Integer loaiBaoCao, String dvvc, String hanhTrinh, Integer pageQuery, Integer pageSize) {
        Pageable page;
        page = PageRequest.of(pageQuery, pageSize);

        Page<ChiTietXeDTO> result = null;

        if(!isAdmin())
            return new ListContentPageDto<>();

        switch (loaiBaoCao){
            case 1:
                result = chiTietXeRepository.chiTietXeChoKhaiThacTTKT5(dvvc, hanhTrinh, page);
                break;
            case 2:
                result = chiTietXeRepository.chiTietXeDangKhaiThacTTKT5(dvvc, hanhTrinh, page);
                break;
            case 3:
                result = chiTietXeRepository.chiTietXeDuKienDenTTKT5(page);
                break;
            default:
                break;
        }

        ListContentPageDto<ChiTietXeDTO> listContent = new ListContentPageDto<>(result);
        return listContent;
    }

    //bảng chi tiết xe
    public ListContentPageDto<ChiTietXeDTO> chiTietXe(Integer loaiBaoCao, List<String> dvvc, List<String> hanhTrinh, String ttkt, String chiSo, Integer pageQuery, Integer pageSize) {
        Pageable page;
        page = PageRequest.of(pageQuery, pageSize);

        Page<ChiTietXeDTO> result = null;

        List<Integer> typeCho = new ArrayList<>();

        switch (chiSo) {
            case "duoi30p":
                typeCho = Arrays.asList(1);
                break;
            case "duoi1h":
                typeCho = Arrays.asList(2);
                break;
            case "duoi2h":
                typeCho = Arrays.asList(3);
                break;
            case "tren2h":
                typeCho = Arrays.asList(4);
                break;
            case "tong":
                typeCho = Arrays.asList(1, 2, 3, 4);
                break;
            default:
                break;
        }

        if (!isAdmin())
            return new ListContentPageDto<>();

        switch (loaiBaoCao) {
            case 1:
                result = chiTietXeRepository.chiTietXeChoKhaiThac(dvvc, hanhTrinh, ttkt, typeCho, page);
                break;
            case 2:
                result = chiTietXeRepository.chiTietXeDangKhaiThac(dvvc, hanhTrinh, ttkt, page);
                break;
            case 3:
                result = chiTietXeRepository.chiTietXeDuKienDen(page);
                break;
            default:
                break;
        }

        ListContentPageDto<ChiTietXeDTO> listContent = new ListContentPageDto<>(result);
        return listContent;
    }

    //bảng sản lượng xe chờ khai thác
    public SimpleAPIResponseWithSum sanLuongXeChoKhaiThac(List<String> dvvc, List<String> hanhTrinh, String ttkt, Integer pageQuery, Integer pageSize) {
        Pageable page;
        page = PageRequest.of(pageQuery, pageSize);

        if (!isAdmin())
            return new SimpleAPIResponseWithSum();

        Page<XeChoKhaiThacDTO> result = chiTietXeRepository.slXeChoKhaiThac(dvvc, hanhTrinh, page);
        XeChoKhaiThacDTO sum = chiTietXeRepository.slXeChoKhaiThacSum(dvvc, hanhTrinh);

        ListContentPageDto<XeChoKhaiThacDTO> listContent = new ListContentPageDto<>(result);
        listContent.setTime(chiTietXeRepository.findTopByOrderByDateDesc());

        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();
        response.setData(listContent);
        response.setSummary(sum);
        return response;
    }

    @Override
    public SimpleAPIResponseWithSum sanLuongXeChoKhaiThacTinh(List<String> dvvc, List<String> hanhTrinh, String chiSo, Integer page, Integer pageSize, String order, String orderBy) {
        if (checkRole()) {
            return new SimpleAPIResponseWithSum();
        }

        Pageable pageable;

        if ((order != null && !order.isEmpty()) && (orderBy != null && !orderBy.isEmpty())) {
            if (order.equals("asc")) {
                pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.ASC, orderBy));
            } else {
                pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, orderBy));
            }
        } else {
            pageable = PageRequest.of(page, pageSize);
        }

        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();

        switch (chiSo) {
            case "soLuongXe":
                Page<XeChoKhaiThacTinh1DTO> result1 = sanLuongXeTinhRepository.soLuongXeChoKhaiThacTinh(dvvc, hanhTrinh, pageable);
                XeChoKhaiThacTinh1DTO sum1 = sanLuongXeTinhRepository.soLuongXeChoKhaiThacTinhSum(dvvc, hanhTrinh);
                ListContentPageDto<XeChoKhaiThacTinh1DTO> listContent1 = new ListContentPageDto<>(result1);
                listContent1.setTime(sanLuongXeTinhRepository.findCalculateTime());
                response.setData(listContent1);
                response.setSummary(sum1);
                break;
            case "trongLuongXe":
                Page<XeChoKhaiThacTinh2DTO> result2 = sanLuongXeTinhRepository.trongLuongXeChoKhaiThacTinh(dvvc, hanhTrinh, pageable);
                XeChoKhaiThacTinh2DTO sum2 = sanLuongXeTinhRepository.trongLuongXeChoKhaiThacTinhSum(dvvc, hanhTrinh);
                ListContentPageDto<XeChoKhaiThacTinh2DTO> listContent2 = new ListContentPageDto<>(result2);
                listContent2.setTime(sanLuongXeTinhRepository.findCalculateTime());
                response.setData(listContent2);
                response.setSummary(sum2);
                break;
            case "soLuongBuuGui":
                Page<XeChoKhaiThacTinh1DTO> result3 = sanLuongXeTinhRepository.soLuongBuuGuiChoKhaiThacTinh(dvvc, hanhTrinh, pageable);
                XeChoKhaiThacTinh1DTO sum3 = sanLuongXeTinhRepository.soLuongBuuGuiChoKhaiThacTinhSum(dvvc, hanhTrinh);
                ListContentPageDto<XeChoKhaiThacTinh1DTO> listContent3 = new ListContentPageDto<>(result3);
                listContent3.setTime(sanLuongXeTinhRepository.findCalculateTime());
                response.setData(listContent3);
                response.setSummary(sum3);
                break;
        }

        return response;
    }

    @Override
    public SimpleAPIResponseV2 sanLuongXeChoKhaiThacTinhAllXe(List<String> dvvc, List<String> hanhTrinh, String ttkt, String thoiGian, Integer page, Integer pageSize) {
        if (checkRole()) {
            return new SimpleAPIResponseV2();
        }

        Pageable pageable = PageRequest.of(page, pageSize);
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();

        Integer thoiGianQuery = null;
        switch (thoiGian) {
            case "duoi30p":
                thoiGianQuery = 1;
                break;
            case "duoi1h":
                thoiGianQuery = 2;
                break;
            case "duoi2h":
                thoiGianQuery = 3;
                break;
            case "tren2h":
                thoiGianQuery = 4;
                break;
            default:
                break;
        }

        Page<XeChoKhaiThacTinhAllXeDTO> result = sanLuongXeTinhRepository.soLuongXeChoKhaiThacTinhAllXe(dvvc, hanhTrinh, ttkt, thoiGianQuery, pageable);
        if (result != null && !result.isEmpty()) {
            int stt = 1;
            for (XeChoKhaiThacTinhAllXeDTO item: result) {
                item.setStt(stt++);
            }
        }
        ListContentPageDto<XeChoKhaiThacTinh1DTO> listContent = new ListContentPageDto<>(result);
        listContent.setTime(sanLuongXeTinhRepository.findCalculateTime());
        response.setData(listContent);

        return response;
    }

    @Override
    public SimpleAPIResponseV2 sanLuongXeChoKhaiThacTinhDetailXe(String bienSoXe) {
        if (checkRole()) {
            return new SimpleAPIResponseV2();
        }
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();

        XeKhaiThacTinhDetailXeDTO result = sanLuongXeTinhRepository.soLuongXeChoKhaiThacTinhDetailXe(bienSoXe);

        response.setData(result);

        return response;
    }

    @Override
    public SimpleAPIResponseWithSum sanLuongXeDangKhaiThacTinh(List<String> dvvc, List<String> hanhTrinh, String chiSo, Integer page, Integer pageSize, String order, String orderBy) {
        if (checkRole()) {
            return new SimpleAPIResponseWithSum();
        }

        Pageable pageable;

        if ((order != null && !order.isEmpty()) && (orderBy != null && !orderBy.isEmpty())) {
            if (order.equals("asc")) {
                pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.ASC, orderBy));
            } else {
                pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, orderBy));
            }
        } else {
            pageable = PageRequest.of(page, pageSize);
        }

        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();

        switch (chiSo) {
            case "soLuongXe":
                Page<XeDangKhaiThacTinh1DTO> result1 = sanLuongXeTinhRepository.soLuongXeDangKhaiThacTinh(dvvc, hanhTrinh, pageable);
                XeDangKhaiThacTinh1DTO sum1 = sanLuongXeTinhRepository.soLuongXeDangKhaiThacTinhSum(dvvc, hanhTrinh);
                ListContentPageDto<XeDangKhaiThacTinh1DTO> listContent1 = new ListContentPageDto<>(result1);
                listContent1.setTime(sanLuongXeTinhRepository.findCalculateTime());
                response.setData(listContent1);
                response.setSummary(sum1);
                break;
            case "trongLuongXe":
                Page<XeDangKhaiThacTinh2DTO> result2 = sanLuongXeTinhRepository.trongLuongXeDangKhaiThacTinh(dvvc, hanhTrinh, pageable);
                XeDangKhaiThacTinh2DTO sum2 = sanLuongXeTinhRepository.trongLuongXeDangKhaiThacTinhSum(dvvc, hanhTrinh);
                ListContentPageDto<XeDangKhaiThacTinh2DTO> listContent2 = new ListContentPageDto<>(result2);
                listContent2.setTime(sanLuongXeTinhRepository.findCalculateTime());
                response.setData(listContent2);
                response.setSummary(sum2);
                break;
            case "soLuongBuuGui":
                Page<XeDangKhaiThacTinh1DTO> result3 = sanLuongXeTinhRepository.soLuongBuuGuiDangKhaiThacTinh(dvvc, hanhTrinh, pageable);
                XeDangKhaiThacTinh1DTO sum3 = sanLuongXeTinhRepository.soLuongBuuGuiDangKhaiThacTinhSum(dvvc, hanhTrinh);
                ListContentPageDto<XeDangKhaiThacTinh1DTO> listContent3 = new ListContentPageDto<>(result3);
                listContent3.setTime(sanLuongXeTinhRepository.findCalculateTime());
                response.setData(listContent3);
                response.setSummary(sum3);
                break;
        }

        return response;
    }

    //bảng sản lượng xe đang khai thác
    public SimpleAPIResponseWithSum sanLuongXeDangKhaiThac(List<String> dvvc, List<String> hanhTrinh, String ttkt, Integer pageQuery, Integer pageSize) {
        Pageable page;
        page = PageRequest.of(pageQuery, pageSize);

        if (!isAdmin())
            return new SimpleAPIResponseWithSum();

        Page<XeDangKhaiThacDTO> result = chiTietXeRepository.slXeDangKhaiThac(dvvc, hanhTrinh, page);
        ListContentPageDto<XeDangKhaiThacDTO> listContent = new ListContentPageDto<>(result);
        XeDangKhaiThacDTO sum = chiTietXeRepository.slXeDangKhaiThacSum(dvvc, hanhTrinh);

        SimpleAPIResponseWithSum response = new SimpleAPIResponseWithSum();
        response.setData(listContent);
        response.setSummary(sum);

        return response;
    }

    @Override
    public SimpleAPIResponseV2 sanLuongXeDangKhaiThacTinhAllXe(List<String> dvvc, List<String> hanhTrinh, String ttkt, Integer page, Integer pageSize) {
        if (checkRole()) {
            return new SimpleAPIResponseV2();
        }

        Pageable pageable = PageRequest.of(page, pageSize);
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();

        Page<XeDangKhaiThacTinhAllXeDTO> result = sanLuongXeTinhRepository.soLuongXeDangKhaiThacTinhAllXe(dvvc, hanhTrinh, ttkt, pageable);
        if (result != null && !result.isEmpty()) {
            int stt = 1;
            for (XeDangKhaiThacTinhAllXeDTO item: result) {
                item.setStt(stt++);
            }
        }
        ListContentPageDto<XeDangKhaiThacTinhAllXeDTO> listContent = new ListContentPageDto<>(result);
        listContent.setTime(sanLuongXeTinhRepository.findCalculateTime());
        response.setData(listContent);

        return response;
    }

    @Override
    public SimpleAPIResponseV2 sanLuongXeDangKhaiThacTinhDetailXe(String bienSoXe) {
        if (checkRole()) {
            return new SimpleAPIResponseV2();
        }
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();

        XeKhaiThacTinhDetailXeDTO result = sanLuongXeTinhRepository.soLuongXeDangKhaiThacTinhDetailXe(bienSoXe);

        response.setData(result);

        return response;
    }

    public ChiTietChuyenXeDTO chiTietChuyenXe(String maChuyenXe) {
        if (!isAdmin())
            return new ChiTietChuyenXeDTO();
        return chiTietXeRepository.chiTietChuyenXe(maChuyenXe);
    }

    private boolean checkRole() {
        if (!UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_ADMIN") &&
                !UserContext.getUserData().getRole().equalsIgnoreCase("LANH_DAO_TCT") &&
                !UserContext.getUserData().getRole().equalsIgnoreCase("LANH_DAO_TTVH") &&
                !UserContext.getUserData().getRole().equalsIgnoreCase("CHUYEN_QUAN_TCT_TTVH") &&
                !UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_CONGTY_LOG") &&
                !UserContext.getUserData().getRole().equalsIgnoreCase("ROLE_LANHDAO_CONGTYLOG")) {
            return true;
        }
        return false;
    }
}
