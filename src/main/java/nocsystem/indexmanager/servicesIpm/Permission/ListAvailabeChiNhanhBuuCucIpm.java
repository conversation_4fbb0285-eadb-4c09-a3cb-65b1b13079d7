package nocsystem.indexmanager.servicesIpm.Permission;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.exception.BadRequestException;
import nocsystem.indexmanager.models.GlobalConfig.JWTInformDTO;
import nocsystem.indexmanager.models.Response.Partner.SystemUserModel;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ListAvailabeChiNhanhBuuCucIpm {
    private static final int WHITE_SPACE_LENGTH = 7;

    public SystemUserModel getAvailabeChiNhanhBuuCuc(String objChiNhanhBuuCuc, String isAdmin, String role) {
        List<String> defaulValue = List.of("0a4f84470092dcc99c6c0d4beab15228");
        String objectString = (objChiNhanhBuuCuc != null && !objChiNhanhBuuCuc.equals("")) ? objChiNhanhBuuCuc.toString().replaceAll(" ", "") : "";
//        String objectString = (!objChiNhanhBuuCuc.equals("") && objChiNhanhBuuCuc != null) ? objChiNhanhBuuCuc.toString() : "";
        Gson gson = new GsonBuilder().serializeSpecialFloatingPointValues().create();
        Map<String, List<String>> test = gson.fromJson(objectString, Map.class);
        SystemUserModel systemUserModel = new SystemUserModel();
        if (isAdmin.equals("false") && (objectString == null || objectString.equals(""))) {
            systemUserModel.setDanhMucChiNhanh(defaulValue);
            systemUserModel.setDanhMucBuuCuc(defaulValue);
            systemUserModel.setRole(role);

            return systemUserModel;
        }

        if (isAdmin.equals("true")) {
            systemUserModel.setDanhMucChiNhanh(null);
            systemUserModel.setDanhMucBuuCuc(null);
        } else {
            try {
                Set<String> maChiNhanhSet = test.keySet();
                List<String> maBuuCuc = new ArrayList<>();

                for (String maChiNhanhDt : maChiNhanhSet) {
                    maChiNhanhDt = String.valueOf(maChiNhanhDt);
                    if (test.containsKey(maChiNhanhDt)) {
                        maBuuCuc.addAll(listBuuCucUserPhuTrachTheoTinh(test, maChiNhanhDt));
                    }
                }

                List<String> maChiNhanh = new ArrayList<>(maChiNhanhSet);
                systemUserModel.setDanhMucChiNhanh(maChiNhanh);
                systemUserModel.setDanhMucBuuCuc(maBuuCuc);
                systemUserModel.setRole(role);
            } catch (Exception exception) {
                throw new BadRequestException("Không có dữ liệu cn-bc");
            }
        }

        return systemUserModel;
    }

    private List<String> listBuuCucUserPhuTrachTheoTinh(Map<String, List<String>> test, String maTinh) {
        List<String> result = new ArrayList<>();

        List<Object> listBuuCuc = Collections.singletonList(test.get(maTinh));

        List<Object> listBuuCucs = (List<Object>) listBuuCuc.get(0);

        if (listBuuCucs == null) {
            return new ArrayList<>();
        }

        result = listBuuCucs.stream().map(o -> Objects.toString(o).trim().replace(".0", "")).collect(Collectors.toList());

        System.out.println("==========> list bưu cục theo tỉnh : tinh :" + maTinh + " -- list BC: " + result);

        return result;
    }

    public JWTInformDTO informFromJWT(HttpServletRequest request) throws JsonProcessingException {
        String jwt = request.getHeader("Authorization");
        if (jwt == null) {
            return null;
        }

        String token = jwt.substring(jwt.indexOf("Bearer") + WHITE_SPACE_LENGTH);

        if (token.equals("")) {
            return null;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        String[] chunks = token.split("\\.");
        Base64.Decoder decoder = Base64.getUrlDecoder();
        String payload = new String(decoder.decode(chunks[1]));
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper.readValue(payload, JWTInformDTO.class);
    }
}
