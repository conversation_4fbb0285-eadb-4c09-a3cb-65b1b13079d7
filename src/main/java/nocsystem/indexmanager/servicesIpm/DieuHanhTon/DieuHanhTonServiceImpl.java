package nocsystem.indexmanager.servicesIpm.DieuHanhTon;

import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.DieuHanhTon.*;
import nocsystem.indexmanager.repositories.DieuHanhTon.DieuHanhTonHoanPhatDetailRepository;
import nocsystem.indexmanager.repositories.DieuHanhTon.DieuHanhTonHoanPhatRepository;
import nocsystem.indexmanager.repositories.DieuHanhTon.DieuHanhTonThuDetailRepository;
import nocsystem.indexmanager.repositories.DieuHanhTon.DieuHanhTonThuRepository;
import nocsystem.indexmanager.services.DieuHanhTon.DieuHanhTonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Service
public class DieuHanhTonServiceImpl<T> implements DieuHanhTonService {
    @Autowired
    DieuHanhTonHoanPhatRepository dieuHanhTonHoanPhatRepository;

    @Autowired
    DieuHanhTonHoanPhatDetailRepository dieuHanhTonHoanPhatDetailRepository;

    @Autowired
    DieuHanhTonThuRepository dieuHanhTonThuRepository;

    @Autowired
    DieuHanhTonThuDetailRepository dieuHanhTonThuDetailRepository;

    @Override
    public Page<DieuHanhTonPhatResponse> getPhaiPhatDHTTongQuan(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, Pageable pageable) {
        Page<DieuHanhTonPhatResponse> listData = Page.empty(pageable);
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTTongQuanTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac, pageable);
            } else {
                listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTTongQuanTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac, pageable);
            }
        }
        return listData;
    }

    @Override
    public Page<DieuHanhTonPhatResponse> getPhaiPhatDHTChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, String sortKey, String sortType, Integer pageNumber, Integer pageSize) {
        Page<DieuHanhTonPhatResponse> pageData = null;
        List<DieuHanhTonPhatResponse> listData = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTChiTietTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac);
            } else {
                if (buuCuc == null || buuCuc.equals("")) {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                } else {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTChiTietTheoCNBCT(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                }
            }

            if (sortKey != null && sortType != null) {
                switch (sortKey) {
                    case "phatTh1d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatTh1d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatTh1d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatTh2d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatTh2d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatTh2d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatTh3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatTh3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatTh3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatThl3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatThl3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatThl3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatThTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatThTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatThTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatDh6h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh6h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh6h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatDh12h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh12h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh12h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatDh18h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh18h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh18h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatDh24h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh24h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDh24h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatDhTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDhTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatDhTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQh1d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh1d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh1d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQh2d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh2d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh2d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQh3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQh4d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh4d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh4d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQh5d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh5d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh5d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQh6d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh6d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh6d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQh7d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh7d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQh7d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQhl7d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQhl7d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQhl7d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "phatQhTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQhTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonPhatResponse::getPhatQhTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    default:
                        break;
                }
            }
            int start = Math.min(pageNumber * pageSize, listData.size());
            int end = Math.min((pageNumber + 1) * pageSize, listData.size());
            List<DieuHanhTonPhatResponse> sublist = listData.subList(start, end);
            pageData = new PageImpl<>(sublist, PageRequest.of(pageNumber, pageSize), listData.size());
        }
        return pageData;
    }

    @Override
    public DieuHanhTonPhatResponse getPhaiPhatDHTTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) {
        DieuHanhTonPhatResponse data = new DieuHanhTonPhatResponse();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            data = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTTong(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
        }
        return data;
    }

    @Override
    public void exportPhaiPhatDHTExcelTongHop(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response) {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=DHTPhat.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("chiNhanh", "Chi nhánh");
        mapHeader.put("buuCuc", "Bưu cục");
        mapHeader.put("tuyenBuuTa", "Tuyến");
        mapHeader.put("phatTh1d", "Trong hạn 1 ngày");
        mapHeader.put("phatTh2d", "Trong hạn 2 ngày");
        mapHeader.put("phatTh3d", "Trong hạn 3 ngày");
        mapHeader.put("phatThl3d", "Trong hạn trên 3 ngày");
        mapHeader.put("phatThTong", "Trong hạn tổng");
        mapHeader.put("phatDh6h", "Đến hạn 6 giờ");
        mapHeader.put("phatDh12h", "Đến hạn 12 giờ");
        mapHeader.put("phatDh18h", "Đến hạn 18 giờ");
        mapHeader.put("phatDh24h", "Đến hạn 24 giờ");
        mapHeader.put("phatDhTong", "Đến hạn tổng");
        mapHeader.put("phatQh1d", "Quá hạn 1 ngày");
        mapHeader.put("phatQh2d", "Quá hạn 2 ngày");
        mapHeader.put("phatQh3d", "Quá hạn 3 ngày");
        mapHeader.put("phatQh4d", "Quá hạn 4 ngày");
        mapHeader.put("phatQh5d", "Quá hạn 5 ngày");
        mapHeader.put("phatQh6d", "Quá hạn 6 ngày");
        mapHeader.put("phatQh7d", "Quá hạn 7 ngày");
        mapHeader.put("phatQhl7d", "Quá hạn trên 7 ngày");
        mapHeader.put("phatQhTong", "Quá hạn tổng");
        List<String> header = Arrays.asList(
                "stt",
                "chiNhanh",
                "buuCuc",
                "tuyenBuuTa",
                "phatTh1d",
                "phatTh2d",
                "phatTh3d",
                "phatThl3d",
                "phatThTong",
                "phatDh6h",
                "phatDh12h",
                "phatDh18h",
                "phatDh24h",
                "phatDhTong",
                "phatQh1d",
                "phatQh2d",
                "phatQh3d",
                "phatQh4d",
                "phatQh5d",
                "phatQh6d",
                "phatQh7d",
                "phatQhl7d",
                "phatQhTong"
        );
        try {
            DieuHanhTonExcelTemplate dieuHanhTonExcelTemplate = new DieuHanhTonExcelTemplate(
                    findDataDHTPhat(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac),
                    mapHeader,
                    header,
                    "DHTPhat");
            dieuHanhTonExcelTemplate.exportDataToExcel(response);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void exportPhaiPhatDHTExcelChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response) {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=DHTPhat.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("maPhieuGui", "Mã phiếu gửi");
        mapHeader.put("tinhNhan", "Tỉnh nhận");
        mapHeader.put("huyenNhan", "Huyện nhận");
        mapHeader.put("tinhPhat", "Tỉnh phát");
        mapHeader.put("huyenPhat", "Huyện phát");
        mapHeader.put("maBuuCucPhatThucTe", "Mã bưu cục phát thực tế");
        mapHeader.put("maDVViettel", "Mã dịch vụ Viettel");
        mapHeader.put("maBuuCucGoc", "Mã bưu cục gốc");
        mapHeader.put("maTrangThai", "Trạng thái");
        mapHeader.put("maBuuCucHT", "Mã bưu cục HT");
        mapHeader.put("maChiNhanhHT", "Chi nhánh HT");
        mapHeader.put("maDoiTac", "Mã đối tác");
        mapHeader.put("maKHGui", "Mã KH gửi");
        mapHeader.put("maBuuCucPhat", "Mã bưu cục phát");
        mapHeader.put("timePCP", "Thời gian PCP");
        mapHeader.put("timeGachBP", "Thời gian gạch BP");
        mapHeader.put("timeGachBP2", "Thời gian gạch BP2");
        mapHeader.put("timeGachBP3", "Thời gian gạch BP3");
        mapHeader.put("ngayGuiBP", "Ngày gửi BP");
        mapHeader.put("loaiPG", "Loại PG");
        mapHeader.put("soLanGiao", "Lần phát");
        mapHeader.put("tuyenBuuTa", "Bưu tá phát");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("tgQuyDinhPhat", "TG quy định");
        mapHeader.put("tgChenhLechPhat", "TG chênh lệch");
        mapHeader.put("ngayBaoCao", "Ngày báo cáo");
        mapHeader.put("noiKho", "Loại đơn");
        mapHeader.put("typeGiao", "Type giao");
        mapHeader.put("danhGiaTon", "Đánh giá");
        mapHeader.put("thoiGianTon", "Thời gian tồn");
        List<String> header = Arrays.asList(
                "stt",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tinhPhat",
                "huyenPhat",
                "maBuuCucPhatThucTe",
                "maDVViettel",
                "maBuuCucGoc",
                "maTrangThai",
                "maBuuCucHT",
                "maChiNhanhHT",
                "maDoiTac",
                "maKHGui",
                "maBuuCucPhat",
                "timePCP",
                "timeGachBP",
                "timeGachBP2",
                "timeGachBP3",
                "ngayGuiBP",
                "loaiPG",
                "soLanGiao",
                "tuyenBuuTa",
                "tienCOD",
                "tgQuyDinhPhat",
                "tgChenhLechPhat",
                "ngayBaoCao",
                "noiKho",
                "typeGiao",
                "danhGiaTon",
                "thoiGianTon"
        );
        try {
            DieuHanhTonExcelTemplate dieuHanhTonExcelTemplate = new DieuHanhTonExcelTemplate(
                    findDataDHTPhatDetail(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac),
                    mapHeader,
                    header,
                    "DHTPhat");
            dieuHanhTonExcelTemplate.exportDataToExcel(response);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<Map<String, Object>> findDataDHTPhatDetail(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) throws NoSuchFieldException, IllegalAccessException{
        List<DieuHanhTonPhatDetailResponse> listData = new ArrayList<>();
        List<DieuHanhTonPhatExcelDetail> listDataExcel = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            listData = dieuHanhTonHoanPhatDetailRepository.getPhaiPhatDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
            int stt = 1;
            for (DieuHanhTonPhatDetailResponse item: listData) {
                DieuHanhTonPhatExcelDetail itemExcel = new DieuHanhTonPhatExcelDetail(
                        stt,
                        item.getMaPhieuGui(),
                        item.getTinhNhan(),
                        item.getHuyenNhan(),
                        item.getTinhPhat(),
                        item.getHuyenPhat(),
                        item.getMaBuuCucPhatThucTe(),
                        item.getMaDVViettel(),
                        item.getMaBuuCucGoc(),
                        item.getMaTrangThai(),
                        item.getMaBuuCucHT(),
                        item.getMaChiNhanhHT(),
                        item.getMaDoiTac(),
                        item.getMaKHGui(),
                        item.getMaBuuCucPhat(),
                        item.getTimePCP(),
                        item.getTimeGachBP(),
                        item.getTimeGachBP2(),
                        item.getTimeGachBP3(),
                        item.getNgayGuiBP(),
                        item.getSoLanGiao(),
                        item.getTuyenBuuTa(),
                        item.getTienCOD(),
                        item.getTgQuyDinhPhat(),
                        item.getTgChenhLechPhat(),
                        item.getNgayBaoCao(),
                        item.getNoiKho(),
                        item.getTypeGiao(),
                        item.getDanhGiaTon(),
                        item.getThoiGianTon()

                );
                listDataExcel.add(itemExcel);
                stt++;
            }
        }
        return tryTestDataExcelDHTPhatDetail((List<T>) listDataExcel);
    }

    private List<Map<String, Object>> findDataDHTPhat(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) throws NoSuchFieldException, IllegalAccessException {
        List<DieuHanhTonPhatResponse> listData = new ArrayList<>();
        List<DieuHanhTonPhatExcel> listDataExcel = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTChiTietTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac);
                int stt = 1;
                for (DieuHanhTonPhatResponse item: listData) {
                    DieuHanhTonPhatExcel itemExcel = new DieuHanhTonPhatExcel(
                            stt,
                            item.getChiNhanh(),
                            item.getPhatTh1d(),
                            item.getPhatTh2d(),
                            item.getPhatTh3d(),
                            item.getPhatThl3d(),
                            item.getPhatThTong(),
                            item.getPhatDh6h(),
                            item.getPhatDh12h(),
                            item.getPhatDh18h(),
                            item.getPhatDh24h(),
                            item.getPhatDhTong(),
                            item.getPhatQh1d(),
                            item.getPhatQh2d(),
                            item.getPhatQh3d(),
                            item.getPhatQh4d(),
                            item.getPhatQh5d(),
                            item.getPhatQh6d(),
                            item.getPhatQh7d(),
                            item.getPhatQhl7d(),
                            item.getPhatQhTong()
                    );
                    listDataExcel.add(itemExcel);
                    stt++;
                }
            } else {
                if (buuCuc == null || buuCuc.equals("")) {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                    int stt = 1;
                    for (DieuHanhTonPhatResponse item: listData) {
                        DieuHanhTonPhatExcel itemExcel = new DieuHanhTonPhatExcel(
                                stt,
                                item.getChiNhanh(),
                                item.getBuuCuc(),
                                item.getPhatTh1d(),
                                item.getPhatTh2d(),
                                item.getPhatTh3d(),
                                item.getPhatThl3d(),
                                item.getPhatThTong(),
                                item.getPhatDh6h(),
                                item.getPhatDh12h(),
                                item.getPhatDh18h(),
                                item.getPhatDh24h(),
                                item.getPhatDhTong(),
                                item.getPhatQh1d(),
                                item.getPhatQh2d(),
                                item.getPhatQh3d(),
                                item.getPhatQh4d(),
                                item.getPhatQh5d(),
                                item.getPhatQh6d(),
                                item.getPhatQh7d(),
                                item.getPhatQhl7d(),
                                item.getPhatQhTong()
                        );
                        listDataExcel.add(itemExcel);
                        stt++;
                    }
                } else {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiPhatDHTChiTietTheoCNBCT(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                    int stt = 1;
                    for (DieuHanhTonPhatResponse item: listData) {
                        DieuHanhTonPhatExcel itemExcel = new DieuHanhTonPhatExcel(
                                stt,
                                item.getChiNhanh(),
                                item.getBuuCuc(),
                                item.getTuyenBuuTa(),
                                item.getPhatTh1d(),
                                item.getPhatTh2d(),
                                item.getPhatTh3d(),
                                item.getPhatThl3d(),
                                item.getPhatThTong(),
                                item.getPhatDh6h(),
                                item.getPhatDh12h(),
                                item.getPhatDh18h(),
                                item.getPhatDh24h(),
                                item.getPhatDhTong(),
                                item.getPhatQh1d(),
                                item.getPhatQh2d(),
                                item.getPhatQh3d(),
                                item.getPhatQh4d(),
                                item.getPhatQh5d(),
                                item.getPhatQh6d(),
                                item.getPhatQh7d(),
                                item.getPhatQhl7d(),
                                item.getPhatQhTong()
                        );
                        listDataExcel.add(itemExcel);
                        stt++;
                    }
                }
            }
        }
        return tryTestDataExcelDHTPhat((List<T>) listDataExcel);
    }

    @Override
    public Page<DieuHanhTonHoanResponse> getPhaiHoanDHTTongQuan(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, Pageable pageable) {
        Page<DieuHanhTonHoanResponse> listData = Page.empty(pageable);
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTTongQuanTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac, pageable);
            } else {
                listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTTongQuanTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac, pageable);
            }
        }
        return listData;
    }

    @Override
    public Page<DieuHanhTonHoanResponse> getPhaiHoanDHTChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, String sortKey, String sortType, Integer pageNumber, Integer pageSize) {
        Page<DieuHanhTonHoanResponse> pageData = null;
        List<DieuHanhTonHoanResponse> listData = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTChiTietTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac);
            } else {
                if (buuCuc == null || buuCuc.equals("")) {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                } else {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTChiTietTheoCNBCT(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                }
            }

            if (sortKey != null && sortType != null) {
                switch (sortKey) {
                    case "hoanTh1d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanTh1d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanTh1d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanTh2d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanTh2d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanTh2d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanTh3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanTh3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanTh3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanThl3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanThl3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanThl3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanThTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanThTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanThTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanDh6h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh6h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh6h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanDh12h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh12h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh12h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanDh18h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh18h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh18h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanDh24h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh24h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDh24h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanDhTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDhTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanDhTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQh1d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh1d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh1d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQh2d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh2d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh2d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQh3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQh4d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh4d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh4d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQh5d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh5d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh5d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQh6d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh6d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh6d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQh7d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh7d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQh7d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQhl7d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQhl7d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQhl7d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "hoanQhTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQhTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonHoanResponse::getHoanQhTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    default:
                        break;
                }
            }
            int start = Math.min(pageNumber * pageSize, listData.size());
            int end = Math.min((pageNumber + 1) * pageSize, listData.size());
            List<DieuHanhTonHoanResponse> sublist = listData.subList(start, end);
            pageData = new PageImpl<>(sublist, PageRequest.of(pageNumber, pageSize), listData.size());
        }
        return pageData;
    }

    @Override
    public DieuHanhTonHoanResponse getPhaiHoanDHTTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) {
        DieuHanhTonHoanResponse data = new DieuHanhTonHoanResponse();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            data = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTTong(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
        }
        return data;
    }

    @Override
    public void exportPhaiHoanDHTExcelTongHop(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response) {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=DHTHoan.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("chiNhanh", "Chi nhánh");
        mapHeader.put("buuCuc", "Bưu cục");
        mapHeader.put("tuyenBuuTa", "Tuyến");
        mapHeader.put("hoanTh1d", "Trong hạn 1 ngày");
        mapHeader.put("hoanTh2d", "Trong hạn 2 ngày");
        mapHeader.put("hoanTh3d", "Trong hạn 3 ngày");
        mapHeader.put("hoanThl3d", "Trong hạn trên 3 ngày");
        mapHeader.put("hoanThTong", "Trong hạn tổng");
        mapHeader.put("hoanDh6h", "Đến hạn 6 giờ");
        mapHeader.put("hoanDh12h", "Đến hạn 12 giờ");
        mapHeader.put("hoanDh18h", "Đến hạn 18 giờ");
        mapHeader.put("hoanDh24h", "Đến hạn 24 giờ");
        mapHeader.put("hoanDhTong", "Đến hạn tổng");
        mapHeader.put("hoanQh1d", "Quá hạn 1 ngày");
        mapHeader.put("hoanQh2d", "Quá hạn 2 ngày");
        mapHeader.put("hoanQh3d", "Quá hạn 3 ngày");
        mapHeader.put("hoanQh4d", "Quá hạn 4 ngày");
        mapHeader.put("hoanQh5d", "Quá hạn 5 ngày");
        mapHeader.put("hoanQh6d", "Quá hạn 6 ngày");
        mapHeader.put("hoanQh7d", "Quá hạn 7 ngày");
        mapHeader.put("hoanQhl7d", "Quá hạn trên 7 ngày");
        mapHeader.put("hoanQhTong", "Quá hạn tổng");
        List<String> header = Arrays.asList(
                "stt",
                "chiNhanh",
                "buuCuc",
                "tuyenBuuTa",
                "hoanTh1d",
                "hoanTh2d",
                "hoanTh3d",
                "hoanThl3d",
                "hoanThTong",
                "hoanDh6h",
                "hoanDh12h",
                "hoanDh18h",
                "hoanDh24h",
                "hoanDhTong",
                "hoanQh1d",
                "hoanQh2d",
                "hoanQh3d",
                "hoanQh4d",
                "hoanQh5d",
                "hoanQh6d",
                "hoanQh7d",
                "hoanQhl7d",
                "hoanQhTong"
        );
        try {
            DieuHanhTonExcelTemplate dieuHanhTonExcelTemplate = new DieuHanhTonExcelTemplate(
                    findDataDHTHoan(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac),
                    mapHeader,
                    header,
                    "DHTHoan");
            dieuHanhTonExcelTemplate.exportDataToExcel(response);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void exportPhaiHoanDHTExcelChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response) {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=DHTHoan.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("maPhieuGui", "Mã phiếu gửi");
        mapHeader.put("tinhNhan", "Tỉnh nhận");
        mapHeader.put("huyenNhan", "Huyện nhận");
        mapHeader.put("tinhPhat", "Tỉnh phát");
        mapHeader.put("huyenPhat", "Huyện phát");
        mapHeader.put("maBuuCucPhatThucTe", "Mã bưu cục phát thực tế");
        mapHeader.put("maDVViettel", "Mã dịch vụ Viettel");
        mapHeader.put("maBuuCucGoc", "Mã bưu cục gốc");
        mapHeader.put("maTrangThai", "Trạng thái");
        mapHeader.put("maBuuCucHT", "Mã bưu cục HT");
        mapHeader.put("maChiNhanhHT", "Chi nhánh HT");
        mapHeader.put("maDoiTac", "Mã đối tác");
        mapHeader.put("maKHGui", "Mã KH gửi");
        mapHeader.put("maBuuCucPhat", "Mã bưu cục phát");
        mapHeader.put("timePCP", "Thời gian PCP");
        mapHeader.put("timeGachBP", "Thời gian gạch BP");
        mapHeader.put("timeGachBP2", "Thời gian gạch BP2");
        mapHeader.put("timeGachBP3", "Thời gian gạch BP3");
        mapHeader.put("ngayGuiBP", "Ngày gửi BP");
        mapHeader.put("loaiPG", "Loại PG");
        mapHeader.put("soLanGiao", "Lần phát");
        mapHeader.put("tuyenBuuTa", "Bưu tá phát");
        mapHeader.put("tienCOD", "Tiền COD");
        mapHeader.put("tgQuyDinhPhat", "TG quy định");
        mapHeader.put("tgChenhLechPhat", "TG chênh lệch");
        mapHeader.put("ngayBaoCao", "Ngày báo cáo");
        mapHeader.put("noiKho", "Loại đơn");
        mapHeader.put("typeGiao", "Type giao");
        mapHeader.put("danhGiaTon", "Đánh giá");
        mapHeader.put("thoiGianTon", "Thời gian tồn");
        List<String> header = Arrays.asList(
                "stt",
                "maPhieuGui",
                "tinhNhan",
                "huyenNhan",
                "tinhPhat",
                "huyenPhat",
                "maBuuCucPhatThucTe",
                "maDVViettel",
                "maBuuCucGoc",
                "maTrangThai",
                "maBuuCucHT",
                "maChiNhanhHT",
                "maDoiTac",
                "maKHGui",
                "maBuuCucPhat",
                "timePCP",
                "timeGachBP",
                "timeGachBP2",
                "timeGachBP3",
                "ngayGuiBP",
                "loaiPG",
                "soLanGiao",
                "tuyenBuuTa",
                "tienCOD",
                "tgQuyDinhPhat",
                "tgChenhLechPhat",
                "ngayBaoCao",
                "noiKho",
                "typeGiao",
                "danhGiaTon",
                "thoiGianTon"
        );
        try {
            DieuHanhTonExcelTemplate dieuHanhTonExcelTemplate = new DieuHanhTonExcelTemplate(
                    findDataDHTHoanDetail(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac),
                    mapHeader,
                    header,
                    "DHTHoan");
            dieuHanhTonExcelTemplate.exportDataToExcel(response);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<Map<String, Object>> findDataDHTHoanDetail(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) throws NoSuchFieldException, IllegalAccessException {
        List<DieuHanhTonHoanDetailResponse> listData = new ArrayList<>();
        List<DieuHanhTonHoanExcelDetail> listDataExcel = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            listData = dieuHanhTonHoanPhatDetailRepository.getPhaiHoanDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
            int stt = 1;
            for (DieuHanhTonHoanDetailResponse item: listData) {
                DieuHanhTonHoanExcelDetail itemExcel = new DieuHanhTonHoanExcelDetail(
                        stt,
                        item.getMaPhieuGui(),
                        item.getTinhNhan(),
                        item.getHuyenNhan(),
                        item.getTinhPhat(),
                        item.getHuyenPhat(),
                        item.getMaBuuCucPhatThucTe(),
                        item.getMaDVViettel(),
                        item.getMaBuuCucGoc(),
                        item.getMaTrangThai(),
                        item.getMaBuuCucHT(),
                        item.getMaChiNhanhHT(),
                        item.getMaDoiTac(),
                        item.getMaKHGui(),
                        item.getMaBuuCucPhat(),
                        item.getTimePCP(),
                        item.getTimeGachBP(),
                        item.getTimeGachBP2(),
                        item.getTimeGachBP3(),
                        item.getNgayGuiBP(),
                        item.getSoLanGiao(),
                        item.getTuyenBuuTa(),
                        item.getTienCOD(),
                        item.getTgQuyDinhPhat(),
                        item.getTgChenhLechPhat(),
                        item.getNgayBaoCao(),
                        item.getNoiKho(),
                        item.getTypeGiao(),
                        item.getDanhGiaTon(),
                        item.getThoiGianTon()
                );
                listDataExcel.add(itemExcel);
                stt++;
            }
        }
        return tryTestDataExcelDHTHoanDetail((List<T>) listDataExcel);
    }

    private List<Map<String, Object>> findDataDHTHoan(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) throws NoSuchFieldException, IllegalAccessException {
        List<DieuHanhTonHoanResponse> listData = new ArrayList<>();
        List<DieuHanhTonHoanExcel> listDataExcel = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTChiTietTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac);
                int stt = 1;
                for (DieuHanhTonHoanResponse item: listData) {
                    DieuHanhTonHoanExcel itemExcel = new DieuHanhTonHoanExcel(
                            stt,
                            item.getChiNhanh(),
                            item.getHoanTh1d(),
                            item.getHoanTh2d(),
                            item.getHoanTh3d(),
                            item.getHoanThl3d(),
                            item.getHoanThTong(),
                            item.getHoanDh6h(),
                            item.getHoanDh12h(),
                            item.getHoanDh18h(),
                            item.getHoanDh24h(),
                            item.getHoanDhTong(),
                            item.getHoanQh1d(),
                            item.getHoanQh2d(),
                            item.getHoanQh3d(),
                            item.getHoanQh4d(),
                            item.getHoanQh5d(),
                            item.getHoanQh6d(),
                            item.getHoanQh7d(),
                            item.getHoanQhl7d(),
                            item.getHoanQhTong()
                    );
                    listDataExcel.add(itemExcel);
                    stt++;
                }
            } else {
                if (buuCuc == null || buuCuc.equals("")) {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                    int stt = 1;
                    for (DieuHanhTonHoanResponse item: listData) {
                        DieuHanhTonHoanExcel itemExcel = new DieuHanhTonHoanExcel(
                                stt,
                                item.getChiNhanh(),
                                item.getBuuCuc(),
                                item.getHoanTh1d(),
                                item.getHoanTh2d(),
                                item.getHoanTh3d(),
                                item.getHoanThl3d(),
                                item.getHoanThTong(),
                                item.getHoanDh6h(),
                                item.getHoanDh12h(),
                                item.getHoanDh18h(),
                                item.getHoanDh24h(),
                                item.getHoanDhTong(),
                                item.getHoanQh1d(),
                                item.getHoanQh2d(),
                                item.getHoanQh3d(),
                                item.getHoanQh4d(),
                                item.getHoanQh5d(),
                                item.getHoanQh6d(),
                                item.getHoanQh7d(),
                                item.getHoanQhl7d(),
                                item.getHoanQhTong()
                        );
                        listDataExcel.add(itemExcel);
                        stt++;
                    }
                } else {
                    listData = dieuHanhTonHoanPhatRepository.getPhaiHoanDHTChiTietTheoCNBCT(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                    int stt = 1;
                    for (DieuHanhTonHoanResponse item: listData) {
                        DieuHanhTonHoanExcel itemExcel = new DieuHanhTonHoanExcel(
                                stt,
                                item.getChiNhanh(),
                                item.getBuuCuc(),
                                item.getTuyenBuuTa(),
                                item.getHoanTh1d(),
                                item.getHoanTh2d(),
                                item.getHoanTh3d(),
                                item.getHoanThl3d(),
                                item.getHoanThTong(),
                                item.getHoanDh6h(),
                                item.getHoanDh12h(),
                                item.getHoanDh18h(),
                                item.getHoanDh24h(),
                                item.getHoanDhTong(),
                                item.getHoanQh1d(),
                                item.getHoanQh2d(),
                                item.getHoanQh3d(),
                                item.getHoanQh4d(),
                                item.getHoanQh5d(),
                                item.getHoanQh6d(),
                                item.getHoanQh7d(),
                                item.getHoanQhl7d(),
                                item.getHoanQhTong()
                        );
                        listDataExcel.add(itemExcel);
                        stt++;
                    }
                }
            }
        }
        return tryTestDataExcelDHTHoan((List<T>) listDataExcel);
    }

    @Override
    public Page<DieuHanhTonThuResponse> getPhaiThuDHTTongQuan(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, Pageable pageable) {
        Page<DieuHanhTonThuResponse> listData = Page.empty(pageable);
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonThuRepository.getPhaiThuDHTTongQuanTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac, pageable);
            } else {
                listData = dieuHanhTonThuRepository.getPhaiThuDHTTongQuanTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac, pageable);
            }
        }
        return listData;
    }

    @Override
    public Page<DieuHanhTonThuResponse> getPhaiThuDHTChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, String sortKey, String sortType, Integer pageNumber, Integer pageSize) {
        Page<DieuHanhTonThuResponse> pageData = null;
        List<DieuHanhTonThuResponse> listData = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonThuRepository.getPhaiThuDHTChiTietTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac);
            } else {
                if (buuCuc == null || buuCuc.equals("")) {
                    listData = dieuHanhTonThuRepository.getPhaiThuDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                } else {
                    listData = dieuHanhTonThuRepository.getPhaiThuDHTChiTietTheoCNBCT(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                }
            }

            if (sortKey != null && sortType != null) {
                switch (sortKey) {
                    case "thuTh1d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuTh1d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuTh1d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuTh2d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuTh2d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuTh2d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuTh3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuTh3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuTh3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuThl3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuThl3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuThl3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuThTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuThTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuThTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuDh6h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh6h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh6h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuDh12h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh12h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh12h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuDh18h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh18h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh18h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuDh24h":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh24h, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDh24h, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuDhTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDhTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuDhTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuQh1d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh1d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh1d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuQh2d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh2d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh2d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuQh3d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh3d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh3d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuQh4d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh4d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh4d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuQh5d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh5d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQh5d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuQhl5d":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQhl5d, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQhl5d, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    case "thuQhTong":
                        switch (sortType) {
                            case "asc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQhTong, Comparator.nullsLast(Comparator.naturalOrder())));
                                break;
                            case "desc":
                                listData.sort(Comparator.comparing(DieuHanhTonThuResponse::getThuQhTong, Comparator.nullsLast(Comparator.reverseOrder())));
                                break;
                        }
                        break;
                    default:
                        break;
                }
            }
            int start = Math.min(pageNumber * pageSize, listData.size());
            int end = Math.min((pageNumber + 1) * pageSize, listData.size());
            List<DieuHanhTonThuResponse> sublist = listData.subList(start, end);
            pageData = new PageImpl<>(sublist, PageRequest.of(pageNumber, pageSize), listData.size());
        }
        return pageData;
    }

    @Override
    public DieuHanhTonThuResponse getPhaiThuDHTTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) {
        DieuHanhTonThuResponse data = new DieuHanhTonThuResponse();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            data = dieuHanhTonThuRepository.getPhaiThuDHTTong(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
        }
        return data;
    }

    @Override
    public void exportPhaiThuDHTExcelTongHop(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response) {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=DHTThu.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("chiNhanh", "Chi nhánh");
        mapHeader.put("buuCuc", "Bưu cục");
        mapHeader.put("tuyenBuuTa", "Tuyến");
        mapHeader.put("thuTh1d", "Trong hạn 1 ngày");
        mapHeader.put("thuTh2d", "Trong hạn 2 ngày");
        mapHeader.put("thuTh3d", "Trong hạn 3 ngày");
        mapHeader.put("thuThl3d", "Trong hạn trên 3 ngày");
        mapHeader.put("thuThTong", "Trong hạn tổng");
        mapHeader.put("thuDh6h", "Đến hạn 6 giờ");
        mapHeader.put("thuDh12h", "Đến hạn 12 giờ");
        mapHeader.put("thuDh18h", "Đến hạn 18 giờ");
        mapHeader.put("thuDh24h", "Đến hạn 24 giờ");
        mapHeader.put("thuDhTong", "Đến hạn tổng");
        mapHeader.put("thuQh1d", "Quá hạn 1 ngày");
        mapHeader.put("thuQh2d", "Quá hạn 2 ngày");
        mapHeader.put("thuQh3d", "Quá hạn 3 ngày");
        mapHeader.put("thuQh4d", "Quá hạn 4 ngày");
        mapHeader.put("thuQh5d", "Quá hạn 5 ngày");
        mapHeader.put("thuQhl5d", "Quá hạn trên 5 ngày");
        mapHeader.put("thuQhTong", "Quá hạn tổng");
        List<String> header = Arrays.asList(
                "stt",
                "chiNhanh",
                "buuCuc",
                "tuyenBuuTa",
                "thuTh1d",
                "thuTh2d",
                "thuTh3d",
                "thuThl3d",
                "thuThTong",
                "thuDh6h",
                "thuDh12h",
                "thuDh18h",
                "thuDh24h",
                "thuDhTong",
                "thuQh1d",
                "thuQh2d",
                "thuQh3d",
                "thuQh4d",
                "thuQh5d",
                "thuQhl5d",
                "thuQhTong"
        );
        try {
            DieuHanhTonExcelTemplate dieuHanhTonExcelTemplate = new DieuHanhTonExcelTemplate(
                    findDataDHTThu(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac),
                    mapHeader,
                    header,
                    "DHTThu");
            dieuHanhTonExcelTemplate.exportDataToExcel(response);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void exportPhaiThuDHTExcelChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response) {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=DHTThu.xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("stt", "STT");
        mapHeader.put("maPhieuGui", "Mã phiếu gửi");
        mapHeader.put("maTrangThai", "Trạng thái");
        mapHeader.put("maDoiTac", "Mã đối tác");
        mapHeader.put("thoiGianTaoDon", "Ngày tạo");
        mapHeader.put("timeKhachHenLay", "Ngày hẹn thu");
        mapHeader.put("tinhKHGui", "Tỉnh KH gửi");
        mapHeader.put("maKHGui", "Tên KH gửi");
        mapHeader.put("tinhPhat", "Tỉnh KH nhận");
        mapHeader.put("maBuuCucGoc", "Mã bưu cục");
        mapHeader.put("chiNhanh", "Chi nhánh");
        mapHeader.put("maDVViettel", "Mã dịch vụ Viettel");
        mapHeader.put("tongCuoc", "Tổng tiền");
        mapHeader.put("buuTaNhan", "Bưu tá nhận");
        mapHeader.put("tenBuuTa", "Tên bưu tá");
        mapHeader.put("ngayTon", "Ngày tồn");
        mapHeader.put("gioTon", "Giờ tồn (giờ)");
        mapHeader.put("thoiGian", "Ngày báo cáo");
        mapHeader.put("danhGiaDon", "Đánh giá");
        mapHeader.put("thoiGianTon", "Thời gian tồn");
        List<String> header = Arrays.asList(
                "stt",
                "maPhieuGui",
                "maTrangThai",
                "maDoiTac",
                "thoiGianTaoDon",
                "timeKhachHenLay",
                "tinhKHGui",
                "maKHGui",
                "tinhPhat",
                "maBuuCucGoc",
                "chiNhanh",
                "maDVViettel",
                "tongCuoc",
                "buuTaNhan",
                "tenBuuTa",
                "ngayTon",
                "gioTon",
                "thoiGian",
                "danhGiaDon",
                "thoiGianTon"
        );
        try {
            DieuHanhTonExcelTemplate dieuHanhTonExcelTemplate = new DieuHanhTonExcelTemplate(
                    findDataDHTThuDetail(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac),
                    mapHeader,
                    header,
                    "DHTThu");
            dieuHanhTonExcelTemplate.exportDataToExcel(response);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<Map<String, Object>> findDataDHTThuDetail(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) throws NoSuchFieldException, IllegalAccessException {
        List<DieuHanhTonThuDetailResponse> listData = new ArrayList<>();
        List<DieuHanhTonThuExcelDetail> listDataExcel = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            listData = dieuHanhTonThuDetailRepository.getPhaiThuDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
            int stt = 1;
            for (DieuHanhTonThuDetailResponse item: listData) {
                String ngayTon = minusTimeStamp(item.getUpdateTime(), item.getTimeKhachHenLay());
                Double gioTon = getHours(item.getUpdateTime(), item.getTimeKhachHenLay());
                DieuHanhTonThuExcelDetail itemExcel = new DieuHanhTonThuExcelDetail(
                        stt,
                        item.getMaPhieuGui(),
                        item.getMaTrangThai(),
                        item.getMaDoiTac(),
                        item.getThoiGianTaoDon(),
                        item.getTimeKhachHenLay(),
                        item.getTinhNhan(),
                        item.getMaKHGui(),
                        item.getTinhPhat(),
                        item.getMaBuuCucGoc(),
                        item.getTinhNhan(),
                        item.getMaDVViettel(),
                        item.getTongCuoc(),
                        ngayTon,
                        gioTon,
                        item.getThoiGian(),
                        item.getDanhGiaDon(),
                        item.getThoiGianTon()
                );
                listDataExcel.add(itemExcel);
                stt++;
            }
        }
        return tryTestDataExcelDHTThuDetail((List<T>) listDataExcel);
    }

    private List<Map<String, Object>> findDataDHTThu(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac) throws NoSuchFieldException, IllegalAccessException {
        List<DieuHanhTonThuResponse> listData = new ArrayList<>();
        List<DieuHanhTonThuExcel> listDataExcel = new ArrayList<>();
        if (UserContext.getUserData().getIsRoleViewAllKHL().equalsIgnoreCase("true")) {
            List<String> chiNhanhSSO = new ArrayList<>();
            List<String> buuCucSSO = new ArrayList<>();

            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
            }
            if (UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")) {
                chiNhanhSSO = UserContext.getUserData().getListChiNhanhVeriable();
                buuCucSSO = UserContext.getUserData().getListBuuCucVeriable();
            }

            if (chiNhanh == null || chiNhanh.equals("")) {
                listData = dieuHanhTonThuRepository.getPhaiThuDHTChiTietTheoCN(ngayBaoCao, chiNhanh, chiNhanhSSO, buuCucSSO, maDoiTac);
                int stt = 1;
                for (DieuHanhTonThuResponse item: listData) {
                    DieuHanhTonThuExcel itemExcel = new DieuHanhTonThuExcel(
                            stt,
                            item.getChiNhanh(),
                            item.getThuTh1d(),
                            item.getThuTh2d(),
                            item.getThuTh3d(),
                            item.getThuThl3d(),
                            item.getThuThTong(),
                            item.getThuDh6h(),
                            item.getThuDh12h(),
                            item.getThuDh18h(),
                            item.getThuDh24h(),
                            item.getThuDhTong(),
                            item.getThuQh1d(),
                            item.getThuQh2d(),
                            item.getThuQh3d(),
                            item.getThuQh4d(),
                            item.getThuQh5d(),
                            item.getThuQhl5d(),
                            item.getThuQhTong()
                    );
                    listDataExcel.add(itemExcel);
                    stt++;
                }
            } else {
                if (buuCuc == null || buuCuc.equals("")) {
                    listData = dieuHanhTonThuRepository.getPhaiThuDHTChiTietTheoCNBC(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                    int stt = 1;
                    for (DieuHanhTonThuResponse item: listData) {
                        DieuHanhTonThuExcel itemExcel = new DieuHanhTonThuExcel(
                                stt,
                                item.getChiNhanh(),
                                item.getBuuCuc(),
                                item.getThuTh1d(),
                                item.getThuTh2d(),
                                item.getThuTh3d(),
                                item.getThuThl3d(),
                                item.getThuThTong(),
                                item.getThuDh6h(),
                                item.getThuDh12h(),
                                item.getThuDh18h(),
                                item.getThuDh24h(),
                                item.getThuDhTong(),
                                item.getThuQh1d(),
                                item.getThuQh2d(),
                                item.getThuQh3d(),
                                item.getThuQh4d(),
                                item.getThuQh5d(),
                                item.getThuQhl5d(),
                                item.getThuQhTong()
                        );
                        listDataExcel.add(itemExcel);
                        stt++;
                    }
                } else {
                    listData = dieuHanhTonThuRepository.getPhaiThuDHTChiTietTheoCNBCT(ngayBaoCao, chiNhanh, buuCuc, chiNhanhSSO, buuCucSSO, maDoiTac);
                    int stt = 1;
                    for (DieuHanhTonThuResponse item: listData) {
                        DieuHanhTonThuExcel itemExcel = new DieuHanhTonThuExcel(
                                stt,
                                item.getChiNhanh(),
                                item.getBuuCuc(),
                                item.getTuyenBuuTa(),
                                item.getThuTh1d(),
                                item.getThuTh2d(),
                                item.getThuTh3d(),
                                item.getThuThl3d(),
                                item.getThuThTong(),
                                item.getThuDh6h(),
                                item.getThuDh12h(),
                                item.getThuDh18h(),
                                item.getThuDh24h(),
                                item.getThuDhTong(),
                                item.getThuQh1d(),
                                item.getThuQh2d(),
                                item.getThuQh3d(),
                                item.getThuQh4d(),
                                item.getThuQh5d(),
                                item.getThuQhl5d(),
                                item.getThuQhTong()
                        );
                        listDataExcel.add(itemExcel);
                        stt++;
                    }
                }
            }
        }
        return tryTestDataExcelDHTThu((List<T>) listDataExcel);
    }

    public List<Map<String, Object>> tryTestDataExcelDHTPhat(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                DieuHanhTonPhatExcel x1 = (DieuHanhTonPhatExcel) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public List<Map<String, Object>> tryTestDataExcelDHTPhatDetail(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                DieuHanhTonPhatExcelDetail x1 = (DieuHanhTonPhatExcelDetail) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public List<Map<String, Object>> tryTestDataExcelDHTHoanDetail(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                DieuHanhTonHoanExcelDetail x1 = (DieuHanhTonHoanExcelDetail) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public List<Map<String, Object>> tryTestDataExcelDHTThuDetail(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                DieuHanhTonThuExcelDetail x1 = (DieuHanhTonThuExcelDetail) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public List<Map<String, Object>> tryTestDataExcelDHTHoan(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                DieuHanhTonHoanExcel x1 = (DieuHanhTonHoanExcel) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public List<Map<String, Object>> tryTestDataExcelDHTThu(List<T> list) {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!list.isEmpty()) {
            int i = 0;
            for (T x : list) {
                DieuHanhTonThuExcel x1 = (DieuHanhTonThuExcel) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    public String minusTimeStamp(String time1, String time2) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try {
            LocalDateTime arrival = LocalDateTime.parse(time2, fmt);
            LocalDateTime scheduled = LocalDateTime.parse(time1, fmt);
            long totalSeconds = ChronoUnit.SECONDS.between(arrival, scheduled);

            long secondsInDay = 24*60*60;
            long secondsInHour = 60*60;
            long secondsInMinute = 60;

            long days = totalSeconds / secondsInDay;
            long remainingSeconds = totalSeconds % secondsInDay;

            long hours = Math.abs(remainingSeconds / secondsInHour);
            remainingSeconds = remainingSeconds % secondsInHour;

            long minutes = Math.abs(remainingSeconds / secondsInMinute);
            long seconds = Math.abs(remainingSeconds % secondsInMinute);

            StringBuffer text = new StringBuffer();
            text.append(days).append(" ngày ");

            if (hours < 10) {
                text.append("0").append(hours).append(":");
            } else {
                text.append(hours).append(":");
            }
            if (minutes < 10) {
                text.append("0").append(minutes).append(":");
            } else {
                text.append(minutes).append(":");
            }
            if (seconds < 10) {
                text.append("0").append(seconds);
            } else {
                text.append(seconds);
            }
            return text.toString();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Date Time incorrect format");
        }
    }

    public Double getHours(String time1, String time2) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        try {
            LocalDateTime arrival = LocalDateTime.parse(time2, fmt);
            LocalDateTime scheduled = LocalDateTime.parse(time1, fmt);

            long totalSeconds = ChronoUnit.SECONDS.between(arrival, scheduled);
            double hours = (double) totalSeconds/(60*60);

            return (double) Math.round(hours * 100) / 100;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Date Time incorrect format");
        }
    }
}
