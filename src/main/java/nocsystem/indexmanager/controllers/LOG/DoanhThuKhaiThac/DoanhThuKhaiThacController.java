package nocsystem.indexmanager.controllers.LOG.DoanhThuKhaiThac;

import nocsystem.indexmanager.config.Pagination;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacRequestBody;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacResponse;
import nocsystem.indexmanager.services.LOG.DoanhThuKhaiThacService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class DoanhThuKhaiThacController {
    @Autowired
    private DoanhThuKhaiThacService doanhThuKhaiThacService;

    @PostMapping("/log/doanh-thu-khai-thac")
    public ResponseEntity<SimpleAPIResponseV2> doanhThuKhaiThac(@RequestBody DoanhThuKhaiThacRequestBody body) throws SQLException {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        List<DoanhThuKhaiThacResponse> listResponse = doanhThuKhaiThacService.doanhThuKhaiThac(body);
        Long totalRecord = doanhThuKhaiThacService.doanhThuKhaiThacTotalRecord(
                body.getNgayBaoCao(),
                body.getLuyKe(),
                body.getChiNhanh(),
                body.getBuuCuc(),
                body.getTtkt(),
                body.getTtktCha(),
                body.getMaDichVu());
        Pagination pagination = new Pagination(body.getPage(), body.getPageSize(), totalRecord);
        response.setData(listResponse);
        response.setPagination(pagination);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/log/doanh-thu-khai-thac/tong")
    public SimpleAPIResponse doanhThuKhaiThacTong(@RequestBody DoanhThuKhaiThacRequestBody body) throws SQLException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        DoanhThuKhaiThacResponse listResponse = doanhThuKhaiThacService.doanhThuKhaiThacTong(body);
        response.setData(listResponse);
        return response;
    }

    @PostMapping("/log/doanh-thu-khai-thac/excel")
    public void doanhThuKhaiThacExcel(
            HttpServletResponse response,
            @RequestBody DoanhThuKhaiThacRequestBody body) throws IOException, IllegalAccessException, SQLException {
        doanhThuKhaiThacService.doanhThuKhaiThacExcel(response, body);
    }

    @PostMapping("/log/doanh-thu-khai-thac/excel-trinh-ky")
    public void doanhThuKhaiThacExcelTrinhKy(
        HttpServletResponse response,
        @RequestBody DoanhThuKhaiThacRequestBody body) throws IOException, IllegalAccessException, SQLException {
        doanhThuKhaiThacService.doanhThuKhaiThacExcelTrinhKy(response, body);
    }
}
