package nocsystem.indexmanager.controllers.LOG.TonTaiBcGoc;

import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.services.LOG.TonChuaKetNoiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;

@RestController
@RequestMapping("/api/v1")
public class TonChuaKetNoiController {

    @Autowired
    private TonChuaKetNoiService tonChuaKetNoiService;

    @GetMapping("/kpi-log/ton-chua-ket-noi-tai-bc-goc")
    public SimpleAPIResponseWithSum getTonChuaKetNoi(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "buuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "dichVu", defaultValue = "3") Integer dichVu,
            @RequestParam(value = "loaiDon", defaultValue = "") String loaiDon,
            @RequestParam(value = "order", defaultValue = "") String order,
            @RequestParam(value = "orderBy", defaultValue = "") String orderBy,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "30") Integer pageSize
    ) throws SQLException {
        SimpleAPIResponseWithSum response = tonChuaKetNoiService.tonChuaKetNoi(ngayBaoCao, maChiNhanh, maBuuCuc, dichVu, loaiDon, page, pageSize, order, orderBy);
        return response;
    }


    @GetMapping("/kpi-log/ton-chua-ket-noi-tai-bc-goc-excel")
    public void getExcel(
            HttpServletResponse response,
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "buuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "dichVu", defaultValue = "3") Integer dichVu,
            @RequestParam(value = "loaiDon", defaultValue = "") String loaiDon
    ) throws IOException, IllegalAccessException, SQLException {
        tonChuaKetNoiService.exportExcelTonChuaKetNoi(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                dichVu,
                loaiDon
        );
    }
}
