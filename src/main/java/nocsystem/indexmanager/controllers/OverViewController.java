package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuCPResponse;
import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuResponse;
import nocsystem.indexmanager.models.Response.NotificationCSKDBody;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuSMSDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticV1Dto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOriginDto;
import nocsystem.indexmanager.services.ChiSoKinhDoanh.ChiSoKinDoanhService;
import nocsystem.indexmanager.services.TienDoDoanhThu.TienDoDoanhThuService;
import nocsystem.indexmanager.services.TienDoDoanhThuCP.TienDoDoanhThuCPService;
import nocsystem.indexmanager.services.TienDoDoanhThuLogistic.TienDoDoanhThuLogisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class OverViewController {
    @Autowired
    private TienDoDoanhThuLogisticService tienDoDoanhThuLogisticService;
    @Autowired
    private ChiSoKinDoanhService chiSoKinDoanhService;

    @Autowired
    private TienDoDoanhThuCPService tienDoDoanhThuCPService;

    @Autowired
    private TienDoDoanhThuService tienDoDoanhThuService;

    @GetMapping("/cskd-tong-doanh-thu")
    public SimpleAPIResponse tongDoanhThuCP(
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc
    ) {
        CSKDTongDoanhThuOriginDto cskdTongDoanhThu =
                chiSoKinDoanhService.findCSKDTongDoanhThu(toTime, maChiNhanh, maBuuCuc);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(cskdTongDoanhThu);
        return simpleAPIResponse;
    }

    @GetMapping("/cskd-tien-do-doanh-thu")
    public SimpleAPIResponse tienDoDoanhThu(
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        List<CSKDTienDoDoanhThuV2Dto> cskdTienDoDoanhThu =
                chiSoKinDoanhService.findCSKDTienDoDoanhThu(maChiNhanh, maBuuCuc, toTime, detail);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(cskdTienDoDoanhThu);
        return simpleAPIResponse;
    }

    @GetMapping("/tong-quan-doanh-thu-logistic")
    public SimpleAPIResponse tongQuanTienDoDoanhThuLogistic(
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "loai_dichvu", defaultValue = "0") Integer loaiDichVu,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuCPRS =
                tienDoDoanhThuLogisticService.findDoanhThuLogistic(maChiNhanh, maBuuCuc, toTime, detail, loaiDichVu);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDoanhThuCPRS);
        return simpleAPIResponse;
    }

    @GetMapping("/tong-quan-doanh-thu-cp")
    public SimpleAPIResponse tongQuanTienDoDoanhThuChuyenPhat(
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        List<TienDoDoanhThuCPV1Dto> tienDoDTChuyenPhatDto =
                tienDoDoanhThuCPService.findTienDoDoanhThuCP(maChiNhanh, maBuuCuc, toTime, detail);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDTChuyenPhatDto);
        return simpleAPIResponse;
    }

    @GetMapping("/bieu-do-thuc-hien-ngay-tien-do-dt")
    public SimpleAPIResponse bieuDoTtTienDoDoanhThu(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        BieuDoTtTienDoDoanhThuResponse tienDoDTChuyenPhatDto =
                tienDoDoanhThuService.bieuDoTtTienDoDoanhThu(maChiNhanh, maBuuCuc, toTime);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDTChuyenPhatDto);
        return simpleAPIResponse;
    }

    @GetMapping("/bieu-do-thu-hien-ngay-tien-do-dt-cp")
    public SimpleAPIResponse bieuDoThucHienNgayTienDoDtCp(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        BieuDoTtTienDoDoanhThuCPResponse tienDoDTChuyenPhatDto =
                tienDoDoanhThuCPService.bieuDoTtTienDoDoanhThu(maChiNhanh, maBuuCuc, toTime);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDTChuyenPhatDto);
        return simpleAPIResponse;
    }

    @GetMapping("/bieu-do-ti-le-tang-truong-logistic")
    public SimpleAPIResponse bieuDoTiLeTangTruongLogistic(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        return tienDoDoanhThuLogisticService.bieuDoTtTienDoDoanhThu(maChiNhanh, maBuuCuc, toTime);
    }

    @GetMapping("notification/cskd-tien-do-doanh-thu")
    public SimpleAPIResponse cskdSMSData(
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        CSKDTienDoDoanhThuSMSDto cskdTienDoDoanhThu =
                chiSoKinDoanhService.findCSKDTienDoDoanhThuSMS(maChiNhanh, maBuuCuc, toTime, detail);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(cskdTienDoDoanhThu);
        return simpleAPIResponse;
    }

    @PostMapping("notification/cskd-tien-do-doanh-thu-cn")
    public SimpleAPIResponse cskdSMSDataCN(
            @RequestBody NotificationCSKDBody body
    ) {
        List<CSKDTienDoDoanhThuSMSDto> cskdTienDoDoanhThu =
                chiSoKinDoanhService.findCSKDTienDoDoanhThuSMSCN(body.getChiNhanh(), body.getToTime());
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(cskdTienDoDoanhThu);
        return simpleAPIResponse;
    }
}
