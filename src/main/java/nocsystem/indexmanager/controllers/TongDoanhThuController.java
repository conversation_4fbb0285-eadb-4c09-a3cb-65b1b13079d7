package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuCNV1ResDto;
import nocsystem.indexmanager.services.TongDoanhThu.TongDoanhThuBCService;
import nocsystem.indexmanager.services.TongDoanhThu.TongDoanhThuCNService;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.LocalDate;

@RestController
@RequestMapping("/api/v1")
public class TongDoanhThuController {
    @Autowired
    private TongDoanhThuBCService tongDoanhThuBCService;
    @Autowired
    private TongDoanhThuCNService tongDoanhThuCNService;

    private final OkHttpClient client = new OkHttpClient();

    @GetMapping("/tong-doanh-thu")
    public SimpleAPIResponse revenue(
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime
    ) throws IOException {
        if (page > 0) page--;

        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            CustomizeDataPage<TongDoanhThuCNV1ResDto> tongDTCN =
                    tongDoanhThuCNService.findAllData(maChiNhanh, maBuuCuc, toTime, page, pageSize);
            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            simpleAPIResponse.setData(tongDTCN);
            return simpleAPIResponse;
        }

        CustomizeDataPage<TongDoanhThuBCV1ResDto> tongDTBC =
                tongDoanhThuBCService.findAllData(maBuuCuc, maChiNhanh, toTime, page, pageSize);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tongDTBC);
        return simpleAPIResponse;
    }
}