package nocsystem.indexmanager.controllers.baocao_taichinh;


import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.services.baocao_taichinh.*;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoChartCommon;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoCommon;
import nocsystem.indexmanager.services.baocao_taichinh.response.ShowBaoCaoResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1")
public class KeHoachTaiChinhController {

    private static final Logger log = LoggerFactory.getLogger(KeHoachTaiChinhController.class);

    @Autowired
    TaiChinhServices taiChinhServices;

    @Autowired
    KeHoachServices keHoachServices;

    @Autowired
    ShowBaoCaoTaiChinhServices showBaoCaoTaiChinhServices;

    @Autowired
    ChartBaoCaoService chartBaoCaoService;


    @PostMapping("/import-excel-taichinh")
    public ResponseEntity<?> importExcelTaiChinh(@RequestParam("file") MultipartFile file) {

        SimpleAPIResponse spa = new SimpleAPIResponse();

        try {
            taiChinhServices.readExcelTaiChinh(file);
            spa.setData("OK");
            return ResponseEntity.ok().header("application/json;charset=UTF-8").body(spa);
        }catch (Exception e) {
            e.printStackTrace();
            spa.setMessage("Import Fail!");
            spa.setError(0);
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(spa);
            return ResponseEntity.ok().body(spa);

        }

    }
    @PostMapping("/import-excel-kehoach")
    public ResponseEntity<?> importExcelKeHoach(@RequestParam("file") MultipartFile file) {

        SimpleAPIResponse spa = new SimpleAPIResponse();

        try {
            keHoachServices.readExcelKeHoach(file);
            spa.setData("OK");
            return ResponseEntity.ok().header("application/json;charset=UTF-8").body(spa);
        }catch (Exception e) {
            e.printStackTrace();
            spa.setMessage("Import Fail!");
            spa.setError(0);
            return ResponseEntity.ok().body(spa);
        }
    }


    @PostMapping("/bao-cao-tai-chinh")
    public ResponseEntity<?> baoCaoTaiChinhNoc(@RequestBody BaoCaoTaiChinhBody body) {
        SimpleAPIResponse spa = new SimpleAPIResponse();
        List<ShowBaoCaoResponse> baoCaoResponses;
        try {
            baoCaoResponses = showBaoCaoTaiChinhServices.showBaoCao(body);
            spa.setData(baoCaoResponses);
            return ResponseEntity.ok().header("application/json;charset=UTF-8").body(spa);
        }catch (Exception e) {
            e.printStackTrace();
            spa.setData(new SimpleAPIResponse());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("lỗi hệ thống");
        }
    }

    @PostMapping("/chart/bao-cao-tai-chinh")
    public ResponseEntity<?> chartTaiChinhNoc(@RequestBody BaoCaoChartTaiChinhBody body) {
        SimpleAPIResponse spa = new SimpleAPIResponse();
        try {

            List<BaoCaoChartCommon> list = chartBaoCaoService.chartBaoCaoTaiChinh(body);

            spa.setData(list);
            return ResponseEntity.ok().header("application/json;charset=UTF-8").body(spa);
        }catch (Exception e) {
            e.printStackTrace();
            spa.setData(new SimpleAPIResponse());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("lỗi hệ thống");
        }
    }

}
