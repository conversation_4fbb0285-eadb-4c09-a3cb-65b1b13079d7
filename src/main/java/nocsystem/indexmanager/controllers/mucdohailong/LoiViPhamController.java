package nocsystem.indexmanager.controllers.mucdohailong;

import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.config.Pagination;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.mucdohailong.LoiViPhamDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TopViPhamNhieuNhatDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyTrongNguyenNhanKHLCaoNhatDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyTrongNguyenNhanKHLDto;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import nocsystem.indexmanager.services.mucdohailong.LoiViPhamService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/api/v1")
public class LoiViPhamController {
    private final LoiViPhamService loiViPhamService;

    public LoiViPhamController(LoiViPhamService loiViPhamService) {
        this.loiViPhamService = loiViPhamService;
    }

    @PostMapping("/loi-vi-pham/bao-cao-tong-hop")
    public ResponseEntity<SimpleAPIResponseV2> getBaoCaoTongHop(@RequestBody GetBaoCaoTyLeHaiLongRq request) throws NoSuchFieldException, IllegalAccessException {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        PageWithTotalNumber<LoiViPhamDto> list = loiViPhamService.getBaoCaoTongHop(request);
        if (!Objects.isNull(list)) {
            Pagination pageContent = new Pagination(request.getPage(), request.getSize(), list.getTotal());
            response.setData(list.getContent());
            response.setPagination(pageContent);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/loi-vi-pham/bao-cao-tong-hop/export-excel")
    public ResponseEntity<SimpleAPIResponse> exportExcelBaoCaoTongHop(@RequestBody GetBaoCaoTyLeHaiLongRq request,
                                                                      HttpServletResponse httpServletResponse) throws IOException, NoSuchFieldException, IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        loiViPhamService.exportExcelBaoCaoTongHop(request, httpServletResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/nguyen-nhan-khong-hai-long/ty-trong-theo-nhom")
    public ResponseEntity<SimpleAPIResponse> getTyTrongNguyenNhanKHL(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                     @RequestParam(value = "vung", required = false) List<String> vung,
                                                                     @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                     @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<TyTrongNguyenNhanKHLDto> list = loiViPhamService.getTyTrongNguyenNhanKHL(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/nguyen-nhan-khong-hai-long/top-ty-trong-cao-nhat")
    public ResponseEntity<SimpleAPIResponse> getTopTyTrongCaoNhat(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                  @RequestParam(value = "vung", required = false) List<String> vung,
                                                                  @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                  @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<TyTrongNguyenNhanKHLCaoNhatDto> list = loiViPhamService.getTopTyTrongCaoNhat(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/loi-vi-pham/top-vi-pham-nhieu-nhat")
    public ResponseEntity<SimpleAPIResponse> getTopViPhamNhieuNhat(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                   @RequestParam(value = "vung", required = false) List<String> vung,
                                                                   @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                   @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc,
                                                                   @RequestParam(value = "type", required = false) String type) throws IOException, NoSuchFieldException, IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<TopViPhamNhieuNhatDto> list = loiViPhamService.getTopViPhamNhieuNhat(ngayBaoCao, vung, maChiNhanh, maBuuCuc, type);
        response.setData(list);
        return ResponseEntity.ok(response);
    }
}
