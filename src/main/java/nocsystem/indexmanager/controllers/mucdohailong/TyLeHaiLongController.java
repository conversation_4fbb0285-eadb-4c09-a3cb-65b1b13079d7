package nocsystem.indexmanager.controllers.mucdohailong;

import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.config.Pagination;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.mucdohailong.*;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRqExcel;
import nocsystem.indexmanager.services.mucdohailong.TyLeHaiLongService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/api/v1")
public class TyLeHaiLongController {
    private final TyLeHaiLongService tyLeHaiLongService;

    public TyLeHaiLongController(TyLeHaiLongService tyLeHaiLongService) {
        this.tyLeHaiLongService = tyLeHaiLongService;
    }

    @GetMapping("/muc-do-hai-long/ty-le-hai-long-dashboard")
    public ResponseEntity<SimpleAPIResponse> getTyLeHaiLongDashBoard(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                     @RequestParam(value = "vung", required = false) List<String> vung,
                                                                     @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                     @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) throws IOException {
        List<TyLeHaiLongDashboardDto> list = new ArrayList<>();
        SimpleAPIResponse response = new SimpleAPIResponse();
        list = tyLeHaiLongService.getTyLeHaiLongDashBoard(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/muc-do-hai-long/bao-cao-tong-hop")
    public ResponseEntity<SimpleAPIResponseV2> getBaoCaoTongHop(@RequestBody GetBaoCaoTyLeHaiLongRq request) throws NoSuchFieldException, IllegalAccessException {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        PageWithTotalNumber<TyLeHaiLongDto> list = tyLeHaiLongService.getBaoCaoTongHop(request);
        if (!Objects.isNull(list)) {
            Pagination pageContent = new Pagination(request.getPage(), request.getSize(), list.getTotal());
            response.setData(list.getContent());
            response.setPagination(pageContent);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/muc-do-hai-long/bao-cao-tong-hop/export-excel")
    public ResponseEntity<SimpleAPIResponse> exportExcelBaoCaoTongHop(@RequestBody GetBaoCaoTyLeHaiLongRq request,
                                                                      HttpServletResponse httpServletResponse) throws IOException, NoSuchFieldException, IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        tyLeHaiLongService.exportExcelBaoCaoTongHop(request, httpServletResponse);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/muc-do-hai-long/bao-cao-chi-tiet/export-excel")
    public ResponseEntity<SimpleAPIResponse> exportExcelBaoCaoChiTiet(@RequestBody GetBaoCaoTyLeHaiLongRqExcel request, HttpServletResponse httpServletResponse) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        tyLeHaiLongService.exportExcelBaoCaoChiTiet(request, httpServletResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/top-chi-nhanh-darhboard")
    public ResponseEntity<SimpleAPIResponse> getTopTenChiNhanhDashboard(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                        @RequestParam(value = "vung", required = false) List<String> vung,
                                                                        @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                        @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc,
                                                                        @RequestParam(value = "type", required = false) String type) throws IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<TopTenChiNhanhDto> list = tyLeHaiLongService.getTopTenChiNhanhDashboard(ngayBaoCao, vung, maChiNhanh, maBuuCuc, type);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/ty-le-hai-long-with-kpi-luy-ke")
    public ResponseEntity<SimpleAPIResponse> getTyLeHaiLongWithKPILuyKe(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                            @RequestParam(value = "vung", required = false) List<String> vung,
                                                                            @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                            @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) throws IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        TyLeHaiLongWithKPIDashboardDto list = tyLeHaiLongService.getTyLeHaiLongWithKPILuyKe(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/ty-le-hai-long-with-kpi-theo-ngay")
    public ResponseEntity<SimpleAPIResponse> getTyLeHaiLongWithKPITheoNgay(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                           @RequestParam(value = "vung", required = false) List<String> vung,
                                                                           @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                           @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) throws IllegalAccessException, IOException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<TyLeHaiLongWithKPIDashboardDto> list = tyLeHaiLongService.getTyLeHaiLongWithKPITheoNgay(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/chi-nhanh-dat-kpi-luy-ke")
    public ResponseEntity<SimpleAPIResponse> getChiNhanhDatKPILuyKe(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                        @RequestParam(value = "vung", required = false) List<String> vung,
                                                                        @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                        @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) throws IOException, IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        ChiNhanhDatKPIDto list = tyLeHaiLongService.getChiNhanhDatKPILuyKe(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/chi-nhanh-dat-kpi-theo-ngay")
    public ResponseEntity<SimpleAPIResponse> getChiNhanhDatKPITheoNgay(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                       @RequestParam(value = "vung", required = false) List<String> vung,
                                                                       @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                                       @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) throws IOException, IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<ChiNhanhDatKPIDto> list = tyLeHaiLongService.getChiNhanhDatKPITheoNgay(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/updated_time")
    public ResponseEntity<SimpleAPIResponse> getUpdatedTime(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        String updatedTime = tyLeHaiLongService.getUpdatedTime(ngayBaoCao);
        response.setData(updatedTime);
        return ResponseEntity.ok(response);
    }
}
