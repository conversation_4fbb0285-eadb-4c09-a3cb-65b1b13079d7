package nocsystem.indexmanager.controllers.mucdohailong.mobile;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.MucDoHaiLongOverview;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.TyLeKHDto;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.TyTrongNguyenNhanKHLMobileDto;
import nocsystem.indexmanager.services.mucdohailong.TyLeHaiLongService;
import nocsystem.indexmanager.services.mucdohailong.mobile.MucDoHaiLongMobileService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class MucDoHaiLongMobileController {

    private final MucDoHaiLongMobileService mucDoHaiLongMobileService;

    private final TyLeHaiLongService tyLeHaiLongService;

    public MucDoHaiLongMobileController(MucDoHaiLongMobileService mucDoHaiLongMobileService, TyLeHaiLongService tyLeHaiLongService) {
        this.mucDoHaiLongMobileService = mucDoHaiLongMobileService;
        this.tyLeHaiLongService = tyLeHaiLongService;
    }


    @GetMapping("/dash-mobile/muc-do-hai-long/ty-le-khach-hang")
    public ResponseEntity<SimpleAPIResponse> getTyLeKhachHang(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                              @RequestParam(value = "maChiNhanh", required = false) String maChiNhanh,
                                                              @RequestParam(value = "maBuuCuc", required = false) String maBuuCuc,
                                                              @RequestParam(value = "type", required = false) String type,
                                                              @RequestParam(value = "name", required = false) String name) {
        TyLeKHDto tyLeKHDto = new TyLeKHDto();
        SimpleAPIResponse response = new SimpleAPIResponse();
        tyLeKHDto = mucDoHaiLongMobileService.getTyLeKhachHang(ngayBaoCao, maChiNhanh, maBuuCuc, type, name);
        response.setData(tyLeKHDto);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dash-mobile/muc-do-hai-long/nguyen-nhan-khong-hai-long")
    public ResponseEntity<SimpleAPIResponse> getNguyenNhanKhongHaiLong(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                       @RequestParam(value = "maChiNhanh", required = false) String maChiNhanh,
                                                                       @RequestParam(value = "maBuuCuc", required = false) String maBuuCuc,
                                                                       @RequestParam(value = "type", required = false) String type) {
        List<TyTrongNguyenNhanKHLMobileDto> tyTrongNguyenNhanKHLMobileDto = new ArrayList<>();
        SimpleAPIResponse response = new SimpleAPIResponse();
        tyTrongNguyenNhanKHLMobileDto = mucDoHaiLongMobileService.getNguyenNhanKhongHaiLong(ngayBaoCao, maChiNhanh, maBuuCuc, type);
        response.setData(tyTrongNguyenNhanKHLMobileDto);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dash-mobile/muc-do-hai-long/so-loi-nghiem-trong")
    public ResponseEntity<SimpleAPIResponse> getSoLoiNghiemTrong(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                 @RequestParam(value = "maChiNhanh", required = false) String maChiNhanh,
                                                                 @RequestParam(value = "maBuuCuc", required = false) String maBuuCuc,
                                                                 @RequestParam(value = "type", required = false) String type) {
        List<TyTrongNguyenNhanKHLMobileDto> tyTrongNguyenNhanKHLMobileDto = new ArrayList<>();
        SimpleAPIResponse response = new SimpleAPIResponse();
        tyTrongNguyenNhanKHLMobileDto = mucDoHaiLongMobileService.getSoLoiNghiemTrong(ngayBaoCao, maChiNhanh, maBuuCuc, type);
        response.setData(tyTrongNguyenNhanKHLMobileDto);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dash-mobile/muc-do-hai-long/nguyen-nhan-khong-hai-long/details")
    public ResponseEntity<SimpleAPIResponse> getNguyenNhanKhongHaiLongDetails(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                              @RequestParam(value = "maChiNhanh", required = false) String maChiNhanh,
                                                                              @RequestParam(value = "maBuuCuc", required = false) String maBuuCuc,
                                                                              @RequestParam(value = "type", required = false) String type,
                                                                              @RequestParam(value = "name", required = false) String name) {
        List<TyTrongNguyenNhanKHLMobileDto> tyTrongNguyenNhanKHLMobileDto = new ArrayList<>();
        SimpleAPIResponse response = new SimpleAPIResponse();
        tyTrongNguyenNhanKHLMobileDto = mucDoHaiLongMobileService.getNguyenNhanKhongHaiLongDetails(ngayBaoCao, maChiNhanh, maBuuCuc, type, name);
        response.setData(tyTrongNguyenNhanKHLMobileDto);
        return ResponseEntity.ok(response);
    }

//    @GetMapping("/dash-mobile/muc-do-hai-long/updated_time")
//    public ResponseEntity<SimpleAPIResponse> getUpdatedTime(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
//        SimpleAPIResponse response = new SimpleAPIResponse();
//        String updatedTime = tyLeHaiLongService.getUpdatedTime(ngayBaoCao);
//        response.setData(updatedTime);
//        return ResponseEntity.ok(response);
//    }

    @GetMapping("/dash-mobile/muc-do-hai-long-overview")
    public ResponseEntity<SimpleAPIResponse> getTyLeKhachHangOverview(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                      @RequestParam(value = "maChiNhanh", required = false) String maChiNhanh,
                                                                      @RequestParam(value = "maBuuCuc", required = false) String maBuuCuc,
                                                                      @RequestParam(value = "type", required = false) String type) {
        MucDoHaiLongOverview mucDoHaiLongOverview = new MucDoHaiLongOverview();
        SimpleAPIResponse response = new SimpleAPIResponse();
        mucDoHaiLongOverview = mucDoHaiLongMobileService.getTyLeKhachHangOverview(ngayBaoCao, maChiNhanh, maBuuCuc, type);
        mucDoHaiLongOverview.setUpdatedTime(tyLeHaiLongService.getUpdatedTime(ngayBaoCao));
        response.setData(mucDoHaiLongOverview);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/remove-value-hashmap")
    public ResponseEntity<SimpleAPIResponse> removeValueHashMap(@RequestParam(value = "key", required = false) String key) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        System.out.println("askhdfiouahiodf " + ChiNhanhVungMap.collection);
        String value = ChiNhanhVungMap.collection.get(key);
        if (value != null) {
            ChiNhanhVungMap.removeValue(key);
            response.setData("Delete area code by branch code successfully!");
        } else {
            response.setData("Not found area code to remove");
        }
        return ResponseEntity.ok(response);
    }

    @GetMapping("/muc-do-hai-long/put-value-hashmap")
    public ResponseEntity<SimpleAPIResponse> putValueHashMap(@RequestParam(value = "key", required = false) String key,
                                                             @RequestParam(value = "value", required = false) String value) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        String existedValue = ChiNhanhVungMap.collection.get(key);
        if (existedValue != null) {
            ChiNhanhVungMap.removeValue(key);
            response.setData("Value with this key existed!");
        } else {
            ChiNhanhVungMap.putNewValue(key, value);
            response.setData("Put new value successfully!");
        }
        return ResponseEntity.ok(response);
    }
}
