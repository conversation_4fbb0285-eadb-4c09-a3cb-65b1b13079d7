package nocsystem.indexmanager.controllers.mucdohailong.mobile;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;

@Getter
@Setter
public class ChiNhanhVungMap {

    public static HashMap<String, String> collection = new HashMap<>();

    static {
        collection.put("TCT", "TCT");

        collection.put("BGG", "1");
        collection.put("BKN", "1");
        collection.put("CBG", "1");
        collection.put("HGG", "1");
        collection.put("LSN", "1");
        collection.put("TNN", "1");
        collection.put("TQG", "1");

        collection.put("DBN", "2");
        collection.put("HBH", "2");
        collection.put("LCI", "2");
        collection.put("LCU", "2");
        collection.put("PHO", "2");
        collection.put("SLA", "2");
        collection.put("VPC", "2");
        collection.put("YBN", "2");

        collection.put("BNH", "3");
        collection.put("HDG", "3");
        collection.put("HNM", "3");
        collection.put("HPG", "3");
        collection.put("HYN", "3");
        collection.put("NDH", "3");
        collection.put("QNH", "3");
        collection.put("TBH", "3");

        collection.put("HTH", "4");
        collection.put("NAN", "4");
        collection.put("NBH", "4");
        collection.put("QBH", "4");
        collection.put("THA", "4");

        collection.put("DNG", "5");
        collection.put("HUE", "5");
        collection.put("KTM", "5");
        collection.put("QNI", "5");
        collection.put("QNM", "5");
        collection.put("QTI", "5");

        collection.put("BDH", "6");
        collection.put("BTN", "6");
        collection.put("DLK", "6");
        collection.put("GLI", "6");
        collection.put("KHA", "6");
        collection.put("LDG", "6");
        collection.put("NTN", "6");
        collection.put("PYN", "6");

        collection.put("BDG", "7");
        collection.put("BPC", "7");
        collection.put("BTE", "7");
        collection.put("DKG", "7");
        collection.put("DNI", "7");
        collection.put("LAN", "7");
        collection.put("TGG", "7");
        collection.put("TNH", "7");
        collection.put("VTU", "7");

        collection.put("AGG", "8");
        collection.put("BLU", "8");
        collection.put("CMU", "8");
        collection.put("CTO", "8");
        collection.put("DTP", "8");
        collection.put("HUG", "8");
        collection.put("KGG", "8");
        collection.put("STG", "8");
        collection.put("TVH", "8");
        collection.put("VLG", "8");

        collection.put("HNI", "9");

        collection.put("HCM", "10");

        collection.put("LOG", "LOG");
    }

    public static String removeValue(String key) {
        return collection.remove(key);
    }

    public static String putNewValue(String key, String value) {
        return collection.put(key, value);
    }
}
