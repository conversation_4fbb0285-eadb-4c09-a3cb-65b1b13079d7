package nocsystem.indexmanager.controllers.mucdohailong;

import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.config.Pagination;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.mucdohailong.TyLePhanHoiDashboardDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyLePhanHoiDto;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import nocsystem.indexmanager.services.mucdohailong.TyLePhanHoiService;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/api/v1")
public class TyLePhanHoiController {
    private final TyLePhanHoiService tyLePhanHoiService;

    public TyLePhanHoiController(TyLePhanHoiService tyLePhanHoiService) {
        this.tyLePhanHoiService = tyLePhanHoiService;
    }

    @GetMapping("/ty-le-phan-hoi/ty-le-phan-hoi-dashboard")
    public ResponseEntity<SimpleAPIResponse> getTyLeHaiLongDashBoard(@RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                 @RequestParam(value = "vung", required = false) List<String> vung,
                                                 @RequestParam(value = "maChiNhanh", required = false) List<String> maChiNhanh,
                                                 @RequestParam(value = "maBuuCuc", required = false) List<String> maBuuCuc) throws IOException {
        List<TyLePhanHoiDashboardDto> list = new ArrayList<>();
        SimpleAPIResponse response = new SimpleAPIResponse();
        list = tyLePhanHoiService.getTyLePhanHoiDashBoard(ngayBaoCao, vung, maChiNhanh, maBuuCuc);
        response.setData(list);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/ty-le-phan-hoi/bao-cao-tong-hop")
    public ResponseEntity<SimpleAPIResponseV2> getBaoCaoTongHop(@RequestBody GetBaoCaoTyLeHaiLongRq request) {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        PageWithTotalNumber<TyLePhanHoiDto> list = tyLePhanHoiService.getBaoCaoTongHop(request);
        if (!Objects.isNull(list)) {
            Pagination pageContent = new Pagination(request.getPage(), request.getSize(), list.getTotal());
            response.setData(list.getContent());
            response.setPagination(pageContent);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/ty-le-phan-hoi/bao-cao-tong-hop/export-excel")
    public ResponseEntity<SimpleAPIResponse> exportExcelBaoCaoTongHop(@RequestBody GetBaoCaoTyLeHaiLongRq request,
                                                                      HttpServletResponse httpServletResponse) throws IOException, NoSuchFieldException, IllegalAccessException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        tyLePhanHoiService.exportExcelBaoCaoTongHop(request, httpServletResponse);
        return ResponseEntity.ok(response);
    }
}
