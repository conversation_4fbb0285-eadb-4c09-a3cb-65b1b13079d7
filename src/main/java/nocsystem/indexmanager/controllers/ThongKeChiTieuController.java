package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong.ChiTieuChatLuongDto;
import nocsystem.indexmanager.services.ChiTieuChatLuong.ThongKeChiTieuChatLuongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/api/v1/report/public")
public class ThongKeChiTieuController {
    @Autowired
    private ThongKeChiTieuChatLuongService thongKeChiTieuChatLuongService;

    @GetMapping("/thong-ke-chi-tieu-chat-luong")
    public ResponseEntity<SimpleAPIResponse> getThongKeChiTieuChatLuong(@RequestParam("type_report") String typeReport
            , @RequestParam("export_date") String exportDate
            , @RequestParam("noti_date") String notiDate, @RequestParam("check_sum") String checkSum
    ) throws Exception {
        SimpleAPIResponse response = new SimpleAPIResponse();
        ChiTieuChatLuongDto chiTieuChatLuongDto =  thongKeChiTieuChatLuongService.verifyAndGetInfo(typeReport, exportDate, notiDate, checkSum);
        response.setMessage("Request Successful");
        response.setError(0);
        response.setData(chiTieuChatLuongDto);

        return ResponseEntity.status(200).body(response);
    }
}
