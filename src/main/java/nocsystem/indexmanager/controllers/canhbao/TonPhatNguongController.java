package nocsystem.indexmanager.controllers.canhbao;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatNguongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class TonPhatNguongController {

    @Autowired
    private TonPhatNguongService tPNService;

    @PostMapping("/ton-phat-nguong")
    public SimpleAPIResponse tPNBC(
            @RequestBody TonPhatNguongDTO body
    ) throws IOException {

        List<String> tinhPhat = body.getTinhPhat();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();
        Integer pageSize = body.getPageSize();
        Integer page = body.getPage();
        tPNService.sort = body.getSort();
        if (body.getPage() > 0) page--;

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonPhatNguongNewResponseDTO> tPN;

        tPN = tPNService.findAllDataBC(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, page, pageSize, body.getNguongTon());
        simpleAPIResponse.setData(tPN);

        return simpleAPIResponse;
    }

    @PostMapping("/ton-phat-nguong-sum")
    public SimpleAPIResponse tPNBCSum(
            @RequestBody TonPhatNguongDTO body
    ) throws IOException {
        List<String> tinhPhat = body.getTinhPhat();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonPhatNguongNewResponseDTO> tPNSum = tPNService.SummaryCalculation(tinhPhat, loaiTon, maBuuCuc, body.getNguongTon(), ngayBaoCao);
        simpleAPIResponse.setData(tPNSum);
        return simpleAPIResponse;
    }

    //Luongnv ---------------------------------------------
    @PostMapping("/ton-phat-nguong-excel")
    public void tPNBC789(
            HttpServletResponse response,
            @RequestBody TonPhatNguongDTO body
    ) throws IOException {

        List<String> tinhPhat = body.getTinhPhat();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=CB_NGUONG.xlsx";

        response.setHeader(headerKey, headerValue);
        tPNService.exportDataToExcel(response, tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, body.getNguongTon());
    }

    @PostMapping("/ton-phat-bill")
    public void tPNBill(
            HttpServletResponse response,
            @RequestBody TonPhatNguongDTO body
    ) throws IOException {

        List<String> tinhPhat = body.getTinhPhat();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=CB_NGUONG_Bill.xlsx";

        response.setHeader(headerKey, headerValue);
        tPNService.exportDataBill(response, tinhPhat, loaiTon,body.getNguongTon(), maBuuCuc,  body.getLoaiDichVu(), body.getLoiKhau(),ngayBaoCao );
    }

}
