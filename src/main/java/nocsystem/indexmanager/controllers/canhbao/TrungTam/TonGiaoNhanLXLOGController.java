package nocsystem.indexmanager.controllers.canhbao.TrungTam;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotificationDisplayDto;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.BuuCucChiNhanhDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.TonGiaoNhanLXLOGRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TrungTam.TonGiaoNhanLXLOGService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Locale;

@RestController
@RequestMapping("/api/v1")
public class TonGiaoNhanLXLOGController {

    @Autowired
    private TonGiaoNhanLXLOGService tGNLXService;


    @PostMapping("/ton-giao-nhan-LX-LOG")
    public SimpleAPIResponse tGNLX(
            @RequestBody KhoLienVung6hDTO body
    ) throws IOException {
        List<String> chiNhanh = body.getChiNhanh();
        List<String> buuCuc = body.getBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();
        Integer pageSize = body.getPageSize();
        Integer page = body.getPage();
        tGNLXService.sort = body.getSort();
        if (body.getPage() > 0) page--;

        ListContentPageDto<TonGiaoNhanLXLOGResponseDTO> tPKLM;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();

        tPKLM = tGNLXService.findAllDataCN(chiNhanh, buuCuc, ngayBaoCao, body.getLoaiVung(), body.getNhomDV(), body.getMocTon(), page, pageSize);
        simpleAPIResponse.setData(tPKLM);
        return simpleAPIResponse;
    }

    @PostMapping("/ton-giao-nhan-LX-LOG-sum")
    public SimpleAPIResponse tGNLXSum(
            @RequestBody KhoLienVung6hDTO body
    ) throws IOException {
        List<String> chiNhanh = body.getChiNhanh();
        List<String> buuCuc = body.getBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();

        List<TonGiaoNhanLXLOGResponseDTO> tPKLMSum = tGNLXService.TonGiaoNhanLXSumCalculation(chiNhanh, buuCuc, ngayBaoCao, body.getLoaiVung(), body.getNhomDV(), body.getMocTon());
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @PostMapping("/ton-giao-nhan-LX-LOG-BCCN")
    public SimpleAPIResponse bCCN(
            @RequestBody BuuCucChiNhanhDTO bCCN
    ) throws IOException {
        List<String> chiNhanh = bCCN.getChiNhanh();
        LocalDate ngayBaoCao = bCCN.getNgayBaoCao();
        List<String> list = tGNLXService.ListBuuCucChiNhanh(chiNhanh, ngayBaoCao);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(list);
        return simpleAPIResponse;
    }

    @PostMapping("/ton-giao-nhan-LX-LOG-excel")
    public void excelExport(
            HttpServletResponse response,
            @RequestBody KhoLienVung6hDTO body
    ) throws IOException {
        List<String> chiNhanh = body.getChiNhanh();
        List<String> buuCuc = body.getBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename = BC_Ton_giao_nhan_LX_LOG.xlsx";

        response.setHeader(headerKey, headerValue);
        tGNLXService.exportDataExcel(response, chiNhanh, buuCuc, ngayBaoCao, body.getLoaiVung(), body.getNhomDV(), body.getMocTon());
    }

    @PostMapping("/ton-giao-nhan-LX-LOG-bill")
    public void excelExportBill(
            HttpServletResponse response,
            @RequestBody KhoLienVung6hDTO body
    ) throws IOException {
        List<String> chiNhanh = body.getChiNhanh();
        List<String> buuCuc = body.getBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename = BC_Ton_TTKT_Bill.xlsx";

        response.setHeader(headerKey, headerValue);
        tGNLXService.exportDataBill(response, chiNhanh, buuCuc, ngayBaoCao, body.getLoaiVung(), body.getNhomDV(), body.getMocTon());
    }

    @GetMapping("/notification/ton-TTKT/v1")
    public SimpleAPIResponse notiLOG(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "nguong_canh_bao") String nguongCanhBao
    ) throws IOException {
        List<TonThuNotificationDisplayDto>  response = tGNLXService.TTKTNotiData(ngayBaoCao, nguongCanhBao.toUpperCase(Locale.ROOT));
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(response);
        return simpleAPIResponse;
    }
}
