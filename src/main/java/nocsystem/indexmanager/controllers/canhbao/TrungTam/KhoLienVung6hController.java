package nocsystem.indexmanager.controllers.canhbao.TrungTam;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.BuuCucChiNhanhDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.BuuCucChiNhanhResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.KhoLienVung6hRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TrungTam.KhoLienVung6hService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class KhoLienVung6hController {

    @Autowired
    private KhoLienVung6hService tGN6HService;

    @Autowired
    private KhoLienVung6hRepository tGNLV6hRepo;


    @PostMapping("/ton-giao-nhan-kho-LV-6h")
    public SimpleAPIResponse tPKLMCN(
            @RequestBody KhoLienVung6hDTO KhoLienVung6h
    ) throws IOException {

        List<String> chiNhanh = KhoLienVung6h.getChiNhanh();
        List<String> buuCuc = KhoLienVung6h.getBuuCuc();
        List<String> mocTon = KhoLienVung6h.getMocTon();
        LocalDate ngayBaoCao = KhoLienVung6h.getNgayBaoCao();
        Integer pageSize = KhoLienVung6h.getPageSize();
        Integer page = KhoLienVung6h.getPage();
        tGN6HService.sort = KhoLienVung6h.getSort();


        if(KhoLienVung6h.getPage() >0) page--;

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<KhoLienVung6hResponseDTO> kLV6h;

        kLV6h = tGN6HService.findAllDataCN(chiNhanh,buuCuc,ngayBaoCao,page,pageSize, mocTon);
        simpleAPIResponse.setData(kLV6h);
        return simpleAPIResponse;
    }



    @PostMapping("/ton-giao-nhan-kho-LV-6h-sum")
    public SimpleAPIResponse tPNCNSum(
            @RequestBody KhoLienVung6hDTO KhoLienVung6h
    ) throws IOException {

        List<String> chiNhanh = KhoLienVung6h.getChiNhanh();
        List<String> buuCuc = KhoLienVung6h.getBuuCuc();
        LocalDate ngayBaoCao = KhoLienVung6h.getNgayBaoCao();
        List<String> mocTon = KhoLienVung6h.getMocTon();

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<KhoLienVung6hResponseDTO> tPKLMSum = tGN6HService.tonGiaoNhanLV6hSum(chiNhanh,buuCuc,ngayBaoCao,mocTon);

        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @PostMapping("/ton-giao-nhan-kho-LV-6h-BCCN")
    public SimpleAPIResponse BCCN(
            @RequestBody BuuCucChiNhanhDTO BCCN
    ) throws IOException{
        List<String> chiNhanh = BCCN.getChiNhanh();
        LocalDate ngayBaoCao = BCCN.getNgayBaoCao();
        List<BuuCucChiNhanhResDTO> list = tGN6HService.listBuuCucChiNhanh(chiNhanh, ngayBaoCao);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(list);
        return simpleAPIResponse;
    }

    @PostMapping("/ton-giao-nhan-kho-LV-6h-excel")
    public void exportData(
            HttpServletResponse response,
            @RequestBody KhoLienVung6hDTO KhoLienVung6h
    ) throws IOException {

        List<String> chiNhanh = KhoLienVung6h.getChiNhanh();
        List<String> buuCuc = KhoLienVung6h.getBuuCuc();
        LocalDate ngayBaoCao = KhoLienVung6h.getNgayBaoCao();
        List<String> mocTon = KhoLienVung6h.getMocTon();
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename = BC_Ton_giao_nhan_kho_LV_6H.xlsx";

        response.setHeader(headerKey, headerValue);
        tGN6HService.exportDataExcel(response, chiNhanh, buuCuc, ngayBaoCao, mocTon);
    }
}
