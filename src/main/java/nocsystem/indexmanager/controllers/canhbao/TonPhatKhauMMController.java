package nocsystem.indexmanager.controllers.canhbao;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMLoaiTonDashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMMainDashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatNguongMainDashResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauMMResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatKhauMMRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatKhauMMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class TonPhatKhauMMController {

    @Autowired
    private TonPhatKhauMMService tPKMMService;

    @Autowired
    private TonPhatKhauMMRepository tPKMMRepo;

    @PostMapping("/ton-phat-khau-MM")
    public SimpleAPIResponse tPKMMBC(
            @RequestBody TonPhatKhauLMDTO body
    ) throws IOException {
        List<String> tinhPhat = body.getMaTinh();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();
        List<String> loaiCanhBao = body.getMaCanhBao();
        List<String> loaiDichVu = body.getLoaiDichVu();
        Integer pageSize = body.getPageSize();
        Integer page = body.getPage();
        tPKMMService.sort = body.getSort();
        if (body.getPage() > 0) page--;


        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonPhatKhauLMResponseDTO> tPKMM;

        tPKMM = tPKMMService.findAllDataBC(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu, page, pageSize);
        simpleAPIResponse.setData(tPKMM);

        return simpleAPIResponse;
    }

    @PostMapping("/ton-phat-khau-MM-sum")
    public SimpleAPIResponse tPNBCSum(
            @RequestBody TonPhatKhauLMDTO body
    ) throws IOException {
        List<String> tinhPhat = body.getMaTinh();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();
        List<String> loaiCanhBao = body.getMaCanhBao();
        List<String> loaiDichVu = body.getLoaiDichVu();

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonPhatKhauLMResponseDTO>  tPKMMSum = tPKMMService.SummaryCalculation(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu);
        simpleAPIResponse.setData(tPKMMSum);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-khau-MM-main-dash")
    public SimpleAPIResponse tPNBC(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonPhatKhauMMMainDashResDTO tPN = tPKMMService.khauMMMainDash(tinhPhat, maBuuCuc, ngayBaoCao);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-khau-MM-loai-ton-dash")
    public SimpleAPIResponse tPMMLoaiTon(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonPhatKhauMMLoaiTonDashResDTO tPN = tPKMMService.khauMMLoaiTon(tinhPhat, maBuuCuc, ngayBaoCao);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-khau-MM-top15-dash")
    public SimpleAPIResponse tPMMTop15(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonPhatKhauMMTop15DashResDTO> tPN = tPKMMService.khauMMTop15(tinhPhat, maBuuCuc, ngayBaoCao);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @PostMapping("/ton-phat-khau-MM-excel")
    public void tPKhauLMExportExcel(
            HttpServletResponse response,
            @RequestBody TonPhatKhauLMDTO tonPhatKhauMM

    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=TonPhatKhauMM.xlsx";
        response.setHeader(headerKey, headerValue);
        tPKMMService.exportExcelTonPhatKhauLM(response, tonPhatKhauMM.getMaTinh(), tonPhatKhauMM.getLoaiTon(), tonPhatKhauMM.getMaBuuCuc(), tonPhatKhauMM.getNgayBaoCao(), tonPhatKhauMM.getMaCanhBao(), tonPhatKhauMM.getLoaiDichVu());
    }
}
