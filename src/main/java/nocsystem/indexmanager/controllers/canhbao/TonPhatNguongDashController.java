package nocsystem.indexmanager.controllers.canhbao;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.*;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatNguongDashRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatNguongDashService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class TonPhatNguongDashController {

    @Autowired
    private TonPhatNguongDashService tPNService;

    @Autowired
    private TonPhatNguongDashRepository tPNRepo;

    @GetMapping("/ton-phat-nguong-main-dash")
    public SimpleAPIResponse tPNBC(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonPhatNguongMainDashResDTO tPN = tPNService.tonPhatNguongDif(ngayBaoCao, tinhPhat,maBuuCuc);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-nguong-loai-ton-dash")
    public SimpleAPIResponse tPNLoaiTon(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonPhatNguongLoaiTonDashResDTO tPN = tPNService.tonPhatNguongLT(ngayBaoCao, tinhPhat,maBuuCuc);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-nguong-HNI-HCM-dash")
    public SimpleAPIResponse tPNHNIHCM(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonPhatNguongHNIHCMResDTO tPN = tPNService.tonPhatNguongHNIHCM(ngayBaoCao);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-nguong-top15-n789-dash")
    public SimpleAPIResponse tPNTop15N789(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonPhatNguongTop15N789DashResDTO> tPN = tPNService.tPNTop15N789(ngayBaoCao, tinhPhat, maBuuCuc);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-nguong-top15-n9-dash")
    public SimpleAPIResponse tPNTop15N9(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonPhatNguongTop15N9DashResDTO> tPN = tPNService.tPNTop15N9(ngayBaoCao, tinhPhat, maBuuCuc);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

}
