package nocsystem.indexmanager.controllers.canhbao;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiReqBody;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotificationDisplayDto;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatKhauLMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Locale;

@RestController
@RequestMapping("/api/v1")
public class TonPhatKhauLMController {

    @Autowired
    private TonPhatKhauLMService tPKLMService;

    @PostMapping("/ton-phat-khau")
    public SimpleAPIResponse tPKLM(
            @RequestBody TonPhatKhauLMDTO body
    ) throws IOException {

        List<String> tinhPhat = body.getMaTinh();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();
        List<String> loaiCanhBao = body.getMaCanhBao();
        Integer pageSize = body.getPageSize();
        Integer page = body.getPage();
        tPKLMService.sort = body.getSort();
        if (body.getPage() > 0) page--;

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonPhatKhauLMResponseDTO> tPKLM;

        tPKLM = tPKLMService.findAllDataBC(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, page, pageSize, body.getLoaiDichVu(), body.getLoiKhau());
        simpleAPIResponse.setData(tPKLM);
        return simpleAPIResponse;
    }

    @PostMapping("/ton-phat-khau-sum")
    public SimpleAPIResponse tPKLMSum(
            @RequestBody TonPhatKhauLMDTO body
    ) throws IOException {

        List<String> tinhPhat = body.getMaTinh();
        List<String> loaiTon = body.getLoaiTon();
        List<String> maBuuCuc = body.getMaBuuCuc();
        LocalDate ngayBaoCao = body.getNgayBaoCao();
        List<String> loaiCanhBao = body.getMaCanhBao();

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonPhatKhauLMResponseDTO> tPKLMSum = tPKLMService.SummaryCalculation(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, body.getLoaiDichVu(), body.getLoiKhau());
        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-phat-khau-LM-top8-dash")
    public SimpleAPIResponse tPMMTop15(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "tinh_phat", defaultValue = "") String tinhPhat,
            @RequestParam(value = "ma_buu_cuc", defaultValue = "") String maBuuCuc
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonPhatKhauLMTop8ResDTO> tPN = tPKLMService.khauMMTop8(tinhPhat, ngayBaoCao);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    // VT_2023/02/27 NgocNt92 add LM ExcelExporter Api to request data.
    @PostMapping("ton-phat-khau-excel")
    public void tPKhauLMExportExcel(
            HttpServletResponse response,
            @RequestBody TonPhatKhauLMDTO body
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=TonPhatKhauLM.xlsx";
        response.setHeader(headerKey, headerValue);
        tPKLMService.exportExcelTonPhatKhauLM(response, body.getMaTinh(), body.getLoaiTon(), body.getMaBuuCuc(), body.getNgayBaoCao(), body.getMaCanhBao(), body.getLoaiDichVu(), body.getLoiKhau());
    }
    // 2023.04.25 ngocnt92 add api to call by noc index
    @GetMapping("/notification/ton-phat/v1")
    public SimpleAPIResponse findAllInForByFillter(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh", defaultValue = "") String tinhPhat,
            @RequestParam(value = "buu_cuc", defaultValue = "") String buu_cuc,
            @RequestParam(value = "doi_tuong", defaultValue = "BAN_TGD") String doiTuongCanhBao,
            @RequestParam(value = "nguong_canh_bao") String nguongCanhBao
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuNotificationDisplayDto> tPN = tPKLMService.getKhauLMNotificationData(ngayBaoCao, doiTuongCanhBao.toUpperCase(Locale.ROOT), tinhPhat, buu_cuc, nguongCanhBao.toUpperCase(Locale.ROOT));
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @PostMapping("ton-phat-noti-excel")
    public void exportDataNoti(
            HttpServletResponse response,
            @RequestBody TonThuNotiReqBody body
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=TonPhatKhauLM.xlsx";
        response.setHeader(headerKey, headerValue);
        tPKLMService.exportExcelTPNoti(response, body.getChiNhanh(), body.getBuuCuc(), body.getNgayBaoCao(), body.getLoaiCanhBao());
    }
}
