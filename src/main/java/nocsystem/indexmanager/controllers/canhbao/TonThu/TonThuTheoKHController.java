package nocsystem.indexmanager.controllers.canhbao.TonThu;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuBillDashboarResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.*;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiReqBody;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotificationDisplayDto;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonDetailBillResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuTheoKHRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuTheoKHService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1")
public class TonThuTheoKHController {

    @Autowired
    private TonThuTheoKHService tTKHService;

    @Autowired
    private TonThuTheoKHRepository tTKHRepo;


    @PostMapping("/ton-thu-theo-KH")
    public SimpleAPIResponse tTKH(
            @RequestBody TonThuTheoKHDTO tonThuTheoKH
    ) throws IOException {

        List<String> chiNhanhNhan = tonThuTheoKH.getChiNhanhNhan();
        List<String> buuCucNhan = tonThuTheoKH.getBuuCucNhan();
        List<String> loaiCanhBao = tonThuTheoKH.getLoaiCanhBao();
        LocalDate ngayBaoCao = LocalDate.parse(tonThuTheoKH.getNgayBaoCao());
        Integer pageSize = tonThuTheoKH.getPageSize();
        Integer page = tonThuTheoKH.getPage();
        TonThuTheoKHService.sort = tonThuTheoKH.getSort();

        if (tonThuTheoKH.getPage() > 0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuTheoKHResponseDTO> tTKH;

        tTKH = tTKHService. findAllDataBT(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, page, pageSize);
        simpleAPIResponse.setData(tTKH);
        return simpleAPIResponse;
    }


    @PostMapping("/ton-thu-theo-KH-sum")
    public SimpleAPIResponse tTKHSum(
            @RequestBody TonThuTheoKHDTO tonThuTheoKH
    ) throws IOException {

        List<String> chiNhanhNhan = tonThuTheoKH.getChiNhanhNhan();
        List<String> buuCucNhan = tonThuTheoKH.getBuuCucNhan();
        List<String> loaiCanhBao = tonThuTheoKH.getLoaiCanhBao();
        LocalDate ngayBaoCao = LocalDate.parse(tonThuTheoKH.getNgayBaoCao());

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuTheoKHResponseDTO> tPKLMSum = tTKHService.TonThuTheoKHSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao);
        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-thu-dash-top10-muc-do")
    public SimpleAPIResponse tTtop10Do(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuTop10DoResDTO> tPKLMSum = tTKHService.getTop10Do(chiNhanhNhan, ngayBaoCao, "MUC_DO");
        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-thu-dash-top10-kh-VIP")
    public SimpleAPIResponse tTtop10KhVIP(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuTop10DoResDTO> tPKLMSum = tTKHService.getTop10Do(chiNhanhNhan, ngayBaoCao, "KH_VIP");
        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-thu-dash-top10-chua-gan-tuyen")
    public SimpleAPIResponse tTtop10CGT(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuTop10CGTResDTO> tPKLMSum = tTKHService.getDataTop10CGT(chiNhanhNhan, ngayBaoCao);
        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-thu-dash-khach-hang")
    public SimpleAPIResponse tTKHDas(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") List<String> buuCucNhan,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") List<String> chiNhanhNhan
    ) throws IOException {

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuTheoKHResponseDTO> tonTTKHResList = tTKHService.findAllDataCase(buuCucNhan, ngayBaoCao, chiNhanhNhan);
        tTKHService.tinhTongThuColumn(tonTTKHResList);
        tTKHService.addDefaulJson(tonTTKHResList);
        tTKHService.sortByLoaiCanhBao(tonTTKHResList);
        List<TonThuTheoKHDashResDTO> tonTTKHRESDisplayList =
                tonTTKHResList.stream()
                        .map(e -> {
                            TonThuTheoKHDashResDTO tTKHResDisPlay = new TonThuTheoKHDashResDTO();
                            BeanUtils.copyProperties(e, tTKHResDisPlay);
                            return tTKHResDisPlay;
                        }).collect(Collectors.toList());

        simpleAPIResponse.setData(tonTTKHRESDisplayList);

        return simpleAPIResponse;
    }

    // VT2023/02/25_Ngocnt92 add function to export data to excel
    @PostMapping("/ton-thu-theo-KH-excel")
    public void exportData(
            HttpServletResponse response,
            @RequestBody TonThuTheoKHDTO tonThuTheoKH
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=CB-TTTheoKH.xlsx";
        response.setHeader(headerKey, headerValue);
        tTKHService.exportExcelTTKH(response, tonThuTheoKH.getChiNhanhNhan(), tonThuTheoKH.getBuuCucNhan(), LocalDate.parse(tonThuTheoKH.getNgayBaoCao()), tonThuTheoKH.getLoaiCanhBao());
    }

    @PostMapping("/ton-thu-bill")
    public void ttBill(
            HttpServletResponse response,
            @RequestBody TonThuTheoKHDTO body
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=TT_Bill.xlsx";
        response.setHeader(headerKey, headerValue);
        String trangThai = body.getTrangThai();
        List<String> buuTa = body.getBuuTaNhan();
        if(trangThai == null)
            trangThai = "";
        if(buuTa == null){
            buuTa = Collections.emptyList();
        }
        String maDoiTac = body.getMaDoiTac();
        tTKHService.exportExcelBill(response, body.getChiNhanhNhan(), body.getVungCon(), body.getBuuCucNhan(), buuTa, LocalDate.parse(body.getNgayBaoCao()) , body.getLoaiCanhBao(),
                trangThai, maDoiTac, body.getLoaiHH(), body.getKhDacThuGui(), body.getKhDacThuNhan(), body.getMaKhGui());
    }

    @GetMapping("/notification/ton-thu")
    public SimpleAPIResponse notiLOG(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "nguong_canh_bao") String loaiCanhBao,
            @RequestParam(value = "chi_nhanh", defaultValue = "") String chiNhanh,
            @RequestParam(value = "buu_cuc", defaultValue = "") String buuCuc,
            @RequestParam(value = "doi_tuong", defaultValue = "BAN_TGD") String doiTuongCanhBao
    ) throws IOException {
        TonThuTop10DoResDTO response = tTKHService.getDataNoti(ngayBaoCao, loaiCanhBao.toUpperCase(Locale.ROOT), doiTuongCanhBao.toUpperCase(Locale.ROOT), chiNhanh, buuCuc);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(response);
        return simpleAPIResponse;
    }
    // 2023.04.24 ngocnt92 add Api to getData follow by param cấu hình
    @GetMapping("/notification/ton-thu/v1")
    public ResponseEntity<?> getDataNotification(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "nguong_canh_bao") String loaiCanhBao,
            @RequestParam(value = "chi_nhanh", defaultValue = "") String chiNhanh,
            @RequestParam(value = "buu_cuc", defaultValue = "") String buuCuc,
            @RequestParam(value = "doi_tuong", defaultValue = "BAN_TGD") String doiTuongCanhBao
    ) throws IOException {
        List<TonThuNotificationDisplayDto> response = tTKHService.getTop10DataTonThu(ngayBaoCao, loaiCanhBao.toUpperCase(Locale.ROOT), doiTuongCanhBao.toUpperCase(Locale.ROOT), chiNhanh, buuCuc);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE).body(simpleAPIResponse);
    }


    @PostMapping("/ton-thu-noti-excel")
    public void exportDataNoti(
            HttpServletResponse response,
            @RequestBody TonThuNotiReqBody tonThuTheoKH
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=CB-TTTheoKH.xlsx";
        response.setHeader(headerKey, headerValue);
        tTKHService.exportExcelTTNoti(response, tonThuTheoKH.getChiNhanh(), tonThuTheoKH.getBuuCuc(), tonThuTheoKH.getNgayBaoCao(), tonThuTheoKH.getLoaiCanhBao());
    }

    @GetMapping("dashboard/cn-bc/ton-thu")
    public SimpleAPIResponse tonThuDash(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "order", defaultValue = "") String order,
            @RequestParam(value = "order_by", defaultValue = "tong") String orderBy,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "page_size", defaultValue = "30") Integer pageSize,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "khDacThuGui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "khDacThuNhan", defaultValue = "") String khDacThuNhan,
            @RequestParam(value = "maKhGui", defaultValue = "") String maKhGui,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHH
    ) throws IOException {
        if(page > 0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboard(chiNhanhNhan, vungCon, buuCucNhan, ngayBaoCao, trangThai, page, pageSize, order, orderBy, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }

    @GetMapping("dashboard/cn-bc/ton-thu-2")
    public SimpleAPIResponse tonThuDash2(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "page_size", defaultValue = "30") Integer pageSize
    ) throws IOException {
        if(page > 0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboard2(chiNhanhNhan, buuCucNhan, ngayBaoCao, trangThai, page, pageSize );
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }


    @GetMapping("dashboard/cn-bc/ton-thu-sum")
    public SimpleAPIResponse tonThuDashSum(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "khDacThuGui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "khDacThuNhan", defaultValue = "") String khDacThuNhan,
            @RequestParam(value = "maKhGui", defaultValue = "") String maKhGui,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHH
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboardSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, trangThai, vungCon, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }

    @GetMapping("dashboard/cn-bc/ton-thu-sum-2")
    public SimpleAPIResponse tonThuDashSum2(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboardSum2(chiNhanhNhan, buuCucNhan, ngayBaoCao, trangThai);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }

    @GetMapping("dashboard/cn-bc/ton-thu-detail")
    public SimpleAPIResponse tonThuDashDetail(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "loai_canh_bao", defaultValue = "") String loaiCanhBao,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "tuyen_buu_ta", defaultValue = "") String buuTa,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "30") Integer pageSize,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "khDacThuGui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "khDacThuNhan", defaultValue = "") String khDacThuNhan,
            @RequestParam(value = "maKhGui", defaultValue = "") String maKhGui,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHH
    ) throws IOException {
        if(page > 0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuBillDashboarResponseDTO> tonThu = tTKHService.tonThuChiTietDashboard(chiNhanhNhan, vungCon, buuCucNhan, ngayBaoCao, buuTa, trangThai, loaiCanhBao, page, pageSize, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }

    @GetMapping("dashboard/cn-bc/get-list-buu-ta-thu")
    public SimpleAPIResponse buuTaTheoBC(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<String> tonThu = tTKHService.buuTaTheoBC(chiNhanhNhan, buuCucNhan, ngayBaoCao);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }

    @GetMapping("dash-mobile/dashboard/cn-bc/ton-thu")
    public SimpleAPIResponse tonThuDashMobile(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "order", defaultValue = "") String order,
            @RequestParam(value = "order_by", defaultValue = "tong") String orderBy,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "page_size", defaultValue = "30") Integer pageSize,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "khDacThuGui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "khDacThuNhan", defaultValue = "") String khDacThuNhan,
            @RequestParam(value = "maKhGui", defaultValue = "") String maKhGui,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHH
    ) throws IOException {
        if(page > 0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboard(chiNhanhNhan, vungCon, buuCucNhan, ngayBaoCao, trangThai, page, pageSize, order, orderBy, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }


    @GetMapping("dash-mobile/dashboard/cn-bc/ton-thu-sum")
    public SimpleAPIResponse tonThuDashSumMobile(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "khDacThuGui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "khDacThuNhan", defaultValue = "") String khDacThuNhan,
            @RequestParam(value = "maKhGui", defaultValue = "") String maKhGui,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHH
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboardSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, trangThai, vungCon, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }

    @GetMapping("dash-mobile/dashboard/cn-bc/ton-thu-2")
    public SimpleAPIResponse tonThuDashMobile2(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "page_size", defaultValue = "30") Integer pageSize
    ) throws IOException {
        if(page > 0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboard2(chiNhanhNhan, buuCucNhan, ngayBaoCao, trangThai, page, pageSize );
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }


    @GetMapping("dash-mobile/dashboard/cn-bc/ton-thu-sum-2")
    public SimpleAPIResponse tonThuDashSumMobile2(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuDashCNBCResponseDTO> tonThu = tTKHService.tonThuDashboardSum2(chiNhanhNhan, buuCucNhan, ngayBaoCao, trangThai);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }

    @GetMapping("dash-mobile/dashboard/cn-bc/ton-thu-detail")
    public SimpleAPIResponse tonThuDashMobileDetail(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan,
            @RequestParam(value = "trang_thai", defaultValue = "") String trangThai,
            @RequestParam(value = "loai_canh_bao", defaultValue = "") String loaiCanhBao,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "tuyen_buu_ta", defaultValue = "") String buuTa,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "30") Integer pageSize,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "khDacThuGui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "khDacThuNhan", defaultValue = "") String khDacThuNhan,
            @RequestParam(value = "maKhGui", defaultValue = "") String maKhGui,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHH
    ) throws IOException {
        if(page > 0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuBillDashboarResponseDTO> tonThu = tTKHService.tonThuChiTietDashboard(chiNhanhNhan, vungCon, buuCucNhan, ngayBaoCao, buuTa, trangThai, loaiCanhBao, page, pageSize, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }


    @GetMapping("dash-mobile/dashboard/cn-bc/get-list-buu-ta-thu")
    public SimpleAPIResponse buuTaTheoBCMobile(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucNhan
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<String> tonThu = tTKHService.buuTaTheoBC(chiNhanhNhan, buuCucNhan, ngayBaoCao);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }


    //todo: ko con dung nua
    @GetMapping("dash-mobile/dashboard/cn-bc/ton-thu-bill")
    public SimpleAPIResponse chiTietDonMobile(
            @RequestParam(value = "ma_phieugui") String maPhieuGui
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonDetailBillResponse tonThu = tTKHService.tonThuChiTietDon(maPhieuGui);
        simpleAPIResponse.setData(tonThu);
        return simpleAPIResponse;
    }
}
