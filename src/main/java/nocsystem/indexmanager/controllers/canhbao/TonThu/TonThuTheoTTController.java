package nocsystem.indexmanager.controllers.canhbao.TonThu;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTCanhBaoDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuTheoTTRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuTheoTTService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class TonThuTheoTTController {

    @Autowired
    private TonThuTheoTTService tTKHService;

    @Autowired
    private TonThuTheoTTRepository tTKHRepo;


    @PostMapping("/ton-thu-theo-TT")
    public SimpleAPIResponse tTTT(
            @RequestBody TonThuTheoKHDTO tonThuTheoKH
    ) throws IOException {

        List<String> chiNhanhNhan = tonThuTheoKH.getChiNhanhNhan();
//        List<String> tinhNhan = tonThuTheoKH.getTinhNhan();
        List<String> buuCucNhan = tonThuTheoKH.getBuuCucNhan();
        List<String> loaiCanhBao = tonThuTheoKH.getLoaiCanhBao();
        LocalDate ngayBaoCao = LocalDate.parse(tonThuTheoKH.getNgayBaoCao());
        Integer pageSize = tonThuTheoKH.getPageSize();
        Integer page = tonThuTheoKH.getPage();
        TonThuTheoTTService.sort = tonThuTheoKH.getSort();

        if(tonThuTheoKH.getPage() >0) page--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<TonThuTheoTTResponseDTO> tTKH;

        tTKH = tTKHService.findAllDataBT(chiNhanhNhan,buuCucNhan,ngayBaoCao,loaiCanhBao,page,pageSize);
        simpleAPIResponse.setData(tTKH);

        return simpleAPIResponse;
    }

    @PostMapping("/ton-thu-theo-TT-sum")
    public SimpleAPIResponse tTTTSum(
            @RequestBody TonThuTheoKHDTO tonThuTheoKH
    ) throws IOException {

        List<String> chiNhanhNhan = tonThuTheoKH.getChiNhanhNhan();
        List<String> buuCucNhan = tonThuTheoKH.getBuuCucNhan();
        List<String> loaiCanhBao = tonThuTheoKH.getLoaiCanhBao();
        LocalDate ngayBaoCao = LocalDate.parse(tonThuTheoKH.getNgayBaoCao());

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuTheoTTResponseDTO> tPKLMSum = tTKHService.SummaryCalculation(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao);
        simpleAPIResponse.setData(tPKLMSum);
        return simpleAPIResponse;
    }

    @GetMapping("/ton-thu-dash-trang-thai")
    public SimpleAPIResponse tPNBC(

            @RequestParam(value = "chi_nhanh_nhan", defaultValue = "") String chiNhanhNhan,
            @RequestParam(value = "buu_cuc_nhan", defaultValue = "") String buuCucnhan,
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao
    ) throws IOException {

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<TonThuTheoTTCanhBaoDTO> tTSumColum = tTKHService.TTSumTrangThai(chiNhanhNhan, buuCucnhan, ngayBaoCao);
        simpleAPIResponse.setData(tTSumColum);
        return simpleAPIResponse;
    }

    //VT_2023/02/25 Ngocnt92 add method GetAPI to export Excel
    @PostMapping("/ton-thu-theo-TT-excel")
    public void exportData(
            HttpServletResponse response,
            @RequestBody TonThuTheoKHDTO tonThuTheoTT
    )throws IOException{
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=CB-TTTheoTT.xlsx";
        response.setHeader(headerKey, headerValue);
        tTKHService.exportExcelTTTT(response, tonThuTheoTT.getChiNhanhNhan(),tonThuTheoTT.getBuuCucNhan(), LocalDate.parse(tonThuTheoTT.getNgayBaoCao()), tonThuTheoTT.getLoaiCanhBao());
    }
}
