package nocsystem.indexmanager.controllers.canhbao.TonThu;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuTheoKHRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuTheoTTRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuTheoKHService;
import nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuTheoTTService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class TonThuDashController {

    @Autowired
    private TonThuTheoKHService tTKHService;

    @Autowired
    private TonThuTheoKHRepository tTKHRepo;

    @Autowired
    private TonThuTheoTTService tTTTService;

    @Autowired
    private TonThuTheoTTRepository tTTTRepo;

}
