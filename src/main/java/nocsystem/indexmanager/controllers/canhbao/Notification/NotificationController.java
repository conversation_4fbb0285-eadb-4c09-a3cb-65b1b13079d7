package nocsystem.indexmanager.controllers.canhbao.Notification;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.SMSNotiResponseDTO;
import nocsystem.indexmanager.services.CanhBaoServices.Notification.NotificationDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Locale;

@RestController
@RequestMapping("/api/v1")
public class NotificationController {

    @Autowired
    private NotificationDataService notiService;

    @GetMapping("/notification/sms")
    public SimpleAPIResponse tPMMNoti(
            @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh", defaultValue = "") String tinhPhat,
            @RequestParam(value = "buu_cuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "doi_tuong", defaultValue = "BAN_TGD") String doiTuongCanhBao
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        SMSNotiResponseDTO tPN = notiService.getSMSData(ngayBaoCao, doiTuongCanhBao.toUpperCase(Locale.ROOT), tinhPhat, maBuuCuc);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }
}
