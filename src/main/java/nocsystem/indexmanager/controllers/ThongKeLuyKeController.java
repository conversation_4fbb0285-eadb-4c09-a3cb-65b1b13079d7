package nocsystem.indexmanager.controllers;


import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeLogistic.TinhTongTienDoLuyKeLogisticDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.ListDetailLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMNDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMTDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeCNHaNoiAndHCMResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTBCResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TongTienDoLuyKeTCTCNResDto;
import nocsystem.indexmanager.services.ThongKeLuyKe.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class ThongKeLuyKeController {

    @Autowired
    private TienDoluyKeTCTCNService tienDoluyKeTCTCNService;

    @Autowired
    private TongTienDoluyKeTCTCNService tongTienDoluyKeTCTCNService;

    @Autowired
    private TienDoluyKeTCTBuuCucService tienDoluyKeTCTBuuCucService;

    @Autowired
    private TongTienDoluyKeTCTBCService tongTienDoluyKeTCTBCService;

    @Autowired
    private TienDoluyKeCNHaNoiAndHCMService tienDoluyKeCNHaNoiAndHCMService;

    @Autowired
    private TongTienDoLuyKeCNHaNoiAndHCMService tongTienDoLuyKeCNHaNoiAndHCMService;

    @Autowired
    private TienDoLuyKeMienBacService tienDoLuyKeMBService;

    @Autowired
    private TienDoLuyKeMienTrungService tienDoLuyKeMTService;

    @Autowired
    private TienDoLuyKeMienNamService tienDoLuyKeMNService;

    @Autowired
    private TienDoLuyKeLogisticService tDLuyKeLogisticService;

    @GetMapping("/thong-ke-luy-ke-tct-chi-nhanh")
    public SimpleAPIResponse findThongKeLuyKeTCTCN(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(defaultValue = "0", value = "page_index") int pageIndex,
            @RequestParam(defaultValue = "10", value = "page_size") int pageSize,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc
    ) {
        if (pageIndex > 0) pageIndex--;
        CustomizeDataPage<TienDoluyKeTCTCNResDto> result = tienDoluyKeTCTCNService.findTienDoLuyKeTCTCN(
                maChiNhanh, maBuuCuc, ngayBaoCao, pageIndex, pageSize);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(result);
        return simpleAPIResponse;
    }

    @GetMapping("/tong-thong-ke-luy-ke-tct-chi-nhanh")
    public SimpleAPIResponse findTongThongKeLuyKeTCTCN(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<TongTienDoLuyKeTCTCNResDto> result = tongTienDoluyKeTCTCNService.findTongTienDoLuyKeTCTCN(ngayBaoCao, maChiNhanh, maBuuCuc);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/thong-ke-luy-ke-tct-buu-cuc")
    public SimpleAPIResponse findThongKeLuyKeTCTCN(
            @RequestParam(value = "ma_chinhanh") String chiNhanh,
            @RequestParam(required = false, value = "ma_buucuc") String buuCuc,
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(defaultValue = "0", value = "page_index") int pageIndex,
            @RequestParam(defaultValue = "10", value = "page_size") int pageSize
    ) {
        if (pageIndex > 0) pageIndex--;
        CustomizeDataPage<TienDoluyKeTCTBCResDto> result = tienDoluyKeTCTBuuCucService.findTienDoLuyKeTCTBC(
                chiNhanh, buuCuc, ngayBaoCao, pageIndex, pageSize);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(result);
        return simpleAPIResponse;
    }

    @GetMapping("/tong-thong-ke-luy-ke-tct-buu-cuc")
    public SimpleAPIResponse findTongThongKeLuyKeTCTBC(
            @RequestParam(value = "ma_chinhanh") String chiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue="") String buuCuc,
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<TienDoluyKeTCTCNResDto> result = tongTienDoluyKeTCTBCService.findTongTienDoLuyKeTCTBC(chiNhanh, buuCuc, ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/thong-ke-luy-ke-chi-nhanh-ha-noi")
    public SimpleAPIResponse findThongKeLuyKeCNHaNoi(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(required = false, value = "ma_buucuc") String buuCuc,
            @RequestParam(defaultValue = "0", value = "page_index") int pageIndex,
            @RequestParam(defaultValue = "10", value = "page_size") int pageSize
    ) {
        if (pageIndex > 0) pageIndex--;
        CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> result = tienDoluyKeCNHaNoiAndHCMService.findTienDoLuyKeCNHaNoi(
                ngayBaoCao, buuCuc, pageIndex, pageSize);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(result);
        return simpleAPIResponse;
    }

    @GetMapping("/thong-ke-luy-ke-chi-nhanh-hcm")
    public SimpleAPIResponse findThongKeLuyKeCNHCM(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(required = false, value = "ma_buucuc") String buuCuc,
            @RequestParam(defaultValue = "0", value = "page_index") int pageIndex,
            @RequestParam(defaultValue = "10", value = "page_size") int pageSize
    ) {
        if (pageIndex > 0) pageIndex--;
        CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> result = tienDoluyKeCNHaNoiAndHCMService.findTienDoLuyKeCNHCM(
                ngayBaoCao, buuCuc, pageIndex, pageSize);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(result);
        return simpleAPIResponse;
    }

    @GetMapping("/tong-thong-ke-luy-ke-chi-nhanh-hn")
    public SimpleAPIResponse findTongThongKeLuyKeCNHN(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<TienDoluyKeTCTCNResDto> result = tongTienDoLuyKeCNHaNoiAndHCMService.findTongTienDoLuyKeCNHaNoi(ngayBaoCao, maChiNhanh, maBuuCuc);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/tong-thong-ke-luy-ke-chi-nhanh-hcm")
    public SimpleAPIResponse findTongThongKeLuyKeCNHCM(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<TienDoluyKeTCTCNResDto> result = tongTienDoLuyKeCNHaNoiAndHCMService.findTongTienDoLuyKeCNHCM(ngayBaoCao, maChiNhanh, maBuuCuc);
        sp.setData(result);
        return sp;
    }

    /*
     * Công thức tính chung như sau:
     * + Đối với số liệu hàng tổng của chi nhánh sẽ lấy dữ liệu trong bảng DashBoard với mã miền
     * + Đối với tổng hàng tổng của bưu cục sẽ lấy dữ liệu trong bảng TCT với mã miền và mã chi nhánh
     */
    @GetMapping("/thong-ke-luy-ke-mien-bac")
    public SimpleAPIResponse findThongKeLuyKeMienBac(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "page_index", defaultValue = "0") int pageIndex,
            @RequestParam(value = "page_size", defaultValue = "10") int pageSize
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> result =
                tienDoLuyKeMBService.findListDetailLuyKeMB(ngayBaoCao, maChiNhanh, maBuuCuc, pageIndex, pageSize);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/tong-luy-ke-mien-bac")
    public SimpleAPIResponse findTongLuyKeMienBac(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String buuCuc
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        TinhTongLuyKeChiNhanhMBDto result = tienDoLuyKeMBService.totalLuyKeCNBCMienBac(ngayBaoCao, maChiNhanh, buuCuc);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/thong-ke-luy-ke-mien-trung")
    public SimpleAPIResponse findThongKeLuyKeMienTrung(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "page_index", defaultValue = "0") int pageIndex,
            @RequestParam(value = "page_size", defaultValue = "10") int pageSize
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> result =
                tienDoLuyKeMTService.listDetailLuyKeMT(ngayBaoCao, maChiNhanh, maBuuCuc, pageIndex, pageSize);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/tong-luy-ke-mien-trung")
    public SimpleAPIResponse findTongLuyKeMienTrung(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String buuCuc
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        TinhTongLuyKeChiNhanhMTDto result = tienDoLuyKeMTService.totalLuyKeCNBCMienTrung(ngayBaoCao, maChiNhanh, buuCuc);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/thong-ke-luy-ke-mien-nam")
    public SimpleAPIResponse findThongKeLuyKeMienNam(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "page_index", defaultValue = "0") int pageIndex,
            @RequestParam(value = "page_size", defaultValue = "10") int pageSize
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        CustomizeDataPage<TinhTongLuyKeChiNhanhMNDto> result =
                tienDoLuyKeMNService.listDetailLuyKeMN(ngayBaoCao, maChiNhanh, maBuuCuc, pageIndex, pageSize);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/tong-luy-ke-mien-nam")
    public SimpleAPIResponse findTongLuyKeMienNam(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String buuCuc
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        TinhTongLuyKeChiNhanhMNDto result = tienDoLuyKeMNService.totalLuyKeCNBCMienNam(ngayBaoCao, maChiNhanh, buuCuc);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/tong-luy-ke-logistic")
    public SimpleAPIResponse thongKeLuyKeLogistic(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh
    ) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        TinhTongTienDoLuyKeLogisticDto result = tDLuyKeLogisticService.totalLuyKeCTLogistic(ngayBaoCao, maChiNhanh);
        sp.setData(result);
        return sp;
    }
}
