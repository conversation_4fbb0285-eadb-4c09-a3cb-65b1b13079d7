package nocsystem.indexmanager.controllers.NguonKhaiThacKhuVuc;

import nocsystem.indexmanager.config.Pagination;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucRequestBody;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucResponse;
import nocsystem.indexmanager.services.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class NguonKhaiThacKhuVucController {
    @Autowired
    private NguonKhaiThacKhuVucService nguonKhaiThacKhuVucService;

    @PostMapping("/nguon-khai-thac-khu-vuc")
    public ResponseEntity<SimpleAPIResponseV2> doanhThuKhaiThac(@RequestBody NguonKhaiThacKhuVucRequestBody body) throws SQLException {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        List<NguonKhaiThacKhuVucResponse> listResponse = nguonKhaiThacKhuVucService.nguonKhaiThacKhuVuc(body);
        Long totalRecord = nguonKhaiThacKhuVucService.nguonKhaiThacKhuVucTotalRecord(
                body.getNgayBaoCao(),
                body.getLuyKe(),
                body.getTtktCha(),
                body.getTtktQuetNhan(),
                body.getMaDichVu());
        Pagination pagination = new Pagination(body.getPage(), body.getPageSize(), totalRecord);
        response.setData(listResponse);
        response.setPagination(pagination);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/nguon-khai-thac-khu-vuc/tong")
    public SimpleAPIResponse doanhThuKhaiThacTong(@RequestBody NguonKhaiThacKhuVucRequestBody body) throws SQLException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        NguonKhaiThacKhuVucResponse listResponse = nguonKhaiThacKhuVucService.nguonKhaiThacKhuVucTong(body);
        response.setData(listResponse);
        return response;
    }

    @PostMapping("/nguon-khai-thac-khu-vuc/excel")
    public void doanhThuKhaiThacExcel(
            HttpServletResponse response,
            @RequestBody NguonKhaiThacKhuVucRequestBody body) throws IOException, IllegalAccessException, SQLException {
        nguonKhaiThacKhuVucService.nguonKhaiThacKhuVucExcel(response, body);
    }
}
