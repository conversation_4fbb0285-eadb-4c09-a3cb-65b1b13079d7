package nocsystem.indexmanager.controllers.MatHanhTrinhTTKT;

import nocsystem.indexmanager.controllers.lastmile_cldv.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.MatHanhTrinhDetailBodyDto;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.MatHanhTrinhListBodyDto;

import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.ResponseMatHanhTrinhDetailDto;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.ResponseMatHanhTrinhTTKTList;

import nocsystem.indexmanager.services.MatHanhTrinhTTKT.MatHanhTrinhTTKTServices;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1")
public class MatHanhTrinhTTKTController {

    @Autowired
    MatHanhTrinhTTKTServices matHanhTrinhTTKTServices;

    @PostMapping("/dash-mobile/log-webview/nguy-co-mat-hanh-trinh-ttkt/list")
    public ResponseEntity<?> fillAllDataShow(@RequestBody MatHanhTrinhListBodyDto body) throws Exception {
        SimpleAPIResponse formatRes = new SimpleAPIResponse();
        ResponseMatHanhTrinhTTKTList resList = matHanhTrinhTTKTServices.getList(body);
        formatRes.setData(resList);

        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8").body(formatRes);
    }

    @PostMapping("/dash-mobile/log-webview/nguy-co-mat-hanh-trinh-ttkt/detail")
    public ResponseEntity<?> detailData(@RequestBody MatHanhTrinhDetailBodyDto bodyDetail) throws Exception {
        SimpleAPIResponse formatRes = new SimpleAPIResponse();
        Pageable paging = PageRequest.of(bodyDetail.pageIndex, bodyDetail.pageSize);


        CustomizeDataPage<ResponseMatHanhTrinhDetailDto> listContent = new CustomizeDataPage<>();
        listContent.setOffset(bodyDetail.pageIndex);
        listContent.setLimit(bodyDetail.pageSize);

        Page<ResponseMatHanhTrinhDetailDto> resDetail = matHanhTrinhTTKTServices.getListDetail(bodyDetail);
        listContent.setTotal((int) resDetail.getTotalElements());

        listContent.setContent(resDetail.getContent());

        formatRes.setData(listContent);


        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8").body(formatRes);
    }

}
