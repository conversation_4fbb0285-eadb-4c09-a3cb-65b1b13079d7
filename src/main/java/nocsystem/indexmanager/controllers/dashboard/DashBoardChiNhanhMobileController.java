package nocsystem.indexmanager.controllers.dashboard;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.constants.DashChiNhanhMobileConstants;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TonChuaPCPListingResponse;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TonHanhTrinhListingResponse;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonDto;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonDtoNoVungCon;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatNguongService;
import nocsystem.indexmanager.services.DashBoardChiNhanh.DBChiNhanhBuuCucService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/dash-mobile")
public class DashBoardChiNhanhMobileController {

    @Autowired
    private DBChiNhanhBuuCucService dbChiNhanhBuuCucService;

    @Autowired
    private TonPhatNguongService tonPhatNguongService;

    @GetMapping("/dashboard/cn-bc/tong-ton")
    public ResponseEntity<SimpleAPIResponse> getDashboardTongTon(@RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                 @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
                                                                 @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc) throws SQLException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        DieuHanhTonTongResponse dieuHanhTonTongResponse = dbChiNhanhBuuCucService.dashBoardTongTon(ngayBaoCao, maChiNhanh, maBuuCuc);
        response.setData(dieuHanhTonTongResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/cn-bc/bieu-do-tong-ton")
    public ResponseEntity<SimpleAPIResponse> getBieuDoTongTon(@RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                              @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
                                                              @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc) {
        List<BieuDoTonTongResponse> bieuDoTonTongResponseList = dbChiNhanhBuuCucService.bieuDoTongTon(ngayBaoCao, maChiNhanh, maBuuCuc);
        SimpleAPIResponse response = new SimpleAPIResponse(bieuDoTonTongResponseList);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/cn-bc/bieu-do-ton-nhap-may")
    public ResponseEntity<SimpleAPIResponse> getBieuDoTonNhapMayChuaKetNoi(@RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                           @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
                                                                           @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc) throws SQLException {
        List<TonNhapMayChuaKetNoiResponse> tonNhapMayChuaKetNoiResponses = new ArrayList<>();
//        List<TonNhapMayChuaKetNoiResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.bieuDoTonChuaNhapMay(ngayBaoCao, maChiNhanh, maBuuCuc);
        SimpleAPIResponse response = new SimpleAPIResponse(tonNhapMayChuaKetNoiResponses);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/cn-bc/ton-hanh-trinh")
    public SimpleAPIResponse getTonHanhTrinh(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonHanhTrinh(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/new-ton-hanh-trinh")
    public SimpleAPIResponse getTonHanhTrinhNew(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "loaiDon", defaultValue = "") String loaiDon,
            @RequestParam(value = "khDacThuGui", defaultValue = "0") String khDacThuGui,
            @RequestParam(value = "khDacThuNhan", defaultValue = "0") String khDacThuNhan,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        List<Integer> list_trang_thai = null;
        if(trangThai != null){
            list_trang_thai = new ArrayList<>();
            list_trang_thai.add(trangThai);
        }
        ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonHanhTrinh(ngayBaoCao, maChiNhanh, maBuuCuc, 0, page, pageSize, vungCon, doiTac, loaiHang, list_trang_thai,loaiDon,khDacThuGui,khDacThuNhan, null);

        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-nhap-may")
    public SimpleAPIResponse getTonNhapMay(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonNhapMay(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/new-ton-nhap-may")
    public SimpleAPIResponse getTonNhapMayNew(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonNhapMay(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-nhap-may-bill")
    public SimpleAPIResponse getTonNhapMayDetail(
            @RequestParam(value = "ma_phieugui", defaultValue = "") String maPhieuGui) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonDetailBillResponse tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonNhapMayBill(maPhieuGui);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }


    @GetMapping("/dashboard/cn-bc/ton-trang-thai-bill")
    public SimpleAPIResponse getTonHanhTrinhDetail(
            @RequestParam(value = "ma_phieugui", defaultValue = "") String maPhieuGui) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonDetailBillResponse tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonHanhTrinhBill(maPhieuGui);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-chua-pcp-bill")
    public SimpleAPIResponse getTonChuaPcpDetail(
            @RequestParam(value = "ma_phieugui", defaultValue = "") String maPhieuGui) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonDetailBillResponse tonChuaPcpResponses = dbChiNhanhBuuCucService.tonChuaPcpBill(maPhieuGui);
        simpleAPIResponse.setData(tonChuaPcpResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-phat-bill")
    public SimpleAPIResponse getPhatDetail(
            @RequestParam(value = "ma_phieugui", defaultValue = "") String maPhieuGui) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TonDetailBillResponse tonChuaPcpResponses = tonPhatNguongService.tonPhatBill(maPhieuGui);
        simpleAPIResponse.setData(tonChuaPcpResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-chua-pcp")
    public SimpleAPIResponse getChuaPCP(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonChuaPCPListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonChuaPCP(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/new-ton-chua-pcp")
    public SimpleAPIResponse getChuaPCPNew(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonChuaPCPListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonChuaPCP(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }


//    @GetMapping("/dashboard/cn-bc/tong-hop-ton")
//    public SimpleAPIResponse getTongHopTon(
//            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
//            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
//            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
//            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
//            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
//            @RequestParam(value = "order", defaultValue = "") String sort,
//            @RequestParam(value = "order_by", defaultValue = "") String sortBy,
//            @RequestParam(value = "page", defaultValue = "0") Integer page,
//            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
//        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
//        if (page > 0) page--;
//        ListContentPageDto<TongHopTonDtoNoVungCon> ton = dbChiNhanhBuuCucService.tonghopTonMobile(ngayBaoCao, maChiNhanh, maBuuCuc, page, pageSize, vungCon, doiTac, sort, sortBy);
//        simpleAPIResponse.setData(ton);
//        return simpleAPIResponse;
//    }

    @GetMapping("/dashboard/cn-bc/tong-hop-ton")
    public SimpleAPIResponse getTongHopTon(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "order", defaultValue = "") String sort,
            @RequestParam(value = "order_by", defaultValue = "") String sortBy,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TongHopTonDto> ton = dbChiNhanhBuuCucService.tonghopTon(ngayBaoCao, maChiNhanh, maBuuCuc, page, pageSize, vungCon, doiTac, sort, sortBy, loaiHang);
        simpleAPIResponse.setData(ton);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/tong-hop-ton-sum")
    public SimpleAPIResponse getTongHopTonSum(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TongHopTonDto ton = dbChiNhanhBuuCucService.tonghopTonSum(ngayBaoCao, maChiNhanh, maBuuCuc, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(ton);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/buu-ta-ton-phat")
    public SimpleAPIResponse getTonPhatBuuTa(
            @RequestParam(value = "buuTa", defaultValue = "") String buuTa
    ) {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        SanLuongTonBuuTa ton = dbChiNhanhBuuCucService.getTonPhatBuuTa(buuTa);
        simpleAPIResponse.setData(ton);
        return simpleAPIResponse;
    }
//    @GetMapping("/dashboard/cn-bc/buu-ta-ton-thu")
//    public SimpleAPIResponse getTonThuBuuTa(
//            @RequestParam(value = "buuTa", defaultValue = "") String buuTa
//    ) {
//        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
//        SanLuongTonBuuTa ton = dbChiNhanhBuuCucService.getTonThuBuuTa(buuTa);
//        simpleAPIResponse.setData(ton);
//        return simpleAPIResponse;
//    }

    @GetMapping("/dashboard/cn-bc/list-bill-buu-ta-ton-phat")
    public SimpleAPIResponse getListBillTonPhatBuuTa(
            @RequestParam(value = "trangThai", defaultValue = DashChiNhanhMobileConstants.DenHanPhat) String trangThai,
            @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
            @RequestParam(value = "pageSize", defaultValue = "30") Integer pageSize,
            @RequestParam(value = "buuTa", defaultValue = "") String buuTa,
            @RequestParam(value = "sort", defaultValue = "") String sort
    ) {
        if (pageIndex > 0) pageIndex--;
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        Page<BillDetailPhat> list = dbChiNhanhBuuCucService.getListBillTonPhatBuuTa(buuTa, trangThai, pageIndex, pageSize, sort);
        ListContentPageDto data = new ListContentPageDto(list);
        simpleAPIResponse.setData(data);
        return simpleAPIResponse;
    }

//    @GetMapping("/dashboard/cn-bc/list-bill-buu-ta-ton-thu")
//    public SimpleAPIResponse getListBillTonThuBuuTa(
//            @RequestParam(value = "trangThai", defaultValue = DashChiNhanhMobileConstants.DenHanTHU) String trangThai,
//            @RequestParam(value = "buuTa", defaultValue = "") String buuTa,
//            @RequestParam(value = "pageIndex", defaultValue = "1") Integer pageIndex,
//            @RequestParam(value = "pageSize", defaultValue = "30") Integer pageSize,
//            @RequestParam(value = "sort", defaultValue = "") String sort
//    ) {
//        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
//        if (pageIndex > 0) pageIndex--;
//        Page<BillDetailThu> list = dbChiNhanhBuuCucService.getListBillTonThuBuuTa(buuTa, trangThai, pageIndex, pageSize, sort);
//        ListContentPageDto data = new ListContentPageDto(list);
//        simpleAPIResponse.setData(data);
//        return simpleAPIResponse;
//    }

    @GetMapping("/dashboard/cn-bc/bieu-do-xu-the-ton")
    public SimpleAPIResponse getBieuDoXuTheTon(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc) {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        BieuDoXuTheTonResponse responses = dbChiNhanhBuuCucService.getBieuDoXuTheTon(ngayBaoCao, maChiNhanh, maBuuCuc);
        simpleAPIResponse.setData(responses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/new-ton-nhan-ban-giao")
    public SimpleAPIResponse getTonNhanBanGiao(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonChuaPCPListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonNhanBanGiao(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/new-ton-chua-co-hanh-trinh")
    public SimpleAPIResponse getTonChuaCoHanhTrinh(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "loaiCanhBao", defaultValue = "") String loaiCanhBao,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonChuaPCPListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonChuaCoHanhTrinh(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang, loaiCanhBao);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/new-ton-kien-cham")
    public SimpleAPIResponse getKienTonCham(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonHanhTrinhListingResponse> tonKienChamResponse = dbChiNhanhBuuCucService.tonKienCham(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonKienChamResponse);
        return simpleAPIResponse;
    }



}
