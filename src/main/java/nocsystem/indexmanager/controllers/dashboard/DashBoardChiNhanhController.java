package nocsystem.indexmanager.controllers.dashboard;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TonChuaPCPListingResponse;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TonDvGiaTangListingResponse;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TonHanhTrinhListingResponse;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.TongHopTonDto;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.services.DashBoardChiNhanh.DBChiNhanhBuuCucService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class DashBoardChiNhanhController {

    @Autowired
    private DBChiNhanhBuuCucService dbChiNhanhBuuCucService;

    @GetMapping("/dashboard/cn-bc/tong-ton")
    public ResponseEntity<SimpleAPIResponse> getDashboardTongTon(@RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                 @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
                                                                 @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc) throws SQLException {
        SimpleAPIResponse response = new SimpleAPIResponse();
        DieuHanhTonTongResponse dieuHanhTonTongResponse = dbChiNhanhBuuCucService.dashBoardTongTon(ngayBaoCao, maChiNhanh, maBuuCuc);
        response.setData(dieuHanhTonTongResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/cn-bc/bieu-do-tong-ton")
    public ResponseEntity<SimpleAPIResponse> getBieuDoTongTon(@RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                              @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
                                                              @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc) {
        List<BieuDoTonTongResponse> bieuDoTonTongResponseList = dbChiNhanhBuuCucService.bieuDoTongTon(ngayBaoCao, maChiNhanh, maBuuCuc);
        SimpleAPIResponse response = new SimpleAPIResponse(bieuDoTonTongResponseList);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/cn-bc/bieu-do-ton-nhap-may")
    public ResponseEntity<SimpleAPIResponse> getBieuDoTonNhapMayChuaKetNoi(@RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                              @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
                                                              @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc) throws SQLException {
        List<TonNhapMayChuaKetNoiResponse> tonNhapMayChuaKetNoiResponses = new ArrayList<>();
//        List<TonNhapMayChuaKetNoiResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.bieuDoTonChuaNhapMay(ngayBaoCao, maChiNhanh, maBuuCuc);
        SimpleAPIResponse response = new SimpleAPIResponse(tonNhapMayChuaKetNoiResponses);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/cn-bc/ton-hanh-trinh")
    public SimpleAPIResponse getTonHanhTrinh(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trang_thai", defaultValue = "") List<Integer> trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "loai_don", defaultValue = "") String loaiDon,
            @RequestParam(value = "kh_dacthu_gui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "kh_dacthu_nhan", defaultValue = "") String khDacThuNhan,
            @RequestParam(value = "loai_canh_bao") String loaiCanhBao,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonHanhTrinh(ngayBaoCao, maChiNhanh, maBuuCuc, 0, page, pageSize, vungCon, doiTac, loaiHang, trangThai,loaiDon,khDacThuGui,khDacThuNhan, loaiCanhBao);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-nhap-may")
    public SimpleAPIResponse getTonNhapMay(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonNhapMay(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-kien-cham")
    public SimpleAPIResponse getKienTonCham(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonHanhTrinhListingResponse> tonKienChamResponse = dbChiNhanhBuuCucService.tonKienCham(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonKienChamResponse);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-chua-co-hanh-trinh")
    public SimpleAPIResponse getTonChuaCoHanhTrinh(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "loaiCanhBao", defaultValue = "") String loaiCanhBao,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonChuaPCPListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonChuaCoHanhTrinh(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang, loaiCanhBao);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-nhan-ban-giao")
    public SimpleAPIResponse getTonNhanBanGiao(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,

            @RequestParam(value = "loaiCanhBao", defaultValue = "") String loaiCanhBao,

            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonChuaPCPListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonNhanBanGiao(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang, loaiCanhBao);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-dich-vu-gia-tang")
    public SimpleAPIResponse getTonDvGiaTang(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String doiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonDvGiaTangListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonDvGiaTang(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/ton-chua-pcp")
    public SimpleAPIResponse getChuaPCP(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TonChuaPCPListingResponse> tonNhapMayChuaKetNoiResponses = dbChiNhanhBuuCucService.tonChuaPCP(ngayBaoCao, maChiNhanh, maBuuCuc, trangThai, page, pageSize, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(tonNhapMayChuaKetNoiResponses);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/tong-hop-ton")
    public SimpleAPIResponse getTongHopTon(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "order", defaultValue = "") String sort,
            @RequestParam(value = "order_by", defaultValue = "") String sortBy,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "15") Integer pageSize) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        if (page > 0) page--;
        ListContentPageDto<TongHopTonDto> ton = dbChiNhanhBuuCucService.tonghopTon(ngayBaoCao, maChiNhanh, maBuuCuc, page, pageSize, vungCon, doiTac, sort, sortBy, loaiHang);
        simpleAPIResponse.setData(ton);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/tong-hop-ton-sum")
    public SimpleAPIResponse getTongHopTonSum(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "doi_tac", defaultValue = "") String doiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        TongHopTonDto ton = dbChiNhanhBuuCucService.tonghopTonSum(ngayBaoCao, maChiNhanh, maBuuCuc, vungCon, doiTac, loaiHang);
        simpleAPIResponse.setData(ton);
        return simpleAPIResponse;
    }

    @GetMapping("/dashboard/cn-bc/all-ma-doi-tac")
    public SimpleAPIResponse getAllMaDoiTac(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) throws SQLException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<String> doiTac = dbChiNhanhBuuCucService.getMaDoiTac(ngayBaoCao);
        simpleAPIResponse.setData(doiTac);
        return simpleAPIResponse;
    }

}
