package nocsystem.indexmanager.controllers.dashboard;

import nocsystem.indexmanager.services.DashBoardChiNhanh.DashCNBCExcelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class DashCNBCExcelController {

    @Autowired
    private DashCNBCExcelService dashCNBCExcelService;

    @GetMapping("/dashboard/cn-bc/ton-hanh-trinh-excel")
    public void getDashboardHanhTrinh(
            HttpServletResponse response,
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "trang_thai", defaultValue = "") List<Integer> trangThai,
            @RequestParam(value = "loai_don", defaultValue = "") String loaiDon,
            @RequestParam(value = "loai_canh_bao", defaultValue = "") String loaiCanhBao,
            @RequestParam(value = "kh_dacthu_gui", defaultValue = "") String khDacThuGui,
            @RequestParam(value = "kh_dacthu_nhan", defaultValue = "") String khDacThuNhan
    ) throws IOException, IllegalAccessException, SQLException {
        dashCNBCExcelService.exportExcelTonKhacToiUu(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                0,
                vungCon,
                maDoiTac,
                "TonHanhTrinh", loaiHang, trangThai,loaiDon,khDacThuGui,khDacThuNhan, loaiCanhBao
        );
    }

    @GetMapping("/dashboard/cn-bc/ton-chua-pcp-excel")
    public void getDashboardChuaPCP(
            HttpServletResponse response,
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai
    ) throws IOException, IllegalAccessException, SQLException {
        dashCNBCExcelService.exportExcelChuaPCP(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                trangThai,
                vungCon,
                maDoiTac, loaiHang
        );
    }

    @GetMapping("/dashboard/cn-bc/ton-nhap-may-excel")
    public void getDashboardNhapMay(
            HttpServletResponse response,
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai
    ) throws IOException, IllegalAccessException, SQLException {
        dashCNBCExcelService.exportExcelHanhTrinh(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                trangThai,
                vungCon,
                maDoiTac,
                "TonNhapMay", loaiHang
        );
    }

    @GetMapping("/dashboard/cn-bc/ton-kien-cham-excel")
    public void getDashboardKienCham(
            HttpServletResponse response,
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "vung_con", defaultValue = "") String vungCon,
            @RequestParam(value = "doi_tac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "loai_hh", defaultValue = "") String loaiHang,
            @RequestParam(value = "trang_thai", defaultValue = "0") Integer trangThai
    ) throws IOException, IllegalAccessException, SQLException {
        dashCNBCExcelService.exportExcelHanhTrinh(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                trangThai,
                vungCon,
                maDoiTac,
                "TonKienCham", loaiHang
        );
    }

    @GetMapping("/dashboard/cn-bc/ton-chua-co-hanh-trinh-excel")
    public void getExcelChuaCoHanhTrinh(
            HttpServletResponse response,
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai,
            @RequestParam(value = "loaiCanhBao", defaultValue = "") String loaiCanhBao
    ) throws IOException, IllegalAccessException, SQLException {
        dashCNBCExcelService.exportExcelChuaCoHanhTrinh(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                trangThai,
                vungCon,
                maDoiTac,
                loaiHang,
                loaiCanhBao
        );
    }

    @GetMapping("/dashboard/cn-bc/ton-nhan-ban-giao-excel")
    public void getExcelTonNhanBanGiao(
            HttpServletResponse response,
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "loaiCanhBao", defaultValue = "") String loaiCanhBao,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai
    ) throws IOException, IllegalAccessException, SQLException {
//        dashCNBCExcelService.exportExcelNhanBanGiao(
        dashCNBCExcelService.exportExcelTonKhacToiUu(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                trangThai,
                vungCon,
                maDoiTac,
                "TonNhanBanGiao",
                loaiHang,
                null,
                "all",
                "0",
                "0",
                loaiCanhBao
        );
    }

    @GetMapping("/dashboard/cn-bc/ton-dich-vu-gia-tang-excel")
    public void getExcelDvCongThem(
            HttpServletResponse response,
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "maChiNhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "maBuuCuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "vungCon", defaultValue = "") String vungCon,
            @RequestParam(value = "doiTac", defaultValue = "") String maDoiTac,
            @RequestParam(value = "loaiHH", defaultValue = "") String loaiHang,
            @RequestParam(value = "trangThai", defaultValue = "0") Integer trangThai
    ) throws IOException, IllegalAccessException, SQLException {
//        dashCNBCExcelService.exportExcelDvGiaTang(
        dashCNBCExcelService.exportExcelDvGiaTangToiUu(
                response,
                ngayBaoCao,
                maChiNhanh,
                maBuuCuc,
                trangThai,
                vungCon,
                maDoiTac,
                "",
                loaiHang
        );
    }


}
