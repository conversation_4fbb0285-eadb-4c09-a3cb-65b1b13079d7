package nocsystem.indexmanager.controllers.KpiDayChuyenChiaChonTTKT5;

import nocsystem.indexmanager.controllers.lastmile_cldv.SimpleAPIResponse;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonBodyDto;
import nocsystem.indexmanager.models.Response.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonResponse;
import nocsystem.indexmanager.services.KpiDayChuyenChiaChonTTKT5.KpiDayChuyenChiaChonTTKT5Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1")
public class KpiDayChuyenChiaChonTTKT5Controller {

    @Autowired
    private KpiDayChuyenChiaChonTTKT5Service kpiDayChuyenChiaChonTTKT5Service;

    @PostMapping("/kpi-day-chuyen-chia-chon-ttkt5")
    public ResponseEntity<?> listData(@RequestBody KpiDayChuyenChiaChonBodyDto body) {

        SimpleAPIResponse formatRes = new SimpleAPIResponse();
        Page<KpiDayChuyenChiaChonResponse> resDetail = kpiDayChuyenChiaChonTTKT5Service.getListDetail(body);

        CustomizeDataPage<KpiDayChuyenChiaChonResponse> listContent = new CustomizeDataPage<>();
        listContent.setOffset(body.pageIndex);
        listContent.setLimit(body.pageSize);
        listContent.setTotal((int) resDetail.getTotalElements());
        listContent.setContent(resDetail.getContent());

        formatRes.setData(listContent);

        return ResponseEntity.ok().body(formatRes);
    }
}
