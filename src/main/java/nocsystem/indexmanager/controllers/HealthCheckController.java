package nocsystem.indexmanager.controllers;

import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Test;
import nocsystem.indexmanager.partners.wms.controller.WmsController;
import nocsystem.indexmanager.repositories.TestRepository;
import nocsystem.indexmanager.services.TestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
@Slf4j
public class HealthCheckController {
    private final Logger log = LoggerFactory.getLogger(HealthCheckController.class);

    private final TestService testService;

    @Autowired
    private TestRepository testRepository;

    @Autowired
    private RedisTemplate template;

    public HealthCheckController(TestService testService) {
        this.testService = testService;
    }

    @GetMapping("/heath-check/ping")
    public String returnHealthCheckServer() {
        return "Pong";
    }

    @GetMapping("/heath-check/connect-db")

    public SimpleAPIResponse getAllAuthorities() {
        List<Test> test = testService.findAllData();
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(test);
        return simpleAPIResponse;
    }

    @GetMapping("/heath-check/redis")
    public SimpleAPIResponse checkRedis() {
        try {
            template.opsForValue().set("loda", "hello world");
            String value_key = (String) template.opsForValue().get("loda");

            if (!value_key.isEmpty()) {
                SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
                simpleAPIResponse.setMessage("Pong");
                return simpleAPIResponse;
            }
        } catch (Exception exception) {
            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            simpleAPIResponse.setError(502);
            simpleAPIResponse.setMessage(exception.getMessage());
            return simpleAPIResponse;
        }
        return null;
    }

    @GetMapping("/heath-check/test-log")
    public SimpleAPIResponse testLog(
            @RequestParam(required = false) int a,
            @RequestParam(required = false) int b,
            @RequestParam("throw_exception") String throwException
    ) throws Exception {
        if (throwException.equals("true")) {
            throw new Exception("Debug with exception");
        }

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(1 / (a - b));
        return simpleAPIResponse;
    }

    @GetMapping("/my-endpoint")
    public String myEndpoint(HttpServletRequest request) throws Exception {
        // Access the request ID associated with the current request
        String requestId = (String) request.getAttribute("requestId");

        // Use the request ID as needed
        // ...
        log.error("NOC-INDEX logging request", request);
        return null;
//        throw new Exception("1212121212");

//        return "Request ID: " + requestId;
    }
}
