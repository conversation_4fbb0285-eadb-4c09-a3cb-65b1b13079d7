package nocsystem.indexmanager.controllers.LogWebview;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.LogWebview.*;
import nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.SanLuongXeTinhRequestBody;
import nocsystem.indexmanager.services.LogWebview.ChiTietXeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/api/v1")
public class SanLuongXeController {

    @Autowired
    private ChiTietXeService chiTietXeService;

    //api cho MHL TTKT5
    @GetMapping("log-webview/chi-tiet-xe-cho-khai-thac")
    public SimpleAPIResponse xeChoKhaiThacTTKT5(
            @RequestParam(value = "dvvc", defaultValue = "") String dvvc,
            @RequestParam(value = "hanhTrinh", defaultValue = "") String hanhTrinh,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<ChiTietXeDTO> chiTietXe;

        if (page > 0) page--;
        chiTietXe = chiTietXeService.chiTietXeTTKT5(1, dvvc , hanhTrinh, page, pageSize);

        simpleAPIResponse.setData(chiTietXe);

        return simpleAPIResponse;
    }

    @GetMapping("log-webview/chi-tiet-xe-dang-khai-thac")
    public SimpleAPIResponse xeDangKhaiThacTTKT5(
            @RequestParam(value = "dvvc", defaultValue = "") String dvvc,
            @RequestParam(value = "hanhTrinh", defaultValue = "") String hanhTrinh,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<ChiTietXeDTO> chiTietXe;

        if (page > 0) page--;
        chiTietXe = chiTietXeService.chiTietXeTTKT5(2, dvvc , hanhTrinh, page, pageSize);

        simpleAPIResponse.setData(chiTietXe);

        return simpleAPIResponse;
    }

    @GetMapping("log-webview/chi-tiet-xe-du-kien-den")
    public SimpleAPIResponse xeDuKienDenTTKT5(
            @RequestParam(value = "dvvc", defaultValue = "") String dvvc,
            @RequestParam(value = "hanhTrinh", defaultValue = "") String hanhTrinh,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<ChiTietXeDTO> chiTietXe;

        if (page > 0) page--;
        chiTietXe = chiTietXeService.chiTietXeTTKT5(3, dvvc , hanhTrinh, page, pageSize);

        simpleAPIResponse.setData(chiTietXe);

        return simpleAPIResponse;
    }

    //api cho sản lượng xe webview

    @PostMapping("dash-mobile/log-webview/xe-cho-khai-thac")
    public SimpleAPIResponseWithSum xeChoKhaiThac(
            @RequestBody SanLuongXeRequestBody body
            ) throws IOException {
        Integer page = body.getPage();
        if (page > 0) page--;
        return chiTietXeService.sanLuongXeChoKhaiThac(body.getDvvc() , body.getHanhTrinh(), body.getTtkt(), page, body.getPageSize());
    }

    @PostMapping("dash-mobile/log-webview/xe-cho-khai-thac-tinh")
    public SimpleAPIResponseWithSum xeChoKhaiThacTinh(
            @RequestBody SanLuongXeTinhRequestBody body
    ) throws IOException {
        return chiTietXeService.sanLuongXeChoKhaiThacTinh(body.getDvvc(), body.getHanhTrinh(), body.getChiSo(), body.getPage(), body.getPageSize(), body.getOrder(), body.getOrderBy());
    }

    @PostMapping("dash-mobile/log-webview/xe-cho-khai-thac-tinh/all-xe")
    public SimpleAPIResponseV2 xeChoKhaiThacTinhAllXe(
            @RequestBody SanLuongXeTinhRequestBody body
    ) throws IOException {
        return chiTietXeService.sanLuongXeChoKhaiThacTinhAllXe(body.getDvvc(), body.getHanhTrinh(), body.getTtkt(), body.getThoiGian(), body.getPage(), body.getPageSize());
    }

    @GetMapping("dash-mobile/log-webview/xe-cho-khai-thac-tinh/detail-xe")
    public SimpleAPIResponseV2 xeChoKhaiThacTinhDetailXe(
            @RequestParam(value = "bienSoXe", defaultValue = "") String bienSoXe
    ) throws IOException {
        return chiTietXeService.sanLuongXeChoKhaiThacTinhDetailXe(bienSoXe);
    }

    @PostMapping("dash-mobile/log-webview/xe-dang-khai-thac-tinh")
    public SimpleAPIResponseWithSum xeDangKhaiThacTinh(
            @RequestBody SanLuongXeTinhRequestBody body
    ) throws IOException {
        return chiTietXeService.sanLuongXeDangKhaiThacTinh(body.getDvvc(), body.getHanhTrinh(), body.getChiSo(), body.getPage(), body.getPageSize(), body.getOrder(), body.getOrderBy());
    }

    @PostMapping("dash-mobile/log-webview/xe-dang-khai-thac-tinh/all-xe")
    public SimpleAPIResponseV2 xeDangKhaiThacTinhAllXe(
            @RequestBody SanLuongXeTinhRequestBody body
    ) throws IOException {
        return chiTietXeService.sanLuongXeDangKhaiThacTinhAllXe(body.getDvvc(), body.getHanhTrinh(), body.getTtkt(), body.getPage(), body.getPageSize());
    }

    @GetMapping("dash-mobile/log-webview/xe-dang-khai-thac-tinh/detail-xe")
    public SimpleAPIResponseV2 xeDangKhaiThacTinhDetailXe(
            @RequestParam(value = "bienSoXe", defaultValue = "") String bienSoXe
    ) throws IOException {
        return chiTietXeService.sanLuongXeDangKhaiThacTinhDetailXe(bienSoXe);
    }

    @PostMapping("dash-mobile/log-webview/xe-dang-khai-thac")
    public SimpleAPIResponseWithSum xeDangKhaiThac(
            @RequestBody SanLuongXeRequestBody body
    ) throws IOException {
        Integer page = body.getPage();
        if (page > 0) page--;
        return chiTietXeService.sanLuongXeDangKhaiThac(body.getDvvc() , body.getHanhTrinh(), body.getTtkt(), page, body.getPageSize());
    }

    @GetMapping("dash-mobile/log-webview/xe-du-kien-den")
    public SimpleAPIResponse xeDuKienDen(
            @RequestParam(value = "dvvc", defaultValue = "") String dvvc,
            @RequestParam(value = "hanhTrinh", defaultValue = "") String hanhTrinh,
            @RequestParam(value = "ttkt", defaultValue = "") String ttkt,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<ChiTietXeDTO> chiTietXe = new ListContentPageDto<>();

        if (page > 0) page--;
//        chiTietXe = chiTietXeService.chiTietXe(3, dvvc , hanhTrinh, ttkt, page, pageSize);

        simpleAPIResponse.setData(chiTietXe);

        return simpleAPIResponse;
    }

    @GetMapping("dash-mobile/log-webview/chi-tiet-chuyen-xe")
    public SimpleAPIResponse xeDuKienDen(
            @RequestParam(value = "maChuyenXe") String maChuyenXe
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ChiTietChuyenXeDTO chiTietChuyenXe = chiTietXeService.chiTietChuyenXe(maChuyenXe);

        simpleAPIResponse.setData(chiTietChuyenXe);

        return simpleAPIResponse;
    }

    @PostMapping("dash-mobile/log-webview/xe-cho-khai-thac-detail")
    public SimpleAPIResponse chiTietXeChoKhaiThac(
            @RequestBody SanLuongXeRequestBody body
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<ChiTietXeDTO> chiTietXe;

        Integer page = body.getPage();
        if (page > 0) page--;
        chiTietXe = chiTietXeService.chiTietXe(1, body.getDvvc() , body.getHanhTrinh(), body.getTtkt(), body.getChiSo(), page, body.getPageSize());

        simpleAPIResponse.setData(chiTietXe);

        return simpleAPIResponse;
    }

    @PostMapping("dash-mobile/log-webview/xe-dang-khai-thac-detail")
    public SimpleAPIResponse chiTietXeDangKhaiThac(
            @RequestBody SanLuongXeRequestBody body
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<ChiTietXeDTO> chiTietXe;

        Integer page = body.getPage();
        if (page > 0) page--;
        chiTietXe = chiTietXeService.chiTietXe(2, body.getDvvc() , body.getHanhTrinh(), body.getTtkt(), body.getChiSo(), page, body.getPageSize());

        simpleAPIResponse.setData(chiTietXe);

        return simpleAPIResponse;
    }
}
