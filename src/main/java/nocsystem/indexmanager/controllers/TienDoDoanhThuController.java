package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.services.ChiSoKinhDoanh.ChiSoKinDoanhService;
import nocsystem.indexmanager.services.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCService;
import nocsystem.indexmanager.services.ThongKeTongDoanhThu.ThongKeTongDoanhThuCNService;
import nocsystem.indexmanager.services.TienDoDoanhThu.TienDoDoanhThuBCService;
import nocsystem.indexmanager.services.TienDoDoanhThu.TienDoDoanhThuChiNhanhService;
import nocsystem.indexmanager.services.TienDoDoanhThu.TienDoDoanhThuService;
import nocsystem.indexmanager.services.TongDoanhThu.TongDoanhThuCNService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/v1")
public class TienDoDoanhThuController {
    @Autowired
    private TienDoDoanhThuBCService tienDoDoanhThuBCService;
    @Autowired
    private TienDoDoanhThuChiNhanhService doanhThuChiNhanhService;
    @Autowired
    private ThongKeTongDoanhThuCNService thongKeTongDoanhThuCNService;
    @Autowired
    private TongDoanhThuCNService tongDoanhThuCNService;
    @Autowired
    private ChiSoKinDoanhService chiSoKinDoanhService;
    @Autowired
    private ThongKeTongDoanhThuBCService thongKeTongDoanhThuBCService;
    @Autowired
    private TienDoDoanhThuService tienDoDoanhThuService;

    @GetMapping("/tien-do-doanh-thu")
    public SimpleAPIResponse revenue(
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "nhom_doanhthu", defaultValue = "0") Integer nhomDoanhThu,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize
    ) {
        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            CustomizeDataPage<TienDoDoanhThuCNV1ResDto> tienDoDoanhThuChiNhanh =
                    doanhThuChiNhanhService.findData(maChiNhanh, nhomDoanhThu, toTime, pageSize, page);
            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            simpleAPIResponse.setData(tienDoDoanhThuChiNhanh);
            return simpleAPIResponse;
        }

        CustomizeDataPage<TienDoDoanhThuBCV1ResDto> tienDoDoanhThuBuuCuc =
                tienDoDoanhThuBCService.findAllData(maBuuCuc, maChiNhanh, nhomDoanhThu, toTime, pageSize, page);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDoanhThuBuuCuc);
        return simpleAPIResponse;
    }

    /**
     * @param maChiNhanh
     * @param maBuuCuc
     * @param nhomDoanhThu
     * @param toTime
     * @return
     */
    @GetMapping("/tong-tien-do-doanh-thu")
    public SimpleAPIResponse revenue(
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "nhom_doanhthu", defaultValue = "0") Integer nhomDoanhThu,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime
    ) {
        return tienDoDoanhThuService.tongTienDoDoanhThu(maChiNhanh, maBuuCuc, nhomDoanhThu, toTime);
    }
}