package nocsystem.indexmanager.controllers.supper_set;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;
import nocsystem.indexmanager.request.SupperSetReq;
import nocsystem.indexmanager.servicesIpm.SupperSet.SupperSetServiceIpm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1")
public class SupperSetController {
    @Autowired
    private SupperSetServiceIpm supperSetServiceIpm;

    @GetMapping("/sql-context")
    public List<Map<String, Object>> tongDoanhThuCP(
            @RequestParam(value = "sql", required = true) String sqlContext,
            @RequestParam(value = "db_name", required = true) String dbName,
            @RequestParam(value = "debug", defaultValue = "") String debug,
            @RequestParam(value = "limit", defaultValue = "0") int limit,
            @RequestParam(value = "offset", defaultValue = "0") int offset
    ) throws SQLException {
        SupperSetReq supperSetReq = new SupperSetReq();
        supperSetReq.setSql(sqlContext);
        supperSetReq.setDbName(dbName);
        supperSetReq.setOffset(offset);
        supperSetReq.setLimit(limit);

        return supperSetServiceIpm.getDataOfQueryBuilder(supperSetReq);
    }

    //    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/security/login")
    public SimpleAPIResponse securityLogin() throws IOException {
        return supperSetServiceIpm.securityLogin();
    }
}
