package nocsystem.indexmanager.controllers.sanLuongCamKet;

import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.sanLuongCamKet.SanLuongCamKet;
import nocsystem.indexmanager.services.sanLuongCamKet.SanLuongCamKetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;


@RestController
@RequestMapping("/api/v1")
public class SanLuongCamKetController {
    private static final String jsonContentType = "application/json;charset=UTF-8";

    @Autowired
    SanLuongCamKetService sanLuongCamKetService;

    @GetMapping("/san-luong-cam-ket")
    public ResponseEntity<SimpleAPIResponseV2> getSanLuongCamKet(
            @RequestParam(value = "ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "vung", defaultValue = "", required = false) String vung,
            @RequestParam(value = "chiNhanh", defaultValue = "", required = false) String chiNhanh,
            @RequestParam(value = "vungCon", defaultValue = "", required = false) String vungCon,
            @RequestParam(value = "buuCuc", defaultValue = "", required = false) String buuCuc,
            @RequestParam(value = "cusid", defaultValue = "", required = false) String cusid,
            @RequestParam(value = "dichVu", required = false) List<String> dichVu,
            @RequestParam(value = "order", defaultValue = "", required = false) String order,
            @RequestParam(value = "orderBy", defaultValue = "", required = false) String orderBy,
            @RequestParam(value = "page", defaultValue = "0", required = false) Integer page,
            @RequestParam(value = "pageSize", defaultValue = "30", required = false) Integer pageSize) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        ListContentPageDto<SanLuongCamKet> listContent = sanLuongCamKetService.getSanLuongCamKet(ngayBaoCao, vung, chiNhanh, vungCon, buuCuc, cusid, dichVu, order, orderBy, page, pageSize);
        responseV2.setData(listContent);
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/san-luong-cam-ket/excel")
    public ResponseEntity<SimpleAPIResponseV2> exportSanLuongCamKetExcel(
            @RequestParam(value = "ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "vung", defaultValue = "", required = false) String vung,
            @RequestParam(value = "chiNhanh", defaultValue = "", required = false) String chiNhanh,
            @RequestParam(value = "vungCon", defaultValue = "", required = false) String vungCon,
            @RequestParam(value = "buuCuc", defaultValue = "", required = false) String buuCuc,
            @RequestParam(value = "cusid", defaultValue = "", required = false) String cusid,
            @RequestParam(value = "dichVu", required = false) List<String> dichVu,
            HttpServletResponse response) throws NoSuchFieldException, IllegalAccessException {
        sanLuongCamKetService.exportSanLuongCamKetExcel(ngayBaoCao, vung, chiNhanh, vungCon, buuCuc, cusid, dichVu, response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, jsonContentType).body(null);
    }
}
