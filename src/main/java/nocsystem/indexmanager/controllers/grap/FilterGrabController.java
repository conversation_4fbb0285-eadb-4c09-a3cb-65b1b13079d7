package nocsystem.indexmanager.controllers.grap;

import nocsystem.indexmanager.exception.PermissionDeniedException;
import nocsystem.indexmanager.global.variable.UserInfo;
import nocsystem.indexmanager.global.variable.UserContext;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.List;

@Aspect
@Component
public class FilterGrabController {
    @Before("execution(* nocsystem.indexmanager.controllers.grap.ReconciliationGrabController.*(..))")
    public void beforeApiMethodExecution() {
        List<String> listAcceptRole = List.of("TCT_TAI_CHINH", "ROLE_ADMIN", "CHUYEN_QUAN_TCT_KD", "LANH_DAO_TTKD");
        UserInfo userInfo = UserContext.getUserData();
        if (!listAcceptRole.contains(userInfo.getRole())) {
            throw new PermissionDeniedException("<PERSON>hông có quyền truy cập chức năng.");
        }
    }
}
