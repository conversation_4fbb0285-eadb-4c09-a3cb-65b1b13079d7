package nocsystem.indexmanager.controllers.grap;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.request.FilterDetailReconciliationReq;
import nocsystem.indexmanager.request.FilterGrabFinancialStatementsReq;
import nocsystem.indexmanager.services.ReconciliationGrab.ReconciliationGrabService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/api/v1")
public class ReconciliationGrabController {
    @Autowired
    private ReconciliationGrabService reconciliationGrabService;

    /* Upload file đối soát từ grab */
    @PostMapping("/reconciliation-financial-upload")
    public SimpleAPIResponse reconciliationFinancial(
            @RequestBody MultipartFile file
    ) throws Exception {
        return reconciliationGrabService.ReconciliationFinancial(file);
    }

    /* Download file upload lỗi hoặc upload thành công */
    @GetMapping("/reconciliation-financial-download")
    public ResponseEntity<Resource> reconciliationFinancialDownload(
            @RequestParam(required = true, name = "file_name") String fileName,
            @RequestParam(required = true, name = "type_upload", defaultValue = "0") String typeUpload
    ) throws Exception {
        return reconciliationGrabService.ReconciliationFinancialDownload(fileName, typeUpload);
    }

    /* Lấy lịch sử upload file */
    @GetMapping("/reconciliation-upload-history")
    public SimpleAPIResponse reconciliationUploadHistory(
            @RequestParam(required = false, name = "file_name") String fileName,
            @RequestParam(required = false, name = "type_upload", defaultValue = "0") short typeUpload
    ) throws Exception {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(reconciliationGrabService.reconciliationUploadHistory(fileName, typeUpload));
        return simpleAPIResponse;
    }

    /* Lấy file đã upload thành công */
    @GetMapping("/reconciliation-upload-success")
    public SimpleAPIResponse reconciliationUploadSuccess(
            @RequestParam(required = true, name = "file_name") String fileName,
            @RequestParam(required = true, name = "type_upload", defaultValue = "0") short typeUpload
    ) throws Exception {
        return (SimpleAPIResponse) reconciliationGrabService.reconciliationUploadSuccess(fileName, typeUpload);
    }

    /* Đối soát giữa grab vs VTP */
    @GetMapping("/reconciliation")
    public SimpleAPIResponse reconciliationCalculate(
            @RequestParam(required = true, name = "file_id") int fileId
    ) throws Exception {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData("");
        return simpleAPIResponse;
//        return reconciliationGrabService.reconciliationCalculate(fileId);
    }

    /* Đối soát giữa grab vs VTP */
    @PostMapping("/filter-detail-reconciliation")
    public SimpleAPIResponse filterDetailReconciliation(
            @RequestBody FilterDetailReconciliationReq filterDetailReconciliationReq
    ) throws Exception {
        return reconciliationGrabService.filterDetailReconciliation(filterDetailReconciliationReq);
    }

    /* Báo cáo tổng hợp đối soát */
    @PostMapping("/filter-grab-financial-statements")
    public SimpleAPIResponse filterGrabFinancialStatements(
            @RequestBody FilterGrabFinancialStatementsReq filterGrabFinancialStatementsReq
    ) throws Exception {
        return reconciliationGrabService.filterGrabFinancialStatements(filterGrabFinancialStatementsReq);
    }

    /* Test cron-job */
    @GetMapping("/reconciliation-sync-data-cronJob")
    public SimpleAPIResponse reconciliationSyncDataCronJob(
            @RequestParam(required = false, name = "file_id") String fileId
    ) throws Exception {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(reconciliationGrabService.processToSaveDataBase());
        return simpleAPIResponse;
    }

    /* Báo cáo tổng hợp đối soát */
    @PostMapping("/export-excel-detail-reconciliation")
    public ResponseEntity<byte[]> exportExcelDetailReconciliation(
            @RequestBody FilterGrabFinancialStatementsReq filterGrabFinancialStatementsReq
    ) throws Exception {
        return reconciliationGrabService.exportExcelDetailReconciliation(filterGrabFinancialStatementsReq);
    }


    @PostMapping("/export-grab-detail-reconciliation")
    public ResponseEntity<?> getExcelBaoCaoCongNoTonLkn(HttpServletResponse response, @RequestBody FilterDetailReconciliationReq filterDetailReconciliationReq
    ) throws IOException, IllegalAccessException {
        reconciliationGrabService.exportExcelChiTietGrab(response, filterDetailReconciliationReq);
        return ResponseEntity.ok().body(null);
    }
}
