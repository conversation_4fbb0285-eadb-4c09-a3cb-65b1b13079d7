package nocsystem.indexmanager.controllers;

import lombok.RequiredArgsConstructor;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.models.Response.BaoCaoChiTietSuCoResp;
import nocsystem.indexmanager.models.Response.ChiTietSuCoResp;
import nocsystem.indexmanager.models.Response.ThongTinDieuChuyenResp;
import nocsystem.indexmanager.models.Response.TongQuanBuuCucResp;
import nocsystem.indexmanager.models.Response.danhgianguonluc.TienDoXLTonVaDuBaoKLHang3NgayRes;
import nocsystem.indexmanager.models.Response.*;
import nocsystem.indexmanager.models.Response.danhgianguonluc.TinhTrangNSDuBaoNhuCauRes;
import nocsystem.indexmanager.request.DanhGiaNguonLucRequest;
import nocsystem.indexmanager.services.danhgia_nguonluc.TongQuanNguonLucService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class TongQuanNguonLucController {
    private final TongQuanNguonLucService tongQuanNguonLucService;

    @PostMapping("/tong-quan-buu-cuc")
    public ResponseEntity<SimpleAPIResponse> getTongQuanBuuCuc(@RequestBody DanhGiaNguonLucRequest request){
        SimpleAPIResponse response = new SimpleAPIResponse();
        TongQuanBuuCucResp tongQuanBuuCucResp = tongQuanNguonLucService.getTongQuanBuuCuc(request);
        if(ObjectUtils.isNotEmpty(tongQuanBuuCucResp)) {
            response.setData(tongQuanBuuCucResp);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/du-bao-chuyen-cap")
    public SimpleAPIResponseWithSum getThongTinDuBaoChuyenCap(@RequestBody DanhGiaNguonLucRequest request){
        SimpleAPIResponseWithSum response = tongQuanNguonLucService.getThongTinDuBaoChuyenCap(request);
        return response;
    }

    @PostMapping("/dash-su-co/tinh-trang-nhan-su-du-bao-nhu-cau")
    public SimpleAPIResponseWithSum getTTNhanSuDuBaoNhuCau(@RequestBody DanhGiaNguonLucRequest request){
        SimpleAPIResponseWithSum response = tongQuanNguonLucService.getTTNhanSuDuBaoNhuCau(request);
        return response;
    }

    @PostMapping("/dash-su-co/tien-do-xu-ly-ton-va-du-bao-kl-hang-3-ngay")
    public ResponseEntity<SimpleAPIResponse> getTienDoXuLyTonDongNhomHang(@RequestBody DanhGiaNguonLucRequest request){
        SimpleAPIResponse response = new SimpleAPIResponse();
        TienDoXLTonVaDuBaoKLHang3NgayRes tongquanNguonLucResp = tongQuanNguonLucService.getTienDoXuLyTonDongNhomHang(request.getNgayBaoCao(), request.getChiNhanh(), request.getBuuCuc());
        if(ObjectUtils.isNotEmpty(tongquanNguonLucResp)) {
            response.setData(tongquanNguonLucResp);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/chi-tiet-su-co")
    public ChiTietSuCoResp getChiTietSuCo(@RequestBody DanhGiaNguonLucRequest request){
        return tongQuanNguonLucService.getChiTietSuCo(request);
    }

    @PostMapping("/thong-tin-dieu-chuyen-hotro")
    public ThongTinDieuChuyenResp getThongTinDieuChuyenHoTro(@RequestBody DanhGiaNguonLucRequest request){
        return tongQuanNguonLucService.getThongTinDieuChuyenHoTro(request);
    }

    @PostMapping("/baocao-sosanh-chiso")
    public BaoCaoChiTietSuCoResp baoCaoSoSanhChiSo(@RequestBody DanhGiaNguonLucRequest request){
        return tongQuanNguonLucService.baoCaoSoSanhChiSo(request);
    }
    @PostMapping("/chiso-chuyencap-suco")
    public ChiSoChuyenCapResp chiSoChuyenCapSuCo(@RequestBody DanhGiaNguonLucRequest request){
        return tongQuanNguonLucService.chiSoChuyenCapSuCo(request);
    }
}
