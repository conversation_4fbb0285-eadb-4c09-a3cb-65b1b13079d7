package nocsystem.indexmanager.controllers.SanLuongDuKienDen;

import nocsystem.indexmanager.config.Pagination;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.SanLuongDuKienDen.SanLuongDuKienDenBCPResponse;
import nocsystem.indexmanager.models.SanLuongDuKienDen.SanLuongDuKienDenBCPParam;
import nocsystem.indexmanager.services.SanLuongDuKienDen.SanLuongDuKienDenBCPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class SanLuongDuKienDenBCPController {
    @Autowired
    private SanLuongDuKienDenBCPService sanLuongDuKienDenBCPService;

    @PostMapping("/dash-mobile/san-luong-du-kien-den-bcp")
    public ResponseEntity<SimpleAPIResponseV2> getBaoCaoSLDuKienDenBCP(@RequestBody SanLuongDuKienDenBCPParam requestBody) throws SQLException {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        List<SanLuongDuKienDenBCPResponse> listData = sanLuongDuKienDenBCPService.getBaoCaoSLDuKienBCP(
                                                                                        requestBody.getNgayBaoCao(),
                                                                                        requestBody.getChiNhanh(),
                                                                                        requestBody.getBuuCuc(),
                                                                                        requestBody.getOrderBy(),
                                                                                        requestBody.getSort(),
                                                                                        requestBody.getPageNumber(),
                                                                                        requestBody.getPageSize(),
                                                                                        requestBody.getHangDacThu(),
                                                                                        requestBody.getKhDacThuGui(),
                                                                                        requestBody.getKhDacThuNhan());
        Long totalRecord = sanLuongDuKienDenBCPService.getTotalRecordBaoCaoSLDuKienBCP(
                                                            requestBody.getNgayBaoCao(),
                                                            requestBody.getChiNhanh(),
                                                            requestBody.getBuuCuc(),
                                                            requestBody.getHangDacThu(),
                                                            requestBody.getKhDacThuGui(),
                                                            requestBody.getKhDacThuNhan());
        Pagination pagination = new Pagination(requestBody.getPageNumber(), requestBody.getPageSize(), totalRecord);
        response.setData(listData);
        response.setPagination(pagination);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/dash-mobile/san-luong-du-kien-den-bcp/total")
    public ResponseEntity<SimpleAPIResponseV2> getTotalBaoCaoSLDuKienDenBCP(@RequestBody SanLuongDuKienDenBCPParam requestBody) throws SQLException {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        SanLuongDuKienDenBCPResponse data = sanLuongDuKienDenBCPService.getTotalBaoCaoSLDuKienBCP(
                                                                            requestBody.getNgayBaoCao(),
                                                                            requestBody.getChiNhanh(),
                                                                            requestBody.getBuuCuc(),
                                                                            requestBody.getHangDacThu(),
                                                                            requestBody.getKhDacThuGui(),
                                                                            requestBody.getKhDacThuNhan());
        response.setData(data);
        return ResponseEntity.ok(response);
    }
}
