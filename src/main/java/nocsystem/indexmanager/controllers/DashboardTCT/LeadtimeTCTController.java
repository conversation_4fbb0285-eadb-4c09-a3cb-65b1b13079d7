package nocsystem.indexmanager.controllers.DashboardTCT;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto;
import nocsystem.indexmanager.models.DashboardTCT.leadtime.*;
import nocsystem.indexmanager.services.DashboardTCT.FirstmileTCTService;
import nocsystem.indexmanager.services.DashboardTCT.LeadtimeTCTService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class LeadtimeTCTController {
    @Autowired
    LeadtimeTCTService leadtimeTCTService;

    @GetMapping("/leadtime")
    public ResponseEntity<SimpleAPIResponse> getLeadtime(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc,
            @RequestParam(name = "luyKe", required = false, defaultValue = "0") Integer luyKe){
        SimpleAPIResponse response = new SimpleAPIResponse();
        DashTCTLeadtimeResponse listData = leadtimeTCTService.getDashTCTLeadtime(ngayBaoCao, chiNhanh, buuCuc, luyKe);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/leadtime/chart/tt-all")
    public ResponseEntity<SimpleAPIResponse> getLeadtimeChartTTAll(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc){
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<LeadtimeChartTTAllResponse> listData = leadtimeTCTService.getLeadtimeChartTTAll(ngayBaoCao, chiNhanh, buuCuc);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/leadtime/chart/tt-tach-kien-tk")
    public ResponseEntity<SimpleAPIResponse> getLeadtimeChartTTTachKienTK(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc){
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<LeadtimeChartTTTachKienTKResponse> listData = leadtimeTCTService.getLeadtimeChartTTTachKienTK(ngayBaoCao, chiNhanh, buuCuc);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }

}
