package nocsystem.indexmanager.controllers.DashboardTCT;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashFirstmileTLThuTCDto;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto;
import nocsystem.indexmanager.services.DashboardTCT.FirstmileTCTService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class FirstmileTCTController {
    @Autowired
    FirstmileTCTService firstmileService;

    @GetMapping("/firstmile")
    public ResponseEntity<SimpleAPIResponse> getFirstmile(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc,
            @RequestParam(name = "luyKe", required = false, defaultValue = "0") Integer luyKe){
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<DashTCTFirstmileDto> listData = firstmileService.getFirstmile(ngayBaoCao, chiNhanh, buuCuc, luyKe);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/firstmile/dashboard/tlthutc-ngay")
    public ResponseEntity<SimpleAPIResponse> getDashFirstmileTLThuTCNgay(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc){
        SimpleAPIResponse response = new SimpleAPIResponse();
        List<DashFirstmileTLThuTCDto> listData = firstmileService.getDashFirstmileTlThuTCNgay(ngayBaoCao, chiNhanh, buuCuc);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }
}
