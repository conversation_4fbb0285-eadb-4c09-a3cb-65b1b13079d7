package nocsystem.indexmanager.controllers.DashboardTCT;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.HieuQuaXeResponse;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.KetNoiResponse;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.KhaiThacResponse;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.MiddleMileResponse;
import nocsystem.indexmanager.services.DashboardTCT.MiddlemileTCTService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@RestController
@RequestMapping("/api/v1")
public class MiddlemileTCTController {
    @Autowired
    MiddlemileTCTService middlemileTCTService;

    @GetMapping("/middlemile/khai-thac")
    public ResponseEntity<SimpleAPIResponse> getKhaiThac(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc,
            @RequestParam(name = "luyKe", required = false, defaultValue = "0") Integer luyKe){
        SimpleAPIResponse response = new SimpleAPIResponse();
        KhaiThacResponse listData = middlemileTCTService.getKhaiThac(ngayBaoCao, chiNhanh, buuCuc, luyKe);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/middlemile/ket-noi")
    public ResponseEntity<SimpleAPIResponse> getKetNoi(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc,
            @RequestParam(name = "luyKe", required = false, defaultValue = "0") Integer luyKe){
        SimpleAPIResponse response = new SimpleAPIResponse();
        KetNoiResponse listData = middlemileTCTService.getKetNoi(ngayBaoCao, chiNhanh, buuCuc, luyKe);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/middlemile/hieu-qua-xe")
    public ResponseEntity<SimpleAPIResponse> getHieuQuaXe(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc,
            @RequestParam(name = "luyKe", required = false, defaultValue = "0") Integer luyKe){
        SimpleAPIResponse response = new SimpleAPIResponse();
        HieuQuaXeResponse listData = middlemileTCTService.getHieuQuaXe(ngayBaoCao, chiNhanh, buuCuc, luyKe);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/middlemile/middle-mile")
    public ResponseEntity<SimpleAPIResponse> getMiddleMile(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(name = "chiNhanh", required = false, defaultValue = "") String chiNhanh,
            @RequestParam(name = "buuCuc", required = false, defaultValue = "") String buuCuc,
            @RequestParam(name = "luyKe", required = false, defaultValue = "0") Integer luyKe){
        SimpleAPIResponse response = new SimpleAPIResponse();
        MiddleMileResponse listData = middlemileTCTService.getMiddleMile(ngayBaoCao, chiNhanh, buuCuc, luyKe);
        response.setData(listData);
        return ResponseEntity.ok(response);
    }
}
