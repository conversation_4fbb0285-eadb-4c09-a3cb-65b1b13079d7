package nocsystem.indexmanager.controllers.CanhBaoKhachHangLon.Grab;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.request.BaoCaoGrabPhatRequest;
import nocsystem.indexmanager.request.BaoCaoGrabPhatSumRequest;
import nocsystem.indexmanager.request.BaoCaoGrabExcelRequest;
import nocsystem.indexmanager.request.BaoCaoGrabExcelChiTietRequest;
import nocsystem.indexmanager.request.BaoCaoGrabExcelPhatTCRequest;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatService;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.KpiPhatTikTokService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/grab")
public class BaoCaoGrabPhatController {

    @Autowired
    private BaoCaoGrabPhatService bcGrabPhatSv;

    @Autowired
    private KpiPhatTikTokService kpiPhatTikTokService;

    @PostMapping("/bao-cao-van-hanh-grab-phat")
    public SimpleAPIResponse bcKPI(@Valid @RequestBody BaoCaoGrabPhatRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<BaoCaoGrabPhatResDTO> grab;

        Integer page = request.getPage();
        if (page > 0) page--;

        grab = bcGrabPhatSv.findAllData(
                request.getLuyKe(),
                request.getLoaiBaoCao(),
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getLoaiDon(),
                request.getNgayBaoCao(),
                page,
                request.getPageSize()
        );

        simpleAPIResponse.setData(grab);

        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-grab-phat-sum")
    public SimpleAPIResponse bcKPISum(@Valid @RequestBody BaoCaoGrabPhatSumRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<BaoCaoGrabPhatResDTO> grab;
        grab = bcGrabPhatSv.baoCaoGrabPhatSum(
                request.getLuyKe(),
                request.getLoaiBaoCao(),
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getLoaiDon(),
                request.getNgayBaoCao()
        );
        simpleAPIResponse.setData(grab);
        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-grab-excel")
    public void exportData(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoGrabExcelRequest request
    ) throws IOException, IllegalAccessException, NoSuchFieldException {
        bcGrabPhatSv.exportExcelGrabPhat(
                response,
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getBuuTa(),
                request.getLoaiDon(),
                request.getLoaiBaoCao(),
                "BCVH_Grab_Phat"
        );
    }

    @PostMapping("/bao-cao-van-hanh-grab-excel-chi-tiet")
    public void exportDataBill(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoGrabExcelChiTietRequest request
    ) throws IOException, IllegalAccessException {
        bcGrabPhatSv.exportExcelBillGrabPhat(
                response,
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getLoaiDon(),
                "BCVH_Grab_Phat_ChiTiet"
        );
    }

    @PostMapping("/bao-cao-van-hanh-grab-excel-phat-tc")
    public void exportDataBillPTC(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoGrabExcelPhatTCRequest request
    ) throws IOException, IllegalAccessException {
        bcGrabPhatSv.exportExcelBillGrabPhat(
                response,
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getLoaiDon(),
                "BCVH_Grab_Phat_Thanh_Cong"
        );
    }

    @GetMapping("/list-buu-ta-theo-buu-cuc")
    public SimpleAPIResponse getDashboardTop10ThapNhat(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh", required = false) String maChiNhanh,
            @RequestParam(value = "buu_cuc", required = false) String maBuuCuc,
            @RequestParam(value = "luy_ke", defaultValue = "0") Integer luyKe) {
        List<String> rs = bcGrabPhatSv.allBuuTa( maChiNhanh, maBuuCuc, ngayBaoCao, luyKe);
        SimpleAPIResponse response = new SimpleAPIResponse();
        response.setData(rs);
        return response;
    }
}
