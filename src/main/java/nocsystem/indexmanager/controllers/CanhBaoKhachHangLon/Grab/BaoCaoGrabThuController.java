package nocsystem.indexmanager.controllers.CanhBaoKhachHangLon.Grab;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.request.BaoCaoGrabThuRequest;
import nocsystem.indexmanager.request.BaoCaoGrabThuSumRequest;
import nocsystem.indexmanager.request.BaoCaoGrabThuExcelRequest;
import nocsystem.indexmanager.request.BaoCaoGrabThuExcelChiTietRequest;
import nocsystem.indexmanager.request.BaoCaoGrabThuExcelPhatSinhRequest;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1/grab")
public class BaoCaoGrabThuController {

    @Autowired
    private BaoCaoGrabThuService bcGrabThuSv;


    @PostMapping("/bao-cao-van-hanh-grab-thu")
    public SimpleAPIResponse bcKPI(@Valid @RequestBody BaoCaoGrabThuRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<BaoCaoGrabPhatResDTO> grab;

        Integer page = request.getPage();
        if (page > 0) page--;

        grab = bcGrabThuSv.findAllData(
                request.getLuyKe(),
                request.getCap(),
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getLoaiDon(),
                request.getNgayBaoCao(),
                page,
                request.getPageSize()
        );

        simpleAPIResponse.setData(grab);

        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-grab-thu-sum")
    public SimpleAPIResponse bcKPISum(@Valid @RequestBody BaoCaoGrabThuSumRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<BaoCaoGrabPhatResDTO> grab = new ArrayList<>();
        grab = bcGrabThuSv.baoCaoGrabPhatSum(
                request.getLuyKe(),
                request.getCap(),
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getLoaiDon(),
                request.getNgayBaoCao()
        );
        simpleAPIResponse.setData(grab);
        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-grab-thu-excel")
    public void exportData(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoGrabThuExcelRequest request
    ) throws IOException, IllegalAccessException, NoSuchFieldException {
        bcGrabThuSv.exportExcelGrabPhat(
                response,
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getBuuTa(),
                request.getLoaiDon(),
                request.getCap(),
                "BCVH_Grab_Thu"
        );
    }

    @PostMapping("/bao-cao-van-hanh-grab-thu-excel-chi-tiet")
    public void exportDataBill(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoGrabThuExcelChiTietRequest request
    ) throws IOException, IllegalAccessException {
        bcGrabThuSv.exportExcelBillGrabThu(
                response,
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getBuuTa(),
                request.getLoaiDon(),
                "BCVH_Grab_Thu_ChiTiet"
        );
    }

    @PostMapping("/bao-cao-van-hanh-grab-thu-excel-phat-sinh")
    public void exportDataBillPhatSinh(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoGrabThuExcelPhatSinhRequest request
    ) throws IOException, IllegalAccessException {
        bcGrabThuSv.exportExcelBillGrabThu(
                response,
                request.getVung(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getBuuTa(),
                request.getLoaiDon(),
                "BCVH_Grab_Thu_Phat_Sinh"
        );
    }
}
