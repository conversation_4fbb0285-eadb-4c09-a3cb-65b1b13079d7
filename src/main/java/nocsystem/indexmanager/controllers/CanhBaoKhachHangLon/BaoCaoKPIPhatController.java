package nocsystem.indexmanager.controllers.CanhBaoKhachHangLon;

//import io.swagger.models.auth.In;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.*;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.BaoCaoKPIPhatLKRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.BaoCaoKPIPhatRepository;
import nocsystem.indexmanager.request.BaoCaoKPIPhatRequest;
import nocsystem.indexmanager.request.BaoCaoKPIPhatSumRequest;
import nocsystem.indexmanager.request.BaoCaoKPIKetQuaPhatRequest;
import nocsystem.indexmanager.request.BaoCaoKPIPhatTonRequest;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.BaoCaoKPIPhatService;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.KpiPhatTikTokService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class BaoCaoKPIPhatController {

    @Autowired
    private BaoCaoKPIPhatService bcKPIPhatSv;

    @Autowired
    private BaoCaoKPIPhatRepository bcKPIPhatRepo;

    @Autowired
    private BaoCaoKPIPhatLKRepository bcKPIPhatLKRepo;

    @Autowired
    private KpiPhatTikTokService kpiPhatTikTokService;

    @PostMapping("/bao-cao-van-hanh-KPI-phat")
    public SimpleAPIResponse bcKPI(@Valid @RequestBody BaoCaoKPIPhatRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<BaoCaoKPIPhatResDTO2> tPN;

        Integer page = request.getPage();
        if (page > 0) page--;

        tPN = bcKPIPhatSv.findAllData(
                request.getLuyKe(),
                request.getLoaiBaoCao(),
                request.getVung(),
                request.getVungCon(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                page,
                request.getPageSize(),
                request.getOrder(),
                request.getOrderBy()
        );

        simpleAPIResponse.setData(tPN);

        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-KPI-phat-sum")
    public SimpleAPIResponse bcKPISum(@Valid @RequestBody BaoCaoKPIPhatSumRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<BaoCaoKPIPhatResDTO2> tPN = new ArrayList<>();
        tPN = bcKPIPhatSv.baoCaoKPIPhatSum(
                request.getLuyKe(),
                request.getLoaiBaoCao(),
                request.getVung(),
                request.getVungCon(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao()
        );
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/bao-cao/tik-tok/dashboard/KPI-phat-top10-san-luong")
    public SimpleAPIResponse bcKPIThuTop10(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String chiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String buuCuc,
            @RequestParam(value = "type", defaultValue = "0") Integer luyKe,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<BaoCaoKPIThuTop10SLResponse> tPN = bcKPIPhatSv.getTop10SanLuong(ngayBaoCao, chiNhanh, buuCuc, luyKe, maDoiTac);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-KPI-ket-qua-phat")
    public void exportData(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoKPIKetQuaPhatRequest request
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCVH_KPI_Phat.xlsx";

        response.setHeader(headerKey, headerValue);
        bcKPIPhatSv.exportExcelKPIPhat(
                response,
                request.getVung(),
                request.getVungCon(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getBuuTa()
        );
    }

    @PostMapping("/bao-cao-van-hanh-KPI-phat-ton")
    public void exportDataBill(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoKPIPhatTonRequest request
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCVH_KPI_Phat_chi_tiet.xlsx";

        response.setHeader(headerKey, headerValue);
        bcKPIPhatSv.exportExcelKPIPhatTon(
                response,
                request.getVung(),
                request.getVungCon(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyke(),
                request.getBuuTa()
        );
    }

    @GetMapping("/bao-cao/tik-tok/dashboard/kpi-phat")
    public ResponseEntity<SimpleAPIResponse> getDashboardTikTokInfo(@RequestParam("type") int type,
                                                                    @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                    @RequestParam(value = "ma_cn", required = false) String maChiNhanh,
                                                                    @RequestParam(value = "ma_bc", required = false) String maBuuCuc,
                                                                    @RequestParam(value = "ma_doitac", required = false) String maDoiTac){
        DashboardTikTokKpiPhatResponse result = kpiPhatTikTokService.getInfoDashBoardKpiPhatTikTok(type, ngayBaoCao, maChiNhanh, maBuuCuc, maDoiTac);
        SimpleAPIResponse response = new SimpleAPIResponse(result);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/bao-cao/tik-tok/dashboard/sl-ton-phat")
    public ResponseEntity<SimpleAPIResponse> getInfoSanLuongTon(@RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                @RequestParam(value = "ma_cn", required = false) String maChiNhanh,
                                                                @RequestParam(value = "ma_bc", required = false) String maBuuCuc,
                                                                @RequestParam(value = "ma_doitac", required = false) String maDoiTac){

        BaoCaoTonResponse result = kpiPhatTikTokService.getBaoCaoTon(maChiNhanh, maBuuCuc, maDoiTac, ngayBaoCao);
        SimpleAPIResponse response = new SimpleAPIResponse(result);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/bao-cao/dashboard/bieu-do-kpi-phat")
    public ResponseEntity<SimpleAPIResponse> getDashboardTop10ThapNhat(@RequestParam("type") int type,
                                                                       @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                       @RequestParam(value = "ma_cn", required = false) String maChiNhanh,
                                                                       @RequestParam(value = "ma_bc", required = false) String maBuuCuc,
                                                                       @RequestParam(value = "ma_doitac", required = false) String maDoiTac) {
        List<DashboardTop10TikTokResponse> rs = kpiPhatTikTokService.bieuDoTop10PhatThapNhat(type, ngayBaoCao, maChiNhanh, maBuuCuc, maDoiTac);
        SimpleAPIResponse response = new SimpleAPIResponse(rs);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/list-buu-ta-theo-buu-cuc")
    public SimpleAPIResponse getDashboardTop10ThapNhat(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh", required = false) String maChiNhanh,
            @RequestParam(value = "buu_cuc", required = false) String maBuuCuc,
            @RequestParam(value = "luy_ke", required = false) Integer luyKe) {
        List<String> rs = bcKPIPhatSv.allBuuTa( maChiNhanh, maBuuCuc, ngayBaoCao, luyKe);
        SimpleAPIResponse response = new SimpleAPIResponse();
        response.setData(rs);
        return response;
    }
}
