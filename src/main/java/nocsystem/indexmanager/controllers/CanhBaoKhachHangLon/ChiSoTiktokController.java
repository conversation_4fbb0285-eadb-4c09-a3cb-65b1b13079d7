package nocsystem.indexmanager.controllers.CanhBaoKhachHangLon;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiSoTiktokDTO;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.ChiSoTiktokService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class ChiSoTiktokController {

    @Autowired
    ChiSoTiktokService service;


    @GetMapping("/get-chi-so-tiktok")
    public SimpleAPIResponse getChiSoTiktokNgay(@RequestParam(value = "luyke") int luyke, @RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao, @RequestParam(value = "ma_doitac") String maDoiTac) {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<ChiSoTiktokDTO> mList = service.getChisoTiktok(ngayBaoCao, luyke, maDoiTac);
        simpleAPIResponse.setData(mList);
        return simpleAPIResponse;
    }

    @GetMapping("/get-list-doitac")
    public SimpleAPIResponse getListDoiTac(@RequestParam(value = "ngay_bao_cao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<String> mList = service.getListDoiTac(ngayBaoCao);
        simpleAPIResponse.setData(mList);
        return simpleAPIResponse;
    }


}

