package nocsystem.indexmanager.controllers.CanhBaoKhachHangLon;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.*;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.BaoCaoKPIThuLKRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.BaoCaoKPIThuRepository;
import nocsystem.indexmanager.request.BaoCaoKPIThuRequest;
import nocsystem.indexmanager.request.BaoCaoKPIThuSumRequest;
import nocsystem.indexmanager.request.BaoCaoKPIThuTonRequest;
import nocsystem.indexmanager.request.BaoCaoKPIKetQuaThuRequest;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.BaoCaoKPIThuService;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.KpiThuTikTokService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class BaoCaoKPIThuController {

    @Autowired
    private BaoCaoKPIThuService bcKPIThuSv;

    @Autowired
    private BaoCaoKPIThuRepository bcKPIThuRepo;

    @Autowired
    private BaoCaoKPIThuLKRepository bcKPIThuLKRepo;

    @Autowired
    private KpiThuTikTokService kpiThuTikTokService;

    @PostMapping("/bao-cao-van-hanh-KPI-thu")
    public SimpleAPIResponse bcKPI(@Valid @RequestBody BaoCaoKPIThuRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        ListContentPageDto<BaoCaoKPIThuResDTO2> tPN;
//        ListContentPageDto<BaoCaoKPIThuResDTO> tPN;

        Integer page = request.getPage();
        if (page > 0) page--;

        tPN = bcKPIThuSv.findAllData(
                request.getLuyKe(),
                request.getCap(),
                request.getVung(),
                request.getVungCon(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getLoaiDon(),
                request.getNgayBaoCao(),
                page,
                request.getPageSize(),
                request.getOrder(),
                request.getOrderBy()
        );
        simpleAPIResponse.setData(tPN);

        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-KPI-thu-sum")
    public SimpleAPIResponse bcKPISum(@Valid @RequestBody BaoCaoKPIThuSumRequest request) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<BaoCaoKPIThuResDTO2> tPN = bcKPIThuSv.summaryCalculation(
                request.getLuyKe(),
                request.getCap(),
                request.getVung(),
                request.getVungCon(),
                request.getChiNhanh(),
                request.getBuuCuc(),
                request.getBuuTa(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getLoaiDon(),
                request.getNgayBaoCao()
        );
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @PostMapping("/bao-cao-van-hanh-KPI-thu-ton")
    public void exportData(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoKPIThuTonRequest request
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCVH_KPI_Thu.xlsx";

        response.setHeader(headerKey, headerValue);
        bcKPIThuSv.exportExcelKPIThuTon(
                response,
                request.getChiNhanh(),
                request.getVung(),
                request.getVungCon(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyKe(),
                request.getLoaiDon()
        );
    }

    @PostMapping("/bao-cao-van-hanh-KPI-ket-qua-thu")
    public void exportDataBill(
            HttpServletResponse response,
            @Valid @RequestBody BaoCaoKPIKetQuaThuRequest request
    ) throws IOException {
        response.setContentType("application/vnd.ms.excel");
//        response.setContentType("application/octet-stream");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=BCVH_KPI_Thu_chi_tiet.xlsx";

        response.setHeader(headerKey, headerValue);
        bcKPIThuSv.exportExcelKPIThuBill(
                response,
                request.getChiNhanh(),
                request.getVung(),
                request.getVungCon(),
                request.getBuuCuc(),
                request.getDoiTac(),
                request.getDichVu(),
                request.getNgayBaoCao(),
                request.getLuyke(),
                request.getLoaiDon()
        );
    }

    @GetMapping("/bao-cao/tik-tok/dashboard/KPI-thu-top10-san-luong")
    public SimpleAPIResponse bcKPIThuTop10(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "ma_cn", defaultValue = "") String chiNhanh,
            @RequestParam(value = "ma_bc", defaultValue = "") String buuCuc,
            @RequestParam(value = "type", defaultValue = "0") Integer luyKe,
            @RequestParam(value = "ma_doitac", defaultValue = "") String maDoiTac
    ) throws IOException {
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        List<BaoCaoKPIThuTop10SLResponse> tPN = bcKPIThuSv.getTop10SanLuong(ngayBaoCao, chiNhanh, buuCuc, luyKe, maDoiTac);
        simpleAPIResponse.setData(tPN);
        return simpleAPIResponse;
    }

    @GetMapping("/bao-cao/dashboard/bieu-do-kpi-thu")
    public ResponseEntity<SimpleAPIResponse> getDashboardTop10ThapNhat(@RequestParam("type") int type,
                                                                       @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
                                                                       @RequestParam(value = "ma_cn", required = false) String maChiNhanh,
                                                                       @RequestParam(value = "ma_bc", required = false) String maBuuCuc,
                                                                       @RequestParam(value = "ma_doitac", required = false) String maDoiTac) {
        List<DashboardTop10TikTokResponse> rs = kpiThuTikTokService.top10ThuThapNhat(type, ngayBaoCao, maChiNhanh, maBuuCuc, maDoiTac);
        SimpleAPIResponse response = new SimpleAPIResponse(rs);
        return ResponseEntity.ok(response);
    }
    @GetMapping("/bao-cao/tik-tok/dashboard/kpi-thu")
    public ResponseEntity<SimpleAPIResponse> getDashboardTikTokInfo(@RequestParam("type") int type,
                                                                    @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd")LocalDate ngayBaoCao,
                                                                    @RequestParam(value = "ma_cn", required = false) String maChiNhanh,
                                                                    @RequestParam(value = "ma_bc", required = false) String maBuuCuc,
                                                                    @RequestParam(value = "ma_doitac", required = false) String maDoiTac){
        DashboardTikTokKpiThuResponse result = kpiThuTikTokService.getInfoDashboardKpiThuTikTok(type, ngayBaoCao, maChiNhanh, maBuuCuc, maDoiTac);
        SimpleAPIResponse response = new SimpleAPIResponse(result);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/bao-cao/tik-tok/dashboard/sl-ton-thu")
    public ResponseEntity<SimpleAPIResponse> getInfoSanLuongTon( @RequestParam(value = "ma_cn", required = false) String maChiNhanh,
                                                                 @RequestParam(value = "ma_bc", required = false) String maBuuCuc,
                                                                 @RequestParam(value = "ma_doitac", required = false) String maDoiTac){

        BaoCaoTonResponse result = kpiThuTikTokService.getBaoCaoTon(maChiNhanh, maBuuCuc, maDoiTac);
        SimpleAPIResponse response = new SimpleAPIResponse(result);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/list-buu-ta-theo-buu-cuc-kpi-thu")
    public SimpleAPIResponse getDashboardTop10ThapNhat(
            @RequestParam("ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "chi_nhanh", required = false) String maChiNhanh,
            @RequestParam(value = "buu_cuc", required = false) String maBuuCuc,
            @RequestParam(value = "luy_ke", required = false) Integer luyKe) {
        List<String> rs = bcKPIThuSv.allBuuTa( maChiNhanh, maBuuCuc, ngayBaoCao, luyKe);
        SimpleAPIResponse response = new SimpleAPIResponse();
        response.setData(rs);
        return response;
    }

}
