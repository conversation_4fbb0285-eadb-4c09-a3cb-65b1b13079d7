package nocsystem.indexmanager.controllers.DieuHanhTon;

import nocsystem.indexmanager.config.Pagination;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse;
import nocsystem.indexmanager.services.DieuHanhTon.DieuHanhTonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;

@RestController
@RequestMapping("/api/v1")
public class DieuHanhTonController {
    private static final String jsonContentType = "application/json;charset=UTF-8";

    @Autowired
    private DieuHanhTonService dieuHanhTonService;

    @GetMapping("/dashboard/dieu-hanh-ton/phai-phat/tong-quan")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiPhatDHTTongQuan(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            @RequestParam(value = "pageNumber", defaultValue = "0", required = false) Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        Pageable pageable = PageRequest.of(pageNumber, pageSize);
        Page<DieuHanhTonPhatResponse> data = dieuHanhTonService.getPhaiPhatDHTTongQuan(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, pageable);
        Pagination pagination = new Pagination(pageNumber, pageSize, data.getTotalElements());
        responseV2.setPagination(pagination);
        responseV2.setData(data.getContent());
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-phat/chi-tiet")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiPhatDHTChiTiet(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            @RequestParam(value = "sortKey", defaultValue = "", required = false) String sortKey,
            @RequestParam(value = "sortType", defaultValue = "", required = false) String sortType,
            @RequestParam(value = "pageNumber", defaultValue = "0", required = false) Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "30", required = false) Integer pageSize) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        Page<DieuHanhTonPhatResponse> data = dieuHanhTonService.getPhaiPhatDHTChiTiet(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, sortKey, sortType, pageNumber, pageSize);
        Pagination pagination = new Pagination(pageNumber, pageSize, data.getTotalElements());
        responseV2.setPagination(pagination);
        responseV2.setData(data.getContent());
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-phat/tong")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiPhatDHTTong(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        DieuHanhTonPhatResponse data = dieuHanhTonService.getPhaiPhatDHTTong(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac);
        responseV2.setData(data);
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-phat/excel-tong-hop")
    public ResponseEntity<SimpleAPIResponseV2> exportPhaiPhatDHTExcelTongHop(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            HttpServletResponse response) {
        dieuHanhTonService.exportPhaiPhatDHTExcelTongHop(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, jsonContentType).body(null);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-phat/excel-chi-tiet")
    public ResponseEntity<SimpleAPIResponseV2> exportPhaiPhatDHTExcelChiTiet(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            HttpServletResponse response) {
        dieuHanhTonService.exportPhaiPhatDHTExcelChiTiet(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, jsonContentType).body(null);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-hoan/tong-quan")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiHoanDHTTongQuan(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            @RequestParam(value = "pageNumber", defaultValue = "0", required = false) Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        Pageable pageable = PageRequest.of(pageNumber, pageSize);
        Page<DieuHanhTonHoanResponse> data = dieuHanhTonService.getPhaiHoanDHTTongQuan(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, pageable);
        Pagination pagination = new Pagination(data.getPageable().getPageNumber(), data.getSize(), data.getTotalElements());
        responseV2.setPagination(pagination);
        responseV2.setData(data.getContent());
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-hoan/chi-tiet")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiHoanDHTChiTiet(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            @RequestParam(value = "sortKey", defaultValue = "", required = false) String sortKey,
            @RequestParam(value = "sortType", defaultValue = "", required = false) String sortType,
            @RequestParam(value = "pageNumber", defaultValue = "0", required = false) Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "30", required = false) Integer pageSize) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        Page<DieuHanhTonHoanResponse> data = dieuHanhTonService.getPhaiHoanDHTChiTiet(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, sortKey, sortType, pageNumber, pageSize);
        Pagination pagination = new Pagination(data.getPageable().getPageNumber(), data.getSize(), data.getTotalElements());
        responseV2.setPagination(pagination);
        responseV2.setData(data.getContent());
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-hoan/tong")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiHoanDHTTong(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        DieuHanhTonHoanResponse data = dieuHanhTonService.getPhaiHoanDHTTong(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac);
        responseV2.setData(data);
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-hoan/excel-tong-hop")
    public ResponseEntity<SimpleAPIResponseV2> exportPhaiHoanDHTExcelTongHop(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            HttpServletResponse response) {
        dieuHanhTonService.exportPhaiHoanDHTExcelTongHop(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, jsonContentType).body(null);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-hoan/excel-chi-tiet")
    public ResponseEntity<SimpleAPIResponseV2> exportPhaiHoanDHTExcelChiTiet(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            HttpServletResponse response) {
        dieuHanhTonService.exportPhaiHoanDHTExcelChiTiet(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, jsonContentType).body(null);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-thu/tong-quan")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiThuDHTTongQuan(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            @RequestParam(value = "pageNumber", defaultValue = "0", required = false) Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        Pageable pageable = PageRequest.of(pageNumber, pageSize);
        Page<DieuHanhTonThuResponse> data = dieuHanhTonService.getPhaiThuDHTTongQuan(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, pageable);
        Pagination pagination = new Pagination(data.getPageable().getPageNumber(), data.getSize(), data.getTotalElements());
        responseV2.setPagination(pagination);
        responseV2.setData(data.getContent());
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-thu/chi-tiet")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiThuDHTChiTiet(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            @RequestParam(value = "sortKey", defaultValue = "", required = false) String sortKey,
            @RequestParam(value = "sortType", defaultValue = "", required = false) String sortType,
            @RequestParam(value = "pageNumber", defaultValue = "0", required = false) Integer pageNumber,
            @RequestParam(value = "pageSize", defaultValue = "30", required = false) Integer pageSize) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        Page<DieuHanhTonThuResponse> data = dieuHanhTonService.getPhaiThuDHTChiTiet(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, sortKey, sortType, pageNumber, pageSize);
        Pagination pagination = new Pagination(data.getPageable().getPageNumber(), data.getSize(), data.getTotalElements());
        responseV2.setPagination(pagination);
        responseV2.setData(data.getContent());
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-thu/tong")
    public ResponseEntity<SimpleAPIResponseV2> getPhaiThuDHTTong(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac) {
        SimpleAPIResponseV2 responseV2 = new SimpleAPIResponseV2();
        DieuHanhTonThuResponse data = dieuHanhTonService.getPhaiThuDHTTong(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac);
        responseV2.setData(data);
        return ResponseEntity.ok(responseV2);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-thu/excel-tong-hop")
    public ResponseEntity<SimpleAPIResponseV2> exportPhaiThuDHTExcelTongHop(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            HttpServletResponse response) {
        dieuHanhTonService.exportPhaiThuDHTExcelTongHop(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, jsonContentType).body(null);
    }

    @GetMapping("/dashboard/dieu-hanh-ton/phai-thu/excel-chi-tiet")
    public ResponseEntity<SimpleAPIResponseV2> exportPhaiThuDHTExcelChiTiet(
            @RequestParam("ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "luyKe", defaultValue = "0", required = false) Integer luyKe,
            @RequestParam(value = "chiNhanh", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", required = false) String buuCuc,
            @RequestParam(value = "maDoiTac", required = false) String maDoiTac,
            HttpServletResponse response) {
        dieuHanhTonService.exportPhaiThuDHTExcelChiTiet(ngayBaoCao, luyKe, chiNhanh, buuCuc, maDoiTac, response);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, jsonContentType).body(null);
    }
}
