package nocsystem.indexmanager.controllers.NocPro;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.DieuHanhTonTongResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.NocPro.ChiTietHieuQuaKDResponse;
import nocsystem.indexmanager.models.Response.NocPro.TongQuanHieuQuaKDResponse;
import nocsystem.indexmanager.services.NocPro.DashMobileNocProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1/dash-mobile")
public class DashMobileNocProController {
    @Autowired
    DashMobileNocProService dashMobileNocProService;

    @GetMapping("/dashboard/tong-quan")
    public ResponseEntity<SimpleAPIResponse> getDashboard(
            @RequestParam(value = "ngayBaoCao", defaultValue = "") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "mien", defaultValue = "", required = false) String mien,
            @RequestParam(value = "chiNhanh", defaultValue = "", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", defaultValue = "", required = false) String buuCuc,
            @RequestParam(value = "vungCon", defaultValue = "", required = false) String vungCon,
            @RequestParam(value = "nhomKhachHang", defaultValue = "", required = false) String nhomKhachHang,
            @RequestParam(value = "nhomDichVu", defaultValue = "", required = false) String nhomDichVu,
            @RequestParam(value = "mucTrongLuong", required = false) List<String> mucTrongLuong,
            @RequestParam(value = "dichVu", defaultValue = "", required = false) String dichVu) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        TongQuanHieuQuaKDResponse tongQuanHieuQuaKDResponse = dashMobileNocProService.getDashboard(ngayBaoCao, mien, chiNhanh, buuCuc, vungCon, nhomKhachHang, nhomDichVu, mucTrongLuong, dichVu);
        response.setData(tongQuanHieuQuaKDResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/chi-tiet")
    public ResponseEntity<SimpleAPIResponseV2> getDetailDashboard(
            @RequestParam(value = "ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "mien", defaultValue = "", required = false) String mien,
            @RequestParam(value = "chiNhanh", defaultValue = "", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", defaultValue = "", required = false) String buuCuc,
            @RequestParam(value = "vungCon", defaultValue = "", required = false) String vungCon,
            @RequestParam(value = "nhomKhachHang", defaultValue = "", required = false) String nhomKhachHang,
            @RequestParam(value = "nhomDichVu", defaultValue = "", required = false) String nhomDichVu,
            @RequestParam(value = "mucTrongLuong", required = false) List<String> mucTrongLuong,
            @RequestParam(value = "dichVu", defaultValue = "", required = false) String dichVu,
            @RequestParam(value = "order", defaultValue = "", required = false) String order,
            @RequestParam(value = "orderBy", defaultValue = "", required = false) String orderBy,
            @RequestParam(value = "page", defaultValue = "0", required = false) Integer page,
            @RequestParam(value = "pageSize", defaultValue = "30", required = false) Integer pageSize) {
        SimpleAPIResponseV2 response = new SimpleAPIResponseV2();
        ListContentPageDto<ChiTietHieuQuaKDResponse> listResponse = dashMobileNocProService.getDetailDashboard(ngayBaoCao, mien, chiNhanh, buuCuc, vungCon, nhomKhachHang, nhomDichVu, mucTrongLuong, dichVu, order, orderBy, page, pageSize);
        response.setData(listResponse);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/chi-tiet-tong")
    public ResponseEntity<SimpleAPIResponse> getTotalDetailDashboard(
            @RequestParam(value = "ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao,
            @RequestParam(value = "mien", defaultValue = "", required = false) String mien,
            @RequestParam(value = "chiNhanh", defaultValue = "", required = false) String chiNhanh,
            @RequestParam(value = "buuCuc", defaultValue = "", required = false) String buuCuc,
            @RequestParam(value = "vungCon", defaultValue = "", required = false) String vungCon,
            @RequestParam(value = "nhomKhachHang", defaultValue = "", required = false) String nhomKhachHang,
            @RequestParam(value = "nhomDichVu", defaultValue = "", required = false) String nhomDichVu,
            @RequestParam(value = "mucTrongLuong", required = false) List<String> mucTrongLuong,
            @RequestParam(value = "dichVu", defaultValue = "", required = false) String dichVu) {
        SimpleAPIResponse response = new SimpleAPIResponse();
        ChiTietHieuQuaKDResponse total = dashMobileNocProService.getTotalDetailDashboard(ngayBaoCao, mien, chiNhanh, buuCuc, vungCon, nhomKhachHang, nhomDichVu, mucTrongLuong, dichVu);
        response.setData(total);
        return ResponseEntity.ok(response);
    }
}
