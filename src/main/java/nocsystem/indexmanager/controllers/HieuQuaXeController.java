package nocsystem.indexmanager.controllers;

import lombok.RequiredArgsConstructor;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.models.Response.hieuquaxe.CanhBaoHQXTheoChieuRes;
import nocsystem.indexmanager.models.Response.hieuquaxe.DuBaoHQTXeRes;
import nocsystem.indexmanager.models.Response.hieuquaxe.TongQuanHQXRes;
import nocsystem.indexmanager.models.Response.hieuquaxe.TopCoHoiBanHangRes;
import nocsystem.indexmanager.request.hieuquaxe.DanhSachXeChuyRequest;
import nocsystem.indexmanager.request.hieuquaxe.DuBaoHQTXeRequest;
import nocsystem.indexmanager.request.hieuquaxe.TongQuanHQXRequest;
import nocsystem.indexmanager.services.danhgia_nguonluc.HieuQuaXeService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
public class HieuQuaXeController {
    private final HieuQuaXeService hieuQuaXeService;

    @PostMapping("/tong-quan-hqx")
    public ResponseEntity<SimpleAPIResponse> getTongQuanXe(@RequestBody TongQuanHQXRequest request){
        TongQuanHQXRes tongQuanHQXRes = hieuQuaXeService.tongQuanHQX(request);

        SimpleAPIResponse response = new SimpleAPIResponse();
        if(ObjectUtils.isNotEmpty(tongQuanHQXRes)) {
            response.setData(tongQuanHQXRes);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/canh-bao-hqx-chieu")
    public ResponseEntity<SimpleAPIResponse> getCanhBaoHQX(@RequestBody TongQuanHQXRequest request){
        CanhBaoHQXTheoChieuRes canhBaoHQXRes = hieuQuaXeService.canhBaoHQXChieu(request);

        SimpleAPIResponse response = new SimpleAPIResponse();
        if(ObjectUtils.isNotEmpty(canhBaoHQXRes)) {
            response.setData(canhBaoHQXRes);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/danh-sach-xe-chu-y")
    public SimpleAPIResponseWithSum getDanhSachXeChuy(@RequestBody DanhSachXeChuyRequest request){
        return hieuQuaXeService.danhSachXeChuy(request);
    }

    @PostMapping("/du-bao-hieu-qua-tuyen")
    public ResponseEntity<SimpleAPIResponse> getDubaoHieuQuaTuyen(@RequestBody DuBaoHQTXeRequest request){
        DuBaoHQTXeRes res = hieuQuaXeService.duBaoHieuQuaTuyenXe(request);

        SimpleAPIResponse response = new SimpleAPIResponse();
        if(ObjectUtils.isNotEmpty(res)) {
            response.setData(res);
        }
        return ResponseEntity.ok(response);
    }

    @PostMapping("/top-co-hoi-ban-hang")
    public SimpleAPIResponseWithSum getTopCoHoiBanHang(@RequestBody DuBaoHQTXeRequest request){
        return hieuQuaXeService.topCoHoiBanHang(request);
    }
}
