package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPBCDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPCNDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPCNV2ResDto;
import nocsystem.indexmanager.services.ChiSoKinhDoanh.ChiSoKinDoanhService;
import nocsystem.indexmanager.services.TienDoDoanhThu.TienDoDoanhThuChiNhanhService;
import nocsystem.indexmanager.services.TienDoDoanhThuCP.TienDoDoanhThuCPBuuCucService;
import nocsystem.indexmanager.services.TienDoDoanhThuCP.TienDoDoanhThuCPChiNhanhService;
import nocsystem.indexmanager.services.TienDoDoanhThuCP.TienDoDoanhThuCPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class TienDoDoanhThuCPController {
    @Autowired
    private TienDoDoanhThuCPBuuCucService doanhThuCPService;

    @Autowired
    private TienDoDoanhThuCPChiNhanhService doanhThuCNService;

    @Autowired
    private ChiSoKinDoanhService chiSoKinDoanhService;

    @Autowired
    private TienDoDoanhThuChiNhanhService tienDoDoanhThuCNService;

    @Autowired
    private TienDoDoanhThuCPService tienDoDoanhThuCPService;

    @GetMapping("/doanh-thu-cp")
    public SimpleAPIResponse tienDoDoanhThuCP(
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "loai_dichvu", defaultValue = "0") Integer loaiiDchVu,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime
    ) {
        if (page > 0) page--;

        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            CustomizeDataPage<TienDoDoanhThuCPCNV2ResDto> doanhThuCPCN =
                    doanhThuCNService.findAllDataCPCN(maChiNhanh, loaiiDchVu, toTime, pageSize, page);
            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            simpleAPIResponse.setData(doanhThuCPCN);
            return simpleAPIResponse;
        }

        CustomizeDataPage<TienDoDoanhThuCPBCV1ResDto> doanhThuCPBC =
                doanhThuCPService.findAllData(maBuuCuc, maChiNhanh, loaiiDchVu, toTime, pageSize, page);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(doanhThuCPBC);
        return simpleAPIResponse;
    }


    @GetMapping("/bieu-do-kho")
    public SimpleAPIResponse tienDoDoanhThuCP(
            @RequestParam(value = "ngay_baocao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao
    ) {
        List<TienDoDoanhThuCNResDto> doanhThuCPCN = doanhThuCNService.findAllDataCPCNLuyKe(ngayBaoCao);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(doanhThuCPCN);
        return simpleAPIResponse;

    }

    @GetMapping("/tong-tien-do-doanh-thu-chuyen-phat")
    public SimpleAPIResponse tongTienDoDoanhThuCP(
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "loai_dichvu", defaultValue = "0") Integer loaiDichVu,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime
    ) {
        return tienDoDoanhThuCPService.tongTienDoDoanhThuCP(maChiNhanh, maBuuCuc, loaiDichVu, toTime);
    }
}
