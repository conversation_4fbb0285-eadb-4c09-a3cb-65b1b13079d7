package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.TopChiNhanh.TopChiNhanhResp;
import nocsystem.indexmanager.request.TopChiNhanhRequest;
import nocsystem.indexmanager.services.TopChiNhanh.TopChiNhanhService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@RestController
@RequestMapping("/api/v1")
public class TopChiNhanhController {
    private final TopChiNhanhService topChiNhanhService;

    public TopChiNhanhController(TopChiNhanhService topChiNhanhService) {
        this.topChiNhanhService = topChiNhanhService;
    }

    @PostMapping("/cldv/top-chi-nhanh")
    public ResponseEntity<SimpleAPIResponse> getTopChiNhanh(@RequestBody TopChiNhanhRequest request){
        SimpleAPIResponse response = new SimpleAPIResponse();
        TopChiNhanhResp topChiNhanhResp = topChiNhanhService.getTop10ChiNhanhBuuCuc(request);
        if(Objects.nonNull(topChiNhanhResp)) {
            response.setData(topChiNhanhResp);
        }
        return ResponseEntity.ok(response);
    }
}
