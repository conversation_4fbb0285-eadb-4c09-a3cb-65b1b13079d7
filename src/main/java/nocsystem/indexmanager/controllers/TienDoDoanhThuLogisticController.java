package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPBCDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPCNDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDTLogisticBuuCucV1Dto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDTLogisticChiNhanhV1Dto;
import nocsystem.indexmanager.models.TienDoDoanhThuLogistic.TienDoDoanhThuLogistic;
import nocsystem.indexmanager.services.ChiSoKinhDoanh.ChiSoKinDoanhService;
import nocsystem.indexmanager.services.TienDoDoanhThu.TienDoDoanhThuChiNhanhService;
import nocsystem.indexmanager.services.TienDoDoanhThuLogistic.TienDoDoanhThuLogisticBCService;
import nocsystem.indexmanager.services.TienDoDoanhThuLogistic.TienDoDoanhThuLogisticCNService;
import nocsystem.indexmanager.services.TienDoDoanhThuLogistic.TienDoDoanhThuLogisticService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/v1")
public class TienDoDoanhThuLogisticController {
    @Autowired
    private TienDoDoanhThuLogisticBCService tienDoDoanhThuLogisticBCService;

    @Autowired
    private TienDoDoanhThuLogisticCNService chiNhanhLogisticService;

    @Autowired
    private TienDoDoanhThuLogisticService tienDoDTLogisticService;

    @GetMapping("/tien-do-doanh-thu-logistic")
    public SimpleAPIResponse tienDoDoanhThuLogistic(
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "loai_dichvu", defaultValue = "0") Integer loaiDichVu
    ) {
        if (page > 0) page--;

        if (maChiNhanh == null || maChiNhanh.isEmpty()) {
            CustomizeDataPage<TienDoDTLogisticChiNhanhV1Dto> logistic_cn =
                    chiNhanhLogisticService.findAllDataLogistic(loaiDichVu, toTime, page, pageSize);
            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            simpleAPIResponse.setData(logistic_cn);
            return simpleAPIResponse;
        } else {
            CustomizeDataPage<TienDoDTLogisticBuuCucV1Dto> logistic_bc =
                    tienDoDoanhThuLogisticBCService.findAllDataLogistic(maChiNhanh, maBuuCuc, loaiDichVu, toTime, page, pageSize);
            SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
            simpleAPIResponse.setData(logistic_bc);
            return simpleAPIResponse;
        }
    }

    /*
     * Tính tổng tiến độ doanh thu logistic;
     */
    @GetMapping("/tong-tien-do-doanh-thu-logistic")
    public SimpleAPIResponse tienDoDoanhThuLogistic(
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "loai_dichvu", defaultValue = "0") Integer loaiDichVu,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime,
            @RequestParam(value = "detail", defaultValue = "false") Boolean detail
    ) {
        return tienDoDTLogisticService.tinhTongTienDoDoanhThuLogistic(maChiNhanh, maBuuCuc, loaiDichVu, toTime, detail);
    }
}
