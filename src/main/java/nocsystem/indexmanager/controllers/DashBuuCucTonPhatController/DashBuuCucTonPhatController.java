package nocsystem.indexmanager.controllers.DashBuuCucTonPhatController;

import nocsystem.indexmanager.config.ListContentPageDto;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.entity.ChiSoVersion;
import nocsystem.indexmanager.models.DashBuuCuc.*;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhat;
import nocsystem.indexmanager.repositories.DashBuuCucRepository;
import nocsystem.indexmanager.services.ChiSoVersion.ChiSoVersionPostgresRepo;
import nocsystem.indexmanager.services.DashBuuCucService.DashBuuCucTonPhatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1")
public class DashBuuCucTonPhatController {

    @Autowired
    private DashBuuCucTonPhatService buuCucTonPhatService;

    @Autowired
    private DashBuuCucRepository tonPhatRepository;

    @Autowired
    private ChiSoVersionPostgresRepo chiSoVersionRepository;



    @PostMapping("/dash-buu-cuc/ton-phat")
    public ResponseEntity<?> findAllByTCT(@RequestBody DashTonPhatParam tonPhatParam) {
        ListContentPageCustomDto<DashBuuCucTonPhatDisplayDto,DashboardTongTonPhatdto > findAll = null;
        LocalDate ngayBaocao = LocalDate.parse(tonPhatParam.getNgayBaoCao());
        long version = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaocao, "ton_phat")
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        String timeUpdate = tonPhatRepository.findFirstByVersion(ngayBaocao,version);
        findAll = buuCucTonPhatService.findAllByRoleTCT(tonPhatParam);
        findAll.setTime(timeUpdate);
        SimpleAPIResponse response = new SimpleAPIResponse();

        response.setData(findAll);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/dash-buu-cuc/ton-phat/details")
    public ResponseEntity<?> findAllByTCTDetail(@RequestBody DashTonPhatParam tonPhatParam) {
        Page<DashBuuCucTonPhatChiTietDisplayDto> findAll = null;
        findAll = buuCucTonPhatService.findAll1(tonPhatParam);
        SimpleAPIResponse response = new SimpleAPIResponse();
        ListContentPageDto<DashBuuCucTonPhatChiTietDisplayDto> content = new ListContentPageDto<>(findAll.getTotalElements(), findAll.getNumber(), findAll.getSize(), findAll.getContent());
        response.setData(content);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/export-excel/dash-buu-cuc/ton-phat/details")
    public ResponseEntity<?>  findAllByTCTDetaiExportExcel(HttpServletResponse response,  @RequestBody DashTonPhatParam tonPhatParam) throws IOException, IllegalAccessException, SQLException {
        Page<DashBuuCucTonPhatChiTietDisplayDto> findAll = null;
//        buuCucTonPhatService.exportExcelChiTietTonPhat(response, tonPhatParam);
        buuCucTonPhatService.findAllListToiUuExcel(response, tonPhatParam);
        return ResponseEntity.ok().body(null);

    }

    @PostMapping("/dash-buu-cuc/ton-phat/tuyen-buu-ta")
    public ResponseEntity<?>  findAllByTuyenBuuTa(HttpServletResponse response,  @RequestBody DashTonPhatParam tonPhatParam)  {
        SimpleAPIResponse response1 = new SimpleAPIResponse();
        List<String> tuyenBuuTas =
        buuCucTonPhatService.findAllListBuuTa(tonPhatParam);
        response1.setData(tuyenBuuTas);
        return ResponseEntity.ok().body(response1);
    }

    // api mobile

    @PostMapping("/dash-mobile/dash-buu-cuc/ton-phat")
    public ResponseEntity<?> findAllByTCTMobile(@RequestBody DashTonPhatParam tonPhatParam) {
        ListContentPageCustomDto<DashBuuCucTonPhatDisplayDto,DashboardTongTonPhatdto > findAll;
        LocalDate ngayBaocao = LocalDate.parse(tonPhatParam.getNgayBaoCao());
        long version = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaocao, "ton_phat")
                .map(ChiSoVersion::getVersion)
                .orElse(0L);
        String timeUpdate = tonPhatRepository.findFirstByVersion(ngayBaocao,version);
        findAll = buuCucTonPhatService.findAllByRoleTCT(tonPhatParam);
        findAll.setTime(timeUpdate);
        SimpleAPIResponse response = new SimpleAPIResponse();

        response.setData(findAll);
        return ResponseEntity.ok(response);
    }


    @PostMapping("/dash-mobile/dash-buu-cuc/ton-phat/details")
    public ResponseEntity<?> findAllByTCTDetailMobile(@RequestBody DashTonPhatParam tonPhatParam) {
        Page<DashBuuCucTonPhatChiTietDisplayDto> findAll = null;
        List<DashBuuCucTonPhatChiTietDisplayDto> result = new ArrayList<>();
        String order = tonPhatParam.getOrder();
        String orderBy = tonPhatParam.getOrderBy();

        if (order == null) {
            findAll = buuCucTonPhatService.findAll1(tonPhatParam);
            result = findAll.getContent();
        } else {
            findAll = buuCucTonPhatService.findAll1(tonPhatParam);
            if (order.equalsIgnoreCase("ASC")) {
                if (orderBy != null && orderBy.equalsIgnoreCase("thoiGianPCP")) {
                    result = findAll.getContent().stream().sorted(Comparator.comparing(DashBuuCucTonPhatChiTietDisplayDto::getTgPcp)).collect(Collectors.toList());
                }
            } else {
                result = findAll.getContent().stream().sorted(Comparator.comparing(DashBuuCucTonPhatChiTietDisplayDto::getTgPcp).reversed()).collect(Collectors.toList());
            }
        }


        SimpleAPIResponse response = new SimpleAPIResponse();
        ListContentPageDto<DashBuuCucTonPhatChiTietDisplayDto> content = new ListContentPageDto<>(findAll.getTotalElements(), findAll.getNumber(), findAll.getSize(), result);
        response.setData(content);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/dash-mobile/dash-buu-cuc/ton-phat/tuyen-buu-ta")
    public ResponseEntity<?> findAllByTuyenBuuTaMobile(HttpServletResponse response, @RequestBody DashTonPhatParam tonPhatParam) {
        SimpleAPIResponse response1 = new SimpleAPIResponse();
        List<String> tuyenBuuTas = buuCucTonPhatService.findAllListBuuTa(tonPhatParam);
        response1.setData(tuyenBuuTas);
        return ResponseEntity.ok().body(response1);
    }


}
