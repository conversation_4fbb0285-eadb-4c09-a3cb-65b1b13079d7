package nocsystem.indexmanager.controllers;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuNCV1ResDto;
import nocsystem.indexmanager.services.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCService;
import nocsystem.indexmanager.services.ThongKeTongDoanhThu.ThongKeTongDoanhThuCNService;
import nocsystem.indexmanager.services.TongDoanhThu.TongDoanhThuCNService;
import nocsystem.indexmanager.services.TongDoanhThu.TongDoanhThuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/v1")
public class ThongKeTongDoanhThuController {
    @Autowired
    private ThongKeTongDoanhThuBCService thongKeTongDoanhThuBCService;
    @Autowired
    private ThongKeTongDoanhThuCNService thongKeTongDoanhThuCNService;
    @Autowired
    private TongDoanhThuCNService tongDoanhThuCNSV;

    @Autowired
    private TongDoanhThuService tongDoanhThuService;

    @GetMapping("/thong-ke-tong-doanh-thu")
    public SimpleAPIResponse revenue(
            @RequestParam(value = "ma_chinhanh", defaultValue = "") String maChiNhanh,
            @RequestParam(value = "ma_buucuc", defaultValue = "") String maBuuCuc,
            @RequestParam(value = "to_time") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toTime
    ) throws Exception {
        return tongDoanhThuService.tinhTongDoanhThuManChiTiet(maChiNhanh, maBuuCuc, toTime);
    }
}