package nocsystem.indexmanager.controllers.lastmile_cldv;

import java.lang.reflect.Method;

public class ChartLmDto {

    public Double tylePtc_1;
    public Double tylePtc_2;
    public Double tylePtc_3;
    public Double tylePtc_4;
    public Double tylePtc_5;
    public Double tylePtc_6;
    public Double tylePtc_7;
    public Double tylePtc_8;
    public Double tylePtc_9;
    public Double tylePtc_10;
    public Double tylePtc_11;
    public Double tylePtc_12;
    public Double tylePtc_13;
    public Double tylePtc_14;
    public Double tylePtc_15;
    public Double tylePtc_16;
    public Double tylePtc_17;
    public Double tylePtc_18;
    public Double tylePtc_19;
    public Double tylePtc_20;
    public Double tylePtc_21;
    public Double tylePtc_22;
    public Double tylePtc_23;
    public Double tylePtc_24;
    public Double tylePtc_25;
    public Double tylePtc_26;
    public Double tylePtc_27;
    public Double tylePtc_28;
    public Double tylePtc_29;
    public Double tylePtc_30;
    public Double tylePtc_31;


    public Double getTylePtc_1() {
        return tylePtc_1;
    }

    public void setTylePtc_1(Double tylePtc_1) {
        this.tylePtc_1 = tylePtc_1;
    }

    public Double getTylePtc_2() {
        return tylePtc_2;
    }

    public void setTylePtc_2(Double tylePtc_2) {
        this.tylePtc_2 = tylePtc_2;
    }

    public Double getTylePtc_3() {
        return tylePtc_3;
    }

    public void setTylePtc_3(Double tylePtc_3) {
        this.tylePtc_3 = tylePtc_3;
    }

    public Double getTylePtc_4() {
        return tylePtc_4;
    }

    public void setTylePtc_4(Double tylePtc_4) {
        this.tylePtc_4 = tylePtc_4;
    }

    public Double getTylePtc_5() {
        return tylePtc_5;
    }

    public void setTylePtc_5(Double tylePtc_5) {
        this.tylePtc_5 = tylePtc_5;
    }

    public Double getTylePtc_6() {
        return tylePtc_6;
    }

    public void setTylePtc_6(Double tylePtc_6) {
        this.tylePtc_6 = tylePtc_6;
    }

    public Double getTylePtc_7() {
        return tylePtc_7;
    }

    public void setTylePtc_7(Double tylePtc_7) {
        this.tylePtc_7 = tylePtc_7;
    }

    public Double getTylePtc_8() {
        return tylePtc_8;
    }

    public void setTylePtc_8(Double tylePtc_8) {
        this.tylePtc_8 = tylePtc_8;
    }

    public Double getTylePtc_9() {
        return tylePtc_9;
    }

    public void setTylePtc_9(Double tylePtc_9) {
        this.tylePtc_9 = tylePtc_9;
    }

    public Double getTylePtc_10() {
        return tylePtc_10;
    }

    public void setTylePtc_10(Double tylePtc_10) {
        this.tylePtc_10 = tylePtc_10;
    }

    public Double getTylePtc_11() {
        return tylePtc_11;
    }

    public void setTylePtc_11(Double tylePtc_11) {
        this.tylePtc_11 = tylePtc_11;
    }

    public Double getTylePtc_12() {
        return tylePtc_12;
    }

    public void setTylePtc_12(Double tylePtc_12) {
        this.tylePtc_12 = tylePtc_12;
    }

    public Double getTylePtc_13() {
        return tylePtc_13;
    }

    public void setTylePtc_13(Double tylePtc_13) {
        this.tylePtc_13 = tylePtc_13;
    }

    public Double getTylePtc_14() {
        return tylePtc_14;
    }

    public void setTylePtc_14(Double tylePtc_14) {
        this.tylePtc_14 = tylePtc_14;
    }

    public Double getTylePtc_15() {
        return tylePtc_15;
    }

    public void setTylePtc_15(Double tylePtc_15) {
        this.tylePtc_15 = tylePtc_15;
    }

    public Double getTylePtc_16() {
        return tylePtc_16;
    }

    public void setTylePtc_16(Double tylePtc_16) {
        this.tylePtc_16 = tylePtc_16;
    }

    public Double getTylePtc_17() {
        return tylePtc_17;
    }

    public void setTylePtc_17(Double tylePtc_17) {
        this.tylePtc_17 = tylePtc_17;
    }

    public Double getTylePtc_18() {
        return tylePtc_18;
    }

    public void setTylePtc_18(Double tylePtc_18) {
        this.tylePtc_18 = tylePtc_18;
    }

    public Double getTylePtc_19() {
        return tylePtc_19;
    }

    public void setTylePtc_19(Double tylePtc_19) {
        this.tylePtc_19 = tylePtc_19;
    }

    public Double getTylePtc_20() {
        return tylePtc_20;
    }

    public void setTylePtc_20(Double tylePtc_20) {
        this.tylePtc_20 = tylePtc_20;
    }

    public Double getTylePtc_21() {
        return tylePtc_21;
    }

    public void setTylePtc_21(Double tylePtc_21) {
        this.tylePtc_21 = tylePtc_21;
    }

    public Double getTylePtc_22() {
        return tylePtc_22;
    }

    public void setTylePtc_22(Double tylePtc_22) {
        this.tylePtc_22 = tylePtc_22;
    }

    public Double getTylePtc_23() {
        return tylePtc_23;
    }

    public void setTylePtc_23(Double tylePtc_23) {
        this.tylePtc_23 = tylePtc_23;
    }

    public Double getTylePtc_24() {
        return tylePtc_24;
    }

    public void setTylePtc_24(Double tylePtc_24) {
        this.tylePtc_24 = tylePtc_24;
    }

    public Double getTylePtc_25() {
        return tylePtc_25;
    }

    public void setTylePtc_25(Double tylePtc_25) {
        this.tylePtc_25 = tylePtc_25;
    }

    public Double getTylePtc_26() {
        return tylePtc_26;
    }

    public void setTylePtc_26(Double tylePtc_26) {
        this.tylePtc_26 = tylePtc_26;
    }

    public Double getTylePtc_27() {
        return tylePtc_27;
    }

    public void setTylePtc_27(Double tylePtc_27) {
        this.tylePtc_27 = tylePtc_27;
    }

    public Double getTylePtc_28() {
        return tylePtc_28;
    }

    public void setTylePtc_28(Double tylePtc_28) {
        this.tylePtc_28 = tylePtc_28;
    }

    public Double getTylePtc_29() {
        return tylePtc_29;
    }

    public void setTylePtc_29(Double tylePtc_29) {
        this.tylePtc_29 = tylePtc_29;
    }

    public Double getTylePtc_30() {
        return tylePtc_30;
    }

    public void setTylePtc_30(Double tylePtc_30) {
        this.tylePtc_30 = tylePtc_30;
    }

    public Double getTylePtc_31() {
        return tylePtc_31;
    }

    public void setTylePtc_31(Double tylePtc_31) {
        this.tylePtc_31 = tylePtc_31;
    }

    public ChartLmDto() {
    }

    public ChartLmDto(Double tylePtc_1, Double tylePtc_2, Double tylePtc_3, Double tylePtc_4, Double tylePtc_5, Double tylePtc_6, Double tylePtc_7, Double tylePtc_8, Double tylePtc_9, Double tylePtc_10, Double tylePtc_11, Double tylePtc_12, Double tylePtc_13, Double tylePtc_14, Double tylePtc_15, Double tylePtc_16, Double tylePtc_17, Double tylePtc_18, Double tylePtc_19, Double tylePtc_20, Double tylePtc_21, Double tylePtc_22, Double tylePtc_23, Double tylePtc_24, Double tylePtc_25, Double tylePtc_26, Double tylePtc_27, Double tylePtc_28, Double tylePtc_29, Double tylePtc_30, Double tylePtc_31) {
        this.tylePtc_1 = tylePtc_1;
        this.tylePtc_2 = tylePtc_2;
        this.tylePtc_3 = tylePtc_3;
        this.tylePtc_4 = tylePtc_4;
        this.tylePtc_5 = tylePtc_5;
        this.tylePtc_6 = tylePtc_6;
        this.tylePtc_7 = tylePtc_7;
        this.tylePtc_8 = tylePtc_8;
        this.tylePtc_9 = tylePtc_9;
        this.tylePtc_10 = tylePtc_10;
        this.tylePtc_11 = tylePtc_11;
        this.tylePtc_12 = tylePtc_12;
        this.tylePtc_13 = tylePtc_13;
        this.tylePtc_14 = tylePtc_14;
        this.tylePtc_15 = tylePtc_15;
        this.tylePtc_16 = tylePtc_16;
        this.tylePtc_17 = tylePtc_17;
        this.tylePtc_18 = tylePtc_18;
        this.tylePtc_19 = tylePtc_19;
        this.tylePtc_20 = tylePtc_20;
        this.tylePtc_21 = tylePtc_21;
        this.tylePtc_22 = tylePtc_22;
        this.tylePtc_23 = tylePtc_23;
        this.tylePtc_24 = tylePtc_24;
        this.tylePtc_25 = tylePtc_25;
        this.tylePtc_26 = tylePtc_26;
        this.tylePtc_27 = tylePtc_27;
        this.tylePtc_28 = tylePtc_28;
        this.tylePtc_29 = tylePtc_29;
        this.tylePtc_30 = tylePtc_30;
        this.tylePtc_31 = tylePtc_31;
    }


    public void setProperty(String propertyName, Object value) {
        try {
            String setIsterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);

            Method setIsterMethod = getClass().getMethod(setIsterMethodName, value.getClass());

            setIsterMethod.invoke(this, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
