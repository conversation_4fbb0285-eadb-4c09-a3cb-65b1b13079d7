package nocsystem.indexmanager.controllers.lastmile_cldv;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Date;

@Table(name = "tile_khauphat_tonghop_thang_cron")
@Entity
@IdClass(LastmileRepoNgayKeyPostgres.class)
public class LastmileThangCronEntity implements Serializable {
    @Id
    @Column(name = "ngay_kpi")
    Date ngay_kpi;

    @Id
    @Column(name = "ma_cn")
    String  ma_cn;

    @Id
    @Column(name = "ma_bc")
    String  ma_bc;

    @Column(name = "sl_phat")
    Long  sl_phat;

    @Column(name = "sl_phat_n1")
    Long  sl_phat_n1;
    @Column(name = "sl_phat_thanhcong")
    Long  sl_phat_thanhcong;
    @Column(name = "sl_phat_thanhcong_n1")
    Long  sl_phat_thanhcong_n1;


    @Column(name = "tyle_phat_thanhcong")
    Double  tyle_phat_thanhcong;


    @Column(name = "tyle_phat_thanhcong_n1")
    Double  tyle_phat_thanhcong_n1;


    @Column(name = "tyle_phat_thanhcong_dunggio")
    Double  tyle_phat_thanhcong_dunggio;

    @Column(name = "tyle_phat_thanhcong_dunggio_n1")
    Double  tyle_phat_thanhcong_dunggio_n1;

    @Column(name = "tyle_phat_thanhcong_ncod")
    Double  tyle_phat_thanhcong_ncod;


    @Column(name = "tyle_phat_thanhcong_ncod_n1")
    Double  tyle_phat_thanhcong_ncod_n1;


    @Column(name = "tyle_phat_thanhcong_cod")
    Double  tyle_phat_thanhcong_cod;


    @Column(name = "tyle_phat_thanhcong_cod_n1")
    Double  tyle_phat_thanhcong_cod_n1;


    @Column(name = "tyle_hoan")
    Double  tyle_hoan;


    @Column(name = "tyle_hoan_n1")
    Double  tyle_hoan_n1;

    @Column(name = "tang_giam_sl_phat")
    Long  tang_giam_sl_phat;

    @Column(name = "tang_giam_tyle_ptc")
    Double  tang_giam_tyle_ptc;

    @Column(name = "kpi_ptc")
    Double  kpi_ptc;

    @Column(name = "tang_giam_sl_ptc")
    Long  tang_giam_sl_ptc;


    @Column(name = "tang_giam_tyle_ptc_ncod")
    Double  tang_giam_tyle_ptc_ncod;

    @Column(name = "kpi_ptc_ncod")
    Double  kpi_ptc_ncod;

    @Column(name = "tang_giam_tyle_ptc_cod")
    Double  tang_giam_tyle_ptc_cod;


    @Column(name = "kpi_ptc_cod")
    Double  kpi_ptc_cod;


    @Column(name = "tang_giam_tyle_ptc_dunggio")
    Double  tang_giam_tyle_ptc_dunggio;


    @Column(name = "kpi_ptc_dunggio")
    Double  kpi_ptc_dunggio;

    @Column(name = "tang_giam_tyle_hoan")
    Double  tang_giam_tyle_hoan;

    @Column(name = "kpi_hoan")
    Double  kpi_hoan;

    @Column(name = "activate")
    Integer  activate;

    @Column(name = "updated_at")
    Long  updated_at;

    public LastmileThangCronEntity() {
    }


    public LastmileThangCronEntity(Date ngay_kpi,
                                  String ma_cn,
                                  String ma_bc,
                                  Long sl_phat,
                                  Long sl_phat_n1,
                                  Long sl_phat_thanhcong,
                                  Long sl_phat_thanhcong_n1,
                                  Double tyle_phat_thanhcong,
                                  Double tyle_phat_thanhcong_n1,
                                  Double tyle_phat_thanhcong_dunggio,
                                  Double tyle_phat_thanhcong_dunggio_n1,
                                  Double tyle_phat_thanhcong_ncod,
                                  Double tyle_phat_thanhcong_ncod_n1,
                                  Double tyle_phat_thanhcong_cod,
                                  Double tyle_phat_thanhcong_cod_n1,
                                  Double tyle_hoan,
                                  Double tyle_hoan_n1,

                                  Long updated_at,

                                  Long tang_giam_sl_phat,
                                  Long tang_giam_sl_ptc,
                                  Double tang_giam_tyle_ptc,
                                  Double tang_giam_tyle_ptc_dunggio,
                                  Double tang_giam_tyle_ptc_ncod,
                                  Double tang_giam_tyle_hoan,
                                  Double tang_giam_tyle_ptc_cod,
                                  Double kpi_ptc,
                                  Double kpi_ptc_ncod,
                                  Double kpi_ptc_cod,
                                  Double kpi_hoan
    ) {
        this.ngay_kpi = ngay_kpi;
        this.ma_cn = ma_cn;
        this.ma_bc = ma_bc;
        this.sl_phat = sl_phat;
        this.sl_phat_n1 = sl_phat_n1;
        this.sl_phat_thanhcong = sl_phat_thanhcong;
        this.sl_phat_thanhcong_n1 = sl_phat_thanhcong_n1;
        this.tyle_phat_thanhcong = tyle_phat_thanhcong;
        this.tyle_phat_thanhcong_n1 = tyle_phat_thanhcong_n1;
        this.tyle_phat_thanhcong_dunggio = tyle_phat_thanhcong_dunggio;
        this.tyle_phat_thanhcong_dunggio_n1 = tyle_phat_thanhcong_dunggio_n1;
        this.tyle_phat_thanhcong_ncod = tyle_phat_thanhcong_ncod;
        this.tyle_phat_thanhcong_ncod_n1 = tyle_phat_thanhcong_ncod_n1;
        this.tyle_phat_thanhcong_cod = tyle_phat_thanhcong_cod;
        this.tyle_phat_thanhcong_cod_n1 = tyle_phat_thanhcong_cod_n1;
        this.tyle_hoan = tyle_hoan;
        this.tyle_hoan_n1 = tyle_hoan_n1;
        this.tang_giam_sl_phat = tang_giam_sl_phat;
        this.tang_giam_tyle_ptc = tang_giam_tyle_ptc;
        this.kpi_ptc = kpi_ptc;
        this.tang_giam_sl_ptc = tang_giam_sl_ptc;
        this.tang_giam_tyle_ptc_ncod = tang_giam_tyle_ptc_ncod;
        this.kpi_ptc_ncod = kpi_ptc_ncod;
        this.tang_giam_tyle_ptc_cod = tang_giam_tyle_ptc_cod;
        this.kpi_ptc_cod = kpi_ptc_cod;
        this.tang_giam_tyle_ptc_dunggio = tang_giam_tyle_ptc_dunggio;
        this.tang_giam_tyle_hoan = tang_giam_tyle_hoan;
        this.kpi_hoan = kpi_hoan;
        this.updated_at = updated_at;
    }


    public LastmileThangCronEntity(Date ngay_kpi, String ma_cn, String ma_bc, Long sl_phat, Long sl_phat_n1, Long sl_phat_thanhcong, Long sl_phat_thanhcong_n1, Double tyle_phat_thanhcong, Double tyle_phat_thanhcong_n1, Double tyle_phat_thanhcong_dunggio, Double tyle_phat_thanhcong_dunggio_n1, Double tyle_phat_thanhcong_ncod, Double tyle_phat_thanhcong_ncod_n1, Double tyle_phat_thanhcong_cod, Double tyle_phat_thanhcong_cod_n1, Double tyle_hoan, Double tyle_hoan_n1, Long tang_giam_sl_phat, Double tang_giam_tyle_ptc, Double kpi_ptc, Long tang_giam_sl_ptc, Double tang_giam_tyle_ptc_ncod, Double kpi_ptc_ncod, Double tang_giam_tyle_ptc_cod, Double kpi_ptc_cod, Double tang_giam_tyle_ptc_dunggio, Double kpi_ptc_dunggio, Double tang_giam_tyle_hoan, Double kpi_hoan, Integer activate, Long updated_at) {
        this.ngay_kpi = ngay_kpi;
        this.ma_cn = ma_cn;
        this.ma_bc = ma_bc;
        this.sl_phat = sl_phat;
        this.sl_phat_n1 = sl_phat_n1;
        this.sl_phat_thanhcong = sl_phat_thanhcong;
        this.sl_phat_thanhcong_n1 = sl_phat_thanhcong_n1;
        this.tyle_phat_thanhcong = tyle_phat_thanhcong;
        this.tyle_phat_thanhcong_n1 = tyle_phat_thanhcong_n1;
        this.tyle_phat_thanhcong_dunggio = tyle_phat_thanhcong_dunggio;
        this.tyle_phat_thanhcong_dunggio_n1 = tyle_phat_thanhcong_dunggio_n1;
        this.tyle_phat_thanhcong_ncod = tyle_phat_thanhcong_ncod;
        this.tyle_phat_thanhcong_ncod_n1 = tyle_phat_thanhcong_ncod_n1;
        this.tyle_phat_thanhcong_cod = tyle_phat_thanhcong_cod;
        this.tyle_phat_thanhcong_cod_n1 = tyle_phat_thanhcong_cod_n1;
        this.tyle_hoan = tyle_hoan;
        this.tyle_hoan_n1 = tyle_hoan_n1;
        this.tang_giam_sl_phat = tang_giam_sl_phat;
        this.tang_giam_tyle_ptc = tang_giam_tyle_ptc;
        this.kpi_ptc = kpi_ptc;
        this.tang_giam_sl_ptc = tang_giam_sl_ptc;
        this.tang_giam_tyle_ptc_ncod = tang_giam_tyle_ptc_ncod;
        this.kpi_ptc_ncod = kpi_ptc_ncod;
        this.tang_giam_tyle_ptc_cod = tang_giam_tyle_ptc_cod;
        this.kpi_ptc_cod = kpi_ptc_cod;
        this.tang_giam_tyle_ptc_dunggio = tang_giam_tyle_ptc_dunggio;
        this.kpi_ptc_dunggio = kpi_ptc_dunggio;
        this.tang_giam_tyle_hoan = tang_giam_tyle_hoan;
        this.kpi_hoan = kpi_hoan;
        this.activate = activate;
        this.updated_at = updated_at;
    }

    public Date getNgay_kpi() {
        return ngay_kpi;
    }

    public void setNgay_kpi(Date ngay_kpi) {
        this.ngay_kpi = ngay_kpi;
    }

    public String getMa_cn() {
        return ma_cn;
    }

    public void setMa_cn(String ma_cn) {
        this.ma_cn = ma_cn;
    }

    public String getMa_bc() {
        return ma_bc;
    }

    public void setMa_bc(String ma_bc) {
        this.ma_bc = ma_bc;
    }

    public Long getSl_phat() {
        return sl_phat;
    }

    public void setSl_phat(Long sl_phat) {
        this.sl_phat = sl_phat;
    }

    public Long getSl_phat_n1() {
        return sl_phat_n1;
    }

    public void setSl_phat_n1(Long sl_phat_n1) {
        this.sl_phat_n1 = sl_phat_n1;
    }

    public Long getSl_phat_thanhcong() {
        return sl_phat_thanhcong;
    }

    public void setSl_phat_thanhcong(Long sl_phat_thanhcong) {
        this.sl_phat_thanhcong = sl_phat_thanhcong;
    }

    public Long getSl_phat_thanhcong_n1() {
        return sl_phat_thanhcong_n1;
    }

    public void setSl_phat_thanhcong_n1(Long sl_phat_thanhcong_n1) {
        this.sl_phat_thanhcong_n1 = sl_phat_thanhcong_n1;
    }

    public Double getTyle_phat_thanhcong() {
        return tyle_phat_thanhcong;
    }

    public void setTyle_phat_thanhcong(Double tyle_phat_thanhcong) {
        this.tyle_phat_thanhcong = tyle_phat_thanhcong;
    }

    public Double getTyle_phat_thanhcong_n1() {
        return tyle_phat_thanhcong_n1;
    }

    public void setTyle_phat_thanhcong_n1(Double tyle_phat_thanhcong_n1) {
        this.tyle_phat_thanhcong_n1 = tyle_phat_thanhcong_n1;
    }

    public Double getTyle_phat_thanhcong_dunggio() {
        return tyle_phat_thanhcong_dunggio;
    }

    public void setTyle_phat_thanhcong_dunggio(Double tyle_phat_thanhcong_dunggio) {
        this.tyle_phat_thanhcong_dunggio = tyle_phat_thanhcong_dunggio;
    }

    public Double getTyle_phat_thanhcong_dunggio_n1() {
        return tyle_phat_thanhcong_dunggio_n1;
    }

    public void setTyle_phat_thanhcong_dunggio_n1(Double tyle_phat_thanhcong_dunggio_n1) {
        this.tyle_phat_thanhcong_dunggio_n1 = tyle_phat_thanhcong_dunggio_n1;
    }

    public Double getTyle_phat_thanhcong_ncod() {
        return tyle_phat_thanhcong_ncod;
    }

    public void setTyle_phat_thanhcong_ncod(Double tyle_phat_thanhcong_ncod) {
        this.tyle_phat_thanhcong_ncod = tyle_phat_thanhcong_ncod;
    }

    public Double getTyle_phat_thanhcong_ncod_n1() {
        return tyle_phat_thanhcong_ncod_n1;
    }

    public void setTyle_phat_thanhcong_ncod_n1(Double tyle_phat_thanhcong_ncod_n1) {
        this.tyle_phat_thanhcong_ncod_n1 = tyle_phat_thanhcong_ncod_n1;
    }

    public Double getTyle_phat_thanhcong_cod() {
        return tyle_phat_thanhcong_cod;
    }

    public void setTyle_phat_thanhcong_cod(Double tyle_phat_thanhcong_cod) {
        this.tyle_phat_thanhcong_cod = tyle_phat_thanhcong_cod;
    }

    public Double getTyle_phat_thanhcong_cod_n1() {
        return tyle_phat_thanhcong_cod_n1;
    }

    public void setTyle_phat_thanhcong_cod_n1(Double tyle_phat_thanhcong_cod_n1) {
        this.tyle_phat_thanhcong_cod_n1 = tyle_phat_thanhcong_cod_n1;
    }

    public Double getTyle_hoan() {
        return tyle_hoan;
    }

    public void setTyle_hoan(Double tyle_hoan) {
        this.tyle_hoan = tyle_hoan;
    }

    public Double getTyle_hoan_n1() {
        return tyle_hoan_n1;
    }

    public void setTyle_hoan_n1(Double tyle_hoan_n1) {
        this.tyle_hoan_n1 = tyle_hoan_n1;
    }

    public Long getTang_giam_sl_phat() {
        return tang_giam_sl_phat;
    }

    public void setTang_giam_sl_phat(Long tang_giam_sl_phat) {
        this.tang_giam_sl_phat = tang_giam_sl_phat;
    }

    public Double getTang_giam_tyle_ptc() {
        return tang_giam_tyle_ptc;
    }

    public void setTang_giam_tyle_ptc(Double tang_giam_tyle_ptc) {
        this.tang_giam_tyle_ptc = tang_giam_tyle_ptc;
    }

    public Double getKpi_ptc() {
        return kpi_ptc;
    }

    public void setKpi_ptc(Double kpi_ptc) {
        this.kpi_ptc = kpi_ptc;
    }

    public Long getTang_giam_sl_ptc() {
        return tang_giam_sl_ptc;
    }

    public void setTang_giam_sl_ptc(Long tang_giam_sl_ptc) {
        this.tang_giam_sl_ptc = tang_giam_sl_ptc;
    }

    public Double getTang_giam_tyle_ptc_ncod() {
        return tang_giam_tyle_ptc_ncod;
    }

    public void setTang_giam_tyle_ptc_ncod(Double tang_giam_tyle_ptc_ncod) {
        this.tang_giam_tyle_ptc_ncod = tang_giam_tyle_ptc_ncod;
    }

    public Double getKpi_ptc_ncod() {
        return kpi_ptc_ncod;
    }

    public void setKpi_ptc_ncod(Double kpi_ptc_ncod) {
        this.kpi_ptc_ncod = kpi_ptc_ncod;
    }

    public Double getTang_giam_tyle_ptc_cod() {
        return tang_giam_tyle_ptc_cod;
    }

    public void setTang_giam_tyle_ptc_cod(Double tang_giam_tyle_ptc_cod) {
        this.tang_giam_tyle_ptc_cod = tang_giam_tyle_ptc_cod;
    }

    public Double getKpi_ptc_cod() {
        return kpi_ptc_cod;
    }

    public void setKpi_ptc_cod(Double kpi_ptc_cod) {
        this.kpi_ptc_cod = kpi_ptc_cod;
    }

    public Double getTang_giam_tyle_ptc_dunggio() {
        return tang_giam_tyle_ptc_dunggio;
    }

    public void setTang_giam_tyle_ptc_dunggio(Double tang_giam_tyle_ptc_dunggio) {
        this.tang_giam_tyle_ptc_dunggio = tang_giam_tyle_ptc_dunggio;
    }

    public Double getKpi_ptc_dunggio() {
        return kpi_ptc_dunggio;
    }

    public void setKpi_ptc_dunggio(Double kpi_ptc_dunggio) {
        this.kpi_ptc_dunggio = kpi_ptc_dunggio;
    }

    public Double getTang_giam_tyle_hoan() {
        return tang_giam_tyle_hoan;
    }

    public void setTang_giam_tyle_hoan(Double tang_giam_tyle_hoan) {
        this.tang_giam_tyle_hoan = tang_giam_tyle_hoan;
    }

    public Double getKpi_hoan() {
        return kpi_hoan;
    }

    public void setKpi_hoan(Double kpi_hoan) {
        this.kpi_hoan = kpi_hoan;
    }

    public Integer getActivate() {
        return activate;
    }

    public void setActivate(Integer activate) {
        this.activate = activate;
    }

    public Long getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(Long updated_at) {
        this.updated_at = updated_at;
    }
}
