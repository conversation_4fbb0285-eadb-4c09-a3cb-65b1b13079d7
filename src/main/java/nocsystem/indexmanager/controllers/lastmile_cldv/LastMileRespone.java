package nocsystem.indexmanager.controllers.lastmile_cldv;

public class LastMileRespone {
    public Long sl_phat;
    public Long sl_phat_n1;
    public Long sl_phat_thanhcong;
    public Long sl_phat_thanhcong_n1;

    public Double tyle_phat_thanhcong;
    public Double tyle_phat_thanhcong_n1;
    public Double tyle_phat_thanhcong_dunggio;
    public Double tyle_phat_thanhcong_dunggio_n1;
    public Double tyle_phat_thanhcong_ncod;
    public Double tyle_phat_thanhcong_ncod_n1;
    public Double tyle_phat_thanhcong_cod;
    public Double tyle_phat_thanhcong_cod_n1;
    public Double tyle_hoan;
    public Double tyle_hoan_n1;

    public Long tang_giam_sl_phat;
    public Long tang_giam_sl_ptc;

    public Double tang_giam_tyle_ptc;
    public Double kpi_ptc;
    public Double tang_giam_tyle_ptc_ncod;
    public Double kpi_ptc_ncod;
    public Double tang_giam_tyle_ptc_cod;
    public Double kpi_ptc_cod;
    public Double tang_giam_tyle_ptc_dunggio;
    public Double kpi_ptc_dunggio;
    public Double tang_giam_tyle_hoan;
    public Double kpi_hoan;


    public Long getTang_giam_sl_phat() {
        return tang_giam_sl_phat;
    }

    public void setTang_giam_sl_phat(Long tang_giam_sl_phat) {
        this.tang_giam_sl_phat = tang_giam_sl_phat;
    }

    public Long getTang_giam_sl_ptc() {
        return tang_giam_sl_ptc;
    }

    public void setTang_giam_sl_ptc(Long tang_giam_sl_ptc) {
        this.tang_giam_sl_ptc = tang_giam_sl_ptc;
    }

    public Double getTang_giam_tyle_ptc() {
        return tang_giam_tyle_ptc;
    }

    public void setTang_giam_tyle_ptc(Double tang_giam_tyle_ptc) {
        this.tang_giam_tyle_ptc = tang_giam_tyle_ptc;
    }

    public Double getKpi_ptc() {
        return kpi_ptc;
    }

    public void setKpi_ptc(Double kpi_ptc) {
        this.kpi_ptc = kpi_ptc;
    }

    public Double getTang_giam_tyle_ptc_ncod() {
        return tang_giam_tyle_ptc_ncod;
    }

    public void setTang_giam_tyle_ptc_ncod(Double tang_giam_tyle_ptc_ncod) {
        this.tang_giam_tyle_ptc_ncod = tang_giam_tyle_ptc_ncod;
    }

    public Double getKpi_ptc_ncod() {
        return kpi_ptc_ncod;
    }

    public void setKpi_ptc_ncod(Double kpi_ptc_ncod) {
        this.kpi_ptc_ncod = kpi_ptc_ncod;
    }

    public Double getTang_giam_tyle_ptc_cod() {
        return tang_giam_tyle_ptc_cod;
    }

    public void setTang_giam_tyle_ptc_cod(Double tang_giam_tyle_ptc_cod) {
        this.tang_giam_tyle_ptc_cod = tang_giam_tyle_ptc_cod;
    }

    public Double getKpi_ptc_cod() {
        return kpi_ptc_cod;
    }

    public void setKpi_ptc_cod(Double kpi_ptc_cod) {
        this.kpi_ptc_cod = kpi_ptc_cod;
    }

    public Double getTang_giam_tyle_ptc_dunggio() {
        return tang_giam_tyle_ptc_dunggio;
    }

    public void setTang_giam_tyle_ptc_dunggio(Double tang_giam_tyle_ptc_dunggio) {
        this.tang_giam_tyle_ptc_dunggio = tang_giam_tyle_ptc_dunggio;
    }

    public Double getKpi_ptc_dunggio() {
        return kpi_ptc_dunggio;
    }

    public void setKpi_ptc_dunggio(Double kpi_ptc_dunggio) {
        this.kpi_ptc_dunggio = kpi_ptc_dunggio;
    }

    public Double getTang_giam_tyle_hoan() {
        return tang_giam_tyle_hoan;
    }

    public void setTang_giam_tyle_hoan(Double tang_giam_tyle_hoan) {
        this.tang_giam_tyle_hoan = tang_giam_tyle_hoan;
    }

    public Double getKpi_hoan() {
        return kpi_hoan;
    }

    public void setKpi_hoan(Double kpi_hoan) {
        this.kpi_hoan = kpi_hoan;
    }

    public Long getSl_phat() {
        return sl_phat;
    }

    public void setSl_phat(Long sl_phat) {
        this.sl_phat = sl_phat;
    }

    public Long getSl_phat_n1() {
        return sl_phat_n1;
    }

    public void setSl_phat_n1(Long sl_phat_n1) {
        this.sl_phat_n1 = sl_phat_n1;
    }

    public Long getSl_phat_thanhcong() {
        return sl_phat_thanhcong;
    }

    public void setSl_phat_thanhcong(Long sl_phat_thanhcong) {
        this.sl_phat_thanhcong = sl_phat_thanhcong;
    }

    public Long getSl_phat_thanhcong_n1() {
        return sl_phat_thanhcong_n1;
    }

    public void setSl_phat_thanhcong_n1(Long sl_phat_thanhcong_n1) {
        this.sl_phat_thanhcong_n1 = sl_phat_thanhcong_n1;
    }

    public Double getTyle_phat_thanhcong() {
        return tyle_phat_thanhcong;
    }

    public void setTyle_phat_thanhcong(Double tyle_phat_thanhcong) {
        this.tyle_phat_thanhcong = tyle_phat_thanhcong;
    }

    public Double getTyle_phat_thanhcong_n1() {
        return tyle_phat_thanhcong_n1;
    }

    public void setTyle_phat_thanhcong_n1(Double tyle_phat_thanhcong_n1) {
        this.tyle_phat_thanhcong_n1 = tyle_phat_thanhcong_n1;
    }

    public Double getTyle_phat_thanhcong_dunggio() {
        return tyle_phat_thanhcong_dunggio;
    }

    public void setTyle_phat_thanhcong_dunggio(Double tyle_phat_thanhcong_dunggio) {
        this.tyle_phat_thanhcong_dunggio = tyle_phat_thanhcong_dunggio;
    }

    public Double getTyle_phat_thanhcong_dunggio_n1() {
        return tyle_phat_thanhcong_dunggio_n1;
    }

    public void setTyle_phat_thanhcong_dunggio_n1(Double tyle_phat_thanhcong_dunggio_n1) {
        this.tyle_phat_thanhcong_dunggio_n1 = tyle_phat_thanhcong_dunggio_n1;
    }

    public Double getTyle_phat_thanhcong_ncod() {
        return tyle_phat_thanhcong_ncod;
    }

    public void setTyle_phat_thanhcong_ncod(Double tyle_phat_thanhcong_ncod) {
        this.tyle_phat_thanhcong_ncod = tyle_phat_thanhcong_ncod;
    }

    public Double getTyle_phat_thanhcong_ncod_n1() {
        return tyle_phat_thanhcong_ncod_n1;
    }

    public void setTyle_phat_thanhcong_ncod_n1(Double tyle_phat_thanhcong_ncod_n1) {
        this.tyle_phat_thanhcong_ncod_n1 = tyle_phat_thanhcong_ncod_n1;
    }

    public Double getTyle_phat_thanhcong_cod() {
        return tyle_phat_thanhcong_cod;
    }

    public void setTyle_phat_thanhcong_cod(Double tyle_phat_thanhcong_cod) {
        this.tyle_phat_thanhcong_cod = tyle_phat_thanhcong_cod;
    }

    public Double getTyle_phat_thanhcong_cod_n1() {
        return tyle_phat_thanhcong_cod_n1;
    }

    public void setTyle_phat_thanhcong_cod_n1(Double tyle_phat_thanhcong_cod_n1) {
        this.tyle_phat_thanhcong_cod_n1 = tyle_phat_thanhcong_cod_n1;
    }

    public Double getTyle_hoan() {
        return tyle_hoan;
    }

    public void setTyle_hoan(Double tyle_hoan) {
        this.tyle_hoan = tyle_hoan;
    }

    public Double getTyle_hoan_n1() {
        return tyle_hoan_n1;
    }

    public void setTyle_hoan_n1(Double tyle_hoan_n1) {
        this.tyle_hoan_n1 = tyle_hoan_n1;
    }
}
