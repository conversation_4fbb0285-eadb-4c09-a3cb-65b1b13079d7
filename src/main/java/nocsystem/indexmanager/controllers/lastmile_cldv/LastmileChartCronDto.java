package nocsystem.indexmanager.controllers.lastmile_cldv;

public class LastmileChartCronDto {
    public String ngayBaoCao;
    public Double tyLe;

    public Double roundToTwoDecimalPlaces(Double number) {
        if(number == null ) return null;
        return Math.round(number * 10000.0) / 100.0;
    }

    public LastmileChartCronDto() {
    }

    public LastmileChartCronDto(String ngayBaoCao, Double tyLe) {
        this.ngayBaoCao = ngayBaoCao;
        this.tyLe = roundToTwoDecimalPlaces(tyLe);
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public Double getTyLe() {
        return tyLe;
    }

    public void setTyLe(Double tyLe) {
        this.tyLe = tyLe;
    }
}
