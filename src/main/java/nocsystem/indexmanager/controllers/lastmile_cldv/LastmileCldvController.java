package nocsystem.indexmanager.controllers.lastmile_cldv;

import nocsystem.indexmanager.services.lastmile_cldv.LastMileCronJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import nocsystem.indexmanager.global.variable.UserContext;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class LastmileCldvController {


    @Autowired
    LastMileCronJob lastMileCronJob;
    @PostMapping("/last-mile/show-infor")
    public ResponseEntity<?> fillAllDataShow(@RequestBody LasmileBody body) {
        SimpleAPIResponse spa = new SimpleAPIResponse();
        if(!
                UserContext.getUserData().getIsRoleViewDashTCT().equals("true")
        ){

            return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8").body(spa);
        }

        LastmileCronDto lm = lastMileCronJob.getDatashow(body);
        spa.setData(lm);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8").body(spa);
    }


    @PostMapping("/last-mile/chart")
    public ResponseEntity<?> fillAllChart(@RequestBody LasmileBody body) {
        SimpleAPIResponse spa = new SimpleAPIResponse();
        if(!
                UserContext.getUserData().getIsRoleViewDashTCT().equals("true")
        ){

            return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8").body(spa);
        }

        List<LastmileChartCronDto> lm = lastMileCronJob.getDataChart(body);
        spa.setData(lm);
        return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8").body(spa);
    }

}
