package nocsystem.indexmanager.controllers;


import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.*;
import nocsystem.indexmanager.services.ChatLuongSanLuong.DmChatLuongService;
import nocsystem.indexmanager.services.ChatLuongSanLuong.DmSanLuongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class DmCLSLController {
    @Autowired
    private DmChatLuongService dmChatLuongService;

    @Autowired
    private DmSanLuongService dmSanLuongService;

    @GetMapping("/san-luong-kho")
    public SimpleAPIResponse getListDmSanLuongKho(
            @RequestParam(required = false) String maTinh, String maBuuCuc,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<DmSanLuongKhoResDto> result = dmSanLuongService.searchKho(maTinh,maBuuCuc,ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/chat-luong-kho")
    public SimpleAPIResponse getListDmChatLuongKho(
            @RequestParam(required = false) String maTinh, String maBuuCuc,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<DmChatLuongKhoResDto> result = dmChatLuongService.searchKho(maTinh,maBuuCuc,ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/chat-luong-buu-ta")
    public SimpleAPIResponse getListDmChatLuongBuuTa(
            @RequestParam(required = false) String maNhanVien, String maChiNhanh, String maBuuCuc,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<DmChatLuongBuuTaResDto> result = dmChatLuongService.searchBuuTa(maNhanVien,maChiNhanh,maBuuCuc,ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/san-luong-buu-ta")
    public SimpleAPIResponse getListDmSanLuongBuuTa(
            @RequestParam(required = false) String maNhanVien, String maChiNhanh, String maBuuCuc,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<DmSanLuongBuuTaResDto> result = dmSanLuongService.searchBuuTa(maNhanVien,maChiNhanh,maBuuCuc,ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/san-luong-buu-ta-lk")
    public SimpleAPIResponse getListDmSanLuongBuuTaLk(
            @RequestParam(required = false) String maNhanVien, String maChiNhanh, String maBuuCuc,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<LkSanLuongBuuTaResDto> result = dmSanLuongService.searchBuuTaLk(maNhanVien,maChiNhanh,maBuuCuc,ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/san-luong-kho-lk")
    public SimpleAPIResponse getListDmSanLuongKhoLk(
            @RequestParam(required = false) String maTinh, String maBuuCuc,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<LkSanLuongKhoResDto> result = dmSanLuongService.searchKhoLk(maTinh,maBuuCuc,ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/san-luong-chi-nhanh-lk")
    public SimpleAPIResponse getListDmSanLuongChiNhanhLk(
            @RequestParam(required = false) String maTinh,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<LkSanLuongChiNhanhResDto> result = dmSanLuongService.searchChiNhanhLk(maTinh,ngayBaoCao);
        sp.setData(result);
        return sp;
    }

    @GetMapping("/san-luong-chi-nhanh-theo-ngay")
    public SimpleAPIResponse getListDmSanLuongChiNhanhTheoNgay(
            @RequestParam(required = false) String maTinh,
            @RequestParam(value="ngayBaoCao") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ngayBaoCao) {
        SimpleAPIResponse sp = new SimpleAPIResponse();
        List<DmSanLuongChiNhanhTResDto> result = dmSanLuongService.searchKhoChiNhanhTheoNgay(maTinh,ngayBaoCao);
        sp.setData(result);
        return sp;
    }
}
