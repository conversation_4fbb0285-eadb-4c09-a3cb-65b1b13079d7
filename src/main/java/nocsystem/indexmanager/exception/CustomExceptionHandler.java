package nocsystem.indexmanager.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

@Slf4j
@RestControllerAdvice
public class CustomExceptionHandler {

    @ExceptionHandler(nocsystem.indexmanager.exception.NotFoundException.class)
    public ResponseEntity<?> handlerNotFoundException(nocsystem.indexmanager.exception.NotFoundException ex, WebRequest req) throws JsonProcessingException {
        // Log err
        log.error("handlerNotFoundException", ex);
        SimpleAPIResponse err = new SimpleAPIResponse(HttpStatus.NOT_FOUND.value(), ex.getMessage(), null);

        return new ResponseEntity<>(err, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(DuplicateRecordException.class)
    public ResponseEntity<?> handlerDuplicateRecordException(DuplicateRecordException ex, WebRequest req) throws JsonProcessingException {
        // Log err
        log.error("handlerDuplicateRecordException", ex);
        SimpleAPIResponse err = new SimpleAPIResponse(HttpStatus.NOT_FOUND.value(), ex.getMessage(), null);

        return new ResponseEntity<>(err, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<?> handlerBadRequestException(BadRequestException ex, WebRequest req) throws JsonProcessingException {
        // Log err
        log.error("handlerBadRequestException", ex);
        SimpleAPIResponse err = new SimpleAPIResponse(HttpStatus.BAD_REQUEST.value(), ex.getMessage(), null);

        return new ResponseEntity<>(err, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(InternalServerException.class)
    public ResponseEntity<?> handlerInternalServerException(InternalServerException ex, WebRequest req) throws JsonProcessingException {
        // Log err
        log.error("handlerInternalServerException", ex);
        SimpleAPIResponse err = new SimpleAPIResponse(HttpStatus.NOT_FOUND.value(), ex.getMessage(), null);
        return new ResponseEntity<>(err, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // Xử lý tất cả các exception chưa được khai báo
//    @ExceptionHandler(Exception.class)
//    public ResponseEntity<?> handlerException(Exception ex, WebRequest req) throws JsonProcessingException {
//        // Log err
//        log.error("handlerException", ex);
//        SimpleAPIResponse err = new SimpleAPIResponse(HttpStatus.BAD_REQUEST.value(), ex.getMessage(), null);
//        return new ResponseEntity<>(err, HttpStatus.OK);
//    }

    @ExceptionHandler(BadDataInputExcelException.class)
    public ResponseEntity<?> handlerBadDataInputExcelException(BadDataInputExcelException ex, WebRequest req) throws JsonProcessingException {
        // Log err
        log.error("handlerBadDataInputExcelException", ex);
        SimpleAPIResponse err = new SimpleAPIResponse(HttpStatus.UNPROCESSABLE_ENTITY.value(), ex.getMessage(), null);

        return new ResponseEntity<>(err, HttpStatus.OK);
    }

    @ExceptionHandler(PermissionDeniedException.class)
    public ResponseEntity<?> handlerPermissionDeniedException(PermissionDeniedException ex, WebRequest req) throws JsonProcessingException {
        // Log err
        log.error("handlerBadDataInputExcelException", ex);
        SimpleAPIResponse err = new SimpleAPIResponse(HttpStatus.FORBIDDEN.value(), ex.getMessage(), null);

        return new ResponseEntity<>(err, HttpStatus.OK);
    }
}

