package nocsystem.indexmanager.constants;

import java.util.HashMap;
import java.util.List;

public class Constant {
    public final static int TONG_CTY = 1;
    public final static int VUNG = 2;
    public final static int CHI_NHANH = 3;
    public final static int DOI_TAC = 4;
    public final static int BUU_CUC = 5;

    public final static int HE_SO_HIEU_QUA = 100;
    public final static HashMap<String, List<String>> TTKT_RELATIONSHIP = new HashMap() {{
        put("TTKT1", List.of("TTKT1", "MBCG", "LOGLBN", "LOGYVN", "LOGBN", "LOGTTI"));
        put("TTKT2", List.of("TTKT2", "MTGG"));
        put("TTKT3", List.of("TTKT3", "MNMG", "LOGKHA", "TTTK3K2", "LOGTTO"));
        put("TTKT4", List.of("TTKT4", "MTNBG"));
        put("TTKT5", List.of("LOGHPG", "LOGNAN", "TTKT5"));
    }};
}