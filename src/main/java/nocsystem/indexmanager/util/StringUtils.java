package nocsystem.indexmanager.util;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class StringUtils {

    public static String list2StringQuery(List<String> list) {
        String version = "";

        if (!list.isEmpty()) {
            //chuyển list thành string để query
            version = list.stream()
                    .map(n -> String.valueOf(n))
                    .collect(Collectors.joining("','", "('", "')"));

        }

        return version;
    }

    public static String longTimestamp2DateString(String ngay) {
        if (ngay != null) {
            long ngays = Long.parseLong(ngay);
            Timestamp ts = new Timestamp(ngays);
            Date date = new Date(ts.getTime());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        }
        return null;
    }
}
