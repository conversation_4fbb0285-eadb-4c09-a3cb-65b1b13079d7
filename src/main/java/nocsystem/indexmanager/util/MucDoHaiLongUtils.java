package nocsystem.indexmanager.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

public class MucDoHaiLongUtils {

    private static final DecimalFormat df = new DecimalFormat("#.##");

    public static Double getTyLeHaiLong(Long tongDG, Long tongDGHaiLong) {
        Double tyLeHL;
        if (tongDGHaiLong > 0 && tongDG > 0) {
            tyLeHL = Double.parseDouble(df.format((double) tongDGHaiLong / tongDG * 100));
        } else {
            tyLeHL = (double) 0;
        }
        return tyLeHL;
    }

    public static Double getKPICurrentMonth(LocalDate ngayBaoCao) throws IOException {
        Map<String, Double> monthKPIMapList = collectiveKPIMonths();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMyyyy");
        String formattedDate = ngayBaoCao.format(formatter);
        if (monthKPIMapList.containsKey(formattedDate)) {
            return monthKPIMapList.get(formattedDate);
        }
        return null;
    }

    private static Map<String, Double> collectiveKPIMonths() throws IOException {
        Map<String, Double> map = new HashMap<>();
        String currentMonth = null;
        Double kpiCurrentMonth = (double) 0;
        //Get KPI muc do hai long hang thang
        InputStream is = MucDoHaiLongUtils.class.getClassLoader().getResourceAsStream("kpi-ty-le-hai-long.txt");
        if (is != null) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    currentMonth = line.substring(0, 6);
                    kpiCurrentMonth = Double.parseDouble(line.substring(7, line.length()).trim());
                    map.put(currentMonth, kpiCurrentMonth);
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                is.close();
            }
        }
        return map;
    }

    // Phương thức để lấy ngày cuối cùng của tháng 2
    public static int getLastDayOfFebruary(int year) {
        // Kiểm tra xem năm đó có phải là năm nhuận hay không
        boolean isLeapYear = LocalDate.ofYearDay(year, 1).isLeapYear();

        // Nếu năm là năm nhuận, ngày cuối cùng của tháng 2 là 29
        // Nếu không, ngày cuối cùng của tháng 2 là 28
        return isLeapYear ? 29 : 28;
    }

    public static LocalDate getSamePeriodPreviousMonth(LocalDate ngayBaoCao) {
        LocalDate previousMonthSameDay;
        LocalDate previousMonthDate = ngayBaoCao.minusMonths(1);
        int year = previousMonthDate.getYear();
        int month = previousMonthDate.getMonthValue();

        if (month == 0) {
            // Nếu tháng hiện tại là tháng 1, lấy ngày cùng kì của tháng 12 năm trước
            month = 12;
            year--;
        }
        if (month == 2 && !isLeapYear(year)) {
            // Kiểm tra xem tháng đó có phải là tháng 2 hay không
            boolean isFebruary = month == 2;
            if (ngayBaoCao.getDayOfMonth() > 28) {
                // Nếu tháng là tháng 2, lấy ngày cuối cùng của tháng đó dựa trên tính chất của năm nhuận
                int lastDayOfPreviousMonth = isFebruary ? getLastDayOfFebruary(year) : previousMonthDate.lengthOfMonth();
                // Xử lý cho trường hợp tháng 2 năm không nhuận
                previousMonthSameDay = LocalDate.of(year, month, lastDayOfPreviousMonth);
            } else {
                previousMonthSameDay = LocalDate.of(year, month, ngayBaoCao.getDayOfMonth());
            }

        } else if (month == 2 && isLeapYear(year)) {
            // Kiểm tra xem tháng đó có phải là tháng 2 hay không
            boolean isFebruary = month == 2;
            if (ngayBaoCao.getDayOfMonth() > 29) {
                // Nếu tháng là tháng 2, lấy ngày cuối cùng của tháng đó dựa trên tính chất của năm nhuận
                int lastDayOfPreviousMonth = isFebruary ? getLastDayOfFebruary(year) : previousMonthDate.lengthOfMonth();
                // Xử lý cho trường hợp tháng 2 năm nhuận
                previousMonthSameDay = LocalDate.of(year, month, lastDayOfPreviousMonth);
            } else {
                previousMonthSameDay = LocalDate.of(year, month, ngayBaoCao.getDayOfMonth());
            }
        } else {
            if (ngayBaoCao.getDayOfMonth() == 31) {
                YearMonth yearMonth = YearMonth.from(previousMonthDate);
                int lastDayOfPreviousMonth = yearMonth.atEndOfMonth().getDayOfMonth();
                previousMonthSameDay = LocalDate.of(year, month, lastDayOfPreviousMonth);
                System.out.println("previous " + previousMonthSameDay);
            } else {
                previousMonthSameDay = LocalDate.of(year, month, ngayBaoCao.getDayOfMonth());
            }
        }
        return previousMonthSameDay;
    }

    private static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }
}
