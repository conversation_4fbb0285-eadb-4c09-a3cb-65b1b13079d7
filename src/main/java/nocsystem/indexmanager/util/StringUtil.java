package nocsystem.indexmanager.util;

import org.apache.logging.log4j.util.Strings;

import java.util.List;

public class StringUtil {
    public static String castListToQueryParam(List<?> lst){
        String multiParam = "";
        for(int i = 0; i < lst.size(); i++){
            String cn = String.format("'%s'", lst.get(i));
            if( i != lst.size() - 1){
                cn += ",";
            }
            multiParam += cn;
        }

        return String.format(" (%s) ", multiParam);
    }

    public static String castListToQuery(List<?> lst){
        String multiParam = "";
        for(int i = 0; i < lst.size(); i++){
            String cn = String.format("%s", lst.get(i));
            if( i != lst.size() - 1){
                cn += ",";
            }
            multiParam += cn;
        }

        return String.format(" (%s) ", multiParam);
    }
}
