package nocsystem.indexmanager.util;

import java.util.Collections;
import java.util.Map;

public class TongQuanNguonLucUtils {
    public static Map<String, Double> weightsCap50_90 = Map.of(
            "w1", 0.18, "w2", 0.12, "w3", 0.30, "w4", 0.25, "w5", 0.10, "w6", 0.05
    );

    public static Map<String, Double> weightsCap10_30 = Map.of(
            "w1", 0.05, "w2", 0.15, "w3", 0.10, "w4", 0.22, "w5", 0.18, "w6", 0.30
    );

    public static Map<String, Double> getWeightMapByCap(int cap) {
        if (cap == 10 || cap == 30) {
            return weightsCap10_30;
        } else if (cap == 50 || cap == 55 || cap == 90) {
            return weightsCap50_90;
        }
        return Collections.emptyMap();
    }
}
