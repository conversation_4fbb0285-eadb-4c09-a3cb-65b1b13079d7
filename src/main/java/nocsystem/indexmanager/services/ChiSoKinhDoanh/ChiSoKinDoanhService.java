package nocsystem.indexmanager.services.ChiSoKinhDoanh;

import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPCNDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuSMSDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.CSKDTongDoanhThuOriginDto;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public interface ChiSoKinDoanhService {

    public CSKDTongDoanhThuOriginDto findCSKDTongDoanhThu(LocalDate to_time, String maChiNhanh, String maBuuCuc);

    public List<CSKDTienDoDoanhThuV2Dto> findCSKDTienDoDoanhThu(String maChiNhanh, String maBuuCuc, LocalDate to_time, Boolean detail);

    public CSKDTienDoDoanhThuSMSDto findCSKDTienDoDoanhThuSMS(String maChiNhanh, String maBuuCuc, LocalDate toTime, Boolean detail);

    List<CSKDTienDoDoanhThuSMSDto> findCSKDTienDoDoanhThuSMSCN(List<String> maChiNhanh, LocalDate toTime);

    public TienDoDoanhThuCNV1ResDto findDoanhThuLogistic(LocalDate to_time);

    public FindTotalTienDoDoanhThuCPCNDto findDoanhThuChuyenPhat(LocalDate to_time);

    public CSKDTienDoDoanhThuV2Dto findTongDoanhThuCuaChiNhanh(LocalDate to_time);

    public CSKDTienDoDoanhThuV2Dto findTongTienDoDTCN(
            String maChiNhanh, String loaiDichVu, String maBuuCuc, LocalDate to_time);



}
