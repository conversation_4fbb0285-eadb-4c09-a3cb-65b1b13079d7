package nocsystem.indexmanager.services.ThongKeLuyKe;

import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.ListDetailLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMNDto;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public interface TienDoLuyKeMienNamService {
    public CustomizeDataPage<TinhTongLuyKeChiNhanhMNDto> listDetailLuyKeMN(
            LocalDate to_time, String ma_chinhanh, String ma_buucuc, int pageIndex, int pageSize);

    public TinhTongLuyKeChiNhanhMNDto totalLuyKeCNBCMienNam(
            LocalDate toTime, String maChiNhanh, String buuCuc
    );
}
