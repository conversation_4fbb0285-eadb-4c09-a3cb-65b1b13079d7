package nocsystem.indexmanager.services.ThongKeLuyKe;

import net.bytebuddy.asm.Advice;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.*;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public interface TienDoLuyKeMienBacService {
    public CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> findListDetailLuyKeMB(
            LocalDate to_time, String ma_chinhanh, String ma_buucuc, int pageIndex, int pageSize);

    public TinhTongLuyKeChiNhanhMBDto totalLuyKeCNBCMienBac(
            LocalDate toTime, String maChiNhanh, String buuCuc
    );
}
