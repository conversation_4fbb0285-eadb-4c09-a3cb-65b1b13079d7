package nocsystem.indexmanager.services.ThongKeLuyKe;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeLogistic.TinhTongTienDoLuyKeLogisticDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMBDto;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public interface TienDoLuyKeLogisticService {
    public TinhTongTienDoLuyKeLogisticDto totalLuyKeCTLogistic(
            LocalDate toTime, String maChiNhanh
    );
}
