package nocsystem.indexmanager.services.ThongKeLuyKe;

import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TongTienDoLuyKeTCTCNResDto;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public interface TongTienDoluyKeTCTCNService {
    public List<TongTienDoLuyKeTCTCNResDto> findTongTienDoLuyKeTCTCN(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);
}
