package nocsystem.indexmanager.services.ThongKeLuyKe;

import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeCNHaNoiAndHCMResDto;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public interface TienDoluyKeCNHaNoiAndHCMService {
    public CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHaNoi(LocalDate ngayBaoCao, String buuCuc, int pageIndex, int pageSize);

    public CustomizeDataPage<TienDoluyKeCNHaNoiAndHCMResDto> findTienDoLuyKeCNHCM(LocalDate ngayBaoCao, String buuCuc,int pageIndex, int pageSize);

}
