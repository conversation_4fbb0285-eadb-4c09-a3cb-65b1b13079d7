package nocsystem.indexmanager.services.ThongKeLuyKe;

import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.ListDetailLuyKeChiNhanhMBDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac.TinhTongLuyKeChiNhanhMTDto;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public interface TienDoLuyKeMienTrungService {
    public CustomizeDataPage<ListDetailLuyKeChiNhanhMBDto> listDetailLuyKeMT(
            LocalDate to_time, String ma_chinhanh, String ma_buucuc, int pageIndex, int pageSize);

    public TinhTongLuyKeChiNhanhMTDto totalLuyKeCNBCMienTrung(
            LocalDate toTime, String maChiNhanh, String buuCuc
    );
}
