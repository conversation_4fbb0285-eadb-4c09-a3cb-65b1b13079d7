package nocsystem.indexmanager.services.ThongKeLuyKe;

import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TienDoluyKeTCTCNResDto;
import nocsystem.indexmanager.models.Response.ThongKeLuyKe.TongTienDoLuyKeTCTCNResDto;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public interface TongTienDoLuyKeCNHaNoiAndHCMService {

    public List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHaNoi(LocalDate ngayBaoCao, String chiNhanh, String maBuuCuc);

    public List<TienDoluyKeTCTCNResDto> findTongTienDoLuyKeCNHCM(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);

}
