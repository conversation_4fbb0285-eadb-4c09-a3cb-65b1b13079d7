package nocsystem.indexmanager.services.ChiTieuChatLuong;

import nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong.ChiTieuChatLuongDto;
import nocsystem.indexmanager.request.RequestToken;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;

@Service
public interface ThongKeChiTieuChatLuongService {
    public ChiTieuChatLuongDto thongKeChiTieuChatLuong(LocalDate ngayBaoCao) throws IOException;

    public ChiTieuChatLuongDto verifyAndGetInfo(String typeReport, String exportDate, String notiDate, String checkSum) throws Exception;
}
