package nocsystem.indexmanager.services.TienDoDoanhThuLogistic;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.BaseFunction;
import nocsystem.indexmanager.helper.FilterLoaiDichVu;
import nocsystem.indexmanager.models.Response.BieuDo.*;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.ParamTienDoDTLogisticDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDoanhThuLogisticV1Dto;
import nocsystem.indexmanager.repositories.ChiSoKinhDoanhRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuLogisticBuuCucRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuLogisticChiNhanhRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuLogisticRepository;
import nocsystem.indexmanager.services.ChiSoKinhDoanh.ChiSoKinDoanhService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class TienDoDoanhThuLogisticService {
    private final TienDoDoanhThuLogisticRepository tienDoDTLogisticRepo;
    private final ChiSoKinhDoanhRepository chiSoKinhDoanhRepo;

    private final TienDoDoanhThuLogisticChiNhanhRepository tienDoDTLogisticCNRepo;

    private final TienDoDoanhThuLogisticBuuCucRepository tienDoDTLogisticBCRepo;

    private FilterLoaiDichVu filterLoaiDichVu;

    @Autowired
    private ChiSoKinDoanhService chiSoKinDoanhService;

    @Autowired
    private ParamTienDoDTLogisticDto paramTienDoDTLogisticDto;

    private BaseFunction baseFunction;

    public TienDoDoanhThuLogisticService(
            TienDoDoanhThuLogisticRepository tienDoDTLogisticRepo,
            ChiSoKinhDoanhRepository chiSoKinhDoanhRepo,
            TienDoDoanhThuLogisticChiNhanhRepository tienDoDTLogisticCNRepo,
            TienDoDoanhThuLogisticBuuCucRepository tienDoDTLogisticBCRepo) {
        this.tienDoDTLogisticRepo = tienDoDTLogisticRepo;
        this.chiSoKinhDoanhRepo = chiSoKinhDoanhRepo;
        this.tienDoDTLogisticCNRepo = tienDoDTLogisticCNRepo;
        this.tienDoDTLogisticBCRepo = tienDoDTLogisticBCRepo;
        this.filterLoaiDichVu = new FilterLoaiDichVu();
        this.baseFunction = new BaseFunction();
    }

    public List<TienDoDoanhThuLogisticV1Dto> findDoanhThuLogistic(String maChiNhanh, String maBuuCuc, LocalDate toTime, Boolean detail, Integer loaiDichVu) {
//        if (ListVariableLocation.isAdmin.equals("false")) {
//            return null;
//        }

//        if (detail == true) {
//            return this.findTotalTienDoDoanhThuOfAllBranch(maChiNhanh, maBuuCuc, toTime, loaiDichVu);
//        }

        if (!maChiNhanh.isEmpty() && maBuuCuc.isEmpty()) {
            return this.tienDoDoanhThuChiNhanh(maChiNhanh, toTime, maBuuCuc);
        } else if (!maChiNhanh.isEmpty() && !maBuuCuc.isEmpty()) {
            return this.tienDoDoanhThuBuuCuc(maChiNhanh, maBuuCuc, toTime);
        } else {
            if (!UserContext.getUserData().getIsAdmin().equals("true")) {
                return this.tienDoDoanhThuChiNhanh(maChiNhanh, toTime, maBuuCuc);
            }
            return this.tienDoDoanhThuTong(toTime);
        }

//        if (maBuuCuc.isEmpty()) {
//            return this.tienDoDoanhThuChiNhanh(maChiNhanh, toTime);
//        } else if (!maChiNhanh.isEmpty() && !maBuuCuc.isEmpty()) {
//            return this.tienDoDoanhThuBuuCuc(maChiNhanh, maBuuCuc, toTime);
//        }
//
//        return null;
    }

    private List<TienDoDoanhThuLogisticV1Dto> findTotalTienDoDoanhThuOfAllBranch(
            String maChiNhanh, String maBuuCuc, LocalDate toTime, Integer loaiDichVu) {

        String loaiDichVuCV = this.filterLoaiDichVu.loaiDichVuLogistic(loaiDichVu);
        List<TienDoDoanhThuLogisticV1Dto> tongDoanhThu;
        if (maBuuCuc.equals("") && maChiNhanh.equals("")) {
            tongDoanhThu = this.findCSKDTienDoDTLogisticTheoCNDetail("", toTime, loaiDichVuCV);
        } else if (!maChiNhanh.equals("") && maBuuCuc.equals("")) {
            tongDoanhThu = this.findCSKDTienDoDTLogisticTheoCNDetail(maChiNhanh, toTime, loaiDichVuCV);
        } else {
            tongDoanhThu = this.findCSKDTienDoDoanhThuTheoBCDetail(maChiNhanh, maBuuCuc, toTime);
        }

        return tongDoanhThu;
    }

    private List<TienDoDoanhThuLogisticV1Dto> findCSKDTienDoDTLogisticTheoCNDetail(String maChiNhanh, LocalDate toTime, String loaiDichVuCV) {
        List<TienDoDoanhThuLogisticOverViewDto> tienDDTLogisticCNOverView = new ArrayList<>();

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tienDDTLogisticCNOverView =
                        tienDoDTLogisticBCRepo.findTienDoDoanhThuChiNhanhDetailV2(maChiNhanh, toTime, UserContext.getUserData().getListChiNhanhVeriable(), loaiDichVuCV);
            } else {
//                tienDDTLogisticCNOverView =
//                        tienDoDTLogisticBCRepo.findTienDoDoanhThuBuuCucOverViewV2(maChiNhanh, toTime, ListVariableLocation.listChiNhanhVeriable, loaiDichVuCV);
            }
        } else {
            tienDDTLogisticCNOverView =
                    tienDoDTLogisticBCRepo.findTienDoDoanhThuChiNhanhDetail(maChiNhanh, toTime, loaiDichVuCV);
        }

        List<TienDoDoanhThuLogisticOverViewDto> tienDoDTKhoVanList = new ArrayList<>();
        List<TienDoDoanhThuLogisticOverViewDto> tienDoDTForwardingList = new ArrayList<>();
        TienDoDoanhThuLogisticV1Dto tongTienDoDTKhoVan = new TienDoDoanhThuLogisticV1Dto();
        TienDoDoanhThuLogisticV1Dto tongTienDoDTForwarding = new TienDoDoanhThuLogisticV1Dto();

        for (TienDoDoanhThuLogisticOverViewDto tienDDTLogisticCNOverViewDt : tienDDTLogisticCNOverView) {
            if (tienDDTLogisticCNOverViewDt.getDichVu().equals("KHO-VAN")) {
                tienDoDTKhoVanList.add(tienDDTLogisticCNOverViewDt);
            }
            if (tienDDTLogisticCNOverViewDt.getDichVu().equals("FORWARDING")) {
                tienDoDTForwardingList.add(tienDDTLogisticCNOverViewDt);
            }
        }

        ParamTienDoDTLogisticDto khoVan = new ParamTienDoDTLogisticDto();
        khoVan.setTienDoDoanhThuLogisticCNModel(tienDoDTKhoVanList);
        khoVan.setMaChiNhanh(maChiNhanh);
        List<TienDoDoanhThuLogisticOverViewDto> tienDoDoanhThuCNModelConvert = new ArrayList<>();
        tongTienDoDTKhoVan = this.tinhTongDoanhThuLogistic(khoVan);

        ParamTienDoDTLogisticDto forwarding = new ParamTienDoDTLogisticDto();
        forwarding.setMaChiNhanh(maChiNhanh);
        forwarding.setTienDoDoanhThuLogisticCNModel(tienDoDTForwardingList);
        tongTienDoDTForwarding = this.tinhTongDoanhThuLogistic(forwarding);
        tongTienDoDTKhoVan.setDichVu("KHO-VAN");
        tongTienDoDTForwarding.setDichVu("FORWARDING");

        tienDoDoanhThuCNModelConvert.add(this.convertTongToChiTietDTLogisticModel(tongTienDoDTKhoVan));
        tienDoDoanhThuCNModelConvert.add(this.convertTongToChiTietDTLogisticModel(tongTienDoDTForwarding));
        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuCNModelConvert);

        ParamTienDoDTLogisticDto totalForwardingLogistic = new ParamTienDoDTLogisticDto();
        totalForwardingLogistic.setMaChiNhanh(maChiNhanh);
        totalForwardingLogistic.setTienDoDoanhThuLogisticCNModel(tienDoDoanhThuCNModelConvert);
        TienDoDoanhThuLogisticV1Dto tongDoanhThu = this.tinhTongDoanhThuLogistic(totalForwardingLogistic);

        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDDTLogisticCNOverView);
    }

    private TienDoDoanhThuLogisticOverViewDto convertTongToChiTietDTLogisticModel(TienDoDoanhThuLogisticV1Dto tongTienDoDTKhoVan) {
        TienDoDoanhThuLogisticOverViewDto convertTDDTLogistic = new TienDoDoanhThuLogisticOverViewDto();
        convertTDDTLogistic.setTienDo(tongTienDoDTKhoVan.getTienDo());
        convertTDDTLogistic.setDichVu(tongTienDoDTKhoVan.getDichVu());
        convertTDDTLogistic.setKeHoach(tongTienDoDTKhoVan.getKeHoach());
        convertTDDTLogistic.setThucHien(tongTienDoDTKhoVan.getThucHien());
        convertTDDTLogistic.setTlHoanThanh(tongTienDoDTKhoVan.getTlHoanThanh());
        convertTDDTLogistic.setTtThang(tongTienDoDTKhoVan.getTtThang());
        convertTDDTLogistic.setTtTbnThang(tongTienDoDTKhoVan.getTtTbnThang());
        convertTDDTLogistic.setTtNam(tongTienDoDTKhoVan.getTtNam());
        convertTDDTLogistic.setCungKyNgay(tongTienDoDTKhoVan.getNgayCungKy());
        convertTDDTLogistic.setCungKyThang(tongTienDoDTKhoVan.getThangCungKy());
        convertTDDTLogistic.setCungKyNam(tongTienDoDTKhoVan.getNamCungKy());
        convertTDDTLogistic.setThangTruoc(tongTienDoDTKhoVan.getThangTruoc());
        convertTDDTLogistic.setNamTruoc(tongTienDoDTKhoVan.getNamTruoc());

        return convertTDDTLogistic;
    }

    private List<TienDoDoanhThuLogisticV1Dto> findCSKDTienDoDoanhThuTheoBCDetail(String maChiNhanh, String maBuuCuc, LocalDate toTime) {
        List<TienDoDoanhThuLogisticOverViewDto> tienDoDoanhThuBCModel = new ArrayList<>();
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            tienDoDoanhThuBCModel =
                    tienDoDTLogisticBCRepo.findListTienDoDoanhThuBuuCucOverViewV2(maChiNhanh, maBuuCuc, "", toTime, UserContext.getUserData().getListBuuCucVeriable());
        } else {
            tienDoDoanhThuBCModel =
                    tienDoDTLogisticBCRepo.findListTienDoDoanhThuBuuCucOverView(maChiNhanh, maBuuCuc, "", toTime);
        }

        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThu = this.convertModelToDomain(tienDoDoanhThuBCModel);

        ParamTienDoDTLogisticDto totalForwardingLogistic = new ParamTienDoDTLogisticDto();
        totalForwardingLogistic.setTienDoDoanhThuLogisticCNModel(tienDoDoanhThuBCModel);
        totalForwardingLogistic.setMaChiNhanh(maChiNhanh);

        TienDoDoanhThuLogisticV1Dto tongDoanhThu = this.tinhTongDoanhThuLogistic(totalForwardingLogistic);
        return this.processResponseTienDoDoanhThu(tienDoDoanhThu, tongDoanhThu, tienDoDoanhThuBCModel);
    }

    private List<TienDoDoanhThuLogisticV1Dto> convertModelToDomain(List<TienDoDoanhThuLogisticOverViewDto> tienDoDoanhThuModel) {
        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThu = new ArrayList<>();
        for (TienDoDoanhThuLogisticOverViewDto tienDoDoanhThuDt : tienDoDoanhThuModel) {
            tienDoDoanhThu.add(this.mapModelToDomainOverView(tienDoDoanhThuDt));
        }

        return tienDoDoanhThu;
    }

    private List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuTong(
            LocalDate toTime
    ) {
        List<TienDoDoanhThuLogisticOverViewDto> tienDoDoanhThuLogisticModel = tienDoDTLogisticRepo.findDoanhThuLogistic(toTime);
        /* Tiến độ doanh thu chi tiết Kho-Vận, Forwarding, TM-UT */
        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuLogistic =
                this.mapModelToDomainTienDoDTLogistic(tienDoDoanhThuLogisticModel);

        for (TienDoDoanhThuLogisticV1Dto tienDoDoanhThuLogisticBCDt : tienDoDoanhThuLogistic) {
            if (tienDoDoanhThuLogisticBCDt.getKeHoach() == 0) {
                tienDoDoanhThuLogisticBCDt.setKeHoach(null);
            }
        }

        /* Lấy ra số tổng doanh thu của ngành chuyển phát bưu cục */
        ParamTienDoDTLogisticDto totalForwardingLogistic = new ParamTienDoDTLogisticDto();
        totalForwardingLogistic.setTienDoDoanhThuLogisticCNModel(tienDoDoanhThuLogisticModel);
        totalForwardingLogistic.setMaChiNhanh("");
        totalForwardingLogistic.setCalculateDashBoard(true);

        TienDoDoanhThuLogisticV1Dto tongTienDoDTLogistic = this.tinhTongDoanhThuLogistic(totalForwardingLogistic);
        return this.processResponseTienDoDoanhThu(tienDoDoanhThuLogistic, tongTienDoDTLogistic, tienDoDoanhThuLogisticModel);
    }

    private List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuChiNhanh(
            String maChiNhanh,
            LocalDate toTime,
            String maBuuCuc
    ) {
        List<TienDoDoanhThuLogisticOverViewDto> tienDDTLogisticCNOverView = new ArrayList<>();
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tienDDTLogisticCNOverView =
                        tienDoDTLogisticCNRepo.findTienDoDoanhThuChiNhanhOverViewV2(maChiNhanh, toTime, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tienDDTLogisticCNOverView =
                        tienDoDTLogisticBCRepo.findTienDoDoanhThuBuuCucOverViewV2(maChiNhanh, maBuuCuc, toTime, UserContext.getUserData().getListBuuCucVeriable());
            }
        } else {
            tienDDTLogisticCNOverView =
                    tienDoDTLogisticCNRepo.findTienDoDoanhThuChiNhanhOverView(maChiNhanh, toTime);
        }

        if (tienDDTLogisticCNOverView.size() == 0) {
            List<TienDoDoanhThuLogisticV1Dto> resultDefault = new ArrayList<>();
            return resultDefault;
        }

        /* Tiến độ doanh thu chi tiết Kho-Vận, Forwarding, TM-UT */
        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuLogisticCN =
                this.mapModelToDomainTienDoDTLogistic(tienDDTLogisticCNOverView);

        /* Lấy ra số tổng doanh thu của ngành chuyển phát chi nhánh */
        ParamTienDoDTLogisticDto totalForwardingLogistic = new ParamTienDoDTLogisticDto();
        totalForwardingLogistic.setTienDoDoanhThuLogisticCNModel(tienDDTLogisticCNOverView);
        totalForwardingLogistic.setMaChiNhanh(maChiNhanh);
        totalForwardingLogistic.setCalculateDashBoard(true);

        TienDoDoanhThuLogisticV1Dto tongTienDoDTLogisticCN =
                this.tinhTongDoanhThuLogistic(totalForwardingLogistic);

        for (TienDoDoanhThuLogisticV1Dto tienDoDoanhThuLogisticCNDt : tienDoDoanhThuLogisticCN) {
            if (tienDoDoanhThuLogisticCNDt.getKeHoach() == 0) {
                tienDoDoanhThuLogisticCNDt.setKeHoach(null);
            }
        }

        return this.processResponseTienDoDoanhThu(tienDoDoanhThuLogisticCN, tongTienDoDTLogisticCN, tienDDTLogisticCNOverView);
    }

    private List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuBuuCuc(
            String maChiNhanh,
            String maBuuCuc,
            LocalDate toTime
    ) {
        List<TienDoDoanhThuLogisticOverViewDto> tienDoDTLogisticOV =
                tienDoDTLogisticBCRepo.findTienDoDoanhThuBuuCucOverView(maChiNhanh, maBuuCuc, toTime);

        /* Tiến độ doanh thu chi tiết Kho-Vận, Forwarding, TM-UT */
        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuLogisticBC =
                this.mapModelToDomainTienDoDTLogistic(tienDoDTLogisticOV);

        for (TienDoDoanhThuLogisticV1Dto tienDoDoanhThuLogisticBCDt : tienDoDoanhThuLogisticBC) {
            if (tienDoDoanhThuLogisticBCDt.getKeHoach() == 0) {
                tienDoDoanhThuLogisticBCDt.setKeHoach(null);
            }
        }

        /* Lấy ra số tổng doanh thu của ngành chuyển phát bưu cục */
        ParamTienDoDTLogisticDto totalForwardingLogistic = new ParamTienDoDTLogisticDto();
        totalForwardingLogistic.setTienDoDoanhThuLogisticCNModel(tienDoDTLogisticOV);
        totalForwardingLogistic.setMaChiNhanh(maChiNhanh);
        totalForwardingLogistic.setCalculateDashBoard(true);

        TienDoDoanhThuLogisticV1Dto tongTienDoDTLogisticBC = this.tinhTongDoanhThuLogistic(totalForwardingLogistic);

        return this.processResponseTienDoDoanhThu(tienDoDoanhThuLogisticBC, tongTienDoDTLogisticBC, tienDoDTLogisticOV);
    }

    private List<TienDoDoanhThuLogisticV1Dto> processResponseTienDoDoanhThu(
            List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuLogistic,
            TienDoDoanhThuLogisticV1Dto tienDoDoanhThu,
            List<TienDoDoanhThuLogisticOverViewDto> tienDDTLogisticOverView
    ) {
        List<TienDoDoanhThuLogisticV1Dto> result = new ArrayList<>();
        TienDoDoanhThuLogisticV1Dto forwarding = new TienDoDoanhThuLogisticV1Dto();
        TienDoDoanhThuLogisticV1Dto khoVan = new TienDoDoanhThuLogisticV1Dto();

        List<TienDoDoanhThuLogisticOverViewDto> listForwarding = new ArrayList<>();
        List<TienDoDoanhThuLogisticOverViewDto> listKhoVan = new ArrayList<>();

        for (TienDoDoanhThuLogisticOverViewDto tienDDTLogisticDt : tienDDTLogisticOverView
        ) {
            if (tienDDTLogisticDt.getDichVu().equals("FORWARDING")) {
                listForwarding.add(tienDDTLogisticDt);
            } else if (tienDDTLogisticDt.getDichVu().equals("KHO-VAN")) {
                listKhoVan.add(tienDDTLogisticDt);
            }
        }

        if (!(tienDoDoanhThu != null && (tienDoDoanhThuLogistic != null && !tienDoDoanhThuLogistic.isEmpty()))) {
            tienDoDoanhThu = new TienDoDoanhThuLogisticV1Dto();
        }

        ParamTienDoDTLogisticDto paramListForwarding = new ParamTienDoDTLogisticDto();
        paramListForwarding.setTienDoDoanhThuLogisticCNModel(listForwarding);
        paramListForwarding.setMaChiNhanh("");
        forwarding = tinhTongDoanhThuLogistic(paramListForwarding);

        ParamTienDoDTLogisticDto paramListKhoVan = new ParamTienDoDTLogisticDto();
        paramListKhoVan.setTienDoDoanhThuLogisticCNModel(listKhoVan);
        paramListKhoVan.setMaChiNhanh("");
        khoVan = tinhTongDoanhThuLogistic(paramListKhoVan);

        forwarding.setDichVu("Forwarding");
        khoVan.setDichVu("Kho vận");

        tienDoDoanhThu.setDichVu("Tổng");
        result.add(forwarding);
        result.add(khoVan);
        result.add(tienDoDoanhThu);

        return result;

//        for (TienDoDoanhThuLogisticV1Dto tienDoDoanhThuDt : tienDoDoanhThuLogistic
//        ) {
//            if (tienDoDoanhThuDt.getDichVu().equals("FORWARDING")) {
//                forwarding = tienDoDoanhThuDt;
//                forwarding.setDichVu("Forwarding");
//            } else if (tienDoDoanhThuDt.getDichVu().equals("KHO-VAN")) {
//                khoVan = tienDoDoanhThuDt;
//                khoVan.setDichVu("Kho vận");
//            }
//        }
//
//        if (!(tienDoDoanhThu != null && (tienDoDoanhThuLogistic != null && !tienDoDoanhThuLogistic.isEmpty()))) {
//            tienDoDoanhThu = new TienDoDoanhThuLogisticV1Dto();
//        }
//
//        tienDoDoanhThu.setDichVu("Tổng");
//        result.add(forwarding);
//        result.add(khoVan);
//        result.add(tienDoDoanhThu);
//
//        return result;
    }

    private List<TienDoDoanhThuLogisticV1Dto> mapModelToDomainTienDoDTLogistic(
            List<TienDoDoanhThuLogisticOverViewDto> tDDTLogisticOverViewDto) {

        List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuLogisticV1Dtos = new ArrayList<>();

        for (TienDoDoanhThuLogisticOverViewDto tienDTLogistic : tDDTLogisticOverViewDto) {
            tienDoDoanhThuLogisticV1Dtos.add(this.mapModelToDomainOverView(tienDTLogistic));
        }
        return tienDoDoanhThuLogisticV1Dtos;
    }

    private TienDoDoanhThuLogisticV1Dto mapModelToDomainOverView(TienDoDoanhThuLogisticOverViewDto tienDoDoanhThuModel) {
        TienDoDoanhThuLogisticV1Dto tienDoDoanhThuCP = new TienDoDoanhThuLogisticV1Dto();
        tienDoDoanhThuCP.setDichVu(tienDoDoanhThuModel.getDichVu());
        tienDoDoanhThuCP.setTlHoanThanh(tienDoDoanhThuModel.getTlHoanThanh());
        tienDoDoanhThuCP.setTienDo(tienDoDoanhThuModel.getTienDo());
        tienDoDoanhThuCP.setTtThang(tienDoDoanhThuModel.getTtThang());
        tienDoDoanhThuCP.setTtTbnThang(tienDoDoanhThuModel.getTtTbnThang());
        tienDoDoanhThuCP.setTtNam(tienDoDoanhThuModel.getTtNam());
        tienDoDoanhThuCP.setKeHoach(tienDoDoanhThuModel.getKeHoach());
        tienDoDoanhThuCP.setThucHien(tienDoDoanhThuModel.getThucHien());
        tienDoDoanhThuCP.setThangTruoc(tienDoDoanhThuModel.getThangTruoc());
        tienDoDoanhThuCP.setNamTruoc(tienDoDoanhThuModel.getNamTruoc());
        tienDoDoanhThuCP.setNgayCungKy(tienDoDoanhThuModel.getCungKyNgay());
        tienDoDoanhThuCP.setThangCungKy(tienDoDoanhThuModel.getCungKyThang());
        tienDoDoanhThuCP.setNamCungKy(tienDoDoanhThuModel.getCungKyNam());
        return tienDoDoanhThuCP;
    }

    /**
     * Hàm tính tổng doanh thu Logistic
     *
     * @param paramLogistic
     * @return
     */
    private TienDoDoanhThuLogisticV1Dto tinhTongDoanhThuLogistic(
            ParamTienDoDTLogisticDto paramLogistic
    ) {
        Float thucHien = Float.valueOf(0);
        Float keHoach = Float.valueOf(0);
        Float ngayCungKy = Float.valueOf(0);
        Float thucHienCungKyThangTruoc = Float.valueOf(0);
        Float thangCungKy = Float.valueOf(0);
        Float tongTHTBNCungKyThangTruoc = Float.valueOf(0);
        Float thucHienCungKyNamTruoc = Float.valueOf(0);
        Float tongTBNThang = Float.valueOf(0);
        Float tienDo = Float.valueOf(0);
        int flag = 0;
        HashMap<String, Float> detailTienDoDT = new HashMap<>();
        List<TienDoDoanhThuLogisticOverViewDto> tienDoDoanhThuLogisticCNModel = paramLogistic.getTienDoDoanhThuLogisticCNModel();
        String maChiNhanh = paramLogistic.getMaChiNhanh();
        Boolean calculateDashBoard = paramLogistic.getCalculateDashBoard();
        Set<Float> keHoachSet = new HashSet<>();

        /* Mặc định calculateDashBoard == true nếu tính cho màn tổng, nếu false tính cho màn chi tiết */
        for (TienDoDoanhThuLogisticOverViewDto tienDoDoanhThuDt : tienDoDoanhThuLogisticCNModel) {
//            if (tienDoDoanhThuDt.getKeHoach() != null && !calculateDashBoard) {
//                keHoach += tienDoDoanhThuDt.getKeHoach();
//            }

//            if (calculateDashBoard && (tienDoDoanhThuDt.getKeHoach() == 0 || tienDoDoanhThuDt.getKeHoach() == null)) {
//                flag = 1;
//            } else {
//                if (tienDoDoanhThuDt.getKeHoach() != null) {
//                    keHoach += tienDoDoanhThuDt.getKeHoach();
//                }
//            }

            if (tienDoDoanhThuDt.getKeHoach() != null) {
                keHoach += tienDoDoanhThuDt.getKeHoach();
                keHoachSet.add(tienDoDoanhThuDt.getKeHoach());
            }


            if (tienDoDoanhThuDt.getThucHien() != null) {
                thucHien += tienDoDoanhThuDt.getThucHien();
            }
            if (tienDoDoanhThuDt.getThangTruoc() != null) {
                thucHienCungKyThangTruoc += tienDoDoanhThuDt.getThangTruoc();
            }
            if (tienDoDoanhThuDt.getNamTruoc() != null) {
                thucHienCungKyNamTruoc += tienDoDoanhThuDt.getNamTruoc();
            }
            ngayCungKy = tienDoDoanhThuDt.getCungKyNgay();
            thangCungKy = tienDoDoanhThuDt.getCungKyThang();
            if (!maChiNhanh.isEmpty() && tienDoDoanhThuDt.getKeHoach() != null) {
                detailTienDoDT.put(tienDoDoanhThuDt.getDichVu(), tienDoDoanhThuDt.getKeHoach());
            }

            if (tienDoDoanhThuDt.getTienDo() != null) {
                tienDo += tienDoDoanhThuDt.getTienDo();
            }
        }
        TienDoDoanhThuLogisticV1Dto tongDoanhThu = new TienDoDoanhThuLogisticV1Dto();
        Float sumAllKeHoach = keHoachSet.stream().reduce((float) 0, Float::sum);
        keHoach = sumAllKeHoach;

        /* Khi search theo bưu cục thì ke hoach sẽ lấy bằng kế hoạch của chi nhánh */
        if (!maChiNhanh.isEmpty() && !detailTienDoDT.isEmpty()) {
            keHoach = detailTienDoDT.values().stream().reduce((float) 0, Float::sum);
        }

//        if (keHoach == 0 || keHoach == null || thucHien == 0 || thucHien == null) {
//            return tongDoanhThu;
//        }

        /* Add thực hiện */
        tongDoanhThu.setThucHien(thucHien);

        /* Add kế hoạch */
        tongDoanhThu.setKeHoach(keHoach);

        /* Tính Tổng Tỉ Lệ Hoàn Thành */
        if (keHoach != 0 && flag == 0) {
            tongDoanhThu.setTlHoanThanh(100 * (thucHien / keHoach));
        }

        /* Tính tiến độ
         * Nếu dữ liệu keHoach = 0 thì tien_do = 0
         * */
        if (!thucHien.isNaN() && !keHoach.isNaN()) {
//            tongDoanhThu.setTienDo(thucHien - keHoach);
            tongDoanhThu.setTienDo(tienDo);
        }

        /* Tính tăng trưởng tháng */
        if (thucHienCungKyThangTruoc != 0) {
            tongDoanhThu.setTtThang(100 * (thucHien - thucHienCungKyThangTruoc) / thucHienCungKyThangTruoc);
        }

        /* Tính TTTBN tháng */
        /* Tính tổng thực hiện TBN tháng này */
        if (ngayCungKy != 0) {
            tongTBNThang = thucHien / ngayCungKy;
        }

        /* Tính tổng thực hiện cùng kỳ tháng trước */
        if (thangCungKy != 0) {
            tongTHTBNCungKyThangTruoc = thucHienCungKyThangTruoc / thangCungKy;
        }

        /*
         * Tổng TT TBN Tháng
         * */
        if (tongTHTBNCungKyThangTruoc != 0) {
            tongDoanhThu.setTtTbnThang(
                    100 * (tongTBNThang - tongTHTBNCungKyThangTruoc) / tongTHTBNCungKyThangTruoc);
        }

        /* Tính TT Năm */
        if (thucHienCungKyNamTruoc != 0) {
            tongDoanhThu.setTtNam(100 * (thucHien - thucHienCungKyNamTruoc) / thucHienCungKyNamTruoc);
        }

        tongDoanhThu.setThangTruoc(thucHienCungKyThangTruoc);
        tongDoanhThu.setNamTruoc(thucHienCungKyNamTruoc);
        tongDoanhThu.setNgayCungKy(ngayCungKy);
        tongDoanhThu.setThangCungKy(thangCungKy);
        return tongDoanhThu;
    }

    /**
     * @param maChiNhanh
     * @param maBuuCuc
     * @param loaiDichVu
     * @param toTime
     * @return
     */
    public TienDoDoanhThuCNV1ResDto tongTienDoDoanhThuLogisticDetailScreen(
            String maChiNhanh, String maBuuCuc, Integer loaiDichVu, LocalDate toTime, Boolean detail) {
        TienDoDoanhThuLogisticV1Dto tongTDDTLogistic = new TienDoDoanhThuLogisticV1Dto();
        List<TienDoDoanhThuLogisticOverViewDto> listTienDoDTlogistic = new ArrayList<>();
        TienDoDoanhThuCNV1ResDto tongDTLogistic = new TienDoDoanhThuCNV1ResDto();
        String loaiDichVuCV = filterLoaiDichVu.loaiDichVuLogistic(loaiDichVu);

        if (!maBuuCuc.isEmpty()) {
            listTienDoDTlogistic = this.findListTienDoDoanhThuBuuCuc(maChiNhanh, maBuuCuc, loaiDichVuCV, toTime);
        } else {
            listTienDoDTlogistic = this.findListTienDoDoanhThuChiNhanh(maChiNhanh, maBuuCuc, loaiDichVuCV, toTime);
        }

        ParamTienDoDTLogisticDto totalForwardingLogistic = new ParamTienDoDTLogisticDto();
        totalForwardingLogistic.setTienDoDoanhThuLogisticCNModel(listTienDoDTlogistic);
        totalForwardingLogistic.setMaChiNhanh(maChiNhanh);
//        if (detail == true) {
//            totalForwardingLogistic.setCalculateDashBoard(false);
//        }

        tongTDDTLogistic = this.tinhTongDoanhThuLogistic(totalForwardingLogistic);
        tongDTLogistic.setMaChiNhanh("Total");
        tongDTLogistic.setNhomDoanhThu(loaiDichVuCV);
        tongDTLogistic.setThucHien(tongTDDTLogistic.getThucHien());
        tongDTLogistic.setKeHoach(tongTDDTLogistic.getKeHoach());
        tongDTLogistic.setTlht(tongTDDTLogistic.getTlHoanThanh());
        tongDTLogistic.setTienDo(tongTDDTLogistic.getTienDo());
        tongDTLogistic.setTtThang(tongTDDTLogistic.getTtThang());
        tongDTLogistic.setTtTbnThang(tongTDDTLogistic.getTtTbnThang());
        tongDTLogistic.setTtNam(tongTDDTLogistic.getTtNam());

        return tongDTLogistic;
    }

    public List<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuBuuCuc(
            String maChiNhanh, String maBuuCuc, String loaiDichVuCV, LocalDate toTime) {

        List<TienDoDoanhThuLogisticOverViewDto> listTDDoanhThuBC = new ArrayList<>();
        int nextIndex = 0;

        while (true) {
            Pageable paging = PageRequest.of(nextIndex, 100);
            Slice<TienDoDoanhThuLogisticOverViewDto> pagedResult = tienDoDTLogisticBCRepo.findListTienDoDoanhThuBuuCuc(
                    maChiNhanh, maBuuCuc, loaiDichVuCV, toTime, paging);
            List<TienDoDoanhThuLogisticOverViewDto> tongDTElement = pagedResult.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listTDDoanhThuBC.addAll(tongDTElement);
            nextIndex++;
        }

        return listTDDoanhThuBC;
    }

    public List<TienDoDoanhThuLogisticOverViewDto> findListTienDoDoanhThuChiNhanh(
            String maChiNhanh, String maBuuCuc, String loaiDichVuCV, LocalDate toTime) {

        List<TienDoDoanhThuLogisticOverViewDto> listTDDoanhThuCN = new ArrayList<>();
        int nextIndex = 0;

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            while (true) {
                Pageable paging = PageRequest.of(nextIndex, 100);
                List<TienDoDoanhThuLogisticOverViewDto> tongDTElement = new ArrayList<>();
                if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                    Slice<TienDoDoanhThuLogisticOverViewDto> pagedResult = tienDoDTLogisticCNRepo.findListTienDoDoanhThuChiNhanhV2(
                            maChiNhanh, loaiDichVuCV, toTime, paging, UserContext.getUserData().getListChiNhanhVeriable());
                    tongDTElement = pagedResult.getContent();
                } else {
                    Slice<TienDoDoanhThuLogisticOverViewDto> pagedResult = tienDoDTLogisticBCRepo.findListTienDoDoanhThuBuuCucV2(
                            maChiNhanh, maBuuCuc, loaiDichVuCV, toTime, paging, UserContext.getUserData().getListBuuCucVeriable());
                    tongDTElement = pagedResult.getContent();
                }

                if (tongDTElement.isEmpty()) {
                    break;
                }

                listTDDoanhThuCN.addAll(tongDTElement);
                nextIndex++;
            }

            return listTDDoanhThuCN;
        }

        while (true) {
            Pageable paging = PageRequest.of(nextIndex, 100);
            Slice<TienDoDoanhThuLogisticOverViewDto> pagedResult = tienDoDTLogisticCNRepo.findListTienDoDoanhThuChiNhanh(
                    maChiNhanh, loaiDichVuCV, toTime, paging);
            List<TienDoDoanhThuLogisticOverViewDto> tongDTElement = pagedResult.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listTDDoanhThuCN.addAll(tongDTElement);
            nextIndex++;
        }

        return listTDDoanhThuCN;
    }

    /**
     * Tính tổng tiến độ doanh thu Logistic màn chi tiết
     *
     * @param maChiNhanh
     * @param maBuuCuc
     * @param loaiDichVu
     * @param toTime
     * @return
     */
    public SimpleAPIResponse tinhTongTienDoDoanhThuLogistic(
            String maChiNhanh, String maBuuCuc, Integer loaiDichVu, LocalDate toTime, Boolean detail
    ) {
        TienDoDoanhThuCNV1ResDto tienDoDoanhThuLogistic =
                this.tongTienDoDoanhThuLogisticDetailScreen(maChiNhanh, maBuuCuc, loaiDichVu, toTime, detail);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDoanhThuLogistic);
        return simpleAPIResponse;
    }

    public SimpleAPIResponse bieuDoTtTienDoDoanhThu(String maChiNhanh, String maBuuCuc, LocalDate ngayKetThuc) {
        List<BieuDoTtTienDoDoanhThuLogisticJPADto> tienDoDTCPCN = getDataBieuDoTienDoDTLogistic(maChiNhanh, maBuuCuc, ngayKetThuc);
        int checkingDay = ngayKetThuc.getDayOfMonth();
        List<String> listTimes = new ArrayList<>();
        List<Float> forwarding = new ArrayList<>();
        List<Float> khoVan = new ArrayList<>();
        LinkedHashMap<String, GroupDataBieuDoTDDTLogistic> fullListData = new LinkedHashMap<>();
        String month = String.valueOf(ngayKetThuc.getMonth().getValue());
        month = month.length() < 2 ? "0" + month : month;
        String year = String.valueOf(ngayKetThuc.getYear());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (int i = 1; i <= checkingDay; i++) {
            String day = i < 10 ? "0" + (i) : String.valueOf(i);
            String dayItem = year + "-" + month + "-" + day;
            GroupDataBieuDoTDDTLogistic sampleData = new GroupDataBieuDoTDDTLogistic();
            sampleData.setDate(dayItem);
            fullListData.put(dayItem, sampleData);
        }

        for (BieuDoTtTienDoDoanhThuLogisticJPADto tienDoDTCPCNItem : tienDoDTCPCN) {
            String startDate = tienDoDTCPCNItem.getNgayBaoCao().format(formatter);
            GroupDataBieuDoTDDTLogistic dataItem = fullListData.get(startDate) ==
                    null ? new GroupDataBieuDoTDDTLogistic() : fullListData.get(startDate);

            dataItem.setDate(startDate);
            if (dataItem.getDate().equals(startDate)) {
                if (tienDoDTCPCNItem.getLoaiDichVu().equals("FORWARDING")) {
                    dataItem.setFORWARDING(tienDoDTCPCNItem.getTiLeTangTruongNgay());
                }
                if (tienDoDTCPCNItem.getLoaiDichVu().equals("KHO-VAN")) {
                    dataItem.setKHOVAN(tienDoDTCPCNItem.getTiLeTangTruongNgay());
                }
            }

            fullListData.put(startDate, dataItem);
        }

        if (!fullListData.isEmpty()) {
            List<GroupDataBieuDoTDDTLogistic> bieuDoDataSort = new ArrayList<>(fullListData.values());
            for (GroupDataBieuDoTDDTLogistic bieuDoDataSortDt : bieuDoDataSort) {
                String[] splitTime = bieuDoDataSortDt.getDate().split("-");
                listTimes.add(splitTime[2] + "/" + splitTime[1]);
                forwarding.add(bieuDoDataSortDt.getFORWARDING());
                khoVan.add(bieuDoDataSortDt.getKHOVAN());
            }
        }

        BieuDoTtTienDoDTLogisticRepo reponseData = new BieuDoTtTienDoDTLogisticRepo();
        reponseData.setDate(listTimes);
        reponseData.setForwarding(forwarding);
        reponseData.setKhoVan(khoVan);

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(reponseData);
        return simpleAPIResponse;
    }

    public List<BieuDoTtTienDoDoanhThuLogisticJPADto> getDataBieuDoTienDoDTLogistic(String maChiNhanh, String maBuuCuc, LocalDate ngayKetThuc) {
        LocalDate ngayBatDau = baseFunction.getFirstDayOfMonth(ngayKetThuc);
        List<BieuDoTtTienDoDoanhThuLogisticJPADto> tienDoDTCPCN = new ArrayList<>();
        if (maChiNhanh.isEmpty()) {
            tienDoDTCPCN = tienDoDTLogisticRepo.dataBieuDoTienDoDoanhThuLogistic(ngayBatDau, ngayKetThuc);
        } else {
            //Tính cho trường hợp bưu cục
            if (!maBuuCuc.isEmpty()) {
                tienDoDTCPCN = tienDoDTLogisticBCRepo.dataBieuDoTienDoDoanhThuLogisticBC(ngayBatDau, ngayKetThuc, maChiNhanh, maBuuCuc);
            } else {
                //Tính cho trường hơp chi nhánh
                tienDoDTCPCN = tienDoDTLogisticCNRepo.dataBieuDoTienDoDoanhThuLogisticCN(ngayBatDau, ngayKetThuc, maChiNhanh);
            }
        }

        return tienDoDTCPCN;
    }
}
