package nocsystem.indexmanager.services.TienDoDoanhThuLogistic;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.helper.FilterLoaiDichVu;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDTLogisticChiNhanhV1Dto;
import nocsystem.indexmanager.repositories.TienDoDoanhThuLogisticBuuCucRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuLogisticChiNhanhRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class TienDoDoanhThuLogisticCNService {
    private final TienDoDoanhThuLogisticChiNhanhRepository logisticChiNhanhRepo;

    private final TienDoDoanhThuLogisticBuuCucRepository logisticBuuCucRepo;

    private FilterLoaiDichVu filterLoaiDichVu;

    private Map<String, String> maTinh;

    public TienDoDoanhThuLogisticCNService(TienDoDoanhThuLogisticChiNhanhRepository logisticChiNhanhRepo, TienDoDoanhThuLogisticBuuCucRepository logisticBuuCucRepo) {
        this.logisticChiNhanhRepo = logisticChiNhanhRepo;
        this.logisticBuuCucRepo = logisticBuuCucRepo;
        this.filterLoaiDichVu = new FilterLoaiDichVu();
        this.maTinh = FilterChiNhanh.maTinh;
    }

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    public CustomizeDataPage<TienDoDTLogisticChiNhanhV1Dto> findAllDataLogistic(
            Integer dichVu, LocalDate toTimePasser, int page, int pageSize) {

        String loaiDichVuCV = this.filterLoaiDichVu.loaiDichVuLogistic(dichVu);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            Page<TienDoDTLogisticChiNhanhV1Dto> logistic_cn;
            Pageable paging = PageRequest.of(page, pageSize);
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                logistic_cn =
                        logisticChiNhanhRepo.findTienDoDoanhThuChiNhanhV2(
                                loaiDichVuCV, toTimePasser, paging, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                logistic_cn =
                        logisticBuuCucRepo.findTienDoDTLogisticBCDetail(
                                loaiDichVuCV, toTimePasser, paging, UserContext.getUserData().getListBuuCucVeriable());
            }

            return this.convertPaginateDataCN(logistic_cn);
        }

        Pageable paging = PageRequest.of(page, pageSize);
        Page<TienDoDTLogisticChiNhanhV1Dto> logistic_cn =
                logisticChiNhanhRepo.findTienDoDoanhThuChiNhanh(
                        loaiDichVuCV, toTimePasser, paging, this.exceptionChiNhanh);
        return this.convertPaginateDataCN(logistic_cn);
    }

    private CustomizeDataPage<TienDoDTLogisticChiNhanhV1Dto> convertPaginateDataCN(Page<TienDoDTLogisticChiNhanhV1Dto> pageResult) {
        ListContentPageDto<TienDoDTLogisticChiNhanhV1Dto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoDTLogisticChiNhanhV1Dto> tienDoDoanhThuCNV1ResDtos = tienDoDTContentPage.getContent();
        for (TienDoDTLogisticChiNhanhV1Dto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(this.maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
            if (tienDoDetail.getKeHoach() == 0) {
                tienDoDetail.setKeHoach(null);
            }
        }

        CustomizeDataPage<TienDoDTLogisticChiNhanhV1Dto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoDTContentPage.getContent());

        return customizeDataPage;
    }
}
