package nocsystem.indexmanager.services.TienDoDoanhThuLogistic;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.helper.FilterLoaiDichVu;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoLogisticDto.TienDoDTLogisticBuuCucV1Dto;
import nocsystem.indexmanager.repositories.TienDoDoanhThuLogisticBuuCucRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class TienDoDoanhThuLogisticBCService {
    private final TienDoDoanhThuLogisticBuuCucRepository tienDoDoanhThuLogisticBuuCucRepository;

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    private FilterLoaiDichVu filterLoaiDichVu;

    private Map<String, String> maTinh;

    public TienDoDoanhThuLogisticBCService(TienDoDoanhThuLogisticBuuCucRepository tienDoDoanhThuLogisticBuuCucRepository) {
        this.tienDoDoanhThuLogisticBuuCucRepository = tienDoDoanhThuLogisticBuuCucRepository;
        this.filterLoaiDichVu = new FilterLoaiDichVu();
        this.maTinh = FilterChiNhanh.maTinh;
    }

    public CustomizeDataPage<TienDoDTLogisticBuuCucV1Dto> findAllDataLogistic(
            String maChiNhanh, String maBuuCuc, Integer dichVu, LocalDate toTimePasser, Integer page, Integer pageSize
    ) {

        String loaiDichVuCV = filterLoaiDichVu.loaiDichVuLogistic(dichVu);
        if ((maBuuCuc != null && !maBuuCuc.isEmpty()) && maChiNhanh != null && !maChiNhanh.isEmpty()) {
            Pageable paging = PageRequest.of(page, pageSize);
            Page<TienDoDTLogisticBuuCucV1Dto> logisticBuuCuc =
                    tienDoDoanhThuLogisticBuuCucRepository.findTienDoDoanhThuBuuCucAndChiNhanh(
                            maChiNhanh, maBuuCuc, loaiDichVuCV, toTimePasser, paging, this.exceptionChiNhanh);
            return this.convertPaginateDataLogistic(logisticBuuCuc);
        }

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            Pageable paging = PageRequest.of(page, pageSize);
            Page<TienDoDTLogisticBuuCucV1Dto> logisticBuuCuc =
                    tienDoDoanhThuLogisticBuuCucRepository.findTienDoDoanhChiNhanhV2(
                            maChiNhanh, loaiDichVuCV, toTimePasser, paging, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginateDataLogistic(logisticBuuCuc);
        }

        Pageable paging = PageRequest.of(page, pageSize);
        Page<TienDoDTLogisticBuuCucV1Dto> logisticBuuCuc =
                tienDoDoanhThuLogisticBuuCucRepository.findTienDoDoanhChiNhanh(
                        maChiNhanh, loaiDichVuCV, toTimePasser, paging, this.exceptionChiNhanh);
        return this.convertPaginateDataLogistic(logisticBuuCuc);
    }

    private CustomizeDataPage<TienDoDTLogisticBuuCucV1Dto> convertPaginateDataLogistic(Page<TienDoDTLogisticBuuCucV1Dto> pageResult) {
        ListContentPageDto<TienDoDTLogisticBuuCucV1Dto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoDTLogisticBuuCucV1Dto> tienDoDoanhThuCNV1ResDtos = tienDoDTContentPage.getContent();
        for (TienDoDTLogisticBuuCucV1Dto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(this.maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
            if (tienDoDetail.getKeHoach() == 0) {
                tienDoDetail.setKeHoach(null);
            }
        }

        CustomizeDataPage<TienDoDTLogisticBuuCucV1Dto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoDTContentPage.getContent());

        return customizeDataPage;
    }
}
