package nocsystem.indexmanager.services.danhgia_nguonluc;

import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.models.Response.BaoCaoChiTietSuCoResp;
import nocsystem.indexmanager.models.Response.ChiTietSuCoResp;
import nocsystem.indexmanager.models.Response.ThongTinDieuChuyenResp;
import nocsystem.indexmanager.models.Response.TongQuanBuuCucResp;
import nocsystem.indexmanager.models.Response.danhgianguonluc.TienDoXLTonVaDuBaoKLHang3NgayRes;
import nocsystem.indexmanager.models.Response.*;
import nocsystem.indexmanager.request.DanhGiaNguonLucRequest;

import java.time.LocalDate;

public interface TongQuanNguonLucService {
    TongQuanBuuCucResp getTongQuanBuuCuc(DanhGiaNguonLucRequest request);

    SimpleAPIResponseWithSum getThongTinDuBaoChuyenCap(DanhGiaNguonLucRequest request);

    SimpleAPIResponseWithSum getTTNhanSuDuBaoNhuCau(DanhGiaNguonLucRequest request);

    TienDoXLTonVaDuBaoKLHang3NgayRes getTienDoXuLyTonDongNhomHang(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);


    ChiTietSuCoResp getChiTietSuCo(DanhGiaNguonLucRequest request);

    ThongTinDieuChuyenResp getThongTinDieuChuyenHoTro(DanhGiaNguonLucRequest request);

    BaoCaoChiTietSuCoResp baoCaoSoSanhChiSo(DanhGiaNguonLucRequest request);

    ChiSoChuyenCapResp chiSoChuyenCapSuCo(DanhGiaNguonLucRequest request);
}
