package nocsystem.indexmanager.services.danhgia_nguonluc;

import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.models.Response.hieuquaxe.CanhBaoHQXTheoChieuRes;
import nocsystem.indexmanager.models.Response.hieuquaxe.DuBaoHQTXeRes;
import nocsystem.indexmanager.models.Response.hieuquaxe.TongQuanHQXRes;
import nocsystem.indexmanager.request.hieuquaxe.DanhSachXeChuyRequest;
import nocsystem.indexmanager.request.hieuquaxe.DuBaoHQTXeRequest;
import nocsystem.indexmanager.request.hieuquaxe.TongQuanHQXRequest;

public interface HieuQuaXeService {

    TongQuanHQXRes tongQuanHQX(TongQuanHQXRequest request);

    CanhBaoHQXTheoChieuRes canhBaoHQXChieu(TongQuanHQXRequest request);

    SimpleAPIResponseWithSum danhSachXeChuy(DanhSachXeChuyRequest request);

    DuBaoHQTXeRes duBaoHieuQuaTuyenXe(DuBaoHQTXeRequest request);

    SimpleAPIResponseWithSum topCoHoiBanHang(DuBaoHQTXeRequest request);
}
