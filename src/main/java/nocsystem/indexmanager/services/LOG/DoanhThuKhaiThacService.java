package nocsystem.indexmanager.services.LOG;

import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacRequestBody;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

public interface DoanhThuKhaiThacService {
    List<DoanhThuKhaiThacResponse> doanhThuKhaiThac(DoanhThuKhaiThacRequestBody doanhThuKhaiThacRequestBody) throws SQLException;
    Long doanhThuKhaiThacTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String ttkt, String ttktCha, List<String> maDichVu) throws SQLException;
    DoanhThuKhaiThacResponse doanhThuKhaiThacTong(DoanhThuKhaiThacRequestBody doanhThuKhaiThacRequestBody) throws SQLException;
    void doanhThuKhaiThacExcel(HttpServletResponse response, DoanhThuKhaiThacRequestBody doanhThuKhaiThacRequestBody) throws IllegalAccessException, IOException, SQLException;
    void doanhThuKhaiThacExcelTrinhKy(HttpServletResponse response, DoanhThuKhaiThacRequestBody doanhThuKhaiThacRequestBody) throws IllegalAccessException, IOException, SQLException;
}
