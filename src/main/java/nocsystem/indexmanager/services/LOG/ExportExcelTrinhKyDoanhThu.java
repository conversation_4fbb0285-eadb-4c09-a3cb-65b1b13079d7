package nocsystem.indexmanager.services.LOG;

import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import nocsystem.indexmanager.models.Response.LOG.DoanhThuKhaiThacTrinhKyExcel;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExportExcelTrinhKyDoanhThu {

  private final XSSFWorkbook workbook;
  private Sheet sheet;
  private int rowIndex;
  private List<DoanhThuKhaiThacTrinhKyExcel> data;
  private LocalDate ngayBaoCao;

  private final HashMap<String, String> ttktName = new HashMap<>() {{
    put("TTKT1", "Trung tâm khai thác 1");
    put("TTKT2", "Trung tâm khai thác 2");
    put("TTKT3", "Trung tâm khai thác 3");
    put("TTKT4", "Trung tâm khai thác 4");
    put("TTKT5", "Trung tâm khai thác 5");
  }};

  public ExportExcelTrinhKyDoanhThu(List<DoanhThuKhaiThacTrinhKyExcel>data, LocalDate ngayBaoCao) {
    this.ngayBaoCao = ngayBaoCao;
    workbook = new XSSFWorkbook();
    sheet = workbook.createSheet("Khai thác T" + ngayBaoCao.getMonthValue());
    this.data = data;
    this.rowIndex = 0;
  }

  private void createHeader(Workbook workbook, Sheet sheet) {
    CellStyle boldStyle = workbook.createCellStyle();
    CellStyle regularStyle = workbook.createCellStyle();
    CellStyle regularStyle_12 = workbook.createCellStyle();

    Font boldFont = workbook.createFont();
    Font boldFont_16 = workbook.createFont();
    Font boldFont_11 = workbook.createFont();
    Font regularFont = workbook.createFont();
    Font regularFont_12 = workbook.createFont();

    boldFont.setBold(true);
    boldFont_16.setBold(true);
    boldFont_11.setBold(true);

    boldFont.setFontHeightInPoints((short) 14);
    boldFont_16.setFontHeightInPoints((short) 16);
    boldFont_11.setFontHeightInPoints((short) 11);
    boldFont_11.setItalic(true);

    regularFont.setFontHeightInPoints((short) 14);
    regularFont_12.setFontHeightInPoints((short) 12);

    boldStyle.setFont(boldFont);
    regularStyle.setFont(regularFont);
    regularStyle_12.setFont(regularFont_12);

    boldFont.setFontName("Times New Roman");
    boldFont_16.setFontName("Times New Roman");
    boldFont_11.setFontName("Times New Roman");
    regularFont.setFontName("Times New Roman");
    regularFont_12.setFontName("Times New Roman");

    // Row 0: Company Info
    Row row0 = sheet.createRow(0);
    Cell cell0 = row0.createCell(0);
    cell0.setCellValue("CÔNG TY TNHH MTV LOGISTICS VIETTEL");
    cell0.setCellStyle(boldStyle);

    // Row 0: Company Info
    Cell cell0_1 = row0.createCell(6);
    cell0_1.setCellValue("Số bảng kê: MM-LOGKT");
    cell0_1.setCellStyle(regularStyle_12);

    // Row 1: Company Details
    Row row1 = sheet.createRow(1);
    Cell cell1 = row1.createCell(0);
    cell1.setCellValue("MST: 0310783329");
    cell1.setCellStyle(regularStyle);

    // Row 0: Company Info
    Cell cell1_1 = row1.createCell(6);
    cell1_1.setCellValue("Kèm theo HĐ số: ngày DD/MM/YYYY");
    CellStyle cell1_1Style = workbook.createCellStyle();
    cell1_1Style.cloneStyleFrom(regularStyle_12);
    cell1_1Style.setVerticalAlignment(VerticalAlignment.TOP);
    cell1_1.setCellStyle(cell1_1Style);

    Row row2 = sheet.createRow(2);
    Cell cell2 = row2.createCell(0);
    cell2.setCellValue(
        "Địa chỉ: 306 Đường Lý Thường Kiệt, Phường 6, Quận Tân Bình, thành phố Hồ Chí Minh, Việt Nam");
    cell2.setCellStyle(regularStyle);
    sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 8));

    Row row3 = sheet.createRow(3);
    row3.setHeightInPoints(18);

    // Row 4: Title

    CellStyle titleStyle = workbook.createCellStyle();
    titleStyle.cloneStyleFrom(boldStyle);
    titleStyle.setFont(boldFont_16);
    titleStyle.setAlignment(HorizontalAlignment.CENTER);
    Row row4 = sheet.createRow(4);
    Cell cell4 = row4.createCell(0);
    row4.setHeightInPoints(20.25f);
    cell4.setCellValue("BẢNG TỔNG HỢP DOANH THU KHAI THÁC BƯU PHẨM BƯU KIỆN");
    cell4.setCellStyle(titleStyle);
    sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 8));

    Row row5 = sheet.createRow(5);
    row5.setHeightInPoints(20.25f);
    Cell cell5 = row5.createCell(0);
    cell5.setCellValue("THÁNG " + String.format("%02d", ngayBaoCao.getMonthValue()) + " NĂM " + ngayBaoCao.getYear());
    cell5.setCellStyle(titleStyle);
    sheet.addMergedRegion(new CellRangeAddress(5, 5, 0, 8));

    Row row6 = sheet.createRow(6);
    row6.setHeightInPoints(13);

    Row row7 = sheet.createRow(7);
    Cell cell7 = row7.createCell(0);
    cell7.setCellValue("Tên người mua: Tổng Công ty Cổ phần Bưu chính Viettel");
    cell7.setCellStyle(regularStyle);

    Row row8 = sheet.createRow(8);
    Cell cell8 = row8.createCell(0);
    cell8.setCellValue(
        "Địa chỉ: Số 2, ngõ 15 phố Duy Tân, Phường Dịch Vọng Hậu, Quận Cầu Giấy, Thành phố Hà Nội, Việt Nam");
    cell8.setCellStyle(regularStyle);

    Row row9 = sheet.createRow(9);
    Cell cell9 = row9.createCell(0);
    cell9.setCellValue("Mã số thuế: 0104093672");
    cell9.setCellStyle(regularStyle);

    Row row10 = sheet.createRow(10);
    Cell cell10 = row10.createCell(7);
    CellStyle unitStyle = workbook.createCellStyle();
    unitStyle.cloneStyleFrom(boldStyle);
    unitStyle.setFont(boldFont_11);
    unitStyle.setAlignment(HorizontalAlignment.RIGHT);
    sheet.addMergedRegion(new CellRangeAddress(10, 10, 7, 8));
    cell10.setCellValue("Đơn vị tính: Đồng");
    cell10.setCellStyle(unitStyle);
  }

  private void createTable(Workbook workbook, Sheet sheet) {
    // Table Headers
    Row headerRow = sheet.createRow(11);
    headerRow.setHeightInPoints(18.75f);
    String[] headers = {"STT", "Đơn vị", "Mã đơn vị", "Sản lượng", "Trọng lượng", "Doanh thu",
        "VAT", "Tổng cộng", "Ghi chú"};
    CellStyle headerStyle = workbook.createCellStyle();
    Font headerFont = workbook.createFont();
    headerFont.setBold(true);
    headerFont.setFontName("Times New Roman");
    headerFont.setFontHeightInPoints((short) 14);
    headerStyle.setFont(headerFont);
    headerStyle.setBorderTop(BorderStyle.THIN);
    headerStyle.setBorderBottom(BorderStyle.THIN);
    headerStyle.setBorderRight(BorderStyle.THIN);
    headerStyle.setBorderLeft(BorderStyle.THIN);
    headerStyle.setAlignment(HorizontalAlignment.CENTER);

    for (int i = 0; i < headers.length; i++) {
      Cell cell = headerRow.createCell(i);
      cell.setCellValue(headers[i]);

      cell.setCellStyle(headerStyle);
    }

    this.rowIndex = 12;
    CellStyle dataStyle = workbook.createCellStyle();
    Font dataFont = workbook.createFont();
    DataFormat dataFormat = workbook.createDataFormat();
    dataStyle.setDataFormat(dataFormat.getFormat("#,##0"));
    dataStyle.setBorderBottom(BorderStyle.DASHED);
    dataStyle.setBorderRight(BorderStyle.THIN);
    dataFont.setFontName("Times New Roman");
    dataFont.setFontHeightInPoints((short) 14);
    dataStyle.setAlignment(HorizontalAlignment.RIGHT);
    dataStyle.setFont(dataFont);
    CellStyle dataStyleCenterAligned = workbook.createCellStyle();
    dataStyleCenterAligned.cloneStyleFrom(dataStyle);
    dataStyleCenterAligned.setAlignment(HorizontalAlignment.CENTER);
    dataStyleCenterAligned.setAlignment(HorizontalAlignment.CENTER);

    CellStyle dataStyleCenterAlignedLeftBorder = workbook.createCellStyle();
    dataStyleCenterAlignedLeftBorder.cloneStyleFrom(dataStyleCenterAligned);
    dataStyleCenterAlignedLeftBorder.setBorderLeft(BorderStyle.THIN);
    Long sumSanLuong = 0L, sumTrongLuong = 0L, sumDoanhThu = 0L, sumVat = 0L, sumTongCong = 0L;
    for (int k = 0; k < data.size(); k++) {

      sumSanLuong += data.get(k).getTongSanLuong();
      sumTrongLuong += data.get(k).getTongTrongLuong();
      sumDoanhThu += data.get(k).getTongDoanhThuSauKpi();
      sumVat += data.get(k).getVAT();
      sumTongCong += data.get(k).getTongCong();

      Row row = sheet.createRow(rowIndex++);
      row.setHeightInPoints(18.75f);
      for (int i = 0; i < 9; i++) {
        Cell cell = row.createCell(i);
        switch (i) {
          case 0:
            cell.setCellValue(k + 1);
            break;
          case 1:
            cell.setCellValue(ttktName.get(data.get(k).getTtkt()));
            break;
          case 2:
            cell.setCellValue(data.get(k).getTtkt());
            break;
          case 3:
            cell.setCellValue(data.get(k).getTongSanLuong());
            break;
          case 4:
            cell.setCellValue(data.get(k).getTongTrongLuong());
            break;
          case 5:
            cell.setCellValue(data.get(k).getTongDoanhThuSauKpi());
            break;
          case 6:
            cell.setCellValue(data.get(k).getVAT());
            break;
          case 7:
            cell.setCellValue(data.get(k).getTongCong());
            break;
          case 8:
            cell.setCellValue("");
            break;
        }
        if (i < 3) {
          if (i == 0) {
            cell.setCellStyle(dataStyleCenterAlignedLeftBorder);
          } else {
            cell.setCellStyle(dataStyleCenterAligned);
          }
        } else {
          cell.setCellStyle(dataStyle);
        }
      }

    }

    Row sumRow = sheet.createRow(rowIndex);
    sumRow.setHeightInPoints(18.75f);
    Cell sumCell = sumRow.createCell(0);

    CellStyle sumStyle = workbook.createCellStyle();
    sumStyle.cloneStyleFrom(headerStyle);
    sumStyle.setAlignment(HorizontalAlignment.RIGHT);
    sumStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    sumStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.index);
    sumStyle.setDataFormat(dataFormat.getFormat("#,##0"));
    for (int i = 3; i < 8; i++) {
      Cell cell = sumRow.createCell(i);
      switch (i) {
        case 3:
          cell.setCellValue(sumSanLuong);
          break;
        case 4:
          cell.setCellValue(sumTrongLuong);
          break;
        case 5:
          cell.setCellValue(sumDoanhThu);
          break;
        case 6:
          cell.setCellValue(sumVat);
          break;
        case 7:
          cell.setCellValue(sumTongCong);
          break;
      }
      cell.setCellStyle(sumStyle);
    }

    CellStyle sumStyleCenterAligned = workbook.createCellStyle();
    sumStyleCenterAligned.cloneStyleFrom(headerStyle);
    sumStyleCenterAligned.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    sumStyleCenterAligned.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.index);

    Cell lastCell = sumRow.createCell(8);
    lastCell.setCellStyle(sumStyleCenterAligned);

    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 2));
    sumCell.setCellValue("Tổng");
    Cell sumCell1 = sumRow.createCell(1);
    Cell sumCell2 = sumRow.createCell(2);
    sumCell.setCellStyle(sumStyleCenterAligned);
    sumCell1.setCellStyle(sumStyleCenterAligned);
    sumCell2.setCellStyle(sumStyleCenterAligned);
    rowIndex++;
  }

  private void createFooter(Workbook workbook, Sheet sheet) {
    CellStyle centerStyle = workbook.createCellStyle();
    centerStyle.setAlignment(HorizontalAlignment.CENTER);
    centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

    CellStyle signDateStyle = workbook.createCellStyle();
    Font signDateFont = workbook.createFont();
    signDateFont.setFontName("Times New Roman");
    signDateFont.setFontHeightInPoints((short) 14);
    signDateFont.setItalic(true);
    signDateStyle.setFont(signDateFont);
    signDateStyle.setAlignment(HorizontalAlignment.CENTER);

    Row signDateRow = sheet.createRow(rowIndex);
    signDateRow.setHeightInPoints(28.5f);

    LocalDate currentDate = LocalDate.now();
    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 6, 8));
    Cell signDateCell = signDateRow.createCell(6);
    signDateCell.setCellValue("Hà Nội, ngày " + String.format("%02d", currentDate.getDayOfMonth()) + " tháng " + String.format("%02d", currentDate.getMonthValue()) + " năm " + currentDate.getYear());
    signDateCell.setCellStyle(signDateStyle);

    rowIndex++;
    // Footer Row 1
    Row footerRow = sheet.createRow(rowIndex);
    CellStyle footerStyle = workbook.createCellStyle();
    Font footerFont = workbook.createFont();
    footerFont.setFontName("Times New Roman");
    footerFont.setFontHeightInPoints((short) 13);
    footerFont.setBold(true);
    footerStyle.setFont(footerFont);
    footerStyle.setAlignment(HorizontalAlignment.CENTER);

    Cell cell1 = footerRow.createCell(0);
    cell1.setCellValue("CÔNG TY TNHH MTV LOGISTICS VIETTEL");
    cell1.setCellStyle(footerStyle);
    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 4));

    Cell cell1Right = footerRow.createCell(6);
    cell1Right.setCellValue("TỔNG CÔNG TY CP BƯU CHÍNH VIETTEL");
    cell1Right.setCellStyle(footerStyle);
    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 6, 8));

    rowIndex++;

    Row footerRow2 = sheet.createRow(rowIndex);
    Cell cell2 = footerRow2.createCell(0);
    cell2.setCellValue("GIÁM ĐỐC");
    cell2.setCellStyle(footerStyle);
    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 4));

    Cell cell2Right = footerRow2.createCell(6);
    cell2Right.setCellValue("P.TỔNG GIÁM ĐỐC");
    cell2Right.setCellStyle(footerStyle);
    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 6, 8));

    rowIndex += 6;

    Row lastRow = sheet.createRow(rowIndex);

    Cell firstCell = lastRow.createCell(0);
    firstCell.setCellValue("PHÒNG KẾ HOẠCH TỔNG HỢP");

    CellStyle footerStyleLeft = workbook.createCellStyle();
    footerStyleLeft.cloneStyleFrom(footerStyle);
    footerStyleLeft.setAlignment(HorizontalAlignment.LEFT);
    firstCell.setCellStyle(footerStyleLeft);

    CellStyle footerStyleRight = workbook.createCellStyle();
    footerStyleRight.cloneStyleFrom(footerStyle);
    footerStyleRight.setAlignment(HorizontalAlignment.RIGHT);

    Cell secondCell = lastRow.createCell(4);
    secondCell.setCellValue("PHÒNG TÀI CHÍNH");
    secondCell.setCellStyle(footerStyleRight);

    Cell thirdCell = lastRow.createCell(6);
    sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 6, 8));
    thirdCell.setCellValue("TRUNG TÂM VẬN HÀNH CHUYỂN PHÁT");
    thirdCell.setCellStyle(footerStyle);
  }

  public void exportDataToExcel(HttpServletResponse response) throws IOException {
    response.setContentType("application/vnd.ms.excel");
    String headerKey = "Content-Disposition";
    String headerValue = "attachment; filename=DoanhThuKhaiThac.xlsx";
    response.setHeader(headerKey, headerValue);

    sheet.setAutobreaks(true);
    workbook.setPrintArea(0, "$A$1:$I$33");

    sheet.setColumnWidth(0, 10 * 256); // STT
    sheet.setColumnWidth(1, 25 * 256); // Đơn vị
    sheet.setColumnWidth(2, 15 * 256); // Mã đơn vị
    sheet.setColumnWidth(3, 15 * 256); // Sản lượng
    sheet.setColumnWidth(4, 20 * 256); // Trọng lượng
    sheet.setColumnWidth(5, 20 * 256); // Doanh thu
    sheet.setColumnWidth(6, 20 * 256); // VAT
    sheet.setColumnWidth(7, 20 * 256); // Tổng cộng
    sheet.setColumnWidth(8, 10 * 256); // Ghi chú

    // Set margins
    sheet.setMargin(Sheet.LeftMargin, 1.3779527559); // 3.5 cm
    sheet.setMargin(Sheet.RightMargin, 0.9842519685); // 2.5 cm
    sheet.setMargin(Sheet.TopMargin, 0.7874015748); // 2 cm
    sheet.setMargin(Sheet.BottomMargin, 0.7874015748); // 2 cm
    sheet.setFitToPage(true);

    PrintSetup printSetup = sheet.getPrintSetup();

    printSetup.setFitHeight((short) 1);
    printSetup.setFitWidth((short) 1);
    printSetup.setLandscape(true);

    // Add header sections
    createHeader(workbook, sheet);

    // Add table
    createTable(workbook, sheet);

    // Add footer sections
    createFooter(workbook, sheet);

    // Write to response
    workbook.write(response.getOutputStream());
    workbook.close();
  }
}
