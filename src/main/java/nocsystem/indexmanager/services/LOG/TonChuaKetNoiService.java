package nocsystem.indexmanager.services.LOG;

import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;

public interface TonChuaKetNoiService {

    SimpleAPIResponseWithSum tonChuaKetNoi(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer dichVu, String loaiDon, Integer page, Integer pageSize, String sort, String sortBy) throws SQLException;

    void exportExcelTonChuaKetNoi(HttpServletResponse response, LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer dichVu, String loaiDon)
            throws IllegalAccessException, IOException, SQLException;
}
