package nocsystem.indexmanager.services.ReconciliationGrab;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.HistoryImportExcelGrabDto;
import nocsystem.indexmanager.models.Response.ReconciliationGrab.ReconciliationRepo;
import nocsystem.indexmanager.request.FilterDetailReconciliationReq;
import nocsystem.indexmanager.request.FilterGrabFinancialStatementsReq;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface ReconciliationGrabService {
    public SimpleAPIResponse ReconciliationFinancial(MultipartFile file) throws Exception;

    public ResponseEntity<Resource> ReconciliationFinancialDownload(String fileName, String typeUpload) throws Exception;

    public List<HistoryImportExcelGrabDto> reconciliationUploadHistory(String fileName, short typeUpload) throws Exception;

    public SimpleAPIResponse reconciliationUploadSuccess(String fileName, short typeUpload) throws Exception;

    public SimpleAPIResponse reconciliationCalculate(int fileId) throws Exception;

    public String processToSaveDataBase() throws Exception;

    public SimpleAPIResponse filterDetailReconciliation(FilterDetailReconciliationReq filterDetailReconciliationReq) throws ParseException;

    public SimpleAPIResponse filterGrabFinancialStatements(FilterGrabFinancialStatementsReq filterGrabFinancialStatementsReq);

    public ResponseEntity<byte[]> exportExcelDetailReconciliation(FilterGrabFinancialStatementsReq filterGrabFinancialStatementsReq);

    List<Map<String, Object>> findAllRecords(FilterDetailReconciliationReq filterDetailReconciliationReq) throws IllegalAccessException;

    void exportExcelChiTietGrab(HttpServletResponse response, FilterDetailReconciliationReq filterDetailReconciliationReq) throws IOException, IllegalAccessException;
}
