package nocsystem.indexmanager.services.ChatLuongSanLuong;

import nocsystem.indexmanager.models.DmSanLuong.LKSanLuongKho;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.*;

import java.time.LocalDate;
import java.util.List;

public interface DmSanLuongService {

    public List<DmSanLuongKhoResDto> searchKho(String maTinh, String maBuuCuc, LocalDate ngayBaoCao);

    public List<DmSanLuongBuuTaResDto> searchBuuTa(String maNhanVien, String maChinhNhanh, String maBuuCuc,LocalDate ngayBaoCao);
    public List<LkSanLuongBuuTaResDto> searchBuuTaLk(String maNhanVien, String maChinhNhanh, String maBuuCuc,LocalDate ngayBaoCao);
    public List<LkSanLuongKhoResDto> searchKhoLk(String maTinh, String maBuuCuc, LocalDate ngayBaoCao);

    public List<LkSanLuongChiNhanhResDto> searchChiNhanhLk(String maTinh, LocalDate ngayBaoCao);

    public List<DmSanLuongChiNhanhTResDto> searchKhoChiNhanhTheoNgay(String maTinh, LocalDate ngayBaoCao);
}
