package nocsystem.indexmanager.services.ChatLuongSanLuong;

import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.DmChatLuongBuuTaResDto;
import nocsystem.indexmanager.models.Response.ChatLuongSanLuong.DmChatLuongKhoResDto;

import java.time.LocalDate;
import java.util.List;

public interface DmChatLuongService {

    public List<DmChatLuongKhoResDto> searchKho(String maTinh, String maBuuCuc, LocalDate ngayBaoCao);

    public List<DmChatLuongBuuTaResDto> searchBuuTa(String maNhanVien, String maChiNhanh, String maBuuCuc,LocalDate ngayBaoCao);
}
