package nocsystem.indexmanager.services;

import nocsystem.indexmanager.models.Test;
import nocsystem.indexmanager.repositories.TestRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class TestService {
    private final TestRepository testRepository;

    public TestService(TestRepository testRepository) {
        this.testRepository = testRepository;
    }

    public List<Test> findAllData(){
        return testRepository.findAll();
    }
}
