package nocsystem.indexmanager.services.mucdohailong;

import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.models.Response.mucdohailong.TyLePhanHoiDashboardDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyLePhanHoiDto;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

public interface TyLePhanHoiService {
    List<TyLePhanHoiDashboardDto> getTyLePhanHoiDashBoard(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException;

    PageWithTotalNumber<TyLePhanHoiDto> getBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request);

    void exportExcelBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request, HttpServletResponse response) throws IOException, NoSuchFieldException, IllegalAccessException;

}
