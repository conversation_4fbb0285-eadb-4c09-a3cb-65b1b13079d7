package nocsystem.indexmanager.services.mucdohailong;

import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.models.Response.mucdohailong.LoiViPhamDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TopViPhamNhieuNhatDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyTrongNguyenNhanKHLCaoNhatDto;
import nocsystem.indexmanager.models.Response.mucdohailong.TyTrongNguyenNhanKHLDto;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

public interface LoiViPhamService {

    PageWithTotalNumber<LoiViPhamDto> getBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request) throws NoSuchFieldException, IllegalAccessException;

    void exportExcelBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request, HttpServletResponse response) throws IOException, NoSuchFieldException, IllegalAccessException;

    List<TyTrongNguyenNhanKHLDto> getTyTrongNguyenNhanKHL(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc);

    List<TyTrongNguyenNhanKHLCaoNhatDto> getTopTyTrongCaoNhat(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc);

    List<TopViPhamNhieuNhatDto> getTopViPhamNhieuNhat(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type);
}
