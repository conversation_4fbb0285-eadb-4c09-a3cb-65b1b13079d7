package nocsystem.indexmanager.services.mucdohailong.mobile;

import nocsystem.indexmanager.models.Response.mucdohailong.mobile.MucDoHaiLongOverview;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.TyLeKHDto;
import nocsystem.indexmanager.models.Response.mucdohailong.mobile.TyTrongNguyenNhanKHLMobileDto;

import java.time.LocalDate;
import java.util.List;

public interface MucDoHaiLongMobileService {
    TyLeKHDto getTyLeKhachHang(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type, String name);

    List<TyTrongNguyenNhanKHLMobileDto> getNguyenNhanKhongHaiLong(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type);

    List<TyTrongNguyenNhanKHLMobileDto> getSoLoiNghiemTrong(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type);

    List<TyTrongNguyenNhanKHLMobileDto> getNguyenNhanKhongHaiLongDetails(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type, String name);

    MucDoHaiLongOverview getTyLeKhachHangOverview(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String type);


}
