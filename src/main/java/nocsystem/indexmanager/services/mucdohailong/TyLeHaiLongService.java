package nocsystem.indexmanager.services.mucdohailong;

import nocsystem.indexmanager.config.PageWithTotalNumber;
import nocsystem.indexmanager.models.Response.mucdohailong.*;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRq;
import nocsystem.indexmanager.request.GetBaoCaoTyLeHaiLongRqExcel;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

public interface TyLeHaiLongService {

    List<TyLeHaiLongDashboardDto> getTyLeHaiLongDashBoard(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException;

    PageWithTotalNumber<TyLeHaiLongDto> getBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request) throws NoSuchFieldException, IllegalAccessException;

    void exportExcelBaoCaoTongHop(GetBaoCaoTyLeHaiLongRq request, HttpServletResponse response) throws IOException, NoSuchFieldException, IllegalAccessException;

    void exportExcelBaoCaoChiTiet(GetBaoCaoTyLeHaiLongRqExcel request, HttpServletResponse response);

    List<TopTenChiNhanhDto> getTopTenChiNhanhDashboard(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc, String type) throws IllegalAccessException;

    TyLeHaiLongWithKPIDashboardDto getTyLeHaiLongWithKPILuyKe(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IllegalAccessException;

    ChiNhanhDatKPIDto getChiNhanhDatKPILuyKe(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException, IllegalAccessException;

    String getUpdatedTime(LocalDate ngayBaoCao);

    List<TyLeHaiLongWithKPIDashboardDto> getTyLeHaiLongWithKPITheoNgay(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IllegalAccessException, IOException;

    List<ChiNhanhDatKPIDto> getChiNhanhDatKPITheoNgay(LocalDate ngayBaoCao, List<String> vung, List<String> maChiNhanh, List<String> maBuuCuc) throws IOException, IllegalAccessException;
}
