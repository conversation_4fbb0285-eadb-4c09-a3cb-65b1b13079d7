package nocsystem.indexmanager.services.CanhBaoServices;

import nocsystem.indexmanager.entity.ChiSoVersion;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongBillResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonDetailBillResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatNguongBillRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatNguongNewRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatNguongRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatHelperExcel.TonPhatBillExcelExporter;
import nocsystem.indexmanager.services.ChiSoVersion.ChiSoVersionPostgresRepo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class TonPhatNguongService {

    private final TonPhatNguongRepository tPNRepo;

    private final TonPhatNguongNewRepository tpnNewRepo;

    private final TonPhatNguongBillRepository tpnBill;
    private final ChiSoVersionPostgresRepo chiSoVersionRepository;

    public TonPhatNguongService(TonPhatNguongRepository tPNrepo, TonPhatNguongNewRepository tpnNewRepo, TonPhatNguongBillRepository tpnBill, ChiSoVersionPostgresRepo chiSoVersionRepository) {
        this.tPNRepo = tPNrepo;
        this.tpnNewRepo = tpnNewRepo;
        this.tpnBill = tpnBill;
        this.chiSoVersionRepository = chiSoVersionRepository;
    }

    public static String sort;

    public boolean isAdmin() {
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true"))
            return true;
        else return false;
    }

    //Tồn phát ngưỡng 789
    public ListContentPageDto<TonPhatNguongNewResponseDTO> findAllDataBC(List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc, LocalDate ngayBaoCao,
                                                                         Integer pageQuery, Integer pageSize, List<String> nguongTon) {
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        Pageable page = Pageable.unpaged();
        switch (sort) {
            case "":
                page = PageRequest.of(pageQuery, pageSize);
                break;
            case "asc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "tongSL"));
                break;
            case "desc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tongSL"));
                break;
            default:
                break;
        }

        if (nguongTon.isEmpty() || nguongTon == null) nguongTon = Arrays.asList("ALL");

        Page<TonPhatNguongNewResponseDTO> pageResult;
        if ((maBuuCuc == null || maBuuCuc.isEmpty())) {
            if (tinhPhat == null || tinhPhat.isEmpty()) {
                if (!isAdmin()){
                    tinhPhat = listCN;
                    maBuuCuc = listBC;
                    pageResult = tpnNewRepo.findAllTonPhatNguongTCTNoAdmin(tinhPhat, loaiTon, nguongTon, ngayBaoCao, maBuuCuc, page);
                }
                else
                    pageResult = tpnNewRepo.findAllTonPhatNguongTCT1(tinhPhat, loaiTon, nguongTon, ngayBaoCao, page);
            } else {
                if (isAdmin())
                    pageResult = tpnNewRepo.findAllTonPhatNguongCN(tinhPhat, loaiTon, nguongTon, ngayBaoCao, page);
                else {
                    tinhPhat.retainAll(listCN);
                    pageResult = tpnNewRepo.findAllTonPhatNguongCN2(tinhPhat, loaiTon, nguongTon, ngayBaoCao, listBC, page);
                }
            }
        } else {
            if (!isAdmin()) {
                tinhPhat.retainAll(listCN);
                maBuuCuc.retainAll(listBC);
            }
            pageResult = tpnNewRepo.findAllTonPhatNguongBC(tinhPhat, loaiTon, maBuuCuc, nguongTon, ngayBaoCao, page);
        }
        long versionTonPhat = chiSoVersionRepository.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaoCao, "ton_phat")
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        String timeTinhToan = tpnNewRepo.getTimeTinhToan(ngayBaoCao,versionTonPhat);
        ListContentPageDto<TonPhatNguongNewResponseDTO> listContent = new ListContentPageDto<>(pageResult);
        listContent.setTime(timeTinhToan);

        return listContent;
    }

    public List<TonPhatNguongNewResponseDTO> getDataSum(List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc,
                                                        List<String> nguongTon, LocalDate ngayBaoCao) {
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        if (nguongTon.isEmpty() || nguongTon == null) nguongTon = Arrays.asList("ALL");

        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
            if ((tinhPhat == null || tinhPhat.isEmpty())) {
                if (!isAdmin()) {
                    tinhPhat = listCN;
                    maBuuCuc = listBC;
                    return tpnNewRepo.findAllTonPhatNguongTCTSumNoAdmin(tinhPhat, loaiTon, nguongTon, ngayBaoCao, maBuuCuc);
                }
                else
                    return tpnNewRepo.findAllTonPhatNguongTCTSum(tinhPhat, loaiTon, nguongTon, ngayBaoCao);
            } else {
                if (isAdmin())
                    return tpnNewRepo.findAllTonPhatNguongCNSum(tinhPhat, loaiTon, nguongTon, ngayBaoCao);
                else {
                    tinhPhat.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                    return tpnNewRepo.findAllTonPhatNguongCNSum2(tinhPhat, loaiTon, nguongTon, ngayBaoCao, listBC);
                }
            }
        } else {
            if (!isAdmin()) {
                tinhPhat.retainAll(listCN);
                maBuuCuc.retainAll(listBC);
            }
            return tpnNewRepo.findAllTonPhatNguongBCSum(tinhPhat, loaiTon, maBuuCuc, nguongTon, ngayBaoCao);
        }
    }

    //Tồn phát ngưỡng 789 sum
    public List<TonPhatNguongNewResponseDTO> SummaryCalculation(List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc, List<String> nguongTon, LocalDate ngayBaoCao) {


        List<TonPhatNguongNewResponseDTO> tPN = getDataSum(tinhPhat, loaiTon, maBuuCuc, nguongTon, ngayBaoCao);
        Long dvNhanh = Long.valueOf(0);
        Long dvCham = Long.valueOf(0);
        Long dvHoaToc = Long.valueOf(0);
        Long tongSL = Long.valueOf(0);


        for (TonPhatNguongNewResponseDTO tPNResponse : tPN) {
            dvNhanh += tPNResponse.getDvNhanh();
            dvCham += tPNResponse.getDvCham();
            dvHoaToc += tPNResponse.getDvHoaToc();
            tongSL += tPNResponse.getTongSL();
        }

        TonPhatNguongNewResponseDTO tPNResponse = new TonPhatNguongNewResponseDTO();
        tPNResponse.setDvNhanh(dvNhanh);
        tPNResponse.setDvCham(dvCham);
        tPNResponse.setDvHoaToc(dvHoaToc);
        tPNResponse.setTongSL(tongSL);

        List<TonPhatNguongNewResponseDTO> listMM = new ArrayList<>();
        listMM.add(tPNResponse);

        return listMM;
    }

    public List<TonPhatNguongNewResponseDTO> exportDataToExcel(HttpServletResponse response, List<String> tinhPhat, List<String> loaiTon,
                                                               List<String> buuCuc, LocalDate ngayBaoCao, List<String> nguongTon) throws IOException {
        List<TonPhatNguongNewResponseDTO> tonPhat789 = getDataSum(tinhPhat, loaiTon, buuCuc, nguongTon, ngayBaoCao);
        CBTonPhatExcelExporter cbTonPhatExcelExporter;

        cbTonPhatExcelExporter = new CBTonPhatExcelExporter(tonPhat789);
        cbTonPhatExcelExporter.exportDataToExcel(response, tinhPhat, nguongTon, buuCuc);
        return tonPhat789;
    }

    public List<TonPhatNguongBillResponseDTO> exportDataBill(HttpServletResponse response, List<String> tinhPhat, List<String> loaiTon, List<String> nguongTon,
                                                             List<String> buuCuc, List<String> dichVu, List<String> loiKhau, LocalDate ngayBaoCao) throws IOException {
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        if (!isAdmin()) {
            if (!tinhPhat.isEmpty())
                tinhPhat.retainAll(listCN);
            else
                tinhPhat = listCN;
            if (!buuCuc.isEmpty())
                buuCuc.retainAll(listBC);
            else
                buuCuc = listBC;
        }
        List<TonPhatNguongBillResponseDTO> tonPhatBill = tpnBill.findAllTonPhatBill(tinhPhat, buuCuc, loaiTon, nguongTon, dichVu, loiKhau, ngayBaoCao);
        TonPhatBillExcelExporter tonPhatBillExcelExporter;

        tonPhatBillExcelExporter = new TonPhatBillExcelExporter(tonPhatBill);
        tonPhatBillExcelExporter.exportDataToExcel(response);
        return tonPhatBill;
    }

    public TonDetailBillResponse tonPhatBill(String maPhieuGui) throws IOException {
        TonPhatNguongBillResponseDTO data = tpnBill.findTonPhatBill(maPhieuGui);
        TonDetailBillResponse result = new TonDetailBillResponse();
        if(data == null) return result;
        result.setMaPhieuGui(data.getMaPhieuGui());
        result.setChiNhanh(data.getChiNhanhHT());
        result.setBuuCucNhan(data.getHuyenNhan());
        result.setTrangThai(data.getTrangThai());
        result.setTuyenBuuTa(data.getBuuTaPhat());
        result.setDanhGia(data.getLoaiCanhBao());
        result.setLoaiDichVu(data.getNhomDV());
        result.setBuuCucHienTai(data.getMaBuuCucHT());
        result.setMaDichVu(data.getMaDvViettel());
        result.setTenBaoCao("Tồn phát");
        return result;
    }

}
