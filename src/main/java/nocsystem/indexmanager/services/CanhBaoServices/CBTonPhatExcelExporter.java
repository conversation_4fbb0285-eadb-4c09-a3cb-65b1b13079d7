package nocsystem.indexmanager.services.CanhBaoServices;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class CBTonPhatExcelExporter {
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;

    private List<TonPhatNguongNewResponseDTO> tonPNResponseDTO;

    public CBTonPhatExcelExporter(List<TonPhatNguongNewResponseDTO> t) {
        this.workbook = workbook = new SXSSFWorkbook();
        this.tonPNResponseDTO = t;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style){
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer){
            cell.setCellValue((Integer) value);
        }else if (value instanceof Double){
            cell.setCellValue((Double) value);
        }else if (value instanceof Boolean){
            cell.setCellValue((Boolean) value);
        }else if (value instanceof Long){
            cell.setCellValue((Long) value);
        }else if(value instanceof String){
            cell.setCellValue((String) value);
        }else if(value instanceof Date){
            cell.setCellValue((Date) value);
        }else if(value instanceof Float){
            cell.setCellValue((Float) value);
        }else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private static final List<String> headerTCT = Arrays.asList("STT", "Tỉnh Phát", "Loại Tồn", "DV NHANH", "DV CHẬM", "DV HỎA TỐC", "TỔNG SL");
    private static final List<String> headerTCTNguong = Arrays.asList("STT", "Tỉnh Phát", "Loại Tồn", "Ngưỡng Tồn", "DV NHANH", "DV CHẬM", "DV HỎA TỐC", "TỔNG SL");
    private static final List<String> headerCN = Arrays.asList("STT", "Tỉnh Phát", "Loại Tồn", "Bưu Cục", "DV NHANH", "DV CHẬM", "DV HỎA TỐC", "TỔNG SL");
    private static final List<String> headerCNNguong = Arrays.asList("STT", "Tỉnh Phát", "Loại Tồn", "Bưu Cục", "Ngưỡng Tồn", "DV NHANH", "DV CHẬM", "DV HỎA TỐC", "TỔNG SL");
    private static final List<String> headerBC = Arrays.asList("STT", "Tỉnh Phát", "Loại Tồn", "Bưu Cục", "Bưu Tá Phát", "DV NHANH", "DV CHẬM", "DV HỎA TỐC", "TỔNG SL");
    private static final List<String> headerBCNguong = Arrays.asList("STT", "Tỉnh Phát", "Loại Tồn", "Bưu Cục", "Ngưỡng Tồn", "Bưu Tá Phát", "DV NHANH", "DV CHẬM", "DV HỎA TỐC", "TỔNG SL");

    private void createHeaderRowBC(List<String> nguongTon){
        sheet   = workbook.createSheet("CẢNH BÁO TỒN PHÁT");
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)13);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        List<String> header;

        if(nguongTon.isEmpty())
            header = headerTCT;
        else header = headerTCTNguong;

        for(int i = 0; i < header.size();i++){
            createCell(row, i, header.get(i), style);
            sheet.setColumnWidth(i,((int)((header.get(i).length() + 4) * 1.14388)) * 256);
        }
    }


    private void writeCustomDataBC(List<String> nguongTon) throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short)12);
//        font.setFontName("Calibri");
        style.setFont(font);

//        CellStyle dateStyle = workbook.createCellStyle();
//        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-mm-yyyy"));
//        dateStyle.setFont(font);
        Integer count = new Integer(0);


        for (TonPhatNguongNewResponseDTO tpn : tonPNResponseDTO){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            count++;
            String tinhPhat = tpn.getTinhPhat();
            String loaiTon = tpn.getLoaiTon();

            createCell(row, columnCount++, count, style);
            createCell(row, columnCount++, tinhPhat, style);
            createCell(row, columnCount++, loaiTon, style);
            if(!nguongTon.isEmpty())
                createCell(row, columnCount++, tpn.getNguongTon(), style);
            createCell(row, columnCount++, tpn.getDvNhanh(), style);
            createCell(row, columnCount++, tpn.getDvCham(), style);
            createCell(row, columnCount++, tpn.getDvHoaToc(), style);
            createCell(row, columnCount++, tpn.getTongSL(), style);
        }

    }

    private void createHeaderRowTCT(List<String> nguongTon){
        sheet   = workbook.createSheet("CẢNH BÁO TỒN PHÁT");
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)13);
//        font.setFontName("Calibri");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        List<String> header;
        if(nguongTon.isEmpty())
            header = headerCN;
        else header = headerCNNguong;

        for(int i = 0; i < header.size();i++){
            createCell(row, i, header.get(i), style);
            sheet.setColumnWidth(i,((int)((header.get(i).length() + 4) * 1.14388)) * 256);
        }
    }


    private void writeCustomDataTCT(List<String> nguongTon) throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short)12);
//        font.setFontName("Times New Roman");
        style.setFont(font);

        DecimalFormat df = new DecimalFormat("0.00");
        Integer count = new Integer(0);

        System.out.println("hahahah"+ tonPNResponseDTO.size());

        for (TonPhatNguongNewResponseDTO tpn : tonPNResponseDTO){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            count++;
            String tinhPhat = tpn.getTinhPhat();

            System.out.println("tinh phat"+ tinhPhat);
            String loaiTon = tpn.getLoaiTon();

            createCell(row, columnCount++, count, style);
            createCell(row, columnCount++, tinhPhat, style);
            createCell(row, columnCount++, loaiTon, style);
            createCell(row, columnCount++, tpn.getMaBuuCuc(), style);
            if(!nguongTon.isEmpty())
                createCell(row, columnCount++, tpn.getNguongTon(), style);
            createCell(row, columnCount++, tpn.getDvNhanh(), style);
            createCell(row, columnCount++, tpn.getDvCham(), style);
            createCell(row, columnCount++, tpn.getDvHoaToc(), style);
            createCell(row, columnCount++, tpn.getTongSL(), style);

        }

    }

    private void createHeaderRowCN(List<String> nguongTon){
        sheet   = workbook.createSheet("CẢNH BÁO TỒN PHÁT");
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)13);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        List<String> header;
        if(nguongTon.isEmpty())
            header = headerBC;
        else header = headerBCNguong;

        for(int i = 0; i < header.size();i++){
            createCell(row, i, header.get(i), style);
            sheet.setColumnWidth(i,((int)((header.get(i).length() + 4) * 1.14388)) * 256);
        }
    }


    private void writeCustomDataCN(List<String> nguongTon) throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short)12);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        DecimalFormat df = new DecimalFormat("0.00");
//        CellStyle dateStyle = workbook.createCellStyle();
//        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-mm-yyyy"));
//        dateStyle.setFont(font);
        Integer count = new Integer(0);


        for (TonPhatNguongNewResponseDTO tpn : tonPNResponseDTO){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            count++;
            String tinhPhat = tpn.getTinhPhat();
            String loaiTon = tpn.getLoaiTon();

            createCell(row, columnCount++, count, style);
            createCell(row, columnCount++, tinhPhat, style);
            createCell(row, columnCount++, loaiTon, style);
            createCell(row, columnCount++, tpn.getMaBuuCuc(), style);
            createCell(row, columnCount++, tpn.getBuuTaPhat(), style);
            if(!nguongTon.isEmpty())
                createCell(row, columnCount++, tpn.getNguongTon(), style);
            createCell(row, columnCount++, tpn.getDvNhanh(), style);
            createCell(row, columnCount++, tpn.getDvCham(), style);
            createCell(row, columnCount++, tpn.getDvHoaToc(), style);
            createCell(row, columnCount++, tpn.getTongSL(), style);
        }

    }

    public void exportDataToExcel(HttpServletResponse response, List<String> tinhPhat,List<String> nguongTon, List<String> buuCuc) throws IOException {
        if(!tinhPhat.isEmpty()){
            if(buuCuc.isEmpty()){
                createHeaderRowTCT(nguongTon);
                writeCustomDataTCT(nguongTon);
            }
            else {
                createHeaderRowCN(nguongTon);
                writeCustomDataCN(nguongTon);
            }
        }
        else {
            createHeaderRowBC(nguongTon);
            writeCustomDataBC(nguongTon);
        }
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }

}
