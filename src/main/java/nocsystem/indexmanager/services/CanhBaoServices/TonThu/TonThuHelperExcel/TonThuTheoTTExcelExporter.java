package nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuHelperExcel;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class TonThuTheoTTExcelExporter {
    private SXSSFWorkbook workbook;

    private SXSSFSheet sheet;
    private final List<TonThuTheoTTResponseDTO> tonThuTTDto;

    public TonThuTheoTTExcelExporter(List<TonThuTheoTTResponseDTO> tonThuTTDto) {
        workbook = new SXSSFWorkbook(10000);
        this.tonThuTTDto = tonThuTTDto;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style){
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer){
            cell.setCellValue((Integer) value);
        }else if (value instanceof Double){
            cell.setCellValue((Double) value);
        }else if (value instanceof Boolean){
            cell.setCellValue((Boolean) value);
        }else if (value instanceof Long){
            cell.setCellValue((Long) value);
        }else if(value instanceof String){
            cell.setCellValue((String) value);
        }else if(value instanceof Date){
            cell.setCellValue((Date) value);
        }else if(value instanceof Float){
            cell.setCellValue((Float) value);
        }else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRowTCT(){
        sheet   = workbook.createSheet("TonThuTheoTT_TCT");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Chi nhánh nhận", style);
        createCell(row, 2, "Cấp độ cảnh báo", style);
        createCell(row, 3, "Trạng thái", style);
        createCell(row, 4, "", style);
        createCell(row, 5, "", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 3, 7));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }

        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Chi nhánh nhận", style);
        createCell(row2, 2, "Cấp độ cảnh báo", style);
        createCell(row2, 3, "100", style);
        createCell(row2, 4, "102", style);
        createCell(row2, 5, "103", style);
        createCell(row2, 6, "104", style);
        createCell(row2, 7, "Tổng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    private void createHeaderRowCN(){
        sheet   = workbook.createSheet("TonThuTheoTT_CN");
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Chi nhánh nhận", style);
        createCell(row, 2, "Bưu cục nhận", style);
        createCell(row, 3, "Cấp độ cảnh báo", style);
        createCell(row, 4, "Trạng thái", style);
        createCell(row, 5, "", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 8));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }


        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Chi nhánh nhận", style);
        createCell(row2, 2, "Bưu cục nhận", style);
        createCell(row2, 3, "Cấp độ cảnh báo", style);
        createCell(row2, 4, "100", style);
        createCell(row2, 5, "102", style);
        createCell(row2, 6, "103", style);
        createCell(row2, 7, "104", style);
        createCell(row2, 8, "Tổng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    private void createHeaderRowBT(){
        sheet   = workbook.createSheet("TonThuTheoTT_BT");
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Row row = sheet.createRow(0);
        createCell(row, 0, "STT", style);
        createCell(row, 1, "Chi nhánh nhận", style);
        createCell(row, 2, "Bưu cục nhận", style);
        createCell(row, 3, "Tuyến bưu tá nhận", style);
        createCell(row, 4, "Cấp độ cảnh báo", style);
        createCell(row, 5, "Trạng thái", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        createCell(row, 9, "", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 5, 9));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }


        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Chi nhánh nhận", style);
        createCell(row2, 2, "Bưu cục nhận", style);
        createCell(row2, 3, "Tuyến bưu tá nhận", style);
        createCell(row2, 4, "Cấp độ cảnh báo", style);
        createCell(row2, 5, "100", style);
        createCell(row2, 6, "102", style);
        createCell(row2, 7, "103", style);
        createCell(row2, 8, "104", style);
        createCell(row2, 9, "Tổng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 4, 4));

        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    public void writeCustomDataTCT() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();
        Integer soThuTu = new Integer(0);

        for (TonThuTheoTTResponseDTO tonThuTT : tonThuTTDto){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String chiNhanhNhan = tonThuTT.getChiNhanhNhan();
            String loaiCanhBao = tonThuTT.getLoaiCanhBao();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, chiNhanhNhan, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, tonThuTT.getTrangThai100(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai102(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai103(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai104(), style);
            createCell(row, columnCount++, tonThuTT.getTong(), style);
        }
    }
    public void writeCustomDataCN() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();

        Integer soThuTu = new Integer(0);
        for (TonThuTheoTTResponseDTO tonThuTT : tonThuTTDto){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String chiNhanhNhan = tonThuTT.getChiNhanhNhan();
            String loaiCanhBao = tonThuTT.getLoaiCanhBao();
            String buuCucNhan = tonThuTT.getBuuCucNhan();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, chiNhanhNhan, style);
            createCell(row, columnCount++, buuCucNhan, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, tonThuTT.getTrangThai100(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai102(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai103(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai104(), style);
            createCell(row, columnCount++, tonThuTT.getTong(), style);
        }
    }

    public void writeCustomDataBT() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();

        Integer soThuTu = new Integer(0);
        for (TonThuTheoTTResponseDTO tonThuTT : tonThuTTDto){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String chiNhanhNhan = tonThuTT.getChiNhanhNhan();
            String loaiCanhBao = tonThuTT.getLoaiCanhBao();
            String buuCucNhan = tonThuTT.getBuuCucNhan();
            String buuTaNhan = tonThuTT.getBuuTaNhan();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, chiNhanhNhan, style);
            createCell(row, columnCount++, buuCucNhan, style);
            createCell(row, columnCount++, buuTaNhan, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, tonThuTT.getTrangThai100(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai102(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai103(), style);
            createCell(row, columnCount++, tonThuTT.getTrangThai104(), style);
            createCell(row, columnCount++, tonThuTT.getTong(), style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response, List<String> chiNhanhNhan, List<String> buuCucNhan) throws IOException {

        if(buuCucNhan == null || buuCucNhan.isEmpty()){
            if(chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                createHeaderRowTCT();
                writeCustomDataTCT();
            }else {
                createHeaderRowCN();
                writeCustomDataCN();
            }
        }else {
            createHeaderRowBT();
            writeCustomDataBT();
        }
        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        }
    }
}
