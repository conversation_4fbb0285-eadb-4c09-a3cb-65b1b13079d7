package nocsystem.indexmanager.services.CanhBaoServices.TonThu;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTCanhBaoDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoTTResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuTheoTTRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuHelperExcel.TonThuTheoTTExcelExporter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;

@Service
public class TonThuTheoTTService {

    private final TonThuTheoTTRepository tTTTrepo;

    public static String sort;

    public TonThuTheoTTService(TonThuTheoTTRepository tTTTrepo) {
        this.tTTTrepo = tTTTrepo;
    }

    public String getCa() {
        String ca;
        Calendar rightNow = Calendar.getInstance();
        int hour = rightNow.get(Calendar.HOUR_OF_DAY);

        if (hour < 18 && hour > 12) {
            ca = "CA2";
        } else ca = "CA1";
        return ca;
    }

    private boolean isAdmin(){
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true"))
            return true;
        else return false;
    }

    public ListContentPageDto<TonThuTheoTTResponseDTO> findAllDataBT(List<String> chiNhanhNhan, List<String> buuCucNhan, LocalDate ngayBaoCao, List<String> loaiCanhBao, Integer pageQuery, Integer pageSize) {

        Pageable page = Pageable.unpaged();
        switch (sort) {
            case "":
                page = PageRequest.of(pageQuery, pageSize);
                break;
            case "asc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "tong"));
                break;
            case "desc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tong"));
                break;
            default:
                break;
        }

        if (loaiCanhBao.isEmpty()) loaiCanhBao = Arrays.asList("XANH", "VANG", "DO_1", "DO_2", "DO_3");


        Page<TonThuTheoTTResponseDTO> pageResult;
        if (buuCucNhan == null || buuCucNhan.isEmpty()) {
            if (chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                if (isAdmin())
                    pageResult = tTTTrepo.TonThuTheoTTTCT(ngayBaoCao, loaiCanhBao, getCa(), page);
                else {
                    chiNhanhNhan = ListVariableLocation.listChiNhanhVeriable;
                    buuCucNhan = ListVariableLocation.listBuuCucVeriable;
                    pageResult = tTTTrepo.TonThuTheoTTBC1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, getCa(), page);
//                    pageResult = tTTTrepo.TonThuTheoTTTCT1(chiNhanhNhan, ngayBaoCao, loaiCanhBao, getCa(), page);
                }
            } else {
                if (isAdmin())
                    pageResult = tTTTrepo.TonThuTheoTTBC(chiNhanhNhan, ngayBaoCao, loaiCanhBao, getCa(), page);
                else {
                    chiNhanhNhan.retainAll(ListVariableLocation.listChiNhanhVeriable);
                    buuCucNhan = ListVariableLocation.listBuuCucVeriable;
                    pageResult = tTTTrepo.TonThuTheoTTBC1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, getCa(), page);
                }
            }
        } else {
            if (!isAdmin()) {
                chiNhanhNhan.retainAll(ListVariableLocation.listChiNhanhVeriable);
                buuCucNhan.retainAll(ListVariableLocation.listBuuCucVeriable);
            }
            pageResult = tTTTrepo.TonThuTheoTTBT(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, getCa(), page);
        }

        Page<String> time = tTTTrepo.getTimeTinhToan(ngayBaoCao,Pageable.ofSize(1));
        String timeTinhToan = null;
        if(time.hasNext())
            timeTinhToan = time.getContent().get(0);
        ListContentPageDto<TonThuTheoTTResponseDTO> listContent = new ListContentPageDto<>(pageResult);
        listContent.setTime(timeTinhToan);

        return listContent;
//        return new ListContentPageDto<TonThuTheoTTResponseDTO>(pageResult);
    }

    public List<TonThuTheoTTResponseDTO> getDataSum(List<String> chiNhanhNhan, List<String> buuCucNhan, LocalDate ngayBaoCao, List<String> loaiCanhBao) {

        if (loaiCanhBao.isEmpty()) loaiCanhBao = Arrays.asList("XANH", "VANG", "DO_1", "DO_2", "DO_3");


        if (buuCucNhan == null || buuCucNhan.isEmpty()) {
            if (chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                if (isAdmin())
                    return tTTTrepo.TonThuTheoTTSum(ngayBaoCao, loaiCanhBao, getCa());
                else {
                    chiNhanhNhan = ListVariableLocation.listChiNhanhVeriable;
                    buuCucNhan = ListVariableLocation.listBuuCucVeriable;
//                    return tTTTrepo.TonThuTheoTTSum1(chiNhanhNhan, ngayBaoCao, loaiCanhBao, getCa());
                    return tTTTrepo.TonThuTheoTTBCSum1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, getCa());
                }
            } else {
                if (isAdmin())
                    return tTTTrepo.TonThuTheoTTBCSum(chiNhanhNhan, ngayBaoCao, loaiCanhBao, getCa());
                else {
                    chiNhanhNhan.retainAll(ListVariableLocation.listChiNhanhVeriable);
                    buuCucNhan = ListVariableLocation.listBuuCucVeriable;
                    return tTTTrepo.TonThuTheoTTBCSum1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, getCa());
                }
            }
        } else {
            if (!isAdmin()) {
                chiNhanhNhan.retainAll(ListVariableLocation.listChiNhanhVeriable);
                buuCucNhan.retainAll(ListVariableLocation.listBuuCucVeriable);
            }
            return tTTTrepo.TonThuTheoTTBTSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, getCa());
        }
    }

    public List<TonThuTheoTTResponseDTO> SummaryCalculation(List<String> chiNhanhNhan, List<String> buuCucNhan, LocalDate ngayBaoCao, List<String> loaiCanhBao) {

        List<TonThuTheoTTResponseDTO> tTKH = getDataSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao);
        Float tt100 = Float.valueOf(0);
        Float tt102 = Float.valueOf(0);
        Float tt103 = Float.valueOf(0);
        Float tt104 = Float.valueOf(0);
        Float tong = Float.valueOf(0);


        for (TonThuTheoTTResponseDTO tonThuTheoTTResponseDTO : tTKH) {

            tt100 += tonThuTheoTTResponseDTO.getTrangThai100();
            tt102 += tonThuTheoTTResponseDTO.getTrangThai102();
            tt103 += tonThuTheoTTResponseDTO.getTrangThai103();
            tt104 += tonThuTheoTTResponseDTO.getTrangThai104();
            tong += tonThuTheoTTResponseDTO.getTong();
        }

        TonThuTheoTTResponseDTO tTTTResponse = new TonThuTheoTTResponseDTO();

        tTTTResponse.setTrangThai100(tt100);
        tTTTResponse.setTrangThai102(tt102);
        tTTTResponse.setTrangThai103(tt103);
        tTTTResponse.setTrangThai104(tt104);
        tTTTResponse.setTong(tong);

        List<TonThuTheoTTResponseDTO> listTT= new ArrayList<>();
        listTT.add(tTTTResponse);

        return listTT;
    }

    private final List<String> colors = Arrays.asList("XANH", "VANG", "DO_1", "DO_2", "DO_3");

    //Luongnv dashboard
    public List<TonThuTheoTTCanhBaoDTO> TTSumTrangThai(String chiNhanhNhan, String buuCucNhan, LocalDate ngayBaoCao) {
        String ca = getCa();
        List<TonThuTheoTTCanhBaoDTO> tTSum;
        if (buuCucNhan == null || buuCucNhan.isEmpty()) {
            if (chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                if (isAdmin()) {
                    List<TonThuTheoTTResponseDTO> l = tTTTrepo.tonThuTTSumNgayBaoCaoAdmin(ngayBaoCao, ca);
                    tTSum = sumAndSort(l);
                } else {
                    List<TonThuTheoTTResponseDTO> l = tTTTrepo.tonThuTTSumNgayBaoCaoNotAdmin(ListVariableLocation.listChiNhanhVeriable, ListVariableLocation.listBuuCucVeriable, ngayBaoCao, ca);
                    tTSum = sumAndSort(l);
                }
            } else {
                if (isAdmin()) {
                    List<TonThuTheoTTResponseDTO> l = tTTTrepo.TonThuTTBCChiNhanhAdmin(chiNhanhNhan, ngayBaoCao, ca);
                    tTSum = sumAndSort(l);
                } else {
                    List<String> chiNhanhNhanTmp = new ArrayList<>();
                    chiNhanhNhanTmp.add(chiNhanhNhan);

                    List<String> buuCucNhanTmp = new ArrayList<>();
                    buuCucNhanTmp = ListVariableLocation.listBuuCucVeriable;
                    chiNhanhNhanTmp.retainAll(ListVariableLocation.listChiNhanhVeriable);
                    List<TonThuTheoTTResponseDTO> l = tTTTrepo.TonThuTTBCChiNhanhNotAdmin(chiNhanhNhanTmp, buuCucNhanTmp, ngayBaoCao, ca);
                    tTSum = sumAndSort(l);
                }
            }
        } else {
            List<String> chiNhanhNhanTmp = new ArrayList<>();
            List<String> buuCucNhanTmp = new ArrayList<>();
            chiNhanhNhanTmp.add(chiNhanhNhan);
            buuCucNhanTmp.add(buuCucNhan);
            if (!isAdmin()) {
                chiNhanhNhanTmp.retainAll(ListVariableLocation.listChiNhanhVeriable);
                buuCucNhanTmp.retainAll(ListVariableLocation.listBuuCucVeriable);
            }
            tTSum = sumAndSort(tTTTrepo.TonThuTheoTTBTSumBuuCuc(chiNhanhNhanTmp, buuCucNhanTmp, ngayBaoCao, ca));
        }
        return tTSum;
    }

    public List<TonThuTheoTTCanhBaoDTO> sumAndSort(List<TonThuTheoTTResponseDTO> lTTRespon) {

        List<TonThuTheoTTCanhBaoDTO> listTTCanhBaoDTO = new ArrayList<>();
        Float tongCot100 = 0f;
        Float tongCot102 = 0f;
        Float tongCot103 = 0f;
        Float tongCot104 = 0f;
        Float tong = 0f;

        class GiaTri{
            public Float tt_100 = 0f;
            public Float tt_102 = 0f;
            public Float tt_103 = 0f;
            public Float tt_104 = 0f;
            public Float tong = 0f;

        }
        HashMap<String, GiaTri> hashMap = new HashMap<>();
        hashMap.put("XANH", new GiaTri());
        hashMap.put("VANG", new GiaTri());
        hashMap.put("DO_1", new GiaTri());
        hashMap.put("DO_2", new GiaTri());
        hashMap.put("DO_3", new GiaTri());


        System.out.println(" kich thuoc db lay ra dươc "+ lTTRespon.size());
        for (TonThuTheoTTResponseDTO tt : lTTRespon) {
            String colorCB = tt.getLoaiCanhBao();
            hashMap.get(colorCB).tt_100 += tt.getTrangThai100();
            hashMap.get(colorCB).tt_102 += tt.getTrangThai102();
            hashMap.get(colorCB).tt_103 += tt.getTrangThai103();
            hashMap.get(colorCB).tt_104 += tt.getTrangThai104();
            hashMap.get(colorCB).tong += tt.getTong();

            tongCot100 += tt.getTrangThai100();
            tongCot102 += tt.getTrangThai102();
            tongCot103 += tt.getTrangThai103();
            tongCot104 += tt.getTrangThai104();
            tong += tt.getTong();

        }
        for (Map.Entry<String, GiaTri> entry : hashMap.entrySet()) {
            String key = entry.getKey();
            GiaTri value = entry.getValue();
            TonThuTheoTTCanhBaoDTO tTCanhBaoDTO = new TonThuTheoTTCanhBaoDTO();
            tTCanhBaoDTO.setLoaiCanhBao(key);
            tTCanhBaoDTO.setTrangThai100(value.tt_100);
            tTCanhBaoDTO.setTrangThai102(value.tt_102);
            tTCanhBaoDTO.setTrangThai103(value.tt_103);
            tTCanhBaoDTO.setTrangThai104(value.tt_104);
            tTCanhBaoDTO.setTong(value.tong);
            listTTCanhBaoDTO.add(tTCanhBaoDTO);
        }
        // sort list object theo color  ----------------
        Comparator<TonThuTheoTTCanhBaoDTO> colorComparator = new Comparator<TonThuTheoTTCanhBaoDTO>() {
            @Override
            public int compare(TonThuTheoTTCanhBaoDTO obj1, TonThuTheoTTCanhBaoDTO obj2) {
                int index1 = colors.indexOf(obj1.getLoaiCanhBao());
                int index2 = colors.indexOf(obj2.getLoaiCanhBao());
                return Integer.compare(index1, index2);
            }
        };
        Collections.sort(listTTCanhBaoDTO, colorComparator);
        // ---------------------------------------------

        TonThuTheoTTCanhBaoDTO tTCanhBaoDTO1 = new TonThuTheoTTCanhBaoDTO();

        tTCanhBaoDTO1.setLoaiCanhBao("Tong");
        tTCanhBaoDTO1.setTrangThai100(tongCot100);
        tTCanhBaoDTO1.setTrangThai102(tongCot102);
        tTCanhBaoDTO1.setTrangThai103(tongCot103);
        tTCanhBaoDTO1.setTrangThai104(tongCot104);
        tTCanhBaoDTO1.setTong(tong);

        listTTCanhBaoDTO.add(tTCanhBaoDTO1);
        return listTTCanhBaoDTO;

    }

    // VT2023/02/25_Ngocnt92 add function to export data to excel
    public void exportExcelTTTT(HttpServletResponse response,
                                List<String> chiNhanhNhan,
                                List<String> buuCucNhan,
                                LocalDate ngayBaoCao,
                                List<String> loaiCanhBao
    )throws IOException {
        List<TonThuTheoTTResponseDTO> tonThuKHExcel = getDataSum( chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao);
        TonThuTheoTTExcelExporter tTTheoTTExcelExporter = new TonThuTheoTTExcelExporter(tonThuKHExcel);
        tTTheoTTExcelExporter.exportDataToExcel(response, chiNhanhNhan, buuCucNhan);
    }

}
