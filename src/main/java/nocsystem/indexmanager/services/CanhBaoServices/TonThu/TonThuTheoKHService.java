package nocsystem.indexmanager.services.CanhBaoServices.TonThu;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import nocsystem.indexmanager.entity.ChiSoVersion;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuBillDashboarResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC.TonThuDashCNBCResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiExcelDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.*;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotificationDisplayDto;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.SanLuongTonBuuTa;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonDetailBillResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuBillRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuTheoKHRepository;
import nocsystem.indexmanager.services.CanhBaoServices.Notification.TonThuNotificationExcelExporter;
import nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuHelperExcel.TonThuBillExcelExporter;
import nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuHelperExcel.TonThuTheoKHExcelExporter;
import nocsystem.indexmanager.services.ChiSoVersion.ChiSoVersionPostgresRepo;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TonThuTheoKHService {

    private final ChiSoVersionPostgresRepo chiSoVersionRepo;
    private final TonThuTheoKHRepository tTKHrepo;

    private final TonThuBillRepository thuBillRepo;

    private final TonThuRepository tonThuRepository;

    public static final Integer Ban_TGD = 1;
    public static final Integer BAN_GIAM_DOC_CHI_NHANH = 2;
    public static final Integer BAN_GIAM_DOC_CHI_NHANH_HN_HCM = 4;
    public static final Integer TRUONG_BUU_CUC = 3;

    public TonThuTheoKHService(TonThuTheoKHRepository tTKHrepo, TonThuBillRepository thuBillRepo, TonThuRepository tonThuRepository, ChiSoVersionPostgresRepo chiSoVersionRepo) {
        this.tTKHrepo = tTKHrepo;
        this.thuBillRepo = thuBillRepo;
        this.tonThuRepository = tonThuRepository;
        this.chiSoVersionRepo = chiSoVersionRepo;
    }

    private String getCa() {
        String ca;
        Calendar rightNow = Calendar.getInstance();
        int hour = rightNow.get(Calendar.HOUR_OF_DAY);

        if (hour < 18 && hour > 12) {
            ca = "CA2";
        } else ca = "CA1";
        return ca;
    }

    public static String sort;

    private boolean isAdmin() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true");
    }

    private boolean isViewTonThu() {
        return UserContext.getUserData().getIsViewAllDashChiNhanhBuuCuc().equalsIgnoreCase("true");
    }

    //lấy list chi nhánh, bưu cục cần để query theo filter + SSO
    public Map<String, List<String>> checkListCNBC(String chiNhanh, String buuCuc) {
        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();
        Map<String, List<String>> mapCNBC = new HashMap<>();
        if (chiNhanh.equals("")) {
            listCN = UserContext.getUserData().getListChiNhanhVeriable();
        } else {
            if (UserContext.getUserData().getListChiNhanhVeriable().contains(chiNhanh))
                listCN.add(chiNhanh);
            else return mapCNBC;
        }
        if (buuCuc.equals("")) {
            listBC = UserContext.getUserData().getListBuuCucVeriable();
        } else {
            if (UserContext.getUserData().getListBuuCucVeriable().contains(buuCuc))
                listBC.add(buuCuc);
            else if (buuCuc.equalsIgnoreCase("CHUA_XAC_DINH"))
                listBC.add("CHUA_XAC_DINH");
            else
                return mapCNBC;
        }
        mapCNBC.put("cn", listCN);
        mapCNBC.put("bc", listBC);
        return mapCNBC;
    }

    //màn hình thông tin tồn thu của dashboard chi nhánh bưu cục
    public ListContentPageDto<TonThuDashCNBCResponseDTO> tonThuDashboard(String chiNhanh, String vungCon, String buuCuc, LocalDate ngayBaoCao, String trangThai, Integer pageQuery, Integer pageSize, String sort, String sortBy, String maDoiTac, String loaiHH, String khDacThuGui, String khDacThuNhan, String maKhGui) {

        Pageable page;
        switch (sort) {
            case "asc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, sortBy.toLowerCase(Locale.ROOT).replace("_", "")));
                break;
            case "desc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, sortBy.toLowerCase(Locale.ROOT).replace("_", "")));
                break;
            default:
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "chiNhanh"));
                break;
        }
        Page<TonThuDashCNBCResponseDTO> pageResult = null;

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();


        if (isViewTonThu() || isAdmin() || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("False")) {
            if (!buuCuc.equals(""))
                listBC = List.of(buuCuc);
            if (!chiNhanh.equals(""))
                listCN = List.of(chiNhanh);
        } else {
            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            if (mapCNBC.isEmpty()) return new ListContentPageDto<>();
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH")
                    && buuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }

        Long version = getChiSoVersion(ngayBaoCao, "ton_thu");

        ListContentPageDto<TonThuDashCNBCResponseDTO> pageContent = new ListContentPageDto<>();

        if (version != null) {
            if (buuCuc.equals("")) {
                if (chiNhanh.equals("")) {
                    pageResult = tonThuRepository.findPageTonThu(ngayBaoCao, listCN, listBC, trangThai, vungCon, getCa(), page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
                } else
                    pageResult = tonThuRepository.findPageTonThuCN(ngayBaoCao, listCN, listBC, trangThai, vungCon, getCa(), page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
            } else {
                pageResult = tonThuRepository.findPageTonThuBC(ngayBaoCao, listCN, listBC, trangThai, vungCon, getCa(), page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
            }
        }

        String timeTinhToan = tonThuRepository.getTimeTinhToan(version,ngayBaoCao);

        if (!(pageResult == null || pageResult.isEmpty())) {
            pageContent = new ListContentPageDto<>(pageResult);
        }
        pageContent.setTime(timeTinhToan);
        return pageContent;


//        Page<String> time = tonThuRepository.getTimeTinhToan(ngayBaoCao, PageRequest.of(1, 1));
//        String timeTinhToan = "";
//        if(!time.getContent().isEmpty())
//            timeTinhToan = time.getContent().get(0);
//
//        pageContent.setTime(timeTinhToan);
//        return new ListContentPageDto<>();
    }

    //màn hình thông tin tồn thu của dashboard chi nhánh bưu cục
    public ListContentPageDto<TonThuDashCNBCResponseDTO> tonThuDashboard2(String chiNhanh, String buuCuc, LocalDate ngayBaoCao, String trangThai, Integer pageQuery, Integer pageSize) {
        Pageable page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tong"));
        Page<TonThuDashCNBCResponseDTO> pageResult = null;

//        if(isViewTonThu())
//            if (buuCuc.equals("")) {
//                if (chiNhanh.equals("")) {
//                    pageResult = thuBillRepo.findPageTonThuTCT(ngayBaoCao, chiNhanh, getCa(), page);
//                } else
//                    pageResult = thuBillRepo.findPageTonThuCN(ngayBaoCao, chiNhanh, buuCuc, getCa(), page);
//            } else
//                pageResult = thuBillRepo.findPageTonThuBC(ngayBaoCao, chiNhanh, buuCuc, getCa(), page);
//        else {
//            List<String> listBC;
//            List<String> listCN;
//            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
//            if(mapCNBC.isEmpty()) return new ListContentPageDto<>();
//            listCN = mapCNBC.get("cn");
//            listBC = mapCNBC.get("bc");
//
//            if (buuCuc.equals("")) {
//                if (chiNhanh.equals("")) {
//                    pageResult = thuBillRepo.findPageTonThuTCTNoAdmin(ngayBaoCao, listCN, getCa(), page);
//                } else
//                    pageResult = thuBillRepo.findPageTonThuCNNoAdmin(ngayBaoCao, listCN, listBC, getCa(), page);
//            }
//            else {
//                pageResult = thuBillRepo.findPageTonThuBCNoAdmin(ngayBaoCao, listCN, listBC , getCa(), page);
//            }
//        }

        Page<String> time = thuBillRepo.getTimeTinhToan2(ngayBaoCao, PageRequest.of(1, 1));
        String timeTinhToan = "";
        if (!time.getContent().isEmpty())
            timeTinhToan = time.getContent().get(0);

        ListContentPageDto<TonThuDashCNBCResponseDTO> pageContent = new ListContentPageDto<>(pageResult);
        pageContent.setTime(timeTinhToan);

        return pageContent;
    }

    //màn hình dashboard CNBC summary
    public List<TonThuDashCNBCResponseDTO> tonThuDashboardSum(String chiNhanh, String buuCuc, LocalDate ngayBaoCao, String trangThai, String vungCon, String maDoiTac, String loaiHH, String khDacThuGui, String khDacThuNhan, String maKhGui) {
        List<TonThuDashCNBCResponseDTO> result = new ArrayList<>();

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        Long version = getChiSoVersion(ngayBaoCao, "ton_thu");

        if (version == null) return new ArrayList<>();

        if (isViewTonThu() || isAdmin() || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("False")) {
            if (!buuCuc.equals(""))
                listBC = List.of(buuCuc);
            if (!chiNhanh.equals(""))
                listCN = List.of(chiNhanh);
        } else {
            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            if (mapCNBC.isEmpty()) return result;
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH")
                    && buuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
        }
        if (listBC.isEmpty()) {
            result = tonThuRepository.findTonThuSum(ngayBaoCao, listCN, listBC, trangThai, vungCon, getCa(), maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
        } else {
            result = tonThuRepository.findTonThuSumBC(ngayBaoCao, listCN, listBC, trangThai, vungCon, getCa(), maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
        }
        return result;
    }

    //màn hình dashboard CNBC summary
    public List<TonThuDashCNBCResponseDTO> tonThuDashboardSum2(String chiNhanh, String buuCuc, LocalDate ngayBaoCao, String trangThai){
        List<TonThuDashCNBCResponseDTO> result = new ArrayList<>();

        if(isViewTonThu())
            if (buuCuc.equals("")) {
                if (chiNhanh.equals("")) {
                    result = thuBillRepo.findPageTonThuTCTSum(ngayBaoCao, chiNhanh, getCa());
                } else
                    result = thuBillRepo.findPageTonThuCNSum(ngayBaoCao, chiNhanh, buuCuc, getCa());
            } else
                result = thuBillRepo.findPageTonThuBCSum(ngayBaoCao, chiNhanh, buuCuc, getCa());
        else {
            List<String> listBC;
            List<String> listCN;
            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            if(mapCNBC.isEmpty()) return result;
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (buuCuc.equals("")) {
                if (chiNhanh.equals("")) {
                    result = thuBillRepo.findPageTonThuTCTNoAdminSum(ngayBaoCao, listCN, getCa());
                } else
                    result = thuBillRepo.findPageTonThuCNNoAdminSum(ngayBaoCao, listCN, listBC, getCa());
            }
            else {
                result = thuBillRepo.findPageTonThuBCNoAdminSum(ngayBaoCao, listCN, listBC , getCa());
            }
        }
        return result;
    }

    //màn hình thông tin tồn thu của dashboard chi nhánh bưu cục
    public ListContentPageDto<TonThuBillDashboarResponseDTO> tonThuChiTietDashboard(String chiNhanh, String vungCon, String buuCuc, LocalDate ngayBaoCao, String buuTa,
                                                                                    String trangThai, String loaiCanhBao, Integer pageQuery, Integer pageSize,
                                                                                    String maDoiTac, String loaiHH, String khDacThuGui, String khDacThuNhan, String maKhGui) {
        Pageable page = PageRequest.of(pageQuery, pageSize);
        Page<TonThuBillDashboarResponseDTO> pageResult = null;

        Long version = getChiSoVersion(ngayBaoCao, "ton_thu");
        if (isViewTonThu() || isAdmin() || UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true")){
            if(version != null)
                pageResult = thuBillRepo.tonThuChiTiet(ngayBaoCao, chiNhanh, vungCon, buuCuc, trangThai, loaiCanhBao, buuTa, getCa(), page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
        }
        else {
            List<String> listBC = new ArrayList<>();
            List<String> listCN;
            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            if (mapCNBC.isEmpty()) return new ListContentPageDto<>();
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE") && !listBC.get(0).equalsIgnoreCase("CHUA_XAC_DINH") && buuCuc.equals("")) {
                listBC.add("CHUA_XAC_DINH");
            }
            if(version != null)
                pageResult = thuBillRepo.tonThuChiTietNoAdmin(ngayBaoCao, listCN, vungCon, listBC, trangThai, loaiCanhBao, buuTa, getCa(), page, maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
        }
        ListContentPageDto<TonThuBillDashboarResponseDTO> pageContent = new ListContentPageDto<>(pageResult);
        return pageContent;
    }

    public TonDetailBillResponse tonThuChiTietDon(String maPhieuGui) {
        TonThuBillResponseDTO data = thuBillRepo.findBill(maPhieuGui, getCa());
        TonDetailBillResponse result = new TonDetailBillResponse();
        if (data == null) return result;
        result.setMaPhieuGui(data.getMaPhieuGui());
        result.setChiNhanh(data.getTinhKHGui());
        result.setBuuCucNhan(data.getMaBuuCuc());
        result.setTrangThai(data.getTrangThai());
        result.setTuyenBuuTa(data.getTenBuuTa());
        result.setDanhGia(data.getLoaiCanhBao());
        result.setLoaiDichVu(data.getNhomDV());
        result.setTenBaoCao("Tồn Thu");
        return result;
    }


    public List<String> buuTaTheoBC(String chiNhanh, String buuCuc, LocalDate ngayBaoCao) {
        List<String> result = new ArrayList<>();

        Long version = getChiSoVersion(ngayBaoCao,"ton_thu");
        if (isViewTonThu()) {
            if (version != null)
                result = thuBillRepo.listBuuTaTonThu(ngayBaoCao, chiNhanh, buuCuc, getCa(), version);
        }
        else {
            if (!UserContext.getUserData().getListChiNhanhVeriable().contains(chiNhanh) || !UserContext.getUserData().getListBuuCucVeriable().contains(buuCuc))
                return result;
            else{
                if(version != null)
                    result = thuBillRepo.listBuuTaTonThu(ngayBaoCao, chiNhanh, buuCuc, getCa(), version);
            }
        }
        return result;
    }

    public ListContentPageDto<TonThuTheoKHResponseDTO> findAllDataBT(List<String> chiNhanhNhan, List<String> buuCucNhan, LocalDate ngayBaoCao, List<String> loaiCanhBao, Integer pageQuery, Integer pageSize) {
        Pageable page = Pageable.unpaged();

        switch (sort) {
            case "":
                page = PageRequest.of(pageQuery, pageSize);
                break;
            case "asc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "tong"));
                break;
            case "desc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tong"));
                break;
            default:
                break;
        }

        String ca = getCa();
        Page<TonThuTheoKHResponseDTO> pageResult;
        if (loaiCanhBao.isEmpty() || loaiCanhBao == null)
            loaiCanhBao = Arrays.asList("XANH", "VANG", "DO_1", "DO_2", "DO_3");

        if (buuCucNhan == null || buuCucNhan.isEmpty()) {
            if (chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                if (isAdmin()) {
                    pageResult = tTKHrepo.TonThuTheoKHTCT(ngayBaoCao, loaiCanhBao, ca, page);
                } else {
                    chiNhanhNhan = UserContext.getUserData().getListChiNhanhVeriable();
                    buuCucNhan = UserContext.getUserData().getListBuuCucVeriable();
                    pageResult = tTKHrepo.TonThuTheoKHBC1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, ca, page);
//                    pageResult = tTKHrepo.TonThuTheoKHTCT1(chiNhanhNhan, ngayBaoCao, loaiCanhBao, ca, page);
                }
            } else {
                if (isAdmin()) {
                    pageResult = tTKHrepo.TonThuTheoKHBC(chiNhanhNhan, ngayBaoCao, loaiCanhBao, ca, page);
                } else {
                    chiNhanhNhan.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                    buuCucNhan = UserContext.getUserData().getListBuuCucVeriable();
                    pageResult = tTKHrepo.TonThuTheoKHBC1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, ca, page);
                }
            }
        } else {
            if (!isAdmin()) {
                chiNhanhNhan.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                buuCucNhan.retainAll(UserContext.getUserData().getListBuuCucVeriable());
            }
            pageResult = tTKHrepo.TonThuTheoKHBT(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, ca, page);
        }
        long versionTonThu = chiSoVersionRepo.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaoCao, "ton_thu")
                .map(ChiSoVersion::getVersion)
                .orElse(0L);

        String timeTinhToan = tTKHrepo.getTimeTinhToan(versionTonThu,ngayBaoCao);
        ListContentPageDto<TonThuTheoKHResponseDTO> listContent = new ListContentPageDto<>(pageResult);
        listContent.setTime(timeTinhToan);

        return listContent;
//        return new ListContentPageDto<TonThuTheoKHResponseDTO>(pageResult);
    }

    public List<TonThuTheoKHResponseDTO> getDataSum(List<String> chiNhanhNhan, List<String> buuCucNhan, LocalDate ngayBaoCao, List<String> loaiCanhBao) {
        String ca = getCa();

        if (loaiCanhBao.isEmpty() || loaiCanhBao == null)
            loaiCanhBao = Arrays.asList("XANH", "VANG", "DO_1", "DO_2", "DO_3");

        if (buuCucNhan == null || buuCucNhan.isEmpty()) {
            if (chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                if (isAdmin()) {
                    return tTKHrepo.TonThuTheoKHSum(ngayBaoCao, loaiCanhBao, ca);
                } else {
                    chiNhanhNhan = UserContext.getUserData().getListChiNhanhVeriable();
                    buuCucNhan = UserContext.getUserData().getListBuuCucVeriable();
                    return tTKHrepo.TonThuTheoKHBCSum1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, ca);
                }
            } else {
                if (isAdmin()) {
                    return tTKHrepo.TonThuTheoKHBCSum(chiNhanhNhan, ngayBaoCao, loaiCanhBao, ca);
                } else {
                    chiNhanhNhan.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                    buuCucNhan = UserContext.getUserData().getListBuuCucVeriable();
                    return tTKHrepo.TonThuTheoKHBCSum1(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, ca);
                }
            }
        } else {
            if (!isAdmin()) {
                chiNhanhNhan.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                buuCucNhan.retainAll(UserContext.getUserData().getListBuuCucVeriable());
            }
            return tTKHrepo.TonThuTheoKHBTSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao, ca);
        }
    }

    public List<TonThuTop10DoResDTO> getTop10Do(String chiNhanhNhan, LocalDate ngayBaoCao, String loaiBC) {

        Table<String, String, Long> tonThu = getDataTop10(chiNhanhNhan, ngayBaoCao, loaiBC);

        List<TonThuTop10DoResDTO> result = new ArrayList<>();

        Set<String> listDonVi = tonThu.rowKeySet();
        Long do1;
        Long do2;
        Long do3;
        Long tong;
        for (String set : listDonVi) {
            if (tonThu.get(set, "DO_1") != null)
                do1 = tonThu.get(set, "DO_1");
            else
                do1 = (long) 0;

            if (tonThu.get(set, "DO_2") != null)
                do2 = tonThu.get(set, "DO_2");
            else
                do2 = (long) 0;

            if (tonThu.get(set, "DO_3") != null)
                do3 = tonThu.get(set, "DO_3");
            else
                do3 = (long) 0;
            tong = do1 + do2 + do3;
            TonThuTop10DoResDTO element = new TonThuTop10DoResDTO(set, do1, do2, do3, tong);
            result.add(element);
        }
        Collections.sort(result, Collections.reverseOrder());

        return result.stream().limit(10).collect(Collectors.toList());
    }

    private Table<String, String, Long> getDataTop10(String chiNhanhNhan, LocalDate ngayBaoCao, String loaiBC) {
        String ca = getCa();
        List<String> cn = List.of(chiNhanhNhan);
//        Table<String, String, Float> tonThu = HashBasedTable.create();
        List<String> listLoaiCanhBao = Arrays.asList("DO_1", "DO_2", "DO_3");

        if (chiNhanhNhan.equals("")) {
            if (isAdmin()) {
                List<TonThuTheoKHResponseDTO> list = tTKHrepo.TonThuTheoKHDashTop10TCT(ngayBaoCao, ca, listLoaiCanhBao);
                return putDataToTable(list, loaiBC, "TCT");
            } else {
                List<TonThuTheoKHResponseDTO> list = tTKHrepo.TonThuTheoKHDashTop10TCT1(ngayBaoCao, UserContext.getUserData().getListChiNhanhVeriable(), ca, listLoaiCanhBao);
                return putDataToTable(list, loaiBC, "TCT");
            }
        } else {
            if (!isAdmin()) {
                cn.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                List<TonThuTheoKHResponseDTO> list = tTKHrepo.TonThuTheoKHDashTop10CN1(ngayBaoCao, cn, UserContext.getUserData().getListBuuCucVeriable(), ca, listLoaiCanhBao);
                return putDataToTable(list, loaiBC, "CN");
            } else {
                List<TonThuTheoKHResponseDTO> list = tTKHrepo.TonThuTheoKHDashTop10CN(ngayBaoCao, chiNhanhNhan, ca, listLoaiCanhBao);
                return putDataToTable(list, loaiBC, "CN");
            }
        }
    }

    private Table<String, String, Long> putDataToTable(List<TonThuTheoKHResponseDTO> list, String loaiBC, String capBC) {
        Table<String, String, Long> tonThu = HashBasedTable.create();
        if (loaiBC.equals("MUC_DO")) {
            if (capBC.equals("TCT")) {
                for (TonThuTheoKHResponseDTO tt : list) {
                    tonThu.put(tt.getChiNhanhNhan(), tt.getLoaiCanhBao(), tt.getTong());
                }
            } else {
                for (TonThuTheoKHResponseDTO tt : list) {
                    tonThu.put(tt.getBuuCucNhan(), tt.getLoaiCanhBao(), tt.getTong());
                }
            }
            return tonThu;
        } else if (loaiBC.equals("KH_VIP")) {
            if (capBC.equals("TCT")) {
                for (TonThuTheoKHResponseDTO tt : list) {
                    tonThu.put(tt.getChiNhanhNhan(), tt.getLoaiCanhBao(), tt.getKhachHang5());
                }
            } else {
                for (TonThuTheoKHResponseDTO tt : list) {
                    tonThu.put(tt.getBuuCucNhan(), tt.getLoaiCanhBao(), tt.getKhachHang5());
                }
            }
            return tonThu;
        }
        return tonThu;
    }

    public List<TonThuTheoKHResponseDTO> TonThuTheoKHSum(List<String> chiNhanhNhan, List<String> buuCucNhan, LocalDate ngayBaoCao, List<String> loaiCanhBao) {

        List<TonThuTheoKHResponseDTO> tTKH = getDataSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao);
        Long kh0 = Long.valueOf(0);
        Long kh1 = Long.valueOf(0);
        Long kh2 = Long.valueOf(0);
        Long kh3 = Long.valueOf(0);
        Long kh4 = Long.valueOf(0);
        Long kh5 = Long.valueOf(0);
        Long kh6 = Long.valueOf(0);
        Long kh7 = Long.valueOf(0);
        Long kh8 = Long.valueOf(0);
        Long tong = Long.valueOf(0);


        for (TonThuTheoKHResponseDTO tGNLXResponse : tTKH) {

            kh0 += tGNLXResponse.getKhachHang0();
            kh1 += tGNLXResponse.getKhachHang1();
            kh2 += tGNLXResponse.getKhachHang2();
            kh3 += tGNLXResponse.getKhachHang3();
            kh4 += tGNLXResponse.getKhachHang4();
            kh5 += tGNLXResponse.getKhachHang5();
            kh6 += tGNLXResponse.getKhachHang6();
            kh7 += tGNLXResponse.getKhachHang7();
            kh8 += tGNLXResponse.getKhachHang8();
            tong += tGNLXResponse.getTong();
        }

        TonThuTheoKHResponseDTO tTKHResponse = new TonThuTheoKHResponseDTO();

        tTKHResponse.setKhachHang0(kh0);
        tTKHResponse.setKhachHang1(kh1);
        tTKHResponse.setKhachHang2(kh2);
        tTKHResponse.setKhachHang3(kh3);
        tTKHResponse.setKhachHang4(kh4);
        tTKHResponse.setKhachHang5(kh5);
        tTKHResponse.setKhachHang6(kh6);
        tTKHResponse.setKhachHang7(kh7);
        tTKHResponse.setKhachHang8(kh8);
        tTKHResponse.setTong(tong);

        List<TonThuTheoKHResponseDTO> listKH = new ArrayList<>();
        listKH.add(tTKHResponse);

        return listKH;
    }

    public List<TonThuTop10CGTResDTO> getDataTop10CGT(String chiNhanhNhan, LocalDate ngayBaoCao) {
        List<TonThuTop10CGTResDTO> list;
        List<String> cn = List.of(chiNhanhNhan);
        if (chiNhanhNhan.equals("")) {
            if (isAdmin()) {
                list = tTKHrepo.TonThuDashTop10TuyenTCT(ngayBaoCao, getCa());
            } else {
                list = tTKHrepo.TonThuDashTop10TuyenTCT1(ngayBaoCao, UserContext.getUserData().getListChiNhanhVeriable(), getCa());
            }
        } else {
            if (!isAdmin()) {
                cn.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                list = tTKHrepo.TonThuDashTop10TuyenCN1(ngayBaoCao, cn, UserContext.getUserData().getListBuuCucVeriable(), getCa());
            } else {
                list = tTKHrepo.TonThuDashTop10TuyenCN(ngayBaoCao, chiNhanhNhan, getCa());
            }
        }

        Collections.sort(list, Collections.reverseOrder());
        return list.stream().limit(10).collect(Collectors.toList());
    }

    private static final List<String> loaiCanhBao = Arrays.asList("XANH", "VANG", "DO_1", "DO_2", "DO_3");

    public List<TonThuTheoKHResponseDTO> findAllDataCase(List<String> buuCucNhan, LocalDate ngayBaoCao, List<String> chiNhanhNhan) {

        String ca = getCa();

        List<TonThuTheoKHResponseDTO> tonThuTheoKhachHang = null;
        List<TonThuTheoKHResponseDTOSum> tonThuTheoKhachHang1 = null;
        if (buuCucNhan == null || buuCucNhan.isEmpty()) {
            if (chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                if (isAdmin()) {
                    tonThuTheoKhachHang = tTKHrepo.TonThuTheoKHDashTCT(ca, ngayBaoCao);
                } else {
                    chiNhanhNhan = UserContext.getUserData().getListChiNhanhVeriable();
                    tonThuTheoKhachHang1 = tTKHrepo.TonThuTheoKHDashTCT01(chiNhanhNhan, ca, ngayBaoCao);
                }
            } else {
                if (isAdmin()) {
                    tonThuTheoKhachHang = tTKHrepo.TonThuTheoKHDashCN(ca, chiNhanhNhan, ngayBaoCao);
                } else {
                    chiNhanhNhan.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                    buuCucNhan = UserContext.getUserData().getListBuuCucVeriable();
                    tonThuTheoKhachHang1 = tTKHrepo.TonThuTheoKHDashCN01(buuCucNhan, ca, chiNhanhNhan, ngayBaoCao);
                }
            }
        } else {
            if (!isAdmin()) {
                if (chiNhanhNhan != null) {
                    chiNhanhNhan.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
                    buuCucNhan.retainAll(UserContext.getUserData().getListBuuCucVeriable());
                }
            }
            tonThuTheoKhachHang = tTKHrepo.TonThuTheoKHDashBC(ca, buuCucNhan, chiNhanhNhan, ngayBaoCao);
        }
        if (tonThuTheoKhachHang != null) {
            return tonThuTheoKhachHang;
        }

        List<TonThuTheoKHResponseDTO> ttCast = null;
        if (tonThuTheoKhachHang1 != null) {
            ttCast = tonThuTheoKhachHang1.stream().map(t ->
            {
                TonThuTheoKHResponseDTO tonThu = new TonThuTheoKHResponseDTO();
                BeanUtils.copyProperties(t, tonThu);
                tonThu.setLoaiCanhBao(t.getDonVi());
                return tonThu;
            }).collect(Collectors.toList());
        }
        return ttCast;
    }

    public void sortByLoaiCanhBao(List<TonThuTheoKHResponseDTO> tonTTKHResList) {

        Comparator<TonThuTheoKHResponseDTO> loaiCanhBaoComparator = (t1, t2) -> Integer.compare(loaiCanhBao.indexOf(t1.getLoaiCanhBao()),
                loaiCanhBao.indexOf(t2.getLoaiCanhBao()));
        tonTTKHResList.sort(loaiCanhBaoComparator);
    }

    public List<TonThuTheoKHResponseDTO> addDefaulJson(List<TonThuTheoKHResponseDTO> tonTTKHResList) {
        int flag1 = 0, flag2 = 0, flag3 = 0, flag4 = 0, flag5 = 0;
        for (TonThuTheoKHResponseDTO ttKh : tonTTKHResList) {
            if (ttKh.getLoaiCanhBao().contentEquals("XANH")) {
                flag1 = 1;
            } else if (ttKh.getLoaiCanhBao().contentEquals("VANG")) {
                flag2 = 1;
            } else if (ttKh.getLoaiCanhBao().contentEquals("DO_1")) {
                flag3 = 1;
            } else if (ttKh.getLoaiCanhBao().contentEquals("DO_2")) {
                flag4 = 1;
            } else if (ttKh.getLoaiCanhBao().contentEquals("DO_3")) {
                flag5 = 1;
            }
        }
        if (flag1 == 0) {
            tonTTKHResList.add(new TonThuTheoKHResponseDTO(null, "", "", "", "XANH", 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L));
        }
        if (flag2 == 0) {
            tonTTKHResList.add(new TonThuTheoKHResponseDTO(null, "", "", "", "VANG", 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L));
        }
        if (flag3 == 0) {
            tonTTKHResList.add(new TonThuTheoKHResponseDTO(null, "", "", "", "DO_1", 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L));
        }
        if (flag4 == 0) {
            tonTTKHResList.add(new TonThuTheoKHResponseDTO(null, "", "", "", "DO_2", 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L));
        }
        if (flag5 == 0) {
            tonTTKHResList.add(new TonThuTheoKHResponseDTO(null, "", "", "", "DO_3", 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L, 0L));
        }
        return tonTTKHResList;
    }


    public List<TonThuTheoKHResponseDTO> tinhTongThuColumn(List<TonThuTheoKHResponseDTO> tonThuTheoKHResponseDTO) {

        Long res0 = 0L;
        Long res1 = 0L;
        Long res2 = 0L;
        Long res3 = 0L;
        Long res4 = 0L;
        Long res5 = 0L;
        Long res6 = 0L;
        Long res7 = 0L;
        Long res8 = 0L;
        Long res9 = 0L;

        for (TonThuTheoKHResponseDTO res : tonThuTheoKHResponseDTO) {
            res0 += res.getKhachHang0();
            res1 += res.getKhachHang1();
            res2 += res.getKhachHang2();
            res3 += res.getKhachHang3();
            res4 += res.getKhachHang4();
            res5 += res.getKhachHang5();
            res6 += res.getKhachHang6();
            res7 += res.getKhachHang7();
            res8 += res.getKhachHang8();
            res9 += res.getTong();
        }
        TonThuTheoKHResponseDTO ttKhRespon = new TonThuTheoKHResponseDTO();
        ttKhRespon.setLoaiCanhBao("TONG");
        ttKhRespon.setKhachHang0(res0);
        ttKhRespon.setKhachHang1(res1);
        ttKhRespon.setKhachHang2(res2);
        ttKhRespon.setKhachHang3(res3);
        ttKhRespon.setKhachHang4(res4);
        ttKhRespon.setKhachHang5(res5);
        ttKhRespon.setKhachHang6(res6);
        ttKhRespon.setKhachHang7(res7);
        ttKhRespon.setKhachHang8(res8);
        ttKhRespon.setTong(res9);
        tonThuTheoKHResponseDTO.add(ttKhRespon);
        return tonThuTheoKHResponseDTO;

    }

    public void exportExcelTTKH(HttpServletResponse response,
                                List<String> chiNhanhNhan,
                                List<String> buuCucNhan,
                                LocalDate ngayBaoCao,
                                List<String> loaiCanhBao) throws IOException {
        List<TonThuTheoKHResponseDTO> tonThuKHExcel = getDataSum(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao);
        TonThuTheoKHExcelExporter tTTheoKHExcelExporter = new TonThuTheoKHExcelExporter(tonThuKHExcel);
        tTTheoKHExcelExporter.exportDataToExcel(response, chiNhanhNhan, buuCucNhan);
    }

    public TonThuTop10DoResDTO getDataNoti(LocalDate ngayBaoCao, String loaiCanhBao, String doiTuongCanhBao, String chiNhanh, String buuCucNhan) {
        String ca = getCa();
        List<String> tenDonVi = new ArrayList<>();
        List<Long> slDonVi = new ArrayList<>();
        List<String> listLoaiCanhBao = Arrays.asList(loaiCanhBao.split(","));
        List<TonThuTheoKHResponseDTO> list = new ArrayList<>();
        List<TonThuTheoKHResponseDTO> listTop10;
        Table<String, String, Long> table = HashBasedTable.create();

        switch (doiTuongCanhBao) {
            case "BAN_TGD":
                list = tTKHrepo.getDataNotiTGD(ngayBaoCao, ca, listLoaiCanhBao);
                listTop10 = tTKHrepo.TonThuTheoKHDashTop10TCT(ngayBaoCao, ca, listLoaiCanhBao);
                for (TonThuTheoKHResponseDTO e : listTop10) {
                    table.put(e.getChiNhanhNhan(), e.getLoaiCanhBao(), e.getTong());
                }
                break;
            case "BAN_GIAM_DOC_CHI_NHANH":
                list = tTKHrepo.getDataNotiCN(ngayBaoCao, chiNhanh, ca, listLoaiCanhBao);
                listTop10 = tTKHrepo.TonThuTheoKHDashTop10CN(ngayBaoCao, chiNhanh, ca, listLoaiCanhBao);
                for (TonThuTheoKHResponseDTO e : listTop10) {
                    table.put(e.getBuuCucNhan(), e.getLoaiCanhBao(), e.getTong());
                }
                break;
            case "TRUONG_BUU_CUC":
                list = tTKHrepo.getDataNotiBC(ngayBaoCao, chiNhanh, buuCucNhan, ca, listLoaiCanhBao);
                listTop10 = tTKHrepo.TonThuTheoKHDashTop10BC(ngayBaoCao, chiNhanh, buuCucNhan, ca, listLoaiCanhBao);
                for (TonThuTheoKHResponseDTO e : listTop10) {
                    table.put(e.getBuuTaNhan(), e.getLoaiCanhBao(), e.getTong());
                }
                break;
            default:
                break;
        }

        Set<String> listDonVi = table.rowKeySet();
        List<TonThuTheoKHResponseDTO> top10 = new ArrayList<>();
        Long tong;
        for (String set : listDonVi) {
            tong = 0L;
            TonThuTheoKHResponseDTO temp = new TonThuTheoKHResponseDTO();
            for (String s : listLoaiCanhBao) {
                if (table.get(set, s) != null)
                    tong += table.get(set, s);
                else
                    tong += 0L;
            }
            temp.setTong(tong);
            temp.setChiNhanhNhan(set);
            top10.add(temp);
        }

        Collections.sort(top10, Collections.reverseOrder());
        Integer count = 0;
//        while (count < 10 && count < top10.size()) {
//            tenDonVi[count] = top10.get(count).getChiNhanhNhan();
//            slDonVi[count] = top10.get(count).getTong();
//            count++;
//        }

        for (TonThuTheoKHResponseDTO top : top10) {
            if (count == 10)
                break;
            tenDonVi.add(top.getChiNhanhNhan());
            slDonVi.add(top.getTong());
            count++;
        }

        Map<String, Long> dataMap = new HashMap<>();
        for (TonThuTheoKHResponseDTO c : list) {
            dataMap.put(c.getLoaiCanhBao(), c.getTong());
        }

        TonThuTop10DoResDTO response = new TonThuTop10DoResDTO();
        response.setXanh(listLoaiCanhBao.contains("XANH") ? dataMap.get("XANH") : 0L);
        response.setVang(listLoaiCanhBao.contains("VANG") ? dataMap.get("VANG") : 0L);
        response.setDo_1(listLoaiCanhBao.contains("DO_1") ? dataMap.get("DO_1") : 0L);
        response.setDo_2(listLoaiCanhBao.contains("DO_2") ? dataMap.get("DO_2") : 0L);
        response.setDo_3(listLoaiCanhBao.contains("DO_3") ? dataMap.get("DO_3") : 0L);
        response.setTong(response.getDo_1() + response.getDo_2() + response.getDo_3() + response.getXanh() + response.getVang());
        response.setConChiTieu(response.getVang() + response.getXanh());
        response.setQuaChiTieu(response.getTong() - response.getConChiTieu());

        if (response.getTong() == 0 || response.getTong() == null) {
            response.setTlConChiTieu(0f);
            response.setTlQuaChiTieu(0f);
        } else {
            response.setTlConChiTieu(((float) response.getConChiTieu() / (float) response.getTong()) * 100);
            response.setTlQuaChiTieu(((float) response.getQuaChiTieu() / (float) response.getTong()) * 100);
        }
        response.setTenTop10(tenDonVi);
        response.setSlTop10(slDonVi);
        return response;
    }

    // 2023-04-24 ngocnt92 write new function to get Data for send email.
    public List<TonThuNotificationDisplayDto> getTop10DataTonThu(LocalDate ngayBaoCao, String loaiCanhBao, String doiTuongCanhBao, String chiNhanh, String buuCucNhan) {
        List<TonThuNotificationDisplayDto> getDataTonThuTop10 = new ArrayList<>();

        String ca = getCa();

        List<String> listLoaiCanhBao = Arrays.asList(loaiCanhBao.split(","));
        List<TonThuTheoKHResponseDTO> list = new ArrayList<>();
        List<TonThuTheoKHResponseDTO> listTop10 = new ArrayList<>();
        Map<String, Long> mapDataTop10 = new HashMap<>();
        int res = 0;
        TonThuNotificationDisplayDto notificationDisplayDto = new TonThuNotificationDisplayDto();
        if (!listLoaiCanhBao.isEmpty()) {
            switch (doiTuongCanhBao) {
                case "BAN_TGD":
                    list = tTKHrepo.getDataNotiTGD(ngayBaoCao, ca, listLoaiCanhBao);
                    listTop10 = tTKHrepo.TonThuTheoKHDashTop10TCT(ngayBaoCao, ca, listLoaiCanhBao);
                    for (int i = 0; i < listTop10.size(); i++) {
                        if (mapDataTop10.containsKey(listTop10.get(i).getChiNhanhNhan())) {
                            mapDataTop10.put(listTop10.get(i).getChiNhanhNhan(), (mapDataTop10.get(listTop10.get(i).getChiNhanhNhan()) + listTop10.get(i).getTong()));
                        } else {
                            mapDataTop10.put(listTop10.get(i).getChiNhanhNhan(), listTop10.get(i).getTong());
                        }
                    }
                    notificationDisplayDto.setTenChiNhanh("Toàn quốc");
                    res = Ban_TGD;
                    break;
                case "BAN_GIAM_DOC_CHI_NHANH":
                    list = tTKHrepo.getDataNotiCN(ngayBaoCao, chiNhanh, ca, listLoaiCanhBao);
                    listTop10 = tTKHrepo.TonThuTheoKHDashTop10CN(ngayBaoCao, chiNhanh, ca, listLoaiCanhBao);
                    for (int i = 0; i < listTop10.size(); i++) {
                        if (mapDataTop10.containsKey(listTop10.get(i).getBuuCucNhan())) {
                            mapDataTop10.put(listTop10.get(i).getBuuCucNhan(), (mapDataTop10.get(listTop10.get(i).getBuuCucNhan()) + listTop10.get(i).getTong()));
                        } else {
                            mapDataTop10.put(listTop10.get(i).getBuuCucNhan(), listTop10.get(i).getTong());
                        }
                    }
                    notificationDisplayDto.setTenChiNhanh("Chi nhánh");
                    if (chiNhanh.equals("HCM") || chiNhanh.equals("HNI") || chiNhanh.equals("CNHCM") || chiNhanh.equals("CNHNI")) {
                        res = BAN_GIAM_DOC_CHI_NHANH_HN_HCM;
                    } else {
                        res = BAN_GIAM_DOC_CHI_NHANH;
                    }

                    break;
                case "TRUONG_BUU_CUC":
                    list = tTKHrepo.getDataNotiBC(ngayBaoCao, chiNhanh, buuCucNhan, ca, listLoaiCanhBao);
                    listTop10 = tTKHrepo.TonThuTheoKHDashTop10BC(ngayBaoCao, chiNhanh, buuCucNhan, ca, listLoaiCanhBao);
                    for (int i = 0; i < listTop10.size(); i++) {
                        if (mapDataTop10.containsKey(listTop10.get(i).getBuuTaNhan())) {
                            mapDataTop10.put(listTop10.get(i).getBuuTaNhan(), (mapDataTop10.get(listTop10.get(i).getBuuTaNhan()) + listTop10.get(i).getTong()));
                        } else {
                            mapDataTop10.put(listTop10.get(i).getBuuTaNhan(), listTop10.get(i).getTong());
                        }
                    }
                    notificationDisplayDto.setTenChiNhanh("Bưu cục");
                    res = TRUONG_BUU_CUC;
                    break;
                default:
                    break;
            }

            notificationDisplayDto.setTongTonThu(list.stream().map(TonThuTheoKHResponseDTO::getTong).reduce(0L, Long::sum));
            notificationDisplayDto.setTrongKpi(list.stream()
                    .filter(l -> l.getLoaiCanhBao().equals("XANH"))
                    .map(TonThuTheoKHResponseDTO::getTong)
                    .reduce(0L, Long::sum) +
                    list.stream()
                            .filter(l -> l.getLoaiCanhBao().equals("VANG"))
                            .map(TonThuTheoKHResponseDTO::getTong)
                            .reduce(0L, Long::sum));
            notificationDisplayDto.setVuotNguong(notificationDisplayDto.getTongTonThu() - notificationDisplayDto.getTrongKpi());
            if (notificationDisplayDto.getTongTonThu() == 0) {
                notificationDisplayDto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * 0)));
            } else {
                notificationDisplayDto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * notificationDisplayDto.getVuotNguong() / notificationDisplayDto.getTongTonThu())));
            }
        }
        getDataTonThuTop10.add(notificationDisplayDto);

        List<Map.Entry<String, Long>> nlist = new ArrayList<>(mapDataTop10.entrySet());
        nlist.sort(Map.Entry.comparingByValue(Comparator.reverseOrder()));
        for (Map.Entry<String, Long> dataTop10Map : nlist) {
            TonThuNotificationDisplayDto dto = new TonThuNotificationDisplayDto();
            dto.setTenChiNhanh(dataTop10Map.getKey());
            dto.setTongTonThu(dataTop10Map.getValue());
            if (doiTuongCanhBao.equals("BAN_TGD")) {
                dto.setTrongKpi(listTop10.stream()
                        .filter(l -> l.getChiNhanhNhan().equals(dataTop10Map.getKey()))
                        .filter(l -> l.getLoaiCanhBao().equals("XANH")).map(TonThuTheoKHResponseDTO::getTong)
                        .reduce(0L, Long::sum) +
                        listTop10.stream()
                                .filter(l -> l.getChiNhanhNhan().equals(dataTop10Map.getKey()))
                                .filter(l -> l.getLoaiCanhBao().equals("VANG")).map(TonThuTheoKHResponseDTO::getTong)
                                .reduce(0L, Long::sum));
            } else if (doiTuongCanhBao.equals("BAN_GIAM_DOC_CHI_NHANH")) {
                dto.setTrongKpi(listTop10.stream()
                        .filter(l -> l.getBuuCucNhan().equals(dataTop10Map.getKey()))
                        .filter(l -> l.getLoaiCanhBao().equals("XANH")).map(TonThuTheoKHResponseDTO::getTong)
                        .reduce(0L, Long::sum) +
                        listTop10.stream()
                                .filter(l -> l.getBuuCucNhan().equals(dataTop10Map.getKey()))
                                .filter(l -> l.getLoaiCanhBao().equals("VANG")).map(TonThuTheoKHResponseDTO::getTong)
                                .reduce(0L, Long::sum));
            } else {
                dto.setTrongKpi(listTop10.stream()
                        .filter(l -> l.getBuuTaNhan().equals(dataTop10Map.getKey()))
                        .filter(l -> l.getLoaiCanhBao().equals("XANH")).map(TonThuTheoKHResponseDTO::getTong)
                        .reduce(0L, Long::sum) +
                        listTop10.stream()
                                .filter(l -> l.getBuuTaNhan().equals(dataTop10Map.getKey()))
                                .filter(l -> l.getLoaiCanhBao().equals("VANG")).map(TonThuTheoKHResponseDTO::getTong)
                                .reduce(0L, Long::sum));
            }

            dto.setVuotNguong(dataTop10Map.getValue() - dto.getTrongKpi());
            if (dto.getTongTonThu() == 0) {
                dto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * 0)));
            } else {
                dto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * dto.getVuotNguong() / dto.getTongTonThu())));
            }
            getDataTonThuTop10.add(dto);
        }
        if (res == Ban_TGD || res == BAN_GIAM_DOC_CHI_NHANH_HN_HCM) {
            getDataTonThuTop10 = getDataTonThuTop10.subList(0, Math.min(getDataTonThuTop10.size(), 11));
        } else if (res == TRUONG_BUU_CUC || res == BAN_GIAM_DOC_CHI_NHANH) {
            getDataTonThuTop10 = getDataTonThuTop10.subList(0, Math.min(getDataTonThuTop10.size(), 4));
        }
        return getDataTonThuTop10;
    }


    public Long getChiSoVersion(LocalDate ngayBaoCao, String maChiSo) {
        Optional<ChiSoVersion> chiSoVersionEntity = chiSoVersionRepo.findChiSoVersionByNgayBaoCaoAndMaChiSo(ngayBaoCao, maChiSo);
        return chiSoVersionEntity.map(ChiSoVersion::getVersion).orElse(null);
    }

    public void exportExcelTTNoti(HttpServletResponse response,
                                  String chiNhanhNhan,
                                  String buuCucNhan,
                                  LocalDate ngayBaoCao,
                                  List<String> loaiCanhBao) throws IOException {
        String ca = getCa();
        List<TonThuNotiExcelDTO> tonThuKHExcel;
        if (buuCucNhan.equals("")) {
            if (chiNhanhNhan.equals(""))
                tonThuKHExcel = tTKHrepo.getDataNotiExcelCN(ngayBaoCao, ca, loaiCanhBao);
            else
                tonThuKHExcel = tTKHrepo.getDataNotiExcelBC(ngayBaoCao, chiNhanhNhan, ca, loaiCanhBao);
        } else {
            tonThuKHExcel = tTKHrepo.getDataNotiExcelTuyen(ngayBaoCao, buuCucNhan, ca, loaiCanhBao);
        }

        List<TonThuNotiExcelDTO> listData2Populate = new ArrayList<>();
        Table<String, String, Long> dataTable = HashBasedTable.create();
        for (TonThuNotiExcelDTO tt : tonThuKHExcel) {
            dataTable.put(tt.getDonVi(), tt.getLoaiCanhBao(), tt.getTong());
        }

        Set<String> listDonVi = dataTable.rowKeySet();
        for (String str : listDonVi) {
            TonThuNotiExcelDTO tt = new TonThuNotiExcelDTO();
            tt.setDonVi(str);
            for (String cb : this.loaiCanhBao) {
                switch (cb) {
                    case "XANH":
                        tt.setXanh(dataTable.get(str, cb) != null ? dataTable.get(str, cb) : 0);
                        break;
                    case "VANG":
                        tt.setVang(dataTable.get(str, cb) != null ? dataTable.get(str, cb) : 0);
                        break;
                    case "DO_1":
                        tt.setDo1(dataTable.get(str, cb) != null ? dataTable.get(str, cb) : 0);
                        break;
                    case "DO_2":
                        tt.setDo2(dataTable.get(str, cb) != null ? dataTable.get(str, cb) : 0);
                        break;
                    case "DO_3":
                        tt.setDo3(dataTable.get(str, cb) != null ? dataTable.get(str, cb) : 0);
                        break;
                    default:
                        break;
                }
            }
            tt.setTong(tt.getVang() + tt.getXanh() + tt.getDo1() + tt.getDo2() + tt.getDo3());
            listData2Populate.add(tt);
        }
        TonThuNotificationExcelExporter tTTheoKHExcelExporter = new TonThuNotificationExcelExporter(listData2Populate, loaiCanhBao);
        tTTheoKHExcelExporter.exportDataToExcel(response);
    }

    public void exportExcelBill(HttpServletResponse response,
                                List<String> chiNhanh,
                                String vungCon,
                                List<String> buuCuc,
                                List<String> buuTaNhan,
                                LocalDate ngayBaoCao,
                                List<String> loaiCanhBao, String trangThai, String maDoiTac, String loaiHH, String khDacThuGui,
                                String khDacThuNhan,
                                String maKhGui) throws IOException {
        if (!isAdmin() && !isViewTonThu() && !UserContext.getUserData().getIsRoleMKTTT().equalsIgnoreCase("true")) {
            if (!chiNhanh.isEmpty())
                chiNhanh.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
            else
                chiNhanh = UserContext.getUserData().getListChiNhanhVeriable();
            if (!buuCuc.isEmpty()) {
                if (!buuCuc.get(0).equalsIgnoreCase("CHUA_XAC_DINH"))
                    buuCuc.retainAll(UserContext.getUserData().getListBuuCucVeriable());
            } else {
                buuCuc = UserContext.getUserData().getListBuuCucVeriable();
                if (UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("TRUE")) {
                    buuCuc = new ArrayList<>();
                }
            }
        }


        Long version = getChiSoVersion(ngayBaoCao, "ton_thu");

        List<TonThuBillResponseDTO> tonThuKHExcel = new ArrayList<>();
        if (version != null) {
            tonThuKHExcel = thuBillRepo.findTonTTKTBill(ngayBaoCao, chiNhanh, vungCon, buuCuc, buuTaNhan, loaiCanhBao, trangThai, getCa(), maDoiTac, loaiHH, khDacThuGui, khDacThuNhan, maKhGui, version);
        }
        TonThuBillExcelExporter tTTheoKHExcelExporter = new TonThuBillExcelExporter(tonThuKHExcel);
        tTTheoKHExcelExporter.exportDataToExcel(response);
    }
}
