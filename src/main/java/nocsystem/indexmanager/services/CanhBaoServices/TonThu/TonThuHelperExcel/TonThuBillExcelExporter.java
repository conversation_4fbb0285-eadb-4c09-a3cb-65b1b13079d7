package nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuHelperExcel;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuBillResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static nocsystem.indexmanager.config.DateTimeUltis.convertMilliSecondsToDate;

public class TonThuBillExcelExporter {
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private final List<TonThuBillResponseDTO> tonThuKHDto;

    private static final List<String> listHeader = Arrays.asList("MA_PHIEUGUI", "TRANG_THAI", "MA_DOITAC", "NGAY_TAO", "NGAY_DUYET", "NGAY_HEN_THU",
            "TINH_KHGUI", "TEN_KHGUI", "TINH_KHNHAN", "MA_BUUCUC", "CHI_NHANH", "MA_DV_VIETTEL", "TONG_TIEN", "BUUTA_NHAN",
            "TEN_BUU_TA", "TG_TON", "NGAY_TON", "LOAI_CANH_BAO", "NHOM_DV", "TIME_TINH_TOAN", "KH_DAC_THU_GUI", "KH_DAC_THU_NHAN", "MA_KHACH_HANG_GUI",
            "ID_PHUONGXA_NHAN",
            "TEN_PHUONGXA_NHAN",
            "DON_DROPOFF", "TEN_QUANHUYEN_NHAN", "MA_DV_CONGTHEM",
            "TIME_102_CUOICUNG", "SO_LAN_102_LUY_KE", "SO_LAN_102_TRONG_NGAY"
            );

    public TonThuBillExcelExporter(List<TonThuBillResponseDTO> tonThuKHDto) {
        workbook = new SXSSFWorkbook(10000);
        this.tonThuKHDto = tonThuKHDto;
    }

    private void createCell(SXSSFRow row, int columnCount, Object value, CellStyle style) {
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRowTCT() {
        sheet = workbook.createSheet("Ton Thu Chi Tiet");
        workbook.setCompressTempFiles(true);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        SXSSFRow row = sheet.createRow(0);

        for (int i = 0; i < listHeader.size(); i++) {
            createCell(row, i, listHeader.get(i), style);
            sheet.setColumnWidth(i, ((int) (listHeader.get(i).length() * 1.5)) * 320);
        }
    }

    public void writeCustomDataTCT() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();

        for (TonThuBillResponseDTO tonThuKH : tonThuKHDto) {
            SXSSFRow row = sheet.createRow(rowCount++);
            int columnCount = 0;
            createCell(row, columnCount++, tonThuKH.getMaPhieuGui(), style);
            createCell(row, columnCount++, tonThuKH.getTrangThai(), style);
            createCell(row, columnCount++, tonThuKH.getMaDoiTac(), style);
            createCell(row, columnCount++, tonThuKH.getNgayTao(), style);
            createCell(row, columnCount++, tonThuKH.getNgayDuyet(), style);
            createCell(row, columnCount++, tonThuKH.getNgayHenThu(), style);
            createCell(row, columnCount++, tonThuKH.getTinhKHGui(), style);
            createCell(row, columnCount++, tonThuKH.getTenKHGui(), style);
//            createCell(row, columnCount++, tonThuKH.getDiaChiKHGui(), style);
            createCell(row, columnCount++, tonThuKH.getTinhKHNhan(), style);
            createCell(row, columnCount++, tonThuKH.getMaBuuCuc(), style);
            createCell(row, columnCount++, tonThuKH.getChiNhanh(), style);
            createCell(row, columnCount++, tonThuKH.getMaDVViettel(), style);
            createCell(row, columnCount++, tonThuKH.getTongTien(), style);
            createCell(row, columnCount++, tonThuKH.getBuuTaNhan(), style);
            createCell(row, columnCount++, tonThuKH.getTenBuuTa(), style);
            createCell(row, columnCount++, tonThuKH.getTgTon(), style);
            createCell(row, columnCount++, tonThuKH.getNgayTon(), style);
            createCell(row, columnCount++, tonThuKH.getLoaiCanhBao(), style);
            createCell(row, columnCount++, tonThuKH.getNhomDV(), style);
            createCell(row, columnCount++, tonThuKH.getTimeTinhToan(), style);
            createCell(row, columnCount++, tonThuKH.getKhDacThuGui(), style);
            createCell(row, columnCount++, tonThuKH.getKhDacThuNhan(), style);
            createCell(row, columnCount++, tonThuKH.getMaKhGui(), style);
            createCell(row, columnCount++, tonThuKH.getIdPhuongXaNhan(), style);
            createCell(row, columnCount++, tonThuKH.getTenPhuongXaNhan(), style);
            createCell(row, columnCount++, tonThuKH.getLoaiDon(), style);
            createCell(row, columnCount++, tonThuKH.getTenQuanHuyenNhan(), style);
            createCell(row, columnCount++, tonThuKH.getMaDichVuCongThem(), style);
            createCell(row, columnCount++,
                    isValidTime(tonThuKH.getTime102CuoiCung()) ?
                    convertMilliSecondsToDate(tonThuKH.getTime102CuoiCung())
                    : tonThuKH.getTime102CuoiCung(), style);
            createCell(row, columnCount++, tonThuKH.getSoLan102LuyKe(), style);
            createCell(row, columnCount++, tonThuKH.getSoLan102TrongNgay(), style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
        createHeaderRowTCT();
        writeCustomDataTCT();
        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        }
    }

    private boolean isValidTime(String time) {
        return !time.equalsIgnoreCase("CHUA_XAC_DINH") &&
                !time.equalsIgnoreCase("null") &&
                !time.isEmpty();
    }
}
