package nocsystem.indexmanager.services.CanhBaoServices.TonThu.TonThuHelperExcel;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class TonThuTheoKHExcelExporter {
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private final List<TonThuTheoKHResponseDTO> tonThuKHDto;

    public TonThuTheoKHExcelExporter(List<TonThuTheoKHResponseDTO> tonThuKHDto) {
        workbook = new SXSSFWorkbook(10000);
        this.tonThuKHDto = tonThuKHDto;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style){
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer){
            cell.setCellValue((Integer) value);
        }else if (value instanceof Double){
            cell.setCellValue((Double) value);
        }else if (value instanceof Boolean){
            cell.setCellValue((Boolean) value);
        }else if (value instanceof Long){
            cell.setCellValue((Long) value);
        }else if(value instanceof String){
            cell.setCellValue((String) value);
        }else if(value instanceof Date){
            cell.setCellValue((Date) value);
        }else if(value instanceof Float){
            cell.setCellValue((Float) value);
        }else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRowTCT(){
        sheet   = workbook.createSheet("TonThuTheoKH_TCT");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Chi nhánh nhận", style);
        createCell(row, 2, "Cấp độ cảnh báo", style);
        createCell(row, 3, "Đối tượng khách hàng", style);
        createCell(row, 4, "", style);
        createCell(row, 5, "", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        createCell(row, 9, "", style);
        createCell(row, 10, "", style);
        createCell(row, 11, "", style);
        createCell(row, 12, "", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 3, 12));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }

        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Chi nhánh nhận", style);
        createCell(row2, 2, "Cấp độ cảnh báo", style);
        createCell(row2, 3, "[0] KH thường", style);
        createCell(row2, 4, "[1] KH khiếu nại gay gắt", style);
        createCell(row2, 5, "[2] KH khiếu nại MXH, báo chí", style);
        createCell(row2, 6, "[3] KH khiếu nại vượt cấp", style);
        createCell(row2, 7, "[4] KH nhà báo, sở, bộ ban hành", style);
        createCell(row2, 8, "[5] KH thân thiết, DT lớn", style);
        createCell(row2, 9, "[6] KH sàn TMĐT, KHHT", style);
        createCell(row2, 10, "[7] KH đối ngoại, ngoại giao", style);
        createCell(row2, 11, "[8] KH Lãnh đạo TCT", style);
        createCell(row2, 12, "Tổng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
//        sheet.setColumnWidth(0,8000);
    }

    private void createHeaderRowCN(){
        sheet   = workbook.createSheet("TonThuTheoKH_CN");
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Chi nhánh nhận", style);
        createCell(row, 2, "Bưu cục nhận", style);
        createCell(row, 3, "Cấp độ cảnh báo", style);
        createCell(row, 4, "Đối tượng khách hàng", style);
        createCell(row, 5, "", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        createCell(row, 9, "", style);
        createCell(row, 10, "", style);
        createCell(row, 11, "", style);
        createCell(row, 12, "", style);
        createCell(row, 13, "", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 13));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }

        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Chi nhánh nhận", style);
        createCell(row2, 2, "Bưu cục nhận", style);
        createCell(row2, 3, "Cấp độ cảnh báo", style);
        createCell(row2, 4, "[0] KH thường", style);
        createCell(row2, 5, "[1] KH khiếu nại gay gắt", style);
        createCell(row2, 6, "[2] KH khiếu nại MXH, báo chí", style);
        createCell(row2, 7, "[3] KH khiếu nại vượt cấp", style);
        createCell(row2, 8, "[4] KH nhà báo, sở, bộ ban hành", style);
        createCell(row2, 9, "[5] KH thân thiết, DT lớn", style);
        createCell(row2, 10, "[6] KH sàn TMĐT, KHHT", style);
        createCell(row2, 11, "[7] KH đối ngoại, ngoại giao", style);
        createCell(row2, 12, "[8] KH Lãnh đạo TCT", style);
        createCell(row2, 13, "Tổng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    private void createHeaderRowBT(){
        sheet   = workbook.createSheet("TonThuTheoKH_BT");
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Row row = sheet.createRow(0);
        createCell(row, 0, "STT", style);
        createCell(row, 1, "Chi nhánh nhận", style);
        createCell(row, 2, "Bưu cục nhận", style);
        createCell(row, 3, "Tuyến bưu tá nhận", style);
        createCell(row, 4, "Cấp độ cảnh báo", style);
        createCell(row, 5, "Đối tượng khách hàng", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        createCell(row, 9, "", style);
        createCell(row, 10, "", style);
        createCell(row, 11, "", style);
        createCell(row, 12, "", style);
        createCell(row, 13, "", style);
        createCell(row, 14, "", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 5, 14));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }

        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Chi nhánh nhận", style);
        createCell(row2, 2, "Bưu cục nhận", style);
        createCell(row2, 3, "Tuyến bưu tá nhận", style);
        createCell(row2, 4, "Cấp độ cảnh báo", style);
        createCell(row2, 5, "[0] KH thường", style);
        createCell(row2, 6, "[1] KH khiếu nại gay gắt", style);
        createCell(row2, 7, "[2] KH khiếu nại MXH, báo chí", style);
        createCell(row2, 8, "[3] KH khiếu nại vượt cấp", style);
        createCell(row2, 9, "[4] KH nhà báo, sở, bộ ban hành", style);
        createCell(row2, 10, "[5] KH thân thiết, DT lớn", style);
        createCell(row2, 11, "[6] KH sàn TMĐT, KHHT", style);
        createCell(row2, 12, "[7] KH đối ngoại, ngoại giao", style);
        createCell(row2, 13, "[8] KH Lãnh đạo TCT", style);
        createCell(row2, 14, "Tổng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 4, 4));

        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    public void writeCustomDataTCT() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();
        Integer soThuTu = new Integer(0);

        for (TonThuTheoKHResponseDTO tonThuKH : tonThuKHDto){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String chiNhanhNhan = tonThuKH.getChiNhanhNhan();
            String loaiCanhBao = tonThuKH.getLoaiCanhBao();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, chiNhanhNhan, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, tonThuKH.getKhachHang0(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang1(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang2(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang3(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang4(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang5(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang6(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang7(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang8(), style);
            createCell(row, columnCount++, tonThuKH.getTong(), style);
        }
    }
    public void writeCustomDataCN() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();

        Integer soThuTu = new Integer(0);
        for (TonThuTheoKHResponseDTO tonThuKH : tonThuKHDto){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String chiNhanhNhan = tonThuKH.getChiNhanhNhan();
            String loaiCanhBao = tonThuKH.getLoaiCanhBao();
            String buuCucNhan = tonThuKH.getBuuCucNhan();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, chiNhanhNhan, style);
            createCell(row, columnCount++, buuCucNhan, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, tonThuKH.getKhachHang0(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang1(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang2(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang3(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang4(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang5(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang6(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang7(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang8(), style);
            createCell(row, columnCount++, tonThuKH.getTong(), style);
        }
    }

    public void writeCustomDataBT() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();

        Integer soThuTu = new Integer(0);
        for (TonThuTheoKHResponseDTO tonThuKH : tonThuKHDto){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String chiNhanhNhan = tonThuKH.getChiNhanhNhan();
            String loaiCanhBao = tonThuKH.getLoaiCanhBao();
            String buuCucNhan = tonThuKH.getBuuCucNhan();
            String buuTaNhan = tonThuKH.getBuuTaNhan();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, chiNhanhNhan, style);
            createCell(row, columnCount++, buuCucNhan, style);
            createCell(row, columnCount++, buuTaNhan, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, tonThuKH.getKhachHang0(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang1(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang2(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang3(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang4(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang5(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang6(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang7(), style);
            createCell(row, columnCount++, tonThuKH.getKhachHang8(), style);
            createCell(row, columnCount++, tonThuKH.getTong(), style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response, List<String> chiNhanhNhan, List<String> buuCucNhan) throws IOException {

        if(buuCucNhan == null || buuCucNhan.isEmpty()){
            if(chiNhanhNhan == null || chiNhanhNhan.isEmpty()) {
                createHeaderRowTCT();
                writeCustomDataTCT();
            }else {
                createHeaderRowCN();
                writeCustomDataCN();
            }
        }else {
            createHeaderRowBT();
            writeCustomDataBT();
        }
        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        }
    }
}
