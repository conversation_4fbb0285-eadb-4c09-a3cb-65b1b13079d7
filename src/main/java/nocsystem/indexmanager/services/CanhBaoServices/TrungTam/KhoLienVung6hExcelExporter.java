package nocsystem.indexmanager.services.CanhBaoServices.TrungTam;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class KhoLienVung6hExcelExporter {

    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private List<KhoLienVung6hResponseDTO> khoLV6h;
    private static final String sheetName = "Kho_LV_6H";
    private static String loaiBaoCao;

    public KhoLienVung6hExcelExporter(List<KhoLienVung6hResponseDTO> khoLV6h) {
        this.workbook = new SXSSFWorkbook();
        this.khoLV6h = khoLV6h;
    }

    public static String getLoaiBaoCao() {
        return loaiBaoCao;
    }

    public static void setLoaiBaoCao(String loaiBaoCao) {
        KhoLienVung6hExcelExporter.loaiBaoCao = loaiBaoCao;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderTCT() {
        sheet = workbook.createSheet(sheetName);
        Row row;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);

        Row row1 = sheet.createRow(0);

        createCell(row1, 0, "STT", style);
        createCell(row1, 1, "Chi Nhánh", style);
        createCell(row1, 2, "Quá 6H", style);
        createCell(row1, 3, "Quá 12H", style);
        createCell(row1, 4, "Quá 24H", style);
        createCell(row1, 5, "Quá 48H", style);
        createCell(row1, 6, "Quá 72H", style);
        createCell(row1, 7, "Quá 96H", style);
        createCell(row1, 8, "Quá 120H", style);
        createCell(row1, 9, "Tổng", style);
        for (int i = 0; i <= row1.getLastCellNum(); i++) {
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    private void writeDataTCT() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();

        Row row;
        int columnCount;

        for (KhoLienVung6hResponseDTO _khoLV6h : khoLV6h) {
            row = sheet.createRow(rowCount++);
            columnCount = 0;
            createCell(row, columnCount++, rowCount - 1, style);
            createCell(row, columnCount++, _khoLV6h.getChiNhanh(), style);
            createCell(row, columnCount++, _khoLV6h.getQua6h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua12h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua24h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua48h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua72h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua96h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua120h(), style);
            createCell(row, columnCount++, _khoLV6h.getTong(), style);
        }
//        long end = System.nanoTime();
//        System.out.print("write: " + (end - start));
    }

    private void createHeaderCN() {
        sheet = workbook.createSheet(sheetName);
        Row row;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);

        Row row1 = sheet.createRow(0);

        createCell(row1, 0, "STT", style);
        createCell(row1, 1, "Chi Nhánh", style);
        createCell(row1, 2, "Bưu Cục", style);
        createCell(row1, 3, "Quá 6H", style);
        createCell(row1, 4, "Quá 12H", style);
        createCell(row1, 5, "Quá 24H", style);
        createCell(row1, 6, "Quá 48H", style);
        createCell(row1, 7, "Quá 72H", style);
        createCell(row1, 8, "Quá 96H", style);
        createCell(row1, 9, "Quá 120H", style);
        createCell(row1, 10, "Tổng", style);
        for (int i = 0; i <= row1.getLastCellNum(); i++) {
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    private void writeDataCN() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();

        Row row;
        int columnCount;

        for (KhoLienVung6hResponseDTO _khoLV6h : khoLV6h) {
            row = sheet.createRow(rowCount++);
            columnCount = 0;
            createCell(row, columnCount++, rowCount - 1, style);
            createCell(row, columnCount++, _khoLV6h.getChiNhanh(), style);
            createCell(row, columnCount++, _khoLV6h.getBuuCuc(), style);
            createCell(row, columnCount++, _khoLV6h.getQua6h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua12h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua24h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua48h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua72h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua96h(), style);
            createCell(row, columnCount++, _khoLV6h.getQua120h(), style);
            createCell(row, columnCount++, _khoLV6h.getTong(), style);
        }
    }

    private void createHeaderBC() {
        sheet = workbook.createSheet(sheetName);
        Row row;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);

        Row row1 = sheet.createRow(0);

        createCell(row1, 0, "STT", style);
        createCell(row1, 1, "Chi Nhánh", style);
        createCell(row1, 2, "Bưu Cục", style);
        createCell(row1, 3, "Mã phiếu gửi", style);
        createCell(row1, 4, "Ngày tạo đơn", style);
        createCell(row1, 5, "Ngày báo cáo", style);
        createCell(row1, 6, "Mốc tồn", style);
        for (int i = 0; i <= row1.getLastCellNum(); i++) {
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    private void writeDataBC() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();

        Row row;
        int columnCount;

        for (KhoLienVung6hResponseDTO _khoLV6h : khoLV6h) {
            row = sheet.createRow(rowCount++);
            columnCount = 0;
            createCell(row, columnCount++, rowCount - 1, style);
            createCell(row, columnCount++, _khoLV6h.getChiNhanh(), style);
            createCell(row, columnCount++, _khoLV6h.getBuuCuc(), style);
            createCell(row, columnCount++, _khoLV6h.getMaPhieuGui(), style);
            createCell(row, columnCount++, _khoLV6h.getNgayTaoDon(), style);
            createCell(row, columnCount++, _khoLV6h.getNgayBaoCao().toString(), style);
            createCell(row, columnCount++, _khoLV6h.getMocTon(), style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
        switch (getLoaiBaoCao()) {
            case "TCT":
                createHeaderTCT();
                writeDataTCT();
                break;
            case "CN":
                createHeaderCN();
                writeDataCN();
                break;
            case "BC":
                createHeaderBC();
                writeDataBC();
                break;
            default:
                break;
        }
        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        }
    }
}
