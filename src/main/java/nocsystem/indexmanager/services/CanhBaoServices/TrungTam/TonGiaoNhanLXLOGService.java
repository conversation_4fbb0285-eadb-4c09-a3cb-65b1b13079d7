package nocsystem.indexmanager.services.CanhBaoServices.TrungTam;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotificationDisplayDto;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatNguongNewResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonTTKTBillResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.ListBCCNRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.TonGiaoNhanLXLOGRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.TonTTKTBillRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TonGiaoNhanLXLOGService {

    private final TonGiaoNhanLXLOGRepository tGNLXrepo;
    private final TonTTKTBillRepository TTKTBillRepo;

    public TonGiaoNhanLXLOGService(TonGiaoNhanLXLOGRepository tPNrepo, TonTTKTBillRepository TTKTBillRepo) {
        this.tGNLXrepo = tPNrepo;
        this.TTKTBillRepo = TTKTBillRepo;
    }

    private boolean isAdmin() {
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true"))
            return true;
        else return false;
    }

    public static String sort;

    public ListContentPageDto<TonGiaoNhanLXLOGResponseDTO> findAllDataCN(List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao,
                                                                         List<String> loaiVung, List<String> nhomDV, List<String> mocTon, Integer pageQuery, Integer pageSize) {

        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        Pageable page = Pageable.unpaged();
        switch (sort) {
            case "":
                page = PageRequest.of(pageQuery, pageSize);
                break;
            case "asc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "tong"));
                break;
            case "desc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tong"));
                break;
            default:
                break;
        }


        Page<TonGiaoNhanLXLOGResponseDTO> pageResult;
        if (buuCuc == null || buuCuc.isEmpty()) {
            if (chiNhanh == null || chiNhanh.isEmpty()) {
                if (isAdmin()) {
                    pageResult = tGNLXrepo.findTonGiaoNhanLXLOGTCT(ngayBaoCao, nhomDV, loaiVung, page);
                } else {
                    chiNhanh = listCN;
                    buuCuc = listBC;
                    pageResult = tGNLXrepo.findTonGiaoNhanLXLOGCN1(chiNhanh, buuCuc, nhomDV, loaiVung, ngayBaoCao, page);
//                    pageResult = tGNLXrepo.findTonGiaoNhanLXLOGTCT1(ngayBaoCao, nhomDV, loaiVung, chiNhanh, page);
                }

            } else {
                if (isAdmin()) {
                    pageResult = tGNLXrepo.findTonGiaoNhanLXLOGCN(chiNhanh, nhomDV, loaiVung, ngayBaoCao, page);
                } else {
                    chiNhanh.retainAll(listCN);
                    buuCuc = listBC;
                    pageResult = tGNLXrepo.findTonGiaoNhanLXLOGCN1(chiNhanh, buuCuc, nhomDV, loaiVung, ngayBaoCao, page);
                }
            }
        } else {
            loaiVung = loaiVung.get(0).equals("ALL") ? Arrays.asList("NOI_TINH", "NOI_MIEN", "LIEN_MIEN") : loaiVung;
            nhomDV = nhomDV.get(0).equals("ALL") ? Arrays.asList("DV_NHANH", "DV_CHAM", "HOA_TOC") : nhomDV;
            if (!isAdmin()) {
                chiNhanh.retainAll(listCN);
                buuCuc.retainAll(listBC);
            }
            pageResult = tGNLXrepo.findTonGiaoNhanLXLOGBC(chiNhanh, buuCuc, nhomDV, loaiVung, mocTon, ngayBaoCao, page);
        }
        Page<String> time = tGNLXrepo.getTimeTinhToan(ngayBaoCao, Pageable.ofSize(1));
        String timeTinhToan = null;
        if (time.hasNext())
            timeTinhToan = time.getContent().get(0);
        ListContentPageDto<TonGiaoNhanLXLOGResponseDTO> listContent = new ListContentPageDto<>(pageResult);
        listContent.setTime(timeTinhToan);

        return listContent;

//        return new ListContentPageDto<TonGiaoNhanLXLOGResponseDTO>(pageResult);
    }

    public List<String> ListBuuCucChiNhanh(List<String> chiNhanh, LocalDate ngayBaoCao) {
        List<String> list;

        if (chiNhanh.isEmpty()) {
            list = tGNLXrepo.findDistinctChiNhanh(ngayBaoCao);
        } else {
            list = tGNLXrepo.findDistinctBCByCN(chiNhanh, ngayBaoCao);
        }
        return list;
    }

    public List<TonGiaoNhanLXLOGResponseDTO> TGNLXSum(List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao,
                                                      List<String> loaiVung, List<String> nhomDV, List<String> mocTon) {
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<TonGiaoNhanLXLOGResponseDTO> result;
        if (buuCuc == null || buuCuc.isEmpty()) {
            if (chiNhanh == null || chiNhanh.isEmpty()) {
                TonGiaoNhanLXLOGExcelExporter.setLoaiBaoCao("TCT");
                if (isAdmin()) {
                    result = tGNLXrepo.findTonGiaoNhanLXLOGTCTSum(nhomDV, loaiVung, ngayBaoCao);
                } else {
                    chiNhanh = listCN;
                    buuCuc = listBC;
                    result = tGNLXrepo.findTonGiaoNhanLXLOGCNSum1(chiNhanh, buuCuc, nhomDV, loaiVung, ngayBaoCao);
//                    result = tGNLXrepo.findTonGiaoNhanLXLOGTCTSum1(ngayBaoCao, nhomDV, loaiVung, chiNhanh);
                }
            } else {
                TonGiaoNhanLXLOGExcelExporter.setLoaiBaoCao("CN");
                if (isAdmin()) {
                    result = tGNLXrepo.findTonGiaoNhanLXLOGCNSum(chiNhanh, nhomDV, loaiVung, ngayBaoCao);
                } else {
                    chiNhanh.retainAll(listCN);
                    buuCuc = listBC;
                    result = tGNLXrepo.findTonGiaoNhanLXLOGCNSum1(chiNhanh, buuCuc, nhomDV, loaiVung, ngayBaoCao);
                }
            }
        } else {
            TonGiaoNhanLXLOGExcelExporter.setLoaiBaoCao("BC");
            loaiVung = loaiVung.get(0).equals("ALL") ? Arrays.asList("NOI_TINH", "NOI_MIEN", "LIEN_MIEN") : loaiVung;
            nhomDV = nhomDV.get(0).equals("ALL") ? Arrays.asList("DV_NHANH", "DV_CHAM", "HOA_TOC") : nhomDV;
            if (!isAdmin()) {
                chiNhanh.retainAll(listCN);
                buuCuc.retainAll(listBC);
            }
            result = tGNLXrepo.findTonGiaoNhanLXLOGBCSum(chiNhanh, buuCuc, nhomDV, loaiVung, mocTon, ngayBaoCao);
        }
        return result;
    }

    public List<TonGiaoNhanLXLOGResponseDTO> TonGiaoNhanLXSumCalculation(List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao,
                                                                         List<String> loaiVung, List<String> nhomDV, List<String> mocTon) {
        List<TonGiaoNhanLXLOGResponseDTO> tPKLM = TGNLXSum(chiNhanh, buuCuc, ngayBaoCao, loaiVung, nhomDV, mocTon);
        Long qua0h = Long.valueOf(0);
        Long qua2h = Long.valueOf(0);
        Long qua6h = Long.valueOf(0);
        Long qua12h = Long.valueOf(0);
        Long qua24h = Long.valueOf(0);
        Long qua48h = Long.valueOf(0);
        Long qua72h = Long.valueOf(0);
        Long qua96h = Long.valueOf(0);
        Long qua120h = Long.valueOf(0);
        Long tong = Long.valueOf(0);


        for (TonGiaoNhanLXLOGResponseDTO tGNLXResponse : tPKLM) {
            qua0h += tGNLXResponse.getQua0h();
            qua2h += tGNLXResponse.getQua2h();
            qua6h += tGNLXResponse.getQua6h();
            qua12h += tGNLXResponse.getQua12h();
            qua24h += tGNLXResponse.getQua24h();
            qua48h += tGNLXResponse.getQua48h();
            qua72h += tGNLXResponse.getQua72h();
            qua96h += tGNLXResponse.getQua96h();
            qua120h += tGNLXResponse.getQua120h();
            tong += tGNLXResponse.getTong();
        }

        TonGiaoNhanLXLOGResponseDTO tGNLXResponse = new TonGiaoNhanLXLOGResponseDTO();
        tGNLXResponse.setQua0h(qua0h);
        tGNLXResponse.setQua2h(qua2h);
        tGNLXResponse.setQua6h(qua6h);
        tGNLXResponse.setQua12h(qua12h);
        tGNLXResponse.setQua24h(qua24h);
        tGNLXResponse.setQua48h(qua48h);
        tGNLXResponse.setQua72h(qua72h);
        tGNLXResponse.setQua96h(qua96h);
        tGNLXResponse.setQua120h(qua120h);
        tGNLXResponse.setTong(tong);

        List<TonGiaoNhanLXLOGResponseDTO> listTPKLM = new ArrayList<>();
        listTPKLM.add(tGNLXResponse);

        return listTPKLM;
    }

    public List<TonGiaoNhanLXLOGResponseDTO> exportDataExcel(HttpServletResponse response, List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao,
                                                             List<String> loaiVung, List<String> nhomDV, List<String> mocTon) throws IOException {
        List<TonGiaoNhanLXLOGResponseDTO> result = TGNLXSum(chiNhanh, buuCuc, ngayBaoCao, loaiVung, nhomDV, mocTon);
        TonGiaoNhanLXLOGExcelExporter exportExcelExporter;
        exportExcelExporter = new TonGiaoNhanLXLOGExcelExporter(result);
        exportExcelExporter.exportDataToExcel(response);
        return result;
    }

    public List<TonTTKTBillResponseDTO> exportDataBill(HttpServletResponse response, List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao,
                                                       List<String> loaiVung, List<String> nhomDV, List<String> mocTon) throws IOException {
        if (!isAdmin()) {
            if(!chiNhanh.isEmpty())
                chiNhanh.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
            else
                chiNhanh = UserContext.getUserData().getListChiNhanhVeriable();
            if(!buuCuc.isEmpty())
                buuCuc.retainAll(UserContext.getUserData().getListBuuCucVeriable());
            else
                buuCuc = UserContext.getUserData().getListBuuCucVeriable();
        }
        List<String> lv = new ArrayList<>();
        List<String> dv = new ArrayList<>();
        if (!loaiVung.get(0).equals("ALL")) {
            lv = loaiVung;
        }
        if (!nhomDV.get(0).equals("ALL")) {
            dv = nhomDV;
        }

        List<TonTTKTBillResponseDTO> result = TTKTBillRepo.findTonTTKTBill(ngayBaoCao, chiNhanh, buuCuc, dv, lv, mocTon);
        TonTTKTBillExcelExporter exportExcelExporter;
        exportExcelExporter = new TonTTKTBillExcelExporter(result);
        exportExcelExporter.exportDataToExcel(response);
        return result;
    }

    public List<TonThuNotificationDisplayDto> TTKTNotiData(LocalDate ngayBaoCao, String nguongCanhBao) {
        TTKTNotiResponseDTO generalData = new TTKTNotiResponseDTO();
        List<String> listNguong = Arrays.asList(nguongCanhBao.split(","));
//        generalData = tGNLXrepo.findNDataTGD(ngayBaoCao, listNguong);

//        if(generalData == null)
//            return new TTKTNotiResponseDTO("",0L,0L,0L,0L,0L,0L,0L,0L,0L,0L,0L,0L,0f,0f,new ArrayList<>(),new ArrayList<>());
// ngocnt92
        List<TonThuNotificationDisplayDto> displayDtoList = new ArrayList<>();
        List<TTKTNotiResponseDTO> top10Data = tGNLXrepo.findNDataCN(ngayBaoCao, listNguong);

        TTKTNotiResponseDTO response = new TTKTNotiResponseDTO();
        Collections.sort(top10Data, Collections.reverseOrder());
        List<TTKTNotiResponseDTO> top10 = top10Data.stream().limit(10).collect(Collectors.toList());
        for (TTKTNotiResponseDTO ttktNotiResponseDTO : top10) {
            TonThuNotificationDisplayDto tonTTKTDto = new TonThuNotificationDisplayDto();
            tonTTKTDto.setTenChiNhanh(ttktNotiResponseDTO.getDonVi());
            tonTTKTDto.setTongTonThu(ttktNotiResponseDTO.getTong());
            tonTTKTDto.setTrongKpi(ttktNotiResponseDTO.getQua0h() + ttktNotiResponseDTO.getQua2h());
            tonTTKTDto.setVuotNguong(tonTTKTDto.getTongTonThu() - tonTTKTDto.getTrongKpi());
            if (tonTTKTDto.getTongTonThu() == 0) {
                tonTTKTDto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * 0)));
            } else {
                tonTTKTDto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * tonTTKTDto.getVuotNguong() / tonTTKTDto.getTongTonThu())));
            }
            displayDtoList.add(tonTTKTDto);
        }

        TonThuNotificationDisplayDto tonTTKTCongTyLog = new TonThuNotificationDisplayDto(" Công ty LOG", 0L, 0L, 0L, 0.0F);
        Long tongSanLuong = 0L;
        Long trongKpi = 0L;
        Long vuotNguong = 0L;
        for (TonThuNotificationDisplayDto thuNotificationDisplayDto : displayDtoList) {
            tongSanLuong += thuNotificationDisplayDto.getTongTonThu();
            trongKpi += thuNotificationDisplayDto.getTrongKpi();
            vuotNguong += thuNotificationDisplayDto.getVuotNguong();
        }
        tonTTKTCongTyLog.setTongTonThu(tongSanLuong);
        tonTTKTCongTyLog.setTenChiNhanh(" Công ty LOG");
        tonTTKTCongTyLog.setTrongKpi(trongKpi);
        tonTTKTCongTyLog.setVuotNguong(vuotNguong);
        if (tonTTKTCongTyLog.getTongTonThu() == 0) {
            tonTTKTCongTyLog.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * 0)));
        } else {
            tonTTKTCongTyLog.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * tonTTKTCongTyLog.getVuotNguong() / tonTTKTCongTyLog.getTongTonThu())));
        }
        displayDtoList.add(0,tonTTKTCongTyLog);
        return displayDtoList;
    }
}
