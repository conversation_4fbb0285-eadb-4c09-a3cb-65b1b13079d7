package nocsystem.indexmanager.services.CanhBaoServices.TrungTam;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonGiaoNhanLXLOGResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.TonTTKTBillResponseDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class TonTTKTBillExcelExporter {

    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private List<TonTTKTBillResponseDTO> tGNLXLOG;
    private static final String sheetName = "Ton TTKT chi tiet";

    private static final List<String> listHeader = Arrays.asList("MA_PHIEUGUI", "TINH_NHAN", "HUYEN_NHAN", "TEN_HUYEN_NHAN", "TTKT_FROM", "TINH_PHAT", "HUYEN_PHAT",
            "TEN_HUYEN_PHAT", "TTKT_TO", "MA_DV_VIETTEL", "MA_BUUCUC_GOC", "TIME_TAC_DONG", "TRANG_THAI", "MA_BUUCUC_HT", "CHI_NHANH_HT", "MA_DOITAC", "MA_KHGUI",
            "MA_BUUCUC_PHAT", "TIME_PCP", "TIME_GACH_BP", "NGAY_GUI_BP", "DANH_GIA", "LOAI_PG", "LAN_PHAT", "TG_CON_PHAT", "BUU_TA_PHAT", "TIEN_COD",
            "KHAU_FM", "KHAU_MM", "KHAU_LM", "TG_QUYDINH", "TG_TT_LUYKE", "TG_CHENHLECH", "LOAI_VUNG", "NHOM_DV", "IS_CHECKED", "NGAY_BAOCAO", "LOAI_CANH_BAO", "MOC_TON_XE");

    public TonTTKTBillExcelExporter(List<TonTTKTBillResponseDTO> tGNLXLOG) {
        this.workbook = new SXSSFWorkbook();
        this.tGNLXLOG = tGNLXLOG;
    }


    private void createCell(SXSSFRow row, int columnCount, Object value, CellStyle style) {
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderTCT() {
        sheet = workbook.createSheet(sheetName);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 13);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        SXSSFRow row = sheet.createRow(0);

        for (int i = 0; i < listHeader.size(); i++) {
            createCell(row, i, listHeader.get(i), style);
            sheet.setColumnWidth(i, ((int) (listHeader.get(i).length() * 1.5)) * 320);
        }
    }

    private void writeDataTCT() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        CreationHelper createHelper = workbook.getCreationHelper();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        SXSSFRow row;
        int columnCount;

        for (TonTTKTBillResponseDTO bill : tGNLXLOG) {
            row = sheet.createRow(rowCount++);
            columnCount = 0;
            createCell(row, columnCount++, bill.getMaPhieuGui(), style);
            createCell(row, columnCount++, bill.getTinhNhan(), style);
            createCell(row, columnCount++, bill.getHuyenNhan(), style);
            createCell(row, columnCount++, bill.getTenHuyenNhan(), style);
            createCell(row, columnCount++, bill.getTtktFrom(), style);
            createCell(row, columnCount++, bill.getTinhPhat(), style);
            createCell(row, columnCount++, bill.getHuyenPhat(), style);
            createCell(row, columnCount++, bill.getTenHuyenPhat(), style);
            createCell(row, columnCount++, bill.getTtktTo(), style);
            createCell(row, columnCount++, bill.getMaDvViettel(), style);
            createCell(row, columnCount++, bill.getMaBuuCucGoc(), style);
            createCell(row, columnCount++, bill.getTimeTacDong(), style);
            createCell(row, columnCount++, bill.getTrangThai(), style);
            createCell(row, columnCount++, bill.getMaBuuCucHT(), style);
            createCell(row, columnCount++, bill.getChiNhanhHT(), style);
            createCell(row, columnCount++, bill.getMaDoiTac(), style);
            createCell(row, columnCount++, bill.getMaKHGui(), style);
            createCell(row, columnCount++, bill.getMaBuuCucPhat(), style);
            createCell(row, columnCount++, bill.getTimePCP(), style);
            createCell(row, columnCount++, bill.getTimeGachBP(), style);
            createCell(row, columnCount++, bill.getNgayguiBP(), style);
            createCell(row, columnCount++, bill.getDanhGia(), style);
            createCell(row, columnCount++, bill.getLoaiPG(), style);
            createCell(row, columnCount++, bill.getLanPhat(), style);
            createCell(row, columnCount++, bill.getTgConPhat(), style);
            createCell(row, columnCount++, bill.getBuuTaPhat(), style);
            createCell(row, columnCount++, bill.getTienCOD(), style);
            createCell(row, columnCount++, bill.getKhauFM(), style);
            createCell(row, columnCount++, bill.getKhauMM(), style);
            createCell(row, columnCount++, bill.getKhauLM(), style);
            createCell(row, columnCount++, bill.getTgQuyDinh(), style);
            createCell(row, columnCount++, bill.getTgTTLuyKe(), style);
            createCell(row, columnCount++, bill.getTgChenhLech(), style);
            createCell(row, columnCount++, bill.getLoaiVung(), style);
            createCell(row, columnCount++, bill.getNhomDV(), style);
            createCell(row, columnCount++, bill.getIsChecked(), style);
            createCell(row, columnCount++, bill.getTimeTinhToan(), style);
            createCell(row, columnCount++, bill.getLoaiCanhBao(), style);
            createCell(row, columnCount++, bill.getMocTonXe(), style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {

        createHeaderTCT();
        writeDataTCT();

        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        }
    }
}
