package nocsystem.indexmanager.services.CanhBaoServices.TrungTam;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.BuuCucChiNhanhResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam.KhoLienVung6hResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.KhoLienVung6hRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.ListBCCNRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Service
public class KhoLienVung6hService {

    private final KhoLienVung6hRepository kLV6hrepo;
    private final ListBCCNRepository BCCNrepo;

    public KhoLienVung6hService(KhoLienVung6hRepository tPNrepo, ListBCCNRepository bccNrepo) {
        this.kLV6hrepo = tPNrepo;
        BCCNrepo = bccNrepo;
    }

    private boolean isAdmin(){
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true"))
            return true;
        else return false;
    }

    public static String sort;

    public ListContentPageDto<KhoLienVung6hResponseDTO> findAllDataCN(List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao, Integer pageQuery, Integer pageSize, List<String> mocTon) {
        Pageable page = Pageable.unpaged();
        switch (sort) {
            case "":
                page = PageRequest.of(pageQuery, pageSize);
                break;
            case "asc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "tong"));
                break;
            case "desc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tong"));
                break;
            default:
                break;
        }

        if (mocTon.isEmpty())
            mocTon = Arrays.asList("QUA_6H", "QUA_12H", "QUA_24H", "QUA_48H", "QUA_72H", "QUA_96H", "QUA_120H");

        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();

        Page<KhoLienVung6hResponseDTO> pageResult;
        if (buuCuc == null || buuCuc.isEmpty()) {
            if (chiNhanh == null || chiNhanh.isEmpty()) {
                if (isAdmin()) {
                    pageResult = kLV6hrepo.findKhoLienVung6hTCT(ngayBaoCao, page);
                } else {
                    chiNhanh = listCN;
                    buuCuc = listBC;
                    pageResult = kLV6hrepo.findKhoLienVung6hCN1(chiNhanh, buuCuc, ngayBaoCao, page);
//                    pageResult = kLV6hrepo.findKhoLienVung6hTCT1(chiNhanh, ngayBaoCao, page);
                }
            } else {
                if (isAdmin())
                    pageResult = kLV6hrepo.findKhoLienVung6hCN(chiNhanh, ngayBaoCao, page);
                else {
                    chiNhanh.retainAll(listCN);
                    buuCuc = listBC;
                    pageResult = kLV6hrepo.findKhoLienVung6hCN1(chiNhanh, buuCuc, ngayBaoCao, page);
                }
            }
        } else {
            if (!isAdmin()) {
                chiNhanh.retainAll(listCN);
                buuCuc.retainAll(listBC);
            }
            pageResult = kLV6hrepo.findKhoLienVung6hBC(chiNhanh, buuCuc, ngayBaoCao, mocTon, page);

        }
        return new ListContentPageDto<>(pageResult);
    }

    private List<KhoLienVung6hResponseDTO> getDataSum(List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao, List<String> mocTon) {
        if (mocTon.isEmpty())
            mocTon = Arrays.asList("QUA_6H", "QUA_12H", "QUA_24H", "QUA_48H", "QUA_72H", "QUA_96H", "QUA_120H");
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();

        if (buuCuc == null || buuCuc.isEmpty()) {
            if (chiNhanh == null || chiNhanh.isEmpty()) {
                KhoLienVung6hExcelExporter.setLoaiBaoCao("TCT");
                if (isAdmin()) {
                    return kLV6hrepo.findKhoLienVung6hTCTSum(ngayBaoCao);
                } else {
                    chiNhanh = listCN;
                    buuCuc = listBC;
                    return kLV6hrepo.findKhoLienVung6hCNSum1(chiNhanh, buuCuc, ngayBaoCao);
//                    return kLV6hrepo.findKhoLienVung6hTCTSum1(chiNhanh, ngayBaoCao);
                }
            } else {
                KhoLienVung6hExcelExporter.setLoaiBaoCao("CN");
                if (isAdmin()) {
                    return kLV6hrepo.findKhoLienVung6hCNSum(chiNhanh, ngayBaoCao);
                } else {
                    chiNhanh.retainAll(listCN);
                    buuCuc = listBC;
                    return kLV6hrepo.findKhoLienVung6hCNSum1(chiNhanh, buuCuc, ngayBaoCao);
                }
            }
        } else {
            KhoLienVung6hExcelExporter.setLoaiBaoCao("BC");
            if (!isAdmin()) {
                chiNhanh.retainAll(listCN);
                buuCuc.retainAll(listBC);
            }
            return kLV6hrepo.findKhoLienVung6hBCSum(chiNhanh, buuCuc, ngayBaoCao, mocTon);
        }
    }

    public List<BuuCucChiNhanhResDTO> listBuuCucChiNhanh(List<String> chiNhanh, LocalDate ngayBaoCao) {
        List<String> list;
        List<String[]> listBC = BCCNrepo.findTenBuuCuc();
        List<String[]> listCN = BCCNrepo.findTenChiNhanh();

        HashMap<String, String> mapBC = new HashMap<String, String>();
        HashMap<String, String> mapCN = new HashMap<String, String>();

        for (String[] str : listBC) {
            mapBC.put(str[0], str[1]);
        }

        for (String[] str : listCN) {
            mapCN.put(str[0], str[1]);
        }

        List<BuuCucChiNhanhResDTO> resultList = new ArrayList<>();

        if (chiNhanh.isEmpty()) {
            list = kLV6hrepo.findDistinctChiNhanh(ngayBaoCao);
            for (String e : list) {
                BuuCucChiNhanhResDTO bccn = new BuuCucChiNhanhResDTO();
                bccn.setMa(e);
                bccn.setTen(mapCN.get(e));
                resultList.add(bccn);
            }
        } else {
            list = kLV6hrepo.findDistinctBCByCN(chiNhanh, ngayBaoCao);
            for (String e : list) {
                BuuCucChiNhanhResDTO bccn = new BuuCucChiNhanhResDTO();
                bccn.setMa(e);
                bccn.setTen(mapBC.get(e));
                resultList.add(bccn);
            }
        }
        return resultList;
    }

    public List<KhoLienVung6hResponseDTO> tonGiaoNhanLV6hSum(List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao, List<String> mocTon) {
        List<KhoLienVung6hResponseDTO> tPKLM = getDataSum(chiNhanh, buuCuc, ngayBaoCao, mocTon);


        Float qua6h = Float.valueOf(0);
        Float qua12h = Float.valueOf(0);
        Float qua24h = Float.valueOf(0);
        Float qua48h = Float.valueOf(0);
        Float qua72h = Float.valueOf(0);
        Float qua96h = Float.valueOf(0);
        Float qua120h = Float.valueOf(0);
        Float tong = Float.valueOf(0);


        for (KhoLienVung6hResponseDTO tGNLXResponse : tPKLM) {

            qua6h += tGNLXResponse.getQua6h();
            qua12h += tGNLXResponse.getQua12h();
            qua24h += tGNLXResponse.getQua24h();
            qua48h += tGNLXResponse.getQua48h();
            qua72h += tGNLXResponse.getQua72h();
            qua96h += tGNLXResponse.getQua96h();
            qua120h += tGNLXResponse.getQua120h();
            tong += tGNLXResponse.getTong();
        }

        KhoLienVung6hResponseDTO tGNLXResponse = new KhoLienVung6hResponseDTO();

        tGNLXResponse.setQua6h(qua6h);
        tGNLXResponse.setQua12h(qua12h);
        tGNLXResponse.setQua24h(qua24h);
        tGNLXResponse.setQua48h(qua48h);
        tGNLXResponse.setQua72h(qua72h);
        tGNLXResponse.setQua96h(qua96h);
        tGNLXResponse.setQua120h(qua120h);
        tGNLXResponse.setTong(tong);

        List<KhoLienVung6hResponseDTO> listTPKLM = new ArrayList<>();
        listTPKLM.add(tGNLXResponse);

        return listTPKLM;
    }

    public List<KhoLienVung6hResponseDTO> exportDataExcel(HttpServletResponse response, List<String> chiNhanh, List<String> buuCuc, LocalDate ngayBaoCao, List<String> mocTon) throws IOException {
        List<KhoLienVung6hResponseDTO> result = getDataSum(chiNhanh, buuCuc, ngayBaoCao, mocTon);
        KhoLienVung6hExcelExporter exportKPIPhatExcelExporter;
        exportKPIPhatExcelExporter = new KhoLienVung6hExcelExporter(result);
        exportKPIPhatExcelExporter.exportDataToExcel(response);
        return result;
    }
}
