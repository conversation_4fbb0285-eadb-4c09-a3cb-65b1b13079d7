package nocsystem.indexmanager.services.CanhBaoServices;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop10ResponeDto;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiExcelDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotificationDisplayDto;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonPhatKhauLM;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatKhauLMRepository;
import nocsystem.indexmanager.services.CanhBaoServices.Notification.TonPhatNotificationExcelExporter;
import nocsystem.indexmanager.services.CanhBaoServices.Notification.TonThuNotificationExcelExporter;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatHelperExcel.TonPhatKhauLMExcelExporter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TonPhatKhauLMService {

    private final TonPhatKhauLMRepository tPKLMrepo;

    public TonPhatKhauLMService(TonPhatKhauLMRepository tPNrepo) {
        this.tPKLMrepo = tPNrepo;
    }

    public static String sort;
    public static final Integer BAN_TGD = 1;
    public static final Integer BAN_GIAM_DOC_CHI_NHANH = 2;
    public static final Integer BAN_GIAM_DOC_CHI_NHANH_HN_HCM = 4;
    public static final Integer TRUONG_BUU_CUC = 3;

    private boolean isAdmin() {
        if (UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true"))
            return true;
        else return false;
    }

    public ListContentPageDto<TonPhatKhauLMResponseDTO> findAllDataBC(List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc,
                                                                      LocalDate ngayBaoCao, List<String> loaiCanhBao, Integer pageQuery,
                                                                      Integer pageSize, List<String> loaiDichVu, List<String> loiKhau) {
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        Pageable page = Pageable.unpaged();
        switch (sort) {
            case "":
                page = PageRequest.of(pageQuery, pageSize);
                break;
            case "asc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "tongSL"));
                break;
            case "desc":
                page = PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tongSL"));
                break;
            default:
                break;
        }

        if (loaiTon.isEmpty() || loaiTon == null) loaiTon = Arrays.asList("HOAN", "CHO_HOAN", "LUU_KHO", "TON_PHAT");

        if (loaiCanhBao.isEmpty() || loaiCanhBao == null)
            loaiCanhBao = Arrays.asList("VANG", "DO_1", "DO_2", "DO_3", "DO_4", "DO_5", "DO_6", "DO_7", "DO_8", "DO_9");

        if(!loaiDichVu.isEmpty())
            loaiDichVu = loaiDichVu.get(0).equals("ALL") ? new ArrayList<>() : loaiDichVu;


        Page<TonPhatKhauLMResponseDTO> pageResult;

        if ((maBuuCuc == null || maBuuCuc.isEmpty())) {
            if (tinhPhat == null || tinhPhat.isEmpty()) {
                if (isAdmin()){
                    loiKhau = loiKhau.isEmpty() ? Arrays.asList("ALL") : loiKhau;
                    loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("ALL") : loaiDichVu;
                    pageResult = tPKLMrepo.findTonPhatKhauLMTCT(loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau, page);
                }
                else {
                    loiKhau = loiKhau.isEmpty() ? Arrays.asList("OK", "LOI_MM") : loiKhau;
                    loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("DV_CHAM", "DV_NHANH", "DV_HOATOC") : loaiDichVu;
                    tinhPhat = listCN;
                    maBuuCuc = listBC;
                    pageResult = tPKLMrepo.findTonPhatKhauLMCN1(tinhPhat, maBuuCuc, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau, page);
                }
            } else {
                loiKhau = loiKhau.isEmpty() ? Arrays.asList("OK", "LOI_MM") : loiKhau;
                loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("DV_CHAM", "DV_NHANH", "DV_HOATOC") : loaiDichVu;
                if (isAdmin())
                    pageResult = tPKLMrepo.findTonPhatKhauLMCN(tinhPhat, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau, page);
                else {
                    tinhPhat.retainAll(listCN);
                    maBuuCuc = listBC;
                    pageResult = tPKLMrepo.findTonPhatKhauLMCN1(tinhPhat, maBuuCuc, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau, page);
                }
            }
        } else {
            loiKhau = loiKhau.isEmpty() ? Arrays.asList("OK", "LOI_MM") : loiKhau;
            loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("DV_CHAM", "DV_NHANH", "DV_HOATOC") : loaiDichVu;
            if (!isAdmin()) {
                tinhPhat.retainAll(listCN);
                maBuuCuc.retainAll(listBC);
            }
            pageResult = tPKLMrepo.findTonPhatKhauLMBC(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau, page);
        }

        Page<String> time = tPKLMrepo.getTimeTinhToan(ngayBaoCao, Pageable.ofSize(1));
        String timeTinhToan = null;
        if (time.hasNext())
            timeTinhToan = time.getContent().get(0);
        ListContentPageDto<TonPhatKhauLMResponseDTO> listContent = new ListContentPageDto<>(pageResult);
        listContent.setTime(timeTinhToan);
        return listContent;
//        return new ListContentPageDto<TonPhatKhauLMResponseDTO>(pageResult);
    }

    public List<TonPhatKhauLMResponseDTO> getDataSum(List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc, LocalDate ngayBaoCao, List<String> loaiCanhBao, List<String> loaiDichVu, List<String> loiKhau) {
        if (loaiTon.isEmpty() || loaiTon == null) loaiTon = Arrays.asList("HOAN", "CHO_HOAN", "LUU_KHO", "TON_PHAT");

        if (loaiCanhBao.isEmpty() || loaiCanhBao == null)
            loaiCanhBao = Arrays.asList("VANG", "DO_1", "DO_2", "DO_3", "DO_4", "DO_5", "DO_6", "DO_7", "DO_8", "DO_9");

        if(!loaiDichVu.isEmpty())
            loaiDichVu = loaiDichVu.get(0).equals("ALL") ? new ArrayList<>() : loaiDichVu;

        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();

        if ((maBuuCuc == null || maBuuCuc.isEmpty())) {
            if (tinhPhat == null || tinhPhat.isEmpty()) {
                if (isAdmin()) {
                    loiKhau = loiKhau.isEmpty() ? Arrays.asList("ALL") : loiKhau;
                    loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("ALL") : loaiDichVu;
                    return tPKLMrepo.findTonPhatKhauLMSumTCT(loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau);
                } else {
                    loiKhau = loiKhau.isEmpty() ? Arrays.asList("OK", "LOI_MM") : loiKhau;
                    loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("DV_CHAM", "DV_NHANH", "DV_HOATOC") : loaiDichVu;
                    tinhPhat = listCN;
                    maBuuCuc = listBC;
                    return tPKLMrepo.findTonPhatKhauLMSumCN1(tinhPhat, maBuuCuc, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau);
                }
            } else {
                loiKhau = loiKhau.isEmpty() ? Arrays.asList("OK", "LOI_MM") : loiKhau;
                loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("DV_CHAM", "DV_NHANH", "DV_HOATOC") : loaiDichVu;
                if (isAdmin()) {
                    return tPKLMrepo.findTonPhatKhauLMSumCN(tinhPhat, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau);
                } else {
                    tinhPhat.retainAll(listCN);
                    maBuuCuc = listBC;
                    return tPKLMrepo.findTonPhatKhauLMSumCN1(tinhPhat, maBuuCuc, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau);
                }
            }
        } else {
            loiKhau = loiKhau.isEmpty() ? Arrays.asList("OK", "LOI_MM") : loiKhau;
            loaiDichVu = loaiDichVu.isEmpty() ? Arrays.asList("DV_CHAM", "DV_NHANH", "DV_HOATOC") : loaiDichVu;
            if (!isAdmin()) {
                tinhPhat.retainAll(listCN);
                maBuuCuc.retainAll(listBC);
            }
            return tPKLMrepo.findTonPhatKhauLMSumBC(tinhPhat, loaiTon, maBuuCuc, loaiCanhBao, ngayBaoCao, loaiDichVu, loiKhau);
        }
    }

    public List<TonPhatKhauLMResponseDTO> SummaryCalculation(List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc, LocalDate ngayBaoCao, List<String> loaiCanhBao, List<String> loaiDichVu, List<String> loiKhau) {

        List<TonPhatKhauLMResponseDTO> tPKLM = getDataSum(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau);

        Long lMVang = Long.valueOf(0);
        Long lMDo1 = Long.valueOf(0);
        Long lMDo2 = Long.valueOf(0);
        Long lMDo3 = Long.valueOf(0);
        Long lMDo4 = Long.valueOf(0);
        Long lMDo5 = Long.valueOf(0);
        Long lMDo6 = Long.valueOf(0);
        Long lMDo7 = Long.valueOf(0);
        Long lMDo8 = Long.valueOf(0);
        Long lMDo9 = Long.valueOf(0);
        Long tongSL = Long.valueOf(0);


        for (TonPhatKhauLMResponseDTO tPKLMResponse : tPKLM) {
            lMVang += tPKLMResponse.getLMVang();
            lMDo1 += tPKLMResponse.getLMDo1();
            lMDo2 += tPKLMResponse.getLMDo2();
            lMDo3 += tPKLMResponse.getLMDo3();
            lMDo4 += tPKLMResponse.getLMDo4();
            lMDo5 += tPKLMResponse.getLMDo5();
            lMDo6 += tPKLMResponse.getLMDo6();
            lMDo7 += tPKLMResponse.getLMDo7();
            lMDo8 += tPKLMResponse.getLMDo8();
            lMDo9 += tPKLMResponse.getLMDo9();
            tongSL += tPKLMResponse.getTongSL();
        }

        TonPhatKhauLMResponseDTO tPKLMResponse = new TonPhatKhauLMResponseDTO();
        tPKLMResponse.setLMVang(lMVang);
        tPKLMResponse.setLMDo1(lMDo1);
        tPKLMResponse.setLMDo2(lMDo2);
        tPKLMResponse.setLMDo3(lMDo3);
        tPKLMResponse.setLMDo4(lMDo4);
        tPKLMResponse.setLMDo5(lMDo5);
        tPKLMResponse.setLMDo6(lMDo6);
        tPKLMResponse.setLMDo7(lMDo7);
        tPKLMResponse.setLMDo8(lMDo8);
        tPKLMResponse.setLMDo9(lMDo9);
        tPKLMResponse.setTongSL(tongSL);


        List<TonPhatKhauLMResponseDTO> listKH = new ArrayList<>();
        listKH.add(tPKLMResponse);
        return listKH;
    }

    public List<TonPhatKhauLMTop8ResDTO> khauMMTop8(String chiNhanh, LocalDate ngayBaoCao) {
        List<TonPhatKhauLMTop8ResDTO> list;
        List<String> cn, bc;

        if (chiNhanh.equals("")) {
            if (isAdmin()) {
                list = tPKLMrepo.findTonPhatKhauLMTop8TCT(ngayBaoCao);
            } else {
                cn = UserContext.getUserData().getListChiNhanhVeriable();
                list = tPKLMrepo.findTonPhatKhauLMTop8TCT1(cn, ngayBaoCao);
            }
        } else {
            if (isAdmin()) {
                list = tPKLMrepo.findTonPhatKhauLMTop8CN(chiNhanh, ngayBaoCao);
            } else {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(chiNhanh)) {
                    return new ArrayList<>();
                }
                bc = UserContext.getUserData().getListBuuCucVeriable();
                list = tPKLMrepo.findTonPhatKhauLMTop8CN1(chiNhanh, bc, ngayBaoCao);
            }
        }

        Collections.sort(list, Collections.reverseOrder());
        return list.stream().limit(8).collect(Collectors.toList());
    }

    // VT_2023/02/27 NgocNt92 add function to get Data fill to excel(api_exportExcel).
    public void exportExcelTonPhatKhauLM(HttpServletResponse response,
                                         List<String> tinhPhat,
                                         List<String> loaiton,
                                         List<String> maBuuCuc,
                                         LocalDate ngayBaoCao,
                                         List<String> loaiCanhBao,
                                         List<String> loaiDichVu, List<String> loiKhau) throws IOException {
        List<TonPhatKhauLMResponseDTO> tPhatKhauLMExcel = getDataSum(tinhPhat, loaiton, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu, loiKhau);
        TonPhatKhauLMExcelExporter tPKhauLMExcelExporter = new TonPhatKhauLMExcelExporter(tPhatKhauLMExcel);
        tPKhauLMExcelExporter.exportDataToExcel(response, tinhPhat, maBuuCuc);
    }

//    private static final List<String> nguong789 = Arrays.asList("DO_7", "DO_8", "DO_9");

    public List<TonThuNotificationDisplayDto> getKhauLMNotificationData(LocalDate ngayBaoCao, String doiTuongCanhBao, String chiNhanh, String buuCuc, String nguongCanhBao) {
        List<TonPhatKhauLMTop10ResponeDto> generalData = new ArrayList<>();
        List<TonPhatKhauLMTop10ResponeDto> top10Data = new ArrayList<>();
        List<String> listNguong = Arrays.asList(nguongCanhBao.split(","));

        List<TonThuNotificationDisplayDto> displayDtoList = new ArrayList<>();
        Map<String, Long> mapDataTop10 = new HashMap<>();
        int res = 0;
        TonThuNotificationDisplayDto notificationDisplayDto = new TonThuNotificationDisplayDto();
        switch (doiTuongCanhBao) {
            case "BAN_TGD":
                generalData = tPKLMrepo.findNTonPhatKhauLMTop8TCT(Arrays.asList("ALL"), ngayBaoCao, listNguong);
                top10Data = tPKLMrepo.findNTop10NotiTGD(ngayBaoCao, listNguong);
                for (int i = 0; i < top10Data.size(); i++) {
                    if (mapDataTop10.containsKey(top10Data.get(i).getTinhPhat())) {
                        mapDataTop10.put(top10Data.get(i).getTinhPhat(), (mapDataTop10.get(top10Data.get(i).getTinhPhat()) + top10Data.get(i).getTongSL()));
                    } else {
                        mapDataTop10.put(top10Data.get(i).getTinhPhat(), top10Data.get(i).getTongSL());
                    }
                }
                notificationDisplayDto.setTenChiNhanh("Toàn quốc");
                res = BAN_TGD;
                break;
            case "BAN_GIAM_DOC_CHI_NHANH":
                generalData = tPKLMrepo.findNTonPhatKhauLMTop8TCT1(chiNhanh, ngayBaoCao, listNguong);
                top10Data = tPKLMrepo.findNTop10NotiCN(chiNhanh, ngayBaoCao, listNguong);
                for (int i = 0; i < top10Data.size(); i++) {
                    if (mapDataTop10.containsKey(top10Data.get(i).getMaBuuCuc())) {
                        mapDataTop10.put(top10Data.get(i).getMaBuuCuc(), (mapDataTop10.get(top10Data.get(i).getMaBuuCuc()) + top10Data.get(i).getTongSL()));
                    } else {
                        mapDataTop10.put(top10Data.get(i).getMaBuuCuc(), top10Data.get(i).getTongSL());
                    }
                }
                notificationDisplayDto.setTenChiNhanh("Chi nhánh");
                if (chiNhanh.equals("HCM") || chiNhanh.equals("HNI") || chiNhanh.equals("CNHCM") || chiNhanh.equals("CNHNI")) {
                    res = BAN_GIAM_DOC_CHI_NHANH_HN_HCM;
                } else {
                    res = BAN_GIAM_DOC_CHI_NHANH;
                }

                break;
            case "TRUONG_BUU_CUC":
                generalData = tPKLMrepo.findNTonPhatKhauLMTop8CN1(chiNhanh, Arrays.asList(buuCuc), ngayBaoCao, listNguong);
                top10Data = tPKLMrepo.findNTop10NotiBC(chiNhanh, buuCuc, ngayBaoCao, listNguong);
                for (int i = 0; i < top10Data.size(); i++) {
                    if (mapDataTop10.containsKey(top10Data.get(i).getBuuTaPhat())) {
                        mapDataTop10.put(top10Data.get(i).getBuuTaPhat(), (mapDataTop10.get(top10Data.get(i).getBuuTaPhat()) + top10Data.get(i).getTongSL()));
                    } else {
                        mapDataTop10.put(top10Data.get(i).getBuuTaPhat(), top10Data.get(i).getTongSL());
                    }
                }
                notificationDisplayDto.setTenChiNhanh("Bưu cục");
                res = TRUONG_BUU_CUC;
                break;
            default:
                break;
        }
        notificationDisplayDto.setTongTonThu(generalData.stream().map(TonPhatKhauLMTop10ResponeDto::getTongSL).reduce(0L, Long::sum));
        notificationDisplayDto.setTrongKpi(generalData.stream()
                .filter(l -> l.getLoaiCanhBao().equals("XANH"))
                .map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                .reduce(0L, Long::sum) +
                generalData.stream()
                        .filter(l -> l.getLoaiCanhBao().equals("VANG"))
                        .map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                        .reduce(0L, Long::sum));
        notificationDisplayDto.setVuotNguong(notificationDisplayDto.getTongTonThu() - notificationDisplayDto.getTrongKpi());
        if(notificationDisplayDto.getTongTonThu()==0){
            notificationDisplayDto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f",100F *  0)));;
        }else {
            notificationDisplayDto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * notificationDisplayDto.getVuotNguong() / notificationDisplayDto.getTongTonThu())));
        }
        displayDtoList.add(notificationDisplayDto);

        List<Map.Entry<String, Long>> nlist = new ArrayList<>(mapDataTop10.entrySet());
        nlist.sort(Map.Entry.comparingByValue(Comparator.reverseOrder()));
        for (Map.Entry<String, Long> dataTop10 : nlist) {
            TonThuNotificationDisplayDto dto = new TonThuNotificationDisplayDto();
            dto.setTenChiNhanh(dataTop10.getKey());
            dto.setTongTonThu(dataTop10.getValue());
            if(doiTuongCanhBao.equals("BAN_TGD")){
                dto.setTrongKpi(top10Data.stream()
                        .filter(l -> l.getTinhPhat().equals(dataTop10.getKey()))
                        .filter(l -> l.getLoaiCanhBao().equals("XANH")).map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                        .reduce(0L, Long::sum) +
                        top10Data.stream()
                                .filter(l -> l.getTinhPhat().equals(dataTop10.getKey()))
                                .filter(l -> l.getLoaiCanhBao().equals("VANG")).map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                                .reduce(0L, Long::sum));
            }else if(doiTuongCanhBao.equals("BAN_GIAM_DOC_CHI_NHANH")){
                dto.setTrongKpi(top10Data.stream()
                        .filter(l -> l.getMaBuuCuc().equals(dataTop10.getKey()))
                        .filter(l -> l.getLoaiCanhBao().equals("XANH")).map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                        .reduce(0L, Long::sum) +
                        top10Data.stream()
                                .filter(l -> l.getMaBuuCuc().equals(dataTop10.getKey()))
                                .filter(l -> l.getLoaiCanhBao().equals("VANG")).map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                                .reduce(0L, Long::sum));
            }else{
                dto.setTrongKpi(top10Data.stream()
                        .filter(l -> l.getBuuTaPhat().equals(dataTop10.getKey()))
                        .filter(l -> l.getLoaiCanhBao().equals("XANH")).map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                        .reduce(0L, Long::sum) +
                        top10Data.stream()
                                .filter(l -> l.getBuuTaPhat().equals(dataTop10.getKey()))
                                .filter(l -> l.getLoaiCanhBao().equals("VANG")).map(TonPhatKhauLMTop10ResponeDto::getTongSL)
                                .reduce(0L, Long::sum));
            }

            dto.setVuotNguong(dataTop10.getValue() - dto.getTrongKpi());
            if(dto.getTongTonThu() ==0){
                dto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f",100F *  0)));;
            }else {
                dto.setTyLeVuotNguong(Float.valueOf(String.format("%.2f", 100F * dto.getVuotNguong() / dto.getTongTonThu())));
            }
            displayDtoList.add(dto);
        }
        if (res == BAN_TGD || res == BAN_GIAM_DOC_CHI_NHANH_HN_HCM) {
            displayDtoList = displayDtoList.subList(0, Math.min(displayDtoList.size(), 11));
        } else if (res == TRUONG_BUU_CUC || res == BAN_GIAM_DOC_CHI_NHANH) {
            displayDtoList = displayDtoList.subList(0, Math.min(displayDtoList.size(), 4));
        }
        return displayDtoList;

    }

    public void exportExcelTPNoti(HttpServletResponse response,
                                  String chiNhanhNhan,
                                  String buuCucNhan,
                                  LocalDate ngayBaoCao,
                                  List<String> loaiCanhBao) throws IOException {
        List<TonPhatKhauLMTop8ResDTO> tonPhatExcel;
        if (buuCucNhan.equals("")) {
            if (chiNhanhNhan.equals(""))
                tonPhatExcel = tPKLMrepo.findTop10NotiTGD(ngayBaoCao, loaiCanhBao);
            else
                tonPhatExcel = tPKLMrepo.findTop10NotiCN(chiNhanhNhan, ngayBaoCao, loaiCanhBao);
        } else {
            tonPhatExcel = tPKLMrepo.findTop10NotiBC(chiNhanhNhan, buuCucNhan, ngayBaoCao, loaiCanhBao);
        }
        TonPhatNotificationExcelExporter tTTheoKHExcelExporter = new TonPhatNotificationExcelExporter(tonPhatExcel, loaiCanhBao);
        tTTheoKHExcelExporter.exportDataToExcel(response);
    }
}
