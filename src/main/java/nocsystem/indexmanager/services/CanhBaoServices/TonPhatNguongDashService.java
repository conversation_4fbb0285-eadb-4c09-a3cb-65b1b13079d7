package nocsystem.indexmanager.services.CanhBaoServices;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.*;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatNguongDashRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TonPhatNguongDashService {

    private final TonPhatNguongDashRepository tPNRepo;

    private static final List<String> listHCMHNI = Arrays.asList("HCM01", "HCM02", "HCM03", "HCM04", "HCM05", "HCM06", "HCM07", "HCM08", "HCM09", "HCM10", "HCM11", "HCM12", "HN01", "HN02", "HN03", "HN04", "HN05", "HN06", "HN07", "HN08", "HN09", "HN10");

    public TonPhatNguongDashService(TonPhatNguongDashRepository tPNrepo) {
        this.tPNRepo = tPNrepo;
    }

    private boolean isAdmin() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true");
    }

    //Tồn phát ngưỡng 789 Main Dashboard
    public TonPhatNguongMainDashResDTO tonPhatNguongDif(LocalDate ngayBaoCao, String tinhPhat, String maBuuCuc) {
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        Long sln7_to = Long.valueOf(0);
        Long sln7_yes = Long.valueOf(0);
        Long sln8_to = Long.valueOf(0);
        Long sln8_yes = Long.valueOf(0);
        Long sln9_to = Long.valueOf(0);
        Long sln9_yes = Long.valueOf(0);
        Long sln789_to = Long.valueOf(0);
        Long sln789_yes = Long.valueOf(0);
        Long tongSL_to = Long.valueOf(0);
        Long tongSL_yes = Long.valueOf(0);
        Float tile789_to = Float.valueOf(0);
        Float tile789_yes = Float.valueOf(0);

        List<TonPhatNguongMainDashResDTO> tPN_yes;// = tPNRepo.TonPhatNguong789MainDashTCT(ngayBaoCao.minusDays(1));

        List<TonPhatNguongMainDashResDTO> tPN_to;// = tPNRepo.TonPhatNguong789MainDashTCT(ngayBaoCao);

        List<String> cn, bc;
        if (maBuuCuc.equals("")) {
            if (tinhPhat.equals("")) {
                if (isAdmin()) {
                    tPN_yes = tPNRepo.TonPhatNguong789MainDashTCT(ngayBaoCao.minusDays(1));
                    tPN_to = tPNRepo.TonPhatNguong789MainDashTCT(ngayBaoCao);
                } else {
                    cn = listCN;
                    bc = listBC;
                    tPN_yes = tPNRepo.TonPhatNguong789MainDashTCTNoAdmin(cn, bc, ngayBaoCao.minusDays(1));
                    tPN_to = tPNRepo.TonPhatNguong789MainDashTCTNoAdmin(cn, bc, ngayBaoCao);
                }
            } else {
                if (isAdmin()) {
                    tPN_yes = tPNRepo.TonPhatNguong789MainDashCN(tinhPhat, ngayBaoCao.minusDays(1));
                    tPN_to = tPNRepo.TonPhatNguong789MainDashCN(tinhPhat, ngayBaoCao);
                } else {
                    if (!listCN.contains(tinhPhat)) {
                        return new TonPhatNguongMainDashResDTO();// List<TonPhatKhauMMTop15DashResDTO>();
                    }
                    bc = listBC;
                    tPN_yes = tPNRepo.TonPhatNguong789MainDashCNNoAdmin(tinhPhat, bc, ngayBaoCao.minusDays(1));
                    tPN_to = tPNRepo.TonPhatNguong789MainDashCNNoAdmin(tinhPhat, bc, ngayBaoCao);
                }
            }
        } else {
            if (!isAdmin()) {
                if (!listCN.contains(tinhPhat) || !listBC.contains(maBuuCuc))
                    return new TonPhatNguongMainDashResDTO();
            }
            tPN_yes = tPNRepo.TonPhatNguong789MainDashBC(tinhPhat, maBuuCuc, ngayBaoCao.minusDays(1));
            tPN_to = tPNRepo.TonPhatNguong789MainDashBC(tinhPhat, maBuuCuc, ngayBaoCao);
        }

        TonPhatNguongMainDashResDTO tPNDashResponse = new TonPhatNguongMainDashResDTO();

        if (!tPN_to.isEmpty() && tPN_to != null && tPN_to.get(0).getTongSL() != null) {
            for (TonPhatNguongMainDashResDTO tPNResponse : tPN_to) {
                sln7_to += tPNResponse.getSln7TongSL();
                sln8_to += tPNResponse.getSln8TongSL();
                sln9_to += tPNResponse.getSln9TongSL();
                sln789_to += tPNResponse.getTongSLN789();
                tongSL_to += tPNResponse.getTongSL();
            }
        }

        if (!tPN_yes.isEmpty() && tPN_yes != null && tPN_yes.get(0).getTongSL() != null) {
            for (TonPhatNguongMainDashResDTO tPNResponse : tPN_yes) {
                sln7_yes += tPNResponse.getSln7TongSL();
                sln8_yes += tPNResponse.getSln8TongSL();
                sln9_yes += tPNResponse.getSln9TongSL();
                sln789_yes += tPNResponse.getTongSLN789();
                tongSL_yes += tPNResponse.getTongSL();
            }
        }

        if (tongSL_to == 0) tile789_to = null;
        else
            tile789_to = ((float) sln789_to / (float) tongSL_to) * 100;
        if (tongSL_yes == 0) tile789_yes = null;
        else
            tile789_yes = ((float) sln789_yes / (float) tongSL_yes) * 100;

        TonPhatNguongMainDashResDTO response = new TonPhatNguongMainDashResDTO();
        response.setSln7TongSL(sln7_to);
        response.setSln8TongSL(sln8_to);
        response.setSln9TongSL(sln9_to);
        response.setTongSLN789(sln789_to);
        response.setTiLeN789(tile789_to);
        response.setSln8Dif(sln8_to - sln8_yes);
        response.setSln9Dif(sln9_to - sln9_yes);
        response.setSln7Dif(sln7_to - sln7_yes);
        response.setTongSLN789Dif(sln789_to - sln789_yes);
        response.setTiLeN789Dif(tile789_to == null || tile789_yes == null ? null : tile789_to - tile789_yes);

        return response;
    }

    //Tồn phát ngưỡng 789 Loại Tồn Dashboard
    public TonPhatNguongLoaiTonDashResDTO tonPhatNguongLT(LocalDate ngayBaoCao, String tinhPhat, String maBuuCuc) {
        List<String> listCN = UserContext.getUserData().getListChiNhanhVeriable();
        List<String> listBC = UserContext.getUserData().getListBuuCucVeriable();
        List<Float> sln789 = new ArrayList<>();
        List<Float> sl_ton_phat = new ArrayList<>();
        List<Float> tl_789 = new ArrayList<>();
        List<String> loaiTon = Arrays.asList("CHO_HOAN", "TON_PHAT", "HOAN", "LUU_KHO");
        List<TonPhatNguongLoaiTonDashDTO> tPN;

        List<String> cn, bc;

        //phân nhánh, lấy data
        if (maBuuCuc.equals("")) {
            if (tinhPhat.equals("")) {
                if (isAdmin()) {
                    tPN = tPNRepo.findAllTonPhatNguong789DashLoaiTon(ngayBaoCao);
                } else {
                    cn = listCN;
                    bc = listBC;
                    tPN = tPNRepo.findAllTonPhatNguong789DashLoaiTonNoAdmin(ngayBaoCao, cn, bc);
                }
            } else {
                if (isAdmin()) {
                    tPN = tPNRepo.findAllTonPhatNguong789DashLoaiTonCN(ngayBaoCao, tinhPhat);
                } else {
                    if (!listCN.contains(tinhPhat)) {
                        return new TonPhatNguongLoaiTonDashResDTO();
                    }
                    bc = listBC;
                    tPN = tPNRepo.findAllTonPhatNguong789DashLoaiTonCNNoAdmin(ngayBaoCao, tinhPhat, bc);
                }
            }
        } else {
            if (!isAdmin()) {
                if (!listCN.contains(tinhPhat) || !listBC.contains(maBuuCuc))
                    return new TonPhatNguongLoaiTonDashResDTO();
            }
            tPN = tPNRepo.findAllTonPhatNguong789DashLoaiTonBC(ngayBaoCao, tinhPhat, maBuuCuc);
        }

        //set số theo từng loại tồn
        for (String _loaiTon : loaiTon) {
            Float cham = Float.valueOf(0);
            Float nhanh = Float.valueOf(0);
            Float hoatoc = Float.valueOf(0);
            Float tl_cham = Float.valueOf(0);
            Float tl_nhanh = Float.valueOf(0);
            Float tl_hoatoc = Float.valueOf(0);
            Float tong_nhanh = Float.valueOf(0);
            Float tong_cham = Float.valueOf(0);
            Float tong_hoatoc = Float.valueOf(0);

            //nếu k có data return null
            if (tPN.isEmpty() || tPN.get(0).getTongSLCham() == null) {
                sln789.addAll(Arrays.asList(null, null, null));
                tl_789.addAll(Arrays.asList(null, null, null));
                sl_ton_phat.addAll(Arrays.asList(null, null, null));
                continue;
            }

            for (TonPhatNguongLoaiTonDashDTO tPNResponse : tPN) {
                if (tPNResponse.getLoaiTon().equals(_loaiTon)) {
                    cham = (float) tPNResponse.getSln7Cham() + tPNResponse.getSln8Cham() + tPNResponse.getSln9Cham();
                    nhanh = (float) tPNResponse.getSln7Nhanh() + tPNResponse.getSln8Nhanh() + tPNResponse.getSln9Nhanh();
                    hoatoc = (float) tPNResponse.getSln7HoaToc() + tPNResponse.getSln8HoaToc() + tPNResponse.getSln9HoaToc();
                    tong_nhanh = (float) tPNResponse.getTongSLNhanh();
                    tong_cham = (float) tPNResponse.getTongSLCham();
                    tong_hoatoc = (float) tPNResponse.getTongSLHoaToc();
                    break;
                } else {
                    cham = nhanh = hoatoc = tong_cham = tong_nhanh = tong_hoatoc = null;
                }
            }

            tl_cham = tong_cham == null || tong_cham == 0 || cham == null ? null : ((cham / tong_cham) * 100);
            tl_nhanh = tong_nhanh == null || tong_nhanh == 0 || nhanh == null ? null : ((nhanh / tong_nhanh) * 100);
            tl_hoatoc = tong_hoatoc == null || tong_hoatoc == 0 || hoatoc == null ? null : ((hoatoc / tong_hoatoc) * 100);

            sln789.addAll(Arrays.asList(cham, nhanh, hoatoc));
            tl_789.addAll(Arrays.asList(tl_cham, tl_nhanh, tl_hoatoc));
            sl_ton_phat.addAll(Arrays.asList(tong_cham, tong_nhanh, tong_hoatoc));
        }
        TonPhatNguongLoaiTonDashResDTO tPNDashResponse = new TonPhatNguongLoaiTonDashResDTO();
        tPNDashResponse.setNgayBaoCao(ngayBaoCao);
        tPNDashResponse.setSLN789(sln789);
        tPNDashResponse.setSLTP(sl_ton_phat);
        tPNDashResponse.setTL789(tl_789);
        return tPNDashResponse;
    }

    //Tồn phát ngưỡng 9 HNI-HCM Dashboard
    public TonPhatNguongHNIHCMResDTO tonPhatNguongHNIHCM(LocalDate ngayBaoCao) {
        List<Long> sln9_cham = new ArrayList<>();
        List<Long> sln9_nhanh = new ArrayList<>();
        List<Long> sln9_hoatoc = new ArrayList<>();
        List<Float> tl_9 = new ArrayList<>();
        Float tile_9;

        while (sln9_cham.size() < 22) {
            sln9_cham.add(null);
            sln9_nhanh.add(null);
            sln9_hoatoc.add(null);
            tl_9.add(null);
        }


        List<TonPhatNguongHNIHCMDTO> tPN = tPNRepo.findAllTonPhatNguong789DashHNIHCM(ngayBaoCao, listHCMHNI);

        //check if admin or khác null thì tính
        if (!tPN.isEmpty() && tPN.get(0).getTongSL() != null && isAdmin()) {
            for (TonPhatNguongHNIHCMDTO tPNResponse : tPN) {
                sln9_cham.set(listHCMHNI.indexOf(tPNResponse.getMaBuuCuc()), tPNResponse.getSln9Cham());
                sln9_nhanh.set(listHCMHNI.indexOf(tPNResponse.getMaBuuCuc()), tPNResponse.getSln9Nhanh());
                sln9_hoatoc.set(listHCMHNI.indexOf(tPNResponse.getMaBuuCuc()), tPNResponse.getSln9HoaToc());
                if (tPNResponse.getTongSL() != 0) {
                    tile_9 = ((float) tPNResponse.getSln9TongSL() / (float) tPNResponse.getTongSL()) * 100;
                } else {
                    tile_9 = null;
                }
                tl_9.set(listHCMHNI.indexOf(tPNResponse.getMaBuuCuc()), tile_9);
            }
        }

        TonPhatNguongHNIHCMResDTO tPNDashResponse = new TonPhatNguongHNIHCMResDTO();
        tPNDashResponse.setNgayBaoCao(ngayBaoCao);
        tPNDashResponse.setSLN9Cham(sln9_cham);
        tPNDashResponse.setSLN9Nhanh(sln9_nhanh);
        tPNDashResponse.setSLN9HoaToc(sln9_hoatoc);
        tPNDashResponse.setTiLe9(tl_9);

        return tPNDashResponse;
    }

    //Tồn phát ngưỡng 789 Top 15 Dashboard
    public List<TonPhatNguongTop15N789DashResDTO> tPNTop15N789(LocalDate ngayBaoCao, String tinhPhat, String maBuuCuc) {
        List<TonPhatNguongTop15N789DashResDTO> tPN = new ArrayList<>();
        List<String> cn, bc;

        if (maBuuCuc.isEmpty() || maBuuCuc.equals("")) {
            if (tinhPhat.isEmpty() || tinhPhat.equals(""))
                if (isAdmin()) {
                    tPN = tPNRepo.Top15N789TCT(ngayBaoCao);
                } else {
                    cn = UserContext.getUserData().getListChiNhanhVeriable();
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    tPN = tPNRepo.Top15N789TCTNoAdmin(ngayBaoCao, cn, bc);
                }
            else {
                if (isAdmin()) {
                    tPN = tPNRepo.Top15N789CN(ngayBaoCao, tinhPhat);
                } else {
                    if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat)) {
                        return tPN;
                    }
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    tPN = tPNRepo.Top15N789CNNoAdmin(ngayBaoCao, tinhPhat, bc);
                }
            }
        } else {
            if (!isAdmin()) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat) || !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc))
                    return tPN;
            }
            tPN = tPNRepo.Top15N789BC(ngayBaoCao, tinhPhat, maBuuCuc);
        }

        if(tPN == null){
            return new ArrayList<>();
        }

        if (!tPN.isEmpty() && tPN.get(0).getTongSL() != null) {
            List<TonPhatNguongTop15N789DashResDTO> list2Remove = new ArrayList<>();
            for (TonPhatNguongTop15N789DashResDTO _tPN : tPN) {
                if (_tPN.getTongSL() == 0) {
                    _tPN.settL789(null);
                } else {
                    _tPN.settL789(((float) _tPN.getsL789() / (float) _tPN.getTongSL()) * 100);
                }
                if (_tPN.getsL789() == 0f) {
                    list2Remove.add(_tPN);
                }
            }
            for (TonPhatNguongTop15N789DashResDTO element : list2Remove) {
                tPN.remove(element);
            }
        }

        tPN.removeIf(tpn -> tpn.getsL789() == 0f);

        //sort list theo thứ tự cao nhất -> thấp nhất
        Collections.sort(tPN, Collections.reverseOrder());

        return tPN.stream().limit(15).collect(Collectors.toList());

    }

    //Tồn phát ngưỡng 9 Top 15 Dashboard
    public List<TonPhatNguongTop15N9DashResDTO> tPNTop15N9(LocalDate ngayBaoCao, String tinhPhat, String maBuuCuc) {
        List<TonPhatNguongTop15N9DashResDTO> tPN = new ArrayList<>();
        List<String> cn, bc;

        if (maBuuCuc.isEmpty() || maBuuCuc.equals("")) {
            if (tinhPhat.isEmpty() || tinhPhat.equals(""))
                if (isAdmin()) {
                    tPN = tPNRepo.Top15N9TCT(ngayBaoCao);
                } else {
                    cn = UserContext.getUserData().getListChiNhanhVeriable();
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    tPN = tPNRepo.Top15N9TCTNoAdmin(ngayBaoCao, cn, bc);
                }
            else {
                if (isAdmin()) {
                    tPN = tPNRepo.Top15N9CN(ngayBaoCao, tinhPhat);
                } else {
                    if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat)) {
                        return tPN;
                    }
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    tPN = tPNRepo.Top15N9CNNoAdmin(ngayBaoCao, tinhPhat, bc);
                }
            }
        } else {
            if (!isAdmin()) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat) || !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc))
                    return tPN;
            }
            tPN = tPNRepo.Top15N9BC(ngayBaoCao, tinhPhat, maBuuCuc);
        }

        if(tPN == null){
            return new ArrayList<>();
        }

        if (!tPN.isEmpty() && tPN.get(0).getTong() != null) {
            List<TonPhatNguongTop15N9DashResDTO> list2Remove = new ArrayList<>();
            for (TonPhatNguongTop15N9DashResDTO _tPN : tPN) {
                if (_tPN.getTong() == 0) {
                    _tPN.setTlN9(null);
                } else {
                    _tPN.setTlN9(((float) _tPN.getSlN9() / (float) _tPN.getTong()) * 100);
                }
                if (_tPN.getSlN9() == 0f) {
                    list2Remove.add(_tPN);
                }
            }
            for (TonPhatNguongTop15N9DashResDTO element : list2Remove) {
                tPN.remove(element);
            }
        }

        //sort list theo thứ tự cao nhất -> thấp nhất
        Collections.sort(tPN, Collections.reverseOrder());
        return tPN.stream().limit(15).collect(Collectors.toList());
    }

}
