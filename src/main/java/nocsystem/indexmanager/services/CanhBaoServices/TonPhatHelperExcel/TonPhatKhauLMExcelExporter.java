package nocsystem.indexmanager.services.CanhBaoServices.TonPhatHelperExcel;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

// VT_2023/02/27 NgocNt92 add LM ExcelExporter form.
public class TonPhatKhauLMExcelExporter {
    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private List<TonPhatKhauLMResponseDTO> tonPhatKhauLMDTO;

    public TonPhatKhauLMExcelExporter(List<TonPhatKhauLMResponseDTO> tonPhatKhauLMDTO) {
        workbook = new SXSSFWorkbook(10000);
        this.tonPhatKhauLMDTO = tonPhatKhauLMDTO;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style){
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer){
            cell.setCellValue((Integer) value);
        }else if (value instanceof Double){
            cell.setCellValue((Double) value);
        }else if (value instanceof Boolean){
            cell.setCellValue((Boolean) value);
        }else if (value instanceof Long){
            cell.setCellValue((Long) value);
        }else if(value instanceof String){
            cell.setCellValue((String) value);
        }else if(value instanceof Date){
            cell.setCellValue((Date) value);
        }else if(value instanceof Float){
            cell.setCellValue((Float) value);
        }else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRowTCT(){
        sheet   = workbook.createSheet("TonPhatKhau_TCT");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)13);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Tỉnh phát", style);
        createCell(row, 2, "Loại tồn", style);
        createCell(row, 3, "Loại cảnh báo", style);
        createCell(row, 4, "Số lượng đánh giá mốc LM", style);
        createCell(row, 5, "", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        createCell(row, 9, "", style);
        createCell(row, 10, "", style);
        createCell(row, 11, "", style);
        createCell(row, 12, "", style);
        createCell(row, 13, "", style);
        createCell(row, 14, "Tổng sản lượng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 13));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }

        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Tỉnh phát", style);
        createCell(row2, 2, "Loại tồn", style);
        createCell(row2, 3, "Loại cảnh báo", style);
        createCell(row2, 4, "Vang", style);
        createCell(row2, 5, "DO_1", style);
        createCell(row2, 6, "DO_2", style);
        createCell(row2, 7, "DO_3", style);
        createCell(row2, 8, "DO_4", style);
        createCell(row2, 9, "DO_5", style);
        createCell(row2, 10, "DO_6", style);
        createCell(row2, 11, "DO_7", style);
        createCell(row2, 12, "DO_8", style);
        createCell(row2, 13, "DO_9", style);
        createCell(row2, 14, "Tổng sản lượng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 14, 14));
        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
//        sheet.setColumnWidth(0,8000);
    }

    private void createHeaderRowCN(){
        sheet   = workbook.createSheet("TonPhatKhau_CN");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)13);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Tỉnh phát", style);
        createCell(row, 2, "Loại tồn", style);
        createCell(row, 3, "Loại cảnh báo", style);
        createCell(row, 4, "Mã bưu cục phát", style);
        createCell(row, 5, "Số lượng đánh giá mốc LM", style);
        createCell(row, 6, "", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        createCell(row, 9, "", style);
        createCell(row, 10, "", style);
        createCell(row, 11, "", style);
        createCell(row, 12, "", style);
        createCell(row, 13, "", style);
        createCell(row, 14, "", style);
        createCell(row, 15, "Tổng sản lượng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 5, 14));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }

        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Tỉnh phát", style);
        createCell(row2, 2, "Loại tồn", style);
        createCell(row2, 3, "Loại cảnh báo", style);
        createCell(row2, 4, "Mã bưu cục phát", style);
        createCell(row2, 5, "Vang", style);
        createCell(row2, 6, "DO_1", style);
        createCell(row2, 7, "DO_2", style);
        createCell(row2, 8, "DO_3", style);
        createCell(row2, 9, "DO_4", style);
        createCell(row2, 10, "DO_5", style);
        createCell(row2, 11, "DO_6", style);
        createCell(row2, 12, "DO_7", style);
        createCell(row2, 13, "DO_8", style);
        createCell(row2, 14, "DO_9", style);
        createCell(row2, 15, "Tổng sản lượng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 15, 15));
        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    private void createHeaderRowBT(){
        sheet   = workbook.createSheet("TonPhatKhau_BT");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)13);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Tỉnh phát", style);
        createCell(row, 2, "Loại tồn", style);
        createCell(row, 3, "Loại cảnh báo", style);
        createCell(row, 4, "Mã bưu cục phát", style);
        createCell(row, 5, "Bưu tá phát ", style);
        createCell(row, 6, "Số lượng đánh giá mốc LM", style);
        createCell(row, 7, "", style);
        createCell(row, 8, "", style);
        createCell(row, 9, "", style);
        createCell(row, 10, "", style);
        createCell(row, 11, "", style);
        createCell(row, 12, "", style);
        createCell(row, 13, "", style);
        createCell(row, 14, "", style);
        createCell(row, 15, "", style);
        createCell(row, 16, "Tổng sản lượng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 6, 15));
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }

        Row row2 = sheet.createRow(1);
        createCell(row2, 0, "STT", style);
        createCell(row2, 1, "Tỉnh phát", style);
        createCell(row2, 2, "Loại tồn", style);
        createCell(row2, 3, "Loại cảnh báo", style);
        createCell(row2, 4, "Mã bưu cục phát", style);
        createCell(row2, 5, "Bưu tá phát ", style);
        createCell(row2, 6, "Vang", style);
        createCell(row2, 7, "DO_1", style);
        createCell(row2, 8, "DO_2", style);
        createCell(row2, 9, "DO_3", style);
        createCell(row2, 10, "DO_4", style);
        createCell(row2, 11, "DO_5", style);
        createCell(row2, 12, "DO_6", style);
        createCell(row2, 13, "DO_7", style);
        createCell(row2, 14, "DO_8", style);
        createCell(row2, 15, "DO_9", style);
        createCell(row2, 16, "Tổng sản lượng", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 16, 16));
        for(int i = 0; i<=row2.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    public void writeCustomDataTCT() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short)12);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        Integer soThuTu = new Integer(0);

        for (TonPhatKhauLMResponseDTO tonPhatKhauLM : tonPhatKhauLMDTO){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String tinhPhat = tonPhatKhauLM.getTinhPhat();
            String loaiTon = tonPhatKhauLM.getLoaiTon();
            String loaiCanhBao = tonPhatKhauLM.getLoaiCanhBao();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, tinhPhat, style);
            createCell(row, columnCount++, loaiTon, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMVang(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo1(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo2(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo3(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo4(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo5(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo6(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo7(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo8(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo9(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getTongSL(), style);
        }
    }
    public void writeCustomDataCN() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short)12);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        Integer soThuTu = new Integer(0);

        for (TonPhatKhauLMResponseDTO tonPhatKhauLM : tonPhatKhauLMDTO){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String tinhPhat = tonPhatKhauLM.getTinhPhat();
            String loaiTon = tonPhatKhauLM.getLoaiTon();
            String loaiCanhBao = tonPhatKhauLM.getLoaiCanhBao();
            String maBuuCucPhat = tonPhatKhauLM.getMaBuuCuc();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, tinhPhat, style);
            createCell(row, columnCount++, loaiTon, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, maBuuCucPhat, style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMVang(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo1(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo2(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo3(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo4(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo5(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo6(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo7(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo8(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo9(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getTongSL(), style);
        }
    }

    public void writeCustomDataBT() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short)12);
//        font.setFontName("Times New Roman");
        style.setFont(font);
        Integer soThuTu = new Integer(0);

        for (TonPhatKhauLMResponseDTO tonPhatKhauLM : tonPhatKhauLMDTO){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String tinhPhat = tonPhatKhauLM.getTinhPhat();
            String loaiTon = tonPhatKhauLM.getLoaiTon();
            String loaiCanhBao = tonPhatKhauLM.getLoaiCanhBao();
            String maBuuCucPhat = tonPhatKhauLM.getMaBuuCuc();
            String buuTaPhat = tonPhatKhauLM.getBuuTaPhat();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, tinhPhat, style);
            createCell(row, columnCount++, loaiTon, style);
            createCell(row, columnCount++, loaiCanhBao, style);
            createCell(row, columnCount++, maBuuCucPhat, style);
            createCell(row, columnCount++, buuTaPhat, style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMVang(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo1(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo2(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo3(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo4(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo5(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo6(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo7(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo8(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getLMDo9(), style);
            createCell(row, columnCount++, tonPhatKhauLM.getTongSL(), style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response, List<String> tinhPhat, List<String> buuCucNhan) throws IOException {

        if(buuCucNhan == null || buuCucNhan.isEmpty()){
            if(tinhPhat == null || tinhPhat.isEmpty()) {
                createHeaderRowTCT();
                writeCustomDataTCT();
            }else {
                createHeaderRowCN();
                writeCustomDataCN();
            }
        }else {
            createHeaderRowBT();
            writeCustomDataBT();
        }
        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        };
    }
}
