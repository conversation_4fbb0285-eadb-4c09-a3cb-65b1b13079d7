package nocsystem.indexmanager.services.CanhBaoServices.Notification;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class TonPhatNotificationExcelExporter {
    private SXSSFWorkbook workbook;
    //    private XSSFWorkbook workbook1;
    private SXSSFSheet sheet;
    private final List<TonPhatKhauLMTop8ResDTO> tonPhatNotiDTO;
    private final List<String> nguongCanhBao;

    public TonPhatNotificationExcelExporter(List<TonPhatKhauLMTop8ResDTO> tonPhatNotiDTO, List<String> nguongCanhBao) {
        this.nguongCanhBao = nguongCanhBao;
        workbook = new SXSSFWorkbook(10000);
        this.tonPhatNotiDTO = tonPhatNotiDTO;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRow() {
        sheet = workbook.createSheet("TonPhat");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Đơn vị", style);
        int count = 1;
        for (String str : nguongCanhBao) {
            createCell(row, 1 + count, str, style);
            count++;
        }
        createCell(row, 1 + count, "Tổng SL tồn", style);
        for (int i = 0; i <= row.getLastCellNum(); i++) {
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    public void writeCustomData() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();

        Integer soThuTu = new Integer(0);
        for (TonPhatKhauLMTop8ResDTO tonPhat : tonPhatNotiDTO) {
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String donVi = tonPhat.getDonVi();
            Long vang = tonPhat.getlMVang();
            Long do1 = tonPhat.getlMDo1();
            Long do2 = tonPhat.getlMDo2();
            Long do3 = tonPhat.getlMDo3();
            Long do4 = tonPhat.getlMDo4();
            Long do5 = tonPhat.getlMDo5();
            Long do6 = tonPhat.getlMDo6();
            Long do7 = tonPhat.getlMDo7();
            Long do8 = tonPhat.getlMDo8();
            Long do9 = tonPhat.getlMDo9();
            Long tongSL = 0L;

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, donVi, style);
            for (String str : nguongCanhBao) {
                switch (str) {
                    case "VANG":
                        createCell(row, columnCount++, vang, style);
                        tongSL += vang;
                        break;
                    case "DO_1":
                        createCell(row, columnCount++, do1, style);
                        tongSL += do1;
                        break;
                    case "DO_2":
                        createCell(row, columnCount++, do2, style);
                        tongSL += do2;
                        break;
                    case "DO_3":
                        createCell(row, columnCount++, do3, style);
                        tongSL += do3;
                        break;
                    case "DO_4":
                        createCell(row, columnCount++, do4, style);
                        tongSL += do4;
                        break;
                    case "DO_5":
                        createCell(row, columnCount++, do5, style);
                        tongSL += do5;
                        break;
                    case "DO_6":
                        createCell(row, columnCount++, do6, style);
                        tongSL += do6;
                        break;
                    case "DO_7":
                        createCell(row, columnCount++, do7, style);
                        tongSL += do7;
                        break;
                    case "DO_8":
                        createCell(row, columnCount++, do8, style);
                        tongSL += do8;
                        break;
                    case "DO_9":
                        createCell(row, columnCount++, do9, style);
                        tongSL += do9;
                        break;
                    default:
                        break;
                }
            }
            createCell(row, columnCount++, tongSL, style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {

        createHeaderRow();
        writeCustomData();
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }
}
