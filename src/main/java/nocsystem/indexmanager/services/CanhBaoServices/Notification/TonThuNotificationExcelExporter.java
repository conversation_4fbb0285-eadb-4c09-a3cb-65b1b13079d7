package nocsystem.indexmanager.services.CanhBaoServices.Notification;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification.TonThuNotiExcelDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class TonThuNotificationExcelExporter {
    private SXSSFWorkbook workbook;
//    private XSSFWorkbook workbook1;
    private SXSSFSheet sheet;
    private final List<TonThuNotiExcelDTO> tonThuNotiDto;
    private final List<String> nguongCanhBao;

    public TonThuNotificationExcelExporter(List<TonThuNotiExcelDTO> tonThuNotiDto, List<String> nguongCanhBao) {
        this.nguongCanhBao = nguongCanhBao;
        workbook = new SXSSFWorkbook(10000);
        this.tonThuNotiDto = tonThuNotiDto;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style){
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer){
            cell.setCellValue((Integer) value);
        }else if (value instanceof Double){
            cell.setCellValue((Double) value);
        }else if (value instanceof Boolean){
            cell.setCellValue((Boolean) value);
        }else if (value instanceof Long){
            cell.setCellValue((Long) value);
        }else if(value instanceof String){
            cell.setCellValue((String) value);
        }else if(value instanceof Date){
            cell.setCellValue((Date) value);
        }else if(value instanceof Float){
            cell.setCellValue((Float) value);
        }else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRow(){
        sheet   = workbook.createSheet("TonThu");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        createCell(row, 0, "STT", style);
        createCell(row, 1, "Đơn vị", style);

        int count = 1;
        for(String str : nguongCanhBao){
            createCell(row, 1 + count, str, style);
            count ++;
        }
        createCell(row, 1 + count, "Tổng SL tồn", style);
        for(int i = 0; i<=row.getLastCellNum();i++){
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
    }

    public void writeCustomData() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();

        Integer soThuTu = new Integer(0);
        for (TonThuNotiExcelDTO tonThuKH : tonThuNotiDto){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            soThuTu++;
            String donVi = tonThuKH.getDonVi();
            Long xanh = tonThuKH.getXanh();
            Long vang = tonThuKH.getVang();
            Long do1 = tonThuKH.getDo1();
            Long do2 = tonThuKH.getDo2();
            Long do3 = tonThuKH.getDo3();

            createCell(row, columnCount++, soThuTu, style);
            createCell(row, columnCount++, donVi, style);
            for(String str : nguongCanhBao){
                switch (str){
                    case "XANH":
                        createCell(row, columnCount++, xanh, style);
                        break;
                    case "VANG":
                        createCell(row, columnCount++, vang, style);
                        break;
                    case "DO_1":
                        createCell(row, columnCount++, do1, style);
                        break;
                    case "DO_2":
                        createCell(row, columnCount++, do2, style);
                        break;
                    case "DO_3":
                        createCell(row, columnCount++, do3, style);
                        break;
                    default:
                        break;
                }
            }
            createCell(row, columnCount++, tonThuKH.getTong(), style);
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {

        createHeaderRow();
        writeCustomData();
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.close();
        outputStream.close();
    }
}
