package nocsystem.indexmanager.services.CanhBaoServices.Notification;

import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauLMTop8ResDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.SMSNotiResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatKhauLMRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonThu.TonThuTheoKHRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TrungTam.TonGiaoNhanLXLOGRepository;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NotificationDataService {

    private final TonPhatKhauLMRepository tpklmRepo;

    private final TonThuTheoKHRepository ttkhRepo;

    private final TonGiaoNhanLXLOGRepository ttktRepo;

    public NotificationDataService(TonPhatKhauLMRepository tpklmrepo, TonThuTheoKHRepository ttkhRepo, TonGiaoNhanLXLOGRepository ttktRepo) {
        this.tpklmRepo = tpklmrepo;
        this.ttkhRepo = ttkhRepo;
        this.ttktRepo = ttktRepo;
    }

    private String getCa() {
        String ca;
        Calendar rightNow = Calendar.getInstance();
        int hour = rightNow.get(Calendar.HOUR_OF_DAY);

        if (hour < 18 && hour > 12) {
            ca = "CA2";
        } else ca = "CA1";
        return ca;
    }

    public SMSNotiResponseDTO getSMSData(LocalDate ngayBaoCao, String doiTuongCanhBao, String chiNhanh, String buuCuc) {
        String ca = getCa();
        TonPhatKhauLMTop8ResDTO tonPhat = new TonPhatKhauLMTop8ResDTO();
        TonPhatKhauLMTop8ResDTO tongTonPhat = new TonPhatKhauLMTop8ResDTO();
        List<TonPhatKhauLMTop8ResDTO> top10DataTP = new ArrayList<>();
        TonThuTheoKHResponseDTO tonThu = new TonThuTheoKHResponseDTO();
        TonThuTheoKHResponseDTO tongTonThu = new TonThuTheoKHResponseDTO();
        List<TonThuTheoKHResponseDTO> topDataTT = new ArrayList<>();
        List<String> nguongCanhBaoTP = Arrays.asList("DO_7", "DO_8", "DO_9");
        List<String> nguongCanhBaoTT = Arrays.asList("DO_1", "DO_2", "DO_3");
        List<String> nguongCanhBaoTTAll = Arrays.asList("XANH", "VANG", "DO_1", "DO_2", "DO_3");
        List<String> nguongCanhBaoTPAll = Arrays.asList("VANG", "DO_1", "DO_2", "DO_3", "DO_4", "DO_5", "DO_6", "DO_7", "DO_8", "DO_9");
        SMSNotiResponseDTO response = new SMSNotiResponseDTO();

        SMSNotiResponseDTO tp = new SMSNotiResponseDTO();
        SMSNotiResponseDTO tt = new SMSNotiResponseDTO();
        TTKTNotiResponseDTO ttkt = new TTKTNotiResponseDTO();

        switch (doiTuongCanhBao) {
            case "BAN_TGD":
                tonPhat = tpklmRepo.findTonPhatSMSTCT(ngayBaoCao, nguongCanhBaoTP);
                top10DataTP = tpklmRepo.findTop10NotiTGD(ngayBaoCao, nguongCanhBaoTP);
                tongTonPhat = tpklmRepo.findTonPhatSMSTCT(ngayBaoCao, nguongCanhBaoTPAll);
                tonThu = ttkhRepo.getDataSMSTGD(ngayBaoCao, ca, nguongCanhBaoTT);
                topDataTT = ttkhRepo.TonThuTheoKHSMSTop10TCT(ngayBaoCao, ca, nguongCanhBaoTT);
                tongTonThu = ttkhRepo.getDataSMSTGD(ngayBaoCao, ca, nguongCanhBaoTTAll);
                tp = TPNotiData(top10DataTP, tonPhat, tongTonPhat);
                tt = TTNotiData(topDataTT, tonThu, tongTonThu, doiTuongCanhBao);
                ttkt = TTKTNotiData(ngayBaoCao);
                break;
            case "BAN_GIAM_DOC_CHI_NHANH":
                tonPhat = tpklmRepo.findTonPhatSMSCN(chiNhanh, ngayBaoCao);
                top10DataTP = tpklmRepo.findTop10NotiCN(chiNhanh, ngayBaoCao, Arrays.asList("ALL"));
                tongTonPhat = tpklmRepo.findTonPhatSMSCNAll(chiNhanh, ngayBaoCao);
                tonThu = ttkhRepo.getDataSMSCN(ngayBaoCao, chiNhanh, ca, nguongCanhBaoTT);
                tongTonThu = ttkhRepo.getDataSMSCN(ngayBaoCao, chiNhanh, ca, nguongCanhBaoTTAll);
                topDataTT = ttkhRepo.TonThuTheoKHSMSTop10CN(ngayBaoCao, chiNhanh, ca, nguongCanhBaoTTAll);
                tp = TPNotiData(top10DataTP, tonPhat, tongTonPhat);
                tt = TTNotiData(topDataTT, tonThu, tongTonThu, doiTuongCanhBao);
                break;
            case "TRUONG_BUU_CUC":
                tonPhat = tpklmRepo.findTonPhatSMSBC(buuCuc, ngayBaoCao);
                top10DataTP = tpklmRepo.findTop10NotiBC(chiNhanh, buuCuc, ngayBaoCao, nguongCanhBaoTP);
                tongTonPhat = tpklmRepo.findTonPhatSMSBCAll(buuCuc, ngayBaoCao);
                tonThu = ttkhRepo.getDataSMSBC(ngayBaoCao, chiNhanh, buuCuc, ca, nguongCanhBaoTT);
                tongTonThu = ttkhRepo.getDataSMSBC(ngayBaoCao, chiNhanh, buuCuc, ca, nguongCanhBaoTTAll);
                topDataTT = ttkhRepo.TonThuTheoKHSMSTop10BC(ngayBaoCao, chiNhanh, buuCuc, ca, nguongCanhBaoTTAll);
                tp = TPNotiData(top10DataTP, tonPhat, tongTonPhat);
                tt = TTNotiData(topDataTT, tonThu, tongTonThu, doiTuongCanhBao);
                break;
            case "TTKT":
                ttkt = TTKTNotiData(ngayBaoCao);
                response.setConChiTieu(ttkt.getConChiTieu());
                response.setQuaChiTieu(ttkt.getQuaChiTieu());
                response.setTlConChiTieu(ttkt.getTlConChiTieu());
                response.setTlQuaChiTieu(ttkt.getTlQuaChiTieu());
                response.setTongTTKT(ttkt.getTong());
                return response;
            default:
                break;
        }

        response.setTongTonPhat(tp.getTongTonPhat());
        response.setTlTonPhat(tp.getTlTonPhat());
        response.setListTonPhat(tp.getListTonPhat());
        response.setTongTonThu(tt.getTongTonThu());
        response.setTlTonThu(tt.getTlTonThu());
        response.setListTonThu(tt.getListTonThu());
        response.setConChiTieu(ttkt.getConChiTieu());
        response.setQuaChiTieu(ttkt.getQuaChiTieu());
        response.setTlConChiTieu(ttkt.getTlConChiTieu());
        response.setTlQuaChiTieu(ttkt.getTlQuaChiTieu());
        response.setTongTTKT(ttkt.getTong());
        return response;
    }

    public TTKTNotiResponseDTO TTKTNotiData(LocalDate ngayBaoCao) {
        List<String> listNguong = Arrays.asList("QUA_0H", "QUA_2H", "QUA_6H", "QUA_12H", "QUA_24H", "QUA_48H", "QUA_72H", "QUA_96H", "QUA_120H");
        TTKTNotiResponseDTO ttktData = ttktRepo.findDataTGD(ngayBaoCao);

        if(ttktData == null) return new TTKTNotiResponseDTO(0L,0L,0L,0F,0F);

        Map<String, Long> map = new HashMap<>();
        Map<String, Long> map2 = new HashMap<>();
        map.put("QUA_0H", ttktData.getQua0h() != null ? ttktData.getQua0h() : 0L);
        map.put("QUA_2H", ttktData.getQua2h() != null ? ttktData.getQua2h() : 0L);
        map.put("QUA_6H", ttktData.getQua6h() != null ? ttktData.getQua6h() : 0L);
        map.put("QUA_12H", ttktData.getQua12h() != null ? ttktData.getQua12h() : 0L);
        map.put("QUA_24H", ttktData.getQua24h() != null ? ttktData.getQua24h() : 0L);
        map.put("QUA_48H", ttktData.getQua48h() != null ? ttktData.getQua48h() : 0L);
        map.put("QUA_72H", ttktData.getQua72h() != null ? ttktData.getQua72h() : 0L);
        map.put("QUA_96H", ttktData.getQua96h() != null ? ttktData.getQua96h() : 0L);
        map.put("QUA_120H", ttktData.getQua120h() != null ? ttktData.getQua120h() : 0L);

        TTKTNotiResponseDTO response = new TTKTNotiResponseDTO();
        for (String str : listNguong) {
            map2.put(str, map.get(str));
        }

        response.setQua0h(map2.containsKey("QUA_0H") ? map2.get("QUA_0H") : 0L);
        response.setQua2h(map2.containsKey("QUA_2H") ? map2.get("QUA_2H") : 0L);
        response.setQua6h(map2.containsKey("QUA_6H") ? map2.get("QUA_6H") : 0L);
        response.setQua12h(map2.containsKey("QUA_12H") ? map2.get("QUA_12H") : 0L);
        response.setQua24h(map2.containsKey("QUA_24H") ? map2.get("QUA_24H") : 0L);
        response.setQua48h(map2.containsKey("QUA_48H") ? map2.get("QUA_48H") : 0L);
        response.setQua72h(map2.containsKey("QUA_72H") ? map2.get("QUA_72H") : 0L);
        response.setQua96h(map2.containsKey("QUA_96H") ? map2.get("QUA_96H") : 0L);
        response.setQua120h(map2.containsKey("QUA_120H") ? map2.get("QUA_120H") : 0L);

        response.setTong(response.getQua0h() + response.getQua6h() + response.getQua2h() + response.getQua12h() + response.getQua24h() + response.getQua48h() + response.getQua72h() + response.getQua96h() + response.getQua120h());
        response.setConChiTieu(response.getQua0h() + response.getQua2h());
        response.setQuaChiTieu(response.getTong() - response.getConChiTieu());

        if (response.getTong() == 0 || response.getTong() == null) {
            response.setTlConChiTieu(0f);
            response.setTlQuaChiTieu(0f);
        } else {
            response.setTlConChiTieu(((float) response.getConChiTieu() / (float) response.getTong()) * 100);
            response.setTlQuaChiTieu(((float) response.getQuaChiTieu() / (float) response.getTong()) * 100);
        }


        return response;
    }

    private SMSNotiResponseDTO TPNotiData(List<TonPhatKhauLMTop8ResDTO> top10DataTP, TonPhatKhauLMTop8ResDTO tonPhat, TonPhatKhauLMTop8ResDTO tongTonPhat) {
        List<String> tenDonVi = new ArrayList<>();
        SMSNotiResponseDTO data = new SMSNotiResponseDTO();
        Collections.sort(top10DataTP, Collections.reverseOrder());
        List<TonPhatKhauLMTop8ResDTO> top10 = top10DataTP.stream().limit(3).collect(Collectors.toList());
        Integer count = 0;
        for (TonPhatKhauLMTop8ResDTO e : top10) {
            tenDonVi.add(e.getDonVi());
            count++;
        }
        data.setTongTonPhat(tonPhat.getTongSL() != null ? tonPhat.getTongSL() : 0L);
        if (tonPhat.getTongSL() != null && tongTonPhat.getTongSL() != null)
            data.setTlTonPhat(((float) tonPhat.getTongSL() / (float) tongTonPhat.getTongSL()) * 100);
        else
            data.setTlTonPhat(0F);
        data.setListTonPhat(tenDonVi);
        return data;
    }

    private SMSNotiResponseDTO TTNotiData(List<TonThuTheoKHResponseDTO> top10DataTT, TonThuTheoKHResponseDTO tonThu, TonThuTheoKHResponseDTO tongTonThu, String doiTuongCanhBao) {
        List<String> tenDonVi = new ArrayList<>();
        SMSNotiResponseDTO data = new SMSNotiResponseDTO();
        Collections.sort(top10DataTT, Collections.reverseOrder());
        List<TonThuTheoKHResponseDTO> top10 = top10DataTT.stream().limit(3).collect(Collectors.toList());
        Integer count = 0;
        for (TonThuTheoKHResponseDTO e : top10) {
            tenDonVi.add(e.getChiNhanhNhan());
            count++;
        }
        data.setTongTonThu(tonThu.getTong() != null ? tonThu.getTong() : 0L);
        if (tonThu.getTong() != null && tongTonThu.getTong() != null)
            data.setTlTonThu(((float) tonThu.getTong() / (float) tongTonThu.getTong()) * 100);
        else
            data.setTlTonThu(0F);
        data.setListTonThu(tenDonVi);
        return data;
    }

}
