package nocsystem.indexmanager.services.CanhBaoServices;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.*;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.TonPhatKhauLMResponseDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatKhauLMRepository;
import nocsystem.indexmanager.repositories.CanhBaoRepositories.TonPhat.TonPhatKhauMMRepository;
import nocsystem.indexmanager.services.CanhBaoServices.TonPhatHelperExcel.TonPhatKhauMMExcelExporter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TonPhatKhauMMService {

    private final TonPhatKhauMMRepository tPKMMrepo;

    private final TonPhatKhauLMRepository tPKLMrepo;

    public TonPhatKhauMMService(TonPhatKhauMMRepository tPNrepo, TonPhatKhauLMRepository tPKLMrepo) {
        this.tPKMMrepo = tPNrepo;
        this.tPKLMrepo = tPKLMrepo;
    }

    public static String sort;

    public ListContentPageDto<TonPhatKhauLMResponseDTO> findAllDataBC(List<String> tinhPhat, List<String> loaiTon,
                                                                      List<String> maBuuCuc, LocalDate ngayBaoCao,
                                                                      List<String> loaiCanhBao, List<String> loaiDichVu,
                                                                      Integer pageQuery, Integer pageSize) {
        Pageable page = Pageable.unpaged();
        page = sort.isEmpty() ? PageRequest.of(pageQuery, pageSize) :
                sort.equals("asc") ? PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.ASC, "tongSL")) :
                        sort.equals("desc") ? PageRequest.of(pageQuery, pageSize, Sort.by(Sort.Direction.DESC, "tongSL")) : Pageable.unpaged();

        loaiTon = loaiTon.isEmpty() ? Arrays.asList("HOAN", "CHO_HOAN", "LUU_KHO", "TON_PHAT") : loaiTon;
        loaiCanhBao = loaiCanhBao.isEmpty() ? Arrays.asList("VANG", "DO_1", "DO_2", "DO_3", "DO_4", "DO_5", "DO_6", "DO_7", "DO_8", "DO_9") : loaiCanhBao;

        Page<TonPhatKhauLMResponseDTO> pageResult;

        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
            if (tinhPhat == null || tinhPhat.isEmpty()) {
                tinhPhat = isAdmin() ? Arrays.asList("") : ListVariableLocation.listChiNhanhVeriable;
                pageResult = isAdmin() ? tPKLMrepo.findTonPhatKhauMMTCT(loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, page)
                        : tPKLMrepo.findTonPhatKhauMMTCT1(tinhPhat, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, page);
            } else {
                if (ListVariableLocation.listChiNhanhVeriable != null)
                    tinhPhat.retainAll(ListVariableLocation.listChiNhanhVeriable);
                maBuuCuc = ListVariableLocation.listBuuCucVeriable;
                pageResult = isAdmin() ? tPKLMrepo.findTonPhatKhauMMCN(tinhPhat, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, page)
                        : tPKLMrepo.findTonPhatKhauMMCN1(tinhPhat, maBuuCuc, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu, page);
            }
        } else {
            tinhPhat = isAdmin() ? tinhPhat : ListVariableLocation.listChiNhanhVeriable;
            if (ListVariableLocation.listBuuCucVeriable != null)
                maBuuCuc.retainAll(ListVariableLocation.listBuuCucVeriable);
            pageResult = tPKLMrepo.findTonPhatKhauMMBC(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu, page);
        }

        return new ListContentPageDto<TonPhatKhauLMResponseDTO>(pageResult);
    }

    private boolean isAdmin() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsPhapChe().equalsIgnoreCase("true");
    }

    public List<TonPhatKhauLMResponseDTO> getDataSum(List<String> tinhPhat, List<String> loaiTon,
                                                     List<String> maBuuCuc, LocalDate ngayBaoCao,
                                                     List<String> loaiCanhBao, List<String> loaiDichVu) {
        loaiTon = loaiTon.isEmpty() ? Arrays.asList("HOAN", "CHO_HOAN", "LUU_KHO", "TON_PHAT") : loaiTon;
        loaiCanhBao = loaiCanhBao.isEmpty() ? Arrays.asList("VANG", "DO_1", "DO_2", "DO_3", "DO_4", "DO_5", "DO_6", "DO_7", "DO_8", "DO_9") : loaiCanhBao;
//        dGMocMM = dGMocMM.isEmpty() ? Arrays.asList("OK", "LOI_MM") : dGMocMM;

        if (maBuuCuc == null || maBuuCuc.isEmpty()) {
            if (tinhPhat == null || tinhPhat.isEmpty()) {
                tinhPhat = isAdmin() ? Arrays.asList("") : ListVariableLocation.listChiNhanhVeriable;
                return isAdmin() ? tPKLMrepo.findTonPhatKhauMMTCTSum(loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu)
                        : tPKLMrepo.findTonPhatKhauMMTCTSum1(tinhPhat, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu);
            } else {
                if (ListVariableLocation.listChiNhanhVeriable != null)
                    tinhPhat.retainAll(ListVariableLocation.listChiNhanhVeriable);
                maBuuCuc = ListVariableLocation.listBuuCucVeriable;
                return isAdmin() ? tPKLMrepo.findTonPhatKhauMMCNSum(tinhPhat, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu)
                        : tPKLMrepo.findTonPhatKhauMMCNSum1(tinhPhat, maBuuCuc, loaiTon, ngayBaoCao, loaiCanhBao, loaiDichVu);
            }
        } else {
            tinhPhat = isAdmin() ? tinhPhat : ListVariableLocation.listChiNhanhVeriable;
            if (ListVariableLocation.listBuuCucVeriable != null)
                maBuuCuc.retainAll(ListVariableLocation.listBuuCucVeriable);
            return tPKLMrepo.findTonPhatKhauMMBCSum(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu);
        }
    }

    public List<TonPhatKhauLMResponseDTO> SummaryCalculation(List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc,
                                                             LocalDate ngayBaoCao, List<String> loaiCanhBao, List<String> loaiDichVu) {

        List<TonPhatKhauLMResponseDTO> tPKLM = getDataSum(tinhPhat, loaiTon, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu);
        Long do9 = Long.valueOf(0);
        Long do8 = Long.valueOf(0);
        Long do7 = Long.valueOf(0);
        Long do6 = Long.valueOf(0);
        Long do5 = Long.valueOf(0);
        Long do4 = Long.valueOf(0);
        Long do3 = Long.valueOf(0);
        Long do2 = Long.valueOf(0);
        Long do1 = Long.valueOf(0);
        Long vang = Long.valueOf(0);
        Long tongSL = Long.valueOf(0);


        for (TonPhatKhauLMResponseDTO tPKMMResponse : tPKLM) {
            do9 += tPKMMResponse.getLMDo9();
            do8 += tPKMMResponse.getLMDo8();
            do7 += tPKMMResponse.getLMDo7();
            do6 += tPKMMResponse.getLMDo6();
            do5 += tPKMMResponse.getLMDo5();
            do4 += tPKMMResponse.getLMDo4();
            do3 += tPKMMResponse.getLMDo3();
            do2 += tPKMMResponse.getLMDo2();
            do1 += tPKMMResponse.getLMDo1();
            vang += tPKMMResponse.getLMVang();
            tongSL += tPKMMResponse.getTongSL();
        }

        TonPhatKhauLMResponseDTO tPKMMResponse = new TonPhatKhauLMResponseDTO();
        tPKMMResponse.setLMDo9(do9);
        tPKMMResponse.setLMDo8(do8);
        tPKMMResponse.setLMDo7(do7);
        tPKMMResponse.setLMDo6(do6);
        tPKMMResponse.setLMDo5(do5);
        tPKMMResponse.setLMDo4(do4);
        tPKMMResponse.setLMDo3(do3);
        tPKMMResponse.setLMDo2(do2);
        tPKMMResponse.setLMDo1(do1);
        tPKMMResponse.setLMVang(vang);
        tPKMMResponse.setTongSL(tongSL);

        List<TonPhatKhauLMResponseDTO> listMM = new ArrayList<>();
        listMM.add(tPKMMResponse);

        return listMM;
    }

    public TonPhatKhauMMMainDashResDTO khauMMMainDash(String tinhPhat, String maBuuCuc, LocalDate ngayBaoCao) {
        List<TonPhatKhauMMMainDashResDTO> listTo, listYes, listTongSLYes, listTongSLTo;
        List<String> cn, bc;

        if (maBuuCuc.equals("")) {
            if (tinhPhat.equals("")) {
                if (isAdmin()) {
                    listTo = tPKMMrepo.findTonPhatKhauMMDashMainTCT(ngayBaoCao, Arrays.asList("LOI_MM"));
                    listYes = tPKMMrepo.findTonPhatKhauMMDashMainTCT(ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM"));
                    listTongSLYes = tPKMMrepo.findTonPhatKhauMMDashMainTCT(ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM", "OK"));
                    listTongSLTo = tPKMMrepo.findTonPhatKhauMMDashMainTCT(ngayBaoCao, Arrays.asList("LOI_MM", "OK"));
                } else {
                    cn = UserContext.getUserData().getListChiNhanhVeriable();
                    listTo = tPKMMrepo.findTonPhatKhauMMDashMainTCT1(cn, ngayBaoCao, Arrays.asList("LOI_MM"));
                    listYes = tPKMMrepo.findTonPhatKhauMMDashMainTCT1(cn, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM"));
                    listTongSLTo = tPKMMrepo.findTonPhatKhauMMDashMainTCT1(cn, ngayBaoCao, Arrays.asList("LOI_MM", "OK"));
                    listTongSLYes = tPKMMrepo.findTonPhatKhauMMDashMainTCT1(cn, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM", "OK"));
                }
            } else {
                if (isAdmin()) {
                    listTo = tPKMMrepo.findTonPhatKhauMMDashMainCN(tinhPhat, ngayBaoCao, Arrays.asList("LOI_MM"));
                    listYes = tPKMMrepo.findTonPhatKhauMMDashMainCN(tinhPhat, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM"));
                    listTongSLTo = tPKMMrepo.findTonPhatKhauMMDashMainCN(tinhPhat, ngayBaoCao, Arrays.asList("LOI_MM", "OK"));
                    listTongSLYes = tPKMMrepo.findTonPhatKhauMMDashMainCN(tinhPhat, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM", "OK"));
                } else {
                    if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat)) {
                        return new TonPhatKhauMMMainDashResDTO();
                    }
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    listTo = tPKMMrepo.findTonPhatKhauMMDashMainCN1(tinhPhat, bc, ngayBaoCao, Arrays.asList("LOI_MM"));
                    listYes = tPKMMrepo.findTonPhatKhauMMDashMainCN1(tinhPhat, bc, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM"));
                    listTongSLTo = tPKMMrepo.findTonPhatKhauMMDashMainCN1(tinhPhat, bc, ngayBaoCao, Arrays.asList("LOI_MM", "OK"));
                    listTongSLYes = tPKMMrepo.findTonPhatKhauMMDashMainCN1(tinhPhat, bc, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM", "OK"));
                }
            }
        } else {
            if (!isAdmin()) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat) || !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc))
                    return new TonPhatKhauMMMainDashResDTO();
            }
            listTo = tPKMMrepo.findTonPhatKhauMMDashMainBC(tinhPhat, maBuuCuc, ngayBaoCao, Arrays.asList("LOI_MM"));
            listYes = tPKMMrepo.findTonPhatKhauMMDashMainBC(tinhPhat, maBuuCuc, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM"));
            listTongSLTo = tPKMMrepo.findTonPhatKhauMMDashMainBC(tinhPhat, maBuuCuc, ngayBaoCao, Arrays.asList("LOI_MM", "OK"));
            listTongSLYes = tPKMMrepo.findTonPhatKhauMMDashMainBC(tinhPhat, maBuuCuc, ngayBaoCao.minusDays(1), Arrays.asList("LOI_MM", "OK"));
        }

        Long tongSLMMTo = Long.valueOf(0);
        Long tongSLMMYes = Long.valueOf(0);
        Long tongSLTo = Long.valueOf(0);
        Long tongSLYes = Long.valueOf(0);
        Float tileTo, tileYes;

        for (TonPhatKhauMMMainDashResDTO mm : listTo) {
            if(mm.getTongSL() != null)
                tongSLMMTo += mm.getTongSL();
        }
        for (TonPhatKhauMMMainDashResDTO mm : listYes) {
            if(mm.getTongSL() != null)
                tongSLMMYes += mm.getTongSL();
        }
        for (TonPhatKhauMMMainDashResDTO mm : listTongSLTo) {
            if(mm.getTongSL() != null)
                tongSLTo += mm.getTongSL();
        }
        for (TonPhatKhauMMMainDashResDTO mm : listTongSLYes) {
            if(mm.getTongSL() != null)
                tongSLYes += mm.getTongSL();
        }

        if (tongSLTo == 0) tileTo = 0f;
        else tileTo = ((float) tongSLMMTo / (float) tongSLTo) * 100;
        if (tongSLYes == 0) tileYes = 0f;
        else tileYes = ((float) tongSLMMYes / (float) tongSLYes) * 100;

        TonPhatKhauMMMainDashResDTO finalResult = new TonPhatKhauMMMainDashResDTO(null, (long) 0);
        finalResult.setTongSL(tongSLMMTo);
        finalResult.setTongDif(tongSLMMTo - tongSLMMYes);
        finalResult.setTiLe(tileTo);
        finalResult.setTiLeDif(tileTo - tileYes);
        finalResult.setNgayBaoCao(ngayBaoCao);
        return finalResult;
    }

    public TonPhatKhauMMLoaiTonDashResDTO khauMMLoaiTon(String tinhPhat, String maBuuCuc, LocalDate ngayBaoCao) {
        List<Float> slMM = new ArrayList<>();
        List<Float> slTonPhat = new ArrayList<>();
        List<Float> listTL = new ArrayList<>();
        List<String> cn, bc;
        Float sl = Float.valueOf(0);
        Float tl = Float.valueOf(0);
        Float tong_sl = Float.valueOf(0);

        List<String> loaiTon = Arrays.asList("CHO_HOAN", "TON_PHAT", "HOAN", "LUU_KHO");
        List<String> nhomDV = Arrays.asList("DV_CHAM", "DV_NHANH", "HOA_TOC");

        List<TonPhatKhauMMLoaiTonDashDTO> listTongSL;
        List<TonPhatKhauMMLoaiTonDashDTO> listLoiMM;
        if (maBuuCuc.equals("")) {
            if (tinhPhat.equals("")) {
                if (isAdmin()) {
                    listTongSL = tPKMMrepo.findTonPhatKhauMMLoaiTonTCT(ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMLoaiTonTCT(ngayBaoCao, "LOI_MM");
                } else {
                    cn = UserContext.getUserData().getListChiNhanhVeriable();
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    listTongSL = tPKMMrepo.findTonPhatKhauMMLoaiTonTCTNoAdmin(cn, bc, ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMLoaiTonTCTNoAdmin(cn, bc, ngayBaoCao, "LOI_MM");
                }
            } else {
                if (isAdmin()) {
                    listTongSL = tPKMMrepo.findTonPhatKhauMMLoaiTonCN(tinhPhat, ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMLoaiTonCN(tinhPhat, ngayBaoCao, "LOI_MM");
                } else {
                    if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat)) {
                        return new TonPhatKhauMMLoaiTonDashResDTO();
                    }
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    listTongSL = tPKMMrepo.findTonPhatKhauMMLoaiTonCNNoAdmin(tinhPhat, bc, ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMLoaiTonCNNoAdmin(tinhPhat, bc, ngayBaoCao, "LOI_MM");
                }
            }
        } else {
            if (!isAdmin()) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat) || !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc))
                    return new TonPhatKhauMMLoaiTonDashResDTO();
            }
            listTongSL = tPKMMrepo.findTonPhatKhauMMLoaiTonBC(tinhPhat, maBuuCuc, ngayBaoCao, "");
            listLoiMM = tPKMMrepo.findTonPhatKhauMMLoaiTonBC(tinhPhat, maBuuCuc, ngayBaoCao, "LOI_MM");
        }

        //nếu list lỗi rỗng -> return
        if (listLoiMM.isEmpty()) {
            return new TonPhatKhauMMLoaiTonDashResDTO(slTonPhat, slMM, listTL);
        }

        //put data vào table [loại tồn | nhóm dịch vụ | tổng SL]
        Table<String, String, Long> dataTable = HashBasedTable.create();
        for (TonPhatKhauMMLoaiTonDashDTO elements: listLoiMM) {
            dataTable.put(elements.getLoaiTon(), elements.getNhomDV(), elements.getTongSL());
        }

        //tính tổng sl
        Table<String, String, Long> dataTableTongSl = HashBasedTable.create();
        for (TonPhatKhauMMLoaiTonDashDTO elements : listTongSL) {
            dataTableTongSl.put(elements.getLoaiTon(), elements.getNhomDV(), elements.getTongSL());
        }

        //tính số cho từng loại tồn + dịch vụ
        for (String _loaiTon : loaiTon) {
            for (String _nhomDV : nhomDV) {
                sl = dataTable.get(_loaiTon, _nhomDV) == null ? null : Float.valueOf(dataTable.get(_loaiTon, _nhomDV));
                tong_sl = dataTableTongSl.get(_loaiTon, _nhomDV) == null ? null : Float.valueOf(dataTableTongSl.get(_loaiTon, _nhomDV));
                slMM.add(sl);

                if (tong_sl == null || sl == null || tong_sl == 0) {
                    tl = null;

                } else {
                    tl = (sl / tong_sl) * 100;
                }
                listTL.add(tl);
                slTonPhat.add(tong_sl);
            }
        }
        return new TonPhatKhauMMLoaiTonDashResDTO(slTonPhat, slMM, listTL);
    }

    public List<TonPhatKhauMMTop15DashResDTO> khauMMTop15(String tinhPhat, String maBuuCuc, LocalDate ngayBaoCao) {
        List<TonPhatKhauMMTop15DashResDTO> listTongSL;// = tPKMMrepo.findTonPhatKhauMMTop15TCT(ngayBaoCao, "");
        List<TonPhatKhauMMTop15DashResDTO> listLoiMM;// = tPKMMrepo.findTonPhatKhauMMTop15TCT(ngayBaoCao, "LOI_MM");
        List<String> cn, bc;
        if (maBuuCuc.equals("")) {
            if (tinhPhat.equals("")) {
                if (isAdmin()) {
                    listTongSL = tPKMMrepo.findTonPhatKhauMMTop15TCT(ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMTop15TCT(ngayBaoCao, "LOI_MM");
                } else {
                    cn = UserContext.getUserData().getListChiNhanhVeriable();
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    listTongSL = tPKMMrepo.findTonPhatKhauMMTop15TCTNoAdmin(cn, bc, ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMTop15TCTNoAdmin(cn, bc, ngayBaoCao, "LOI_MM");
                }
            } else {
                if (isAdmin()) {
                    listTongSL = tPKMMrepo.findTonPhatKhauMMTop15CN(tinhPhat, ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMTop15CN(tinhPhat, ngayBaoCao, "LOI_MM");
                } else {
                    if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat)) {
                        return new ArrayList<>();// List<TonPhatKhauMMTop15DashResDTO>();
                    }
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    listTongSL = tPKMMrepo.findTonPhatKhauMMTop15CNNoAdmin(tinhPhat, bc, ngayBaoCao, "");
                    listLoiMM = tPKMMrepo.findTonPhatKhauMMTop15CNNoAdmin(tinhPhat, bc, ngayBaoCao, "LOI_MM");
                }
            }
        } else {
            if (!isAdmin()) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(tinhPhat) || !UserContext.getUserData().getListBuuCucVeriable().contains(maBuuCuc))
                    return new ArrayList<>();
            }
            listTongSL = tPKMMrepo.findTonPhatKhauMMTop15BC(tinhPhat, maBuuCuc, ngayBaoCao, "");
            listLoiMM = tPKMMrepo.findTonPhatKhauMMTop15BC(tinhPhat, maBuuCuc, ngayBaoCao, "LOI_MM");
        }
        Map<String, Long> mapTongSL = new HashMap<>();
        for (TonPhatKhauMMTop15DashResDTO mm : listTongSL) {
            mapTongSL.put(mm.getDonVi(), mm.getSlMM());
        }

        if (listLoiMM.isEmpty()) {
            return listLoiMM;
        } else {
            for (TonPhatKhauMMTop15DashResDTO mm : listLoiMM) {
                mm.setTong(mapTongSL.get(mm.getDonVi()));
                mm.setTlLoiMM(((float) mm.getSlMM() / (float) mm.getTong()) * 100);
            }
        }

        Collections.sort(listLoiMM, Collections.reverseOrder());
        return listLoiMM.stream().limit(15).collect(Collectors.toList());
    }

    // VT_2023/02/27 NgocNt92 add function to get Data fill to excel(api_exportExcel).
    public void exportExcelTonPhatKhauLM(HttpServletResponse response,
                                         List<String> tinhPhat,
                                         List<String> loaiton,
                                         List<String> maBuuCuc,
                                         LocalDate ngayBaoCao,
                                         List<String> loaiCanhBao,
                                         List<String> loaiDichVu) throws IOException {//List<String> tinhPhat, List<String> loaiTon, List<String> maBuuCuc, LocalDate ngayBaoCao, List<String> loaiCanhBao, List<String> dGMocMM
        List<TonPhatKhauLMResponseDTO> tPhatKhauLMExcel = getDataSum(tinhPhat, loaiton, maBuuCuc, ngayBaoCao, loaiCanhBao, loaiDichVu);
        TonPhatKhauMMExcelExporter tPKhauMMExcelExporter = new TonPhatKhauMMExcelExporter(tPhatKhauLMExcel);
        tPKhauMMExcelExporter.exportDataToExcel(response, tinhPhat, maBuuCuc);
    }
}
