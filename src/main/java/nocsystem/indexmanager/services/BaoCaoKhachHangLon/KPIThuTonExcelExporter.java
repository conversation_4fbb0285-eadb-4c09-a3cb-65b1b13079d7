package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuBillResDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class KPIThuTonExcelExporter {

    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private List<BaoCaoKPIThuBillResDTO> KPIPhat;

    public KPIThuTonExcelExporter(List<BaoCaoKPIThuBillResDTO> KPIPhat) {
        this.workbook = workbook = new SXSSFWorkbook(1);
        this.KPIPhat = KPIPhat;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private Row row1;

    private void createHeaderRow() {
        sheet = workbook.createSheet("KPI Thu Ton");
        workbook.setCompressTempFiles(true);

        Row row = sheet.createRow(0);
        row1 = row;
        CellStyle style = workbook.createCellStyle();
//        SXSSFFont font = workbook.createFont();
//        font.setBold(true);
//        font.setFontHeight(20);
//        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
//        createCell(row, 0, "Tinh Information", style);
//        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));
//        font.setFontHeightInPoints((short) 10);

//        font.setBold(true);
//        font.setFontHeight(16);
//        style.setFont(font);
//        createCell(row, 0, "thoi_gian", style);
        createCell(row, 0, "ma_phieugui", style);
        createCell(row, 1, "vung", style);
        createCell(row, 2, "tinh_nhan", style);
        createCell(row, 3, "tinh_phat", style);
        createCell(row, 4, "huyen_nhan", style);
        createCell(row, 5, "huyen_phat", style);
        createCell(row, 6, "ma_xanhan", style);
        createCell(row, 7, "ma_xaphat", style);
        createCell(row, 8, "ma_dv_viettel", style);
        createCell(row, 9, "ma_buucuc_goc", style);
        createCell(row, 10, "ma_buucuc_phat", style);
        createCell(row, 11, "time_yeucau_layhang", style);
        createCell(row, 12, "thoigiantaodon", style);
        createCell(row, 13, "time_thulan1", style);
        createCell(row, 14, "time_thulan2", style);
        createCell(row, 15, "time_thulan3", style);
        createCell(row, 16, "time_nhap_may", style);
        createCell(row, 17, "ma_trangthai", style);
        createCell(row, 18, "ma_doitac", style);
        createCell(row, 19, "ma_khgui", style);
        createCell(row, 20, "ma_dichvu_doitac", style);
        createCell(row, 21, "tien_cod", style);
        createCell(row, 22, "tienhang", style);
        createCell(row, 23, "trong_luong", style);
        createCell(row, 24, "tong_cuoc", style);
        createCell(row, 25, "loai_canh_bao", style);
        createCell(row, 26, "ly_do_tt1", style);
        createCell(row, 27, "ly_do_tt2", style);
        createCell(row, 28, "ly_do_tt3", style);
        createCell(row, 29, "buu_ta_ntc", style);
        createCell(row, 30, "buu_ta_td_1", style);
        createCell(row, 31, "time_ketnoidi_tai_goc", style);
        createCell(row, 32, "loai_don", style);
        createCell(row, 33, "buu_ta_tonthu", style);
        createCell(row, 34, "time_ht", style);


        sheet.setColumnWidth(row.getLastCellNum(), 8000);
    }

    private void writeCustomData() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        CreationHelper createHelper = workbook.getCreationHelper();
        sheet.setRandomAccessWindowSize(1000);

//        CellStyle dateStyle = workbook.createCellStyle();
//        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-mm-yyyy"));
        //dateStyle.setFont(font);

        CellStyle timeStyle = workbook.createCellStyle();
        timeStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-mm-yyyy HH:mm:ss"));
//        timeStyle.setFont(font);

        for (BaoCaoKPIThuBillResDTO kpiThu : KPIPhat) {
            SXSSFRow row = sheet.createRow(rowCount++);
            int columnCount = 0;
            createCell(row, columnCount++, kpiThu.getMaPhieuGui(), style);
            createCell(row, columnCount++, kpiThu.getVung(), style);
            createCell(row, columnCount++, kpiThu.getTinhNhan(), style);
            createCell(row, columnCount++, kpiThu.getTinhPhat(), style);
            createCell(row, columnCount++, kpiThu.getHuyenNhan(), style);
            createCell(row, columnCount++, kpiThu.getHuyenPhat(), style);
            createCell(row, columnCount++, kpiThu.getMaXaNhan(), style);
            createCell(row, columnCount++, kpiThu.getMaXaPhat(), style);
            createCell(row, columnCount++, kpiThu.getMaDVViettel(), style);
            createCell(row, columnCount++, kpiThu.getMaBuuCucGoc(), style);
            createCell(row, columnCount++, kpiThu.getMaBuuCucPhat(), style);
            createCell(row, columnCount++, kpiThu.getTimeYeuCauLayHang(), timeStyle);
            createCell(row, columnCount++, kpiThu.getThoiGianTaoDon(), timeStyle);
            createCell(row, columnCount++, kpiThu.getTimeThuLan1(), timeStyle);
            createCell(row, columnCount++, kpiThu.getTimeThuLan2(), timeStyle);
            createCell(row, columnCount++, kpiThu.getTimeThuLan3(), timeStyle);
            createCell(row, columnCount++, kpiThu.getTimeNhapMay(), timeStyle);
            createCell(row, columnCount++, kpiThu.getMaTrangThai(), style);
            createCell(row, columnCount++, kpiThu.getMaDoiTac(), style);
            createCell(row, columnCount++, kpiThu.getMaKHGui(), style);
            createCell(row, columnCount++, kpiThu.getMaDichVuDoiTac(), style);
            createCell(row, columnCount++, kpiThu.getTienCOD(), style);
            createCell(row, columnCount++, kpiThu.getTienhang(), style);
            createCell(row, columnCount++, kpiThu.getTrongLuong(), style);
            createCell(row, columnCount++, kpiThu.getTongCuoc(), style);
            createCell(row, columnCount++, kpiThu.getCanhBao(), style);
            createCell(row, columnCount++, kpiThu.getLyDoTT1(), style);
            createCell(row, columnCount++, kpiThu.getLyDoTT2(), style);
            createCell(row, columnCount++, kpiThu.getLyDoTT3(), style);
            createCell(row, columnCount++, kpiThu.getBuuTaNTC(), style);
            createCell(row, columnCount++, kpiThu.getBuuTaTD1(), style);
            createCell(row, columnCount++, kpiThu.getTimeKetNoiDiTaiGoc(), style);
            createCell(row, columnCount++, kpiThu.getLoaiDon(), style);
            createCell(row, columnCount++, kpiThu.getBuu_ta_tonthu(), style);
            createCell(row, columnCount++, kpiThu.getTime_ht(), timeStyle);
        }

    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
//        workbook.dispose();
        createHeaderRow();
        long start = System.nanoTime();
        writeCustomData();
        long end = System.nanoTime();
        System.out.println("write" + (end - start));
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        workbook.dispose();
        workbook.close();
        outputStream.close();
    }
}
