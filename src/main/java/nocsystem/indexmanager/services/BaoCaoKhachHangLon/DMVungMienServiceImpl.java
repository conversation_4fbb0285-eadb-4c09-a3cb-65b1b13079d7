package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMVungMien;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.DMVungMienRepository;
import nocsystem.indexmanager.services.BaoCaoKhachHangLon.DMVungMienService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Optional;

/**
 * Service Implementation for managing {@link DMVungMienService}.
 */
@Service
@Transactional
public class DMVungMienServiceImpl implements DMVungMienService {

    private final Logger log = LoggerFactory.getLogger(DMVungMienServiceImpl.class);

    private final DMVungMienRepository dmVungMienRepository;

    public DMVungMienServiceImpl(DMVungMienRepository dmVungMienRepository) {
        this.dmVungMienRepository = dmVungMienRepository;
    }

    @Override
    public DMVungMien save(DMVungMien dmVungMien) {
        log.debug("Request to save DMVungMien : {}", dmVungMien);
        DMVungMien dmVungMien1 = dmVungMienRepository.save(dmVungMien);
        return dmVungMien1;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DMVungMien> findAll(Pageable pageable) {
        log.debug("Request to get all DMVungMien");
        return dmVungMienRepository.findAll(pageable);
    }

}
