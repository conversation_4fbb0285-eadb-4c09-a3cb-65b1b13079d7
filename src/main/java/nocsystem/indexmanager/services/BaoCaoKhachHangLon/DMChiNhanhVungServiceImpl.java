package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanhVung;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.DMChiNhanhVungRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * Service Interface for managing {@link DMChiNhanhVung}.
 */
@Service
public class DMChiNhanhVungServiceImpl implements DMChiNhanhVungService {
    private final Logger log = LoggerFactory.getLogger(DMChiNhanhVungServiceImpl.class);
    private final DMChiNhanhVungRepository dmChiNhanhVungRepository;

    public DMChiNhanhVungServiceImpl(DMChiNhanhVungRepository dmChiNhanhVungRepository) {
        this.dmChiNhanhVungRepository = dmChiNhanhVungRepository;
    }


    @Override
    public DMChiNhanhVung save(DMChiNhanhVung dmChiNhanhVung) {
        log.debug("Request to save DMChiNhanhVung : {}", dmChiNhanhVung);
        DMChiNhanhVung dmChiNhanhVung1 = dmChiNhanhVungRepository.save(dmChiNhanhVung);
        return dmChiNhanhVung1;
    }

    @Override
    public Page<DMChiNhanhVung> findAll(Pageable pageable) {
        log.debug("Request to get all DMChiNhanhVung");
        return dmChiNhanhVungRepository.findAll(pageable);
    }
}
