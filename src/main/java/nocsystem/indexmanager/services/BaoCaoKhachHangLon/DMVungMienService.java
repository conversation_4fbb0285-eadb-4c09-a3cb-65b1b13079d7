package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMVungMien;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * Service Interface for managing {@link nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMVungMien}.
 */
public interface DMVungMienService {
    DMVungMien save(DMVungMien dmVungMien);
    Page<DMVungMien> findAll(Pageable pageable);
}
