package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatBillResDTO;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIPhatResDTO2;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class KPIPhatExcelExporter {

    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private List<BaoCaoKPIPhatBillResDTO> KPIPhat;

    public KPIPhatExcelExporter(List<BaoCaoKPIPhatBillResDTO> KPIPhat) {
        this.workbook = workbook = new SXSSFWorkbook();
        this.KPIPhat = KPIPhat;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRow() {
        sheet = workbook.createSheet("KPI Phai Phat");
        Row row = sheet.createRow(0);
        CellStyle style = workbook.createCellStyle();

        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
//        createCell(row, 0, "Tinh Information", style);
//        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

        createCell(row, 0, "ma_phieugui", style);
        createCell(row, 1, "tinh_nhan", style);
        createCell(row, 2, "tinh_phat", style);
        createCell(row, 3, "huyen_nhan", style);
        createCell(row, 4, "huyen_phat", style);
        createCell(row, 5, "ma_xanhan", style);
        createCell(row, 6, "ma_xaphat", style);
        createCell(row, 7, "ma_dv_viettel", style);
        createCell(row, 8, "ma_buucuc_goc", style);
        createCell(row, 9, "ma_buucuc_phat", style);
        createCell(row, 10, "time_yeucau_layhang", style);
        createCell(row, 11, "ngay_gui_bp", style);
        createCell(row, 12, "ma_trangthai", style);
        createCell(row, 13, "ma_buucuc_ht", style);
        createCell(row, 14, "ma_chinhanh_ht", style);
        createCell(row, 15, "ma_doitac", style);
        createCell(row, 16, "ma_khgui", style);
        createCell(row, 17, "ma_dichvu_doitac", style);
        createCell(row, 18, "tg_quydinhphat", style);
        createCell(row, 19, "tg_quydinh", style);
        createCell(row, 20, "tg_chenhlechphat", style);
        createCell(row, 21, "time_pcp", style);
        createCell(row, 22, "time_gach_bp", style);
        createCell(row, 23, "time_gach_bp2", style);
        createCell(row, 24, "time_gach_bp3", style);
        createCell(row, 25, "ly_do_tt", style);
        createCell(row, 26, "ly_do_tt2", style);
        createCell(row, 27, "ly_do_tt3", style);
        createCell(row, 28, "tien_cod", style);
        createCell(row, 29, "tienhang", style);
        createCell(row, 30, "trong_luong", style);
        createCell(row, 31, "tong_cuoc", style);
        createCell(row, 32, "canh_bao", style);
        createCell(row, 33, "tuyen_buuta", style);
        createCell(row, 34, "so_lan_giao", style);
        createCell(row, 35, "tg_fm", style);
        createCell(row, 36, "tg_mm", style);
        createCell(row, 37, "tg_lm", style);
        createCell(row, 38, "tg_toantrinh", style);
        createCell(row, 39, "danhgia_time_gach_bp1", style);
        createCell(row, 40, "danhgia_time_gach_bp2", style);
        createCell(row, 41, "danhgia_time_gach_bp3", style);
        createCell(row, 42, "tg_ptc", style);
        createCell(row, 43, "ma_buucuc_phat_thucte", style);
        createCell(row, 44, "tg_nhantai_bcp", style);
//        for(int i = 1; i<=row.getLastCellNum()-1;i++){
//            sheet.trackAllColumnsForAutoSizing();
//            sheet.autoSizeColumn(i);
//        }
        sheet.setColumnWidth(row.getLastCellNum(),8000);
    }

    private void writeCustomData() throws IOException {
        int rowCount = 1;
        CellStyle style = workbook.createCellStyle();
        CreationHelper createHelper = workbook.getCreationHelper();
        DecimalFormat df = new DecimalFormat("0.00");


        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-mm-yyyy"));

        CellStyle timeStyle = workbook.createCellStyle();
        timeStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-mm-yyyy HH:mm:ss"));

        for (BaoCaoKPIPhatBillResDTO kpiPhat : KPIPhat){
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;
            createCell(row, columnCount++, kpiPhat.getMaPhieuGui(), style);
            createCell(row, columnCount++, kpiPhat.getTinhNhan(), style);
            createCell(row, columnCount++, kpiPhat.getTinhPhat(), style);
            createCell(row, columnCount++, kpiPhat.getHuyenNhan(), style);
            createCell(row, columnCount++, kpiPhat.getHuyenPhat(), style);
            createCell(row, columnCount++, kpiPhat.getMaXaNhan(), style);
            createCell(row, columnCount++, kpiPhat.getMaXaPhat(), style);
            createCell(row, columnCount++, kpiPhat.getMaDVViettel(), style);
            createCell(row, columnCount++, kpiPhat.getMaBuuCucGoc(), style);
            createCell(row, columnCount++, kpiPhat.getMaBuuCucPhat(), style);
            createCell(row, columnCount++, kpiPhat.getTimeYeuCauLayHang(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getNgayGuiBP(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getMaTrangThai(), style);
            createCell(row, columnCount++, kpiPhat.getMaBuuCucHT(), style);
            createCell(row, columnCount++, kpiPhat.getMaChiNhanhHT(), style);
            createCell(row, columnCount++, kpiPhat.getMaDoiTac(), style);
            createCell(row, columnCount++, kpiPhat.getMaKHGui(), style);
            createCell(row, columnCount++, kpiPhat.getMaDichVuDoiTac(), style);
            createCell(row, columnCount++, kpiPhat.getTgQuyDinhPhat(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getTgQuyDinh(), style);
            createCell(row, columnCount++, kpiPhat.getTgChenhLechPhat(), style);
//            createCell(row, columnCount++, kpiPhat.getTgChenhLech(), style);
            createCell(row, columnCount++, kpiPhat.getTimePCP(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getTimeGachBP(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getTimeGachBP2(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getTimeGachBP3(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getLyDoTT(), style);
            createCell(row, columnCount++, kpiPhat.getLyDoTT2(), style);
            createCell(row, columnCount++, kpiPhat.getLyDoTT3(), style);
            createCell(row, columnCount++, kpiPhat.getTienCOD(), style);
            createCell(row, columnCount++, kpiPhat.getTienhang(), style);
            createCell(row, columnCount++, kpiPhat.getTrongLuong(), style);
            createCell(row, columnCount++, kpiPhat.getTongCuoc(), style);
            createCell(row, columnCount++, kpiPhat.getCanhBao(), style);
            createCell(row, columnCount++, kpiPhat.getTuyenBuuTa(), style);
            createCell(row, columnCount++, kpiPhat.getSoLanGiao(), style);
            createCell(row, columnCount++, "", style);
            createCell(row, columnCount++, "", style);
            createCell(row, columnCount++, "", style);
            createCell(row, columnCount++, "", style);
            createCell(row, columnCount++, kpiPhat.getDgTimeGachBP1(), style);
            createCell(row, columnCount++, kpiPhat.getDgTimeGachBP2(), style);
            createCell(row, columnCount++, kpiPhat.getDgTimeGachBP3(), style);
            createCell(row, columnCount++, kpiPhat.getTgPTC(), timeStyle);
            createCell(row, columnCount++, kpiPhat.getMaBuuCucPhatThucTe(), style);
            createCell(row, columnCount++, kpiPhat.getTgNhanTai(), timeStyle);
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
        createHeaderRow();
        writeCustomData();
        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        }
        outputStream.close();
    }
}
