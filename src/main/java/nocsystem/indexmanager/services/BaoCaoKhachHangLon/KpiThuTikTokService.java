package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoTonResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTikTokKpiThuResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokResponse;

import java.time.LocalDate;
import java.util.List;

public interface KpiThuTikTokService {

    DashboardTikTokKpiThuResponse getInfoDashboardKpiThuTikTok(int type, LocalDate requestDate, String maChiNhanh, String maBuuCuc, String maDoiTac);
    List<DashboardTop10TikTokResponse> top10ThuThapNhat(int type, LocalDate requestDay, String maChiNhanh, String maBuuCuc, String doiTac);
    BaoCaoTonResponse getBaoCaoTon(String maChiNhanh, String maBuuCuc, String maDoiTac);

}
