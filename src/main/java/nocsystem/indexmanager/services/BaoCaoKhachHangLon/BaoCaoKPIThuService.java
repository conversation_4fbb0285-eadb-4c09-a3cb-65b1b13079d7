package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import lombok.extern.slf4j.Slf4j;
import nocsystem.indexmanager.constants.Constant;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.*;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.BaoCaoKPIThuBillRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.BaoCaoKPIThuLKRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.BaoCaoKPIThuRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

@Slf4j
@Service
public class BaoCaoKPIThuService implements KpiThuTikTokService {

    private static final Logger logger = LoggerFactory.getLogger(BaoCaoKPIThuService.class);


    private final BaoCaoKPIThuRepository bcKPIThuRepo;

    private final BaoCaoKPIThuBillRepository bcKPIThuBillRepo;

    public BaoCaoKPIThuService(BaoCaoKPIThuRepository bcKPIThuRepo, BaoCaoKPIThuBillRepository bcKPIThuBillRepo) {
        this.bcKPIThuRepo = bcKPIThuRepo;
        this.bcKPIThuBillRepo = bcKPIThuBillRepo;
    }

    private boolean isAdmin() {
        return UserContext.getUserData().getIsViewAllBcTiktok().equalsIgnoreCase("true");
    }

    public String getTime(LocalDate ngayBaoCao){
        Page<String> time = bcKPIThuRepo.getTimeTinhToan(ngayBaoCao, Pageable.ofSize(1));
        String timeTinhToan = "";
        if (time.hasNext())
            timeTinhToan = time.getContent().get(0);

        return timeTinhToan;
    }


    public ListContentPageDto<BaoCaoKPIThuResDTO2> findAllData(Integer luyKe, Integer cap, String vung, String vungCon, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao, Integer pageQuery, Integer pageSize, String sort, String sortBy) {
        Pageable page;
        List<String> cn;
        List<String> bc;

        page = PageRequest.of(pageQuery, pageSize);
        List<BaoCaoKPIThuResDTO2> pageResult = new ArrayList<>();
        LocalDate ngayDauThang = LocalDate.now();
        List<String> listVung = Arrays.asList("HCM", "HNI");

        if (luyKe == 1){
            ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        }else if (luyKe == 2){
            ngayDauThang = LocalDate.from(ngayBaoCao).plusDays(6);
        }

        if (!SSOChecking(cap, chiNhanh, buuCuc)) {
            return new ListContentPageDto<>();
        }

        if (chiNhanh.isEmpty()) {
            cn = ListVariableLocation.listChiNhanhVeriable;
        } else {
            cn = List.of(chiNhanh);
        }

        if (buuCuc.isEmpty()) {
            bc = ListVariableLocation.listBuuCucVeriable;
        } else {
            bc = List.of(buuCuc);
        }

        if (isAdmin()) {
            if (luyKe == 0) {
                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            if (listVung.contains(vung)) {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCTShopeeVaVungCon(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao);
                            } else {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCTShopee(doiTac, vung, dichVu, loaiDon, ngayBaoCao);
                            }
                            break;
                        case Constant.VUNG:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuVung1Shopee(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCNShopee(doiTac, chiNhanh, buuCuc,vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.DOI_TAC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuDTShopee(doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBCShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            if (listVung.contains(vung)) {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCTVaVungCon(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao);
                            } else {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCT(doiTac, vung, dichVu, loaiDon, ngayBaoCao);
                            }
                            break;
                        case Constant.VUNG:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuVung1(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCN(doiTac, chiNhanh, buuCuc, vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.DOI_TAC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuDT(doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBC(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }

                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            if (listVung.contains(vung)) {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCTLKShopeeVaVungCon(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            } else {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCTLKShopee(doiTac, vung, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            }
                            break;
                        case Constant.VUNG:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuVung1LKShopee(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCNLKShopee(doiTac, chiNhanh, buuCuc, vungCon,dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.DOI_TAC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuDTLKShopee(doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBCLKShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            if (listVung.contains(vung)) {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCTLKVaVungCon(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            } else {
                                pageResult = bcKPIThuRepo.findBaoCaoKPIThuTCTLK(doiTac, vung, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            }
                            break;
                        case Constant.VUNG:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuVung1LK(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCNLK(doiTac, chiNhanh, buuCuc,vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.DOI_TAC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuDTLK(doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBCLK(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                }
            }
        } else {
            if (luyKe == 0) {
                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCN1Shopee(doiTac, cn, bc,vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBC1Shopee(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCN1(doiTac, cn, bc,vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBC1(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCN1LKShopee(doiTac, cn, bc,vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBC1LKShopee(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuCN1LK(doiTac, cn, bc, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            pageResult = bcKPIThuRepo.findBaoCaoKPIThuBC1LK(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        for (BaoCaoKPIThuResDTO2 bcKPI : pageResult) {
            if (bcKPI.getSlThuTC() == 0f || bcKPI.getSlThuTC() == null) {
                bcKPI.setTlThuDG(null);
                bcKPI.setTlThuDG1(null);
            } else {
                bcKPI.setTlThuDG((((float) bcKPI.getSlThuDG() / (float) bcKPI.getSlThuTC()) * 100));
                bcKPI.setTlThuDG1((((float) bcKPI.getSlThuDG1() / (float) bcKPI.getSlThuTC()) * 100));
            }
            if (bcKPI.getTongSlTrongKy() != null && bcKPI.getTongSlTrongKy() != 0L) {
                bcKPI.setTlLayHangDungHen((((float) bcKPI.getTongSlLayHangDungHen() / (float) bcKPI.getTongSlTrongKy()) * 100));
                bcKPI.setTlLayHangTcDungHen((((float) bcKPI.getTongSlLayHangTcDungHen() / (float) bcKPI.getTongSlTrongKy()) * 100));
            }
        }

        //sort theo cột
        if (!sort.equals(""))
            switch (sortBy) {
                case "sL":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getsL, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getsL, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "dropOff":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getDropOff, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getDropOff, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "huy":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getHuy, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getHuy, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slThuTC":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getSlThuTC, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getSlThuTC, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slThuDG":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getSlThuDG, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getSlThuDG, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slThuDG1":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getSlThuDG1, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getSlThuDG1, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlThuDG":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlThuDG, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlThuDG, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlThuDG1":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlThuDG1, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlThuDG1, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tonSLAConHan":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTonSLAConHan, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTonSLAConHan, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tonSLAQuaHan":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTonSLAQuaHan, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTonSLAQuaHan, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tongTon":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTongTon, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTongTon, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlLayHangDungHen":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlLayHangDungHen, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlLayHangDungHen, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlLayHangTcDungHen":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlLayHangTcDungHen, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIThuResDTO2::getTlLayHangTcDungHen, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                default:
                    break;
            }

        //sublist lấy số lượng theo pageSize
        int start = (int) page.getOffset();
        int end = Math.min((start + page.getPageSize()), pageResult.size());
        if(start > pageResult.size())
            return new ListContentPageDto<>();
        List<BaoCaoKPIThuResDTO2> pageData = pageResult.subList(start, end);

        //tạo page từ list
        Page<BaoCaoKPIThuResDTO2> finalPage = new PageImpl<>(pageData, page, pageResult.size());

        ListContentPageDto<BaoCaoKPIThuResDTO2> listContent = new ListContentPageDto<>(finalPage);
        listContent.setTime(getTime(ngayBaoCao));
        return listContent;
    }

    private Boolean SSOChecking(Integer cap, String chiNhanh, String buuCuc) {
        if (!isAdmin() && (cap == 1 || cap == 2 || cap == 4)) {
            return false;
        }
        if (!isAdmin()) {
            if (!chiNhanh.equals("")) {
                log.info("Admin: " + isAdmin());
                if (!ListVariableLocation.listChiNhanhVeriable.contains(chiNhanh)) {
                    return false;
                }
            }
            if (!buuCuc.equals("")) {
                if (!ListVariableLocation.listBuuCucVeriable.contains(buuCuc)) {
                    return false;
                }
            }
        }
        return true;
    }

    public List<BaoCaoKPIThuResDTO2> summaryCalculation(Integer luyKe, Integer cap, String vung, String vungCon, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao) {

        List<BaoCaoKPIThuResDTO2> bcKPI = getSumData(luyKe, cap, vung, vungCon, chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
        long sl = 0L;
        long slThuTC = 0L;
        long slThuDG = 0L;
        long slThuDG1 = 0L;
        Float tlThuDungGio;
        Float tlThuDG1;
        long huy = 0L;
        long xanh = 0L;
        long vang = 0L;
        long do1 = 0L;
        long do2 = 0L;
        long do3 = 0L;
        long tongTon = 0L;
        long slDropOff = 0L;
        long slGiaiTrinh = 0L;
        long slChuaGiaiTrinh = 0L;
        long quaHanSLA = 0L;
        long conHanSLA = 0L;
        long tongSlLayHangDungHen = 0L;
        long tongSlLayHangTcDungHen = 0L;
        long tongSlTrongKy = 0L;
        Float tlLayHangDungHen;
        Float tlLayHangTcDungHen;

        for (BaoCaoKPIThuResDTO2 baocaoKPIRes : bcKPI) {
            sl = baocaoKPIRes.getsL() != null ? sl + baocaoKPIRes.getsL() : sl;
            slThuTC = baocaoKPIRes.getSlThuTC() != null ? slThuTC + baocaoKPIRes.getSlThuTC() : slThuTC;
            slThuDG = baocaoKPIRes.getSlThuDG() != null ? slThuDG + baocaoKPIRes.getSlThuDG() : slThuDG;
            slThuDG1 = baocaoKPIRes.getSlThuDG1() != null ? slThuDG1 + baocaoKPIRes.getSlThuDG1() : slThuDG1;
            huy = baocaoKPIRes.getHuy() != null ? huy + baocaoKPIRes.getHuy() : huy;
            tongTon = baocaoKPIRes.getTongTon() != null ? tongTon + baocaoKPIRes.getTongTon() : tongTon;
            slDropOff = baocaoKPIRes.getDropOff() != null ? slDropOff + baocaoKPIRes.getDropOff() : slDropOff;
            quaHanSLA = baocaoKPIRes.getTonSLAQuaHan() != null ? quaHanSLA + baocaoKPIRes.getTonSLAQuaHan() : quaHanSLA;
            conHanSLA = baocaoKPIRes.getTonSLAConHan() != null ? conHanSLA + baocaoKPIRes.getTonSLAConHan() : conHanSLA;
            tongSlLayHangDungHen = baocaoKPIRes.getTongSlLayHangDungHen() != null ? tongSlLayHangDungHen + baocaoKPIRes.getTongSlLayHangDungHen() : tongSlLayHangDungHen;
            tongSlLayHangTcDungHen = baocaoKPIRes.getTongSlLayHangTcDungHen() != null ? tongSlLayHangTcDungHen + baocaoKPIRes.getTongSlLayHangTcDungHen() : tongSlLayHangTcDungHen;
            tongSlTrongKy = baocaoKPIRes.getTongSlTrongKy() != null ? tongSlTrongKy + baocaoKPIRes.getTongSlTrongKy() : tongSlTrongKy;
        }

        if (slThuTC == 0) {
            tlThuDungGio = null;
            tlThuDG1 = null;
        } else {
            tlThuDungGio = ((float) slThuDG / (float) slThuTC) * 100;
            tlThuDG1 = ((float) slThuDG1 / (float) slThuTC) * 100;
        }

        if (tongSlTrongKy == 0L) {
            tlLayHangDungHen = null;
            tlLayHangTcDungHen = null;
        } else {
            tlLayHangDungHen = ((float) tongSlLayHangDungHen / (float) tongSlTrongKy) * 100;
            tlLayHangTcDungHen = ((float) tongSlLayHangTcDungHen / (float) tongSlTrongKy) * 100;
        }

        BaoCaoKPIThuResDTO2 bcKPIThu = new BaoCaoKPIThuResDTO2();
        bcKPIThu.setsL(sl);
        bcKPIThu.setSlThuTC(slThuTC);
        bcKPIThu.setSlThuDG(slThuDG);
        bcKPIThu.setSlThuDG1(slThuDG1);
        bcKPIThu.setHuy(huy);
        bcKPIThu.setXanh(xanh);
        bcKPIThu.setVang(vang);
        bcKPIThu.setDo1(do1);
        bcKPIThu.setDo2(do2);
        bcKPIThu.setDo3(do3);
        bcKPIThu.setTongTon(tongTon);
        bcKPIThu.setTlThuDG(tlThuDungGio);
        bcKPIThu.setTlThuDG1(tlThuDG1);
        bcKPIThu.setDropOff(slDropOff);
        bcKPIThu.setGiaiTrinh(slGiaiTrinh);
        bcKPIThu.setChuaGiaiTrinh(slChuaGiaiTrinh);
        bcKPIThu.setTonSLAQuaHan(quaHanSLA);
        bcKPIThu.setTonSLAConHan(conHanSLA);
        bcKPIThu.setTlLayHangDungHen(tlLayHangDungHen);
        bcKPIThu.setTlLayHangTcDungHen(tlLayHangTcDungHen);

        List<BaoCaoKPIThuResDTO2> listTPN = new ArrayList<>();
        listTPN.add(bcKPIThu);

        return listTPN;
    }

    public List<BaoCaoKPIThuResDTO2> getSumData(Integer luyKe, Integer cap, String vung, String vungCon, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao) {
        List<BaoCaoKPIThuResDTO2> result = new ArrayList<>();
        List<String> cn;
        List<String> bc;

        LocalDate ngayDauThang = LocalDate.now();
        if (luyKe == 1){
            ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        }else if (luyKe == 2){
            ngayDauThang = LocalDate.from(ngayBaoCao).plusDays(6);
        }

//        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        if (!SSOChecking(cap, chiNhanh, buuCuc)) {
            return result;
        }

        if (chiNhanh.isEmpty()) {
            cn = ListVariableLocation.listChiNhanhVeriable;
        } else {
            cn = List.of(chiNhanh);
        }

        if (buuCuc.isEmpty()) {
            bc = ListVariableLocation.listBuuCucVeriable;
        } else {
            bc = List.of(buuCuc);
        }

        if (isAdmin()) {
            if (luyKe == 0) {
                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            result = bcKPIThuRepo.findBaoCaoKPIThuTCTSumShopee(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.VUNG:
                            result = bcKPIThuRepo.findBaoCaoKPIThuVung1SumShopee(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSumShopee(doiTac, chiNhanh, buuCuc,vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.DOI_TAC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuDTSumShopee(doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSumShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            result = bcKPIThuRepo.findBaoCaoKPIThuTCTSum(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.VUNG:
                            result = bcKPIThuRepo.findBaoCaoKPIThuVung1Sum(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSum(doiTac, chiNhanh, buuCuc,vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.DOI_TAC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuDTSum(doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSum(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            result = bcKPIThuRepo.findBaoCaoKPIThuTCTSumLKShopee(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.VUNG:
                            result = bcKPIThuRepo.findBaoCaoKPIThuVung1SumLKShopee(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;

                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSumLKShopee(doiTac, chiNhanh, buuCuc,vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.DOI_TAC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuDTSumLKShopee(doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSumLKShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (cap) {
                        case Constant.TONG_CTY:
                            //cr tháng 10-2024: báo cáo cấp tổng cty + vùng HNI/HCM -> thêm bộ lọc vùng con
                            result = bcKPIThuRepo.findBaoCaoKPIThuTCTSumLK(doiTac, vung, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.VUNG:
                            result = bcKPIThuRepo.findBaoCaoKPIThuVung1SumLK(doiTac, vung, chiNhanh, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;

                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSumLK(doiTac, chiNhanh, buuCuc, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.DOI_TAC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuDTSumLK(doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSumLK(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                }
            }
        } else {
            if (luyKe == 0) {
                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSum1Shopee(doiTac, cn, bc,vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSum1Shopee(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSum1(doiTac, cn, bc, vungCon, dichVu, loaiDon, ngayBaoCao);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSum1(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                if (doiTac.equals("SHOPEE")) {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSum1LKShopee(doiTac, cn, bc, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSum1LKShopee(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                } else  {
                    switch (cap) {
                        case Constant.CHI_NHANH:
                            result = bcKPIThuRepo.findBaoCaoKPIThuCNSum1LK(doiTac, cn, bc, vungCon, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        case Constant.BUU_CUC:
                            result = bcKPIThuRepo.findBaoCaoKPIThuBCSum1LK(cn, bc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang,tmp);
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        return result;
    }

    public List<BaoCaoKPIThuBillResDTO> exportExcelKPIThuBill(HttpServletResponse response, String chiNhanh, String vung, String vungCon, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String loaiDon) throws IOException {
        KPIThuBillExcelExporter exportKPIPhatBillExcelExporter;
        List<BaoCaoKPIThuBillResDTO> kpiThu = Collections.emptyList();

        LocalDate ngayDauThang = LocalDate.now();
        if (luyke == 1){
            ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        }else if (luyke == 2){
            ngayDauThang = LocalDate.from(ngayBaoCao).plusDays(6);
        }

//        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        List<String> cn;
        List<String> bc;
        if (!isAdmin()) {
            if (!chiNhanh.isEmpty()) {
                if (!ListVariableLocation.listChiNhanhVeriable.contains(chiNhanh)) {
                    exportKPIPhatBillExcelExporter = new KPIThuBillExcelExporter(kpiThu);
                    exportKPIPhatBillExcelExporter.exportDataToExcel(response);
                    return kpiThu;
                }
                cn = List.of(chiNhanh);
            } else cn = ListVariableLocation.listChiNhanhVeriable;
            if (!buuCuc.isEmpty()) {
                if (!ListVariableLocation.listBuuCucVeriable.contains(buuCuc)) {
                    exportKPIPhatBillExcelExporter = new KPIThuBillExcelExporter(kpiThu);
                    exportKPIPhatBillExcelExporter.exportDataToExcel(response);
                    return kpiThu;
                }
                bc = List.of(buuCuc);
            } else bc = ListVariableLocation.listBuuCucVeriable;
            if (luyke == 0)
                kpiThu = bcKPIThuBillRepo.findBaoCaoKPIThuBillCN1(vung, vungCon, cn, bc, doiTac, dichVu, loaiDon, ngayBaoCao);
            else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                kpiThu = bcKPIThuBillRepo.findBaoCaoKPIThuBillCNLK1(vung, vungCon, cn, bc, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang);
            }
        } else {
            if (luyke == 0)
                kpiThu = bcKPIThuBillRepo.findBaoCaoKPIThuBillCN(vung, vungCon, chiNhanh, buuCuc, doiTac, dichVu, loaiDon, ngayBaoCao);
            else{
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                kpiThu = bcKPIThuBillRepo.findBaoCaoKPIThuBillCNLK(vung, vungCon, chiNhanh, buuCuc, doiTac, dichVu, loaiDon, ngayBaoCao, ngayDauThang);
            }
        }
        exportKPIPhatBillExcelExporter = new KPIThuBillExcelExporter(kpiThu);
        exportKPIPhatBillExcelExporter.exportDataToExcel(response);
        return kpiThu;
    }

    public List<BaoCaoKPIThuBillResDTO> exportExcelKPIThuTon(HttpServletResponse response, String chiNhanh, String vung, String vungCon, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String loaiDon) throws IOException {
        KPIThuTonExcelExporter exportKPIThuTonExcelExporter;
        List<BaoCaoKPIThuBillResDTO> kpiThu = Collections.emptyList();
        List<String> cn;
        List<String> bc;
        if (!isAdmin()) {
            if (!chiNhanh.isEmpty()) {
                if (!ListVariableLocation.listChiNhanhVeriable.contains(chiNhanh)) {
                    exportKPIThuTonExcelExporter = new KPIThuTonExcelExporter(kpiThu);
                    exportKPIThuTonExcelExporter.exportDataToExcel(response);
                    return kpiThu;
                }
                cn = List.of(chiNhanh);
            } else {
                cn = ListVariableLocation.listChiNhanhVeriable;
            }
            if (!buuCuc.isEmpty()) {
                if (!ListVariableLocation.listBuuCucVeriable.contains(buuCuc)) {
                    exportKPIThuTonExcelExporter = new KPIThuTonExcelExporter(kpiThu);
                    exportKPIThuTonExcelExporter.exportDataToExcel(response);
                    return kpiThu;
                }
                bc = List.of(buuCuc);
            } else {
                bc = ListVariableLocation.listBuuCucVeriable;
            }
            kpiThu = bcKPIThuBillRepo.findBaoCaoKPIThuTonCN1(vung, vungCon, cn, bc, doiTac, dichVu, loaiDon,ngayBaoCao);
        } else {
            kpiThu = bcKPIThuBillRepo.findBaoCaoKPIThuTonCN(vung, vungCon, chiNhanh, buuCuc, doiTac, dichVu, loaiDon,ngayBaoCao);

        }
        exportKPIThuTonExcelExporter = new KPIThuTonExcelExporter(kpiThu);
        exportKPIThuTonExcelExporter.exportDataToExcel(response);
        return kpiThu;
    }

    @Override
    public DashboardTikTokKpiThuResponse getInfoDashboardKpiThuTikTok(int type, LocalDate requestDate, String maChiNhanh, String maBuuCuc, String maDoiTac) {

        List<String> listChinhanh = null;
        List<String> listBuuCuc = null;
        if(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsTTDVKH().equalsIgnoreCase("true")){
            logger.info("Case view All Cn-Bc");
            listChinhanh = new ArrayList<>();
            listBuuCuc = new ArrayList<>();

        }else if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")){
            logger.info("Case GD-CN");
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = new ArrayList<>();
            logger.info("List CN : {}", listChinhanh);
        }else if(UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")){
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = UserContext.getUserData().getListBuuCucVeriable();
            logger.info("Case LD Buu cuc");
            logger.info("List CN : {}", listChinhanh);
            logger.info("List BC : {}", listBuuCuc);
        }else{
            return new DashboardTikTokKpiThuResponse();
        }

        LocalDate previousRequestDate = requestDate.minusDays(1);
        LocalDate firstDayOfMonth = YearMonth.of(requestDate.getYear(), requestDate.getMonthValue()).atDay(1);
        final String dropOff = "DROPOFF";
        /* Result sort descending by Date */
        List<DashboardTikTokKpiThuResponse> dashboardTikTokKpiThuResponseList;
        DashboardTikTokKpiThuResponse response = new DashboardTikTokKpiThuResponse();
        Long slTonThu = bcKPIThuRepo.getTongSlTonThu(maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc,requestDate);
        slTonThu = slTonThu == null ? 0 : slTonThu;
        Long numberDropOff;

        /*
         * type = 1 -> N & N-1
         * type = 0 -> Luy Ke
         * */
        /* Ngay N & N-1*/
        if (type == 1) {
            logger.debug("Case theo ngày : {}", requestDate);
            dashboardTikTokKpiThuResponseList = bcKPIThuRepo.getInfoDashBoardKpiThuTikTok(previousRequestDate, requestDate, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
            numberDropOff = bcKPIThuRepo.sumByLoaiDon(dropOff, requestDate, requestDate, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
            numberDropOff = numberDropOff == null ? 0 : numberDropOff;
            logger.debug("Number Dropoff theo theo ngày from : {} to : {}", previousRequestDate, requestDate);
            if (dashboardTikTokKpiThuResponseList.size() == 0) {
                response.setTongSanLuong(null, null, null, null, null, null);
                response.setTyLe(null, null, null);
            } else {
                DashboardTikTokKpiThuResponse requestDateData = dashboardTikTokKpiThuResponseList.get(0);
                boolean isRequestDateHasData = requestDateData.getNgayBaoCao().isEqual(requestDate);
                if (isRequestDateHasData) {
                    response.setTongSanLuong(requestDateData.getTongSlThu(), requestDateData.getSlThuThanhCong(), requestDateData.getSlThuThanhCongDungGio(), requestDateData.getSlThuLan1DungHan(), slTonThu, requestDateData.getSlHuy());
                    /* set Ty le*/
                    setTyLe(response, numberDropOff);
                } else {
                    response.setTongSanLuong(null, null, null, null, null, null);
                    response.setTyLe(null, null, null);
                }
            }
            /* set chenh lech value */
            setChenhLechValuesDashboardKpiTikTok(dashboardTikTokKpiThuResponseList, requestDate, firstDayOfMonth, response, type);

        } else {
            logger.debug("Case theo Lũy Kế: {}", requestDate);
            dashboardTikTokKpiThuResponseList = bcKPIThuRepo.getInfoDashBoardKpiThuTikTok(firstDayOfMonth, requestDate, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
            if (dashboardTikTokKpiThuResponseList.size() == 0) {
                response.setTongSanLuong(null, null, null, null, null, null);
                response.setTyLe(null, null, null);
            } else {
                setTongSanLuongLuyKe(dashboardTikTokKpiThuResponseList, response, slTonThu);
                /* set Ty le*/
                numberDropOff = bcKPIThuRepo.sumByLoaiDon(dropOff, firstDayOfMonth, requestDate, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
                numberDropOff = numberDropOff == null ? 0 : numberDropOff;
                logger.debug("Number Dropoff : {} theo theo ngày from : {} to : {}", numberDropOff, firstDayOfMonth, requestDate);
                setTyLe(response, numberDropOff);
            }
            /* set chenh lech value */
            setChenhLechValuesDashboardKpiTikTok(dashboardTikTokKpiThuResponseList, requestDate, firstDayOfMonth, response, type);

        }

        return response;
    }

    @Override
    public BaoCaoTonResponse getBaoCaoTon(String maChiNhanh, String maBuuCuc, String maDoiTac) {

        List<String> listChinhanh = null;
        List<String> listBuuCuc = null;
        if(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true")){
            logger.info("Case view All Cn-Bc");
            listChinhanh = new ArrayList<>();
            listBuuCuc = new ArrayList<>();

        }else if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")){
            logger.info("Case GD-CN");
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = new ArrayList<>();
            logger.info("List CN : {}", listChinhanh);
        }else if(UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")){
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = UserContext.getUserData().getListBuuCucVeriable();
            logger.info("Case LD Buu cuc");
            logger.info("List CN : {}", listChinhanh);
            logger.info("List BC : {}", listBuuCuc);
        }else{
            return new BaoCaoTonResponse();
        }

        BaoCaoKPIThuResDTO2 tongCacLoaiCanhBao = bcKPIThuRepo.getTongLoaiCanhBao(maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);

        /* Check case các loại cảnh báo == null */
        if (tongCacLoaiCanhBao.getTongTon() == null) {
            String message = String.format("Tổng các loại cảnh báo của bưu cục: %s - chi nhánh: %s - đối tác: %s: NULL", maChiNhanh, maBuuCuc, maDoiTac);
            logger.info(message);
            return new BaoCaoTonResponse();
        }
        long tonXanhValue = tongCacLoaiCanhBao.getXanh();
        long tonVangValue = tongCacLoaiCanhBao.getVang();
        long tonDo1Value = tongCacLoaiCanhBao.getDo1();
        long tonDo2Value = tongCacLoaiCanhBao.getDo2();
        long tonDo3Value = tongCacLoaiCanhBao.getDo3();
        long tongTonValue = tongCacLoaiCanhBao.getTongTon();

        BaoCaoTonDto tonXanh = new BaoCaoTonDto("Xanh", tonXanhValue, calculateTyLeTon(tonXanhValue, tongTonValue));
        BaoCaoTonDto tonVang = new BaoCaoTonDto("Vang", tonVangValue, calculateTyLeTon(tonVangValue, tongTonValue));
        BaoCaoTonDto tonDo1 = new BaoCaoTonDto("Do1", tonDo1Value, calculateTyLeTon(tonDo1Value, tongTonValue));
        BaoCaoTonDto tonDo2 = new BaoCaoTonDto("Do2", tonDo2Value, calculateTyLeTon(tonDo2Value, tongTonValue));
        BaoCaoTonDto tonDo3 = new BaoCaoTonDto("Do3", tonDo3Value, calculateTyLeTon(tonDo3Value, tongTonValue));
        return new BaoCaoTonResponse(List.of(tonXanh, tonVang, tonDo1, tonDo2, tonDo3), tongTonValue);

    }

    private void setChenhLechValuesDashboardKpiTikTok(List<DashboardTikTokKpiThuResponse> responses, LocalDate requestDate, LocalDate firstDayOfMonth, DashboardTikTokKpiThuResponse resultChenhLech, int type) {
        int numberRecord = responses.size();
        switch (numberRecord) {
            case 0:
                logger.debug("Case number record = 0");
                resultChenhLech.setValueChenhLech(null, null, null, null, null, null);
                break;
            case 1:
                logger.debug("Case numberRecord result = 1");
                resultChenhLech.setValueChenhLech(null, null, null, null, null, null);
                break;
            default:
                DashboardTikTokKpiThuResponse dayNData = responses.get(0);
                logger.debug("Data DayN: {}", dayNData.toString());
                DashboardTikTokKpiThuResponse previousDayNData = responses.get(1);
                logger.debug("Data previous DayN: {}", previousDayNData.toString());

                /* Sản lượng tồn không ăn theo bộ lọc thời gian , chỉ filter theo chi nhánh, buu cục, mã khách hàng */
                if (type == 1) {
                    logger.debug("Case numberRecord result >= 2  & Data theo ngày từ ngày : {} đến ngày : {} ", requestDate.minusDays(1), requestDate);
                    resultChenhLech.setValueChenhLech(dayNData.getTongSlThu() - previousDayNData.getTongSlThu(),
                            dayNData.getSlThuThanhCong() - previousDayNData.getSlThuThanhCong(),
                            dayNData.getSlThuThanhCongDungGio() - previousDayNData.getSlThuThanhCongDungGio(),
                            dayNData.getSlThuLan1DungHan() - previousDayNData.getSlThuLan1DungHan(),
                            0L,
                            dayNData.getSlHuy() - previousDayNData.getSlHuy());
                }
                if (type == 0) {
                    LocalDate previousRequestDay = requestDate.minusDays(1);
                    if (dayNData.getNgayBaoCao().isEqual(requestDate) && previousDayNData.getNgayBaoCao().isEqual(previousRequestDay)) {
                        logger.debug("Case numberRecord result >= 2  & Data theo Lũy Kế từ ngày : {} đến ngày : {} ", firstDayOfMonth, requestDate);
                        resultChenhLech.setValueChenhLech(dayNData.getTongSlThu() - previousDayNData.getTongSlThu(),
                                dayNData.getSlThuThanhCong() - previousDayNData.getSlThuThanhCong(),
                                dayNData.getSlThuThanhCongDungGio() - previousDayNData.getSlThuThanhCongDungGio(),
                                dayNData.getSlThuLan1DungHan() - previousDayNData.getSlThuLan1DungHan(),
                                0L,
                                dayNData.getSlHuy() - previousDayNData.getSlHuy());
                    } else {
                        logger.debug("Case numberRecord result >= 2  & Data theo Lũy Kế từ ngày : {} đến ngày : {} nhưng data thiếu ngày : {} hoặc {} hoăc cả 2", firstDayOfMonth, requestDate, previousRequestDay, requestDate);
                        resultChenhLech.setValueChenhLech(null, null, null, null, null, null);
                    }
                }
                break;
        }
    }

    private void setTongSanLuongLuyKe(List<DashboardTikTokKpiThuResponse> dataList, DashboardTikTokKpiThuResponse response, Long sanLuongTon) {
        long slTongThu = dataList.stream().map(DashboardTikTokKpiThuResponse::getTongSlThu).reduce(0L, Long::sum);
        long slTTC = dataList.stream().map(DashboardTikTokKpiThuResponse::getSlThuThanhCong).reduce(0L, Long::sum);
        long slThuThanhCongDungGio = dataList.stream().map(DashboardTikTokKpiThuResponse::getSlThuThanhCongDungGio).reduce(0L, Long::sum);
        long slThuLan1DungHan = dataList.stream().map(DashboardTikTokKpiThuResponse::getSlThuLan1DungHan).reduce(0L, Long::sum);
//        long slTon = dataList.stream().map(DashboardTikTokKpiThuResponse::getSlTon).reduce(0L, Long::sum);
        long slHuy = dataList.stream().map(DashboardTikTokKpiThuResponse::getSlHuy).reduce(0L, Long::sum);

        response.setTongSanLuong(slTongThu, slTTC, slThuThanhCongDungGio, slThuLan1DungHan, sanLuongTon, slHuy);
    }

    private void setTyLe(DashboardTikTokKpiThuResponse response, long slDropOff) {
        Double tyLeTTC = (response.getTongSlThu() - response.getSlHuy() - slDropOff) == 0 ? null : (response.getSlThuThanhCong() * 100.0) / (response.getTongSlThu() - response.getSlHuy() - slDropOff);
        Double tyLeThuThanhCongDungGio = response.getSlThuThanhCong() == 0 ? null : (response.getSlThuThanhCongDungGio() * 100.0 / response.getSlThuThanhCong());
        Double tyLeThuThanhCongLan1DungGio = response.getSlThuThanhCong() == 0 ? null : (response.getSlThuLan1DungHan() * 100.0 / response.getSlThuThanhCong());

        response.setTyLe(tyLeTTC, tyLeThuThanhCongDungGio, tyLeThuThanhCongLan1DungGio);
    }

    public static Double calculateTyLeTon(Long tonValue, Long tongTon) {
        return ( tongTon == null || tongTon == 0 ) ? null : tonValue * 100.0 / tongTon;
    }

    @Override
    public List<DashboardTop10TikTokResponse> top10ThuThapNhat(int type, LocalDate requestDay, String maChiNhanh, String maBuuCuc, String doiTac) {
        LocalDate firstDayOfMonth = YearMonth.of(requestDay.getYear(), requestDay.getMonthValue()).atDay(1);
        List<DashboardTop10TikTokKpiThuResult> results = null;
        List<DashboardTop10TikTokKpiThuResult> resultsLkt = null;
        List<Top10TikTokKpiThuSlTheoLoaiDonResult> slDropoff = null;
        List<DashboardTop10TikTokResponse> responses = null;
        // type=1 -> ngày, type=0 -> lũy kế
        if (type == 1) {
            if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                results = bcKPIThuRepo.getDataChartKpiThuChiNhanhTikTok(requestDay, requestDay, null, null, doiTac);
                resultsLkt = bcKPIThuRepo.getDataChartKpiThuChiNhanhTikTok(firstDayOfMonth, requestDay, null, null, doiTac);
//                slDropoff = bcKPIThuRepo.sumSlChiNhanhByLoaiDon("DROPOFF", requestDay, requestDay, null, null, doiTac);
            } else {
                results = bcKPIThuRepo.getDataChartKpiThuBuuCucTikTok(requestDay, requestDay, maChiNhanh, maBuuCuc, doiTac);
//                slDropoff = bcKPIThuRepo.sumSlBuuCucByLoaiDon("DROPOFF", requestDay, requestDay, maChiNhanh, maBuuCuc, doiTac);
                resultsLkt = bcKPIThuRepo.getDataChartKpiThuBuuCucTikTok(firstDayOfMonth, requestDay, maChiNhanh, maBuuCuc, doiTac);
            }
            responses = getTop10KpiThuTikTok(slDropoff, results, resultsLkt, type);

        } else {
            if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                results = bcKPIThuRepo.getDataChartKpiThuChiNhanhTikTok(firstDayOfMonth, requestDay, null, null, doiTac);
//                slDropoff = bcKPIThuRepo.sumSlChiNhanhByLoaiDon("DROPOFF", firstDayOfMonth, requestDay, null, null, doiTac);
            } else {
                results = bcKPIThuRepo.getDataChartKpiThuBuuCucTikTok(firstDayOfMonth, requestDay, maChiNhanh, maBuuCuc, doiTac);
//                slDropoff = bcKPIThuRepo.sumSlBuuCucByLoaiDon("DROPOFF", firstDayOfMonth, requestDay, maChiNhanh, maBuuCuc, doiTac);
            }
            responses = getTop10KpiThuTikTok(slDropoff, results, results, type);
        }
        return responses;
    }

    private List<DashboardTop10TikTokResponse> getTop10KpiThuTikTok(List<Top10TikTokKpiThuSlTheoLoaiDonResult> slDropoff, List<DashboardTop10TikTokKpiThuResult> dashboardTop10TikTokKpiThuResults, List<DashboardTop10TikTokKpiThuResult> dashboardTop10TikTokKpiThuResultLkt, int type) {
        List<DashboardTop10TikTokKpiThuResult> newResult = new ArrayList<>();
        List<DashboardTop10TikTokKpiThuResult> listTlThuThanhCong = new ArrayList<>();
        //Tính tỉ lệ
        dashboardTop10TikTokKpiThuResultLkt.forEach(o -> {
            Long slPhatSinhLkt = Optional.ofNullable(o.getSlPhatSinh()).orElse(0l);
            Long slThuThanhCongLkt = Optional.ofNullable(o.getSlThuThanhCong()).orElse(0l);
            Float tlThuThanhCong = slPhatSinhLkt == 0 ? null : (slThuThanhCongLkt.floatValue() / slPhatSinhLkt * 100);
            o.setTlThuThanhCong(tlThuThanhCong);
            listTlThuThanhCong.add(o);
        });
        dashboardTop10TikTokKpiThuResults.forEach(obj -> {
            //Tính sản lượng tực tế
//            Top10TikTokKpiThuSlTheoLoaiDonResult slDropOff = slDropoff.stream().filter(s -> s.getDonVi().equals(obj.getDonVi())).findFirst().orElse(new Top10TikTokKpiThuSlTheoLoaiDonResult(obj.getDonVi(), 0l));
//            Long slHuy = Optional.ofNullable(obj.getSlHuy()).orElse(0l);

//            Long slThucTe = slPhatSinh - slHuy - slDropOff.getSl();
            // Tỉ lệ thu thành công thực tế
            Float tlThuDungHanThucTe = obj.getSlThuThanhCong() == 0 ? null : (obj.getSlThuThanhCongDunggio().floatValue() / obj.getSlThuThanhCong() * 100);
            // Tỉ lệ thu thành công lần 1
            Float tlThuLan1DungHan = obj.getSlThuThanhCong() == 0 ? null : (obj.getSlThuThanhCongDungGioLan1().floatValue() / obj.getSlThuThanhCong() * 100);
            obj.setTlThuThanhCongDungGio(tlThuDungHanThucTe);
            obj.setTlThuThanhCongDungGioLan1(tlThuLan1DungHan);
            newResult.add(obj);
        });
        List<DashboardTop10TikTokResponse> listBieuDoTop10ThuThanhCongThapNhat = new ArrayList<>();
        // Biểu đồ top10 tỉ lệ thu thành công thấp nhất
        List<String> donViTlThuThanhCongThapNhat = new ArrayList<>();
        List<Long> tongSlPhatSinh = new ArrayList<>();
        List<Long> slThuThanhCong = new ArrayList<>();
        List<Float> tlThuThanhCong = new ArrayList<>();
        // Get top 10 có tỉ lệ thu thành công thấp nhất
        /*List<DashboardTop10TikTokKpiThuResult> sorted = listTlThuThanhCong.stream()
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCong, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());*/
        List<DashboardTop10TikTokKpiThuResult> top10TlThuThanhCongThapNhat = listTlThuThanhCong.stream().sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCong, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlThuThanhCong() != null)
                .limit(10)
                .collect(Collectors.toList());
        // Tạo biểu đồ top 10 có tỷ lệ thu thành công tấp nhất
        DashboardTop10TikTokResponse top10ThuThanhCongThapNhat = null;
        top10TlThuThanhCongThapNhat.forEach(o -> {
            donViTlThuThanhCongThapNhat.add(o.getDonVi());
            tongSlPhatSinh.add(o.getSlPhatSinh());
            slThuThanhCong.add(o.getSlThuThanhCong());
            tlThuThanhCong.add(o.getTlThuThanhCong());
        });
        Map<String, List<Object>> mapChart1 = new HashMap<>();
        mapChart1.put("donVi", Collections.singletonList(donViTlThuThanhCongThapNhat));
        mapChart1.put("sl", Collections.singletonList(tongSlPhatSinh));
        mapChart1.put("slThuTC", Collections.singletonList(slThuThanhCong));
        mapChart1.put("tlThuTC", Collections.singletonList(tlThuThanhCong));
        top10ThuThanhCongThapNhat = new DashboardTop10TikTokResponse("Top10ThuThanhCongThapNhat", mapChart1);
        listBieuDoTop10ThuThanhCongThapNhat.add(top10ThuThanhCongThapNhat);

        // Biểu đồ top10 tỉ lệ thu thành công đúng giờ thấp nhất
        List<String> donViTlThuDungGio = new ArrayList<>();
        List<Long> tongSlThuThanhCong = new ArrayList<>();
        List<Long> slThuThanhCongDungGio = new ArrayList<>();
        List<Float> tlThuThanhCongDungGio = new ArrayList<>();
        // Get top 10 có tỉ lệ thu thành công đúng giờ thấp nhất

        /*List<DashboardTop10TikTokKpiThuResult> sorted2 = newResult.stream()
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCongDungGio, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());*/
        List<DashboardTop10TikTokKpiThuResult> top10TlThuThanhCongDungGioThapNhat = newResult.stream().sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCongDungGio, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlThuThanhCongDungGio() != null)
                .limit(10)
                .collect(Collectors.toList());

        // Tạo biểu đồ top 10 có tỷ lệ thu thành công đúng giờ thấp nhất
        DashboardTop10TikTokResponse top10ThuThanhCongDungGioThapNhat = null;
        top10TlThuThanhCongDungGioThapNhat.forEach(o -> {
            donViTlThuDungGio.add(o.getDonVi());
            tongSlThuThanhCong.add(o.getSlThuThanhCong());
            slThuThanhCongDungGio.add(o.getSlThuThanhCongDunggio());
            tlThuThanhCongDungGio.add(o.getTlThuThanhCongDungGio());
        });
        Map<String, List<Object>> mapChart2 = new HashMap<>();
        mapChart2.put("donVi", Collections.singletonList(donViTlThuDungGio));
        mapChart2.put("slThuTC", Collections.singletonList(tongSlThuThanhCong));
        mapChart2.put("slThuDungHan", Collections.singletonList(slThuThanhCongDungGio));
        mapChart2.put("tlThuDungHan", Collections.singletonList(tlThuThanhCongDungGio));
        top10ThuThanhCongDungGioThapNhat = new DashboardTop10TikTokResponse("Top10ThuThanhCongDungGioThapNhat", mapChart2);
        listBieuDoTop10ThuThanhCongThapNhat.add(top10ThuThanhCongDungGioThapNhat);

        // Biểu đồ top10 tỉ lệ thu thành công lần 1 thấp nhất
        List<String> donViTlThuLan1 = new ArrayList<>();
        List<Long> tongSlThuTC = new ArrayList<>();
        List<Long> slThuLan1 = new ArrayList<>();
        List<Float> tlThuLan1 = new ArrayList<>();

        // Get top 10 có tỉ lệ thu thành công lần 1 thấp nhất
       /* List<DashboardTop10TikTokKpiThuResult> sorted3 = newResult.stream()
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCongDungGioLan1, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());*/
        List<DashboardTop10TikTokKpiThuResult> top10TlThuThanhCongLan1ThapNhat = newResult.stream().sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCongDungGioLan1, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlThuThanhCongDungGioLan1() != null)
                .limit(10)
                .collect(Collectors.toList());
        // Tạo biểu đồ top 10 có tỉ lệ thu thành công lần 1 thấp nhất
        top10TlThuThanhCongLan1ThapNhat.forEach(o -> {
            donViTlThuLan1.add(o.getDonVi());
            tongSlThuTC.add(o.getSlThuThanhCong());
            slThuLan1.add(o.getSlThuThanhCongDungGioLan1());
            tlThuLan1.add(o.getTlThuThanhCongDungGioLan1());
        });
        Map<String, List<Object>> mapChart3 = new HashMap<>();
        mapChart3.put("donVi", Collections.singletonList(donViTlThuLan1));
        mapChart3.put("slThuTC", Collections.singletonList(tongSlThuTC));
        mapChart3.put("slThuLan1", Collections.singletonList(slThuLan1));
        mapChart3.put("tlThuLan1", Collections.singletonList(tlThuLan1));
        DashboardTop10TikTokResponse top10ThuThanhCongLan1ThapNhat = null;
        top10ThuThanhCongLan1ThapNhat = new DashboardTop10TikTokResponse("Top10ThuThanhCongLan1ThapNhat", mapChart3);
        listBieuDoTop10ThuThanhCongThapNhat.add(top10ThuThanhCongLan1ThapNhat);

        //Tổng hợp 3 biểu đồ tính theo kpi thu đúng giờ lần 1 của đơn vị , Tiêu chí đánh giá tỉ lệ thu đúng giờ nhỏ hơn 85%
        List<DashboardTop10TikTokKpiThuResult> sortedByTlThuDungGio = newResult.stream().filter(o -> o.getTlThuThanhCongDungGio() != null).filter(o -> o.getTlThuThanhCongDungGio() < 85f)
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCongDungGio, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        List<DashboardTop10TikTokKpiThuResult> top15TlThuThanhCongDungGioThapNhat = sortedByTlThuDungGio.stream().sorted(Comparator.comparing(DashboardTop10TikTokKpiThuResult::getTlThuThanhCongDungGio, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlThuThanhCongDungGio() != null)
                .limit(15)
                .collect(Collectors.toList());
        // Tạo list 3 biểu đồ
        List<String> donVi = new ArrayList<>();
        List<Float> tlTTC = new ArrayList<>();
        List<Float> tlTDG = new ArrayList<>();
        List<Float> tlTDGL1 = new ArrayList<>();
        top15TlThuThanhCongDungGioThapNhat.forEach(o -> {
            donVi.add(o.getDonVi());
            tlTDG.add(o.getTlThuThanhCongDungGio());
            DashboardTop10TikTokKpiThuResult dashboardTop10TikTokKpiThuResult = newResult.stream().filter(s -> s.getDonVi().equals(o.getDonVi())).findFirst().orElse(new DashboardTop10TikTokKpiThuResult(o.getDonVi(), 0f, 0f, 0f));
            tlTTC.add(dashboardTop10TikTokKpiThuResult.getTlThuThanhCong());
            tlTDGL1.add(dashboardTop10TikTokKpiThuResult.getTlThuThanhCongDungGioLan1());
        });
        Map<String, List<Object>> mapChart4 = new HashMap<>();
        mapChart4.put("donVi", Collections.singletonList(donVi));
        mapChart4.put("tlThuTC", Collections.singletonList(tlTTC));
        mapChart4.put("tlThuDG", Collections.singletonList(tlTDG));
        mapChart4.put("tlThuLan1", Collections.singletonList(tlTDGL1));
        DashboardTop10TikTokResponse top15ThuThanhCongLan1ThapNhat = new DashboardTop10TikTokResponse("Top15BieuDoTyLe", mapChart4);
        listBieuDoTop10ThuThanhCongThapNhat.add(top15ThuThanhCongLan1ThapNhat);
        return listBieuDoTop10ThuThanhCongThapNhat;

    }

    public List<BaoCaoKPIThuTop10SLResponse> getTop10SanLuong(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe, String maDoiTac) {
        LocalDate ngayBatDau;

        //nếu lũy kế thì ngày bắt đầu = ngày đầu tiên của tháng
        //không lũy kế thì ngày bắt đầu = ngày báo cáo
        ngayBatDau = luyKe == 0 ? ngayBaoCao.withDayOfMonth(1) : ngayBaoCao;

        List<String> bc;
        List<String> cn;
        List<BaoCaoKPIThuTop10SLResponse> result = new ArrayList<>();

        if (!isAdmin())
            if (!retainBCCN(chiNhanh, buuCuc)) return result;

        //lấy data cho tài khoản admin
        if (isAdmin()) {
            if (buuCuc.equals("")) {
                if (chiNhanh.equals("")) {
                    result = bcKPIThuRepo.findKPIThuTop10SLTCT(ngayBaoCao, ngayBatDau, maDoiTac);
                } else
                    result = bcKPIThuRepo.findKPIThuTop10SLCN(ngayBaoCao, chiNhanh, ngayBatDau, maDoiTac);
            } else
                result = bcKPIThuRepo.findKPIThuTop10SLBC(ngayBaoCao, chiNhanh, buuCuc, ngayBatDau, maDoiTac);
        }
        //lấy data cho tài khoản không phải admin
        else {
            if (buuCuc.equals("")) {
                if (chiNhanh.equals("")) {
                    bc = ListVariableLocation.listBuuCucVeriable;
                    cn = ListVariableLocation.listChiNhanhVeriable;
                    result = bcKPIThuRepo.findKPIThuTop10SLTCTNoAdmin(ngayBaoCao, cn, bc, ngayBatDau, maDoiTac);
                } else {
                    bc = ListVariableLocation.listBuuCucVeriable;
                    result = bcKPIThuRepo.findKPIThuTop10SLCNNoAdmin(ngayBaoCao, chiNhanh, bc, ngayBatDau, maDoiTac);
                }
            } else
                result = bcKPIThuRepo.findKPIThuTop10SLBC(ngayBaoCao, chiNhanh, buuCuc, ngayBatDau, maDoiTac);
        }

        //sort List theo chiều ngược lại(sản lượng cao nhất lên trên)
        Collections.sort(result, Collections.reverseOrder());

        //return 10 kết quả
        return result.stream().limit(10).collect(Collectors.toList());
    }

    //check bưu cục, chi nhánh có nằm trong list mà gateway trả về hay không
    //nếu không đúng như list gateway trả về thì không ra số liệu
    private boolean retainBCCN(String chiNhanh, String buuCuc) {
        List<String> cn = new ArrayList<>(Arrays.asList(chiNhanh));
        List<String> bc = new ArrayList<>(Arrays.asList(buuCuc));
        if (!chiNhanh.equals("")) {
            cn.retainAll(ListVariableLocation.listChiNhanhVeriable);
            if (cn.isEmpty()) {
                return false;
            }
        }
        if (!buuCuc.equals("")) {
            bc.retainAll(ListVariableLocation.listBuuCucVeriable);
            if (bc.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public List<String> allBuuTa(String chinhanh, String buuCuc, LocalDate ngayBaoCao, Integer luyKe){
        LocalDate ngayDauThang = luyKe == 1 ? ngayBaoCao.withDayOfMonth(1) : ngayBaoCao;

        return bcKPIThuRepo.findDistinctBuuTa(ngayBaoCao, ngayDauThang, chinhanh, buuCuc);
    }
}
