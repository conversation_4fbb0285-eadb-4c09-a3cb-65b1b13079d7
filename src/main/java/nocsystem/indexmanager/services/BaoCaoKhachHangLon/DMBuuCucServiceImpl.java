package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMBuuCuc;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.DMBuuCucRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.DMChiNhanhRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.DMChiNhanhVungRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service Interface for managing {@link DMBuuCuc}.
 */
@Service
public class DMBuuCucServiceImpl implements DMBuuCucService {
    private final Logger log = LoggerFactory.getLogger(DMBuuCucServiceImpl.class);
    private final DMBuuCucRepository dmBuuCucRepository;
    private final DMChiNhanhRepository dmChiNhanhRepository;
    private final DMChiNhanhVungRepository dmChiNhanhVungRepository;

    public DMBuuCucServiceImpl(DMBuuCucRepository dmBuuCucRepository, DMChiNhanhRepository dmChiNhanhRepository, DMChiNhanhVungRepository dmChiNhanhVungRepository) {
        this.dmBuuCucRepository = dmBuuCucRepository;
        this.dmChiNhanhRepository = dmChiNhanhRepository;
        this.dmChiNhanhVungRepository = dmChiNhanhVungRepository;
    }
    @Override
    public DMBuuCuc save(DMBuuCuc dmBuuCuc) {
        log.debug("Request to save DMBuuCuc : {}", dmBuuCuc);
        DMBuuCuc dmChiNhanh1 = dmBuuCucRepository.save(dmBuuCuc);
        return dmChiNhanh1;
    }

    @Override
    public Page<DMBuuCuc> findAll(Pageable pageable) {
        log.debug("Request to get all DMBuuCuc");
        return dmBuuCucRepository.findAll(pageable);
    }


}
