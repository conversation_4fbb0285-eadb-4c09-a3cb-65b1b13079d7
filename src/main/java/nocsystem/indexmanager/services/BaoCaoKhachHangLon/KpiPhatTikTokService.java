package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoTonResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTikTokKpiPhatResponse;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.DashboardTop10TikTokResponse;

import java.time.LocalDate;
import java.util.List;

public interface KpiPhatTikTokService {
    DashboardTikTokKpiPhatResponse getInfoDashBoardKpiPhatTikTok(int type, LocalDate requestDate, String maChiNhanh, String maBuuCuc, String maDoiTac);

    BaoCaoTonResponse getBaoCaoTon(String maChiNhanh, String maBuuCuc, String maDoiTac, LocalDate ngayBaoCao);

    List<DashboardTop10TikTokResponse> bieuDoTop10PhatThapNhat(int type, LocalDate requestDay, String maChiNhanh, String maBuuCuc, String doiTac);
}
