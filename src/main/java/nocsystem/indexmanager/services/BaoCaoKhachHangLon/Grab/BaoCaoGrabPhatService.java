package nocsystem.indexmanager.services.BaoCaoKhachHangLon.Grab;


import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatBillDTO;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabPhatResDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.Grab.BaoCaoGrabBillRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.Grab.BaoCaoGrabRepository;
import nocsystem.indexmanager.services.DashBoardChiNhanh.ExportExcelTemplateCNBC;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;

@Service
public class BaoCaoGrabPhatService<T> {

    private static final Logger logger = LoggerFactory.getLogger(BaoCaoGrabPhatService.class);

    private final BaoCaoGrabRepository bcGrabRepo;

    private final BaoCaoGrabBillRepository bcGrabPhatBillRepo;

    public static final DecimalFormat df = new DecimalFormat("0.00");

    public static final List<String> trangThaiTon = Arrays.asList("200","202","300","301","302","303","400","401","402","403","500","505","506","507","508","509","550","502", "515");
    public static final List<String> trangThaiPTC = Arrays.asList("501", "503", "504");


    public BaoCaoGrabPhatService(BaoCaoGrabRepository bcGrabRepo, BaoCaoGrabBillRepository bcGrabPhatBillRepo) {
        this.bcGrabRepo = bcGrabRepo;
        this.bcGrabPhatBillRepo = bcGrabPhatBillRepo;
    }

    //check admin
    private boolean isAdmin() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsViewAllBcTiktok().equalsIgnoreCase("true");
    }

    public String getTime(LocalDate ngayBaoCao) {
        Page<String> time = bcGrabRepo.getTimeTinhToan(ngayBaoCao, Pageable.ofSize(1));
        String timeTinhToan = "";
        if (time.hasNext())
            timeTinhToan = time.getContent().get(0);

        return timeTinhToan;
    }


    //get data KPI phát
    public ListContentPageDto<BaoCaoGrabPhatResDTO> findAllData(Integer luyKe, Integer loaiBaoCao, String vung, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao, Integer pageQuery, Integer pageSize) {
        Pageable page;
        page = PageRequest.of(pageQuery, pageSize);

        Page<BaoCaoGrabPhatResDTO> pageResult = Page.empty(page);

        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);

        if (!SSOChecking(loaiBaoCao, chiNhanh, buuCuc)) {
            return new ListContentPageDto<BaoCaoGrabPhatResDTO>(Page.empty(page));
        }

        if (isAdmin()) {
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 0:
                        pageResult = bcGrabRepo.baoCaoGrabPhatTCT(vung, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 1:
                        pageResult = bcGrabRepo.baoCaoGrabPhatVung(vung, chiNhanh, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 2:
                        pageResult = bcGrabRepo.baoCaoGrabPhatCN(chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 3:
                        pageResult = bcGrabRepo.baoCaoGrabPhatDT(doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 4:
                        pageResult = bcGrabRepo.baoCaoGrabPhatBC(chiNhanh, buuCuc, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 0:
                        pageResult = bcGrabRepo.baoCaoGrabPhatTCTLK(vung, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 1:
                        pageResult = bcGrabRepo.baoCaoGrabPhatVungLK(vung, chiNhanh, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 2:
                        pageResult = bcGrabRepo.baoCaoGrabPhatCNLK(chiNhanh, buuCuc, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 3:
                        pageResult = bcGrabRepo.baoCaoGrabPhatDTLK(doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 4:
                        pageResult = bcGrabRepo.baoCaoGrabPhatBCLK(buuCuc, chiNhanh, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    default:
                        break;
                }
            }
        } else {
            List<String> listBC;
            List<String> listCN;

            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 2:
                        pageResult = bcGrabRepo.baoCaoGrabPhatCN(listCN, listBC, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 4:
                        pageResult = bcGrabRepo.baoCaoGrabPhatBC(listCN, listBC, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 2:
                        pageResult = bcGrabRepo.baoCaoGrabPhatCNLK(listCN, listBC, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 4:
                        pageResult = bcGrabRepo.baoCaoGrabPhatBCLK(listCN, listBC, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    default:
                        break;
                }
            }
        }

        for (BaoCaoGrabPhatResDTO bc : pageResult) {
            ratioCalculation(bc);
        }
        ListContentPageDto<BaoCaoGrabPhatResDTO> listContent = new ListContentPageDto<>(pageResult);
        listContent.setTime(getTime(ngayBaoCao));
        return listContent;
    }

    //tính tỷ lệ
    private void ratioCalculation(BaoCaoGrabPhatResDTO bc) {

        //PTC = phát thành công
        //tỷ lệ PTC = sản lượng PTC / sản lượng phải phát
        if (bc.getSlPhaiPhat() == null || bc.getSlPhaiPhat() == 0f) {
            bc.setTlPTC(null);
        } else {
            bc.setTlPTC(Float.valueOf(df.format(((float) bc.getSlPTC() / (float) bc.getSlPhaiPhat()) * 100)));
        }

        //tỷ lệ PTC đúng giờ = sản lượng PTC đúng giờ / sản lượng PTC
        if (bc.getSlPTC() == null || bc.getSlPTC() == 0f) {
            bc.setTlPTCDungGio(null);
        } else {
            bc.setTlPTCDungGio(Float.valueOf(df.format(((float) bc.getSlPTCDungGio() / (float) bc.getSlPTC()) * 100)));
        }

        //Tỷ lệ hoàn (7) = SL Hoàn (6) / [SL PTC (5)+ SL Hoàn (6)]
        Long hoan = bc.getSlHoan() + bc.getSlPTC();
        if (hoan == 0) {
            bc.setTlHoan(null);
        } else {
            bc.setTlHoan(Float.valueOf(df.format(((float) bc.getSlHoan() / hoan) * 100)));
        }
    }

    //check SSO
    private boolean SSOChecking(Integer loaiBaoCao, String chiNhanh, String buuCuc) {
        if (!isAdmin() && (loaiBaoCao == 0 || loaiBaoCao == 1 || loaiBaoCao == 3)) {
            return false;
        }
        if (!isAdmin()) {
            if (!chiNhanh.equals("")) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(chiNhanh)) {
                    return false;
                }
            }
            if (!buuCuc.equals("")) {
                if (!UserContext.getUserData().getListBuuCucVeriable().contains(buuCuc)) {
                    return false;
                }
            }
        }
        return true;
    }

    //tính sum và trả kết quả
    public List<BaoCaoGrabPhatResDTO> baoCaoGrabPhatSum(Integer luyKe, Integer loaiBaoCao, String vung, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao) {
        List<BaoCaoGrabPhatResDTO> bcKPI = responseCalculation(luyKe, loaiBaoCao, vung, chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
        Long slPhaiPhat = 0L;
        Long slTonNhanBanGiao = 0L;
        Long slPTC = 0L;
        Long slPTCDungGio = 0L;
        Long tongTon = 0L;
        Long tt500 = 0L;
        Long ttKhac = 0L;
        Long slHuy = 0L;
        Long slHoan = 0L;

        for (BaoCaoGrabPhatResDTO baocaoKPIRes : bcKPI) {
            slPhaiPhat = baocaoKPIRes.getSlPhaiPhat() != null ? slPhaiPhat + baocaoKPIRes.getSlPhaiPhat() : slPhaiPhat;
            slTonNhanBanGiao = baocaoKPIRes.getSlTonNhanBanGiao() != null ? slTonNhanBanGiao + baocaoKPIRes.getSlTonNhanBanGiao() : slTonNhanBanGiao;
            slPTC = baocaoKPIRes.getSlPTC() != null ? slPTC + baocaoKPIRes.getSlPTC() : slPTC;
            tongTon = baocaoKPIRes.getTongTon() != null ? tongTon + baocaoKPIRes.getTongTon() : tongTon;
            tt500 = baocaoKPIRes.getTt500() != null ? tt500 + baocaoKPIRes.getTt500() : tt500;
            ttKhac = baocaoKPIRes.getTtKhac() != null ? ttKhac + baocaoKPIRes.getTtKhac() : ttKhac;
            slPTCDungGio = baocaoKPIRes.getSlPTCDungGio() != null ? slPTCDungGio + baocaoKPIRes.getSlPTCDungGio() : slPTCDungGio;
            slHuy = baocaoKPIRes.getSlHuy() != null ? slHuy + baocaoKPIRes.getSlHuy() : slHuy;
            slHoan = baocaoKPIRes.getSlHoan() != null ? slHoan + baocaoKPIRes.getSlHoan() : slHoan;
        }

        BaoCaoGrabPhatResDTO result = new BaoCaoGrabPhatResDTO();
        result.setSlPhaiPhat(slPhaiPhat);
        result.setSlPTC(slPTC);
        result.setSlPTCDungGio(slPTCDungGio);
        result.setSlTonNhanBanGiao(slTonNhanBanGiao);
        result.setTongTon(tongTon);
        result.setTt500(tt500);
        result.setTtKhac(ttKhac);
        result.setSlHuy(slHuy);
        result.setSlHoan(slHoan);

        ratioCalculation(result);

        List<BaoCaoGrabPhatResDTO> khlGrab = new ArrayList<>();
        khlGrab.add(result);

        return khlGrab;
    }

    //get data cho việc tính sum
    public List<BaoCaoGrabPhatResDTO> responseCalculation(Integer luyKe, Integer loaiBaoCao, String vung, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao) {
        List<BaoCaoGrabPhatResDTO> result = new ArrayList<>();
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        Integer statusMoiNhat = 1;
        if (isAdmin()) {
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 0:
                        result = bcGrabRepo.baoCaoGrabPhatTCTSum(vung, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 1:
                        result = bcGrabRepo.baoCaoGrabPhatVungSum(vung, chiNhanh, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 2:
                        result = bcGrabRepo.baoCaoGrabPhatCNSum(chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 3:
                        result = bcGrabRepo.baoCaoGrabPhatDTSum(doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 4:
                        result = bcGrabRepo.baoCaoGrabPhatBCSum(chiNhanh, buuCuc, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 0:
                        result = bcGrabRepo.baoCaoGrabPhatTCTLKSum(vung, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 1:
                        result = bcGrabRepo.baoCaoGrabPhatVungLKSum(vung, chiNhanh, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 2:
                        result = bcGrabRepo.baoCaoGrabPhatCNLKSum(chiNhanh, buuCuc, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 3:
                        result = bcGrabRepo.baoCaoGrabPhatDTLKSum(doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 4:
                        result = bcGrabRepo.baoCaoGrabPhatBCLKSum(chiNhanh, buuCuc, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            }
        } else {
            List<String> listBC;
            List<String> listCN;

            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 2:
                        result = bcGrabRepo.baoCaoGrabPhatCNSum(listCN, listBC, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 4:
                        result = bcGrabRepo.baoCaoGrabPhatBCSum(listCN, listBC, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 2:
                        result = bcGrabRepo.baoCaoGrabPhatCNLKSum(listCN, listBC, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 4:
                        result = bcGrabRepo.baoCaoGrabPhatBCLKSum(listCN, listBC, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    //lấy data cho xuất excel KPI phát
    public List<Map<String, Object>> getDataExcelGrabPhat(String vungPhat, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String buuTa, String loaiDon, Integer loaiBaoCao) throws IOException, IllegalAccessException, NoSuchFieldException {

        List<BaoCaoGrabPhatResDTO> grabPhat = responseCalculation(luyke, loaiBaoCao, vungPhat, chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
        for (BaoCaoGrabPhatResDTO grab : grabPhat) {
            if (loaiBaoCao == 0) {
                String donVi = grab.getDonVi();
                grab.setDonVi("Vùng " + donVi);
            }
            ratioCalculation(grab);
            BaoCaoGrabPhatResDTO newGrabObject = new BaoCaoGrabPhatResDTO(grab.getDonVi(), grab.getSlPhaiPhat(), grab.getSlTonNhanBanGiao(), grab.getTt500(), grab.getTtKhac()
                    , grab.getSlPTC(), grab.getTlHoan(), grab.getSlHoan(), grab.getSlHuy(), grab.getSlPTCDungGio(), grab.getTlPTCDungGio(), grab.getTlPTC(), grab.getTongTon());

            grabPhat.set(grabPhat.indexOf(grab), newGrabObject);
        }
        return tryTest((List<T>) grabPhat);
    }

    public void exportExcelGrabPhat(HttpServletResponse response, String vung, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String buuTa, String loaiDon, Integer loaiBaoCao, String fileName)
            throws IllegalAccessException, IOException, NoSuchFieldException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("donVi", "Đơn Vị");
        mapHeader.put("slPhaiPhat", "SL Phải phát");
        mapHeader.put("slTonNhanBanGiao", "SL Tồn nhận bàn giao");
        mapHeader.put("tt500", "TT500");
        mapHeader.put("ttKhac", "TT Khác");
        mapHeader.put("slPTC", "SL Phát thành công");
        mapHeader.put("slHoan", "SL Đơn hoàn");
        mapHeader.put("tlHoan", "Tỉ lệ Hoàn");
        mapHeader.put("slHuy", "SL Đơn hủy");
        mapHeader.put("slPTCDungGio", "SL PTC Đúng giờ");
        mapHeader.put("tlPTC", "Tỉ lệ PTC");
        mapHeader.put("tlPTCDungGio", "Tỉ lệ PTC Đúng giờ");
        mapHeader.put("tongTon", "Tổng tồn");

        List<String> header = Arrays.asList(
                "donVi",
                "slPhaiPhat",
                "slTonNhanBanGiao",
                "tt500",
                "ttKhac",
                "slPTC",
                "slHoan",
                "tlHoan",
                "slHuy",
                "slPTCDungGio",
                "tlPTC",
                "tlPTCDungGio",
                "tongTon"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                getDataExcelGrabPhat(vung, chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, luyke, buuTa, loaiDon, loaiBaoCao),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    public List<Map<String, Object>> tryTest(List<T> canIOTList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!canIOTList.isEmpty()) {
            int i = 0;
            for (T x : canIOTList) {
                BaoCaoGrabPhatResDTO x1 = (BaoCaoGrabPhatResDTO) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    //lấy data cho xuất excel Grab phát tồn
    public List<Map<String, Object>> getDataExcelPhatBill(String vungPhat, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String loaiDon, Integer type) throws IOException, IllegalAccessException {
        List<BaoCaoGrabPhatBillDTO> grabPhat = Collections.emptyList();
        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);

        //type = 1 : excel tồn phát
        //type = 2 : excel phát thành công
        List<String> listTrangThai = type == 1 ? trangThaiTon : trangThaiPTC;
        if (!isAdmin()) {
            List<String> listBC;
            List<String> listCN;

            if (!SSOChecking(2, chiNhanh, buuCuc)) {
                return tryTestBill((List<T>) grabPhat);
            }

            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");

            if (luyke == 0) {
                grabPhat = type == 1 ? bcGrabPhatBillRepo.findBaoCaoGrabPhatBill(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayBaoCao, listTrangThai) :
                        bcGrabPhatBillRepo.findBaoCaoGrabPhatTCBill(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayBaoCao, listTrangThai);
            } else if (luyke == 1) {
                grabPhat = type == 1 ? bcGrabPhatBillRepo.findBaoCaoGrabPhatBillLK(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayBaoCao, ngayDauThang, listTrangThai) :
                        bcGrabPhatBillRepo.findBaoCaoGrabPhatTCBillLK(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayBaoCao, ngayDauThang, listTrangThai);
            }
        } else {
            if (luyke == 0) {
                grabPhat = type == 1 ? bcGrabPhatBillRepo.findBaoCaoGrabPhatBill(chiNhanh, vungPhat, doiTac, buuCuc, dichVu, loaiDon, ngayBaoCao, listTrangThai) :
                        bcGrabPhatBillRepo.findBaoCaoGrabPhatTCBill(chiNhanh, vungPhat, doiTac, buuCuc, dichVu, loaiDon, ngayBaoCao, listTrangThai);
            } else {
                grabPhat = type == 1 ? bcGrabPhatBillRepo.findBaoCaoGrabPhatBillLK(chiNhanh, vungPhat, doiTac, buuCuc, dichVu, loaiDon, ngayBaoCao, ngayDauThang, listTrangThai) :
                        bcGrabPhatBillRepo.findBaoCaoGrabPhatTCBillLK(chiNhanh, vungPhat, doiTac, buuCuc, dichVu, loaiDon, ngayBaoCao, ngayDauThang, listTrangThai);
            }
        }
        return tryTestBill((List<T>) grabPhat);
    }

    public void exportExcelBillGrabPhat(HttpServletResponse response, String vung, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String loaiDon, String fileName)
            throws IllegalAccessException, IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("maPhieuGui", "Mã phiếu gửi");
        mapHeader.put("tinhNhan", "Tỉnh nhận");
        mapHeader.put("tinhPhat", "Tỉnh phát");
        mapHeader.put("huyenNhan", "Quận/Huyện nhận");
        mapHeader.put("huyenPhat", "Quận/Huyện phát");
        mapHeader.put("maBuuCucGoc", "Mã bưu cục gốc");
        mapHeader.put("maBuuCucHT", "Mã bưu cục hiện tại");
        mapHeader.put("timeYeuCauLayHang", "Thời gian yêu cầu lấy hàng");
        mapHeader.put("timeTaoDon", "Thời gian tạo đơn");
        mapHeader.put("timeNhapMay", "Thời gian nhập máy");
        mapHeader.put("timeQuyDinhPhat", "Thời gian quy định phát");
        mapHeader.put("timePhatTC", "Thời gian phát thành công");
        mapHeader.put("maTrangThai", "Mã trạng thái");
        mapHeader.put("maDoiTac", "Mã Đối tác");
        mapHeader.put("maKhachHang", "Mã khách hàng");
        mapHeader.put("maDichVu", "Mã dịch vụ");
        mapHeader.put("loaiDon", "Loại đơn");
        mapHeader.put("trongLuong", "Trọng lượng");
        mapHeader.put("loaiVung", "Loại vùng");
        mapHeader.put("timePhanCongPhat", "Thời gian Phân công phát");
        mapHeader.put("maVanDonGrab", "Mã vận đơn Grab");
        mapHeader.put("xaNhan", "Xã nhận");
        mapHeader.put("xaPhat", "Xã phát");
        mapHeader.put("soKm", "Số Km");
        mapHeader.put("requestTime", "Thời gian request tìm tài xế");
        mapHeader.put("tongCuoc", "Tổng cước (có VAT)");
        mapHeader.put("tongCOD", "Tiền COD");
        mapHeader.put("lyDoHuy", "Lý do hủy");
        mapHeader.put("trangThaiGrab", "Trạng thái Grab");
//        mapHeader.put("trangThaiVTP", "Trạng thái VTP");

        List<String> header = Arrays.asList(
                "maPhieuGui",
                "tinhNhan",
                "tinhPhat",
                "huyenNhan",
                "huyenPhat",
                "maBuuCucGoc",
                "maBuuCucHT",
                "timeYeuCauLayHang",
                "timeTaoDon",
                "timeNhapMay",
                "timeQuyDinhPhat",
                "timePhatTC",
                "maTrangThai",
                "maDoiTac",
                "maKhachHang",
                "maDichVu",
                "loaiDon",
                "trongLuong",
                "loaiVung",
                "timePhanCongPhat",
                "maVanDonGrab",
                "xaNhan",
                "xaPhat",
                "soKm",
                "requestTime",
                "tongCuoc",
                "tongCOD",
                "lyDoHuy",
                "trangThaiGrab"
//                "trangThaiVTP"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                getDataExcelPhatBill(vung, chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, luyke, loaiDon, fileName.equalsIgnoreCase("BCVH_Grab_Phat_ChiTiet") ? 1 : 2),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    public List<Map<String, Object>> tryTestBill(List<T> canIOTList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!canIOTList.isEmpty()) {
            int i = 0;
            for (T x : canIOTList) {
                BaoCaoGrabPhatBillDTO x1 = (BaoCaoGrabPhatBillDTO) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    //lấy list chi nhánh, bưu cục cần để query theo filter + SSO
    public Map<String, List<String>> checkListCNBC(String chiNhanh, String buuCuc) {
        List<String> listBC;
        List<String> listCN;

        if (chiNhanh.equals("")) {
            listCN = UserContext.getUserData().getListChiNhanhVeriable();
        } else {
            listCN = List.of(chiNhanh);
        }
        if (buuCuc.equals("")) {
            listBC = UserContext.getUserData().getListBuuCucVeriable();
        } else {
            listBC = List.of(buuCuc);
        }
        Map<String, List<String>> mapCNBC = new HashMap<>();
        mapCNBC.put("cn", listCN);
        mapCNBC.put("bc", listBC);
        return mapCNBC;
    }

    //check bưu cục, chi nhánh có nằm trong list mà gateway trả về hay không
    //nếu không đúng như list gateway trả về thì không ra số liệu
    private boolean retainBCCN(String chiNhanh, String buuCuc) {
        List<String> cn = new ArrayList<>(List.of(chiNhanh));
        List<String> bc = new ArrayList<>(List.of(buuCuc));
        if (!chiNhanh.equals("")) {
            cn.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
            if (cn.isEmpty()) {
                return false;
            }
        }
        if (!buuCuc.equals("")) {
            bc.retainAll(UserContext.getUserData().getListBuuCucVeriable());
            if (bc.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public List<String> allBuuTa(String chinhanh, String buuCuc, LocalDate ngayBaoCao, Integer luyKe) {
        LocalDate ngayDauThang = luyKe == 1 ? ngayBaoCao.withDayOfMonth(1) : ngayBaoCao;
        return bcGrabRepo.findDistinctBuuTa(ngayBaoCao, ngayDauThang, chinhanh, buuCuc);
    }
}
