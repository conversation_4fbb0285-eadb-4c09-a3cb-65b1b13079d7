package nocsystem.indexmanager.services.BaoCaoKhachHangLon.Grab;


import net.bytebuddy.asm.Advice;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuBillDTO;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab.BaoCaoGrabThuResDTO;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.Grab.BaoCaoGrabBillRepository;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.Grab.BaoCaoGrabRepository;
import nocsystem.indexmanager.services.DashBoardChiNhanh.ExportExcelTemplateCNBC;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class BaoCaoGrabThuService<T> {

    private static final Logger logger = LoggerFactory.getLogger(BaoCaoGrabThuService.class);

    private final BaoCaoGrabRepository bcGrabRepo;

    private final BaoCaoGrabBillRepository bcGrabPhatBillRepo;

    public BaoCaoGrabThuService(BaoCaoGrabRepository bcGrabRepo, BaoCaoGrabBillRepository bcGrabPhatBillRepo) {
        this.bcGrabRepo = bcGrabRepo;
        this.bcGrabPhatBillRepo = bcGrabPhatBillRepo;
    }

    public static final DecimalFormat df = new DecimalFormat("0.00");

    //check admin
    private boolean isAdmin() {
        return UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsViewAllBcTiktok().equalsIgnoreCase("true");
    }

    public String getTime(LocalDate ngayBaoCao) {
        // Null check for ngayBaoCao to prevent NullPointerException
        if (ngayBaoCao == null) {
            throw new IllegalArgumentException("ngayBaoCao cannot be null");
        }

        Page<String> time = bcGrabRepo.getTimeTinhToan(ngayBaoCao, Pageable.ofSize(1));
        String timeTinhToan = "";
        if (time.hasNext())
            timeTinhToan = time.getContent().get(0);

        return timeTinhToan;
    }


    //get data KPI phát
    public ListContentPageDto<BaoCaoGrabThuResDTO> findAllData(Integer luyKe, Integer loaiBaoCao, String vung, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao, Integer pageQuery, Integer pageSize) {
        Pageable page;
        page = PageRequest.of(pageQuery, pageSize);

        Page<BaoCaoGrabThuResDTO> pageResult = Page.empty(page);

        // Null check for ngayBaoCao to prevent NullPointerException
        if (ngayBaoCao == null) {
            throw new IllegalArgumentException("ngayBaoCao cannot be null");
        }

        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);

        if (!SSOChecking(loaiBaoCao, chiNhanh, buuCuc)) {
            return new ListContentPageDto<BaoCaoGrabThuResDTO>(Page.empty(page));
        }

        if (isAdmin()) {
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 1:
                        pageResult = bcGrabRepo.baoCaoGrabThuTCT(vung, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 2:
                        pageResult = bcGrabRepo.baoCaoGrabThuVung(vung, chiNhanh, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 3:
                        pageResult = bcGrabRepo.baoCaoGrabThuCN(chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 4:
                        pageResult = bcGrabRepo.baoCaoGrabThuDT(doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 5:
                        pageResult = bcGrabRepo.baoCaoGrabThuBC(chiNhanh, buuCuc, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 1:
                        pageResult = bcGrabRepo.baoCaoGrabThuTCTLK(vung, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 2:
                        pageResult = bcGrabRepo.baoCaoGrabThuVungLK(vung, chiNhanh, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 3:
                        pageResult = bcGrabRepo.baoCaoGrabThuCNLK(chiNhanh, buuCuc, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 4:
                        pageResult = bcGrabRepo.baoCaoGrabThuDTLK(doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    case 5:
                        pageResult = bcGrabRepo.baoCaoGrabThuBCLK(buuCuc, chiNhanh, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon, page);
                        break;
                    default:
                        break;
                }
            }
        } else {
            List<String> listBC;
            List<String> listCN;

            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 3:
                        pageResult = bcGrabRepo.baoCaoGrabThuCN(listCN, listBC, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    case 5:
                        pageResult = bcGrabRepo.baoCaoGrabThuBC(listCN, listBC, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon, page);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 3:
                        pageResult = bcGrabRepo.baoCaoGrabThuCNLK(listCN, listBC, doiTac, dichVu, ngayDauThang, ngayBaoCao,  loaiDon, page);
                        break;
                    case 5:
                        pageResult = bcGrabRepo.baoCaoGrabThuBCLK(listCN, listBC, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao,  loaiDon, page);
                        break;
                    default:
                        break;
                }
            }
        }

        for (BaoCaoGrabThuResDTO bc : pageResult) {
            ratioCalculation(bc);
        }
        ListContentPageDto<BaoCaoGrabThuResDTO> listContent = new ListContentPageDto<>(pageResult);
        listContent.setTime(getTime(ngayBaoCao));
        return listContent;
    }

    //tính tỷ lệ
    private void ratioCalculation(BaoCaoGrabThuResDTO bc) {

        //TTC = thu thành công
        //tỷ lệ TTC = sản lượng TTC / sản lượng phát sinh
        if (bc.getsL() == null || bc.getsL() == 0f) {
            bc.setTlThuTC(null);
        } else {
            bc.setTlThuTC(Float.valueOf(df.format(((float) bc.getSlThuTC() / (float) bc.getsL()) * 100)));
        }

        //tỷ lệ TTC đúng giờ = sản lượng TTC đúng giờ / sản lượng TTC
        if (bc.getSlThuTC() == null || bc.getSlThuTC() == 0f) {
            bc.setTlThuDG(null);
        } else {
            bc.setTlThuDG(Float.valueOf(df.format(((float) bc.getSlThuDG() / (float) bc.getSlThuTC()) * 100)));
        }


    }

    //check SSO
    private boolean SSOChecking(Integer loaiBaoCao, String chiNhanh, String buuCuc) {
        if (!isAdmin() && (loaiBaoCao == 1 || loaiBaoCao == 2 || loaiBaoCao == 4)) {
            return false;
        }
        if (!isAdmin()) {
            if (!chiNhanh.equals("")) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(chiNhanh)) {
                    return false;
                }
            }
            if (!buuCuc.equals("")) {
                if (!UserContext.getUserData().getListBuuCucVeriable().contains(buuCuc)) {
                    return false;
                }
            }
        }
        return true;
    }

    //tính sum và trả kết quả
    public List<BaoCaoGrabThuResDTO> baoCaoGrabPhatSum(Integer luyKe, Integer loaiBaoCao, String vung, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao) {
        List<BaoCaoGrabThuResDTO> bcKPI = responseCalculation(luyKe, loaiBaoCao, vung, chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
        Long slPhatSinh = 0L;
        Long slDropOff = 0L;
        Long slTTC = 0L;
        Long slTTCDungGio = 0L;
        Long tonConHan = 0L;
        Long tonQuaHan = 0L;
        Long slHuy = 0L;
        Long tongTon = 0L;

        for (BaoCaoGrabThuResDTO baocaoKPIRes : bcKPI) {
            slPhatSinh = baocaoKPIRes.getsL() != null ? slPhatSinh + baocaoKPIRes.getsL() : slPhatSinh;
            slDropOff = baocaoKPIRes.getDropOff() != null ? slDropOff + baocaoKPIRes.getDropOff() : slDropOff;
            slTTC = baocaoKPIRes.getSlThuTC() != null ? slTTC + baocaoKPIRes.getSlThuTC() : slTTC;
            tonConHan = baocaoKPIRes.getTonSLAConHan() != null ? tonConHan + baocaoKPIRes.getTonSLAConHan() : tonConHan;
            tonQuaHan = baocaoKPIRes.getTonSLAQuaHan() != null ? tonQuaHan + baocaoKPIRes.getTonSLAQuaHan() : tonQuaHan;
            slTTCDungGio = baocaoKPIRes.getSlThuDG() != null ? slTTCDungGio + baocaoKPIRes.getSlThuDG() : slTTCDungGio;
            slHuy = baocaoKPIRes.getHuy() != null ? slHuy + baocaoKPIRes.getHuy() : slHuy;
            tongTon = baocaoKPIRes.getTongTon() != null ? tongTon + baocaoKPIRes.getTongTon() : tongTon;
        }

        BaoCaoGrabThuResDTO result = new BaoCaoGrabThuResDTO();
        result.setsL(slPhatSinh);
        result.setSlThuTC(slTTC);
        result.setSlThuDG(slTTCDungGio);
        result.setDropOff(slDropOff);
        result.setTonSLAConHan(tonConHan);
        result.setTonSLAQuaHan(tonQuaHan);
        result.setHuy(slHuy);
        result.setTongTon(tongTon);

        ratioCalculation(result);

        List<BaoCaoGrabThuResDTO> khlGrab = new ArrayList<>();
        khlGrab.add(result);

        return khlGrab;
    }

    //get data cho việc tính sum
    public List<BaoCaoGrabThuResDTO> responseCalculation(Integer luyKe, Integer loaiBaoCao, String vung, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, String loaiDon, LocalDate ngayBaoCao) {
        List<BaoCaoGrabThuResDTO> result = new ArrayList<>();

        // Null check for ngayBaoCao to prevent NullPointerException
        if (ngayBaoCao == null) {
            throw new IllegalArgumentException("ngayBaoCao cannot be null");
        }

        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        if (isAdmin()) {
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 1:
                        result = bcGrabRepo.baoCaoGrabThuTCTSum(vung, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 2:
                        result = bcGrabRepo.baoCaoGrabThuVungSum(vung, chiNhanh, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 3:
                        result = bcGrabRepo.baoCaoGrabThuCNSum(chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 4:
                        result = bcGrabRepo.baoCaoGrabThuDTSum(doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 5:
                        result = bcGrabRepo.baoCaoGrabThuBCSum(chiNhanh, buuCuc, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 1:
                        result = bcGrabRepo.baoCaoGrabThuTCTLKSum(vung, doiTac, dichVu, ngayDauThang, ngayBaoCao , loaiDon);
                        break;
                    case 2:
                        result = bcGrabRepo.baoCaoGrabThuVungLKSum(vung, chiNhanh, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 3:
                        result = bcGrabRepo.baoCaoGrabThuCNLKSum(chiNhanh, buuCuc, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 4:
                        result = bcGrabRepo.baoCaoGrabThuDTLKSum(doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 5:
                        result = bcGrabRepo.baoCaoGrabThuBCLKSum(chiNhanh, buuCuc, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            }
        } else {
            List<String> listBC;
            List<String> listCN;

            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (luyKe == 0) {
                switch (loaiBaoCao) {
                    case 3:
                        result = bcGrabRepo.baoCaoGrabThuCNSum(listCN, listBC, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    case 5:
                        result = bcGrabRepo.baoCaoGrabThuBCSum(listCN, listBC, buuTa, doiTac, dichVu, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            } else {
                switch (loaiBaoCao) {
                    case 3:
                        result = bcGrabRepo.baoCaoGrabThuCNLKSum(listCN, listBC, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    case 5:
                        result = bcGrabRepo.baoCaoGrabThuBCLKSum(listCN, listBC, buuTa, doiTac, dichVu, ngayDauThang, ngayBaoCao, loaiDon);
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    //lấy data cho xuất excel KPI phát
    public List<Map<String, Object>> getDataExcelGrabThu(String vungPhat, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String buuTa, String loaiDon, Integer loaiBaoCao) throws IOException, IllegalAccessException, NoSuchFieldException {

        List<BaoCaoGrabThuResDTO> grabPhat = responseCalculation(luyke, loaiBaoCao, vungPhat, chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiDon, ngayBaoCao);
        for (BaoCaoGrabThuResDTO grab : grabPhat) {
            if(loaiBaoCao == 1) {
                String donVi = grab.getDonVi();
                grab.setDonVi("Vùng " + donVi);
            }
            ratioCalculation(grab);
            BaoCaoGrabThuResDTO newGrabObject = new BaoCaoGrabThuResDTO(grab.getDonVi(), grab.getsL(), grab.getDropOff(), grab.getSlThuTC(), grab.getSlThuDG(),
                     grab.getTlThuTC(), grab.getTlThuDG() , grab.getTonSLAConHan(), grab.getTonSLAQuaHan(), grab.getHuy(), grab.getTongTon());
            grabPhat.set(grabPhat.indexOf(grab), newGrabObject);
        }
        return tryTest((List<T>) grabPhat);
    }

    public void exportExcelGrabPhat(HttpServletResponse response, String vung, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String buuTa, String loaiDon, Integer loaiBaoCao, String fileName)
            throws IllegalAccessException, IOException, NoSuchFieldException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("donVi", "Đơn Vị");
        mapHeader.put("sL", "SL phát sinh");
        mapHeader.put("dropOff", "SL Drop Off");
        mapHeader.put("slThuTC", "SL Thu Thành công");
        mapHeader.put("slThuDG", "SL Thu Thành công Đúng giờ");
        mapHeader.put("tlThuTC", "Tỷ lệ Thu Thành công");
        mapHeader.put("tlThuDG", "Tỷ lệ Thu Thành công Đúng giờ");
        mapHeader.put("tonSLAConHan", "Tồn SLA Còn hạn");
        mapHeader.put("tonSLAQuaHan", "Tồn SLA Quá hạn");
        mapHeader.put("huy", "SL Đơn hủy");
        mapHeader.put("tongTon", "Tổng tồn");

        List<String> header = Arrays.asList(
                "donVi",
                "sL",
                "dropOff",
                "slThuTC",
                "slThuDG",
                "tlThuTC",
                "tlThuDG",
                "tonSLAConHan",
                "tonSLAQuaHan",
                "huy",
                "tongTon"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                getDataExcelGrabThu(vung, chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, luyke, buuTa, loaiDon, loaiBaoCao),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    public List<Map<String, Object>> tryTest(List<T> canIOTList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!canIOTList.isEmpty()) {
            int i = 0;
            for (T x : canIOTList) {
                BaoCaoGrabThuResDTO x1 = (BaoCaoGrabThuResDTO) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    //lấy data cho xuất excel Grab phát tồn
    public List<Map<String, Object>> getDataExcelThuBill(String vungPhat, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String loaiDon, Integer type) throws IOException, IllegalAccessException {
        List<BaoCaoGrabThuBillDTO> grabThu = Collections.emptyList();

        // Null check for ngayBaoCao to prevent NullPointerException
        if (ngayBaoCao == null) {
            throw new IllegalArgumentException("ngayBaoCao cannot be null");
        }

        //list thu hủy:
        //lấy các đơn có trạng thái hủy (107, -15)
        //có ngày phát sinh trong khoảng đầu tháng N-1 đến ngày báo cáo
        //có ngày hủy(cột time_hoan_huy) trong khoảng thời gian đc chọn
        List<BaoCaoGrabThuBillDTO> grabThuHuy = Collections.emptyList();

        //type = 1: đơn tồn
        //type = 2: sl phát sinh
        List<String> listTrangThai = type == 1 ? Arrays.asList("100", "102", "103", "104") : new ArrayList<>();
        List<String> listTrangThaiHuy = Arrays.asList("-15", "107");

        LocalDate ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        LocalDate ngayDauThangN1 = ngayDauThang.minusMonths(1);
        LocalDate ngayN1 = ngayBaoCao.minusDays(1);
        LocalDate ngayCuoiThangN1 = ngayDauThang.minusDays(1);

        List<String> listBC = new ArrayList<>();
        List<String> listCN = new ArrayList<>();

        if (!isAdmin()) {

            if (!SSOChecking(3, chiNhanh, buuCuc)) {
                return tryTestBill((List<T>) grabThu);
            }

            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");

            if (luyke == 0) {
                grabThu = bcGrabPhatBillRepo.findBaoCaoGrabThuBill(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayBaoCao, listTrangThai);
                if(type == 2)
                    grabThuHuy = bcGrabPhatBillRepo.findBaoCaoGrabThuBillHuy(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayN1, ngayBaoCao, ngayBaoCao, ngayDauThangN1, listTrangThaiHuy);
            } else if (luyke == 1) {
                grabThu = bcGrabPhatBillRepo.findBaoCaoGrabThuBillLK(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayBaoCao, ngayDauThang, listTrangThai);
                if(type == 2)
                    grabThuHuy = bcGrabPhatBillRepo.findBaoCaoGrabThuBillHuy(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayCuoiThangN1, ngayDauThang, ngayBaoCao, ngayDauThangN1, listTrangThaiHuy);
            }
        } else {
            if(!chiNhanh.equalsIgnoreCase(""))
                listCN.add(chiNhanh);
            if(!buuCuc.equalsIgnoreCase(""))
                listBC.add(buuCuc);

            if (luyke == 0) {
                grabThu = bcGrabPhatBillRepo.findBaoCaoGrabThuBill(chiNhanh, vungPhat, doiTac, buuCuc, dichVu, loaiDon, ngayBaoCao, listTrangThai);
                if(type == 2)
                    grabThuHuy = bcGrabPhatBillRepo.findBaoCaoGrabThuBillHuy(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayN1, ngayBaoCao, ngayBaoCao, ngayDauThangN1, listTrangThaiHuy);
            } else {
                grabThu = bcGrabPhatBillRepo.findBaoCaoGrabThuBillLK(chiNhanh, vungPhat, doiTac, buuCuc, dichVu, loaiDon, ngayBaoCao, ngayDauThang, listTrangThai);
                if(type == 2)
                    grabThuHuy = bcGrabPhatBillRepo.findBaoCaoGrabThuBillHuy(listCN, vungPhat, doiTac, listBC, dichVu, loaiDon, ngayCuoiThangN1, ngayDauThang, ngayBaoCao, ngayDauThangN1, listTrangThaiHuy);
            }
        }
        List<BaoCaoGrabThuBillDTO> finalList = Stream.concat(grabThu.stream(), grabThuHuy.stream())
                .collect(Collectors.toList());
        return tryTestBill((List<T>) finalList);
    }

    public void exportExcelBillGrabThu(HttpServletResponse response, String vung, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String buuTa, String loaiDon, String fileName)
            throws IllegalAccessException, IOException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=" + fileName + ".xlsx";
        response.setHeader(headerKey, headerValue);
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("maPhieuGui", "Mã phiếu gửi");
        mapHeader.put("tinhNhan", "Tỉnh nhận");
        mapHeader.put("tinhPhat", "Tỉnh phát");
        mapHeader.put("huyenNhan", "Quận/Huyện nhận");
        mapHeader.put("huyenPhat", "Quận/Huyện phát");
        mapHeader.put("maBuuCucGoc", "Mã bưu cục gốc");
        mapHeader.put("maBuuCucHT", "Mã bưu cục hiện tại");
        mapHeader.put("timeYeuCauLayHang", "Thời gian yêu cầu lấy hàng");
        mapHeader.put("timeTaoDon", "Thời gian tạo đơn");
        mapHeader.put("timeNhapMay", "Thời gian nhập máy");
        mapHeader.put("timeQuyDinhThu", "Thời gian quy định thu");
        mapHeader.put("maTrangThai", "Mã trạng thái");
        mapHeader.put("maDoiTac", "Mã Đối tác");
        mapHeader.put("maKhachHang", "Mã khách hàng");
        mapHeader.put("maDichVu", "Mã dịch vụ");
        mapHeader.put("loaiDon", "Loại đơn");
        mapHeader.put("trongLuong", "Trọng lượng");
        mapHeader.put("loaiVung", "Loại vùng");
        mapHeader.put("maVanDonGrab", "Mã vận đơn Grab");
        mapHeader.put("xaNhan", "Xã nhận");
        mapHeader.put("xaPhat", "Xã phát");
        mapHeader.put("soKm", "Số Km");
        mapHeader.put("requestTime", "Thời gian request tìm tài xế");
        mapHeader.put("tongCuoc", "Tổng cước (có VAT)");
        mapHeader.put("tongCOD", "Tiền COD");
        mapHeader.put("lyDoHuy", "Lý do hủy");
        mapHeader.put("trangThaiGrab", "Trạng thái Grab");
//        mapHeader.put("trangThaiVTP", "Trạng thái VTP");

        List<String> header = Arrays.asList(
                "maPhieuGui",
                "tinhNhan",
                "tinhPhat",
                "huyenNhan",
                "huyenPhat",
                "maBuuCucGoc",
                "maBuuCucHT",
                "timeYeuCauLayHang",
                "timeTaoDon",
                "timeNhapMay",
                "timeQuyDinhThu",
                "maTrangThai",
                "maDoiTac",
                "maKhachHang",
                "maDichVu",
                "loaiDon",
                "trongLuong",
                "loaiVung",
                "maVanDonGrab",
                "xaNhan",
                "xaPhat",
                "soKm",
                "requestTime",
                "tongCuoc",
                "tongCOD",
                "lyDoHuy",
                "trangThaiGrab"
//                "trangThaiVTP"
        );
        ExportExcelTemplateCNBC tonExcelTemplate = new ExportExcelTemplateCNBC(
                getDataExcelThuBill(vung, chiNhanh, buuCuc, doiTac, dichVu, ngayBaoCao, luyke, loaiDon, fileName.equalsIgnoreCase("BCVH_Grab_Thu_ChiTiet") ? 1 : 2),
                mapHeader,
                header,
                fileName
        );
        tonExcelTemplate.exportDataToExcel(response);
    }

    public List<Map<String, Object>> tryTestBill(List<T> canIOTList) throws IllegalAccessException {
        List<Map<String, Object>> totalRows = new ArrayList<>();
        if (!canIOTList.isEmpty()) {
            int i = 0;
            for (T x : canIOTList) {
                BaoCaoGrabThuBillDTO x1 = (BaoCaoGrabThuBillDTO) x;
                totalRows.add(x1.getObjectHashMap());
                i++;
            }
        }
        return totalRows;
    }

    //lấy list chi nhánh, bưu cục cần để query theo filter + SSO
    public Map<String, List<String>> checkListCNBC(String chiNhanh, String buuCuc) {
        List<String> listBC;
        List<String> listCN;

        if (chiNhanh.equals("")) {
            listCN = UserContext.getUserData().getListChiNhanhVeriable();
        } else {
            listCN = List.of(chiNhanh);
        }
        if (buuCuc.equals("")) {
            listBC = UserContext.getUserData().getListBuuCucVeriable();
        } else {
            listBC = List.of(buuCuc);
        }
        Map<String, List<String>> mapCNBC = new HashMap<>();
        mapCNBC.put("cn", listCN);
        mapCNBC.put("bc", listBC);
        return mapCNBC;
    }

    //check bưu cục, chi nhánh có nằm trong list mà gateway trả về hay không
    //nếu không đúng như list gateway trả về thì không ra số liệu
    private boolean retainBCCN(String chiNhanh, String buuCuc) {
        List<String> cn = new ArrayList<>(List.of(chiNhanh));
        List<String> bc = new ArrayList<>(List.of(buuCuc));
        if (!chiNhanh.equals("")) {
            cn.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
            if (cn.isEmpty()) {
                return false;
            }
        }
        if (!buuCuc.equals("")) {
            bc.retainAll(UserContext.getUserData().getListBuuCucVeriable());
            if (bc.isEmpty()) {
                return false;
            }
        }
        return true;
    }
}
