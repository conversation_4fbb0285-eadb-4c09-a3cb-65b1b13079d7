package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanh;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * Service Interface for managing {@link nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanh}.
 */

public interface DMChiNhanhService {
    DMChiNhanh save(DMChiNhanh dmChiNhanh);
    Page<DMChiNhanh> findAll(Pageable pageable);
}
