package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanhVung;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanh}.
 */

public interface DMChiNhanhVungService {
    DMChiNhanhVung save(DMChiNhanhVung dmChiNhanhVung);
    Page<DMChiNhanhVung> findAll(Pageable pageable);
}
