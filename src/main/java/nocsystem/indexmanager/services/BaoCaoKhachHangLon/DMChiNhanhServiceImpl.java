package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanh;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.DMChiNhanhRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * Service Interface for managing {@link nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMChiNhanh}.
 */
@Service
public class DMChiNhanhServiceImpl implements DMChiNhanhService {
    private final Logger log = LoggerFactory.getLogger(DMChiNhanhServiceImpl.class);
    private final DMChiNhanhRepository dmChiNhanhRepository;

    public DMChiNhanhServiceImpl(DMChiNhanhRepository dmChiNhanhRepository) {
        this.dmChiNhanhRepository = dmChiNhanhRepository;
    }

    @Override
    public DMChiNhanh save(DMChiNhanh dmChiNhanh) {
        log.debug("Request to save DMChiNhanh : {}", dmChiNhanh);
        DMChiNhanh dmChiNhanh1 = dmChiNhanhRepository.save(dmChiNhanh);
        return dmChiNhanh1;
    }

    @Override
    public Page<DMChiNhanh> findAll(Pageable pageable) {
        log.debug("Request to get all DMChiNhanh");
        return dmChiNhanhRepository.findAll(pageable);
    }
}
