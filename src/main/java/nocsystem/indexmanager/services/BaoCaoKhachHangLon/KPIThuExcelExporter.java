package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.BaoCaoKPIThuResDTO2;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public class KPIThuExcelExporter {

    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    private List<BaoCaoKPIThuResDTO2> KPIPhat;

    public KPIThuExcelExporter(List<BaoCaoKPIThuResDTO2> KPIPhat) {
        this.workbook = workbook = new SXSSFWorkbook();
        this.KPIPhat = KPIPhat;
    }

    private void createCell(Row row, int columnCount, Object value, CellStyle style) {
        //sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRow() {
        sheet = workbook.createSheet("KPI Thu");
//        sheet.setColumnWidth();
        Row row;
        CellStyle style = workbook.createCellStyle();
//        Font font = workbook.createFont();
//        font.setBold(true);
//        font.setFontHeight((short)20);
//        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
//        createCell(row, 0, "Tinh Information", style);
//        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));
//        font.setFontHeightInPoints((short) 10);
//        font.setBold(true);
//        font.setFontHeight((short)16);
//        style.setFont(font);
        row = sheet.createRow(0);


        createCell(row, 0, "Đơn vị", style);
        createCell(row, 1, "SL phát sinh", style);
        createCell(row, 2, "SL Dropoff", style);
        createCell(row, 3, "SL Hủy", style);
        createCell(row, 4, "SL Thu thành công", style);
        createCell(row, 5, "SL Thu đúng giờ", style);
        createCell(row, 6, "SL Thu đúng giờ lần 1", style);
        createCell(row, 7, "Tỷ lệ Thu đúng giờ", style);
        createCell(row, 8, "Tỷ lệ Thu đúng giờ lần 1", style);
        createCell(row, 9, "Tồn SLA", style);
        createCell(row, 10, "", style);
        createCell(row, 11, "Tổng tồn", style);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 9, 10));
        for (int i = 1; i <= row.getLastCellNum()-1; i++) {
            sheet.trackAllColumnsForAutoSizing();
            sheet.autoSizeColumn(i);
        }
        Row row1 = sheet.createRow(1);

        createCell(row1, 0, "Đơn vị", style);
        createCell(row1, 1, "", style);
        createCell(row1, 2, "", style);
        createCell(row1, 3, "", style);
        createCell(row1, 4, "", style);
        createCell(row1, 5, "", style);
        createCell(row1, 6, "", style);
        createCell(row1, 7, "", style);
        createCell(row1, 8, "", style);
        createCell(row1, 9, "Còn Hạn", style);
        createCell(row1, 10, "Quá hạn", style);
        createCell(row1, 11, "", style);
        for (int i = 0; i <= 8; i++) {
            sheet.addMergedRegion(new CellRangeAddress(0, 1, i, i));
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 11, 11));
        long start = System.nanoTime();


        long end = System.nanoTime();
        System.out.println("write" + (end - start));
//        sheet.setColumnWidth(0, 8000);
    }

    private void writeCustomData() throws IOException {
        int rowCount = 2;
        CellStyle style = workbook.createCellStyle();
//        Font font = workbook.createFont();
//        CreationHelper createHelper = workbook.getCreationHelper();
//        font.setFontHeight((short) 14);
//        style.setFont(font);

        DecimalFormat df = new DecimalFormat("0.00");

//        CellStyle dateStyle = workbook.createCellStyle();
//        dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("dd-mm-yyyy"));
//        dateStyle.setFont(font);
        long start = System.nanoTime();

        Row row;
        int columnCount = 0;

        for (BaoCaoKPIThuResDTO2 kpiPhat : KPIPhat) {
            row = sheet.createRow(rowCount++);
            columnCount = 0;
//            String TlThuTC = String.valueOf(kpiPhat.getTlThuTC());
            String TlThuDG = String.valueOf(kpiPhat.getTlThuDG());
            String TlThuDG1 = String.valueOf(kpiPhat.getTlThuDG1());
//            if(kpiPhat.getTlThuTC() != null){
//                TlThuTC = df.format(kpiPhat.getTlThuTC());
//            }
            if (kpiPhat.getTlThuDG() != null) {
                TlThuDG = df.format(kpiPhat.getTlThuDG());
            }
            if (kpiPhat.getTlThuDG1() != null) {
                TlThuDG1 = df.format(kpiPhat.getTlThuDG1());
            }

            createCell(row, columnCount++, kpiPhat.getDonVi(), style);
            createCell(row, columnCount++, kpiPhat.getsL(), style);
            createCell(row, columnCount++, kpiPhat.getDropOff(), style);
            createCell(row, columnCount++, kpiPhat.getSlThuTC(), style);
            createCell(row, columnCount++, kpiPhat.getHuy(), style);
            createCell(row, columnCount++, kpiPhat.getSlThuDG(), style);
            createCell(row, columnCount++, kpiPhat.getSlThuDG1(), style);
            createCell(row, columnCount++, TlThuDG, style);
            createCell(row, columnCount++, TlThuDG1, style);
            createCell(row, columnCount++, kpiPhat.getTonSLAQuaHan(), style);
            createCell(row, columnCount++, kpiPhat.getTonSLAConHan(), style);
            createCell(row, columnCount++, kpiPhat.getTongTon(), style);
        }
        long end = System.nanoTime();
        System.out.print("write: " + (end - start));
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
        createHeaderRow();
        writeCustomData();
        ServletOutputStream outputStream = response.getOutputStream();
        try{
            workbook.write(outputStream);
            workbook.dispose();
        } finally {
            workbook.close();
            outputStream.close();
        }
        outputStream.close();
    }
}
