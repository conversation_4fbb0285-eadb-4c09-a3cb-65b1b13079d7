package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.models.BaoCaoKhachHangLon.DMBuuCuc;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link DMBuuCuc}.
 */

public interface DMBuuCucService {
    DMBuuCuc save(DMBuuCuc dmBuuCuc);
    Page<DMBuuCuc> findAll(Pageable pageable);
}
