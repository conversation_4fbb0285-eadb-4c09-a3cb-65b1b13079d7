package nocsystem.indexmanager.services.BaoCaoKhachHangLon;


import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.*;
import nocsystem.indexmanager.models.Response.LOG.TonChuaKetNoiResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.*;
import org.apache.catalina.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static nocsystem.indexmanager.services.BaoCaoKhachHangLon.BaoCaoKPIThuService.calculateTyLeTon;

@Service
public class BaoCaoKPIPhatService implements KpiPhatTikTokService {

    private static final Logger logger = LoggerFactory.getLogger(BaoCaoKPIPhatService.class);

    private final BaoCaoKPIPhatRepository bcKPIPhatRepo;

    private final BaoCaoKPIPhatBillRepository bcKPIPhatBillRepo;

    public BaoCaoKPIPhatService(BaoCaoKPIPhatRepository bcKPIPhatRepo, BaoCaoKPIPhatBillRepository bcKPIPhatBillRepo) {
        this.bcKPIPhatRepo = bcKPIPhatRepo;
        this.bcKPIPhatBillRepo = bcKPIPhatBillRepo;
    }

    //check admin
    private boolean isAdmin() {
        return UserContext.getUserData().getIsViewAllBcTiktok().equalsIgnoreCase("true");
    }

    public String getTime(LocalDate ngayBaoCao){
        Page<String> time = bcKPIPhatRepo.getTimeTinhToan(ngayBaoCao, Pageable.ofSize(1));
        String timeTinhToan = "";
        if (time.hasNext())
            timeTinhToan = time.getContent().get(0);

        return timeTinhToan;
    }


    //get data KPI phát
    public ListContentPageDto<BaoCaoKPIPhatResDTO2> findAllData(Integer luyKe, Integer loaiBaoCao, String vung, String vungCon, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer pageQuery, Integer pageSize, String sort, String sortBy) {
        Pageable page;
        page = PageRequest.of(pageQuery, pageSize);

        List<BaoCaoKPIPhatResDTO2> pageResult = new ArrayList<>();
        List<BaoCaoKPIPhatResDTO2> pageResult2 = new ArrayList<>();

//        List<BaoCaoKPIPhatResDTO2> pageResultTlHoan = new ArrayList<>();

        List<String> listDoiTacBank = Arrays.asList("ATM", "HDB", "RVCB", "VT01", "TPBANK", "TIMO", "HRV");
        List<String> listVung = Arrays.asList("HCM", "HNI");

        HashMap<String, List<Long>> data0LK = new HashMap<>();
        LocalDate ngayDauThang = LocalDate.now();
        if (luyKe == 1){
            ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        }else if (luyKe == 2){
            ngayDauThang = LocalDate.from(ngayBaoCao).plusDays(6);
        }


        Integer statusMoiNhat = 1;

        if (!SSOChecking(loaiBaoCao, chiNhanh, buuCuc)) {
            return new ListContentPageDto<>();
        }

        // Loại báo cáo: 0 -> TCT, 1 -> vùng, 2 -> chi nhánh, 3 -> đối tác, 4 -> bưu cục
        if (isAdmin()) {
            if (luyKe == 0) {
                if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                    switch (loaiBaoCao) {
                        case 0:
                            //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                            if (listVung.contains(vung)) {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTBankVaVungCon(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            } else {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTBank(vung, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            }
                            break;
                        case 1:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatVungBank(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 2:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCNBank(chiNhanh, buuCuc,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 3:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatDTBank(doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBCBank(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else if (doiTac.equals("SHOPEE")) {
                    switch (loaiBaoCao) {
                        case 0:
                            //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                            if (listVung.contains(vung)) {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTShopeeVaVungCon(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            } else {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTShopee(vung, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            }
                            break;
                        case 1:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatVungShopee(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 2:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCNShopee(chiNhanh, buuCuc, vungCon ,doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 3:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatDTShopee(doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBCShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (loaiBaoCao) {
                        case 0:
                            //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                            if (listVung.contains(vung)) {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTVaVungCon(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            } else {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCT(vung, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            }
                            break;
                        case 1:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatVung(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 2:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCN(chiNhanh, buuCuc,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 3:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatDT(doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBC(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                switch (loaiBaoCao) {
                    case 0:
                        //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                        if (listVung.contains(vung)) {
                            if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLKBankVaVungCon(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                            } else if (doiTac.equals("SHOPEE")) {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLKShopeeVaVungCon(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                            } else {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLKVaVungCon(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                            }
                            data0LK = toMap(pageResult);
                            pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLKInListVaVungCon(new ArrayList<>(data0LK.keySet()), vungCon, doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        } else {
                            if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLKBank(vung, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                            } else if (doiTac.equals("SHOPEE")) {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLKShopee(vung, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                            } else {
                                pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLK(vung, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                            }
                            data0LK = toMap(pageResult);
                            pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatTCTLKInList(new ArrayList<>(data0LK.keySet()), doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        }
                        break;
                    case 1:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatVungLKBank(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatVungLKShopee(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatVungLK(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        data0LK = toMap(pageResult);
                        pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatVungLKInList( new ArrayList<>(data0LK.keySet()), doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 2:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCNLKBank(chiNhanh, buuCuc, vungCon,doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCNLKShopee(chiNhanh, buuCuc, vungCon ,doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCNLK(chiNhanh, buuCuc, vungCon,doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        data0LK = toMap(pageResult);
                        pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatCNLKInList(chiNhanh, new ArrayList<>(data0LK.keySet()),vungCon, doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 3:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatDTLKBank(doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatDTLKShopee(doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatDTLK(doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        data0LK = toMap(pageResult);
                        pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatDTLKInList(new ArrayList<>(data0LK.keySet()), dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 4:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBCLKBank(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBCLKShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBCLK(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        data0LK = toMap(pageResult);
                        pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatBCLKInList(chiNhanh, buuCuc, new ArrayList<>(data0LK.keySet()), doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    default:
                        break;
                }
            }
        } else {
            List<String> listBC;
            List<String> listCN;

            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (luyKe == 0) {
                if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                    switch (loaiBaoCao) {
                        case 2:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCN1Bank(listCN, listBC,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBC1Bank(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else if (doiTac.equals("SHOPEE")) {
                    switch (loaiBaoCao) {
                        case 2:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCN1Shopee(listCN, listBC,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBC1Shopee(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (loaiBaoCao) {
                        case 2:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCN1(listCN, listBC,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBC1(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                switch (loaiBaoCao) {
                    case 2:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCN1LKBank(listCN, listBC,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCN1LKShopee(listCN, listBC, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatCN1LK(listCN, listBC,vungCon,  doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        data0LK = toMap(pageResult);
                        pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatCN1LKNew(listCN, new ArrayList<>(data0LK.keySet()),vungCon,  doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 4:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBC1LKBank(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBC1LKShopee(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            pageResult = bcKPIPhatRepo.findBaoCaoKPIPhatBC1LK(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        data0LK = toMap(pageResult);
                        pageResult2 = bcKPIPhatRepo.findBaoCaoKPIPhatBC1LKInList(listCN, listBC, new ArrayList<>(data0LK.keySet()), doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    default:
                        break;
                }
            }
        }

        //đẩy các data theo lũy kế vào map
        HashMap<String, BaoCaoKPIPhatResDTO2> mapResultLuyKe = new HashMap<>();

        //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
        if (loaiBaoCao == 0 && listVung.contains(vung)) {
            for(BaoCaoKPIPhatResDTO2 result : pageResult2){
                mapResultLuyKe.put(result.getVungCon(), result);
            }
        } else {
            for(BaoCaoKPIPhatResDTO2 result : pageResult2){
                mapResultLuyKe.put(result.getDonVi(), result);
            }
        }

        //set các data lấy theo lũy kế lấy từ map
        for (BaoCaoKPIPhatResDTO2 bc : pageResult) {
            if(luyKe == 1 || luyKe == 2) {
                if (loaiBaoCao == 0 && listVung.contains(vung)) {
                    if (mapResultLuyKe.containsKey(bc.getVungCon())) {
                        bc.setSlPhaiPhat(mapResultLuyKe.get(bc.getVungCon()).getSlPhaiPhat());
                        bc.setTt500(mapResultLuyKe.get(bc.getVungCon()).getTt500());
                        bc.setTtKhac(mapResultLuyKe.get(bc.getVungCon()).getTtKhac());
                        bc.setTongTon(mapResultLuyKe.get(bc.getVungCon()).getTongTon());
                    } else {
                        bc.setSlPhaiPhat(0L);
                        bc.setTt500(0L);
                        bc.setTtKhac(0L);
                        bc.setTongTon(0L);
                    }
                } else {
                    if (mapResultLuyKe.containsKey(bc.getDonVi())) {
                        bc.setSlPhaiPhat(mapResultLuyKe.get(bc.getDonVi()).getSlPhaiPhat());
                        bc.setTt500(mapResultLuyKe.get(bc.getDonVi()).getTt500());
                        bc.setTtKhac(mapResultLuyKe.get(bc.getDonVi()).getTtKhac());
                        bc.setTongTon(mapResultLuyKe.get(bc.getDonVi()).getTongTon());
                    } else {
                        bc.setSlPhaiPhat(0L);
                        bc.setTt500(0L);
                        bc.setTtKhac(0L);
                        bc.setTongTon(0L);
                    }
                }
            }
            //tính tỷ lệ
            ratioCalculation(luyKe,bc);
        }

        //sort theo cột
        if (!sort.equals(""))
            switch (sortBy) {
                case "slPhaiPhat":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPhaiPhat, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPhaiPhat, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slChuaPCP":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlChuaPCP, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlChuaPCP, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tt500":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTt500, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTt500, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "ttKhac":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTtKhac, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTtKhac, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slPTC":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPTC, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPTC, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slHoan":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlHoan, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlHoan, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slHuy":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlHuy, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlHuy, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlHoan":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlHoan, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlHoan, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slPTCDungGio":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPTCDungGio, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPTCDungGio, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "slPTCDungGioLanDau":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPTCDungGioLanDau, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getSlPTCDungGioLanDau, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlPTCDungGio":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlPTCDungGio, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlPTCDungGio, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlPTCDungGioLanDau":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlPTCDungGioLanDau, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlPTCDungGioLanDau, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tongTon":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTongTon, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTongTon, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlGiaoHangDungHen":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlGiaoHangDungHen, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlGiaoHangDungHen, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlGiaoThanhCongLanDau":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlGiaoThanhCongLanDau, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlGiaoThanhCongLanDau, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                case "tlGiaoThanhCongLanDauLK":
                    if (sort.equalsIgnoreCase("asc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlGiaoThanhCongLanDauLK, Comparator.nullsFirst(Comparator.naturalOrder())));
                    if (sort.equalsIgnoreCase("desc"))
                        Collections.sort(pageResult, comparing(BaoCaoKPIPhatResDTO2::getTlGiaoThanhCongLanDauLK, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
                    break;
                default:
                    break;
            }

        //sublist lấy số lượng theo pageSize
        int start = (int) page.getOffset();
        int end = Math.min((start + page.getPageSize()), pageResult.size());
        if(start > pageResult.size())
            return new ListContentPageDto<>();
        List<BaoCaoKPIPhatResDTO2> pageData = pageResult.subList(start, end);

        //tạo page từ list
        Page<BaoCaoKPIPhatResDTO2> finalPage = new PageImpl<>(pageData, page, pageResult.size());


        ListContentPageDto<BaoCaoKPIPhatResDTO2> listContent = new ListContentPageDto<>(finalPage);
        listContent.setTime(getTime(ngayBaoCao));
        return listContent;
    }

    private List<BaoCaoKPIPhatResDTO2> addMoreColumn(List<BaoCaoKPIPhatResDTO2> pageResult, List<BaoCaoKPIPhatResDTO2> pageResultTlHoan) {
        for (int i = 0; i < pageResult.size(); i++) {
            for (int j = 0; j < pageResultTlHoan.size(); j++) {
                if (pageResultTlHoan.get(j).getDonVi().equals(pageResult.get(i).getDonVi())) {
                    pageResult.get(i).setTuHoan(pageResultTlHoan.get(j).getTuHoan());
                    pageResult.get(i).setMauHoan(pageResultTlHoan.get(j).getMauHoan());
                    j = pageResultTlHoan.size();
                }
            }
        }
        return pageResult;
    }

    //tạo map chứa data lấy theo ngày (trạng thái 500, tổng tồn, trạng thái khác, sản lượng phải phát)
    HashMap<String, List<Long>> toMap(List<BaoCaoKPIPhatResDTO2> page) {
        HashMap<String, List<Long>> data0LK = new HashMap<>();
        for (BaoCaoKPIPhatResDTO2 bc : page) {
            data0LK.put(bc.getDonVi(), Arrays.asList(bc.getTt500(), bc.getTongTon(), bc.getTtKhac(), bc.getSlPhaiPhat()));
        }
        return data0LK;
    }

    //tính tỷ lệ
    private void ratioCalculation(int isLuyKe,BaoCaoKPIPhatResDTO2 bc){
        if (bc.getSlPTC() != null) {
            if (bc.getSlPTC() == 0f) {
                bc.setTlPTCDungGioLanDau(null);
                bc.setTlPTCDungGio(null);
            } else {
                bc.setTlPTCDungGioLanDau(((float) bc.getSlPTCDungGioLanDau() / (float) bc.getSlPTC()) * 100);
                bc.setTlPTCDungGio(((float) bc.getSlPTCDungGio() / (float) bc.getSlPTC()) * 100);
            }
        } else {
            bc.setTlPTCDungGioLanDau(null);
            bc.setTlPTCDungGio(null);
        }

//        //Tỷ lệ hoàn (7) = SL Hoàn (6) / [SL PTC (5)+ SL Hoàn (6)]
//        Long hoan = bc.getSlHoan() + bc.getSlPTC();
//        if (hoan == 0) {
//            bc.setTlHoan(null);
//        } else {
//            bc.setTlPTC(((float) bc.getSlHoan() / hoan) * 100);
//        }

        //Công thức tính tỷ lệ hoàn mới
        //Tỷ lệ hoàn = Tử Hoàn / Mẫu Hoàn
        if (isLuyKe == 0){
            if (bc.getTuHoan() != null && bc.getMauHoan() != null) {
                if (bc.getMauHoan() == 0L) {
                    bc.setTlHoan(null);
                } else {
                    bc.setTlHoan(((float) bc.getTuHoan() / bc.getMauHoan()) * 100);
                }
            }
        }else {
            if (bc.getTuHoanLk() != null && bc.getMauHoanLk() != null) {
                if (bc.getMauHoanLk() == 0L) {
                    bc.setTlHoan(null);
                } else {
                    bc.setTlHoan(((float) bc.getTuHoanLk() / bc.getMauHoanLk()) * 100);
                }
            }
        }

        if (bc.getMauGiaoHangDungHen() != null && bc.getMauGiaoHangDungHen() != 0L) {
            bc.setTlGiaoHangDungHen(((float) bc.getTuGiaoHangDungHen() / (float) bc.getMauGiaoHangDungHen()) * 100);
        }
        if (bc.getMauGiaoThanhCongLanDau() != null && bc.getMauGiaoThanhCongLanDau() != 0L) {
            bc.setTlGiaoThanhCongLanDau(((float) bc.getTuGiaoThanhCongLanDau() / (float) bc.getMauGiaoThanhCongLanDau()) * 100);
        }

        if (bc.getMauGiaoThanhCongLanDauLK() != null && bc.getMauGiaoThanhCongLanDauLK() != 0L) {
            bc.setTlGiaoThanhCongLanDauLK(((float) bc.getTuGiaoThanhCongLanDauLK() / (float) bc.getMauGiaoThanhCongLanDauLK()) * 100);
        }


        //fix tạm số lượng phải phát = sl phát thành công + sl hoàn + sl hủy
//        bc.setSlPhaiPhat(bc.getSlPTC() + bc.getSlHoan() + bc.getSlHuy());
    }

    //check SSO
    private boolean SSOChecking(Integer loaiBaoCao, String chiNhanh, String buuCuc) {
        if (!isAdmin() && (loaiBaoCao == 0 || loaiBaoCao == 1 || loaiBaoCao == 3)) {
            return false;
        }
        if (!isAdmin()) {
            if (!chiNhanh.equals("")) {
                if (!UserContext.getUserData().getListChiNhanhVeriable().contains(chiNhanh)) {
                    return false;
                }
            }
            if (!buuCuc.equals("")) {
                if (!UserContext.getUserData().getListBuuCucVeriable().contains(buuCuc)) {
                    return false;
                }
            }
        }
        return true;
    }

    //tính sum và trả kết quả
    public List<BaoCaoKPIPhatResDTO2> baoCaoKPIPhatSum(Integer luyKe, Integer loaiBaoCao, String vung, String vungCon, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, LocalDate ngayBaoCao) {
        List<BaoCaoKPIPhatResDTO2> bcKPI = responseCalculation(luyKe, loaiBaoCao, vung, vungCon, chiNhanh, buuCuc, buuTa, doiTac, dichVu, ngayBaoCao);
        Long slPhaiPhat = 0L;
        Long slChuaPCP = 0L;
        Long slPTC = 0L;
        Long tongSL = 0L;
        Long slPTCDungGio = 0L;
        Long slPTCDungGioLanDau = 0L;
        Long tonXanh = 0L;
        Long tonVang = 0L;
        Long tonDo1 = 0L;
        Long tonDo2 = 0L;
        Long tonDo3 = 0L;
        Long tonDo4 = 0L;
        Long tonDo5 = 0L;
        Long tonDo6 = 0L;
        Long tonDo7 = 0L;
        Long tonDo8 = 0L;
        Long tonDo9 = 0L;
        Long tongTon = 0L;
        Long tt500 = 0L;
        Long ttKhac = 0L;
        Long slHuy = 0L;
        Long slHoan = 0L;

        Long tuHoan = 0l;
        Long mauHoan = 0L;
        Long tuHoanLk = 0l;
        Long mauHoanLk = 0L;

        Long tuGiaoHangDungHen = 0L;
        Long mauGiaoHangDungHen = 0L;
        Long tuGiaoThanhCongLanDau = 0L;
        Long mauGiaoThanhCongLanDau = 0L;
        Long tuGiaoThanhCongLanDauLK = 0L;
        Long mauGiaoThanhCongLanDauLK = 0L;


        for (BaoCaoKPIPhatResDTO2 baocaoKPIRes : bcKPI) {
            slPhaiPhat = baocaoKPIRes.getSlPhaiPhat() != null ? slPhaiPhat + baocaoKPIRes.getSlPhaiPhat() : slPhaiPhat;
            slChuaPCP = baocaoKPIRes.getSlChuaPCP() != null ? slChuaPCP + baocaoKPIRes.getSlChuaPCP() : slChuaPCP;
            slPTC = baocaoKPIRes.getSlPTC() != null ? slPTC + baocaoKPIRes.getSlPTC() : slPTC;
            tongTon = baocaoKPIRes.getTongTon() != null ? tongTon + baocaoKPIRes.getTongTon() : tongTon;
            tt500 = baocaoKPIRes.getTt500() != null ? tt500 + baocaoKPIRes.getTt500() : tt500;
            ttKhac = baocaoKPIRes.getTtKhac() != null ? ttKhac + baocaoKPIRes.getTtKhac() : ttKhac;
            slPTCDungGio = baocaoKPIRes.getSlPTCDungGio() != null ? slPTCDungGio + baocaoKPIRes.getSlPTCDungGio() : slPTCDungGio;
            slPTCDungGioLanDau = baocaoKPIRes.getSlPTCDungGioLanDau() != null ? slPTCDungGioLanDau + baocaoKPIRes.getSlPTCDungGioLanDau() : slPTCDungGioLanDau;
            slHuy = baocaoKPIRes.getSlHuy() != null ? slHuy + baocaoKPIRes.getSlHuy() : slHuy;
            slHoan = baocaoKPIRes.getSlHoan() != null ? slHoan + baocaoKPIRes.getSlHoan() : slHoan;

            tuHoan = baocaoKPIRes.getTuHoan() != null ? tuHoan + baocaoKPIRes.getTuHoan() : tuHoan;
            mauHoan = baocaoKPIRes.getMauHoan() != null ? mauHoan + baocaoKPIRes.getMauHoan() : mauHoan;

            tuHoanLk = baocaoKPIRes.getTuHoanLk() != null ? tuHoanLk + baocaoKPIRes.getTuHoanLk() : tuHoanLk;
            mauHoanLk = baocaoKPIRes.getMauHoanLk() != null ? mauHoanLk + baocaoKPIRes.getMauHoanLk() : mauHoanLk;

            tuGiaoHangDungHen = baocaoKPIRes.getTuGiaoHangDungHen() != null ? tuGiaoHangDungHen + baocaoKPIRes.getTuGiaoHangDungHen() : tuGiaoHangDungHen;
            mauGiaoHangDungHen = baocaoKPIRes.getMauGiaoHangDungHen() != null ? mauGiaoHangDungHen + baocaoKPIRes.getMauGiaoHangDungHen() : mauGiaoHangDungHen;
            tuGiaoThanhCongLanDau = baocaoKPIRes.getTuGiaoThanhCongLanDau() != null ? tuGiaoThanhCongLanDau + baocaoKPIRes.getTuGiaoThanhCongLanDau() : tuGiaoThanhCongLanDau;
            mauGiaoThanhCongLanDau = baocaoKPIRes.getMauGiaoThanhCongLanDau() != null ? mauGiaoThanhCongLanDau + baocaoKPIRes.getMauGiaoThanhCongLanDau() : mauGiaoThanhCongLanDau;
            tuGiaoThanhCongLanDauLK = baocaoKPIRes.getTuGiaoThanhCongLanDauLK() != null ? tuGiaoThanhCongLanDauLK + baocaoKPIRes.getTuGiaoThanhCongLanDauLK() : tuGiaoThanhCongLanDauLK;
            mauGiaoThanhCongLanDauLK = baocaoKPIRes.getMauGiaoThanhCongLanDauLK() != null ? mauGiaoThanhCongLanDauLK + baocaoKPIRes.getMauGiaoThanhCongLanDauLK() : mauGiaoThanhCongLanDauLK;
        }

        BaoCaoKPIPhatResDTO2 bcKPIPhat = new BaoCaoKPIPhatResDTO2();
        bcKPIPhat.setTongSanLuong(tongSL);
        bcKPIPhat.setSlPhaiPhat(slPhaiPhat);
        bcKPIPhat.setSlChuaPCP(slChuaPCP);
        bcKPIPhat.setSlPTC(slPTC);
        bcKPIPhat.setSlPTCDungGio(slPTCDungGio);
        bcKPIPhat.setSlPTCDungGioLanDau(slPTCDungGioLanDau);
        bcKPIPhat.setTonXanh(tonXanh);
        bcKPIPhat.setTonVang(tonVang);
        bcKPIPhat.setTonDo1(tonDo1);
        bcKPIPhat.setTonDo2(tonDo2);
        bcKPIPhat.setTonDo3(tonDo3);
        bcKPIPhat.setTonDo4(tonDo4);
        bcKPIPhat.setTonDo5(tonDo5);
        bcKPIPhat.setTonDo6(tonDo6);
        bcKPIPhat.setTonDo7(tonDo7);
        bcKPIPhat.setTonDo8(tonDo8);
        bcKPIPhat.setTonDo9(tonDo9);
        bcKPIPhat.setTongTon(tongTon);
        bcKPIPhat.setTt500(tt500);
        bcKPIPhat.setTtKhac(ttKhac);
        bcKPIPhat.setSlHuy(slHuy);
        bcKPIPhat.setSlHoan(slHoan);
        bcKPIPhat.setTuHoanLk(tuHoanLk);
        bcKPIPhat.setMauHoanLk(mauHoanLk);
        bcKPIPhat.setTuHoan(tuHoan);
        bcKPIPhat.setMauHoan(mauHoan);
        bcKPIPhat.setTuGiaoHangDungHen(tuGiaoHangDungHen);
        bcKPIPhat.setMauGiaoHangDungHen(mauGiaoHangDungHen);
        bcKPIPhat.setTuGiaoThanhCongLanDau(tuGiaoThanhCongLanDau);
        bcKPIPhat.setMauGiaoThanhCongLanDau(mauGiaoThanhCongLanDau);
        bcKPIPhat.setTuGiaoThanhCongLanDauLK(tuGiaoThanhCongLanDauLK);
        bcKPIPhat.setMauGiaoThanhCongLanDauLK(mauGiaoThanhCongLanDauLK);

        ratioCalculation(luyKe,bcKPIPhat);

        List<BaoCaoKPIPhatResDTO2> listTPN = new ArrayList<>();
        listTPN.add(bcKPIPhat);

        return listTPN;
    }

    //get data cho việc tính sum
    public List<BaoCaoKPIPhatResDTO2> responseCalculation(Integer luyKe, Integer loaiBaoCao, String vung, String vungCon, String chiNhanh, String buuCuc, String buuTa, String doiTac, String dichVu, LocalDate ngayBaoCao) {
        List<BaoCaoKPIPhatResDTO2> result = new ArrayList<>();
        List<BaoCaoKPIPhatResDTO2> result2 = new ArrayList<>();

//        List<BaoCaoKPIPhatResDTO2> resultTlHoan = new ArrayList<>();

        List<String> listDoiTacBank = Arrays.asList("ATM", "HDB", "RVCB", "VT01", "TPBANK", "TIMO", "HRV");

        LocalDate ngayDauThang = LocalDate.now();
        if (luyKe == 1){
            ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        }else if (luyKe == 2){
            ngayDauThang = LocalDate.from(ngayBaoCao).plusDays(6);
        }

        Integer statusMoiNhat = 1;


        // Loại báo cáo: 0 -> TCT, 1 -> vùng, 2 -> chi nhánh, 3 -> đối tác, 4 -> bưu cục
        if (isAdmin()) {
            if (luyKe == 0) {
                if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                    switch (loaiBaoCao) {
                        case 0:
                            //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatTCTSumBank(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 1:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatVungSumBank(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 2:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSumBank(chiNhanh, buuCuc, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 3:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatDTSumBank(doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSumBank(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else if (doiTac.equals("SHOPEE")) {
                    switch (loaiBaoCao) {
                        case 0:
                            //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatTCTSumShopee(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 1:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatVungSumShopee(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 2:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSumShopee(chiNhanh, buuCuc, vungCon,doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 3:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatDTSumShopee(doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSumShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (loaiBaoCao) {
                        case 0:
                            //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatTCTSum(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 1:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatVungSum(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 2:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum(chiNhanh, buuCuc,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 3:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatDTSum(doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                switch (loaiBaoCao) {
                    case 0:
                        //cr tháng 10-2024: báo cáo cấp TCT + vùng HNI/HCM -> thêm bộ lọc vùng con
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatTCTSumLKBank(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatTCTSumLKShopee(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatTCTSumLK(vung, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        result2 = bcKPIPhatRepo.findBaoCaoKPIPhatTCTSum(vung, vungCon, doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 1:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatVungSumLKBank(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatVungSumLKShopee(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatVungSumLK(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        result2 = bcKPIPhatRepo.findBaoCaoKPIPhatVungSum(vung, chiNhanh, doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 2:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSumLKBank(chiNhanh, buuCuc,vungCon ,doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSumLKShopee(chiNhanh, buuCuc, vungCon,doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSumLK(chiNhanh, buuCuc,vungCon,doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        result2 = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum(chiNhanh, buuCuc,vungCon ,doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 3:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatDTSumLKBank(doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatDTSumLKShopee(doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatDTSumLK(doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        result2 = bcKPIPhatRepo.findBaoCaoKPIPhatDTSum(doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 4:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSumLKBank(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSumLKShopee(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSumLK(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        result2 = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum(chiNhanh, buuCuc, buuTa, doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    default:
                        break;
                }
            }
        } else {
            List<String> listBC;
            List<String> listCN;

            Map<String, List<String>> mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");
            if (luyKe == 0) {
                if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                    switch (loaiBaoCao) {
                        case 2:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum1Bank(listCN, listBC,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum1Bank(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else if (doiTac.equals("SHOPEE")) {
                    switch (loaiBaoCao) {
                        case 2:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum1Shopee(listCN, listBC, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum1Shopee(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                } else {
                    switch (loaiBaoCao) {
                        case 2:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum1(listCN, listBC,vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        case 4:
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum1(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, statusMoiNhat);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                switch (loaiBaoCao) {
                    case 2:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum1LKBank(listCN, listBC, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum1LKShopee(listCN, listBC, vungCon, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum1LK(listCN, listBC,vungCon ,doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        result2 = bcKPIPhatRepo.findBaoCaoKPIPhatCNSum1(listCN, listBC,vungCon, doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    case 4:
                        if (listDoiTacBank.contains(doiTac.toUpperCase())) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum1LKBank(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else if (doiTac.equals("SHOPEE")) {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum1LKShopee(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        } else {
                            result = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum1LK(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, ngayBaoCao, ngayDauThang, statusMoiNhat);
                        }
                        result2 = bcKPIPhatRepo.findBaoCaoKPIPhatBCSum1(listCN, listBC, buuTa, doiTac, dichVu, loaiBaoCao, tmp, statusMoiNhat);
                        break;
                    default:
                        break;
                }
            }
        }

        if(luyKe == 1 || luyKe == 2) {
            //lấy SL chưa phân công phát và tổng tồn theo lũy kế 30 ngày cho phần lũy kế
            HashMap<String, List<Long>> data0LK = new HashMap<>();
            for (BaoCaoKPIPhatResDTO2 bcKPI : result2) {
                data0LK.put(bcKPI.getDonVi(), Arrays.asList(bcKPI.getTt500(), bcKPI.getTongTon(), bcKPI.getTtKhac(), bcKPI.getSlPhaiPhat()));
            }

            for (BaoCaoKPIPhatResDTO2 bcKPI : result) {
                if (data0LK.containsKey(bcKPI.getDonVi())) {
                    bcKPI.setTt500(data0LK.get(bcKPI.getDonVi()).get(0));
                    bcKPI.setTongTon(data0LK.get(bcKPI.getDonVi()).get(1));
                    bcKPI.setTtKhac(data0LK.get(bcKPI.getDonVi()).get(2));
                    bcKPI.setSlPhaiPhat(data0LK.get(bcKPI.getDonVi()).get(3));
                } else {
                    bcKPI.setTt500(0L);
                    bcKPI.setTongTon(0L);
                    bcKPI.setTtKhac(0L);
                    bcKPI.setSlPhaiPhat(0L);
                }
            }
        }
        return result;
    }

    //lấy data cho xuất excel KPI phát
    public List<BaoCaoKPIPhatBillResDTO> exportExcelKPIPhat(HttpServletResponse response, String vungPhat, String vungCon, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String buuTa) throws IOException {

        KPIPhatExcelExporter exportKPIPhatBillExcelExporter;
        List<BaoCaoKPIPhatBillResDTO> kpiPhat = Collections.emptyList();
        Integer statusMoiNhat = 1;

        LocalDate ngayDauThang = LocalDate.now();
        if (luyke == 1){
            ngayDauThang = ngayBaoCao.withDayOfMonth(1);
        }else if (luyke == 2){
            ngayDauThang = LocalDate.from(ngayBaoCao).plusDays(6);
        }

        if (!isAdmin()) {
            List<String> listBC = new ArrayList<>();
            List<String> listCN = new ArrayList<>();

            if (!SSOChecking(2, chiNhanh, buuCuc)) {
                exportKPIPhatBillExcelExporter = new KPIPhatExcelExporter(kpiPhat);
                exportKPIPhatBillExcelExporter.exportDataToExcel(response);
                return kpiPhat;
            }

            Map<String, List<String>> mapCNBC = new HashMap<>();
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");

            if (luyke == 0) {
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatBill1(listCN, vungPhat, vungCon, doiTac, listBC, dichVu, ngayBaoCao, statusMoiNhat, buuTa);
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatBillLK1(listCN, vungPhat, vungCon, doiTac, listBC, dichVu, ngayBaoCao, ngayDauThang, statusMoiNhat, buuTa);
            }
        } else {
            if (luyke == 0) {
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatBill(chiNhanh, vungPhat, vungCon, doiTac, buuCuc, dichVu, ngayBaoCao, statusMoiNhat, buuTa);
            } else {
                LocalDate tmp = ngayBaoCao;
                if (ngayDauThang.isAfter(ngayBaoCao)) {
                    ngayBaoCao = ngayDauThang;
                    ngayDauThang = tmp;
                }
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatBillLK(chiNhanh, vungPhat, vungCon, doiTac, buuCuc, dichVu, ngayBaoCao, ngayDauThang, statusMoiNhat, buuTa);
            }
        }
        exportKPIPhatBillExcelExporter = new KPIPhatExcelExporter(kpiPhat);
        exportKPIPhatBillExcelExporter.exportDataToExcel(response);
        return kpiPhat;
    }

    //lấy data cho xuất excel KPI phát tồn
    public List<BaoCaoKPIPhatBillResDTO> exportExcelKPIPhatTon(HttpServletResponse response, String vungPhat, String vungCon, String chiNhanh, String buuCuc, String doiTac, String dichVu, LocalDate ngayBaoCao, Integer luyke, String buuTa) throws IOException {
        KPIPhatBillExcelExporter exportKPIPhatBillExcelExporter;
        List<BaoCaoKPIPhatBillResDTO> kpiPhat = Collections.emptyList();
        Integer statusMoiNhat = 1;
        if (!isAdmin()) {
            List<String> listBC;
            List<String> listCN;

            if (!SSOChecking(2, chiNhanh, buuCuc)) {
                exportKPIPhatBillExcelExporter = new KPIPhatBillExcelExporter(kpiPhat);
                exportKPIPhatBillExcelExporter.exportDataToExcel(response);
                return kpiPhat;
            }

            Map<String, List<String>> mapCNBC;
            mapCNBC = checkListCNBC(chiNhanh, buuCuc);
            listCN = mapCNBC.get("cn");
            listBC = mapCNBC.get("bc");

            if (luyke == 0) {
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatTon1(listCN, vungPhat, vungCon, doiTac, listBC, dichVu, ngayBaoCao, statusMoiNhat, buuTa);
            } else if (luyke == 1) {
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatTonLK1(listCN, vungPhat, vungCon, doiTac, listBC, dichVu, ngayBaoCao, statusMoiNhat, buuTa);
            }
        } else {
            if (luyke == 0) {
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatTon(chiNhanh, vungPhat, vungCon, doiTac, buuCuc, dichVu, ngayBaoCao, statusMoiNhat, buuTa);
            } else {
                kpiPhat = bcKPIPhatBillRepo.findBaoCaoKPIPhatTonLK(chiNhanh, vungPhat, vungCon, doiTac, buuCuc, dichVu, ngayBaoCao, statusMoiNhat, buuTa);
            }
        }
        exportKPIPhatBillExcelExporter = new KPIPhatBillExcelExporter(kpiPhat);
        exportKPIPhatBillExcelExporter.exportDataToExcel(response);
        return kpiPhat;
    }

    //lấy list chi nhánh, bưu cục cần để query theo filter + SSO
    public Map<String, List<String>> checkListCNBC(String chiNhanh, String buuCuc){
        List<String> listBC;
        List<String> listCN;

        if (chiNhanh.equals("")) {
            listCN = UserContext.getUserData().getListChiNhanhVeriable();
        }
        else {
            listCN = List.of(chiNhanh);
        }
        if (buuCuc.equals("")) {
            listBC = UserContext.getUserData().getListBuuCucVeriable();
        }
        else{
            listBC = List.of(buuCuc);
        }
        Map<String, List<String>> mapCNBC = new HashMap<>();
        mapCNBC.put("cn", listCN);
        mapCNBC.put("bc", listBC);
        return mapCNBC;
    }

    @Override
    public DashboardTikTokKpiPhatResponse getInfoDashBoardKpiPhatTikTok(int type, LocalDate requestDate, String maChiNhanh, String maBuuCuc, String maDoiTac) {

        List<String> listChinhanh = null;
        List<String> listBuuCuc = null;
        if(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true") || UserContext.getUserData().getIsTTDVKH().equalsIgnoreCase("true")){
            logger.info("Case view All Cn-Bc");
            listChinhanh = new ArrayList<>();
            listBuuCuc = new ArrayList<>();
        }else if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")){
            logger.info("Case GD-CN");
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = new ArrayList<>();
            logger.info("List CN : {}", listChinhanh);
        }else if(UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")){
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = UserContext.getUserData().getListBuuCucVeriable();
            logger.info("Case LD Buu cuc");
            logger.info("List CN : {}", listChinhanh);
            logger.info("List BC : {}", listBuuCuc);
        }else{
            return new DashboardTikTokKpiPhatResponse();
        }

        LocalDate previousRequestDay = requestDate.minusDays(1);
        LocalDate firstDayOfMonth = YearMonth.of(requestDate.getYear(), requestDate.getMonthValue()).atDay(1);

        /* result sort descending by date */
        List<DashboardTikTokKpiPhatResponse> dashboardTikTokKpiPhatResponseList;
        DashboardTikTokKpiPhatResponse response = new DashboardTikTokKpiPhatResponse();

        /* Calculate sl Phải Phát */
        DashboardTikTokKpiPhatResponse slPhaiPhatRequestDateRes = bcKPIPhatRepo.getSanLuongPhaiPhat(requestDate, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
        DashboardTikTokKpiPhatResponse slPhaiPhatPreviousRequestDateRes = bcKPIPhatRepo.getSanLuongPhaiPhat(previousRequestDay, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);

        /* Calculate Tổng tồn */
        Long slTonRequestDate = bcKPIPhatRepo.getSlTongTon(requestDate, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
        Long slTonPreviousRequestDate = bcKPIPhatRepo.getSlTongTon(previousRequestDay, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);

        /*
         * type = 1 -> N & N-1
         * type = 0 -> Luy Ke
         * */
        /* Ngay N & N-1*/
        if (type == 1) {
            logger.debug("Case theo ngày : {} ", requestDate);
            dashboardTikTokKpiPhatResponseList = bcKPIPhatRepo.getInfoDashBoardKpiPhatTikTok(requestDate, previousRequestDay, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
            if (dashboardTikTokKpiPhatResponseList.size() == 0) {
                response.setTyLe(null, null, null);
                response.setTongSanLuong(null, null, null, null, null, null);
            } else {
                DashboardTikTokKpiPhatResponse requestDateData = dashboardTikTokKpiPhatResponseList.get(0);
                boolean isRequestDateHasData = requestDateData.getNgayBaoCao().isEqual(requestDate);
                Long slPhaiPhat = slPhaiPhatRequestDateRes == null ? null : slPhaiPhatRequestDateRes.getSlPhaiPhat();
                if (isRequestDateHasData) {
                    Long slPhat = slPhaiPhat != null ? requestDateData.getSlPTC() + slPhaiPhat : requestDateData.getSlPTC();
                    response.setTongSanLuong(slPhat, requestDateData.getSlPTC(), requestDateData.getSlPhatThanhCongDungGio(), requestDateData.getSlPhatLan1DungHan(), slTonRequestDate, slPhaiPhat);
                    setTyLe(response);
                } else {
                    response.setTongSanLuong(null, null, null, null, slTonRequestDate, slPhaiPhat);
                    response.setTyLe(null, null, null);
                }
            }
            /* set chenh lech value */
            setChenhLechValuesDashboardKpiTikTok(dashboardTikTokKpiPhatResponseList, requestDate, firstDayOfMonth, response, type, slPhaiPhatRequestDateRes, slPhaiPhatPreviousRequestDateRes, slTonRequestDate, slTonPreviousRequestDate);
        } else {
            /* Luy Ke */
            logger.debug("Case theo Lũy kế from: {} to: {} ", firstDayOfMonth, requestDate);
            dashboardTikTokKpiPhatResponseList = bcKPIPhatRepo.getInfoDashBoardKpiPhatTikTok(requestDate, firstDayOfMonth, maChiNhanh, maBuuCuc, maDoiTac, listChinhanh, listBuuCuc);
            if (dashboardTikTokKpiPhatResponseList.size() == 0) {
                response.setTyLe(null, null, null);
                response.setTongSanLuong(null, null, null, null, null, null);
            } else {
                Long phaiPhatLuyKe = slPhaiPhatRequestDateRes == null ? null : slPhaiPhatRequestDateRes.getSlPhaiPhatLuyKe();
                setTongSanLuongLuyKe(dashboardTikTokKpiPhatResponseList, response, slTonRequestDate, phaiPhatLuyKe);
                setTyLe(response);
            }
        }
        /* Set value chenh lech */
        setChenhLechValuesDashboardKpiTikTok(dashboardTikTokKpiPhatResponseList, requestDate, firstDayOfMonth, response, type, slPhaiPhatRequestDateRes, slPhaiPhatPreviousRequestDateRes, slTonRequestDate, slTonPreviousRequestDate);

        return response;
    }

    @Override
    public BaoCaoTonResponse getBaoCaoTon(String maChiNhanh, String maBuuCuc, String maDoiTac, LocalDate ngayBaoCao) {

        List<String> listChinhanh = null;
        List<String> listBuuCuc = null;
        if(UserContext.getUserData().getIsAdmin().equalsIgnoreCase("true")){
            logger.info("Case view All Cn-Bc");
            listChinhanh = new ArrayList<>();
            listBuuCuc = new ArrayList<>();

        }else if(UserContext.getUserData().getIsLanhDaoChiNhanh().equalsIgnoreCase("true")){
            logger.info("Case GD-CN");
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = new ArrayList<>();
            logger.info("List CN : {}", listChinhanh);
        }else if(UserContext.getUserData().getIsLanhDaoBuuCuc().equalsIgnoreCase("true")){
            listChinhanh = UserContext.getUserData().getListChiNhanhVeriable();
            listBuuCuc = UserContext.getUserData().getListBuuCucVeriable();
            logger.info("Case LD Buu cuc");
            logger.info("List CN : {}", listChinhanh);
            logger.info("List BC : {}", listBuuCuc);
        }else{
            return new BaoCaoTonResponse();
        }

        BaoCaoKPIPhatResDTO2 tongCacLoaiCanhBao = bcKPIPhatRepo.getTongLoaiCanhBao(maChiNhanh, maBuuCuc, maDoiTac, ngayBaoCao, listChinhanh, listBuuCuc);

        /* Check case các loại cảnh báo == null */
        if (tongCacLoaiCanhBao.getTongTon() == null) {
            String message = String.format("Tổng các loại cảnh báo của bưu cục: %s - chi nhánh: %s - đối tác: %s: NULL", maChiNhanh, maBuuCuc, maDoiTac);
            logger.info(message);
            return new BaoCaoTonResponse();
        }
        long tonXanhValue = tongCacLoaiCanhBao.getTonXanh();
        long tonVangValue = tongCacLoaiCanhBao.getTonVang();
        long tonDo1Value = tongCacLoaiCanhBao.getTonDo1();
        long tonDo2Value = tongCacLoaiCanhBao.getTonDo2();
        long tonDo3Value = tongCacLoaiCanhBao.getTonDo3();
        long tonDo4Value = tongCacLoaiCanhBao.getTonDo4();
        long tonDo5Value = tongCacLoaiCanhBao.getTonDo5();
        long tonDo6Value = tongCacLoaiCanhBao.getTonDo6();
        long tonDo7Value = tongCacLoaiCanhBao.getTonDo7();
        long tonDo8Value = tongCacLoaiCanhBao.getTonDo8();
        long tonDo9Value = tongCacLoaiCanhBao.getTonDo9();
        long tongTon = tongCacLoaiCanhBao.getTongTon();

        BaoCaoTonDto tonXanh = new BaoCaoTonDto("Xanh", tonXanhValue, calculateTyLeTon(tonXanhValue, tongTon));
        BaoCaoTonDto tonVang = new BaoCaoTonDto("Vang", tonVangValue, calculateTyLeTon(tonVangValue, tongTon));
        BaoCaoTonDto tonDo1 = new BaoCaoTonDto("Do1", tonDo1Value, calculateTyLeTon(tonDo1Value, tongTon));
        BaoCaoTonDto tonDo2 = new BaoCaoTonDto("Do2", tonDo2Value, calculateTyLeTon(tonDo2Value, tongTon));
        BaoCaoTonDto tonDo3 = new BaoCaoTonDto("Do3", tonDo3Value, calculateTyLeTon(tonDo3Value, tongTon));
        BaoCaoTonDto tonDo4 = new BaoCaoTonDto("Do4", tonDo4Value, calculateTyLeTon(tonDo4Value, tongTon));
        BaoCaoTonDto tonDo5 = new BaoCaoTonDto("Do5", tonDo5Value, calculateTyLeTon(tonDo5Value, tongTon));
        BaoCaoTonDto tonDo6 = new BaoCaoTonDto("Do6", tonDo6Value, calculateTyLeTon(tonDo6Value, tongTon));
        BaoCaoTonDto tonDo7 = new BaoCaoTonDto("Do7", tonDo7Value, calculateTyLeTon(tonDo7Value, tongTon));
        BaoCaoTonDto tonDo8 = new BaoCaoTonDto("Do8", tonDo8Value, calculateTyLeTon(tonDo8Value, tongTon));
        BaoCaoTonDto tonDo9 = new BaoCaoTonDto("Do9", tonDo9Value, calculateTyLeTon(tonDo9Value, tongTon));

        return new BaoCaoTonResponse(List.of(tonXanh, tonVang, tonDo1, tonDo2, tonDo3, tonDo4, tonDo5, tonDo6, tonDo7, tonDo8, tonDo9), tongTon);
    }

    private void setChenhLechValuesDashboardKpiTikTok(List<DashboardTikTokKpiPhatResponse> responses, LocalDate requestDay, LocalDate firstDayOfMonth, DashboardTikTokKpiPhatResponse resultChenhLech, int type, DashboardTikTokKpiPhatResponse slPhaiPhatRequestDate, DashboardTikTokKpiPhatResponse slPhaiPhatPreviousRequestDate, Long slTonRequestDate, Long slTonPreviousRequestDate) {
        int numberRecord = responses.size();
        Long chenhLechSlPhaiPhat = (slPhaiPhatRequestDate.getSlPhaiPhat() != null && slPhaiPhatPreviousRequestDate.getSlPhaiPhat() != null) ?
                slPhaiPhatRequestDate.getSlPhaiPhat() - slPhaiPhatPreviousRequestDate.getSlPhaiPhat() : null;
        Long chenhLechPhaiPhatLuyKe = (slPhaiPhatRequestDate.getSlPhaiPhatLuyKe() != null && slPhaiPhatPreviousRequestDate.getSlPhaiPhatLuyKe() != null) ?
                slPhaiPhatRequestDate.getSlPhaiPhatLuyKe() - slPhaiPhatPreviousRequestDate.getSlPhaiPhatLuyKe() : null;

        Long chenhLechPhaiPhatByType = type == 1 ? chenhLechSlPhaiPhat : chenhLechPhaiPhatLuyKe;

        Long chenhLechTon = (slTonRequestDate != null && slTonPreviousRequestDate != null) ? slTonRequestDate - slTonPreviousRequestDate : null;

        switch (numberRecord) {
            case 0:
                logger.debug("Case numberRecord result = 0");
                resultChenhLech.setValueChenhLech(null, null, null, null, chenhLechTon, chenhLechPhaiPhatByType);
                break;
            case 1:
                logger.debug("Case numberRecord result = 1");
                resultChenhLech.setValueChenhLech(null, null, null, null, chenhLechTon, chenhLechPhaiPhatByType);
                break;
            /* Number records >= 2 */
            default:
                DashboardTikTokKpiPhatResponse dayNData = responses.get(0);
                logger.debug("Data DayN: {}", dayNData.toString());
                DashboardTikTokKpiPhatResponse previousDayNData = responses.get(1);
                logger.debug("Data previous DayN: {}", previousDayNData.toString());
                if (type == 1) {
                    Long tongPhatRequestDate = slPhaiPhatRequestDate.getSlPhaiPhat() == null ? dayNData.getSlPTC() : dayNData.getSlPTC() + slPhaiPhatRequestDate.getSlPhaiPhat();
                    Long tongPhatPreviousRequestDate = slPhaiPhatPreviousRequestDate.getSlPhaiPhat() == null ? previousDayNData.getSlPTC() : previousDayNData.getSlPTC() + slPhaiPhatPreviousRequestDate.getSlPhaiPhat();
                    logger.debug("Case numberRecord result >= 2  & Data theo ngày từ ngày : {} đến ngày : {} ", requestDay.minusDays(1), requestDay);
                    resultChenhLech.setValueChenhLech(tongPhatRequestDate - tongPhatPreviousRequestDate, dayNData.getSlPTC() - previousDayNData.getSlPTC(),
                            dayNData.getSlPhatThanhCongDungGio() - previousDayNData.getSlPhatThanhCongDungGio(),
                            dayNData.getSlPhatLan1DungHan() - previousDayNData.getSlPhatLan1DungHan(),
                            chenhLechTon,
                            chenhLechPhaiPhatByType);
                }
                if (type == 0) {
                    LocalDate previousRequestDay = requestDay.minusDays(1);
                    if (dayNData.getNgayBaoCao().isEqual(requestDay) && previousDayNData.getNgayBaoCao().isEqual(previousRequestDay)) {
                        Long tongPhatLKRequestDate = slPhaiPhatRequestDate.getSlPhaiPhatLuyKe() == null ? dayNData.getSlPTC() : dayNData.getSlPTC() + slPhaiPhatRequestDate.getSlPhaiPhatLuyKe();
                        Long tongPhatLKPreviousRequestDate = slPhaiPhatPreviousRequestDate.getSlPhaiPhatLuyKe() == null ? previousDayNData.getSlPTC() : previousDayNData.getSlPTC() + slPhaiPhatPreviousRequestDate.getSlPhaiPhatLuyKe();
                        logger.debug("Case numberRecord result >= 2  & Data theo Lũy Kế từ ngày : {} đến ngày : {} ", firstDayOfMonth, requestDay);
                        resultChenhLech.setValueChenhLech(tongPhatLKRequestDate - tongPhatLKPreviousRequestDate, dayNData.getSlPTC() - previousDayNData.getSlPTC(),
                                dayNData.getSlPhatThanhCongDungGio() - previousDayNData.getSlPhatThanhCongDungGio(),
                                dayNData.getSlPhatLan1DungHan() - previousDayNData.getSlPhatLan1DungHan(),
                                chenhLechTon,
                                chenhLechPhaiPhatByType);
                    } else {
                        logger.debug("Case numberRecord result >= 2  & Data theo Lũy Kế từ ngày : {} đến ngày : {} nhưng data thiếu ngày : {} hoặc {} hoăc cả 2", firstDayOfMonth, requestDay, previousRequestDay, requestDay);
                        resultChenhLech.setValueChenhLech(null, null, null, null, chenhLechTon, chenhLechPhaiPhatByType);
                    }
                }
                break;
        }
    }

    private void setTongSanLuongLuyKe(List<DashboardTikTokKpiPhatResponse> dataList, DashboardTikTokKpiPhatResponse response, Long tonPhat, Long phaiPhatLuyKe) {
        long slPTC = dataList.stream().map(DashboardTikTokKpiPhatResponse::getSlPTC).reduce(0L, Long::sum);
        long slTongPhat = phaiPhatLuyKe == null ? slPTC : slPTC + phaiPhatLuyKe;
        long slPtcDungGio = dataList.stream().map(DashboardTikTokKpiPhatResponse::getSlPhatThanhCongDungGio).reduce(0L, Long::sum);
        long slDungHanLan1 = dataList.stream().map(DashboardTikTokKpiPhatResponse::getSlPhatLan1DungHan).reduce(0L, Long::sum);
        response.setTongSanLuong(slTongPhat, slPTC, slPtcDungGio, slDungHanLan1, tonPhat, phaiPhatLuyKe);
    }

    private void setTyLe(DashboardTikTokKpiPhatResponse response) {
        Double tyLePtc = response.getTongSlPhat() == 0 ? null : (response.getSlPTC() * 100.0 / response.getTongSlPhat());
        Double tyLePtcDungGio = response.getSlPTC() == 0 ? null : (response.getSlPhatThanhCongDungGio() * 100.0 / response.getSlPTC());
        Double tyLePtcDungGioLan1 = response.getSlPTC() == 0 ? null : (response.getSlPhatLan1DungHan() * 100.0 / response.getSlPTC());
        response.setTyLe(tyLePtc, tyLePtcDungGio, tyLePtcDungGioLan1);
    }

    @Override
    public List<DashboardTop10TikTokResponse> bieuDoTop10PhatThapNhat(int type, LocalDate requestDay, String maChiNhanh, String maBuuCuc, String doiTac) {
        LocalDate firstDayOfMonth = YearMonth.of(requestDay.getYear(), requestDay.getMonthValue()).atDay(1);
        List<DashboardTop10TikTokKpiPhatResult> results = null;
        List<DashboardTop10TikTokKpiPhatResult> slPhaiPhat;
        // type=1 -> ngày, type=0 -> lũy kế
        if (type == 1) {
            if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                results = bcKPIPhatRepo.getDataChartKpiPhatChiNhanhTikTok(requestDay, requestDay, null, null, doiTac);
                slPhaiPhat = bcKPIPhatRepo.getTop10CNSlPhaiPhat(requestDay, null, null, doiTac);
                results.forEach(o -> {
                    Long slPP = slPhaiPhat.stream().filter(s -> s.getDonVi().equals(o.getDonVi())).findFirst().orElse(new DashboardTop10TikTokKpiPhatResult(o.getDonVi(), 0l, 0l)).getSlPhaiPhat();
                    o.setSlPhaiPhat(slPP);
                });
            } else {
                results = bcKPIPhatRepo.getDataChartKpiPhatBuuCucTikTok(requestDay, requestDay, maChiNhanh, maBuuCuc, doiTac);
                slPhaiPhat = bcKPIPhatRepo.getTop10BCSlPhaiPhat(requestDay, maChiNhanh, maBuuCuc, doiTac);
                results.forEach(o -> {
                    Long slPP = slPhaiPhat.stream().filter(s -> s.getDonVi().equals(o.getDonVi())).findFirst().orElse(new DashboardTop10TikTokKpiPhatResult(o.getDonVi(), 0l, 0l)).getSlPhaiPhat();
                    o.setSlPhaiPhat(slPP);
                });
            }
        } else {
            if (maChiNhanh == null || maChiNhanh.isEmpty()) {
                results = bcKPIPhatRepo.getDataChartKpiPhatChiNhanhTikTok(firstDayOfMonth, requestDay, null, null, doiTac);
                slPhaiPhat = bcKPIPhatRepo.getTop10CNSlPhaiPhat(requestDay, null, null, doiTac);
                results.forEach(o -> {
                    Long slPP = slPhaiPhat.stream().filter(s -> s.getDonVi().equals(o.getDonVi())).findFirst().orElse(new DashboardTop10TikTokKpiPhatResult(o.getDonVi(), 0l, 0l)).getSlPhaiPhatLk();
                    o.setSlPhaiPhatLk(slPP);
                });
            } else {
                results = bcKPIPhatRepo.getDataChartKpiPhatBuuCucTikTok(firstDayOfMonth, requestDay, maChiNhanh, maBuuCuc, doiTac);
                slPhaiPhat = bcKPIPhatRepo.getTop10BCSlPhaiPhat(requestDay, maChiNhanh, maBuuCuc, doiTac);
                results.forEach(o -> {
                    Long slPP = slPhaiPhat.stream().filter(s -> s.getDonVi().equals(o.getDonVi())).findFirst().orElse(new DashboardTop10TikTokKpiPhatResult(o.getDonVi(), 0l, 0l)).getSlPhaiPhatLk();
                    o.setSlPhaiPhatLk(slPP);
                });
            }
        }
        List<DashboardTop10TikTokResponse> responses = getTop10KpiThuTikTok(results, type);
        return responses;
    }

    private List<DashboardTop10TikTokResponse> getTop10KpiThuTikTok(List<DashboardTop10TikTokKpiPhatResult> dashboardTop10TikTokKpiPhatResults, int type) {
        List<DashboardTop10TikTokKpiPhatResult> newResult = new ArrayList<>();
        //Tính tỉ lệ
        dashboardTop10TikTokKpiPhatResults.forEach(obj -> {
            //Lấy sản lương phải phát theo ngày/lũy kế
            Long tongSLPhat;
            if (type == 1) {
                tongSLPhat = Optional.ofNullable(obj.getSlPhatTC()).orElse(0l) + Optional.ofNullable(obj.getSlPhaiPhat()).orElse(0l);
            } else {
                tongSLPhat = Optional.ofNullable(obj.getSlPhatTC()).orElse(0l) + Optional.ofNullable(obj.getSlPhaiPhatLk()).orElse(0l);
            }
            //Tính tổng số lượng phát
            // Tỉ lệ phát thành công
            Float tlPtc = tongSLPhat == 0 ? null : (obj.getSlPhatTC().floatValue() / tongSLPhat * 100);
            // Tỉ lệ phát thành công đúng giờ
            Float tlPtcDungGio = obj.getSlPhatTC() == 0 ? null : (obj.getSlPtcDungGio().floatValue() / obj.getSlPhatTC() * 100);
            // Tỉ lệ phát thành công lần 1
            Float tlPtcLan1 = obj.getSlPhatTC() == 0 ? null : (obj.getSlPtcDungGioLan1().floatValue() / obj.getSlPhatTC() * 100);
            obj.setTlPhatThanhCong(tlPtc);
            obj.setTlPhatThanhCongDungGio(tlPtcDungGio);
            obj.setTlPhatThanhCongLan1(tlPtcLan1);
            obj.setTongSlPhat(tongSLPhat);
            newResult.add(obj);
        });
        List<DashboardTop10TikTokResponse> listBieuDoTop10PhatThanhCongThapNhat = new ArrayList<>();
        // Biểu đồ top10 tỉ lệ phát thành công thấp nhất
        List<String> donViTlPtcThapNhat = new ArrayList<>();
        List<Long> tongSlPhat = new ArrayList<>();
        List<Long> slPtc = new ArrayList<>();
        List<Float> tlPtc = new ArrayList<>();
        // Get top 10 có tỉ lệ phát thành công thấp nhất
        List<DashboardTop10TikTokKpiPhatResult> top10TlPtcThapNhat = newResult.stream()
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiPhatResult::getTlPhatThanhCong, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlPhatThanhCong() != null).limit(10)
                .collect(Collectors.toList());
//        List<DashboardTop10TikTokKpiPhatResult> top10TlPtcThapNhat = sorted.stream()
//                .limit(10)
//                .collect(Collectors.toList());
        // Tạo biểu đồ top 10 có tỷ lệ phát thành công thấp nhất
        top10TlPtcThapNhat.forEach(o -> {
            donViTlPtcThapNhat.add(o.getDonVi());
            tongSlPhat.add(o.getTongSlPhat());
            slPtc.add(o.getSlPhatTC());
            tlPtc.add(o.getTlPhatThanhCong());
        });
        Map<String, List<Object>> mapChart1 = new HashMap<>();
        mapChart1.put("donVi", Collections.singletonList(donViTlPtcThapNhat));
        mapChart1.put("tongSlPhat", Collections.singletonList(tongSlPhat));
        mapChart1.put("slPtc", Collections.singletonList(slPtc));
        mapChart1.put("tlPtc", Collections.singletonList(tlPtc));
        DashboardTop10TikTokResponse dashboardTop10TikTokPtcResponse = new DashboardTop10TikTokResponse("Top10PtcThapNhat", mapChart1);
        listBieuDoTop10PhatThanhCongThapNhat.add(dashboardTop10TikTokPtcResponse);

        // Biểu đồ top10 tỉ lệ phát thành công đúng giờ thấp nhất
        List<String> donViPtcDungGio = new ArrayList<>();
        List<Long> slPtcDungGio = new ArrayList<>();
        List<Long> slPtcDG = new ArrayList<>();
        List<Float> tlPtcDG = new ArrayList<>();
        // Get top 10 có tỉ lệ phát thành công đúng giờ thấp nhất

        List<DashboardTop10TikTokKpiPhatResult> top10TlPtcDungGioThapNhat = newResult.stream()
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiPhatResult::getTlPhatThanhCongDungGio, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlPhatThanhCongDungGio() != null).limit(10)
                .collect(Collectors.toList());
//        List<DashboardTop10TikTokKpiPhatResult> top10TlPtcDungGioThapNhat = sorted2.stream()
//                .limit(10)
//                .collect(Collectors.toList());

        // Tạo biểu đồ top 10 có tỷ lệ phát thành công đúng giờ thấp nhất
        top10TlPtcDungGioThapNhat.forEach(o -> {
            donViPtcDungGio.add(o.getDonVi());
            slPtcDungGio.add(o.getSlPtcDungGio());
            slPtcDG.add(o.getSlPhatTC());
            tlPtcDG.add(o.getTlPhatThanhCongDungGio());
        });
        Map<String, List<Object>> mapChart2 = new HashMap<>();
        mapChart2.put("donVi", Collections.singletonList(donViPtcDungGio));
        mapChart2.put("slPtcDG", Collections.singletonList(slPtcDungGio));
        mapChart2.put("slPtc", Collections.singletonList(slPtcDG));
        mapChart2.put("tlPtcDG", Collections.singletonList(tlPtcDG));
        DashboardTop10TikTokResponse top10PtcDungGioThapNhat = new DashboardTop10TikTokResponse("Top10PtcDungGioThapNhat", mapChart2);
        listBieuDoTop10PhatThanhCongThapNhat.add(top10PtcDungGioThapNhat);

        // Biểu đồ top10 tỉ lệ phát thành công lần 1 thấp nhất
        List<String> donViPtcL1 = new ArrayList<>();
        List<Long> tongSlPtcL1 = new ArrayList<>();
        List<Long> slPtcLan1 = new ArrayList<>();
        List<Float> tlPtcLan1 = new ArrayList<>();

        // Get top 10 có tỉ lệ phát thành công lần 1 thấp nhất
        //.filter(s->s.getTlThuThanhCongDungGio()
        List<DashboardTop10TikTokKpiPhatResult> top10TlPtcLan1ThapNhat = newResult.stream()
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiPhatResult::getTlPhatThanhCongLan1, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlPhatThanhCongLan1() != null).limit(10)
                .collect(Collectors.toList());
//        List<DashboardTop10TikTokKpiPhatResult> top10TlPtcLan1ThapNhat = sorted3.stream()
//                .limit(10)
//                .collect(Collectors.toList());
        // Tạo biểu đồ top 10 có tỉ lệ phát thành công lần 1 thấp nhất
        top10TlPtcLan1ThapNhat.forEach(o -> {
            donViPtcL1.add(o.getDonVi());
            tongSlPtcL1.add(o.getSlPtcDungGioLan1());
            slPtcLan1.add(o.getSlPhatTC());
            tlPtcLan1.add(o.getTlPhatThanhCongLan1());
        });
        Map<String, List<Object>> mapChart3 = new HashMap<>();
        mapChart3.put("donVi", Collections.singletonList(donViPtcL1));
        mapChart3.put("slPTC", Collections.singletonList(slPtcLan1));
        mapChart3.put("slPtcLan1", Collections.singletonList(tongSlPtcL1));
        mapChart3.put("tlPtcLan1", Collections.singletonList(tlPtcLan1));
        DashboardTop10TikTokResponse top10ThuThanhCongLan1ThapNhat = new DashboardTop10TikTokResponse("Top10PtcLan1ThapNhat", mapChart3);
        listBieuDoTop10PhatThanhCongThapNhat.add(top10ThuThanhCongLan1ThapNhat);

        //Tổng hợp 3 biểu đồ tính theo kpi phát đúng giờ lần 1 của đơn vị , Tiêu chí đánh giá tỉ lệ phát đúng giờ nhỏ hơn 85%
        List<DashboardTop10TikTokKpiPhatResult> top15TlPtcDungGioThapNhat = newResult.stream().filter(o -> o.getTlPhatThanhCongDungGio() != null).filter(o -> o.getTlPhatThanhCongDungGio() < 85f)
                .sorted(Comparator.comparing(DashboardTop10TikTokKpiPhatResult::getTlPhatThanhCongDungGio, Comparator.nullsLast(Comparator.naturalOrder()))).filter(s -> s.getTlPhatThanhCongDungGio() != null).limit(15)
                .collect(Collectors.toList());
//        List<DashboardTop10TikTokKpiPhatResult> top15TlPtcDungGioThapNhat = sortedByTlPhatDungGio.stream()
//                .limit(15)
//                .collect(Collectors.toList());
        // Tạo list 3 biểu đồ
        List<String> donVi = new ArrayList<>();
        List<Float> tlPTC = new ArrayList<>();
        List<Float> tlPTCDG = new ArrayList<>();
        List<Float> tlPtcDGL1 = new ArrayList<>();
        top15TlPtcDungGioThapNhat.forEach(o -> {
            donVi.add(o.getDonVi());
            tlPTCDG.add(o.getTlPhatThanhCongDungGio());
            DashboardTop10TikTokKpiPhatResult dashboardTop10TikTokKpiPhatResult = newResult.stream().filter(s -> s.getDonVi().equals(o.getDonVi())).findFirst().orElse(new DashboardTop10TikTokKpiPhatResult(o.getDonVi(), 0l, 0l, 0l, 0l));
            tlPTC.add(dashboardTop10TikTokKpiPhatResult.getTlPhatThanhCong());
            tlPtcDGL1.add(dashboardTop10TikTokKpiPhatResult.getTlPhatThanhCongLan1());
        });
        Map<String, List<Object>> mapChart4 = new HashMap<>();
        mapChart4.put("donVi", Collections.singletonList(donVi));
        mapChart4.put("tlPTC", Collections.singletonList(tlPTC));
        mapChart4.put("tlPtcDG", Collections.singletonList(tlPTCDG));
        mapChart4.put("tlPtcLan1", Collections.singletonList(tlPtcDGL1));
        DashboardTop10TikTokResponse top15PhatThanhCongLan1ThapNhat = new DashboardTop10TikTokResponse("Top15BieuDoTyLe", mapChart4);
        listBieuDoTop10PhatThanhCongThapNhat.add(top15PhatThanhCongLan1ThapNhat);
        return listBieuDoTop10PhatThanhCongThapNhat;
    }

    public List<BaoCaoKPIThuTop10SLResponse> getTop10SanLuong(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe, String maDoiTac) {

        // luyke = 0 => lũy kế
        // = 1 => theo ngày
        //nếu lũy kế thì ngày bắt đầu = ngày đầu tiên của tháng
        //không lũy kế thì ngày bắt đầu = ngày báo cáo
        LocalDate ngayBatDau = luyKe == 0 ? ngayBaoCao.withDayOfMonth(1) : ngayBaoCao;

        List<String> bc;
        List<String> cn;
        List<BaoCaoKPIThuTop10SLResponse> result = new ArrayList<>();
        //result2 = lấy data lũy kế của cột số lượng phải phát
        List<BaoCaoKPIThuTop10SLResponse> result2 = new ArrayList<>();

        switch (luyKe) {
            case 1:
                //lấy data cho tài khoản admin
                if (isAdmin()) {
                    if (buuCuc.equals("")) {
                        if (chiNhanh.equals("")) {
                            result = bcKPIPhatRepo.findKPIPhatTop10SLTCT(ngayBaoCao, maDoiTac);
                        } else {
                            result = bcKPIPhatRepo.findKPIPhatTop10SLCN(ngayBaoCao, chiNhanh, maDoiTac);
                        }
                    } else {
                        result = bcKPIPhatRepo.findKPIPhatTop10SLBC(ngayBaoCao, chiNhanh, buuCuc, maDoiTac);
                    }
                }
                //lấy data cho tài khoản không phải admin
                else {
                    if (!retainBCCN(chiNhanh, buuCuc)) return result;
                    if (buuCuc.equals("")) {
                        if (chiNhanh.equals("")) {
                            bc = UserContext.getUserData().getListBuuCucVeriable();
                            cn = UserContext.getUserData().getListChiNhanhVeriable();
                            result = bcKPIPhatRepo.findKPIPhatTop10SLTCTNoAdmin(ngayBaoCao, cn, bc, maDoiTac);
                        } else {
                            bc = UserContext.getUserData().getListBuuCucVeriable();
                            result = bcKPIPhatRepo.findKPIPhatTop10SLCNNoAdmin(ngayBaoCao, chiNhanh, bc, maDoiTac);
                        }
                    } else {
                        result = bcKPIPhatRepo.findKPIPhatTop10SLBC(ngayBaoCao, chiNhanh, buuCuc, maDoiTac);
                    }
                }
                break;
            case 0:
                result = getDataLuyKe(chiNhanh, buuCuc, ngayBaoCao, ngayBatDau, maDoiTac);
                break;
        }

        //sort List theo chiều ngược lại(sản lượng cao nhất lên trên)
        Collections.sort(result, Collections.reverseOrder());

        //return 10 kết quả
        return result.stream().limit(10).collect(Collectors.toList());
    }

    private List<BaoCaoKPIThuTop10SLResponse> getDataLuyKe(String chiNhanh, String buuCuc, LocalDate ngayBaoCao, LocalDate ngayBatDau, String maDoiTac) {
        List<String> bc;
        List<String> cn;
        List<BaoCaoKPIThuTop10SLResponse> result = new ArrayList<>();
        //result2 = lấy data lũy kế của cột số lượng phải phát
        List<BaoCaoKPIThuTop10SLResponse> result2 = new ArrayList<>();

        if (isAdmin()) {
            if (buuCuc.equals("")) {
                if (chiNhanh.equals("")) {
                    result = bcKPIPhatRepo.findKPIPhatTop10SLTCTLuyKe(ngayBaoCao, ngayBatDau, maDoiTac);
                    result2 = bcKPIPhatRepo.findKPIPhatTop10SLTCTLuyKePhaiPhat(ngayBaoCao, maDoiTac);
                } else {
                    result = bcKPIPhatRepo.findKPIPhatTop10SLCNLuyKe(ngayBaoCao, chiNhanh, ngayBatDau, maDoiTac);
                    result2 = bcKPIPhatRepo.findKPIPhatTop10SLCNLuyKePhaiPhat(ngayBaoCao, chiNhanh, maDoiTac);
                }
            } else {
                result = bcKPIPhatRepo.findKPIPhatTop10SLBCLuyKe(ngayBaoCao, chiNhanh, buuCuc, ngayBatDau, maDoiTac);
                result2 = bcKPIPhatRepo.findKPIPhatTop10SLBCLuyKePhaiPhat(ngayBaoCao, chiNhanh, buuCuc, maDoiTac);
            }
        }
        //lấy data cho tài khoản không phải admin
        else {
            if (!retainBCCN(chiNhanh, buuCuc)) return result;
            if (buuCuc.equals("")) {
                if (chiNhanh.equals("")) {
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    cn = UserContext.getUserData().getListChiNhanhVeriable();
                    result = bcKPIPhatRepo.findKPIPhatTop10SLTCTNoAdminLuyKePhaiPhat(ngayBaoCao, cn, bc, maDoiTac);
                } else {
                    bc = UserContext.getUserData().getListBuuCucVeriable();
                    result = bcKPIPhatRepo.findKPIPhatTop10SLCNNoAdminLuyKePhaiPhat(ngayBaoCao, chiNhanh, bc, maDoiTac);
                }
            } else {
                result = bcKPIPhatRepo.findKPIPhatTop10SLBCLuyKePhaiPhat(ngayBaoCao, chiNhanh, buuCuc, maDoiTac);
            }
        }
        for (BaoCaoKPIThuTop10SLResponse _result : result) {
            for (BaoCaoKPIThuTop10SLResponse _result2 : result2) {
                if (_result.getDonVi().equals(_result2.getDonVi())) {
                    _result.setsL(_result.getsL() == null ? 0 : _result.getsL());
                    _result2.setsL(_result2.getsL() == null ? 0 : _result2.getsL());
                    _result.setsL(_result.getsL() + _result2.getsL());
                }
            }
        }
        return result;
    }

    //check bưu cục, chi nhánh có nằm trong list mà gateway trả về hay không
    //nếu không đúng như list gateway trả về thì không ra số liệu
    private boolean retainBCCN(String chiNhanh, String buuCuc) {
        List<String> cn = new ArrayList<>(List.of(chiNhanh));
        List<String> bc = new ArrayList<>(List.of(buuCuc));
        if (!chiNhanh.equals("")) {
            cn.retainAll(UserContext.getUserData().getListChiNhanhVeriable());
            if (cn.isEmpty()) {
                return false;
            }
        }
        if (!buuCuc.equals("")) {
            bc.retainAll(UserContext.getUserData().getListBuuCucVeriable());
            if (bc.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public List<String> allBuuTa(String chinhanh, String buuCuc, LocalDate ngayBaoCao, Integer luyKe){
        LocalDate ngayDauThang = luyKe == 1 ? ngayBaoCao.withDayOfMonth(1) : ngayBaoCao;

        return bcKPIPhatRepo.findDistinctBuuTa(ngayBaoCao, ngayDauThang, chinhanh, buuCuc);
    }
}
