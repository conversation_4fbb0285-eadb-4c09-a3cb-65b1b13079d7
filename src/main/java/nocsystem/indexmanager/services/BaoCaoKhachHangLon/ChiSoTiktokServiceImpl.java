package nocsystem.indexmanager.services.BaoCaoKhachHangLon;

import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.ChiSoTiktokDTO;
import nocsystem.indexmanager.repositories.BaoCaoKhachHangLon.ChiSoTiktokRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

@Service
public class ChiSoTiktokServiceImpl implements ChiSoTiktokService {

    @Autowired
    ChiSoTiktokRepository repository;

    private boolean isAdmin() {
        return UserContext.getUserData().getIsViewAllBcTiktok().equalsIgnoreCase("true");
    }

    @Override
    public List<ChiSoTiktokDTO> getChisoTiktok(LocalDate ngayBaocao, int luyke,String maDoiTac) {
        LocalDate ngayDauThang;
        if (isAdmin()) {
            if (luyke == 0) {
                return repository.getChiSoTiktokNgay(ngayBaocao,maDoiTac);
            } else if (luyke == 1) {
                ngayDauThang = ngayBaocao.withDayOfMonth(1);
                return repository.getChiSoTiktokThang(ngayDauThang, ngayBaocao,maDoiTac);
            } else {
                ngayDauThang = LocalDate.from(ngayBaocao).plusDays(6);
                return repository.getChiSoTiktokTuan(ngayBaocao, ngayDauThang,maDoiTac);
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getListDoiTac(LocalDate ngayBaocao) {
        LocalDate ngayDauThang = ngayBaocao.withDayOfMonth(1);
        return repository.getListMaDoiTac(ngayDauThang,ngayBaocao);
    }
}
