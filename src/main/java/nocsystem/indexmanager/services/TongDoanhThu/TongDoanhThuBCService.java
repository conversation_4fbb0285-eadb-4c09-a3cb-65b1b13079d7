package nocsystem.indexmanager.services.TongDoanhThu;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuCNV1ResDto;
import nocsystem.indexmanager.repositories.TongDoanhThuBCRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class TongDoanhThuBCService {
    private final TongDoanhThuBCRepository tDTBCRepo;

    public TongDoanhThuBCService(TongDoanhThuBCRepository tDTBCRepo) {
        this.tDTBCRepo = tDTBCRepo;
    }

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    private Map<String, String> maTinh = FilterChiNhanh.maTinh;

    public CustomizeDataPage<TongDoanhThuBCV1ResDto> findAllData(String maBuuCuc, String maChiNhanh, LocalDate toTime, Integer pageQuery, Integer pageSize) {
        if ((maBuuCuc != null && !maBuuCuc.isEmpty()) && maChiNhanh != null && !maChiNhanh.isEmpty()) {
            Pageable paging = PageRequest.of(pageQuery, pageSize);
            Page<TongDoanhThuBCV1ResDto> pagedResult = tDTBCRepo.findTongDoanhThuBCAndCN(
                    maBuuCuc, maChiNhanh, toTime, paging, this.exceptionChiNhanh);
            return this.convertPaginateDataCN(pagedResult);
        } else {
            if (!UserContext.getUserData().getIsAdmin().equals("true")) {
                Pageable paging = PageRequest.of(pageQuery, pageSize);
                if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                    Page<TongDoanhThuBCV1ResDto> pagedResult = tDTBCRepo.findTongDoanhThuBCAndCNAllV2(
                            maChiNhanh, toTime, paging, UserContext.getUserData().getListBuuCucVeriable());
                    return this.convertPaginateDataCN(pagedResult);
                } else {
                    Page<TongDoanhThuBCV1ResDto> pagedResult = tDTBCRepo.findListDoanhThuBCOfCNV3(
                            maChiNhanh, maBuuCuc, toTime, paging, UserContext.getUserData().getListBuuCucVeriable());
                    return this.convertPaginateDataCN(pagedResult);
                }
            }

            Pageable paging = PageRequest.of(pageQuery, pageSize);
            Page<TongDoanhThuBCV1ResDto> pagedResult = tDTBCRepo.findTongDoanhThuBCAndCNAll(
                    maChiNhanh, toTime, paging, this.exceptionChiNhanh);
            return this.convertPaginateDataCN(pagedResult);
        }
    }

    private CustomizeDataPage<TongDoanhThuBCV1ResDto> convertPaginateDataCN(Page<TongDoanhThuBCV1ResDto> pageResult) {
        ListContentPageDto<TongDoanhThuBCV1ResDto> tongDoanhThuBC =
                new ListContentPageDto<>(pageResult);

        List<TongDoanhThuBCV1ResDto> tienDoDoanhThuCNV1ResDtos = tongDoanhThuBC.getContent();
        for (TongDoanhThuBCV1ResDto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
        }

        CustomizeDataPage<TongDoanhThuBCV1ResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tongDoanhThuBC.getTotal());
        customizeDataPage.setOffset((int) tongDoanhThuBC.getOffset());
        customizeDataPage.setLimit(tongDoanhThuBC.getLimit());
        customizeDataPage.setContent(tongDoanhThuBC.getContent());

        return customizeDataPage;
    }
}
