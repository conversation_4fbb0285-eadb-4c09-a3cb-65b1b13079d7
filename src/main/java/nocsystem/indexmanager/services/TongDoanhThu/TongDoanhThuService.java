package nocsystem.indexmanager.services.TongDoanhThu;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuDetailScreenDto;
import nocsystem.indexmanager.services.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCService;
import nocsystem.indexmanager.services.ThongKeTongDoanhThu.ThongKeTongDoanhThuCNService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


@Service
public class TongDoanhThuService {
    @Autowired
    private ThongKeTongDoanhThuCNService thongKeTongDoanhThuCNService;

    @Autowired
    private ThongKeTongDoanhThuBCService thongKeTongDoanhThuBCService;

    public SimpleAPIResponse tinhTongDoanhThuManChiTiet(String maChiNhanh, String maBuuCuc, LocalDate toTime) throws Exception {
        List<TongDoanhThuDetailScreenDto> listDoanhThuLogistic = new ArrayList<>();
        if (!maBuuCuc.isEmpty()) {
            listDoanhThuLogistic =
                    thongKeTongDoanhThuBCService.findListDoanhThuBCOfCN(maChiNhanh, maBuuCuc, toTime);
        } else {
            listDoanhThuLogistic =
                    thongKeTongDoanhThuCNService.findAllData(maChiNhanh, maBuuCuc, toTime);
        }

        TongDoanhThuCNV1ResDto tongDoanhThuCN = new TongDoanhThuCNV1ResDto();
        if (!listDoanhThuLogistic.isEmpty()) {
            tongDoanhThuCN = this.tinhTongDoanhThuChiNhanh(listDoanhThuLogistic, maChiNhanh);
        }

        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tongDoanhThuCN);
        return simpleAPIResponse;
    }

    private TongDoanhThuCNV1ResDto tinhTongDoanhThuChiNhanh(
            List<TongDoanhThuDetailScreenDto> tienDoDoanhThuCNModel,
            String maChiNhanh) {
        Float thucHien = Float.valueOf(0);
        Float keHoach = Float.valueOf(0);
        Float ngayCungKy = Float.valueOf(0);
        Float thucHienCungKyThangTruoc = Float.valueOf(0);
        Float thangCungKy = Float.valueOf(0);
        Float tongTHTBNCungKyThangTruoc = Float.valueOf(0);
        Float thucHienCungKyNamTruoc = Float.valueOf(0);
        Float tongTBNThang = Float.valueOf(0);
        Float tongDTNumber = Float.valueOf(0);
        Float soNgayCKNamTruoc = Float.valueOf(0);
        int flag = 0;

        for (TongDoanhThuDetailScreenDto tienDoDoanhThuDt : tienDoDoanhThuCNModel) {
            if (!maChiNhanh.isEmpty()) {
                keHoach = tienDoDoanhThuDt.getKeHoach();
            } else {
                keHoach += tienDoDoanhThuDt.getKeHoach();
            }

            if (tienDoDoanhThuDt.getKeHoach() == 0) {
                tienDoDoanhThuDt.setKeHoach(null);
            }

            thucHien += tienDoDoanhThuDt.getTongDoanhThu();
            thucHienCungKyThangTruoc += tienDoDoanhThuDt.getThangTruoc();
            ngayCungKy = tienDoDoanhThuDt.getCungKyNgay();
            thangCungKy = tienDoDoanhThuDt.getCungKyThang();
            soNgayCKNamTruoc = tienDoDoanhThuDt.getCungKyNam();
            thucHienCungKyNamTruoc += tienDoDoanhThuDt.getNamTruoc();
            tongDTNumber += tienDoDoanhThuDt.getTongDoanhThu();
        }
        TongDoanhThuCNV1ResDto tongDoanhThu = new TongDoanhThuCNV1ResDto();

        if ((keHoach == 0 || keHoach == null) && thucHien == 0) {
            return tongDoanhThu;
        }

        /* Tính Tổng Tỉ Lệ Hoàn Thành */
        if (keHoach != 0) {
            tongDoanhThu.setTlht(100 * (thucHien / keHoach));
        } else {
            tongDoanhThu.setTlht(null);
        }

        /* Tính tăng trưởng tháng */
        if (thucHienCungKyThangTruoc != 0) {
            tongDoanhThu.setTtThang(100 * (thucHien - thucHienCungKyThangTruoc) / thucHienCungKyThangTruoc);
        }

        /* Tính TTTBN tháng */
        /* Tính tổng thực hiện TBN tháng này */
        if (ngayCungKy != 0) {
            tongTBNThang = thucHien / ngayCungKy;
        }

        /* Tính tổng thực hiện cùng kỳ tháng trước */
        if (thangCungKy != 0) {
            tongTHTBNCungKyThangTruoc = thucHienCungKyThangTruoc / thangCungKy;
        }

        /*
         * Tổng TT TBN Tháng
         * */
        if (tongTHTBNCungKyThangTruoc != 0) {
            tongDoanhThu.setTtTbnThang(
                    100 * (tongTBNThang - tongTHTBNCungKyThangTruoc) / tongTHTBNCungKyThangTruoc);
        }

        /* Tính TT Năm */
        if (thucHienCungKyNamTruoc != 0) {
            tongDoanhThu.setTtNam(100 * (thucHien - thucHienCungKyNamTruoc) / thucHienCungKyNamTruoc);
        }

        /* Set chi nhánh */
        tongDoanhThu.setMaChiNhanh("Tổng");

        /* Set Tổng Doanh Thu */
        tongDoanhThu.setTongDoanhThu(thucHien);

        if (soNgayCKNamTruoc != 0) {
            /* Tổng thực hiện TBN tháng {tongThucHienTBNThangNay} */
            /* Tổng thực hiện cùng kỳ năm trước {thucHienCungKyNamTruoc} */
            /* Số ngày cùng kỳ năm trước {soNgayCKNamTruoc} */
            /* Tính tổng thực hiện cùng kỳ năm trước */
            Float tongTHTBNCungKyNamTruoc = thucHienCungKyNamTruoc / soNgayCKNamTruoc;
            if (tongTHTBNCungKyNamTruoc != 0) {
                tongDoanhThu.setTtTbnNam(
                        100 * (tongTBNThang - tongTHTBNCungKyNamTruoc) / tongTHTBNCungKyNamTruoc);
            }
        }

        tongDoanhThu.setKeHoach(keHoach);
        tongDoanhThu.setThucHien(thucHien);

        return tongDoanhThu;
    }
}
