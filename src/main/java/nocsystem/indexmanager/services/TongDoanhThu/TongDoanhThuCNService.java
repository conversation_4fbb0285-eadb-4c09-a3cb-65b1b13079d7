package nocsystem.indexmanager.services.TongDoanhThu;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuCNV1ResDto;
import nocsystem.indexmanager.repositories.TongDoanhThuBCRepository;
import nocsystem.indexmanager.repositories.TongDoanhThuCNRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class TongDoanhThuCNService {
    private final TongDoanhThuCNRepository tDTCNRepo;

    private final TongDoanhThuBCRepository tDTBCRepo;

    public TongDoanhThuCNService(TongDoanhThuCNRepository tDTCNRepo, TongDoanhThuBCRepository tDTBCRepo) {
        this.tDTCNRepo = tDTCNRepo;
        this.tDTBCRepo = tDTBCRepo;
    }

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    @Autowired
    private CustomizeDataPage customizeDataPage;

    private Map<String, String> maTinh = FilterChiNhanh.maTinh;

    public CustomizeDataPage<TongDoanhThuCNV1ResDto> findAllData(String maChiNhanh, String maBuuCuc, LocalDate toTime, Integer pageQuery, Integer pageSize) {
        Pageable paging = PageRequest.of(pageQuery, pageSize);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
                Page<TongDoanhThuCNV1ResDto> pagedResult = tDTCNRepo.findTongDoanhThuCNV2(
                        maChiNhanh, toTime, paging, UserContext.getUserData().getListBuuCucVeriable());
                return this.convertPaginateDataCN(pagedResult);
            }

            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Page<TongDoanhThuCNV1ResDto> pagedResult = tDTCNRepo.findTongDoanhThuCNAllV2(
                        toTime, paging, UserContext.getUserData().getListChiNhanhVeriable());
                return this.convertPaginateDataCN(pagedResult);
            } else {
                Page<TongDoanhThuCNV1ResDto> pagedResult = tDTBCRepo.findListDoanhThuBCOfCNV2(
                        maChiNhanh, maBuuCuc, toTime, paging, UserContext.getUserData().getListBuuCucVeriable());
                return this.convertPaginateDataCN(pagedResult);
            }
        }

        if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
            Page<TongDoanhThuCNV1ResDto> pagedResult = tDTCNRepo.findTongDoanhThuCN(
                    maChiNhanh, toTime, paging, this.exceptionChiNhanh);
            return this.convertPaginateDataCN(pagedResult);
        }

        Page<TongDoanhThuCNV1ResDto> pagedResult = tDTCNRepo.findTongDoanhThuCNAll(
                toTime, paging, this.exceptionChiNhanh);
        return this.convertPaginateDataCN(pagedResult);
    }

    public ThongKeTongDoanhThuBCV1ResDto findDoanhThuCuaChiNhanh(String maChiNhanh, LocalDate toTime) {
        ThongKeTongDoanhThuBCV1ResDto dtChiNhanh = tDTCNRepo.findDoanhThuCN(maChiNhanh, toTime);
        return dtChiNhanh;
    }

    private CustomizeDataPage<TongDoanhThuCNV1ResDto> convertPaginateDataCN(Page<TongDoanhThuCNV1ResDto> pageResult) {
        ListContentPageDto<TongDoanhThuCNV1ResDto> tongDTChiNhanhPage =
                new ListContentPageDto<>(pageResult);

        List<TongDoanhThuCNV1ResDto> tienDoDoanhThuCNV1ResDtos = tongDTChiNhanhPage.getContent();
        for (TongDoanhThuCNV1ResDto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
        }

        CustomizeDataPage<TongDoanhThuCNV1ResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tongDTChiNhanhPage.getTotal());
        customizeDataPage.setOffset((int) tongDTChiNhanhPage.getOffset());
        customizeDataPage.setLimit(tongDTChiNhanhPage.getLimit());
        customizeDataPage.setContent(tongDTChiNhanhPage.getContent());

        return customizeDataPage;
    }
}
