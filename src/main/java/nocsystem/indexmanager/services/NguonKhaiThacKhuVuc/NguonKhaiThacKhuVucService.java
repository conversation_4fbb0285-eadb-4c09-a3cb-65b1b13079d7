package nocsystem.indexmanager.services.NguonKhaiThacKhuVuc;

import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucRequestBody;
import nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc.NguonKhaiThacKhuVucResponse;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

public interface NguonKhaiThacKhuVucService {
    List<NguonKhaiThacKhuVucResponse> nguonKhaiThacKhuVuc(NguonKhaiThacKhuVucRequestBody requestBody) throws SQLException;
    Long nguonKhaiThacKhuVucTotalRecord(LocalDate ngayBaoCao, Integer luyKe, String ttktCha, String ttktQuetNhan, List<String> maDichVu) throws SQLException;
    NguonKhaiThacKhuVucResponse nguonKhaiThacKhuVucTong(NguonKhaiThacKhuVucRequestBody requestBody) throws SQLException;
    void nguonKhaiThacKhuVucExcel(HttpServletResponse response, NguonKhaiThacKhuVucRequestBody requestBody) throws IOException, IllegalAccessException, SQLException;
}
