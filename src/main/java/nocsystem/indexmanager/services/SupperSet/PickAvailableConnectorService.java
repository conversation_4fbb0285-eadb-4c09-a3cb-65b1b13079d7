package nocsystem.indexmanager.services.SupperSet;

import nocsystem.indexmanager.config.SupperSet.*;
import nocsystem.indexmanager.models.SupperSet.ConnectorInform;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PickAvailableConnectorService {
    @Autowired
    private NocIndexManager nocIndexManager;

    @Autowired
    private NocQuantityManager nocQuantityManager;

    @Autowired
    private NocBase nocBase;

    @Autowired
    private NocNetworkMonitoring nocNetworkMonitoring;

    @Autowired
    private NocCanhBao nocCanhBao;

    /*
     * List các database được phép truy cập hệ thống supper-set*/
    public static List<String> listPrefix = List.of("noc-index-manager", "noc-quantity-manager", "noc-base",
            "noc-canhbao", "noc-quality", "noc-network-monitoring");

    public ConnectorInform getConfigConnector(String prefixDatabase) {
        ConnectorInform setUpConnector = new ConnectorInform();
        String url = "";
        String user = "";
        String password = "";

        if (!listPrefix.contains(prefixDatabase)) {
            System.out.println("===================> There aren't available database !");
            return setUpConnector;
        }

        switch (prefixDatabase) {
            case "noc-index-manager":
                url = nocIndexManager.getUrl();
                user = nocIndexManager.getUsername();
                password = nocIndexManager.getPassword();
                break;
            case "noc-quality":
                url = nocQuantityManager.getUrl();
                user = nocQuantityManager.getUsername();
                password = nocQuantityManager.getPassword();
                break;
            case "noc-base":
                url = nocBase.getUrl();
                user = nocBase.getUsername();
                password = nocBase.getPassword();
                break;
            case "noc-network-monitoring":
                url = nocNetworkMonitoring.getUrl();
                user = nocNetworkMonitoring.getUsername();
                password = nocNetworkMonitoring.getPassword();
                break;
            case "noc-canhbao":
                url = nocCanhBao.getUrl();
                user = nocCanhBao.getUsername();
                password = nocCanhBao.getPassword();
                break;
            default:
                break;
        }

        System.out.println("connect-host =========> " + url + "-" + user + "-" + password);
        setUpConnector.setUrl(url);
        setUpConnector.setUser(user);
        setUpConnector.setPassword(password);
        return setUpConnector;
    }
}
