package nocsystem.indexmanager.services.SupperSet;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.SupperSet.SuperSetAccessTokenResponse;
import nocsystem.indexmanager.request.SupperSetReq;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Service
public interface SupperSetService
{
    public List<Map<String, Object>> getDataOfQueryBuilder (SupperSetReq supperSetReq) throws Exception;

    SimpleAPIResponse securityLogin() throws IOException;
}
