package nocsystem.indexmanager.services.LogWebview;

import nocsystem.indexmanager.config.SimpleAPIResponseV2;
import nocsystem.indexmanager.config.SimpleAPIResponseWithSum;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.LogWebview.ChiTietChuyenXeDTO;
import nocsystem.indexmanager.models.Response.LogWebview.ChiTietXeDTO;

import java.util.List;

public interface ChiTietXeService {
    ListContentPageDto<ChiTietXeDTO> chiTietXeTTKT5(Integer loaiBaoCao, String dvvc, String hanhTrinh, Integer pageQuery, Integer pageSize);

    ListContentPageDto<ChiTietXeDTO> chiTietXe(Integer loaiBaoCao, List<String> dvvc, List<String> hanhTrinh, String ttkt, String chiSo, Integer pageQuery, Integer pageSize);

    SimpleAPIResponseWithSum sanLuongXeChoKhaiThac(List<String> dvvc, List<String> hanhTrinh, String ttkt, Integer pageQuery, Integer pageSize);

    SimpleAPIResponseWithSum sanLuongXeChoKhaiThacTinh(List<String> dvvc, List<String> hanhTrinh, String chiSo, Integer page, Integer pageSize, String order, String orderBy);

    SimpleAPIResponseV2 sanLuongXeChoKhaiThacTinhAllXe(List<String> dvvc, List<String> hanhTrinh, String ttkt, String thoiGian, Integer page, Integer pageSize);

    SimpleAPIResponseV2 sanLuongXeChoKhaiThacTinhDetailXe(String bienSoXe);

    SimpleAPIResponseWithSum sanLuongXeDangKhaiThacTinh(List<String> dvvc, List<String> hanhTrinh, String chiSo, Integer page, Integer pageSize, String order, String orderBy);

    SimpleAPIResponseWithSum sanLuongXeDangKhaiThac(List<String> dvvc, List<String> hanhTrinh, String ttkt, Integer pageQuery, Integer pageSize);

    SimpleAPIResponseV2 sanLuongXeDangKhaiThacTinhAllXe(List<String> dvvc, List<String> hanhTrinh, String ttkt, Integer page, Integer pageSize);

    SimpleAPIResponseV2 sanLuongXeDangKhaiThacTinhDetailXe(String bienSoXe);

    ChiTietChuyenXeDTO chiTietChuyenXe(String maChuyenXe);
}
