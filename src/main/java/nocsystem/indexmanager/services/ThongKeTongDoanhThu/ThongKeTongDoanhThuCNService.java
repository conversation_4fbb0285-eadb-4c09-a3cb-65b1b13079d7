package nocsystem.indexmanager.services.ThongKeTongDoanhThu;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuDetailScreenDto;
import nocsystem.indexmanager.repositories.ThongKeTongDoanhThuBCRepository;
import nocsystem.indexmanager.repositories.ThongKeTongDoanhThuCNRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class ThongKeTongDoanhThuCNService {
    private final ThongKeTongDoanhThuCNRepository tKTDTCNRepo;

    private final ThongKeTongDoanhThuBCRepository thongKeTDTBCRepo;

    public ThongKeTongDoanhThuCNService(ThongKeTongDoanhThuCNRepository tKTDTCNRepo, ThongKeTongDoanhThuBCRepository thongKeTDTBCRepo) {
        this.tKTDTCNRepo = tKTDTCNRepo;
        this.thongKeTDTBCRepo = thongKeTDTBCRepo;
    }

    public List<TongDoanhThuDetailScreenDto> findAllData(String maChiNhanh, String maBuuCuc, LocalDate toTime) throws Exception {
        List<TongDoanhThuDetailScreenDto> listDoanhThu = new ArrayList<>();

        int nextIndex = 0;

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            while (true) {
                try {
                    Pageable paging = PageRequest.of(nextIndex, 100);
                    List<TongDoanhThuDetailScreenDto> tongDTElement;
                    Slice<TongDoanhThuDetailScreenDto> pagedResult;
                    System.out.println("==============>ROLE: " + UserContext.getUserData().getRole());
                    if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                        pagedResult = tKTDTCNRepo.findThongKeTongDoanhThuCNAllV2(maChiNhanh, toTime, paging, UserContext.getUserData().getListChiNhanhVeriable());
                    } else {
                        pagedResult = thongKeTDTBCRepo.findThongKeTongDoanhThuBCAll(maChiNhanh, maBuuCuc, toTime, paging, UserContext.getUserData().getListBuuCucVeriable());
                    }

                    if (pagedResult.getContent().isEmpty()) {
                        break;
                    }
                    tongDTElement = pagedResult.getContent();
                    listDoanhThu.addAll(tongDTElement);
                    nextIndex++;
                } catch (Exception exception) {
                    throw new Exception("==============>ROLE: " + UserContext.getUserData().getRole() + " ===> MESSAGE: " + exception.getMessage());
                }
            }

            return listDoanhThu;
        }

        while (true) {
            Pageable paging = PageRequest.of(nextIndex, 100);
            Slice<TongDoanhThuDetailScreenDto> pagedResult = tKTDTCNRepo.findThongKeTongDoanhThuCNAll(maChiNhanh, toTime, paging);
            List<TongDoanhThuDetailScreenDto> tongDTElement = pagedResult.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listDoanhThu.addAll(tongDTElement);
            nextIndex++;
        }

        return listDoanhThu;
    }

    public TienDoDoanhThuCNV1ResDto findTongDoanhThuCuaChiNhanh(LocalDate toTime) {
        TienDoDoanhThuCNV1ResDto tongTienDoDoanhThu = tKTDTCNRepo.findTongDoanhThuCuaChiNhanh(toTime);
        if (tongTienDoDoanhThu != null) {
            tongTienDoDoanhThu.setMaChiNhanh("Total");
        }

        return tongTienDoDoanhThu;
    }
}
