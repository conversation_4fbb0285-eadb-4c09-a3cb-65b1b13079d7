package nocsystem.indexmanager.services.ThongKeTongDoanhThu;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.helper.FilterNhomDoanhThu;
import nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TongDoanhThu.TongDoanhThuDetailScreenDto;
import nocsystem.indexmanager.repositories.*;
import nocsystem.indexmanager.servicesIpm.ChiSoKinhDoanh.ChiSoKinhDoanhIpml;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class ThongKeTongDoanhThuBCService {
    private final ThongKeTongDoanhThuBCRepository tKTDTBCRepo;
    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;
    private FilterNhomDoanhThu filterNhomDoanhThu;

    public ThongKeTongDoanhThuBCService(ThongKeTongDoanhThuBCRepository tKTDTBCRepo) {
        this.tKTDTBCRepo = tKTDTBCRepo;
        this.filterNhomDoanhThu = new FilterNhomDoanhThu();
    }

    @Autowired
    private TienDoDoanhThuBuuCucRepository tienDoDoanhThuBuuCucRepo;

    @Autowired
    private TienDoDoanhThuChiNhanhRepository tienDoDoanhThuChiNhanhRepo;

    @Autowired
    private ChiSoKinhDoanhIpml chiSoKinhDoanhIpml;

    @Autowired
    private TongDoanhThuBCRepository tongDoanhThuBCRepo;

    public ThongKeTongDoanhThuBCV1ResDto findAllData(String maBuuCuc, String maChiNhanh, LocalDate toTime, Integer pageQuery, Integer pageSize) {
        ThongKeTongDoanhThuBCV1ResDto pagedResult = tKTDTBCRepo.findThongKeTongDoanhThuBCAndCNAll(maChiNhanh, toTime);
        return pagedResult;
    }

    /* Tính tổng của bảng bưu cục
     * Tính tổng của bảng tiến độ doanh thu của một chi nhánh (Trong bảng bưu cục) theo ngày:
     * select * from cskd_tong_doanh_thu_cn where ngay_baocao : =ngay_baocao;
     * fuction findTongDoanhThuCuaChiNhanh
     */
    public TienDoDoanhThuBCV1ResDto findDoanhThuMoiChiNhanh(String maChiNhanh, LocalDate toTime) {
        TienDoDoanhThuBCV1ResDto doanhThu = tKTDTBCRepo.findDoanhThuMoiChiNhanh(maChiNhanh, toTime);

        if (doanhThu != null) {
            doanhThu.setMaChiNhanh("Total");
        }
        return doanhThu;
    }

    /* Tính tổng tiến độ doanh thu theo BC
     * Tính tổng của bảng tiến độ doanh thu của một chi nhánh (Trong bảng bưu cục) theo ngày:
     * select * from cskd_tien_do_doanh_thu_cn where ngay_baocao : =ngay_baocao;
     *
     */
    public TienDoDoanhThuBCV1ResDto tongTienDoDoanhThu(
            String maChiNhanh, String maBuuCuc, Integer nhomDoanhThu, LocalDate toTime) {

        String nhomDoanhThuCV = filterNhomDoanhThu.nhomDoanhThu(nhomDoanhThu);
        CSKDTienDoDoanhThuV2Dto tienDoDoanhThu = new CSKDTienDoDoanhThuV2Dto();
        List<CSKDTienDoDoanhThuOverViewDto> tienDoDTCP = new ArrayList<>();
        TienDoDoanhThuBCV1ResDto doanhThu = new TienDoDoanhThuBCV1ResDto();
        /* Nếu search theo bưu cục + nhóm doanh thu thì lấy ở bảng bưu cục
         * còn lại mặc định lấy ở bảng chi nhánh */

        if (!maBuuCuc.isEmpty()) {
            tienDoDTCP = this.findListTienDoDoanhThuBC(maChiNhanh, maBuuCuc, nhomDoanhThuCV, toTime);
        } else {
            tienDoDTCP = this.findListTienDoDoanhThuChiNhanh(maChiNhanh, maBuuCuc, nhomDoanhThuCV, toTime);
        }

        if (!tienDoDTCP.isEmpty()) {
            tienDoDoanhThu = chiSoKinhDoanhIpml.tinhTongDoanhThu(tienDoDTCP, maChiNhanh);
        }

        doanhThu.setMaChiNhanh("Total");
        doanhThu.setThucHien(tienDoDoanhThu.getThucHien());
        doanhThu.setNhomDoanhThu(tienDoDoanhThu.getNhomDt());
        doanhThu.setKeHoach(tienDoDoanhThu.getKeHoach());
        doanhThu.setTlht(tienDoDoanhThu.getTlHoanThanh());
        doanhThu.setTienDo(tienDoDoanhThu.getTienDo());
        doanhThu.setTtThang(tienDoDoanhThu.getTtThang());
        doanhThu.setTtTbnThang(tienDoDoanhThu.getTtTbnThang());
        doanhThu.setTtNam(tienDoDoanhThu.getTtNam());
        doanhThu.setTtTbnNam(tienDoDoanhThu.getTtTbnNam());

        return doanhThu;
    }

    public List<TongDoanhThuDetailScreenDto> findListDoanhThuBCOfCN(String maChiNhanh, String maBuuCuc, LocalDate toTime) {

        List<TongDoanhThuDetailScreenDto> listDoanhThuBC = new ArrayList<>();
        int nextIndex = 0;

        while (true) {
            Pageable paging = PageRequest.of(nextIndex, 100);
            Slice<TongDoanhThuDetailScreenDto> pagedResult = tongDoanhThuBCRepo.findListDoanhThuBCOfCN(maChiNhanh, maBuuCuc, toTime, paging);
            List<TongDoanhThuDetailScreenDto> tongDTElement = pagedResult.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listDoanhThuBC.addAll(tongDTElement);
            nextIndex++;
        }

        return listDoanhThuBC;
    }

    /**
     * Lấy danh sách tiến độ doanh thu theo bưu cục để tính tổng
     *
     * @param maChiNhanh
     * @param maBuuCuc
     * @param nhomDoanhThuCV
     * @param toTime
     * @return
     */
    public List<CSKDTienDoDoanhThuOverViewDto> findListTienDoDoanhThuBC(
            String maChiNhanh, String maBuuCuc, String nhomDoanhThuCV, LocalDate toTime) {

        List<CSKDTienDoDoanhThuOverViewDto> listDoanhThuBC = new ArrayList<>();
        int nextIndex = 0;

        while (true) {
            Pageable paging = PageRequest.of(nextIndex, 100);
            Slice<CSKDTienDoDoanhThuOverViewDto> pagedResult =
                    tienDoDoanhThuBuuCucRepo.findListTienDoDoanhThuBC(maChiNhanh, maBuuCuc, nhomDoanhThuCV, toTime, paging);
            List<CSKDTienDoDoanhThuOverViewDto> tongDTElement = pagedResult.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listDoanhThuBC.addAll(tongDTElement);
            nextIndex++;
        }

        return listDoanhThuBC;
    }

    /**
     * Lấy danh sách tiến độ doanh thu theo chi nhánh để tính tổng
     *
     * @param maChiNhanh
     * @param nhomDoanhThuCV
     * @param toTime
     * @return
     */
    public List<CSKDTienDoDoanhThuOverViewDto> findListTienDoDoanhThuChiNhanh(String maChiNhanh, String maBuuCuc, String nhomDoanhThuCV,
                                                                              LocalDate toTime) {

        List<CSKDTienDoDoanhThuOverViewDto> listDoanhThuCN = new ArrayList<>();
        int nextIndex = 0;

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            while (true) {
                Pageable paging = PageRequest.of(nextIndex, 100);
                List<CSKDTienDoDoanhThuOverViewDto> tongDTElement = new ArrayList<>();
                if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                    Slice<CSKDTienDoDoanhThuOverViewDto> pagedResult =
                            tienDoDoanhThuChiNhanhRepo.findListTienDoDoanhThuChiNhanhV2(maChiNhanh, nhomDoanhThuCV, toTime, paging, UserContext.getUserData().getListChiNhanhVeriable());
                    tongDTElement = pagedResult.getContent();
                } else {
                    Slice<CSKDTienDoDoanhThuOverViewDto> pagedResult =
                            tienDoDoanhThuBuuCucRepo.findListTienDoDoanhThuBCV2(maChiNhanh, maBuuCuc, nhomDoanhThuCV, toTime, paging, UserContext.getUserData().getListBuuCucVeriable());
                    tongDTElement = pagedResult.getContent();
                }

                if (tongDTElement.isEmpty()) {
                    break;
                }

                listDoanhThuCN.addAll(tongDTElement);
                nextIndex++;
            }

            return listDoanhThuCN;
        }

        while (true) {
            Pageable paging = PageRequest.of(nextIndex, 100);
            Slice<CSKDTienDoDoanhThuOverViewDto> pagedResult =
                    tienDoDoanhThuChiNhanhRepo.findListTienDoDoanhThuChiNhanh(maChiNhanh, nhomDoanhThuCV, toTime, paging);
            List<CSKDTienDoDoanhThuOverViewDto> tongDTElement = pagedResult.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listDoanhThuCN.addAll(tongDTElement);
            nextIndex++;
        }

        return listDoanhThuCN;
    }
}