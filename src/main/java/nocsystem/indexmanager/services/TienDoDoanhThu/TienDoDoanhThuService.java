package nocsystem.indexmanager.services.TienDoDoanhThu;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.helper.BaseFunction;
import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuResponse;
import nocsystem.indexmanager.models.Response.BieuDo.BieuDoTtTienDoDoanhThuOriginResponse;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.DataBieuDoTienDoDoanhThuDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.repositories.TienDoDoanhThu.TienDoDoanhThuRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuBuuCucRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuChiNhanhRepository;
import nocsystem.indexmanager.services.ThongKeTongDoanhThu.ThongKeTongDoanhThuBCService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TienDoDoanhThuService {
    @Autowired
    private ThongKeTongDoanhThuBCService thongKeTongDoanhThuBCService;

    @Autowired
    private TienDoDoanhThuRepository tienDoDoanhThuRepo;

    @Autowired
    private TienDoDoanhThuChiNhanhRepository tienDoDoanhThuCNRepo;

    @Autowired
    private TienDoDoanhThuBuuCucRepository tienDoDoanhThuBCRepo;

    public SimpleAPIResponse tongTienDoDoanhThu(
            String maChiNhanh, String maBuuCuc, Integer nhomDoanhThu, LocalDate toTime) {
        TienDoDoanhThuBCV1ResDto result = thongKeTongDoanhThuBCService.tongTienDoDoanhThu(
                maChiNhanh, maBuuCuc, nhomDoanhThu, toTime);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(result);
        return simpleAPIResponse;
    }

    public BieuDoTtTienDoDoanhThuResponse bieuDoTtTienDoDoanhThu(
            String maChiNhanh, String maBuuCuc, LocalDate ngayKetThuc
    ) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        String month = String.valueOf(ngayKetThuc.getMonth().getValue());
        month = month.length() < 2 ? "0" + month : month;
        String year = String.valueOf(ngayKetThuc.getYear());
        int checkingDay = ngayKetThuc.getDayOfMonth();
        List<DataBieuDoTienDoDoanhThuDto> dataBieuDoTienDoDTs =
                getDataBieuDoTienDoDoanhThu(maChiNhanh, maBuuCuc, ngayKetThuc);


        List<String> listTimes = new ArrayList<>();
        List<Float> thNgayCPs = new ArrayList<>();
        List<Float> thNgayLogistics = new ArrayList<>();
        LinkedHashMap<String, BieuDoTtTienDoDoanhThuOriginResponse> listData = new LinkedHashMap<>();
        LinkedHashMap<String, BieuDoTtTienDoDoanhThuOriginResponse> fullListData = new LinkedHashMap<>();

        for (int i = 1; i <= checkingDay; i++) {
            String day = i < 10 ? "0" + (i) : String.valueOf(i);
            String dayItem = year + "-" + month + "-" + day;
            BieuDoTtTienDoDoanhThuOriginResponse sampleData = new BieuDoTtTienDoDoanhThuOriginResponse();
            sampleData.setDate(dayItem);
            fullListData.put(dayItem, sampleData);
        }

        for (DataBieuDoTienDoDoanhThuDto dataBieuDoTienDoDT : dataBieuDoTienDoDTs) {
            String startDate = dataBieuDoTienDoDT.getNgayBaoCao().format(formatter);
            BieuDoTtTienDoDoanhThuOriginResponse dataItem = fullListData.get(startDate) ==
                    null ? new BieuDoTtTienDoDoanhThuOriginResponse() : fullListData.get(startDate);
            dataItem.setDate(startDate);
            if (dataItem.getDate().equals(startDate)) {
                if (dataBieuDoTienDoDT.getNhomDt().equals("DT-CP")) {
                    dataItem.setDtChuyenPhat(dataBieuDoTienDoDT.getTiLeTangTruongNgay());
                }
                if (dataBieuDoTienDoDT.getNhomDt().equals("DT-LOGISTIC")) {
                    dataItem.setDtLogistics(dataBieuDoTienDoDT.getTiLeTangTruongNgay());
                }
            }

            fullListData.put(startDate, dataItem);
        }

        if (!fullListData.isEmpty()) {
            List<BieuDoTtTienDoDoanhThuOriginResponse> bieuDoDataSort = new ArrayList<>(fullListData.values());
            for (BieuDoTtTienDoDoanhThuOriginResponse bieuDoDataSortDt : bieuDoDataSort) {
                String[] splitTime = bieuDoDataSortDt.getDate().split("-");
                listTimes.add(splitTime[2]+"/"+splitTime[1]);
                thNgayCPs.add(bieuDoDataSortDt.getDtChuyenPhat());
                thNgayLogistics.add(bieuDoDataSortDt.getDtLogistics());
            }
        }

        BieuDoTtTienDoDoanhThuResponse reponseData = new BieuDoTtTienDoDoanhThuResponse();
        reponseData.setDate(listTimes);
        reponseData.setDtChuyenPhat(thNgayCPs);
        reponseData.setDtLogistics(thNgayLogistics);

        return reponseData;
    }

    public List<DataBieuDoTienDoDoanhThuDto> getDataBieuDoTienDoDoanhThu(String maChiNhanh, String maBuuCuc, LocalDate ngayKetThuc) {
        List<DataBieuDoTienDoDoanhThuDto> dataBieuDoTienDoDTs = new ArrayList<>();
        BaseFunction baseFunction = new BaseFunction();
        LocalDate ngayBatDau = baseFunction.getFirstDayOfMonth(ngayKetThuc);
        if (maChiNhanh.isEmpty() && maBuuCuc.isEmpty()) {
            dataBieuDoTienDoDTs =
                    tienDoDoanhThuRepo.dataBieuDoTienDoDoanhThu(ngayBatDau, ngayKetThuc);
        }

        if (!maChiNhanh.isEmpty()) {
            /*Tinh cho mã Bưu Cục*/
            if (!maBuuCuc.isEmpty()) {
                dataBieuDoTienDoDTs =
                        tienDoDoanhThuBCRepo.dataBieuDoTienDoDoanhThu(ngayBatDau, ngayKetThuc, maChiNhanh, maBuuCuc);
            }
            /*Tính cho mã Chi Nhánh*/
            else {
                dataBieuDoTienDoDTs =
                        tienDoDoanhThuCNRepo.dataBieuDoTienDoDoanhThu(ngayBatDau, ngayKetThuc, maChiNhanh);
            }
        }

        return dataBieuDoTienDoDTs;
    }
}
