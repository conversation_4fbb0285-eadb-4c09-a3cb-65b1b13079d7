package nocsystem.indexmanager.services.TienDoDoanhThu;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.helper.FilterNhomDoanhThu;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.repositories.TienDoDoanhThuBuuCucRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class TienDoDoanhThuBCService {
    private final TienDoDoanhThuBuuCucRepository tienDoDoanhThuBuuCucRepository;

    private List<String> exceptionChiNhanh;

    private FilterNhomDoanhThu filterNhomDoanhThu;

    private Map<String, String> maTinh;

    public TienDoDoanhThuBCService(TienDoDoanhThuBuuCucRepository tienDoDoanhThuBuuCucRepository) {
        this.tienDoDoanhThuBuuCucRepository = tienDoDoanhThuBuuCucRepository;
        this.exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;
        this.filterNhomDoanhThu = new FilterNhomDoanhThu();
        this.maTinh = FilterChiNhanh.maTinh;
    }

    /**
     * Lấy danh sách tến độ doanh thu Bưu Cục
     *
     * @param maBuuCuc
     * @param maChiNhanh
     * @param nhom_doanh_thu
     * @param toTime
     * @param pageSize
     * @param page_query
     * @return
     */
    public CustomizeDataPage<TienDoDoanhThuBCV1ResDto> findAllData(
            String maBuuCuc, String maChiNhanh, Integer nhom_doanh_thu, LocalDate toTime, Integer pageSize,
            Integer page_query) {

        String nhomDoanhThuCV = filterNhomDoanhThu.nhomDoanhThu(nhom_doanh_thu);
        if (page_query > 0) page_query--;

        if (UserContext.getUserData().getIsAdmin().equals("true")) {
            if ((maBuuCuc != null && !maBuuCuc.isEmpty()) && maChiNhanh != null && !maChiNhanh.isEmpty()) {
                Pageable paging = PageRequest.of(page_query, pageSize);
                Page<TienDoDoanhThuBCV1ResDto> pageResult =
                        tienDoDoanhThuBuuCucRepository.findTienDoDoanhThuBuuCucAndChiNhanh(
                                maBuuCuc, maChiNhanh, nhomDoanhThuCV, toTime, paging, this.exceptionChiNhanh);
                return this.convertPaginateDataBC(pageResult);
            }

            if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
                Pageable pagingMaCN = PageRequest.of(page_query, pageSize);
                Page<TienDoDoanhThuBCV1ResDto> pageResult =
                        tienDoDoanhThuBuuCucRepository.findTienDoDoanhThuChiNhanh(
                                maChiNhanh, nhomDoanhThuCV, toTime, pagingMaCN, this.exceptionChiNhanh);
                return this.convertPaginateDataBC(pageResult);
            }

            Pageable pageAll = PageRequest.of(page_query, 10);
            Page<TienDoDoanhThuBCV1ResDto> pageResult =
                    tienDoDoanhThuBuuCucRepository.findTienDoDoanhThuFollowTime(
                            nhomDoanhThuCV, toTime, pageAll, this.exceptionChiNhanh);
            return this.convertPaginateDataBC(pageResult);
        }

        if ((maBuuCuc != null && !maBuuCuc.isEmpty()) && maChiNhanh != null && !maChiNhanh.isEmpty()) {
            Pageable paging = PageRequest.of(page_query, pageSize);
            Page<TienDoDoanhThuBCV1ResDto> pageResult =
                    tienDoDoanhThuBuuCucRepository.findTienDoDoanhThuBuuCucAndChiNhanhV2(
                            maBuuCuc, maChiNhanh, nhomDoanhThuCV, toTime, paging);
            return this.convertPaginateDataBC(pageResult);
        }

        if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
            Pageable pagingMaCN = PageRequest.of(page_query, pageSize);
            Page<TienDoDoanhThuBCV1ResDto> pageResult =
                    tienDoDoanhThuBuuCucRepository.findTienDoDoanhThuChiNhanhV2(
                            maChiNhanh, nhomDoanhThuCV, toTime, pagingMaCN, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginateDataBC(pageResult);
        }

        Pageable pageAll = PageRequest.of(page_query, 10);
        Page<TienDoDoanhThuBCV1ResDto> pageResult =
                tienDoDoanhThuBuuCucRepository.findTienDoDoanhThuFollowTimeV2(
                        nhomDoanhThuCV, toTime, pageAll, UserContext.getUserData().getListBuuCucVeriable());
        return this.convertPaginateDataBC(pageResult);
    }

    /**
     * Convert để customize dữ liệu trả về từ pagination
     *
     * @param pageResult
     * @return
     */
    private CustomizeDataPage<TienDoDoanhThuBCV1ResDto> convertPaginateDataBC(Page<TienDoDoanhThuBCV1ResDto> pageResult) {
        ListContentPageDto<TienDoDoanhThuBCV1ResDto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoDoanhThuBCV1ResDto> tienDoDoanhThuCNV1ResDtos = tienDoDTContentPage.getContent();
        for (TienDoDoanhThuBCV1ResDto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (tienDoDetail.getNhomDoanhThu().equals("DT-LOGISTIC")) {
                tienDoDetail.setNhomDoanhThu("DT-LOGISTICS");
            }
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(this.maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
            if (tienDoDetail.getKeHoach() == 0) {
                tienDoDetail.setKeHoach(null);
            }
        }

        CustomizeDataPage<TienDoDoanhThuBCV1ResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoDTContentPage.getContent());

        return customizeDataPage;
    }
}
