package nocsystem.indexmanager.services.TienDoDoanhThu;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.helper.FilterNhomDoanhThu;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.CSKDTienDoDoanhThuCPOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPBCDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.CSKDTienDoDoanhThuV2Dto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNV1ResDto;
import nocsystem.indexmanager.repositories.TienDoDoanhThuBuuCucRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuCPChiNhanhRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuChiNhanhRepository;
import nocsystem.indexmanager.services.TienDoDoanhThuCP.TienDoDoanhThuCPService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class TienDoDoanhThuChiNhanhService {
    private final TienDoDoanhThuChiNhanhRepository tDDTChiNhanhRepo;

    private List<String> exceptionChiNhanh;

    private FilterNhomDoanhThu nhomDoanhThu;

    private Map<String, String> maTinh;

    @Autowired
    private CustomizeDataPage customizeDataPage;

    @Autowired
    private TienDoDoanhThuCPChiNhanhRepository tienDoDTCPChiNhanhRepo;

    @Autowired
    private TienDoDoanhThuBuuCucRepository tienDoDTBuuCucRepo;

    @Autowired
    private TienDoDoanhThuCPService tienDoDoanhThuCPService;

    public TienDoDoanhThuChiNhanhService(TienDoDoanhThuChiNhanhRepository tienDoDoanhThuChiNhanhRepository) {
        this.tDDTChiNhanhRepo = tienDoDoanhThuChiNhanhRepository;
        this.exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;
        this.nhomDoanhThu = new FilterNhomDoanhThu();
        this.maTinh = FilterChiNhanh.maTinh;
    }

    public CustomizeDataPage<TienDoDoanhThuCNV1ResDto> findData(
            String maChiNhanh, Integer nhomDoanhThu, LocalDate toTime, Integer page_size, Integer page) {

        if (page > 0) page--;
        String nhomDoanhThuCV = this.nhomDoanhThu.nhomDoanhThu(nhomDoanhThu);
        Pageable page_all = PageRequest.of(page, page_size);

        if (UserContext.getUserData().getIsAdmin().equals("true")) {
            //Tính cho bưu cục
            if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
                Page<TienDoDoanhThuCNV1ResDto> pageResult = tDDTChiNhanhRepo.findTienDoDoanhThuChiNhanh(
                        maChiNhanh, nhomDoanhThuCV, toTime, page_all, this.exceptionChiNhanh);
                return this.convertPaginateDataCN(pageResult);
            }

            //Tính cho chi nhánh
            Page<TienDoDoanhThuCNV1ResDto> pageResult = tDDTChiNhanhRepo.findTienDoDoanhThuChiNhanhFollowTime(
                    nhomDoanhThuCV, toTime, page_all, this.exceptionChiNhanh);
            return this.convertPaginateDataCN(pageResult);
        }

        if (maChiNhanh.isEmpty()) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                //Tính cho chi nhánh
                Page<TienDoDoanhThuCNV1ResDto> pageResult = tDDTChiNhanhRepo.findTienDoDoanhThuChiNhanhFollowTimeV2(
                        nhomDoanhThuCV, toTime, page_all, UserContext.getUserData().getListChiNhanhVeriable());
                return this.convertPaginateDataCN(pageResult);
            }

            //Tính cho bưu cục
            Page<TienDoDoanhThuCNV1ResDto> pageResult = tienDoDTBuuCucRepo.findTienDoDoanhThuBC(
                    nhomDoanhThuCV, toTime, page_all, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginateDataCN(pageResult);
        }

        //Tính cho bưu cục
        if (maChiNhanh != null && !maChiNhanh.isEmpty()) {
            Page<TienDoDoanhThuCNV1ResDto> pageResult = tDDTChiNhanhRepo.findTienDoDoanhThuChiNhanhV2(
                    maChiNhanh, nhomDoanhThuCV, toTime, page_all, this.exceptionChiNhanh);
            return this.convertPaginateDataCN(pageResult);
        }

        return null;
    }

    public TienDoDoanhThuBCV1ResDto getTDDTByMaChiNhanh(String maChiNhanh, LocalDate toTime) {
        String nhom_doanhthu = "DT-LOGISTIC";
        TienDoDoanhThuBCV1ResDto tienDRDChiNhanhRepo = tDDTChiNhanhRepo.findDoanhThuLogistic(maChiNhanh, nhom_doanhthu, toTime);
        if (tienDRDChiNhanhRepo != null) {
            tienDRDChiNhanhRepo.setNhomDoanhThu(nhom_doanhthu);
            tienDRDChiNhanhRepo.setMaChiNhanh("Total");
            tienDRDChiNhanhRepo.setMaBuuCuc("");
        }

        return tienDRDChiNhanhRepo;
    }

    public FindTotalTienDoDoanhThuCPBCDto getTDDTCPByMaChiNhanh(String maChiNhanh, LocalDate toTime) {
        String loaiDoanhThu = "DT-CP";
        FindTotalTienDoDoanhThuCPBCDto tienDRDChiNhanhRepo = tDDTChiNhanhRepo.findDoanhThuChuyenPhat(maChiNhanh, loaiDoanhThu, toTime);
        if (tienDRDChiNhanhRepo != null) {
            tienDRDChiNhanhRepo.setMaChiNhanh("Total");
        } else {
            tienDRDChiNhanhRepo = new FindTotalTienDoDoanhThuCPBCDto();
        }

        return tienDRDChiNhanhRepo;
    }

    private CustomizeDataPage<TienDoDoanhThuCNV1ResDto> convertPaginateDataCN(Page<TienDoDoanhThuCNV1ResDto> pageResult) {
        ListContentPageDto<TienDoDoanhThuCNV1ResDto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoDoanhThuCNV1ResDto> tienDoDoanhThuCNV1ResDtos = tienDoDTContentPage.getContent();
        for (TienDoDoanhThuCNV1ResDto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (tienDoDetail.getNhomDoanhThu().equals("DT-LOGISTIC")) {
                tienDoDetail.setNhomDoanhThu("DT-LOGISTICS");
            }
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
            if (tienDoDetail.getKeHoach() == 0) {
                tienDoDetail.setKeHoach(null);
            }
        }

        CustomizeDataPage<TienDoDoanhThuCNV1ResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoDTContentPage.getContent());

        return customizeDataPage;
    }
}
