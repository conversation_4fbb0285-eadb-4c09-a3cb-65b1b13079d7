package nocsystem.indexmanager.services.ChiSoVersion;

import nocsystem.indexmanager.entity.ChiSoVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;

@Repository
public interface ChiSoVersionPostgresRepo extends JpaRepository<ChiSoVersion, Long> {
     Optional<ChiSoVersion> findChiSoVersionByNgayBaoCaoAndMaChiSo(LocalDate ngayBaoCao, String maChiSo);
}
