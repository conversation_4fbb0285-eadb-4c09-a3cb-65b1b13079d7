package nocsystem.indexmanager.services.sanLuongCamKet;

import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.sanLuongCamKet.SanLuongCamKet;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

public interface SanLuongCamKetService {
    ListContentPageDto<SanLuongCamKet> getSanLuongCamKet(LocalDate ngayBaoCao, String vung, String chiNhanh, String vungCon, String buuCuc, String cusid, List<String> dichVu, String order, String orderBy, Integer page, Integer pageSize);
    void exportSanLuongCamKetExcel(LocalDate ngayBaoCao, String vung, String chiNhanh, String vungCon, String buuCuc, String cusid, List<String> dichVu, HttpServletResponse response) throws NoSuchFieldException, IllegalAccessException;

}
