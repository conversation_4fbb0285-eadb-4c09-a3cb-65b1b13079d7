package nocsystem.indexmanager.services.DashBuuCucService;

import nocsystem.indexmanager.models.DashBuuCuc.*;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface DashBuuCucTonPhatService {
    ListContentPageCustomDto<DashBuuCucTonPhatDisplayDto, DashboardTongTonPhatdto> findAllByRoleTCT(DashTonPhatParam tonPhatParam);

    Page<DashBuuCucTonPhatChiTietDisplayDto> findAll1(DashTonPhatParam tonPhatParam);
    List<Map<String, Object>> findAllList(DashTonPhatParam tonPhatParam) throws IllegalAccessException;
     List<String> findAllListBuuTa(DashTonPhatParam tonPhatParam);

    void exportExcelChiTietTonPhat(HttpServletResponse response, DashTonPhatParam tonPhatParam) throws IOException, IllegalAccessException;
    void findAllListToiUuExcel(HttpServletResponse response, DashTonPhatParam tonPhatParam) throws IOException, IllegalAccessException, SQLException;
}
