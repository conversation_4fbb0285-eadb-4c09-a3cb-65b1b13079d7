package nocsystem.indexmanager.services.DashBoardChiNhanh;

import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.*;
import nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac.*;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import org.springframework.data.domain.Page;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

public interface DBChiNhanhBuuCucService {

    DieuHanhTonTongResponse dashBoardTongTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) throws SQLException;
    List<BieuDoTonTongResponse> bieuDoTongTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);

    List<TonNhapMayChuaKetNoiResponse> bieuDoTonChuaNhapMay(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc) throws SQLException;

    ListContentPageDto<TonHanhTrinhListingResponse> tonHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang ) throws SQLException;
    ListContentPageDto<TonHanhTrinhListingResponse> tonHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan, String loaiCanhBao) throws SQLException;
    ListContentPageDto<TonChuaPCPListingResponse> tonChuaPCP(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException;

    ListContentPageDto<TonHanhTrinhListingResponse> tonNhapMay(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException;
    ListContentPageDto<TonHanhTrinhListingResponse> tonKienCham(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException;

    ListContentPageDto<TonChuaPCPListingResponse> tonChuaCoHanhTrinh(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang, String loaiCanhBao) throws SQLException;

    ListContentPageDto<TonChuaPCPListingResponse> tonNhanBanGiao(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang, String loaiCanhBao) throws SQLException;
    ListContentPageDto<TonChuaPCPListingResponse> tonNhanBanGiao(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException;

    ListContentPageDto<TonDvGiaTangListingResponse> tonDvGiaTang(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer trangThai, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String loaiHang) throws SQLException;

    TonDetailBillResponse tonNhapMayBill(String maPhieuGui) throws SQLException;

    TonDetailBillResponse tonHanhTrinhBill(String maPhieuGui) throws SQLException;

    TonDetailBillResponse tonChuaPcpBill(String maPhieuGui) throws SQLException;

    ListContentPageDto<TongHopTonDto> tonghopTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String sort, String sortBy, String loaiHang) throws SQLException;

    ListContentPageDto<TongHopTonDtoNoVungCon> tonghopTonMobile(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, Integer pageNo, Integer pageSize, String vungCon, String doiTac, String sort, String sortBy) throws SQLException;

    TongHopTonDto tonghopTonSum(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc, String vungCon, String doiTac, String loaiHang) throws SQLException;

    List<String> getMaDoiTac(LocalDate ngayBaoCao) throws SQLException;

    SanLuongTonBuuTa getTonPhatBuuTa(String buuTa);
//    SanLuongTonBuuTa getTonThuBuuTa(String buuTa);
//    Page<BillDetailThu> getListBillTonThuBuuTa(String buuTa, String trangThai, Integer pageIndex, Integer pageSize, String orderBy);
    Page<BillDetailPhat> getListBillTonPhatBuuTa(String buuTa, String trangThai, Integer pageIndex, Integer pageSize, String orderBy);

    BieuDoXuTheTonResponse getBieuDoXuTheTon(LocalDate ngayBaoCao, String maChiNhanh, String maBuuCuc);
}
