package nocsystem.indexmanager.services.DashBoardChiNhanh;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface DashCNBCExcelService {

    List<Map<String, Object>> findAllRecordsHanhTrinh(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String loaiBaoCao, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao) throws IllegalAccessException, SQLException;
    List<Map<String, Object>> findAllRecordsHanhTrinh(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String loaiBaoCao, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan) throws IllegalAccessException, SQLException;

    List<Map<String, Object>> excelDvGiaTang(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, String loaiBaoCao, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao) throws IllegalAccessException, SQLException;

    void exportExcelHanhTrinh(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang)
            throws IllegalAccessException, IOException, SQLException;
    void exportExcelHanhTrinh(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan, String loaiCanhBao)
            throws IllegalAccessException, IOException, SQLException;
    void exportExcelTonKhacToiUu(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang, List<Integer> trangThaiList,String loaiDon,String khDacThuGui,String khDacThuNhan, String loaiCanhBao)
            throws IllegalAccessException, IOException, SQLException;

    void exportExcelKienCham(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang)
            throws IllegalAccessException, IOException, SQLException;

    void exportExcelChuaPCP(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String loaiHang)
            throws IllegalAccessException, IOException, SQLException;

    void exportExcelChuaCoHanhTrinh(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String loaiHang, String loaiCanhBao)
            throws IllegalAccessException, IOException, SQLException;

    void exportExcelNhanBanGiao(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang) throws IllegalAccessException, IOException, SQLException;
    void exportExcelNhanBanGiao(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang, String loaiCanhBao) throws IllegalAccessException, IOException, SQLException;
    void exportExcelDvGiaTang(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang) throws IllegalAccessException, IOException, SQLException;
    void exportExcelDvGiaTangToiUu(HttpServletResponse response, LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer trangThai, String vungCon, String maDoiTac, String fileName, String loaiHang) throws IllegalAccessException, IOException, SQLException;
}
