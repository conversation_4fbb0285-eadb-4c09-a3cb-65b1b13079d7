package nocsystem.indexmanager.services.SanLuongDuKienDen;

import nocsystem.indexmanager.models.Response.SanLuongDuKienDen.SanLuongDuKienDenBCPResponse;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;

public interface SanLuongDuKienDenBCPService {
    List<SanLuongDuKienDenBCPResponse> getBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanh, List<String> buuCuc, String orderBy, String sort, Integer pageNumber, Integer pageSize, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException;
    Long getTotalRecordBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException;
    SanLuongDuKienDenBCPResponse getTotalBaoCaoSLDuKienBCP(LocalDate ngayBaoCao, List<String> chiNhanh, List<String> buuCuc, String hangDacThu, String khDacThuGui, String khDacThuNhan) throws SQLException;

}
