package nocsystem.indexmanager.services.TienDoDoanhThuCP;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.BaseFunction;
import nocsystem.indexmanager.helper.FilterLoaiDichVu;
import nocsystem.indexmanager.models.Response.BieuDo.*;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.CSKDTienDoDoanhThuCPOverViewDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.FindTotalTienDoDoanhThuCPBCDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;
import nocsystem.indexmanager.repositories.ChiSoKinhDoanhRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuCPBuuCucRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuCPChiNhanhRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuCPRepository;
import nocsystem.indexmanager.services.ChiSoKinhDoanh.ChiSoKinDoanhService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class TienDoDoanhThuCPService {
    private final TienDoDoanhThuCPRepository doanhThuCPRepo;
    @Autowired
    private ChiSoKinhDoanhRepository chiSoKinhDoanhRepo;

    @Autowired
    private TienDoDoanhThuCPChiNhanhRepository tienDoDTCPChiNhanhRepo;

    @Autowired
    private TienDoDoanhThuCPBuuCucRepository tienDoDTCPBuuCucRepo;

    @Autowired
    private ChiSoKinDoanhService chiSoKinDoanhService;

    private FilterLoaiDichVu filterLoaiDichVu;

    private BaseFunction baseFunction;

    public TienDoDoanhThuCPService(TienDoDoanhThuCPRepository doanhThuCPRepo) {
        this.doanhThuCPRepo = doanhThuCPRepo;
        this.filterLoaiDichVu = new FilterLoaiDichVu();
        this.baseFunction = new BaseFunction();
    }

    public List<TienDoDoanhThuCPV1Dto> findTienDoDoanhThuCP(String maChiNhanh, String maBuuCuc, LocalDate toTime,
                                                            Boolean detail) {
//        if (detail == true) {
//            return this.calculateTienDoDTCPDetailScreen(maChiNhanh, maBuuCuc, toTime);
//        }

        if (!maChiNhanh.isEmpty() && maBuuCuc.isEmpty()) {
            return this.tienDoDoanhThuChuyenPhatCN(maChiNhanh, toTime, maBuuCuc);
        } else if (!maChiNhanh.isEmpty() && !maBuuCuc.isEmpty()) {
            return this.tienDoDoanhThuChuyenPhatBC(maChiNhanh, maBuuCuc, toTime);
        } else {
            if (!UserContext.getUserData().getIsAdmin().equals("true")) {
                return this.tienDoDoanhThuChuyenPhatCN(maChiNhanh, toTime, maBuuCuc);
            }
            return this.tienDoDoanhThuChuyenPhatTong(toTime);
        }
    }

    private List<TienDoDoanhThuCPV1Dto> calculateTienDoDTCPDetailScreen(String maChiNhanh, String maBuuCuc,
                                                                        LocalDate toTime) {
        if (!maBuuCuc.isEmpty()) {
            return this.tienDoDoanhThuChuyenPhatBCDetail(maChiNhanh, maBuuCuc, toTime);
        } else {
            return this.tienDoDoanhThuChuyenPhatCNDetail(maChiNhanh, maBuuCuc, toTime);
        }
    }

    /**
     * @param toTime
     * @return
     */
    private List<TienDoDoanhThuCPV1Dto> tienDoDoanhThuChuyenPhatTong(LocalDate toTime) {
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTChuyenPhat = doanhThuCPRepo.findDoanhThuCP(toTime);
        List<TienDoDoanhThuCPV1Dto> tienDoDTChuyenPhatConvert = this.convertModelToDomain(tienDoDTChuyenPhat);

        /* Lấy ra số tổng doanh thu của ngành chuyển phát chi nhánh */
        TienDoDoanhThuCPV1Dto tongDTChuyenPhat = this.tinhTongDoanhThuCP(tienDoDTChuyenPhat, "");
        return this.processResponseTienDoDoanhThuCP(tienDoDTChuyenPhatConvert, tongDTChuyenPhat, tienDoDTChuyenPhat);
    }

    /**
     * @param maChiNhanh
     * @param toTime
     * @return
     */
    private List<TienDoDoanhThuCPV1Dto> tienDoDoanhThuChuyenPhatCN(String maChiNhanh, LocalDate toTime, String maBuuCuc) {
        /* Lấy ra tiến độ doanh thu ngành chuyển phát chi tiết */
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDoanhThuCPCNModel = new ArrayList<>();
        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tienDoDoanhThuCPCNModel =
                        tienDoDTCPChiNhanhRepo.findTienDoDoanhChiNhanhOverViewV2(maChiNhanh, toTime, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
                tienDoDoanhThuCPCNModel =
                        tienDoDTCPBuuCucRepo.findTienDoDoanhThuBuuCucOverViewV2(maChiNhanh, maBuuCuc, toTime, UserContext.getUserData().getListBuuCucVeriable());
            }
        } else {
            tienDoDoanhThuCPCNModel =
                    tienDoDTCPChiNhanhRepo.findTienDoDoanhChiNhanhOverView(maChiNhanh, toTime);
        }

        List<TienDoDoanhThuCPV1Dto> tienDoDTChuyenPhat = this.convertModelToDomain(tienDoDoanhThuCPCNModel);

        /* Lấy ra số tổng doanh thu của ngành chuyển phát chi nhánh */
        TienDoDoanhThuCPV1Dto tongDTChuyenPhat = this.tinhTongDoanhThuCP(tienDoDoanhThuCPCNModel, maChiNhanh);
        return this.processResponseTienDoDoanhThuCP(tienDoDTChuyenPhat, tongDTChuyenPhat, tienDoDoanhThuCPCNModel);
    }

    private List<TienDoDoanhThuCPV1Dto> tienDoDoanhThuChuyenPhatCNDetail(String maChiNhanh, String maBuuCuc, LocalDate toTime) {
        /* Lấy ra tiến độ doanh thu ngành chuyển phát chi tiết */
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDoanhThuCPCNModel = new ArrayList<>();

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                tienDoDoanhThuCPCNModel =
                        tienDoDTCPChiNhanhRepo.findTienDoDoanhChiNhanhOverViewV2(maChiNhanh, toTime, UserContext.getUserData().getListChiNhanhVeriable());
            } else {
//                tienDoDoanhThuCPCNModel =
//                        tienDoDTCPBuuCucRepo.findTienDoDoanhChiNhanhOverViewV2(maChiNhanh, maBuuCuc, toTime, ListVariableLocation.listChiNhanhVeriable);
            }
        } else {
            tienDoDoanhThuCPCNModel =
                    tienDoDTCPChiNhanhRepo.findTienDoDoanhChiNhanhOverView(maChiNhanh, toTime);
        }

        return this.groupAndProcessMultidata(tienDoDoanhThuCPCNModel, maChiNhanh, "");
    }

    /**
     * @param maChiNhanh
     * @param maBuuCuc
     * @param toTime
     * @return List<TienDoDoanhThuCPV1Dto>
     */
    private List<TienDoDoanhThuCPV1Dto> tienDoDoanhThuChuyenPhatBC(String maChiNhanh, String maBuuCuc, LocalDate toTime) {
        /* Lấy ra tiến độ doanh thu ngành chuyển phát chi tiết */
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTChuyenPhatBC =
                tienDoDTCPBuuCucRepo.findTienDoDoanhThuBuuCucOverView(maChiNhanh, maBuuCuc, toTime);
        List<TienDoDoanhThuCPV1Dto> tienDoDTChuyenPhat = this.convertModelToDomain(tienDoDTChuyenPhatBC);

        /* Lấy ra số tổng doanh thu của ngành chuyển phát chi nhánh */
        TienDoDoanhThuCPV1Dto tongDTChuyenPhat = this.tinhTongDoanhThuCP(tienDoDTChuyenPhatBC, maBuuCuc);
        return this.processResponseTienDoDoanhThuCP(tienDoDTChuyenPhat, tongDTChuyenPhat, tienDoDTChuyenPhatBC);
    }

    private List<TienDoDoanhThuCPV1Dto> tienDoDoanhThuChuyenPhatBCDetail(String maChiNhanh, String maBuuCuc, LocalDate toTime) {
        /* Lấy ra tiến độ doanh thu ngành chuyển phát chi tiết */
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTChuyenPhatBC =
                tienDoDTCPBuuCucRepo.findTienDoDoanhThuBuuCucOverView(maChiNhanh, maBuuCuc, toTime);
        return this.groupAndProcessMultidata(tienDoDTChuyenPhatBC, maChiNhanh, maBuuCuc);
    }

    private List<TienDoDoanhThuCPV1Dto> groupAndProcessMultidata(List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTChuyenPhatBC,
                                                                 String maChiNhanh, String maBuuCuc) {
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTCPCOD = new ArrayList<>();
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTCPNoNCOD = new ArrayList<>();
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTCPEPX = new ArrayList<>();
        TienDoDoanhThuCPV1Dto tienDoDTCPCODDetail = new TienDoDoanhThuCPV1Dto();
        TienDoDoanhThuCPV1Dto tienDoDTCPNoNCODDetail = new TienDoDoanhThuCPV1Dto();
        TienDoDoanhThuCPV1Dto tienDoDTCPEPXDetail = new TienDoDoanhThuCPV1Dto();
        List<TienDoDoanhThuCPV1Dto> detailTienDo = new ArrayList<>();

        for (CSKDTienDoDoanhThuCPOverViewDto tienDoDTChuyenPhatBCDetail : tienDoDTChuyenPhatBC) {
            if (tienDoDTChuyenPhatBCDetail.getLoaiDichVu().equals("NoN-COD")) {
                tienDoDTCPNoNCOD.add(tienDoDTChuyenPhatBCDetail);
            }
            if (tienDoDTChuyenPhatBCDetail.getLoaiDichVu().equals("COD")) {
                tienDoDTCPCOD.add(tienDoDTChuyenPhatBCDetail);
            }
            if (tienDoDTChuyenPhatBCDetail.getLoaiDichVu().equals("EXP")) {
                tienDoDTCPEPX.add(tienDoDTChuyenPhatBCDetail);
            }
        }

        /* Calculate for each serivce */
        tienDoDTCPCODDetail = this.tinhTongDoanhThuCP(tienDoDTCPCOD, maChiNhanh);
        tienDoDTCPNoNCODDetail = this.tinhTongDoanhThuCP(tienDoDTCPNoNCOD, maChiNhanh);
        tienDoDTCPEPXDetail = this.tinhTongDoanhThuCP(tienDoDTCPEPX, maChiNhanh);
        tienDoDTCPCODDetail.setDichVu("COD");
        tienDoDTCPNoNCODDetail.setDichVu("NoN-COD");
        tienDoDTCPEPXDetail.setDichVu("EXP");
        detailTienDo.add(tienDoDTCPCODDetail);
        detailTienDo.add(tienDoDTCPNoNCODDetail);
        detailTienDo.add(tienDoDTCPEPXDetail);

        /* Convert to OverView to calculate total */
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTChuyenPhat = this.mapModelOverViewToDomain(detailTienDo);

        /* Lấy ra số tổng doanh thu của ngành chuyển phát chi nhánh */
        TienDoDoanhThuCPV1Dto tongDTChuyenPhat = this.tinhTongDoanhThuCP(tienDoDTChuyenPhat, maBuuCuc);
        return this.processResponseTienDoDoanhThuCP(detailTienDo, tongDTChuyenPhat, tienDoDTChuyenPhatBC);
    }

    /**
     * @param tienDoDoanhThuCPCNModel
     * @return TienDoDoanhThuCPV1Dto tongDoanhThu
     */
    public TienDoDoanhThuCPV1Dto tinhTongDoanhThuCP(List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDoanhThuCPCNModel,
                                                    String maChiNhanh) {
        Float thucHien = Float.valueOf(0);
        Float keHoach = Float.valueOf(0);
        Float ngayCungKy = Float.valueOf(0);
        Float thucHienCungKyThangTruoc = Float.valueOf(0);
        Float thangCungKy = Float.valueOf(0);
        Float tongTHTBNCungKyThangTruoc = Float.valueOf(0);
        Float thucHienCungKyNamTruoc = Float.valueOf(0);
        Float tongTBNThang = Float.valueOf(0);
        Float tienDo = Float.valueOf(0);
        int flag = 0;

        HashMap<String, Float> detailTienDoDT = new HashMap<>();

        for (CSKDTienDoDoanhThuCPOverViewDto tienDoDoanhThuDt : tienDoDoanhThuCPCNModel) {
            if (tienDoDoanhThuDt.getThucHien() != null) {
                thucHien += tienDoDoanhThuDt.getThucHien();
            }
            if (tienDoDoanhThuDt.getKeHoach() != null) {
                keHoach += tienDoDoanhThuDt.getKeHoach();
            }
            if (keHoach == 0) {
                flag = 1;
            }
            if (tienDoDoanhThuDt.getThangTruoc() != null) {
                thucHienCungKyThangTruoc += tienDoDoanhThuDt.getThangTruoc();
            }
            if (tienDoDoanhThuDt.getNamTruoc() != null) {
                thucHienCungKyNamTruoc += tienDoDoanhThuDt.getNamTruoc();
            }
            if (ngayCungKy == 0) {
                ngayCungKy = tienDoDoanhThuDt.getCungKyNgay();
            }
            if (thangCungKy == 0) {
                thangCungKy = tienDoDoanhThuDt.getCungKyThang();
            }

            if (!maChiNhanh.isEmpty() && tienDoDoanhThuDt.getKeHoach() != null) {
                detailTienDoDT.put(tienDoDoanhThuDt.getLoaiDichVu(), tienDoDoanhThuDt.getKeHoach());
            }

            if (tienDoDoanhThuDt.getTienDo() != null) {
                tienDo += tienDoDoanhThuDt.getTienDo();
            }
        }
        TienDoDoanhThuCPV1Dto tongDoanhThu = new TienDoDoanhThuCPV1Dto();

        /* Khi search theo bưu cục thì ke hoach sẽ lấy bằng kế hoạch của chi nhánh */
        if (!maChiNhanh.isEmpty() && !detailTienDoDT.isEmpty()) {
            keHoach = detailTienDoDT.values().stream().reduce((float) 0, Float::sum);
        }

        if ((keHoach == 0 || keHoach == null) && (thucHien == 0)) {
            tongDoanhThu.setKeHoach(keHoach);
            return tongDoanhThu;
        }

        /* Tính Tổng Tỉ Lệ Hoàn Thành */
        if (keHoach != 0) {
            tongDoanhThu.setTlHoanThanh(100 * (thucHien / keHoach));
        } else {
            tongDoanhThu.setTlHoanThanh(null);
        }

        /* Tính tiến độ */
        if (flag == 0) {
//            tongDoanhThu.setTienDo(thucHien - keHoach);
            tongDoanhThu.setTienDo(tienDo);
        }

        /* Tính tăng trưởng tháng */
        if (thucHienCungKyThangTruoc != 0) {
            tongDoanhThu.setTtThang(100 * (thucHien - thucHienCungKyThangTruoc) / thucHienCungKyThangTruoc);
        }

        /* Tính TTTBN tháng */
        /* Tính tổng thực hiện TBN tháng này */
        if (ngayCungKy != 0) {
            tongTBNThang = thucHien / ngayCungKy;
        }

        /* Tính tổng thực hiện cùng kỳ tháng trước */
        if (thangCungKy != 0) {
            tongTHTBNCungKyThangTruoc = thucHienCungKyThangTruoc / thangCungKy;
        }

        if (tongTHTBNCungKyThangTruoc != 0) {
            tongDoanhThu.setTtTbnThang(
                    100 * (tongTBNThang - tongTHTBNCungKyThangTruoc) / tongTHTBNCungKyThangTruoc);
        }

        /* Tính TT Năm */
        if (thucHienCungKyNamTruoc != 0) {
            tongDoanhThu.setTtNam(100 * (thucHien - thucHienCungKyNamTruoc) / thucHienCungKyNamTruoc);
        }

        tongDoanhThu.setThucHien(thucHien);
        tongDoanhThu.setKeHoach(keHoach);
        tongDoanhThu.setThangTruoc(thucHienCungKyThangTruoc);
        tongDoanhThu.setNamTruoc(thucHienCungKyNamTruoc);
        tongDoanhThu.setCungKyNgay(ngayCungKy);
        tongDoanhThu.setCungKyThang(thangCungKy);

        return tongDoanhThu;
    }

    /**
     * Mapper dữ liệu trả ra cho client
     *
     * @param tienDoDTChuyenPhat
     * @param tongDTChuyenPhat
     * @return
     */
    private List<TienDoDoanhThuCPV1Dto> processResponseTienDoDoanhThuCP(
            List<TienDoDoanhThuCPV1Dto> tienDoDTChuyenPhat,
            TienDoDoanhThuCPV1Dto tongDTChuyenPhat,
            List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDoanhThuCPModel) {
        List<TienDoDoanhThuCPV1Dto> result = new ArrayList<>();
        TienDoDoanhThuCPV1Dto tienDoNoNCOD = new TienDoDoanhThuCPV1Dto();
        TienDoDoanhThuCPV1Dto tienDoCOD = new TienDoDoanhThuCPV1Dto();
        TienDoDoanhThuCPV1Dto tienDoEPX = new TienDoDoanhThuCPV1Dto();

        List<CSKDTienDoDoanhThuCPOverViewDto> listNoNCOD = new ArrayList<>();
        List<CSKDTienDoDoanhThuCPOverViewDto> listCOD = new ArrayList<>();
        List<CSKDTienDoDoanhThuCPOverViewDto> listEXP = new ArrayList<>();

        if (tongDTChuyenPhat != null && (tienDoDTChuyenPhat != null && !tienDoDTChuyenPhat.isEmpty())) {
            for (CSKDTienDoDoanhThuCPOverViewDto tienDoDoanhThuCP : tienDoDoanhThuCPModel) {
                if (tienDoDoanhThuCP.getLoaiDichVu() == null) {
                    continue;
                }
                if (tienDoDoanhThuCP.getLoaiDichVu().equals("NoN-COD")) {
                    listNoNCOD.add(tienDoDoanhThuCP);
                }
                if (tienDoDoanhThuCP.getLoaiDichVu().equals("COD")) {
                    listCOD.add(tienDoDoanhThuCP);
                }
                if (tienDoDoanhThuCP.getLoaiDichVu().equals("EXP")) {
                    listEXP.add(tienDoDoanhThuCP);
                }
            }
        }

        if (tongDTChuyenPhat == null) {
            tongDTChuyenPhat = new TienDoDoanhThuCPV1Dto();
        }

        tienDoNoNCOD = tinhTongDoanhThuCP(listNoNCOD, "");
        tienDoCOD = tinhTongDoanhThuCP(listCOD, "");
        tienDoEPX = tinhTongDoanhThuCP(listEXP, "");

        tienDoNoNCOD.setDichVu("NoN-COD");
        tienDoCOD.setDichVu("COD");
        tienDoEPX.setDichVu("EXP");
        tongDTChuyenPhat.setDichVu("Tổng");
        tienDoDTChuyenPhat.add(tongDTChuyenPhat);

        result.add(tienDoNoNCOD);
        result.add(tienDoCOD);
        result.add(tienDoEPX);
        result.add(tongDTChuyenPhat);

        return result;

//        if (tongDTChuyenPhat != null && (tienDoDTChuyenPhat != null && !tienDoDTChuyenPhat.isEmpty())) {
//            for (TienDoDoanhThuCPV1Dto doanhThuCPDetail : tienDoDTChuyenPhat) {
//                if (doanhThuCPDetail.getDichVu() == null) {
//                    continue;
//                }
//                if (doanhThuCPDetail.getDichVu().equals("NoN-COD")) {
//                    tienDoNoNCOD = doanhThuCPDetail;
//                }
//                if (doanhThuCPDetail.getDichVu().equals("COD")) {
//                    tienDoCOD = doanhThuCPDetail;
//                }
//                if (doanhThuCPDetail.getDichVu().equals("EXP")) {
//                    tienDoEPX = doanhThuCPDetail;
//                }
//            }
//        }
//
//        if (tongDTChuyenPhat == null) {
//            tongDTChuyenPhat = new TienDoDoanhThuCPV1Dto();
//        }
//
//        tongDTChuyenPhat.setDichVu("Tổng");
//        tienDoDTChuyenPhat.add(tongDTChuyenPhat);
//
//        result.add(tienDoNoNCOD);
//        result.add(tienDoCOD);
//        result.add(tienDoEPX);
//        result.add(tongDTChuyenPhat);
//
//        return result;
    }

    private List<TienDoDoanhThuCPV1Dto> convertModelToDomain(List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDoanhThuModel) {
        List<TienDoDoanhThuCPV1Dto> tienDoDoanhThu = new ArrayList<>();
        for (CSKDTienDoDoanhThuCPOverViewDto tienDoDoanhThuDt : tienDoDoanhThuModel) {
            tienDoDoanhThu.add(this.mapModelToDomainOverView(tienDoDoanhThuDt));
        }

        return tienDoDoanhThu;
    }

    private TienDoDoanhThuCPV1Dto mapModelToDomainOverView(CSKDTienDoDoanhThuCPOverViewDto tienDoDoanhThuModel) {
        TienDoDoanhThuCPV1Dto tienDoDoanhThuCP = new TienDoDoanhThuCPV1Dto();
        tienDoDoanhThuCP.setDichVu(tienDoDoanhThuModel.getLoaiDichVu());
        tienDoDoanhThuCP.setTienDo(tienDoDoanhThuModel.getTienDo());
        tienDoDoanhThuCP.setTlHoanThanh(tienDoDoanhThuModel.getTlHoanThanh());
        tienDoDoanhThuCP.setTtThang(tienDoDoanhThuModel.getTtThang());
        tienDoDoanhThuCP.setTtTbnThang(tienDoDoanhThuModel.getTtTbnThang());
        tienDoDoanhThuCP.setTtNam(tienDoDoanhThuModel.getTtNam());
        tienDoDoanhThuCP.setKeHoach(tienDoDoanhThuModel.getKeHoach());
        tienDoDoanhThuCP.setThucHien(tienDoDoanhThuModel.getThucHien());
        return tienDoDoanhThuCP;
    }

    private List<CSKDTienDoDoanhThuCPOverViewDto> mapModelOverViewToDomain(List<TienDoDoanhThuCPV1Dto> tienDoDoanhThuModel) {
        List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDoanhThuCPOverView = new ArrayList<>();

        for (TienDoDoanhThuCPV1Dto tienDoDoanhThuModelDt : tienDoDoanhThuModel) {
            CSKDTienDoDoanhThuCPOverViewDto tienDoDTOverView = new CSKDTienDoDoanhThuCPOverViewDto();
            tienDoDTOverView.setLoaiDichVu(tienDoDoanhThuModelDt.getDichVu());
            tienDoDTOverView.setThucHien(tienDoDoanhThuModelDt.getThucHien());
            tienDoDTOverView.setKeHoach(tienDoDoanhThuModelDt.getKeHoach());
            tienDoDTOverView.setNamTruoc(tienDoDoanhThuModelDt.getNamTruoc());
            tienDoDTOverView.setThangTruoc(tienDoDoanhThuModelDt.getThangTruoc());
            tienDoDTOverView.setCungKyNgay(tienDoDoanhThuModelDt.getCungKyNgay());
            tienDoDTOverView.setCungKyThang(tienDoDoanhThuModelDt.getCungKyThang());
            tienDoDTOverView.setCungKyNam(tienDoDoanhThuModelDt.getCungKyNam());
            tienDoDTOverView.setTienDo(tienDoDoanhThuModelDt.getTienDo());
            tienDoDTOverView.setTlHoanThanh(tienDoDoanhThuModelDt.getTlHoanThanh());
            tienDoDTOverView.setTtThang(tienDoDoanhThuModelDt.getTtThang());
            tienDoDTOverView.setTtTbnThang(tienDoDoanhThuModelDt.getTtTbnThang());
            tienDoDTOverView.setTtNam(tienDoDTOverView.getTtNam());
            tienDoDTOverView.setTtTbnNam(tienDoDTOverView.getTtTbnNam());

            tienDoDoanhThuCPOverView.add(tienDoDTOverView);
        }

        return tienDoDoanhThuCPOverView;
    }


    public FindTotalTienDoDoanhThuCPBCDto tienDoDoanhThuCP(
            String maChiNhanh, String maBuuCuc, Integer loaiDichVu, LocalDate toTime) {
        List<CSKDTienDoDoanhThuCPOverViewDto> listTienDoDTCP = new ArrayList<>();
        TienDoDoanhThuCPV1Dto tongDoanhThuCP = new TienDoDoanhThuCPV1Dto();
        FindTotalTienDoDoanhThuCPBCDto tongDoanhThuCPConvert = new FindTotalTienDoDoanhThuCPBCDto();
        String loaiDichVuCV = filterLoaiDichVu.loaiDichVuCP(loaiDichVu);

        if (!maBuuCuc.isEmpty()) {
            listTienDoDTCP =
                    this.tienDoDTChuyenPhatBC(maChiNhanh, maBuuCuc, loaiDichVuCV, toTime);
        } else {
            listTienDoDTCP =
                    this.tienDoDTChuyenPhatCN(maChiNhanh, maBuuCuc, loaiDichVuCV, toTime);
        }

        tongDoanhThuCP = this.tinhTongDoanhThuCP(listTienDoDTCP, maChiNhanh);
        tongDoanhThuCPConvert.setLoaiDichVu(loaiDichVuCV);
        tongDoanhThuCPConvert.setTlht(tongDoanhThuCP.getTlHoanThanh());
        tongDoanhThuCPConvert.setTienDo(tongDoanhThuCP.getTienDo());
        tongDoanhThuCPConvert.setTtThang(tongDoanhThuCP.getTtThang());
        tongDoanhThuCPConvert.setTtTbnThang(tongDoanhThuCP.getTtTbnThang());
        tongDoanhThuCPConvert.setTtNam(tongDoanhThuCP.getTtNam());
        tongDoanhThuCPConvert.setMaChiNhanh("Total");
        tongDoanhThuCPConvert.setKeHoach(tongDoanhThuCP.getKeHoach());
        tongDoanhThuCPConvert.setThucHien(tongDoanhThuCP.getThucHien());

        return tongDoanhThuCPConvert;
    }

    private List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTChuyenPhatBC(
            String maChiNhanh, String maBuuCuc, String loaiDichVuCV, LocalDate toTime) {

        List<CSKDTienDoDoanhThuCPOverViewDto> listDoanhThuBC = new ArrayList<>();
        int nextIndex = 0;
        while (true) {
            Pageable pageable = PageRequest.of(nextIndex, 100);
            Slice<CSKDTienDoDoanhThuCPOverViewDto> listDTCPItem =
                    tienDoDTCPBuuCucRepo.findListTienDoDTCPBC(maChiNhanh, maBuuCuc, loaiDichVuCV, toTime, pageable);
            List<CSKDTienDoDoanhThuCPOverViewDto> tongDTElement = listDTCPItem.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listDoanhThuBC.addAll(tongDTElement);
            nextIndex++;
        }

        return listDoanhThuBC;
    }

    private List<CSKDTienDoDoanhThuCPOverViewDto> tienDoDTChuyenPhatCN(
            String maChiNhanh, String maBuuCuc, String loaiDichVuCV, LocalDate toTime) {

        List<CSKDTienDoDoanhThuCPOverViewDto> listDoanhThuCPCN = new ArrayList<>();
        int nextIndex = 0;

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            while (true) {
                Pageable pageable = PageRequest.of(nextIndex, 100);
                List<CSKDTienDoDoanhThuCPOverViewDto> tongDTElement = new ArrayList<>();
                if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                    Slice<CSKDTienDoDoanhThuCPOverViewDto> listDTCPItem =
                            tienDoDTCPChiNhanhRepo.findListTinhTongTDDTCPChiNhanhV2(maChiNhanh, loaiDichVuCV, toTime, pageable,
                                    UserContext.getUserData().getListChiNhanhVeriable());
                    tongDTElement = listDTCPItem.getContent();
                } else {
                    Slice<CSKDTienDoDoanhThuCPOverViewDto> listDTCPItem =
                            tienDoDTCPBuuCucRepo.findListTienDoDTCPBCV2(maChiNhanh, maBuuCuc, loaiDichVuCV, toTime, pageable,
                                    UserContext.getUserData().getListBuuCucVeriable());
                    tongDTElement = listDTCPItem.getContent();
                }

                if (tongDTElement.isEmpty()) {
                    break;
                }

                listDoanhThuCPCN.addAll(tongDTElement);
                nextIndex++;
            }

            return listDoanhThuCPCN;
        }

        while (true) {
            Pageable pageable = PageRequest.of(nextIndex, 100);
            Slice<CSKDTienDoDoanhThuCPOverViewDto> listDTCPItem =
                    tienDoDTCPChiNhanhRepo.findListTinhTongTDDTCPChiNhanh(maChiNhanh, loaiDichVuCV, toTime, pageable);
            List<CSKDTienDoDoanhThuCPOverViewDto> tongDTElement = listDTCPItem.getContent();
            if (tongDTElement.isEmpty()) {
                break;
            }

            listDoanhThuCPCN.addAll(tongDTElement);
            nextIndex++;
        }

        return listDoanhThuCPCN;
    }

    /**
     * Tính tổng tiến độ doanh thu CP màn chi tiết
     *
     * @param maChiNhanh
     * @param maBuuCuc
     * @param loaiDichVu
     * @param toTime
     * @return
     */
    public SimpleAPIResponse tongTienDoDoanhThuCP(String maChiNhanh, String maBuuCuc, Integer loaiDichVu, LocalDate toTime) {
        FindTotalTienDoDoanhThuCPBCDto chuyenPhatBuuCuc = this.tienDoDoanhThuCP(
                maChiNhanh, maBuuCuc, loaiDichVu, toTime);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(chuyenPhatBuuCuc);
        return simpleAPIResponse;
    }

    public BieuDoTtTienDoDoanhThuCPResponse bieuDoTtTienDoDoanhThu(String maChiNhanh, String maBuuCuc, LocalDate ngayKetThuc) {
        List<BieuDoTtTienDoDoanhThuJPADto> tienDoDTCPCN = getDataBieuDoTtTienDoDoanhThuCP(ngayKetThuc, maChiNhanh, maBuuCuc);
        int checkingDay = ngayKetThuc.getDayOfMonth();
        List<String> listTimes = new ArrayList<>();
        List<Float> dtNoNCOD = new ArrayList<>();
        List<Float> dtCOD = new ArrayList<>();
        List<Float> exp = new ArrayList<>();
        LinkedHashMap<String, GroupDataBieuDoDTCP> fullListData = new LinkedHashMap<>();
        String month = String.valueOf(ngayKetThuc.getMonth().getValue());
        month = month.length() < 2 ? "0" + month : month;
        String year = String.valueOf(ngayKetThuc.getYear());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (int i = 1; i <= checkingDay; i++) {
            String day = i < 10 ? "0" + (i) : String.valueOf(i);
            String dayItem = year + "-" + month + "-" + day;
            GroupDataBieuDoDTCP sampleData = new GroupDataBieuDoDTCP();
            sampleData.setDate(dayItem);
            fullListData.put(dayItem, sampleData);
        }

        for (BieuDoTtTienDoDoanhThuJPADto tienDoDTCPCNItem : tienDoDTCPCN) {
            String startDate = tienDoDTCPCNItem.getNgayBaoCao().format(formatter);
            GroupDataBieuDoDTCP dataItem = fullListData.get(startDate) ==
                    null ? new GroupDataBieuDoDTCP() : fullListData.get(startDate);

            dataItem.setDate(startDate);
            if (dataItem.getDate().equals(startDate)) {
                if (tienDoDTCPCNItem.getLoaiDichVu().equals("COD")) {
                    dataItem.setDtCOD(tienDoDTCPCNItem.getTiLeTangTruongNgay());
                }
                if (tienDoDTCPCNItem.getLoaiDichVu().equals("NoN-COD")) {
                    dataItem.setDtNoNCOD(tienDoDTCPCNItem.getTiLeTangTruongNgay());
                }
                if (tienDoDTCPCNItem.getLoaiDichVu().equals("EXP")) {
                    dataItem.setExp(tienDoDTCPCNItem.getTiLeTangTruongNgay());
                }
            }

            fullListData.put(startDate, dataItem);
        }

        if (!fullListData.isEmpty()) {
            List<GroupDataBieuDoDTCP> bieuDoDataSort = new ArrayList<>(fullListData.values());
            for (GroupDataBieuDoDTCP bieuDoDataSortDt : bieuDoDataSort) {
                String[] splitTime = bieuDoDataSortDt.getDate().split("-");
                listTimes.add(splitTime[2] + "/" + splitTime[1]);
                dtCOD.add(bieuDoDataSortDt.getDtCOD());
                dtNoNCOD.add(bieuDoDataSortDt.getDtNoNCOD());
                exp.add(bieuDoDataSortDt.getExp());
            }
        }

        BieuDoTtTienDoDoanhThuCPResponse reponseData = new BieuDoTtTienDoDoanhThuCPResponse();
        reponseData.setDate(listTimes);
        reponseData.setDTCOD(dtCOD);
        reponseData.setDTNoNCOD(dtNoNCOD);
        reponseData.setEXP(exp);

        return reponseData;
    }


    public List<BieuDoTtTienDoDoanhThuJPADto> getDataBieuDoTtTienDoDoanhThuCP(LocalDate ngayKetThuc, String maChiNhanh, String maBuuCuc) {
        LocalDate ngayBatDau = baseFunction.getFirstDayOfMonth(ngayKetThuc);
        List<BieuDoTtTienDoDoanhThuJPADto> tienDoDTCPCN = new ArrayList<>();
        if (maChiNhanh.isEmpty()) {
            tienDoDTCPCN = doanhThuCPRepo.bieuDoTtTienDoDoanhThuCP(ngayBatDau, ngayKetThuc);
        } else {
            //Tính cho trường hợp bưu cục
            if (!maBuuCuc.isEmpty()) {
                tienDoDTCPCN = tienDoDTCPBuuCucRepo.bieuDoTtTienDoDoanhThu(ngayBatDau, ngayKetThuc, maChiNhanh, maBuuCuc);
            } else {
                //Tính cho trường hơp chi nhánh
                tienDoDTCPCN = tienDoDTCPChiNhanhRepo.bieuDoTtTienDoDoanhThu(ngayBatDau, ngayKetThuc, maChiNhanh);
            }
        }

        return tienDoDTCPCN;
    }
}
