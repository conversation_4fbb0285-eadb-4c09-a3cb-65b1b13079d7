package nocsystem.indexmanager.services.TienDoDoanhThuCP;

import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.helper.FilterLoaiDichVu;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPBCV1ResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPCNV2ResDto;
import nocsystem.indexmanager.repositories.TienDoDoanhThuCPBuuCucRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class TienDoDoanhThuCPBuuCucService {
    private final TienDoDoanhThuCPBuuCucRepository cpBuuCucRepository;

    private FilterLoaiDichVu filterLoaiDichVu;

    private Map<String, String> maTinh;

    public TienDoDoanhThuCPBuuCucService(TienDoDoanhThuCPBuuCucRepository cpBuuCucRepository) {
        this.cpBuuCucRepository = cpBuuCucRepository;
        this.filterLoaiDichVu = new FilterLoaiDichVu();
        this.maTinh = FilterChiNhanh.maTinh;
    }

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    public CustomizeDataPage<TienDoDoanhThuCPBCV1ResDto> findAllData(
            String maBC, String maCN, Integer loaiDichVu, LocalDate toTime, int pageSize, int pageQuery){

        String loaiDichVuCV = this.filterLoaiDichVu.loaiDichVuCP(loaiDichVu);

        if (!UserContext.getUserData().getIsAdmin().equals("true")) {
            if((!maBC.isEmpty()) && !maCN.isEmpty())
            {
                Pageable paging = PageRequest.of(pageQuery, pageSize);
                Page doanhThuCPBC = cpBuuCucRepository.findTienDoDoanhThuBuuCucAndChiNhanhV2(
                        maBC, maCN, loaiDichVuCV, toTime, paging);
                return this.convertPaginateDataBC(doanhThuCPBC);
            }

            Pageable pageAll = PageRequest.of(pageQuery, pageSize);
            Page doanhThuCPBC = cpBuuCucRepository.findTienDoDoanhThuFollowTimeV2(
                    maCN, loaiDichVuCV, toTime, pageAll, UserContext.getUserData().getListBuuCucVeriable());
            return this.convertPaginateDataBC(doanhThuCPBC);
        }

        if((maBC != null && !maBC.isEmpty()) && maCN != null && !maCN.isEmpty())
        {
            Pageable paging = PageRequest.of(pageQuery, pageSize);
            Page doanhThuCPBC = cpBuuCucRepository.findTienDoDoanhThuBuuCucAndChiNhanh(
                    maBC, maCN, loaiDichVuCV, toTime, paging, this.exceptionChiNhanh);
            return this.convertPaginateDataBC(doanhThuCPBC);
        }

        Pageable pageAll = PageRequest.of(pageQuery, pageSize);
        Page doanhThuCPBC = cpBuuCucRepository.findTienDoDoanhThuFollowTime(
                maCN, loaiDichVuCV, toTime, pageAll, this.exceptionChiNhanh);
        return this.convertPaginateDataBC(doanhThuCPBC);

    }

    private CustomizeDataPage<TienDoDoanhThuCPBCV1ResDto> convertPaginateDataBC(Page<TienDoDoanhThuCPBCV1ResDto> pageResult) {
        ListContentPageDto<TienDoDoanhThuCPBCV1ResDto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoDoanhThuCPBCV1ResDto> tienDoDoanhThuCNV1ResDtos = tienDoDTContentPage.getContent();
        for (TienDoDoanhThuCPBCV1ResDto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(this.maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
            if (tienDoDetail.getKeHoach() == 0) {
                tienDoDetail.setKeHoach(null);
            }
        }

        CustomizeDataPage<TienDoDoanhThuCPBCV1ResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoDTContentPage.getContent());

        return customizeDataPage;
    }
}
