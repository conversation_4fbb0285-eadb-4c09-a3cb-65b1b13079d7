package nocsystem.indexmanager.services.TienDoDoanhThuCP;

import nocsystem.indexmanager.config.SSOPermission.ConfigPermissions;
import nocsystem.indexmanager.global.variable.ListVariableLocation;
import nocsystem.indexmanager.global.variable.UserContext;
import nocsystem.indexmanager.helper.CustomizeDataPage;
import nocsystem.indexmanager.helper.FilterChiNhanh;
import nocsystem.indexmanager.helper.FilterLoaiDichVu;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.TienDoDoanhThuCNResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPCNV2ResDto;
import nocsystem.indexmanager.repositories.TienDoDoanhThuCPBuuCucRepository;
import nocsystem.indexmanager.repositories.TienDoDoanhThuCPChiNhanhRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static nocsystem.indexmanager.global.variable.ListVariableLocation.isAdmin;
import static nocsystem.indexmanager.global.variable.ListVariableLocation.listChiNhanhVeriable;

@Service
public class TienDoDoanhThuCPChiNhanhService {
    private final TienDoDoanhThuCPChiNhanhRepository doanhThuCPCNRepo;

    private final TienDoDoanhThuCPBuuCucRepository doanhThuCPBuuCucRepo;

    private FilterLoaiDichVu filterLoaiDichVu;

    private Map<String, String> maTinh;

    public TienDoDoanhThuCPChiNhanhService(TienDoDoanhThuCPChiNhanhRepository doanhThuCPCNRepo, TienDoDoanhThuCPBuuCucRepository doanhThuCPBuuCucRepo) {
        this.doanhThuCPCNRepo = doanhThuCPCNRepo;
        this.doanhThuCPBuuCucRepo = doanhThuCPBuuCucRepo;
        this.filterLoaiDichVu = new FilterLoaiDichVu();
        this.maTinh = FilterChiNhanh.maTinh;
    }

    private List<String> exceptionChiNhanh = FilterChiNhanh.NOTREGULAR;

    public CustomizeDataPage<TienDoDoanhThuCPCNV2ResDto> findAllDataCPCN(String maCN, Integer loaiDichVu, LocalDate to_time, int pageSize, int pageQuery) {
        String loaiDichVuCN = filterLoaiDichVu.loaiDichVuCP(loaiDichVu);

        if (!isAdmin.equals("true")) {
            if (maCN != null && !maCN.isEmpty()) {
                Pageable paging = PageRequest.of(pageQuery, pageSize);
                Page<TienDoDoanhThuCPCNV2ResDto> doanhThuCPCN =
                        doanhThuCPCNRepo.findTienDoDoanhThuBuuCucAndChiNhanhV2(
                                maCN, loaiDichVuCN, to_time, paging, this.exceptionChiNhanh);

                return this.convertPaginateDataBC(doanhThuCPCN);
            }

            if (ConfigPermissions.ALL_POST_OFFICIALS.contains(UserContext.getUserData().getRole())) {
                Pageable paging = PageRequest.of(pageQuery, pageSize);
                Page<TienDoDoanhThuCPCNV2ResDto> doanhThuCPCN =
                        doanhThuCPCNRepo.findTienDoDoanhThuCPCNFollowTimeV2(
                                loaiDichVuCN, to_time, paging, UserContext.getUserData().getListChiNhanhVeriable());
                return this.convertPaginateDataBC(doanhThuCPCN);
            } else {
                Pageable paging = PageRequest.of(pageQuery, pageSize);
                Page<TienDoDoanhThuCPCNV2ResDto> doanhThuCPCN =
                        doanhThuCPBuuCucRepo.findListTienDoDTCPBCDetail(
                                loaiDichVuCN, to_time, paging, UserContext.getUserData().getListBuuCucVeriable());
                return this.convertPaginateDataBC(doanhThuCPCN);
            }
        }

        if (maCN != null && !maCN.isEmpty()) {
            Pageable paging = PageRequest.of(pageQuery, pageSize);
            Page<TienDoDoanhThuCPCNV2ResDto> doanhThuCPCN =
                    doanhThuCPCNRepo.findTienDoDoanhThuBuuCucAndChiNhanh(
                            maCN, loaiDichVuCN, to_time, paging, this.exceptionChiNhanh);

            return this.convertPaginateDataBC(doanhThuCPCN);
        }

        Pageable paging = PageRequest.of(pageQuery, pageSize);
        Page<TienDoDoanhThuCPCNV2ResDto> doanhThuCPCN =
                doanhThuCPCNRepo.findTienDoDoanhThuCPCNFollowTime(
                        loaiDichVuCN, to_time, paging, this.exceptionChiNhanh);
        return this.convertPaginateDataBC(doanhThuCPCN);
    }


    public List<TienDoDoanhThuCNResDto> findAllDataCPCNLuyKe(LocalDate ngayBaoCao) {

        LocalDate currentdate = LocalDate.parse(LocalDate.now().minusDays(1).toString());
        YearMonth yearMonthObject = YearMonth.of(currentdate.getYear(), currentdate.getMonthValue());
        int daysInMonth = yearMonthObject.lengthOfMonth(); // tong ngay trong thang
        int currentDay = currentdate.getDayOfMonth(); // ngay n-1
        float luykeThangNow = (float) ((100.0 / daysInMonth) * currentDay);

        List<TienDoDoanhThuCNResDto> doanhThuCPCN = new ArrayList<>();

        if (isAdmin.equals("true")) {
            doanhThuCPCN = doanhThuCPCNRepo.findTienDoDoanhThuCPCNFollowTimeLuyKe(ngayBaoCao);
        } else {
            doanhThuCPCN = doanhThuCPCNRepo.findTienDoDoanhThuCPCNFollowTimeLuyKeSSO(ngayBaoCao, listChiNhanhVeriable);
        }

        HashMap<TienDoDoanhThuCNResDto, Float> res = new HashMap<>();

        if (doanhThuCPCN != null) {
            for (TienDoDoanhThuCNResDto dto : doanhThuCPCN) {
                dto.setLuyKeNow(luykeThangNow);
                res.put(dto, luykeThangNow);
            }
        }
        return doanhThuCPCN;
    }

    /**
     * Convert để customize dữ liệu trả về từ pagination
     *
     * @param pageResult
     * @return
     */
    private CustomizeDataPage<TienDoDoanhThuCPCNV2ResDto> convertPaginateDataBC(Page<TienDoDoanhThuCPCNV2ResDto> pageResult) {
        ListContentPageDto<TienDoDoanhThuCPCNV2ResDto> tienDoDTContentPage =
                new ListContentPageDto<>(pageResult);

        List<TienDoDoanhThuCPCNV2ResDto> tienDoDoanhThuCNV1ResDtos = tienDoDTContentPage.getContent();
        for (TienDoDoanhThuCPCNV2ResDto tienDoDetail : tienDoDoanhThuCNV1ResDtos) {
            if (maTinh.containsKey(tienDoDetail.getMaChiNhanh())) {
                tienDoDetail.setTenChiNhanh(this.maTinh.get(tienDoDetail.getMaChiNhanh()));
            }
            if (tienDoDetail.getKeHoach() == 0) {
                tienDoDetail.setKeHoach(null);
            }
        }

        CustomizeDataPage<TienDoDoanhThuCPCNV2ResDto> customizeDataPage = new CustomizeDataPage<>();
        customizeDataPage.setTotal((int) tienDoDTContentPage.getTotal());
        customizeDataPage.setOffset((int) tienDoDTContentPage.getOffset());
        customizeDataPage.setLimit(tienDoDTContentPage.getLimit());
        customizeDataPage.setContent(tienDoDTContentPage.getContent());

        return customizeDataPage;
    }

}
