package nocsystem.indexmanager.services.DieuHanhTon;

import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonHoanResponse;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonPhatResponse;
import nocsystem.indexmanager.models.Response.DieuHanhTon.DieuHanhTonThuResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

public interface DieuHanhTonService {
    Page<DieuHanhTonPhatResponse> getPhaiPhatDHTTongQuan(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, Pageable pageable);
    Page<DieuHanhTonPhatResponse> getPhaiPhatDHTChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, String sortKey, String sortType, Integer pageNumber, Integer pageSize);
    DieuHanhTonPhatResponse getPhaiPhatDHTTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac);
    void exportPhaiPhatDHTExcelTongHop(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response);
    void exportPhaiPhatDHTExcelChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response);
    Page<DieuHanhTonHoanResponse> getPhaiHoanDHTTongQuan(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, Pageable pageable);
    Page<DieuHanhTonHoanResponse> getPhaiHoanDHTChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, String sortKey, String sortType, Integer pageNumber, Integer pageSize);
    DieuHanhTonHoanResponse getPhaiHoanDHTTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac);
    void exportPhaiHoanDHTExcelTongHop(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response);
    void exportPhaiHoanDHTExcelChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response);
    Page<DieuHanhTonThuResponse> getPhaiThuDHTTongQuan(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, Pageable pageable);
    Page<DieuHanhTonThuResponse> getPhaiThuDHTChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, String sortKey, String sortType, Integer pageNumber, Integer pageSize);
    DieuHanhTonThuResponse getPhaiThuDHTTong(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac);
    void exportPhaiThuDHTExcelTongHop(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response);
    void exportPhaiThuDHTExcelChiTiet(LocalDate ngayBaoCao, Integer luyKe, String chiNhanh, String buuCuc, String maDoiTac, HttpServletResponse response);

}
