package nocsystem.indexmanager.services.DashboardTCT;

import nocsystem.indexmanager.models.DashboardTCT.leadtime.DashTCTLeadtimeResponse;
import nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTAllResponse;
import nocsystem.indexmanager.models.DashboardTCT.leadtime.LeadtimeChartTTTachKienTKResponse;

import java.time.LocalDate;
import java.util.List;

public interface LeadtimeTCTService {
    DashTCTLeadtimeResponse getDashTCTLeadtime(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe);
    List<LeadtimeChartTTAllResponse> getLeadtimeChartTTAll(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);
    List<LeadtimeChartTTTachKienTKResponse> getLeadtimeChartTTTachKienTK(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);

}
