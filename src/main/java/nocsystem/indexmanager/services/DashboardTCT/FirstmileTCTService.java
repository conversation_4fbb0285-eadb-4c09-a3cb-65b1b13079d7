package nocsystem.indexmanager.services.DashboardTCT;

import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashFirstmileTLThuTCDto;
import nocsystem.indexmanager.models.DashboardTCT.firstmile.DashTCTFirstmileDto;

import java.time.LocalDate;
import java.util.List;

public interface FirstmileTCTService {
    List<DashTCTFirstmileDto> getFirstmile(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe);
    List<DashFirstmileTLThuTCDto> getDashFirstmileTlThuTCNgay(LocalDate ngayBaoCao, String chiNhanh, String buuCuc);
}
