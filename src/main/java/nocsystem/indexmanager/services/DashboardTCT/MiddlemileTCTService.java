package nocsystem.indexmanager.services.DashboardTCT;

import nocsystem.indexmanager.models.DashboardTCT.middlemile.HieuQuaXeResponse;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.KetNoiResponse;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.KhaiThacResponse;
import nocsystem.indexmanager.models.DashboardTCT.middlemile.MiddleMileResponse;

import java.time.LocalDate;

public interface MiddlemileTCTService {
    KhaiThacResponse getKhaiThac(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe);
    KetNoiResponse getKetNoi(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe);
    HieuQuaXeResponse getHieuQuaXe(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe);
    MiddleMileResponse getMiddleMile(LocalDate ngayBaoCao, String chiNhanh, String buuCuc, Integer luyKe);

}
