package nocsystem.indexmanager.services.MatHanhTrinhTTKT;

import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.MatHanhTrinhDetailBodyDto;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.MatHanhTrinhListBodyDto;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.ResponseMatHanhTrinhDetailDto;
import nocsystem.indexmanager.models.Response.MatHanhTrinhTTKT.ResponseMatHanhTrinhTTKTList;
import org.springframework.data.domain.Page;

import java.sql.SQLException;

public interface MatHanhTrinhTTKTServices {
    public ResponseMatHanhTrinhTTKTList getList(MatHanhTrinhListBodyDto body);

    public Page<ResponseMatHanhTrinhDetailDto> getListDetail(MatHanhTrinhDetailBodyDto body) throws SQLException;

}
