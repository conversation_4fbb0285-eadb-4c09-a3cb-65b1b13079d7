package nocsystem.indexmanager.services.NocPro;

import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.models.Response.NocPro.ChiTietHieuQuaKDResponse;
import nocsystem.indexmanager.models.Response.NocPro.TongQuanHieuQuaKDResponse;

import java.time.LocalDate;
import java.util.List;

public interface DashMobileNocProService {
    TongQuanHieuQuaKDResponse getDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, String vungCon, String nhomKhachHang, String nhomDichVu, List<String> mucTrongLuong, String dichVu);
    ListContentPageDto<ChiTietHieuQuaKDResponse> getDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, String vungCon, String nhomKhachHang, String nhomDichVu, List<String> mucTrongLuong, String dichVu, String order, String orderBy, Integer page, Integer pageSize);
    ChiTietHieuQuaKDResponse getTotalDetailDashboard(LocalDate ngayBaoCao, String mien, String chiNhanh, String buuCuc, String vungCon, String nhomKhachHang, String nhomDichVu, List<String> mucTrongLuong, String dichVu);

}
