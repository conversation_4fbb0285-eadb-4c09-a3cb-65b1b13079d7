package nocsystem.indexmanager.services.baocao_taichinh.taiChinhCommon;

import nocsystem.indexmanager.models.baocao_taichinh.TaiChinhModel;

import java.util.HashMap;
import java.util.Map;

public class TaiChinhModelCommon {

    public static Map<Integer, String> CELL_TO_HEADER = new HashMap<Integer, String>();

    static {
        CELL_TO_HEADER.put(2, "Hợp nhất");
        CELL_TO_HEADER.put(3, "Báo cáo riêng công ty mẹ");
        CELL_TO_HEADER.put(4, "Công ty TMĐT");
        CELL_TO_HEADER.put(5, "Công ty công nghệ");
        CELL_TO_HEADER.put(6, "Công ty LOG");
        CELL_TO_HEADER.put(7, "Campuchia (Nguyên tệ)");

        CELL_TO_HEADER.put(8, "Campuchia (Chuyển đổi)");
        CELL_TO_HEADER.put(9, "Myanmar (Nguyên tệ)");
        CELL_TO_HEADER.put(10, "Myanmar (Chuyển đổi)");
    }

    public static Map<String, String> HEADER_TO_PROPERTIES = new HashMap<String, String>();

    static {
        HEADER_TO_PROPERTIES.put("Hợp nhất", "hop_nhat");
        HEADER_TO_PROPERTIES.put("Báo cáo riêng công ty mẹ", "cty_me");
        HEADER_TO_PROPERTIES.put("Công ty TMĐT", "cty_tmdt");
        HEADER_TO_PROPERTIES.put("Công ty công nghệ", "cty_congnghe");
        HEADER_TO_PROPERTIES.put("Công ty LOG", "cty_log");
        HEADER_TO_PROPERTIES.put("Campuchia (Nguyên tệ)", "campuchia_nguyente");
        HEADER_TO_PROPERTIES.put("Campuchia (Chuyển đổi)", "campuchia_chuyendoi");
        HEADER_TO_PROPERTIES.put("Myanmar (Nguyên tệ)", "myanmar_nguyente");
        HEADER_TO_PROPERTIES.put("Myanmar (Chuyển đổi)", "myanmar_chuyendoi");
    }

    public static Map<Integer, String> NUMBER_SHEET = new HashMap<Integer, String>();

    static {
        NUMBER_SHEET.put(0, "B01");
        NUMBER_SHEET.put(1, "B02");
        NUMBER_SHEET.put(2, "B03");
    }


    public static void setValueProperties(TaiChinhModel taiChinhModel , String nameProperty, Float valueProperty) {
        taiChinhModel.setProperty(nameProperty, valueProperty);
    }

}
