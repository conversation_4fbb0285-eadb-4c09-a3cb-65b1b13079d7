package nocsystem.indexmanager.services.baocao_taichinh.taiChinhCommon;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ShowBaoCaoCommon {

    public static Map<Integer, String> STT_CHITIEU = new HashMap<Integer, String>();
    //    public static Map<String, Boolean> CHITIEU_STT = new HashMap<String, Boolean>();
    public static List<String> CHITIEU_STT = new ArrayList<>();

    static {
        STT_CHITIEU.put(1, "ROCE (Return On Capital Employed)");
        STT_CHITIEU.put(2, "ROTC (Return On Total Capital)");
        STT_CHITIEU.put(3, "ROE (Return On Equity)");
        STT_CHITIEU.put(4, "ROA (Return On Assets)");
        STT_CHITIEU.put(5, "EBIT (Earnings Before Interest and Taxes)");
        STT_CHITIEU.put(6, "EBITDA (Earning Before Interest, Taxes, Depreciation and Amortization)");
        STT_CHITIEU.put(7, "ROS (Return On Sales)");
        STT_CHITIEU.put(8, "GPM (Gross Profit Margin)");
        STT_CHITIEU.put(9, "NPM (Net Profit Margin)");
        STT_CHITIEU.put(10, "DER (Debt to Equity Ratio)");
        STT_CHITIEU.put(11, "NWC (Net Working Capital)");
        STT_CHITIEU.put(12, "WCR (Working Capital Requirement)");
        STT_CHITIEU.put(13, "Tài sản kinh doanh");
        STT_CHITIEU.put(14, "Nợ kinh doanh");
        STT_CHITIEU.put(15, "Chỉ số thanh toán hiện hành (Current Ratio)");
        STT_CHITIEU.put(16, "Chỉ số thanh toán nhanh (Quick Ratio)");
        STT_CHITIEU.put(17, "Chỉ số thanh toán tức thời (Cash Ratio)");
        STT_CHITIEU.put(18, "Tỷ số khả năng thanh toán lãi vay (ICR: Interest Coverage Ratio)");
        STT_CHITIEU.put(19, "Vòng quay các khoản phải thu (Receivable Turnover Ratio)");
        STT_CHITIEU.put(20, "Kỳ thu tiền bình quân");
        STT_CHITIEU.put(21, "Vòng quay các khoản phải trả (Payable Turnover Ratio)");
        STT_CHITIEU.put(22, "Kỳ trả tiền bình quân");
        STT_CHITIEU.put(23, "Vòng quay hàng tồn kho (Inventory Turnover Ratio)");
        STT_CHITIEU.put(24, "Số ngày lưu kho bình quân");
        STT_CHITIEU.put(25, "Vòng quay tổng tài sản (Total Asset Turnover Ratio)");
        STT_CHITIEU.put(26, "ROI (Returns On Investment)");
    }


    static {
        CHITIEU_STT.add("ROCE (Return On Capital Employed)");
        CHITIEU_STT.add("ROTC (Return On Total Capital)");
        CHITIEU_STT.add("ROE (Return On Equity)");
        CHITIEU_STT.add("ROA (Return On Assets)");
        CHITIEU_STT.add("EBIT (Earnings Before Interest and Taxes)");
        CHITIEU_STT.add("EBITDA (Earning Before Interest, Taxes, Depreciation and Amortization)");
        CHITIEU_STT.add("ROS (Return On Sales)");
        CHITIEU_STT.add("GPM (Gross Profit Margin)");
        CHITIEU_STT.add("NPM (Net Profit Margin)");
        CHITIEU_STT.add("DER (Debt to Equity Ratio)");
        CHITIEU_STT.add("NWC (Net Working Capital)");
        CHITIEU_STT.add("WCR (Working Capital Requirement)");
        CHITIEU_STT.add("Tài sản kinh doanh");
        CHITIEU_STT.add("Nợ kinh doanh");
        CHITIEU_STT.add("Chỉ số thanh toán hiện hành (Current Ratio)");
        CHITIEU_STT.add("Chỉ số thanh toán nhanh (Quick Ratio)");
        CHITIEU_STT.add("Chỉ số thanh toán tức thời (Cash Ratio)");
        CHITIEU_STT.add("Tỷ số khả năng thanh toán lãi vay (ICR: Interest Coverage Ratio)");
        CHITIEU_STT.add("Vòng quay các khoản phải thu (Receivable Turnover Ratio)");
        CHITIEU_STT.add("Kỳ thu tiền bình quân");
        CHITIEU_STT.add("Vòng quay các khoản phải trả (Payable Turnover Ratio)");
        CHITIEU_STT.add("Kỳ trả tiền bình quân");
        CHITIEU_STT.add("Vòng quay hàng tồn kho (Inventory Turnover Ratio)");
        CHITIEU_STT.add("Số ngày lưu kho bình quân");
        CHITIEU_STT.add("Vòng quay tổng tài sản (Total Asset Turnover Ratio)");
        CHITIEU_STT.add("ROI (Returns On Investment)");
    }

//    static {
//        CHITIEU_STT.put("ROCE (Return On Capital Employed)" , true);
//        CHITIEU_STT.put("ROTC (Return On Total Capital)" , true);
//        CHITIEU_STT.put("ROE (Return On Equity)" , true);
//        CHITIEU_STT.put("ROA (Return On Assets)" , true);
//        CHITIEU_STT.put("EBIT (Earnings Before Interest and Taxes)" , true);
//        CHITIEU_STT.put("EBITDA (Earning Before Interest, Taxes, Depreciation and Amortization)" , true);
//        CHITIEU_STT.put("ROS (Return On Sales)" , true);
//        CHITIEU_STT.put("GPM (Gross Profit Margin)" , true);
//        CHITIEU_STT.put("NPM (Net Profit Margin)" , true);
//        CHITIEU_STT.put("DER (Debt to Equity Ratio)" , true);
//        CHITIEU_STT.put("NWC (Net Working Capital)" , true);
//        CHITIEU_STT.put("WCR (Working Capital Requirement)" , true);
//        CHITIEU_STT.put("Tài sản kinh doanh" , true);
//        CHITIEU_STT.put("Nợ kinh doanh" , true);
//        CHITIEU_STT.put("Chỉ số thanh toán hiện hành (Current Ratio)" , true);
//        CHITIEU_STT.put("Chỉ số thanh toán nhanh (Quick Ratio)" , true);
//        CHITIEU_STT.put("Chỉ số thanh toán tức thời (Cash Ratio)" , true);
//        CHITIEU_STT.put("Tỷ số khả năng thanh toán lãi vay (ICR: Interest Coverage Ratio)" , true);
//        CHITIEU_STT.put("Vòng quay các khoản phải thu (Receivable Turnover Ratio)" , true);
//        CHITIEU_STT.put("Kỳ thu tiền bình quân" , true);
//        CHITIEU_STT.put("Vòng quay các khoản phải trả (Payable Turnover Ratio)" , true);
//        CHITIEU_STT.put("Kỳ trả tiền bình quân" , true);
//        CHITIEU_STT.put("Vòng quay hàng tồn kho (Inventory Turnover Ratio)" , true);
//        CHITIEU_STT.put("Số ngày lưu kho bình quân" , true);
//        CHITIEU_STT.put("Vòng quay tổng tài sản (Total Asset Turnover Ratio)" , true);
//        CHITIEU_STT.put("ROI (Returns On Investment)" , true);
//    }
}
