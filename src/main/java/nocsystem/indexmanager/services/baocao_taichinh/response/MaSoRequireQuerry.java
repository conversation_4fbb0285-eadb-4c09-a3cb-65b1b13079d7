package nocsystem.indexmanager.services.baocao_taichinh.response;


import nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso.HandleChiSoDauTu;
import nocsystem.indexmanager.services.baocao_taichinh.taiChinhCommon.ShowBaoCaoCommon;

public class MaSoRequireQuerry {
    public static String maSoShowBaoCao =
            " '270' ,  " +
                    " '100' ,  " +
                    " '400' ,  " +
                    " '300' ,  " +
                    " '310' ,  " +
                    " '130' ,  " +
                    " '131' ,  " +
                    " '140' ,  " +
                    " '150' ,  " +
                    " '311' ,  " +
                    " '320' ,  " +
                    " '110' ,  " +
                    " '120' ,  " +
                    " '210' ,  " +
                    " '211' ,  " +
                    " '411' ,  " +

//                    // todo : B02
//                    " '10' ,  " +
//                    " '23' ,  " +
//                    " '60' ,  " +
//                    " '50' ,  " +
//                    " '01' ,  " +
//                    " '20' ,  " +
//                    " '11' ,  " +
//                    " '21' ,  " +
//                    " '31' ,  " +

                    // todo :B03
                    " '02' ";


    public static String maSoTableB02 =

            // todo : B02
            " '10' ,  " +
                    " '23' ,  " +
                    " '60' ,  " +
                    " '50' ,  " +
                    " '01' ,  " +
                    " '20' ,  " +
                    " '11' ,  " +
                    " '21' ,  " +
                    " '31'  ";

    public static String[] getMasoFromChiSoB01(String chiSo) {

        String[] str = new String[2];
        str[0] = " '2700' , '1000' ";
        str[1] = " '5000' , '2300' ";
        switch (chiSo) {
            case "1":
                str[0] = " '270' , '100' ";
                str[1] = " '50' , '23' ";
                return str;
            case "2":
                str[0] = " '270' ";
                str[1] = " '60' , '23' ";
                return str;
            case "3":
                str[0] = " '270' , '400' ";
                str[1] = " '60' ";
                return str;

            case "4":
                str[0] = " '270' ";
                str[1] = " '60' ";
                return str;

            case "5":
                str[0] = " '270' ";
                str[1] = " '50', '23' ";
                return str;
            case "6":
                str[0] = " '02' "; // todo : cua bang b03
                str[1] = " '50', '23' ";
                return str;
            case "7":
                str[0] = " '02' "; // todo: chi so nay thi thang nay ko can
                str[1] = " '60', '01', '21', '31' ";
                return str;
            case "8":
                str[0] = " '02' "; // todo: chi so nay thi thang nay ko can
                str[1] = " '20', '10' ";
                return str;
            case "9":
                str[0] = " '02' "; // todo: chi so nay thi thang nay ko can
                str[1] = " '60', '10' ";
                return str;
            case "10":
                str[0] = " '300', '400' ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "11":
                str[0] = " '310', '100' ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "12":
                str[0] = " '130', '140' , '150' , '311', '310', '320' ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "13":
                str[0] = " '130', '140' , '150'  ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "14":
                str[0] = "  '311', '310', '320' ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "15":
                str[0] = "  '100', '310' ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "16":
                str[0] = "  '100', '310', '140' ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "17":
                str[0] = "  '110', '310', '120' ";
                str[1] = " '60', '10' ";  // todo: chi so nay thi thang nay ko can
                return str;
            case "18":
                str[0] = " '270' "; // todo: chi so nay thi thang nay ko can
                str[1] = " '50', '23' ";
                return str;
            case "19":
                str[0] = " '131', '211' ";
                str[1] = " '01' ";
                return str;
            case "20":
                str[0] = " '131', '211' ";
                str[1] = " '01' ";
                return str;
            case "21":
                str[0] = " '311' ";
                str[1] = "  '11' ";
                return str;
            case "22":
                str[0] = " '300' ";
                str[1] = "  '11' ";
                return str;
            case "23":
                str[0] = " '140' ";
                str[1] = "  '11' ";
                return str;
            case "24":
                str[0] = " '140' ";
                str[1] = "  '11' ";
                return str;
            case "25":
                str[0] = " '270' ";
                str[1] = "  '01', '21', '31' ";
                return str;
            case "26":
                str[0] = " '411' ";
                str[1] = "  '60' ";
                return str;
        }
        return str;
    }
}

