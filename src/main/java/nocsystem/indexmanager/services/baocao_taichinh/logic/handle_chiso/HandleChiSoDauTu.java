package nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso;

import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.CacChiSoSoSanhCommon;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.ChiSoDauTu;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoCommon;

import java.util.Map;

public class HandleChiSoDauTu {

    public static BaoCaoCommon getChiSoROI(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float maSoLoiNhuanSauThue_B02_60 = baoCaoTcHienTai.get("60_B02");
        Float vonDauTu_B01_411 = baoCaoTcHienTai.get("411_B01");
        Float chiSoThucHien = ChiSoDauTu.getROI(maSoLoiNhuanSauThue_B02_60, vonDauTu_B01_411);

        Float chiSoKHThang = keHoachTcHienTai.get("ROI (Returns On Investment)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);

        Float maSoLoiNhuanSauThue_B02_60_CK = baoCaoTcTheoNamN1.get("60_B02");
        Float vonDauTu_B01_411_CK = baoCaoTcTheoNamN1.get("411_B01");
        Float chiSoThucHienCK = ChiSoDauTu.getROI(maSoLoiNhuanSauThue_B02_60_CK, vonDauTu_B01_411_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoLoiNhuanSauThue_B02_60_KyTruoc = baoCaoTcKyTruoc.get("60_B02");
        Float vonDauTu_B01_411_KyTruoc = baoCaoTcKyTruoc.get("411_B01");
        Float chiSoThucHienKyTruoc = ChiSoDauTu.getROI(maSoLoiNhuanSauThue_B02_60_KyTruoc, vonDauTu_B01_411_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienKyTruoc);

        // todo : chỉ số so sánh buu cuc ngay 3
        Float chiSoNgay3 = keHoachTcHienTai.get("ROI (Returns On Investment)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }




}
