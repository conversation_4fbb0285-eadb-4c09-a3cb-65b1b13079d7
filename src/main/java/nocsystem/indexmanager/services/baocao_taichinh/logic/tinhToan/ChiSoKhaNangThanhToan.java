package nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan;


import nocsystem.indexmanager.common.CalculateCommon;

public class ChiSoKhaNangThanhToan {

    public static Float getChiSoThanhToanHienHanh(Float maSoTaiSanNganHan_B01_100, Float maSoNoNganHan_B01_310) {
        if(maSoTaiSanNganHan_B01_100 == null || maSoNoNganHan_B01_310 == null || maSoNoNganHan_B01_310 == 0) return null;
        return maSoTaiSanNganHan_B01_100 / maSoNoNganHan_B01_310;
    }

    public static Float getChiSoThanhToanNhanh(Float maSoTaiSanNganHan_B01_100, Float maSoHangTonKho_B01_140, Float maSoNoNganHan_B01_310) {
        if(maSoNoNganHan_B01_310 == null || maSoNoNganHan_B01_310 == 0) return null;
        return (CalculateCommon.sub2Nums(maSoTaiSanNganHan_B01_100, maSoHangTonKho_B01_140)) /  maSoNoNganHan_B01_310;
    }

    public static Float getChiSoThanhToanTucThoi(Float maSoTienVaCacKhoanTuongDuongTien_B01_110, Float maSoDauTuChinhNganHan_B01_120, Float maSoNoNganHan_B01_310) {
        if(maSoNoNganHan_B01_310 == null || maSoNoNganHan_B01_310 == 0) return null;
        return (CalculateCommon.sum2Nums(maSoTienVaCacKhoanTuongDuongTien_B01_110, maSoDauTuChinhNganHan_B01_120)) /  maSoNoNganHan_B01_310;
    }

    public static Float getTySoKhaNangThanhToanLaiVay(Float giaTriEBIT_TinhTuChiSoHoatDong, Float chiPhiLaiVay_B02_23) {
        if(chiPhiLaiVay_B02_23 == null || chiPhiLaiVay_B02_23 == 0) return null;
        return giaTriEBIT_TinhTuChiSoHoatDong / chiPhiLaiVay_B02_23;
    }
}
