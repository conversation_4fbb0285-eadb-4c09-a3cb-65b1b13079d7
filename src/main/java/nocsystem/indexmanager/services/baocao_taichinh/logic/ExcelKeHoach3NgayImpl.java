package nocsystem.indexmanager.services.baocao_taichinh.logic;


import nocsystem.indexmanager.common.StringCommon;
import nocsystem.indexmanager.models.baocao_taichinh.KeHoach3NgayModel;
import nocsystem.indexmanager.repositories.baocao_taichinh.KeHoachRepo;
import nocsystem.indexmanager.services.baocao_taichinh.KeHoachServices;
import nocsystem.indexmanager.services.baocao_taichinh.taiChinhCommon.KeHoachCommon;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class ExcelKeHoach3NgayImpl implements KeHoachServices {
    public static int NUMBER_HEADER = 13;   // todo : số cột cần đọc dữ liệu

    private static final Logger logger = LoggerFactory.getLogger(ExcelKeHoach3NgayImpl.class);

    @Autowired
    KeHoachTaiChinhProcessRepo  keHoachTaiChinhProcessRepo;

    public List<KeHoach3NgayModel> getKeHoach3Ngay(int numberSheet, Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(numberSheet);
        Iterator<Row> rowIterator = sheet.iterator();

        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
        List<KeHoach3NgayModel> list = new ArrayList<>();
        rowIterator.next();
        while (rowIterator.hasNext()) {

            Row row = rowIterator.next();
            String chiTieu = row.getCell(1).getStringCellValue();
            String tanSuat = row.getCell(2).getStringCellValue();
            int ky;
            try {
                ky = (int) row.getCell(3).getNumericCellValue();
            }catch (Exception e){
                continue;
            }

            if(tanSuat == null || tanSuat.isEmpty()){
                continue;
            }
            KeHoach3NgayModel keHoachModel = new KeHoach3NgayModel();

            for (int i = 4; i < NUMBER_HEADER; i++) {
                Cell cell = row.getCell(i);
                Float value = null;
                if (cell.getCellType() == CellType.FORMULA) {
                    CellValue cellValue = evaluator.evaluate(cell);

                    switch (cellValue.getCellType()) {
                        case NUMERIC:
                            value = (float) cellValue.getNumberValue();
                            break;
                        case STRING:
                            value = Float.parseFloat(cellValue.getStringValue());
                            break;
                        case BOOLEAN:
                            break;
                        case ERROR:
                            break;
                        default:
                            break;
                    }
                } else if (cell.getCellType() == CellType.STRING) {
                    value = Float.parseFloat(cell.getStringCellValue());
                } else if (cell.getCellType() == CellType.NUMERIC) {
                    value = (float) cell.getNumericCellValue();
                }

                KeHoachCommon.setValueProperties(
                        keHoachModel,
                        KeHoachCommon.HEADER_TO_PROPERTIES.get(KeHoachCommon.CELL_TO_HEADER.get(i)),
                        value);

            }


            chiTieu = StringCommon.normalizeString(chiTieu);
            keHoachModel.setChi_tieu(chiTieu);
            keHoachModel.setKy(ky);
            keHoachModel.setTan_suat(tanSuat);
            keHoachModel.setLoai_bang(KeHoachCommon.NUMBER_SHEET.get(numberSheet));

            list.add(keHoachModel);

        }
        return list;
    }



    @Override
    public void readExcelKeHoach(MultipartFile file) {

        try {
            InputStream inputStream = file.getInputStream();
            Workbook workbook = new XSSFWorkbook(inputStream);

            List<KeHoach3NgayModel> list_01 = getKeHoach3Ngay(0, workbook);
            List<KeHoach3NgayModel> list_02 = getKeHoach3Ngay(1, workbook);

            keHoachTaiChinhProcessRepo.insertStudentsBatch(list_01);
            keHoachTaiChinhProcessRepo.insertStudentsBatch(list_02);

        } catch (IOException e) {
            logger.info("Lỗi khi lưu danh sách kế hoạch 3 ngày: {}", e);
        }
    }
}