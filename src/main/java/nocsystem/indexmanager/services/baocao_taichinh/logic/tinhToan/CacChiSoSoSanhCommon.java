package nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan;


import nocsystem.indexmanager.common.CalculateCommon;

public class CacChiSoSoSanhCommon {

    public static Float soSanhKeHoach(Float soThuc<PERSON>ien, Float soKeHoach){
        return CalculateCommon.div2Nums(soThucHien, soKeHoach);
    }

    public static Float soSanhCungKy(Float soThucHienHienTai, Float soThucHienCungKy){
        return CalculateCommon.div2Nums(CalculateCommon.sub2Nums(soThucHienHienTai, soThucHienCungKy), soThucHienCungKy);
    }


    public static Float soSanhKyTruoc(Float soThucHienKyHienTai, Float soThucHienKyTruoc){
        return CalculateCommon.div2Nums(CalculateCommon.sub2Nums(soThucHienKyHienTai, soThucHienKyTruoc), soThucHienKyTruoc);
    }

    public static Float soSanhBcNgay3(Float soThuc<PERSON>ien, Float soBaoCaoNgay3){
        return CalculateCommon.div2Nums(soThucHien, soBaoCaoNgay3);
    }
}
