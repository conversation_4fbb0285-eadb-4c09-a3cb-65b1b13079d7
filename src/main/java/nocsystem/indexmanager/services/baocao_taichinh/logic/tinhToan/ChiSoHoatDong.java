package nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan;


import nocsystem.indexmanager.common.CalculateCommon;

public class ChiSoHoatDong {
    public static Float getChiSoEBIT(Float loiNhuanTruocThue_B02_50, Float chiPhiLaiVay_B02_23) {
        return CalculateCommon.sum2Nums(loiNhuanTruocThue_B02_50, chiPhiLaiVay_B02_23);
    }
    public static Float getChiSoEBITDA(Float loiNhuanTruocThue_B02_50, Float chiPhiLaiVay_B02_23, Float chiPhiKhauHao_B03_02) {
        return CalculateCommon.sum2Nums(CalculateCommon.sum2Nums(loiNhuanTruocThue_B02_50, chiPhiLaiVay_B02_23), chiPhiKhauHao_B03_02) ;
    }

    public static Float getChiSoROS(Float loiNhuanSauThue_B02_60, Float tongDoanhThu_B02_01,  Float tongDoanhThu_B02_21,  Float tongDoanhThu_B02_31) {
        Float sum = CalculateCommon.sum3Nums(tongDoanhThu_B02_01, tongDoanhThu_B02_21, tongDoanhThu_B02_31);
        if(loiNhuanSauThue_B02_60 == null ||  sum == 0) return null;
        return  (loiNhuanSauThue_B02_60 / sum);
    }

    public static Float getChiSoGPM(Float loiNhuanGop_B02_20, Float doanhThuThuan_B02_10) {
        if(loiNhuanGop_B02_20 == null || doanhThuThuan_B02_10 == null || doanhThuThuan_B02_10 == 0) return null;
        return (loiNhuanGop_B02_20 / doanhThuThuan_B02_10);
    }

    public static Float getChiSoNPM(Float loiNhuanDong_B02_60, Float doanhThuThuan_B02_10) {
        if(loiNhuanDong_B02_60 == null || doanhThuThuan_B02_10 == null || doanhThuThuan_B02_10 == 0) return null;
        return  (loiNhuanDong_B02_60 / doanhThuThuan_B02_10);
    }

}
