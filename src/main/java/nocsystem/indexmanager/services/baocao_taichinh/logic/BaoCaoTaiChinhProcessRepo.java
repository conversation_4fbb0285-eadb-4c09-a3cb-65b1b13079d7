package nocsystem.indexmanager.services.baocao_taichinh.logic;

import nocsystem.indexmanager.models.baocao_taichinh.TaiChinhModel;
import nocsystem.indexmanager.repositories.baocao_taichinh.BaoCaoTaiChinhRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class BaoCaoTaiChinhProcessRepo {
    @Autowired
    BaoCaoTaiChinhRepo baoCaoTaiChinhRepo;

    @Transactional
    public void insertStudentsBatch(List<TaiChinhModel> taiChinhModels) {
        baoCaoTaiChinhRepo.saveAll(taiChinhModels); // Lưu tất cả dữ liệu
    }

    public List<TaiChinhModel> getDataFollowMasoThangLoaiBang(List<String> listMaSo, Integer thang, List<String> loaiBang){
        return baoCaoTaiChinhRepo.findByMaSoAndNgayBaoCaoAndLoaiBangIn(listMaSo, thang, loaiBang);
    }

}
