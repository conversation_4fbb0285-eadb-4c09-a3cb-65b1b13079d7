package nocsystem.indexmanager.services.baocao_taichinh.logic;


import nocsystem.indexmanager.common.StringCommon;
import nocsystem.indexmanager.models.baocao_taichinh.TaiChinhModel;
import nocsystem.indexmanager.repositories.baocao_taichinh.BaoCaoTaiChinhRepo;
import nocsystem.indexmanager.services.baocao_taichinh.TaiChinhServices;
import nocsystem.indexmanager.services.baocao_taichinh.taiChinhCommon.TaiChinhModelCommon;
import org.apache.poi.ss.usermodel.*;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Service
public class ExcelBaoCaoTaiChinhImpl implements TaiChinhServices {

    public static int NUMBER_ROW_HEADER = 2;  // todo : hàng chứa header thì bỏ qua ko lấy giá trị
    public static int NUMBER_HEADER = 11;   // todo : số cột cần đọc dữ liệu


    @Autowired
    BaoCaoTaiChinhProcessRepo baoCaoTaiChinhProcessRepo;


    public List<TaiChinhModel> getTaiChinh(int numberSheet, Workbook workbook) {

        Sheet sheet = workbook.getSheetAt(numberSheet);
        Iterator<Row> rowIterator = sheet.iterator();

        String ngayBaoCao = rowIterator.next().getCell(0).getStringCellValue();
        int thang = StringCommon.convertDateStringToInt(ngayBaoCao);

        if (StringUtils.isEmpty(ngayBaoCao)) {
            return new ArrayList<>();
        }
        rowIterator.next(); // todo : đoạn này để nó bỏ qua row của header
        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
        List<TaiChinhModel> taiChinhModels = new ArrayList<>();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            String maSo = "";
            if (row.getCell(1).getCellType() == CellType.STRING) {
                maSo = row.getCell(1).getStringCellValue();
            } else if (row.getCell(1).getCellType() == CellType.NUMERIC) {
                maSo = row.getCell(1).getNumericCellValue() + "";
            }
            if (StringUtils.isEmpty(maSo)) continue;

            maSo = StringCommon.processString(maSo);
            TaiChinhModel taiChinhModel = new TaiChinhModel();
            for (int i = 2; i < NUMBER_HEADER; i++) {
                Cell cell = row.getCell(i);
                Float value = null;
                if (cell.getCellType() == CellType.FORMULA) {
                    CellValue cellValue = evaluator.evaluate(cell);

                    switch (cellValue.getCellType()) {
                        case NUMERIC:
                            value = (float) cellValue.getNumberValue();
                            break;
                        case STRING:
                            value = Float.parseFloat(cellValue.getStringValue());
                            break;
                        case BOOLEAN:
                            break;
                        case ERROR:
                            break;
                        default:
                            break;
                    }
                } else if (cell.getCellType() == CellType.STRING) {
                    value = Float.parseFloat(cell.getStringCellValue());
                } else if (cell.getCellType() == CellType.NUMERIC) {
                    value = (float) cell.getNumericCellValue();
                }

                TaiChinhModelCommon.setValueProperties(
                        taiChinhModel,
                        TaiChinhModelCommon.HEADER_TO_PROPERTIES.get(TaiChinhModelCommon.CELL_TO_HEADER.get(i)), // todo : tên thuộc tính đóng vào entity
                        value);

            }

            taiChinhModel.setThang(thang);
            taiChinhModel.setMa_so(maSo);
            taiChinhModel.setNgay_baocao(ngayBaoCao);
            taiChinhModel.setLoai_bang(TaiChinhModelCommon.NUMBER_SHEET.get(numberSheet)); // todo : thuộc sheet nào

            taiChinhModels.add(taiChinhModel);
        }
        return taiChinhModels;
    }

    // todo : cahc lam la lay ra list ma so , ngay thi chung 1 ngay tu excel roi, con loai bang ( b01, b02 ) ma so loai bang nay cung khac nhau do lay theo the la duoc
    //        select * from bao_cao_tai_chinh where ma_so in( list ma so ) and ngay_baocao = ' ngay tu file excel ' and loai_bang in ('B01' , 'B02')



    public List<TaiChinhModel> processRowExist(List<TaiChinhModel> listDataExcel){
        if(listDataExcel == null || listDataExcel.isEmpty()) return new ArrayList<>();
        Integer thang = null;
        List<String> listMaSo = new ArrayList<>();

        for(TaiChinhModel o : listDataExcel){
            thang = o.getThang();
            listMaSo.add(o.getMa_so());

        }
        List<String> listLoaiBang = new ArrayList<>();
        listLoaiBang.add("B01");
        listLoaiBang.add("B02");

        List<TaiChinhModel> listDataFromMaSo = baoCaoTaiChinhProcessRepo.getDataFollowMasoThangLoaiBang(listMaSo, thang, listLoaiBang);
        List<TaiChinhModel> listDataTmp = new ArrayList<>();
        if(!(listDataFromMaSo == null || listDataFromMaSo.isEmpty())){
            Map<String, TaiChinhModel> dict = new HashMap<>();
            for(TaiChinhModel o : listDataFromMaSo){
                String key = o.getMa_so() + "_" + o.getThang() + "_" + o.getLoai_bang();
                dict.put(key, o);
            }

            for(TaiChinhModel o : listDataExcel){
                String key = o.getMa_so() + "_" + o.getThang() + "_" + o.getLoai_bang();
                if(dict.containsKey(key)){
                    o.setCty_me(dict.get(key).getCty_me());
                    o.setCty_tmdt(dict.get(key).getCty_tmdt());
                    o.setCty_congnghe(dict.get(key).getCty_congnghe());
                    o.setCty_log(dict.get(key).getCty_log());
                }
                listDataTmp.add(o);
            }
            return listDataTmp;
        }
        else {
            for(TaiChinhModel o : listDataExcel){
                o.setCty_me(null);
                o.setCty_tmdt(null);
                o.setCty_congnghe(null);
                o.setCty_log(null);
                listDataTmp.add(o);
            }
            return listDataTmp;
        }
    }

    @Override
    public void readExcelTaiChinh(MultipartFile file) {

        try {
            InputStream inputStream = file.getInputStream();
            Workbook workbook = new XSSFWorkbook(inputStream);


            // todo : doan nay nghiep vu chi lay tu cho Chien sheet 0 vs sheet 1
            List<TaiChinhModel> list_01 = processRowExist(getTaiChinh(0, workbook));
            List<TaiChinhModel> l = getTaiChinh(1, workbook);
            List<TaiChinhModel> list_02 = processRowExist(getTaiChinh(1, workbook));
            List<TaiChinhModel> list_03 = getTaiChinh(2, workbook);

            baoCaoTaiChinhProcessRepo.insertStudentsBatch(list_01);
            baoCaoTaiChinhProcessRepo.insertStudentsBatch(list_02);
            baoCaoTaiChinhProcessRepo.insertStudentsBatch(list_03);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
