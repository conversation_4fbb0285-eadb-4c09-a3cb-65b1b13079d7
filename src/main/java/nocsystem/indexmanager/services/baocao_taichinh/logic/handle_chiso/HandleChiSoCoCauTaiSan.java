package nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso;

import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.CacChiSoSoSanhCommon;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.ChiSoCoCauTaiSan;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoCommon;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HandleChiSoCoCauTaiSan {

    public BaoCaoCommon getChiSoDER(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float noPhaiTra_B01_300 = baoCaoTcHienTai.get("300_B01");
        Float vonChuSoHuu_B01_400 = baoCaoTcHienTai.get("400_B01");
        Float chiSoThucHien = ChiSoCoCauTaiSan.getChiSoDER(noPhaiTra_B01_300, vonChuSoHuu_B01_400);

        Float chiSoKHThang = keHoachTcHienTai.get("DER (Debt to Equity Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float noPhaiTra_B01_300_CK = baoCaoTcTheoNamN1.get("300_B01");
        Float vonChuSoHuu_B01_400_CK = baoCaoTcTheoNamN1.get("400_B01");
        Float chiSoThucHienCK = ChiSoCoCauTaiSan.getChiSoDER(noPhaiTra_B01_300_CK, vonChuSoHuu_B01_400_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float noPhaiTra_B01_300_KyTruoc = baoCaoTcKyTruoc.get("300_B01");
        Float vonChuSoHuu_B01_400_KyTruoc = baoCaoTcKyTruoc.get("400_B01");
        Float chiSoThucHienKyTruoc = ChiSoCoCauTaiSan.getChiSoDER(noPhaiTra_B01_300_KyTruoc, vonChuSoHuu_B01_400_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("DER (Debt to Equity Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoNWC(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float taiSanNgan_B01_100 = baoCaoTcHienTai.get("100_B01");
        Float noNganHan_B01_310 = baoCaoTcHienTai.get("310_B01");
        Float chiSoThucHien = ChiSoCoCauTaiSan.getChiSoNWC(taiSanNgan_B01_100, noNganHan_B01_310);

        Float chiSoKHThang = keHoachTcHienTai.get("NWC (Net Working Capital)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float taiSanNgan_B01_100_CK = baoCaoTcTheoNamN1.get("100_B01");
        Float noNganHan_B01_310_CK = baoCaoTcTheoNamN1.get("310_B01");
        Float chiSoThucHienCK = ChiSoCoCauTaiSan.getChiSoNWC(taiSanNgan_B01_100_CK, noNganHan_B01_310_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float taiSanNgan_B01_100_KyTruoc = baoCaoTcKyTruoc.get("100_B01");
        Float noNganHan_B01_310_KyTruoc = baoCaoTcKyTruoc.get("310_B01");
        Float chiSoThucHienKyTruoc = ChiSoCoCauTaiSan.getChiSoNWC(taiSanNgan_B01_100_KyTruoc, noNganHan_B01_310_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("NWC (Net Working Capital)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }



    public  BaoCaoCommon getChiSoTaiSanKinhDoanh(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float maSoPhaiThuNganHan_B01_130 = baoCaoTcHienTai.get("130_B01");
        Float maSoHangTonKho_B01_140 = baoCaoTcHienTai.get("140_B01");
        Float maSoTaiSanNganHan_B01_150 = baoCaoTcHienTai.get("150_B01");
        Float chiSoThucHien = ChiSoCoCauTaiSan.getTaiSanKinhDoanh(maSoPhaiThuNganHan_B01_130, maSoHangTonKho_B01_140, maSoTaiSanNganHan_B01_150);

        Float chiSoKHThang = keHoachTcHienTai.get("Tài sản kinh doanh_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float maSoPhaiThuNganHan_B01_130_CK = baoCaoTcTheoNamN1.get("130_B01");
        Float maSoHangTonKho_B01_140_CK = baoCaoTcTheoNamN1.get("140_B01");
        Float maSoTaiSanNganHan_B01_150_CK = baoCaoTcTheoNamN1.get("150_B01");
        Float chiSoThucHienCK = ChiSoCoCauTaiSan.getTaiSanKinhDoanh(maSoPhaiThuNganHan_B01_130_CK, maSoHangTonKho_B01_140_CK, maSoTaiSanNganHan_B01_150_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoPhaiThuNganHan_B01_130_KyTruoc = baoCaoTcKyTruoc.get("130_B01");
        Float maSoHangTonKho_B01_140_KyTruoc = baoCaoTcKyTruoc.get("140_B01");
        Float maSoTaiSanNganHan_B01_150_KyTruoc = baoCaoTcKyTruoc.get("150_B01");
        Float chiSoThucHienKyTruoc = ChiSoCoCauTaiSan.getTaiSanKinhDoanh(maSoPhaiThuNganHan_B01_130_KyTruoc, maSoHangTonKho_B01_140_KyTruoc, maSoTaiSanNganHan_B01_150_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Tài sản kinh doanh_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoNoKinhDoanh(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float maSoNoNganHan_B01_310 = baoCaoTcHienTai.get("310_B01");
        Float maSoVayVaNoThueTaiChinhNganHan_B01_320 = baoCaoTcHienTai.get("320_B01");
        Float chiSoThucHien = ChiSoCoCauTaiSan.getNoKinhDoanh(maSoNoNganHan_B01_310, maSoVayVaNoThueTaiChinhNganHan_B01_320);

        Float chiSoKHThang = keHoachTcHienTai.get("Nợ kinh doanh_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float maSoNoNganHan_B01_310_CK = baoCaoTcTheoNamN1.get("310_B01");
        Float maSoVayVaNoThueTaiChinhNganHan_B01_320_CK = baoCaoTcTheoNamN1.get("320_B01");
        Float chiSoThucHienCK = ChiSoCoCauTaiSan.getNoKinhDoanh(maSoNoNganHan_B01_310_CK, maSoVayVaNoThueTaiChinhNganHan_B01_320_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoNoNganHan_B01_310_KyTruoc = baoCaoTcKyTruoc.get("310_B01");
        Float maSoVayVaNoThueTaiChinhNganHan_B01_320_KyTruoc = baoCaoTcKyTruoc.get("320_B01");
        Float chiSoThucHienKyTruoc = ChiSoCoCauTaiSan.getNoKinhDoanh(maSoNoNganHan_B01_310_KyTruoc, maSoVayVaNoThueTaiChinhNganHan_B01_320_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Nợ kinh doanh_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }

    public  BaoCaoCommon getChiSoWCR(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){
        BaoCaoCommon baoCaoTaiSanKinhDoanh = getChiSoTaiSanKinhDoanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc);
        BaoCaoCommon baoCaoNoKinhDoanh = getChiSoNoKinhDoanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc);
        Float chiSoThucHien = ChiSoCoCauTaiSan.getChiSoWCR(baoCaoTaiSanKinhDoanh.getThucHien(), baoCaoNoKinhDoanh.getThucHien());

        Float chiSoKHThang = keHoachTcHienTai.get("WCR (Working Capital Requirement)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);

        Float chiSoThucHienCK = ChiSoCoCauTaiSan.getChiSoWCR(baoCaoTaiSanKinhDoanh.getThucHienCK(), baoCaoNoKinhDoanh.getThucHienCK());
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);

        Float chiSoThucHienKyTruoc = ChiSoCoCauTaiSan.getChiSoWCR(baoCaoTaiSanKinhDoanh.getThucHienKyTruoc(), baoCaoNoKinhDoanh.getThucHienKyTruoc());
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("WCR (Working Capital Requirement)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }

}
