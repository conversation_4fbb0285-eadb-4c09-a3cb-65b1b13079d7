package nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso;


import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.CacChiSoSoSanhCommon;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.ChiSoHoatDong;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoCommon;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HandleChiSoHoatDong {

    public BaoCaoCommon getChiSoEBIT(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc){

        Float loiNhuanTruocThue_B02_50 = baoCaoTcHienTai.get("50_B02");
        Float chiPhiLaiVay_B02_23 = baoCaoTcHienTai.get("23_B02");
        Float chiSoThucHien = ChiSoHoatDong.getChiSoEBIT(loiNhuanTruocThue_B02_50, chiPhiLaiVay_B02_23);

        Float chiSoKHThang = keHoachTcHienTai.get("EBIT (Earnings Before Interest and Taxes)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float loiNhuanTruocThue_B02_50_CK = baoCaoTcTheoNamN1.get("50_B02");
        Float chiPhiLaiVay_B02_23_CK = baoCaoTcTheoNamN1.get("23_B02");
        Float chiSoThucHienCK = ChiSoHoatDong.getChiSoEBIT(loiNhuanTruocThue_B02_50_CK, chiPhiLaiVay_B02_23_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float loiNhuanSauThue_B02_50_KyTruoc = baoCaoTcKyTruoc.get("50_B02");
        Float chiPhiLaiVay_B02_23_KyTruoc = baoCaoTcKyTruoc.get("23_B02");
        Float chiSoThucHienKyTruoc = ChiSoHoatDong.getChiSoEBIT(loiNhuanSauThue_B02_50_KyTruoc, chiPhiLaiVay_B02_23_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("EBIT (Earnings Before Interest and Taxes)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoEBITDA(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float loiNhuanTruocThue_B02_50 = baoCaoTcHienTai.get("50_B02");
        Float chiPhiLaiVay_B02_23 = baoCaoTcHienTai.get("23_B02");
        Float chiPhiKhauHao_B03_02 = baoCaoTcHienTai.get("02_B03");
        Float chiSoThucHien = ChiSoHoatDong.getChiSoEBITDA(loiNhuanTruocThue_B02_50, chiPhiLaiVay_B02_23, chiPhiKhauHao_B03_02);

        Float chiSoKHThang = keHoachTcHienTai.get("EBITDA (Earning Before Interest, Taxes, Depreciation and Amortization)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float loiNhuanTruocThue_B02_50_CK = baoCaoTcTheoNamN1.get("50_B02");
        Float chiPhiLaiVay_B02_23_CK = baoCaoTcTheoNamN1.get("23_B02");
        Float chiPhiKhauHao_B03_02_CK = baoCaoTcTheoNamN1.get("02_B03");
        Float chiSoThucHienCK = ChiSoHoatDong.getChiSoEBITDA(loiNhuanTruocThue_B02_50_CK, chiPhiLaiVay_B02_23_CK, chiPhiKhauHao_B03_02_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float loiNhuanTruocThue_B02_50_KyTruoc = baoCaoTcKyTruoc.get("50_B02");
        Float chiPhiLaiVay_B02_23_KyTruoc = baoCaoTcKyTruoc.get("23_B02");
        Float chiPhiKhauHao_B03_02_KyTruoc = baoCaoTcKyTruoc.get("02_B03");
        Float chiSoThucHienKyTruoc = ChiSoHoatDong.getChiSoEBITDA(loiNhuanTruocThue_B02_50_KyTruoc, chiPhiLaiVay_B02_23_KyTruoc, chiPhiKhauHao_B03_02_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);


        Float chiSoNgay3 = keHoachTcHienTai.get("EBITDA (Earning Before Interest, Taxes, Depreciation and Amortization)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }

    public  BaoCaoCommon getChiSoROS(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float loiNhuanSauThue_B02_60 = baoCaoTcHienTai.get("60_B02");
        Float tongDoanhThu_B02_01 = baoCaoTcHienTai.get("01_B02");
        Float tongDoanhThu_B02_21 = baoCaoTcHienTai.get("21_B02");
        Float tongDoanhThu_B02_31 = baoCaoTcHienTai.get("31_B02");
        Float chiSoThucHien = ChiSoHoatDong.getChiSoROS(loiNhuanSauThue_B02_60, tongDoanhThu_B02_01, tongDoanhThu_B02_21, tongDoanhThu_B02_31);

        Float chiSoKHThang = keHoachTcHienTai.get("ROS (Return On Sales)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);

        Float loiNhuanTruocThue_B02_60_CK = baoCaoTcTheoNamN1.get("60_B02");
        Float tongDoanhThu_B02_01_CK = baoCaoTcTheoNamN1.get("01_B02");
        Float tongDoanhThu_B02_21_CK = baoCaoTcTheoNamN1.get("21_B02");
        Float tongDoanhThu_B02_31_CK = baoCaoTcTheoNamN1.get("31_B02");
        Float chiSoThucHienCK = ChiSoHoatDong.getChiSoROS(loiNhuanTruocThue_B02_60_CK, tongDoanhThu_B02_01_CK, tongDoanhThu_B02_21_CK, tongDoanhThu_B02_31_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float loiNhuanTruocThue_B02_60_KyTruoc = baoCaoTcKyTruoc.get("60_B02");
        Float tongDoanhThu_B02_01_KyTruoc = baoCaoTcKyTruoc.get("01_B02");
        Float tongDoanhThu_B02_21_KyTruoc = baoCaoTcKyTruoc.get("21_B02");
        Float tongDoanhThu_B02_31_KyTruoc = baoCaoTcKyTruoc.get("31_B02");
        Float chiSoThucHienKyTruoc = ChiSoHoatDong.getChiSoROS(loiNhuanTruocThue_B02_60_KyTruoc, tongDoanhThu_B02_01_KyTruoc, tongDoanhThu_B02_21_KyTruoc, tongDoanhThu_B02_31_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("ROS (Return On Sales)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoGPM(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float loiNhuanGop_B02_20 = baoCaoTcHienTai.get("20_B02");
        Float doanhThuThuan_B02_10 = baoCaoTcHienTai.get("10_B02");
        Float chiSoThucHien = ChiSoHoatDong.getChiSoGPM(loiNhuanGop_B02_20, doanhThuThuan_B02_10);

        Float chiSoKHThang = keHoachTcHienTai.get("GPM (Gross Profit Margin)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float loiNhuanTruocThue_B02_20_CK = baoCaoTcTheoNamN1.get("20_B02");
        Float doanhThuThuan_B02_10_CK = baoCaoTcTheoNamN1.get("10_B02");
        Float chiSoThucHienCK = ChiSoHoatDong.getChiSoGPM(loiNhuanTruocThue_B02_20_CK, doanhThuThuan_B02_10_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float loiNhuanTruocThue_B02_20_KyTruoc = baoCaoTcKyTruoc.get("20_B02");
        Float doanhThuThuan_B02_10_KyTruoc = baoCaoTcKyTruoc.get("10_B02");
        Float chiSoThucHienKyTruoc = ChiSoHoatDong.getChiSoGPM(loiNhuanTruocThue_B02_20_KyTruoc, doanhThuThuan_B02_10_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("GPM (Gross Profit Margin)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoNPM(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float loiNhuanDong_B02_60 = baoCaoTcHienTai.get("60_B02");
        Float doanhThuThuan_B02_10 = baoCaoTcHienTai.get("10_B02");
        Float chiSoThucHien = ChiSoHoatDong.getChiSoNPM(loiNhuanDong_B02_60, doanhThuThuan_B02_10);

        Float chiSoKHThang = keHoachTcHienTai.get("NPM (Net Profit Margin)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);

        Float loiNhuanTruocThue_B02_60_CK = baoCaoTcTheoNamN1.get("60_B02");
        Float doanhThuThuan_B02_10_CK = baoCaoTcTheoNamN1.get("10_B02");
        Float chiSoThucHienCK = ChiSoHoatDong.getChiSoNPM(loiNhuanTruocThue_B02_60_CK, doanhThuThuan_B02_10_CK);
        Float chiSoSoSanhCK = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float loiNhuanTruocThue_B02_60_KyTruoc = baoCaoTcKyTruoc.get("60_B02");
        Float doanhThuThuan_B02_10_KyTruoc = baoCaoTcKyTruoc.get("10_B02");
        Float chiSoThucHienKyTruoc = ChiSoHoatDong.getChiSoNPM(loiNhuanTruocThue_B02_60_KyTruoc, doanhThuThuan_B02_10_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("NPM (Net Profit Margin)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCK);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }






}
