package nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso;

import nocsystem.indexmanager.common.CalculateCommon;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.CacChiSoSoSanhCommon;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.ChiSoHieuQuaQuanLySuDungTaiSan;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoCommon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HandleQuanLySuDungTaiSan {
    @Autowired
    private ChiSoHieuQuaQuanLySuDungTaiSan chiSoHieuQuaQuanLySuDungTaiSan;

    public BaoCaoCommon getChiSoVongQuayCacKhoanPhaiThu(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body,
            BaoCaoTaiChinhBody bodyN1,
            BaoCaoTaiChinhBody bodyKyTruoc
            , String type

    ){

        Float tongDoanhThu_B02_01 = baoCaoTcHienTai.get("01_B02");
        Float chiSoThucHien = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayCacKhoanPhaiThu(tongDoanhThu_B02_01, body, type);

        Float chiSoKHThang = keHoachTcHienTai.get("Vòng quay các khoản phải thu (Recievable Turnover Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float tongDoanhThu_B02_01_N1 = baoCaoTcTheoNamN1.get("01_B02");
        Float chiSoROTCN1 = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayCacKhoanPhaiThu(tongDoanhThu_B02_01_N1, bodyN1, type);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoROTCN1);

        Float tongDoanhThu_B02_01_KyTruoc = baoCaoTcKyTruoc.get("01_B02");
        Float chiSoThucHienKyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayCacKhoanPhaiThu(tongDoanhThu_B02_01_KyTruoc, bodyKyTruoc, type);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Vòng quay các khoản phải thu (Recievable Turnover Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoKyThuTienBinhQuan(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        // todo : chỉ số thục hien


        Float tongDoanhThu_B02_01 = baoCaoTcHienTai.get("01_B02");
        Float maSoPhaiThuNganHan_B01_131 = baoCaoTcHienTai.get("131_B01");
        Float maSoPhaiThuDai_B01_211 = baoCaoTcHienTai.get("211_B01");
        Float chiSoThucHien = chiSoHieuQuaQuanLySuDungTaiSan.getKyThuTienBinhQuan(tongDoanhThu_B02_01, maSoPhaiThuNganHan_B01_131, maSoPhaiThuDai_B01_211);

        // todo : chỉ số so sánh kế hoạch
        Float chiSoKHThang = keHoachTcHienTai.get("Kỳ thu tiền bình quân_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);

        // todo : chỉ số so sánh cùng kỳ

        Float tongDoanhThu_B02_01_CK = baoCaoTcTheoNamN1.get("01_B02");
        Float maSoPhaiThuNganHan_B01_131_CK = baoCaoTcTheoNamN1.get("131_B01");
        Float maSoPhaiThuDai_B01_211_CK = baoCaoTcTheoNamN1.get("211_B01");

        Float chiSoThucHienCK = chiSoHieuQuaQuanLySuDungTaiSan.getKyThuTienBinhQuan(tongDoanhThu_B02_01_CK, maSoPhaiThuNganHan_B01_131_CK, maSoPhaiThuDai_B01_211_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);

        // todo : chỉ số so sánh kỳ trước


        Float tongDoanhThu_B02_01_KyTruoc = baoCaoTcKyTruoc.get("01_B02");
        Float maSoPhaiThuNganHan_B01_131_KyTruoc = baoCaoTcKyTruoc.get("131_B01");
        Float maSoPhaiThuDai_B01_211_KyTruoc = baoCaoTcKyTruoc.get("211_B01");

        Float chiSoThucHienKyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.getKyThuTienBinhQuan(tongDoanhThu_B02_01_KyTruoc, maSoPhaiThuNganHan_B01_131_KyTruoc, maSoPhaiThuDai_B01_211_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        // todo : chỉ số so sánh buu cuc ngay 3
        Float chiSoNgay3 = keHoachTcHienTai.get("Kỳ thu tiền bình quân_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoVongQuayCacKhoanPhaiTra(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body ,
            BaoCaoTaiChinhBody bodyN1 ,
            BaoCaoTaiChinhBody bodyKyTruoc ,
            String type
    ){


        Float maSoVon_B02_11 = baoCaoTcHienTai.get("11_B02");
        Float cacKhoanPhaiTraNCC_B01_300 = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(body, type, "311");
        Float chiSoThucHien = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayCacKhoanPhaiTra(cacKhoanPhaiTraNCC_B01_300, maSoVon_B02_11);

        Float chiSoKHThang = keHoachTcHienTai.get("Vòng quay các khoản phải trả (Payable Turnover Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);



        Float maSoVon_B02_11_CK = baoCaoTcTheoNamN1.get("11_B02");
        Float cacKhoanPhaiTraNCC_B01_300_CK = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyN1, type, "311");
        Float chiSoThucHienCK = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayCacKhoanPhaiTra(cacKhoanPhaiTraNCC_B01_300_CK, maSoVon_B02_11_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoVon_B02_11_KyTruoc = baoCaoTcKyTruoc.get("11_B02");
        Float cacKhoanPhaiTraNCC_B01_300_KyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyKyTruoc, type, "311");
        Float chiSoThucHienKyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayCacKhoanPhaiTra(cacKhoanPhaiTraNCC_B01_300_KyTruoc, maSoVon_B02_11_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Vòng quay các khoản phải trả (Payable Turnover Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }



    public  BaoCaoCommon getChiSoKyTraTienBinhQuan(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body ,
            BaoCaoTaiChinhBody bodyN1 ,
            BaoCaoTaiChinhBody bodyKyTruoc ,
            String type
    ){


        BaoCaoCommon chiSoVongQuayCacKhoanPhaiTra = getChiSoVongQuayCacKhoanPhaiTra(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc,  type);
        Float chiSoThucHien = chiSoHieuQuaQuanLySuDungTaiSan.getKyTraTienBinhQuan(chiSoVongQuayCacKhoanPhaiTra.getThucHien());

        Float chiSoKHThang = keHoachTcHienTai.get("Kỳ trả tiền bình quân_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float chiSoThucHienCK = chiSoHieuQuaQuanLySuDungTaiSan.getKyTraTienBinhQuan(chiSoVongQuayCacKhoanPhaiTra.getThucHienCK());
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float chiSoThucHienKyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.getKyTraTienBinhQuan(chiSoVongQuayCacKhoanPhaiTra.getThucHienKyTruoc());
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Kỳ trả tiền bình quân_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }



    public  BaoCaoCommon getChiSoVongQuayHangTonKho(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body ,
            BaoCaoTaiChinhBody bodyN1 ,
            BaoCaoTaiChinhBody bodyKyTruoc ,
            String type
    ){


        Float maSoVon_B02_11 = baoCaoTcHienTai.get("11_B02");
        Float hangTonKhoBinhQuan_B01_140 = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(body, type, "140");
        Float chiSoThucHien = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayHangTonKho(hangTonKhoBinhQuan_B01_140, maSoVon_B02_11);

        Float chiSoKHThang = keHoachTcHienTai.get("Vòng quay hàng tồn kho (Inventory Turnover Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float maSoVon_B02_11_CK = baoCaoTcTheoNamN1.get("11_B02");
        Float hangTonKhoBinhQuan_B01_140_CK = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyN1, type, "140");
        Float chiSoThucHienCK = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayHangTonKho(hangTonKhoBinhQuan_B01_140_CK, maSoVon_B02_11_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoVon_B02_11_KyTruoc = baoCaoTcKyTruoc.get("11_B02");
        Float hangTonKhoBinhQuan_B01_140_KyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyKyTruoc, type, "140");
        Float chiSoThucHienKyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayHangTonKho(hangTonKhoBinhQuan_B01_140_KyTruoc, maSoVon_B02_11_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Vòng quay hàng tồn kho (Inventory Turnover Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoNgayLuuKhoBinhQuan(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body ,
            BaoCaoTaiChinhBody bodyN1 ,
            BaoCaoTaiChinhBody bodyKyTruoc ,

            String type
    ){


        Float maSoVon_B02_11 = baoCaoTcHienTai.get("11_B02");
        Float hangTonKhoBinhQuan_B01_140 = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(body, type, "140");
        Float chiSoThucHien = chiSoHieuQuaQuanLySuDungTaiSan.getSoNgayLuuKhoBinhQuan(hangTonKhoBinhQuan_B01_140, maSoVon_B02_11);

        Float chiSoKHThang = keHoachTcHienTai.get("Số ngày lưu kho bình quân_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float maSoVon_B02_11_CK = baoCaoTcTheoNamN1.get("11_B02");
        Float hangTonKhoBinhQuan_B01_140_CK = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyN1, type, "140");
        Float chiSoThucHienCK = chiSoHieuQuaQuanLySuDungTaiSan.getSoNgayLuuKhoBinhQuan(hangTonKhoBinhQuan_B01_140_CK, maSoVon_B02_11_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoVon_B02_11_KyTruoc = baoCaoTcKyTruoc.get("11_B02");
        Float hangTonKhoBinhQuan_B01_140_KyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyKyTruoc, type, "140");
        Float chiSoThucHienKyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.getSoNgayLuuKhoBinhQuan(hangTonKhoBinhQuan_B01_140_KyTruoc, maSoVon_B02_11_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Số ngày lưu kho bình quân_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }



    public  BaoCaoCommon getVongQuayTongTaiSan(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body ,
            BaoCaoTaiChinhBody bodyN1 ,
            BaoCaoTaiChinhBody bodyKyTruoc ,
            String type
    ){


        Float maSoDoanhThuBanHang_B02_01 = baoCaoTcHienTai.get("01_B02");
        Float maSoDoanhThuTaiChinh_B02_21 = baoCaoTcHienTai.get("21_B02");
        Float maSoThuNhapKhac_B02_31 = baoCaoTcHienTai.get("31_B02");
        Float tongTaiSanBinhQuan_B01_270 = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(body, type, "270");
        Float chiSoThucHien = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayTongTaiSan(maSoDoanhThuBanHang_B02_01, maSoDoanhThuTaiChinh_B02_21, maSoThuNhapKhac_B02_31, tongTaiSanBinhQuan_B01_270);

        Float chiSoKHThang = keHoachTcHienTai.get("Vòng quay tổng tài sản (Total Asset Turnover Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float maSoDoanhThuBanHang_B02_01_CK = baoCaoTcTheoNamN1.get("01_B02");
        Float maSoDoanhThuTaiChinh_B02_21_CK = baoCaoTcTheoNamN1.get("21_B02");
        Float maSoThuNhapKhac_B02_31_CK = baoCaoTcTheoNamN1.get("31_B02");
        Float tongTaiSanBinhQuan_B01_270_CK = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyN1, type, "270");
        Float chiSoThucHienCK = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayTongTaiSan(maSoDoanhThuBanHang_B02_01_CK, maSoDoanhThuTaiChinh_B02_21_CK, maSoThuNhapKhac_B02_31_CK, tongTaiSanBinhQuan_B01_270_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoDoanhThuBanHang_B02_01_KyTruoc = baoCaoTcKyTruoc.get("01_B02");
        Float maSoDoanhThuTaiChinh_B02_21_KyTruoc = baoCaoTcKyTruoc.get("21_B02");
        Float maSoThuNhapKhac_B02_31_KyTruoc = baoCaoTcKyTruoc.get("31_B02");
        Float tongTaiSanBinhQuan_B01_270_KyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.tinhBinhQuanCommon(bodyKyTruoc, type, "270");
        Float chiSoThucHienKyTruoc = chiSoHieuQuaQuanLySuDungTaiSan.getVongQuayTongTaiSan(maSoDoanhThuBanHang_B02_01_KyTruoc, maSoDoanhThuTaiChinh_B02_21_KyTruoc, maSoThuNhapKhac_B02_31_KyTruoc, tongTaiSanBinhQuan_B01_270_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Vòng quay tổng tài sản (Total Asset Turnover Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }




}
