package nocsystem.indexmanager.services.baocao_taichinh.logic;

import nocsystem.indexmanager.common.*;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoChartTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.ChartBaoCaoService;
import nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso.*;
import nocsystem.indexmanager.services.baocao_taichinh.response.*;
import nocsystem.indexmanager.services.baocao_taichinh.taiChinhCommon.ShowBaoCaoCommon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ChartBaoCaoTaiChinhImpl implements ChartBaoCaoService {

    @Autowired
    HandleChiSoSinhLoi handleChiSoSinhLoi;
    @Autowired
    ObjectShowBaoCaoCommon showBaoCaoCommon;
    @Autowired
    HandleChiSoHoatDong handleChiSoHoatDong;

    @Autowired
    HandleChiSoCoCauTaiSan handleChiSoCoCauTaiSan;
    @Autowired
    HandleKhaNangThanhToan handleKhaNangThanhToan;

    @Autowired
    HandleQuanLySuDungTaiSan handleQuanLySuDungTaiSan;

    public Map<String, BaoCaoCommon> processShowDataChart(
            List<BaoCaoTaiChinhResponseDb> taiChinhHienTai,
            List<BaoCaoTaiChinhResponseDb> taiChinhB02HienTai,

            List<KeHoach3NgayResponseDb> keHoachNgay3HienTai,

            List<BaoCaoTaiChinhResponseDb> taiChinhTheoNamN1,
            List<BaoCaoTaiChinhResponseDb> taiChinhResponseQuyB02_N1,

            List<BaoCaoTaiChinhResponseDb> taiChinhKyTruoc,
            List<BaoCaoTaiChinhResponseDb> taiChinhResponseQuyB02_KyTruoc,

            BaoCaoTaiChinhBody body,
            BaoCaoTaiChinhBody bodyN1,
            BaoCaoTaiChinhBody bodyKyTruoc,
            String type,
            String stt
    ) {



        ObjectMapValueBaoCaoTC mapValueBaoCaoTC = new ObjectMapValueBaoCaoTC();

        // todo : hien tai ( thang, nam , quy )
        Map<String, Float> baoCaoTcHienTai = mapValueBaoCaoTC.mapBaoCaoTaiChinh(taiChinhHienTai);
        Map<String, Float> baoCaoTcB02 = mapValueBaoCaoTC.mapValueB02(taiChinhB02HienTai);


        baoCaoTcHienTai = mapValueBaoCaoTC.mergetDictB02_B01(baoCaoTcB02, baoCaoTcHienTai);


        Map<String, Float> keHoachTcHienTai = mapValueBaoCaoTC.mapKeHoachTaiChi(keHoachNgay3HienTai);

        // todo : n-1 ( thang, nam , quy )
        Map<String, Float> baoCaoTcTheoNamN1 = mapValueBaoCaoTC.mapBaoCaoTaiChinh(taiChinhTheoNamN1);
        Map<String, Float> baoCaoTcB02_N1 = mapValueBaoCaoTC.mapValueB02(taiChinhResponseQuyB02_N1);
        baoCaoTcTheoNamN1 = mapValueBaoCaoTC.mergetDictB02_B01(baoCaoTcB02_N1, baoCaoTcTheoNamN1);


        // todo : kỳ trước của hiện tại
        Map<String, Float> baoCaoTcKyTruoc = mapValueBaoCaoTC.mapBaoCaoTaiChinh(taiChinhKyTruoc);
        Map<String, Float> baoCaoTcB02KyTruoc = mapValueBaoCaoTC.mapValueB02(taiChinhResponseQuyB02_KyTruoc);
        baoCaoTcKyTruoc = mapValueBaoCaoTC.mergetDictB02_B01(baoCaoTcB02KyTruoc, baoCaoTcKyTruoc);



        Map<String, BaoCaoCommon> dictionary = new HashMap<>();
        switch (stt){
            case "1":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(1), handleChiSoSinhLoi.getChiSoROCE(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "2":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(2), handleChiSoSinhLoi.getChiSoROTC(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;
            case "3":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(3), handleChiSoSinhLoi.getChiSoROE(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;

            case "4":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(4), handleChiSoSinhLoi.getChiSoROA(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body,  bodyN1, bodyKyTruoc, type));
                break;

            case "5":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(5), handleChiSoHoatDong.getChiSoEBIT(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "6":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(6), handleChiSoHoatDong.getChiSoEBITDA(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "7":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(7), handleChiSoHoatDong.getChiSoROS(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "8":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(8), handleChiSoHoatDong.getChiSoGPM(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "9":
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(9), handleChiSoHoatDong.getChiSoNPM(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "10":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(10), handleChiSoCoCauTaiSan.getChiSoDER(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "11":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(11), handleChiSoCoCauTaiSan.getChiSoNWC(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "12":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(12), handleChiSoCoCauTaiSan.getChiSoWCR(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "13":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(13), handleChiSoCoCauTaiSan.getChiSoTaiSanKinhDoanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "14":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(14), handleChiSoCoCauTaiSan.getChiSoNoKinhDoanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "15":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(15), handleKhaNangThanhToan.getChiSoThanhToanHienHanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "16":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(16), handleKhaNangThanhToan.getChiSoThanhToanNhanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "17":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(17), handleKhaNangThanhToan.getChiSoThanhToanTucThoi(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "18":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(18), handleKhaNangThanhToan.getChiSoThanhToanLaiVay(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "19":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(19), handleQuanLySuDungTaiSan.getChiSoVongQuayCacKhoanPhaiThu(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;
            case "20":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(20), handleQuanLySuDungTaiSan.getChiSoKyThuTienBinhQuan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
            case "21":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(21), handleQuanLySuDungTaiSan.getChiSoVongQuayCacKhoanPhaiTra(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;
            case "22":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(22), handleQuanLySuDungTaiSan.getChiSoKyTraTienBinhQuan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;
            case "23":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(23), handleQuanLySuDungTaiSan.getChiSoVongQuayHangTonKho(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;
            case "24":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(24), handleQuanLySuDungTaiSan.getChiSoNgayLuuKhoBinhQuan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;
            case "25":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(25), handleQuanLySuDungTaiSan.getVongQuayTongTaiSan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
                break;
            case "26":
                if(type.equals("thang")) break;
                dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(26), HandleChiSoDauTu.getChiSoROI(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
                break;
        }
        return dictionary;
    }


    @Override
    public List<BaoCaoChartCommon> chartBaoCaoTaiChinh(BaoCaoChartTaiChinhBody body) {

        String stt = body.stt;
        String type = body.type;
        BodyCustom bodyCustom = new BodyCustom();
        String[] listMaSo = MaSoRequireQuerry.getMasoFromChiSoB01(body.stt);

        List<BaoCaoChartCommon> list = new ArrayList<>();
        if(type.equals("thang")){

            BaoCaoTaiChinhBody[] listBodyChart = bodyCustom.getBodyChart(body);
            int i = 0;
            for(BaoCaoTaiChinhBody bd : listBodyChart){
                BaoCaoChartCommon chartCommon = new BaoCaoChartCommon();
                String maSoB01 = listMaSo[0];
                String maSoB02 = listMaSo[1];
                BaoCaoTaiChinhGetForm formThang =  showBaoCaoCommon.getFormChart(bd, "thang", maSoB01, maSoB02);

                Map<String, BaoCaoCommon> dict  = processShowDataChart(
                        formThang.getTaiChinhResponseB01B03(),
                        formThang.getTaiChinhResponseB02(),
                        formThang.getKeHoach3NgayResponse(),

                        formThang.getTaiChinhResponseB01B03_N1(),
                        formThang.getTaiChinhResponseB02_N1(),

                        formThang.getTaiChinhResponseKyTruoc(),
                        formThang.getTaiChinhResponseB02_KyTruoc(),

                        formThang.getBody(),
                        formThang.getBodyN1(),
                        formThang.getBodyKyTruoc(),
                        "thang",
                        stt
                        );
                String thangQuyNam = "Tháng " + (i+1);
                chartCommon.setThangQuyNam(thangQuyNam);
                convertDictToObject(dict, chartCommon);
                list.add(chartCommon);
                i++;
            }

            return list;
        }


       else if(type.equals("quy")){

            BaoCaoTaiChinhBody[] listBodyChart = bodyCustom.getBodyChart(body);
            int i = 0;
            for(BaoCaoTaiChinhBody bd : listBodyChart){
                BaoCaoChartCommon chartCommon = new BaoCaoChartCommon();
                String maSoB01 = listMaSo[0];
                String maSoB02 = listMaSo[1];
                BaoCaoTaiChinhGetForm formThang =  showBaoCaoCommon.getFormChart(bd, "quy", maSoB01, maSoB02);

                Map<String, BaoCaoCommon> dict  = processShowDataChart(
                        formThang.getTaiChinhResponseB01B03(),
                        formThang.getTaiChinhResponseB02(),
                        formThang.getKeHoach3NgayResponse(),

                        formThang.getTaiChinhResponseB01B03_N1(),
                        formThang.getTaiChinhResponseB02_N1(),

                        formThang.getTaiChinhResponseKyTruoc(),
                        formThang.getTaiChinhResponseB02_KyTruoc(),

                        formThang.getBody(),
                        formThang.getBodyN1(),
                        formThang.getBodyKyTruoc(),
                        "quy",
                        stt
                );
                String thangQuyNam = "Quý " + (i + 1) +"/" + body.filterNam;
                chartCommon.setThangQuyNam(thangQuyNam);
                convertDictToObject(dict, chartCommon);
                list.add(chartCommon);
                i++;
            }

            return list;
        }
       else {
            BaoCaoTaiChinhBody[] listBodyChart = bodyCustom.getBodyChart(body);
            for(BaoCaoTaiChinhBody bd : listBodyChart){
                BaoCaoChartCommon chartCommon = new BaoCaoChartCommon();
                String maSoB01 = listMaSo[0];
                String maSoB02 = listMaSo[1];
                BaoCaoTaiChinhGetForm formThang =  showBaoCaoCommon.getFormChart(bd, "nam", maSoB01, maSoB02);

                Map<String, BaoCaoCommon> dict  = processShowDataChart(
                        formThang.getTaiChinhResponseB01B03(),
                        formThang.getTaiChinhResponseB02(),
                        formThang.getKeHoach3NgayResponse(),

                        formThang.getTaiChinhResponseB01B03_N1(),
                        formThang.getTaiChinhResponseB02_N1(),

                        formThang.getTaiChinhResponseKyTruoc(),
                        formThang.getTaiChinhResponseB02_KyTruoc(),

                        formThang.getBody(),
                        formThang.getBodyN1(),
                        formThang.getBodyKyTruoc(),
                        "nam",
                        stt
                );
                String thangQuyNam = "Năm " + (Integer.parseInt(bd.filterNam));
                chartCommon.setThangQuyNam(thangQuyNam);
                convertDictToObject(dict, chartCommon);
                list.add(chartCommon);
            }

            return list;
        }

    }



    public BaoCaoChartCommon convertDictToObject(Map<String, BaoCaoCommon> dict, BaoCaoChartCommon chartCommon){
        if(dict == null || dict.isEmpty()) return chartCommon;

        for (Map.Entry<String, BaoCaoCommon> entry : dict.entrySet()) {
            chartCommon.setThucHien(CalculateCommon.roundFloat(entry.getValue().getThucHien()));
            chartCommon.setSoSanhKH(CalculateCommon.roundFloat(entry.getValue().getSoSanhKH()));
            chartCommon.setSoSanhCK(CalculateCommon.roundFloat(entry.getValue().getSoSanhCK()));
            chartCommon.setSoSanhKyTruoc(CalculateCommon.roundFloat(entry.getValue().getSoSanhKyTruoc()));
            chartCommon.setSoSanhNgay3(CalculateCommon.roundFloat(entry.getValue().getSoSanhNgay3()));
            break;
        }
        return chartCommon;
    }


}
