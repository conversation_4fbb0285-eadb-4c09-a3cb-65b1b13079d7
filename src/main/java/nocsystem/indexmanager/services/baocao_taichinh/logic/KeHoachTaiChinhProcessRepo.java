package nocsystem.indexmanager.services.baocao_taichinh.logic;

import nocsystem.indexmanager.models.baocao_taichinh.KeHoach3NgayModel;
import nocsystem.indexmanager.repositories.baocao_taichinh.KeHoachRepo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class KeHoachTaiChinhProcessRepo {
    private static final Logger logger = LoggerFactory.getLogger(KeHoachTaiChinhProcessRepo.class);

    @Autowired
    KeHoachRepo keHoach3NgayRepo;
    @Transactional
    public void insertStudentsBatch(List<KeHoach3NgayModel> keHoach3NgayModels) {
        try {
            keHoach3NgayRepo.saveAll(keHoach3NgayModels);
        }catch (Exception e){
            logger.info("Lỗi repo khi lưu danh sách kế hoạch 3 ngày: {}", e);
        }

    }

}
