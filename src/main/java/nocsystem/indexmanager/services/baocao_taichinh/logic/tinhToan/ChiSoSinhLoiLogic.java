package nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan;


import nocsystem.indexmanager.common.CalculateCommon;
import nocsystem.indexmanager.common.DateTimeCommon;
import nocsystem.indexmanager.common.ObjectProcessBinhQuan;
import nocsystem.indexmanager.repositories.baocao_taichinh.logic.BaoCaoTaiChinhRepoImpl;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoTaiChinhResponseDb;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ChiSoSinhLoiLogic {

    @Autowired
    BaoCaoTaiChinhRepoImpl repo;
    public  Float getChiSoThucHienROCE(Float chiSoEBIT, Float tongTaiSanB01_270, Float tongTaiSanNganHanB01_100) {
        if (chiSoEBIT == null || CalculateCommon.sub2Nums(tongTaiSanB01_270, tongTaiSanNganHanB01_100) == 0) return null;

        return  chiSoEBIT / (CalculateCommon.sub2Nums(tongTaiSanB01_270, tongTaiSanNganHanB01_100));
    }

    public Float getChiSoThucHienROTC(Float maSoThuNhapDong_B02_10, Float maSoChiPhiLaiVay_B02_23, BaoCaoTaiChinhBody body, String type) {

        float taiSanBinhQuan;
        if(type.equals("thang")){
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuandauKy_CuoiKyThang(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "270");
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "270");
            taiSanBinhQuan = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;
        } else if (type.equals("quy")) {
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuanDauKy_CuoiKyQuy(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "270");
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "270");
            taiSanBinhQuan = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;

        }
        else {
            BaoCaoTaiChinhBody[] bonQuyTrongNam = ObjectProcessBinhQuan.binhQuan4QuyNam(body);
            BaoCaoTaiChinhResponseDb quy1 = repo.getDataFromMaSo(bonQuyTrongNam[0], "270");
            BaoCaoTaiChinhResponseDb quy2 = repo.getDataFromMaSo(bonQuyTrongNam[1], "270");
            BaoCaoTaiChinhResponseDb quy3 = repo.getDataFromMaSo(bonQuyTrongNam[2], "270");
            BaoCaoTaiChinhResponseDb quy4 = repo.getDataFromMaSo(bonQuyTrongNam[3], "270");

            Float sumQuy12 = CalculateCommon.sum2Nums(quy1.getGiaTriLoaiBaoCao() , quy2.getGiaTriLoaiBaoCao());
            Float sumQuy34 = CalculateCommon.sum2Nums(quy3.getGiaTriLoaiBaoCao() , quy4.getGiaTriLoaiBaoCao());
            taiSanBinhQuan = CalculateCommon.sum2Nums(sumQuy12, sumQuy34) / 4;

        }
        if (taiSanBinhQuan == 0 || taiSanBinhQuan == 0.0){
            return null;
        }
        return  (CalculateCommon.sum2Nums(maSoThuNhapDong_B02_10, maSoChiPhiLaiVay_B02_23)) / taiSanBinhQuan;
    }

    public  Float getChiSoThucHienROE(Float loiNhuanSauThue_B02_60,  BaoCaoTaiChinhBody body,  String type) {

        float vonChuSoHuuBinhQuan_B01_400;
        if(type.equals("thang")){
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuandauKy_CuoiKyThang(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "400");
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "400");
            vonChuSoHuuBinhQuan_B01_400 = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;
        } else if (type.equals("quy")) {
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuanDauKy_CuoiKyQuy(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "400");
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "400");
            vonChuSoHuuBinhQuan_B01_400 = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;
        }
        else {
            BaoCaoTaiChinhBody[] bonQuyTrongNam = ObjectProcessBinhQuan.binhQuan4QuyNam(body);
            BaoCaoTaiChinhResponseDb quy1 = repo.getDataFromMaSo(bonQuyTrongNam[0], "400");
            BaoCaoTaiChinhResponseDb quy2 = repo.getDataFromMaSo(bonQuyTrongNam[1], "400");
            BaoCaoTaiChinhResponseDb quy3 = repo.getDataFromMaSo(bonQuyTrongNam[2], "400");
            BaoCaoTaiChinhResponseDb quy4 = repo.getDataFromMaSo(bonQuyTrongNam[3], "400");

            Float sumQuy12 = CalculateCommon.sum2Nums(quy1.getGiaTriLoaiBaoCao() , quy2.getGiaTriLoaiBaoCao());
            Float sumQuy34 = CalculateCommon.sum2Nums(quy3.getGiaTriLoaiBaoCao() , quy4.getGiaTriLoaiBaoCao());
            vonChuSoHuuBinhQuan_B01_400 = CalculateCommon.sum2Nums(sumQuy12, sumQuy34) / 4;
        }

        if (loiNhuanSauThue_B02_60 == null || vonChuSoHuuBinhQuan_B01_400 == 0) return null;

        return  (loiNhuanSauThue_B02_60 / vonChuSoHuuBinhQuan_B01_400);
    }


    public  Float getChiSoThucHienROA(Float loiNhuanSauThue_B02_60,  BaoCaoTaiChinhBody body,  String type) {

        float vonChuSoHuuBinhQuan_B01_270;
        if(type.equals("thang")){
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuandauKy_CuoiKyThang(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "270");
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "270");
            vonChuSoHuuBinhQuan_B01_270 = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;
        } else if (type.equals("quy")) {
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuanDauKy_CuoiKyQuy(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "270");
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "270");
            vonChuSoHuuBinhQuan_B01_270 = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;
        }
        else {
            BaoCaoTaiChinhBody[] bonQuyTrongNam = ObjectProcessBinhQuan.binhQuan4QuyNam(body);
            BaoCaoTaiChinhResponseDb quy1 = repo.getDataFromMaSo(bonQuyTrongNam[0], "270");
            BaoCaoTaiChinhResponseDb quy2 = repo.getDataFromMaSo(bonQuyTrongNam[1], "270");
            BaoCaoTaiChinhResponseDb quy3 = repo.getDataFromMaSo(bonQuyTrongNam[2], "270");
            BaoCaoTaiChinhResponseDb quy4 = repo.getDataFromMaSo(bonQuyTrongNam[3], "270");

            Float sumQuy12 = CalculateCommon.sum2Nums(quy1.getGiaTriLoaiBaoCao() , quy2.getGiaTriLoaiBaoCao());
            Float sumQuy34 = CalculateCommon.sum2Nums(quy3.getGiaTriLoaiBaoCao() , quy4.getGiaTriLoaiBaoCao());
            vonChuSoHuuBinhQuan_B01_270 = CalculateCommon.sum2Nums(sumQuy12, sumQuy34) / 4;
        }

        if (loiNhuanSauThue_B02_60 == null || vonChuSoHuuBinhQuan_B01_270 == 0) return null;

        return  (loiNhuanSauThue_B02_60 / vonChuSoHuuBinhQuan_B01_270);
    }


    // todo : cach tinh cu
    public  Float getChiSoThucHienROA(Float loiNhuanSauThue_B02_60, Float tongTaiSan_B01_270) {
        if (tongTaiSan_B01_270 == null || loiNhuanSauThue_B02_60 == null || tongTaiSan_B01_270 == 0) return null;
        return  (loiNhuanSauThue_B02_60 / tongTaiSan_B01_270);
    }

}
