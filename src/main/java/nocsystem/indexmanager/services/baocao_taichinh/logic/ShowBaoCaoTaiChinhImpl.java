package nocsystem.indexmanager.services.baocao_taichinh.logic;


import nocsystem.indexmanager.common.BaoCaoTaiChinhGetForm;
import nocsystem.indexmanager.common.CalculateCommon;
import nocsystem.indexmanager.common.ObjectMapValueBaoCaoTC;
import nocsystem.indexmanager.common.ObjectShowBaoCaoCommon;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.ShowBaoCaoTaiChinhServices;
import nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso.*;
import nocsystem.indexmanager.services.baocao_taichinh.response.*;
import nocsystem.indexmanager.services.baocao_taichinh.taiChinhCommon.ShowBaoCaoCommon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ShowBaoCaoTaiChinhImpl extends AbstractDao implements ShowBaoCaoTaiChinhServices {

    @Autowired
    HandleChiSoSinhLoi handleChiSoSinhLoi;

    @Autowired
    ObjectShowBaoCaoCommon showBaoCaoCommon;

    @Autowired
    HandleChiSoHoatDong handleChiSoHoatDong;

    @Autowired
    HandleChiSoCoCauTaiSan handleChiSoCoCauTaiSan;
    @Autowired
    HandleKhaNangThanhToan handleKhaNangThanhToan;

    @Autowired
    HandleQuanLySuDungTaiSan handleQuanLySuDungTaiSan;

    public Map<String, BaoCaoCommon> processShowData(
            List<BaoCaoTaiChinhResponseDb> taiChinhHienTai,
            List<BaoCaoTaiChinhResponseDb> taiChinhB02HienTai,

            List<KeHoach3NgayResponseDb> keHoachNgay3HienTai,

            List<BaoCaoTaiChinhResponseDb> taiChinhTheoNamN1,
            List<BaoCaoTaiChinhResponseDb> taiChinhResponseQuyB02_N1,

            List<BaoCaoTaiChinhResponseDb> taiChinhKyTruoc,
            List<BaoCaoTaiChinhResponseDb> taiChinhResponseQuyB02_KyTruoc,

            BaoCaoTaiChinhBody body,
            BaoCaoTaiChinhBody bodyN1,
            BaoCaoTaiChinhBody bodyKyTruoc,
            String type
    ) {



        ObjectMapValueBaoCaoTC mapValueBaoCaoTC = new ObjectMapValueBaoCaoTC();

        // todo : hien tai ( thang, nam , quy )
        Map<String, Float> baoCaoTcHienTai = mapValueBaoCaoTC.mapBaoCaoTaiChinh(taiChinhHienTai);
        Map<String, Float> baoCaoTcB02 = mapValueBaoCaoTC.mapValueB02(taiChinhB02HienTai);


        baoCaoTcHienTai = mapValueBaoCaoTC.mergetDictB02_B01(baoCaoTcB02, baoCaoTcHienTai);


        Map<String, Float> keHoachTcHienTai = mapValueBaoCaoTC.mapKeHoachTaiChi(keHoachNgay3HienTai);

        // todo : n-1 ( thang, nam , quy )
        Map<String, Float> baoCaoTcTheoNamN1 = mapValueBaoCaoTC.mapBaoCaoTaiChinh(taiChinhTheoNamN1);
        Map<String, Float> baoCaoTcB02_N1 = mapValueBaoCaoTC.mapValueB02(taiChinhResponseQuyB02_N1);
        baoCaoTcTheoNamN1 = mapValueBaoCaoTC.mergetDictB02_B01(baoCaoTcB02_N1, baoCaoTcTheoNamN1);


        // todo : kỳ trước của hiện tại
        Map<String, Float> baoCaoTcKyTruoc = mapValueBaoCaoTC.mapBaoCaoTaiChinh(taiChinhKyTruoc);
        Map<String, Float> baoCaoTcB02KyTruoc = mapValueBaoCaoTC.mapValueB02(taiChinhResponseQuyB02_KyTruoc);
        baoCaoTcKyTruoc = mapValueBaoCaoTC.mergetDictB02_B01(baoCaoTcB02KyTruoc, baoCaoTcKyTruoc);


        Map<String, BaoCaoCommon> dictionary = new HashMap<>();
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(1), handleChiSoSinhLoi.getChiSoROCE(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(2), handleChiSoSinhLoi.getChiSoROTC(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(3), handleChiSoSinhLoi.getChiSoROE(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(4), handleChiSoSinhLoi.getChiSoROA(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body,  bodyN1, bodyKyTruoc, type));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(5), handleChiSoHoatDong.getChiSoEBIT(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(6), handleChiSoHoatDong.getChiSoEBITDA(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(7), handleChiSoHoatDong.getChiSoROS(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(8), handleChiSoHoatDong.getChiSoGPM(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
        dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(9), handleChiSoHoatDong.getChiSoNPM(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));


        if(!type.equals("thang")){
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(10), handleChiSoCoCauTaiSan.getChiSoDER(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(11), handleChiSoCoCauTaiSan.getChiSoNWC(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(12), handleChiSoCoCauTaiSan.getChiSoWCR(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(13), handleChiSoCoCauTaiSan.getChiSoTaiSanKinhDoanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(14), handleChiSoCoCauTaiSan.getChiSoNoKinhDoanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));

            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(15), handleKhaNangThanhToan.getChiSoThanhToanHienHanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(16), handleKhaNangThanhToan.getChiSoThanhToanNhanh(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(17), handleKhaNangThanhToan.getChiSoThanhToanTucThoi(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(18), handleKhaNangThanhToan.getChiSoThanhToanLaiVay(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(19), handleQuanLySuDungTaiSan.getChiSoVongQuayCacKhoanPhaiThu(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(20), handleQuanLySuDungTaiSan.getChiSoKyThuTienBinhQuan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(21), handleQuanLySuDungTaiSan.getChiSoVongQuayCacKhoanPhaiTra(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(22), handleQuanLySuDungTaiSan.getChiSoKyTraTienBinhQuan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(23), handleQuanLySuDungTaiSan.getChiSoVongQuayHangTonKho(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(24), handleQuanLySuDungTaiSan.getChiSoNgayLuuKhoBinhQuan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(25), handleQuanLySuDungTaiSan.getVongQuayTongTaiSan(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc, body, bodyN1, bodyKyTruoc, type));
//
            dictionary.put(ShowBaoCaoCommon.STT_CHITIEU.get(26), HandleChiSoDauTu.getChiSoROI(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc));
        }
        return dictionary;
    }


    public BaoCaoCommon lamTronSoBaoCaoCommon(BaoCaoCommon bc){
        if(bc == null) return new BaoCaoCommon();
        BaoCaoCommon bcTmp = new BaoCaoCommon();
        bcTmp.setThucHien(CalculateCommon.roundFloat(bc.getThucHien()));
        bcTmp.setThucHienKyTruoc(CalculateCommon.roundFloat(bc.getThucHienKyTruoc()));
        bcTmp.setThucHienCK(CalculateCommon.roundFloat(bc.getThucHienCK()));

        bcTmp.setSoSanhKH(CalculateCommon.roundFloat(bc.getSoSanhKH()));
        bcTmp.setSoSanhCK(CalculateCommon.roundFloat(bc.getSoSanhCK()));
        bcTmp.setSoSanhKyTruoc(CalculateCommon.roundFloat(bc.getSoSanhKyTruoc()));
        bcTmp.setSoSanhNgay3(CalculateCommon.roundFloat(bc.getSoSanhNgay3()));
        return bcTmp;
    }
    public List<ShowBaoCaoResponse> mapDictionary(Map<String, BaoCaoCommon> dictThang, Map<String, BaoCaoCommon> dictQuy, Map<String, BaoCaoCommon> dictNam) {
        List<ShowBaoCaoResponse> list = new ArrayList<>();
        for(int i = 1; i<= 26 ; i++){
            ShowBaoCaoResponse response = new ShowBaoCaoResponse();
            response.setChiTieu(ShowBaoCaoCommon.CHITIEU_STT.get(i-1));
            if(dictThang.containsKey(ShowBaoCaoCommon.CHITIEU_STT.get(i-1)) && i < 10){
                response.setBaoCaoThang(dictThang.get(ShowBaoCaoCommon.CHITIEU_STT.get(i-1)));
            }
            if(dictQuy.containsKey(ShowBaoCaoCommon.CHITIEU_STT.get(i-1))){
                response.setBaoCaoQuy(dictQuy.get(ShowBaoCaoCommon.CHITIEU_STT.get(i-1)));
            }
            if(dictNam.containsKey(ShowBaoCaoCommon.CHITIEU_STT.get(i-1))){
                response.setBaoCaoNam(dictNam.get(ShowBaoCaoCommon.CHITIEU_STT.get(i-1)));
            }
            list.add(response);
        }

        // todo : chuyen tu list chua lam tron sang list lam tron so, code nhu nay hoi thua sau se sua lai nhung do phai
        //        sua tu luc map
        List<ShowBaoCaoResponse> listTmp = new ArrayList<>();
        for(ShowBaoCaoResponse o : list){
            ShowBaoCaoResponse tmp = new ShowBaoCaoResponse();

            BaoCaoCommon bcThang = lamTronSoBaoCaoCommon(o.getBaoCaoThang());
            BaoCaoCommon bcQuy = lamTronSoBaoCaoCommon(o.getBaoCaoQuy());
            BaoCaoCommon bcNam = lamTronSoBaoCaoCommon(o.getBaoCaoNam());


            tmp.setChiTieu(o.getChiTieu());
            tmp.setBaoCaoThang(bcThang);
            tmp.setBaoCaoQuy(bcQuy);
            tmp.setBaoCaoNam(bcNam);

            listTmp.add(tmp);
        }

        return listTmp;
    }

    @Override
    public List<ShowBaoCaoResponse> showBaoCao(BaoCaoTaiChinhBody body) {

        BaoCaoTaiChinhGetForm formThang =  showBaoCaoCommon.getForm(body, "thang");
        BaoCaoTaiChinhGetForm formQuy =  showBaoCaoCommon.getForm(body, "quy");
        BaoCaoTaiChinhGetForm formNam =  showBaoCaoCommon.getForm(body, "nam");



        Map<String, BaoCaoCommon> dictThang  = processShowData(
                formThang.getTaiChinhResponseB01B03(),
                formThang.getTaiChinhResponseB02(),
                formThang.getKeHoach3NgayResponse(),

                formThang.getTaiChinhResponseB01B03_N1(),
                formThang.getTaiChinhResponseB02_N1(),

                formThang.getTaiChinhResponseKyTruoc(),
                formThang.getTaiChinhResponseB02_KyTruoc(),

                body,
                formThang.getBodyN1(),
                formThang.getBodyKyTruoc(),
                "thang"
                );


//
        Map<String, BaoCaoCommon> dictQuy = processShowData(
                formQuy.getTaiChinhResponseB01B03(),
                formQuy.getTaiChinhResponseB02(),
                formQuy.getKeHoach3NgayResponse(),

                formQuy.getTaiChinhResponseB01B03_N1(),
                formQuy.getTaiChinhResponseB02_N1(),

                formQuy.getTaiChinhResponseKyTruoc(),
                formQuy.getTaiChinhResponseB02_KyTruoc(),

                formQuy.getBody(),
                formQuy.getBodyN1(),
                formQuy.getBodyKyTruoc(),
                "quy"
        );

        Map<String, BaoCaoCommon> dictNam = processShowData(
                formNam.getTaiChinhResponseB01B03(),
                formNam.getTaiChinhResponseB02(),
                formNam.getKeHoach3NgayResponse(),

                formNam.getTaiChinhResponseB01B03_N1(),
                formNam.getTaiChinhResponseB02_N1(),

                formNam.getTaiChinhResponseKyTruoc(),
                formNam.getTaiChinhResponseB02_KyTruoc(),

                formNam.getBody(),
                formNam.getBodyN1(),
                formNam.getBodyKyTruoc(),
                "nam"
        );

//        Map<String, BaoCaoCommon> dictQuy = new HashMap<>();
//        Map<String, BaoCaoCommon> dictNam = new HashMap<>();
//        Map<String, BaoCaoCommon> dictThang = new HashMap<>();
        return mapDictionary(dictThang, dictQuy, dictNam);

    }
}
