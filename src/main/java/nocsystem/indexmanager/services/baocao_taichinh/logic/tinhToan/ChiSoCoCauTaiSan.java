package nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan;


import nocsystem.indexmanager.common.CalculateCommon;

public class ChiSoCoCauTaiSan {

    public static Float getChiSoDER(Float noPhaiTra_B01_300, Float vonChuSoHuu_B01_400) {
        if(noPhaiTra_B01_300 == null || vonChuSoHuu_B01_400 == null || vonChuSoHuu_B01_400 == 0) return null;
        return noPhaiTra_B01_300/vonChuSoHuu_B01_400 ;
    }

    public static Float getChiSoNWC(Float taiSanNgan_B01_100, Float noNganHan_B01_310) {
        return CalculateCommon.sub2Nums(taiSanNgan_B01_100, noNganHan_B01_310);
    }

    public static Float getTaiSanKinhDoanh(Float maSoPhaiThuNganHan_B01_130, Float maSoHangTonKho_B01_140,
                                    Float maSoTaiSanNganHan_B01_150) {
        return CalculateCommon.sum2Nums(CalculateCommon.sum2Nums(maSoPhaiThuNganHan_B01_130, maSoHangTonKho_B01_140), maSoTaiSanNganHan_B01_150);
    }

    public static Float getNoKinhDoanh(Float maSoNoNganHan_B01_310, Float maSoVayVaNoThueTaiChinhNganHan_B01_320) {
        return CalculateCommon.sub2Nums(maSoNoNganHan_B01_310, maSoVayVaNoThueTaiChinhNganHan_B01_320);
    }

    public static Float getChiSoWCR(Float taiSanKinhDoanh, Float noKinhDoanh) {
        return CalculateCommon.sub2Nums(taiSanKinhDoanh, noKinhDoanh);
    }

}
