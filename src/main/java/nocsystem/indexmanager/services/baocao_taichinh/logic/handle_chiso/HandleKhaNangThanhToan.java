package nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso;

import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.CacChiSoSoSanhCommon;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.ChiSoKhaNangThanhToan;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoCommon;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HandleKhaNangThanhToan {


    private final HandleChiSoHoatDong handleChiSoHoatDong;

    public HandleKhaNangThanhToan(HandleChiSoHoatDong handleChiSoHoatDong) {
        this.handleChiSoHoatDong = handleChiSoHoatDong;
    }

    public BaoCaoCommon getChiSoThanhToanHienHanh(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float maSoTaiSanNganHan_B01_100 = baoCaoTcHienTai.get("100_B01");
        Float maSoNoNganHan_B01_310 = baoCaoTcHienTai.get("310_B01");
        Float chiSoThucHien = ChiSoKhaNangThanhToan.getChiSoThanhToanHienHanh(maSoTaiSanNganHan_B01_100, maSoNoNganHan_B01_310);

        Float chiSoKHThang = keHoachTcHienTai.get("Chỉ số thanh toán hiện hành (Current Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float maSoTaiSanNganHan_B01_100_CK = baoCaoTcTheoNamN1.get("100_B01");
        Float maSoNoNganHan_B01_310_CK = baoCaoTcTheoNamN1.get("310_B01");
        Float chiSoThucHienCK = ChiSoKhaNangThanhToan.getChiSoThanhToanHienHanh(maSoTaiSanNganHan_B01_100_CK, maSoNoNganHan_B01_310_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoTaiSanNganHan_B01_100_KyTruoc = baoCaoTcKyTruoc.get("100_B01");
        Float maSoNoNganHan_B01_310_KyTruoc = baoCaoTcKyTruoc.get("310_B01");
        Float chiSoThucHienKyTruoc = ChiSoKhaNangThanhToan.getChiSoThanhToanHienHanh(maSoTaiSanNganHan_B01_100_KyTruoc, maSoNoNganHan_B01_310_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Chỉ số thanh toán hiện hành (Current Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoThanhToanNhanh(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float maSoTaiSanNganHan_B01_100 = baoCaoTcHienTai.get("100_B01");
        Float maSoHangTonKho_B01_140 = baoCaoTcHienTai.get("140_B01");
        Float maSoNoNganHan_B01_310 = baoCaoTcHienTai.get("310_B01");
        Float chiSoThucHien = ChiSoKhaNangThanhToan.getChiSoThanhToanNhanh(maSoTaiSanNganHan_B01_100, maSoHangTonKho_B01_140, maSoNoNganHan_B01_310);

        Float chiSoKHThang = keHoachTcHienTai.get("Chỉ số thanh toán nhanh (Quick Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float maSoTaiSanNganHan_B01_100_CK = baoCaoTcTheoNamN1.get("100_B01");
        Float maSoHangTonKho_B01_140_CK = baoCaoTcTheoNamN1.get("140_B01");
        Float maSoNoNganHan_B01_310_CK = baoCaoTcTheoNamN1.get("310_B01");
        Float chiSoThucHienCK = ChiSoKhaNangThanhToan.getChiSoThanhToanNhanh(maSoTaiSanNganHan_B01_100_CK, maSoHangTonKho_B01_140_CK, maSoNoNganHan_B01_310_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoTaiSanNganHan_B01_100_KyTruoc = baoCaoTcKyTruoc.get("100_B01");
        Float maSoHangTonKho_B01_140_KyTruoc = baoCaoTcKyTruoc.get("140_B01");
        Float maSoNoNganHan_B01_310_KyTruoc = baoCaoTcKyTruoc.get("310_B01");
        Float chiSoThucHienKyTruoc = ChiSoKhaNangThanhToan.getChiSoThanhToanNhanh(maSoTaiSanNganHan_B01_100_KyTruoc, maSoHangTonKho_B01_140_KyTruoc, maSoNoNganHan_B01_310_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Chỉ số thanh toán nhanh (Quick Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoThanhToanTucThoi(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc
    ){

        Float maSoTienVaCacKhoanTuongDuongTien_B01_110 = baoCaoTcHienTai.get("110_B01");
        Float maSoDauTuChinhNganHan_B01_120 = baoCaoTcHienTai.get("120_B01");
        Float maSoNoNganHan_B01_310 = baoCaoTcHienTai.get("310_B01");
        Float chiSoThucHien = ChiSoKhaNangThanhToan.getChiSoThanhToanTucThoi(maSoTienVaCacKhoanTuongDuongTien_B01_110, maSoDauTuChinhNganHan_B01_120, maSoNoNganHan_B01_310);

        Float chiSoKHThang = keHoachTcHienTai.get("Chỉ số thanh toán tức thời (Cash Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);

        Float maSoTienVaCacKhoanTuongDuongTien_B01_110_CK = baoCaoTcTheoNamN1.get("110_B01");
        Float maSoDauTuChinhNganHan_B01_120_CK = baoCaoTcTheoNamN1.get("120_B01");
        Float maSoNoNganHan_B01_310_CK = baoCaoTcTheoNamN1.get("310_B01");
        Float chiSoThucHienCK = ChiSoKhaNangThanhToan.getChiSoThanhToanTucThoi(maSoTienVaCacKhoanTuongDuongTien_B01_110_CK, maSoDauTuChinhNganHan_B01_120_CK, maSoNoNganHan_B01_310_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);


        Float maSoTienVaCacKhoanTuongDuongTien_B01_110_KyTruoc = baoCaoTcKyTruoc.get("110_B01");
        Float maSoDauTuChinhNganHan_B01_120_KyTruoc = baoCaoTcKyTruoc.get("120_B01");
        Float maSoNoNganHan_B01_310_KyTruoc = baoCaoTcKyTruoc.get("310_B01");
        Float chiSoThucHienKyTruoc = ChiSoKhaNangThanhToan.getChiSoThanhToanTucThoi(maSoTienVaCacKhoanTuongDuongTien_B01_110_KyTruoc, maSoDauTuChinhNganHan_B01_120_KyTruoc, maSoNoNganHan_B01_310_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Chỉ số thanh toán tức thời (Cash Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoThanhToanLaiVay(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc
    ){


        BaoCaoCommon baoCao = handleChiSoHoatDong.getChiSoEBIT(baoCaoTcHienTai, keHoachTcHienTai, baoCaoTcTheoNamN1, baoCaoTcKyTruoc);

        Float chiSoEBITThang = baoCao.getThucHien();
        Float chiPhiLaiVay_B02_23 = baoCaoTcHienTai.get("23_B02");
        Float chiSoThucHien = ChiSoKhaNangThanhToan.getTySoKhaNangThanhToanLaiVay(chiSoEBITThang, chiPhiLaiVay_B02_23);

        Float chiSoKHThang = keHoachTcHienTai.get("Tỷ số khả năng thanh toán lãi vay (ICR: Interest Coverage Ratio)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);


        Float chiSoEBIT_CK = baoCao.getThucHienCK();
        Float chiPhiLaiVay_B02_23_CK = baoCaoTcTheoNamN1.get("23_B02");
        Float chiSoThucHienCK = ChiSoKhaNangThanhToan.getTySoKhaNangThanhToanLaiVay(chiSoEBIT_CK, chiPhiLaiVay_B02_23_CK);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienCK);

        Float chiSoEBIT_KyTruoc = baoCao.getThucHienKyTruoc();
        Float chiPhiLaiVay_B02_23_KyTruoc = baoCaoTcKyTruoc.get("23_B02");
        Float chiSoThucHienKyTruoc = ChiSoKhaNangThanhToan.getTySoKhaNangThanhToanLaiVay(chiSoEBIT_KyTruoc, chiPhiLaiVay_B02_23_KyTruoc);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("Tỷ số khả năng thanh toán lãi vay (ICR: Interest Coverage Ratio)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienCK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }



}
