package nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan;

import nocsystem.indexmanager.common.CalculateCommon;
import nocsystem.indexmanager.common.DateTimeCommon;
import nocsystem.indexmanager.common.ObjectProcessBinhQuan;
import nocsystem.indexmanager.repositories.baocao_taichinh.logic.BaoCaoTaiChinhRepoImpl;
import nocsystem.indexmanager.repositories.baocao_taichinh.logic.query.BaoCaoTaiChinhSql;
import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoTaiChinhResponseDb;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class ChiSoHieuQuaQuanLySuDungTaiSan {

   @Autowired
   BaoCaoTaiChinhRepoImpl repo ;

    public Float sumValueInList(List<BaoCaoTaiChinhResponseDb> list){
        Float data = 0F;
        if(!(list == null || list.isEmpty())){
            for(BaoCaoTaiChinhResponseDb o : list){
                data = CalculateCommon.sum2Nums(data, o.getGiaTriLoaiBaoCao());
            }
        }
        return data;
    }
    public Float getVongQuayCacKhoanPhaiThu(Float tongDoanhThu_B02_01, BaoCaoTaiChinhBody body , String type) {

        float valueBinhQuan;

        if(type.equals("thang")){
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuandauKy_CuoiKyThang(body);
            BaoCaoTaiChinhResponseDb dbDauKy_130 = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "131");
            BaoCaoTaiChinhResponseDb dbDauKy_210 = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "211");
            BaoCaoTaiChinhResponseDb dbCuoiKy_130 = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "131");
            BaoCaoTaiChinhResponseDb dbCuoiKy_210 = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "211");

            Float a = CalculateCommon.sum2Nums(dbDauKy_130.getGiaTriLoaiBaoCao() , dbCuoiKy_130.getGiaTriLoaiBaoCao());
            Float b = CalculateCommon.sum2Nums(dbDauKy_210.getGiaTriLoaiBaoCao() , dbCuoiKy_210.getGiaTriLoaiBaoCao());

            valueBinhQuan = CalculateCommon.sum2Nums(a, b) / 2;
        } else if (type.equals("quy")) {
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuanDauKy_CuoiKyQuy(body);
            BaoCaoTaiChinhResponseDb dbDauKy_130 = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "131");
            BaoCaoTaiChinhResponseDb dbDauKy_210 = repo.getDataFromMaSo(dauKy_CuoiKythang[0], "211");
            BaoCaoTaiChinhResponseDb dbCuoiKy_130 = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "131");
            BaoCaoTaiChinhResponseDb dbCuoiKy_210 = repo.getDataFromMaSo(dauKy_CuoiKythang[1], "211");

            Float a = CalculateCommon.sum2Nums(dbDauKy_130.getGiaTriLoaiBaoCao() , dbCuoiKy_130.getGiaTriLoaiBaoCao());
            Float b = CalculateCommon.sum2Nums(dbDauKy_210.getGiaTriLoaiBaoCao() , dbCuoiKy_210.getGiaTriLoaiBaoCao());

            valueBinhQuan = CalculateCommon.sum2Nums(a, b) / 2;

        }
        else {
            BaoCaoTaiChinhBody[] bonQuyTrongNam = ObjectProcessBinhQuan.binhQuan4QuyNam(body);
            BaoCaoTaiChinhResponseDb quy1_130 = repo.getDataFromMaSo(bonQuyTrongNam[0], "131");
            BaoCaoTaiChinhResponseDb quy2_130 = repo.getDataFromMaSo(bonQuyTrongNam[1], "131");
            BaoCaoTaiChinhResponseDb quy3_130 = repo.getDataFromMaSo(bonQuyTrongNam[2], "131");
            BaoCaoTaiChinhResponseDb quy4_130 = repo.getDataFromMaSo(bonQuyTrongNam[3], "131");


            BaoCaoTaiChinhResponseDb quy1_210 = repo.getDataFromMaSo(bonQuyTrongNam[0], "211");
            BaoCaoTaiChinhResponseDb quy2_210 = repo.getDataFromMaSo(bonQuyTrongNam[1], "211");
            BaoCaoTaiChinhResponseDb quy3_210 = repo.getDataFromMaSo(bonQuyTrongNam[2], "211");
            BaoCaoTaiChinhResponseDb quy4_210 = repo.getDataFromMaSo(bonQuyTrongNam[3], "211");

            Float a_130 = CalculateCommon.sum2Nums(quy1_130.getGiaTriLoaiBaoCao() , quy2_130.getGiaTriLoaiBaoCao());
            Float b_130 = CalculateCommon.sum2Nums(quy3_130.getGiaTriLoaiBaoCao() , quy4_130.getGiaTriLoaiBaoCao());

            Float a_210 = CalculateCommon.sum2Nums(quy1_210.getGiaTriLoaiBaoCao() , quy2_210.getGiaTriLoaiBaoCao());
            Float b_210 = CalculateCommon.sum2Nums(quy3_210.getGiaTriLoaiBaoCao() , quy4_210.getGiaTriLoaiBaoCao());

            Float a = CalculateCommon.sum2Nums(a_130, b_130);
            Float b = CalculateCommon.sum2Nums(a_210, b_210);
            valueBinhQuan = CalculateCommon.sum2Nums(a, b) / 4;

        }
        return CalculateCommon.div2NumsNoPercent(tongDoanhThu_B02_01, valueBinhQuan);


    }

    public Float tinhBinhQuanCommon(BaoCaoTaiChinhBody body , String type, String maSo){

        float valueBinhQuan;

        if(type.equals("thang")){
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuandauKy_CuoiKyThang(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], maSo);
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], maSo);
            valueBinhQuan = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;
        } else if (type.equals("quy")) {
            BaoCaoTaiChinhBody[] dauKy_CuoiKythang = ObjectProcessBinhQuan.binhQuanDauKy_CuoiKyQuy(body);
            BaoCaoTaiChinhResponseDb dbDauKy = repo.getDataFromMaSo(dauKy_CuoiKythang[0], maSo);
            BaoCaoTaiChinhResponseDb dbCuoiKy = repo.getDataFromMaSo(dauKy_CuoiKythang[1], maSo);
            valueBinhQuan = CalculateCommon.sum2Nums(dbDauKy.getGiaTriLoaiBaoCao() , dbCuoiKy.getGiaTriLoaiBaoCao()) / 2;
        }
        else {
            BaoCaoTaiChinhBody[] bonQuyTrongNam = ObjectProcessBinhQuan.binhQuan4QuyNam(body);
            BaoCaoTaiChinhResponseDb quy1 = repo.getDataFromMaSo(bonQuyTrongNam[0], maSo);
            BaoCaoTaiChinhResponseDb quy2 = repo.getDataFromMaSo(bonQuyTrongNam[1], maSo);
            BaoCaoTaiChinhResponseDb quy3 = repo.getDataFromMaSo(bonQuyTrongNam[2], maSo);
            BaoCaoTaiChinhResponseDb quy4 = repo.getDataFromMaSo(bonQuyTrongNam[3], maSo);

            Float sumQuy12 = CalculateCommon.sum2Nums(quy1.getGiaTriLoaiBaoCao() , quy2.getGiaTriLoaiBaoCao());
            Float sumQuy34 = CalculateCommon.sum2Nums(quy3.getGiaTriLoaiBaoCao() , quy4.getGiaTriLoaiBaoCao());
            valueBinhQuan = CalculateCommon.sum2Nums(sumQuy12, sumQuy34) / 4;

        }
        return valueBinhQuan;
    }




    public Float getKyThuTienBinhQuan(Float tongDoanhThu_B02_01, Float maSoPhaiThuNganHan_B01_130, Float maSoPhaiThuDai_B01_210) {
        if(tongDoanhThu_B02_01 == null || tongDoanhThu_B02_01 == 0) return null;
        return 365 * CalculateCommon.sum2Nums(maSoPhaiThuNganHan_B01_130, maSoPhaiThuDai_B01_210) / tongDoanhThu_B02_01;
    }

    public Float getVongQuayCacKhoanPhaiTra(Float cacKhoanPhaiTraNCC_B01_300 , Float maSoVon_B02_11) {
        if(cacKhoanPhaiTraNCC_B01_300 == null ||  cacKhoanPhaiTraNCC_B01_300 == 0) return null;
        return CalculateCommon.div2NumsNoPercent(maSoVon_B02_11  , cacKhoanPhaiTraNCC_B01_300);
    }

    public Float getKyTraTienBinhQuan( Float vongQuayCacKhoanPhaiTra) {
        // todo : maSo la 300
        if(vongQuayCacKhoanPhaiTra == null ||  vongQuayCacKhoanPhaiTra == 0) return null;
        return 365 / vongQuayCacKhoanPhaiTra;
    }


    public Float getVongQuayHangTonKho(Float hangTonKhoBinhQuan_B01_140,  Float maSoVon_B02_11) {
        // todo : 140
        if(maSoVon_B02_11 == null || hangTonKhoBinhQuan_B01_140 == null ||  hangTonKhoBinhQuan_B01_140 == 0) return null;
        return maSoVon_B02_11 / hangTonKhoBinhQuan_B01_140;
    }

    public Float getSoNgayLuuKhoBinhQuan( Float hangTonKhoBinhQuan_B01_140, Float maSoVon_B02_11) {
        if(hangTonKhoBinhQuan_B01_140 == null || maSoVon_B02_11 == null ||  maSoVon_B02_11 == 0) return null;
        return 365*hangTonKhoBinhQuan_B01_140 / maSoVon_B02_11;
    }

    public Float getVongQuayTongTaiSan(
            Float maSoDoanhThuBanHang_B02_01,
            Float maSoDoanhThuTaiChinh_B02_21,
            Float maSoThuNhapKhac_B02_31,
            Float tongTaiSanBinhQuan_B01_270
            ) {

        // todo : tongTaiSanBinhQuan : 270
        if(tongTaiSanBinhQuan_B01_270 == null ||  tongTaiSanBinhQuan_B01_270 == 0) return null;
        return CalculateCommon.sum2Nums(CalculateCommon.sum2Nums(maSoDoanhThuBanHang_B02_01, maSoDoanhThuTaiChinh_B02_21), maSoThuNhapKhac_B02_31) / tongTaiSanBinhQuan_B01_270;
    }

}
