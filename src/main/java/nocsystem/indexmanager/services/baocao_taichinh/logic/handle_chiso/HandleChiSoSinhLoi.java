package nocsystem.indexmanager.services.baocao_taichinh.logic.handle_chiso;


import nocsystem.indexmanager.services.baocao_taichinh.BaoCaoTaiChinhBody;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.CacChiSoSoSanhCommon;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.ChiSoHoatDong;
import nocsystem.indexmanager.services.baocao_taichinh.logic.tinhToan.ChiSoSinhLoiLogic;
import nocsystem.indexmanager.services.baocao_taichinh.response.BaoCaoCommon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HandleChiSoSinhLoi {

    @Autowired
    ChiSoSinhLoiLogic chiSoSinhLoiLogic;
    public BaoCaoCommon getChiSoROCE(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,

            Map<String, Float> baoCaoTcKyTruoc){

        Float chiSoEBIT = ChiSoHoatDong.getChiSoEBIT(baoCaoTcHienTai.get("50_B02"), baoCaoTcHienTai.get("23_B02"));
        Float chiSoThucHienROCE = chiSoSinhLoiLogic.getChiSoThucHienROCE(chiSoEBIT, baoCaoTcHienTai.get("270_B01"), baoCaoTcHienTai.get("100_B01"));

        Float chiSoKHThang = keHoachTcHienTai.get("ROCE (Return On Capital Employed)_KH");
        Float soSanhKHROCE = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHienROCE, chiSoKHThang);

        Float chiSoEBITTheoN1 = ChiSoHoatDong.getChiSoEBIT(baoCaoTcTheoNamN1.get("50_B02"), baoCaoTcTheoNamN1.get("23_B02"));
        Float chiSoThucHienROCECK = chiSoSinhLoiLogic.getChiSoThucHienROCE(chiSoEBITTheoN1, baoCaoTcTheoNamN1.get("270_B01"), baoCaoTcTheoNamN1.get("100_B01"));
        Float chiSoSoSanhCK = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHienROCE, chiSoThucHienROCECK);

        Float chiSoEBITTheoKyTruoc = ChiSoHoatDong.getChiSoEBIT(baoCaoTcKyTruoc.get("50_B02"), baoCaoTcKyTruoc.get("23_B02"));
        Float chiSoThucHienROCETheoKyTruoc = chiSoSinhLoiLogic.getChiSoThucHienROCE(chiSoEBITTheoKyTruoc, baoCaoTcKyTruoc.get("270_B01"), baoCaoTcKyTruoc.get("100_B01"));
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHienROCE, chiSoThucHienROCETheoKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("ROCE (Return On Capital Employed)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHienROCE, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHienROCE);
        baoCaoCommon.setThucHienCK(chiSoThucHienROCECK);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienROCETheoKyTruoc);
        baoCaoCommon.setThucHien(chiSoThucHienROCE);
        baoCaoCommon.setSoSanhKH(soSanhKHROCE);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCK);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }

    public BaoCaoCommon getChiSoROTC(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,

            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc,

            BaoCaoTaiChinhBody body,
            BaoCaoTaiChinhBody bodyN1,
            BaoCaoTaiChinhBody bodyKyTruoc,
            String type){


        Float thuNhapDongHienTai = baoCaoTcHienTai.get("60_B02");
        Float chiPhiLaiVayHienTai = baoCaoTcHienTai.get("23_B02");
        Float chiSoROTCHienTai = chiSoSinhLoiLogic.getChiSoThucHienROTC(thuNhapDongHienTai, chiPhiLaiVayHienTai, body, type);

        Float chiSoKHThang = keHoachTcHienTai.get("ROTC (Return On Total Capital)_KH");
        Float soSanhKHROTC = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoROTCHienTai, chiSoKHThang);


        Float thuNhapDongN1 = baoCaoTcTheoNamN1.get("60_B02");
        Float chiPhiLaiVayN1 = baoCaoTcTheoNamN1.get("23_B02");
        Float chiSoROTCN1 = chiSoSinhLoiLogic.getChiSoThucHienROTC(thuNhapDongN1, chiPhiLaiVayN1, bodyN1, type);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoROTCHienTai, chiSoROTCN1);


        Float thuNhapDongKyTruoc = baoCaoTcKyTruoc.get("60_B02");
        Float chiPhiLaiVayKyTruoc = baoCaoTcKyTruoc.get("23_B02");
        Float chiSoROTCKyTruoc = chiSoSinhLoiLogic.getChiSoThucHienROTC(thuNhapDongKyTruoc, chiPhiLaiVayKyTruoc, bodyKyTruoc, type);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoROTCHienTai, chiSoROTCKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("ROTC (Return On Total Capital)_N3");

        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoROTCHienTai, chiSoNgay3);


        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoROTCHienTai);
        baoCaoCommon.setThucHienCK(chiSoROTCN1);
        baoCaoCommon.setThucHienKyTruoc(chiSoROTCKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKHROTC);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoROE(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body,
            BaoCaoTaiChinhBody bodyN1,
            BaoCaoTaiChinhBody bodyKyTruoc,
            String type){

        Float loiNhuanSauThue_B02_60 = baoCaoTcHienTai.get("60_B02");
        Float chiSoThucHienHienTai = chiSoSinhLoiLogic.getChiSoThucHienROE(loiNhuanSauThue_B02_60, body, type);

        Float chiSoKHThang = keHoachTcHienTai.get("ROE (Return On Equity)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHienHienTai, chiSoKHThang);


        Float loiNhuanSauThueCK = baoCaoTcTheoNamN1.get("60_B02");
        Float chiSoN1 = chiSoSinhLoiLogic.getChiSoThucHienROE(loiNhuanSauThueCK, bodyN1, type);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHienHienTai, chiSoN1);

        Float loiNhuanThueKyTruoc = baoCaoTcKyTruoc.get("60_B02");
        Float chiSoKyTruoc = chiSoSinhLoiLogic.getChiSoThucHienROE(loiNhuanThueKyTruoc, bodyKyTruoc, type);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHienHienTai, chiSoKyTruoc);

        Float chiSoNgay3 = keHoachTcHienTai.get("ROE (Return On Equity)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHienHienTai, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHienHienTai);
        baoCaoCommon.setThucHienCK(chiSoN1);
        baoCaoCommon.setThucHienKyTruoc(chiSoKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }


    public  BaoCaoCommon getChiSoROA(
            Map<String, Float> baoCaoTcHienTai ,
            Map<String, Float> keHoachTcHienTai,
            Map<String, Float> baoCaoTcTheoNamN1,
            Map<String, Float> baoCaoTcKyTruoc,
            BaoCaoTaiChinhBody body,
            BaoCaoTaiChinhBody bodyN1,
            BaoCaoTaiChinhBody bodyKyTruoc,
            String type
            ){

        Float loiNhuanSauThue_B02_60 = baoCaoTcHienTai.get("60_B02");
//        Float tongTaiSan_B01_270 = baoCaoTcHienTai.get("270_B01");
//        Float chiSoThucHien = chiSoSinhLoiLogic.getChiSoThucHienROA(loiNhuanSauThue_B02_60, tongTaiSan_B01_270);
        Float chiSoThucHien = chiSoSinhLoiLogic.getChiSoThucHienROA(loiNhuanSauThue_B02_60, body, type);

        Float chiSoKHThang = keHoachTcHienTai.get("ROA (Return On Assets)_KH");
        Float soSanhKH = CacChiSoSoSanhCommon.soSanhKeHoach(chiSoThucHien, chiSoKHThang);

        Float loiNhuanSauThue_B02_60_N1 = baoCaoTcTheoNamN1.get("60_B02");
//        Float tongTaiSan_B01_270_N1 = baoCaoTcTheoNamN1.get("270_B01");
//        Float chiSoThucHienTheoN1 = chiSoSinhLoiLogic.getChiSoThucHienROA(loiNhuanSauThue_B02_60_N1, tongTaiSan_B01_270_N1);
        Float chiSoThucHienTheoN1 = chiSoSinhLoiLogic.getChiSoThucHienROA(loiNhuanSauThue_B02_60_N1, bodyN1, type);
        Float chiSoSoSanhCungKy = CacChiSoSoSanhCommon.soSanhCungKy(chiSoThucHien, chiSoThucHienTheoN1);


        Float loiNhuanSauThue_B02_60_KyTruoc = baoCaoTcKyTruoc.get("60_B02");
//        Float tongTaiSan_B01_270_KyTruoc = baoCaoTcKyTruoc.get("270_B01");
//        Float chiSoThucHienKyTruoc = chiSoSinhLoiLogic.getChiSoThucHienROA(loiNhuanSauThue_B02_60_KyTruoc, tongTaiSan_B01_270_KyTruoc);
        Float chiSoThucHienKyTruoc = chiSoSinhLoiLogic.getChiSoThucHienROA(loiNhuanSauThue_B02_60_KyTruoc, bodyKyTruoc, type);
        Float chiSoSoSanhKyTruoc = CacChiSoSoSanhCommon.soSanhKyTruoc(chiSoThucHien, chiSoThucHienKyTruoc);


        Float chiSoNgay3 = keHoachTcHienTai.get("ROA (Return On Assets)_N3");
        Float chiSoSoSanhBcNgay3 = CacChiSoSoSanhCommon.soSanhBcNgay3(chiSoThucHien, chiSoNgay3);

        BaoCaoCommon baoCaoCommon = new BaoCaoCommon();
        baoCaoCommon.setThucHien(chiSoThucHien);
        baoCaoCommon.setThucHienCK(chiSoThucHienTheoN1);
        baoCaoCommon.setThucHienKyTruoc(chiSoThucHienKyTruoc);
        baoCaoCommon.setSoSanhKH(soSanhKH);
        baoCaoCommon.setSoSanhCK(chiSoSoSanhCungKy);
        baoCaoCommon.setSoSanhKyTruoc(chiSoSoSanhKyTruoc);
        baoCaoCommon.setSoSanhNgay3(chiSoSoSanhBcNgay3);

        return baoCaoCommon;
    }



}
