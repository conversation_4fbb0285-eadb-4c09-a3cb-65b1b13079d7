package nocsystem.indexmanager.models.KpiDayChuyenChiaChon;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "kpi_dccc_ttkt5")
public class KpiDayChuyenChiaChonEntity {
    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaocao;

    @Column(name = "stt")
    private int stt;

    @Column(name = "noi_dung")
    private String noiDung;

    @Column(name = "kpi")
    private Double kpi;

    @Column(name = "ket_qua")
    private Double ketQua;
}
