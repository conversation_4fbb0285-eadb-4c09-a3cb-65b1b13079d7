package nocsystem.indexmanager.models.canhbao.TrungTam;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "dm_ten_bc_cn")
public class ListBuuCucChiNhanh {

    @Id
    @Column(name = "chinhanh")
    private String chiNhanh;

    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "ten_buucuc")
    private String tenBuuCuc;

    @Column(name = "ten_chinhanh")
    private String tenChiNhanh;

}
