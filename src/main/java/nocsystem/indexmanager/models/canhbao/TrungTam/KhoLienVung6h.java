package nocsystem.indexmanager.models.canhbao.TrungTam;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "ctgskholienvung6h")
public class KhoLienVung6h {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chi_nhanh")
    private String chiNhanh;

    @Column(name = "buu_cuc")
    private String buuCuc;

    @Column(name = "ma_phieugui")
    private String maPhieuGui;

    @Column(name = "ngay_tao_don")
    private String ngayTaoDon;

    @Column(name = "moc_ton")
    private String mocTon;

    @Column(name = "qua_0h")
    private Float qua0h;

    @Column(name = "qua_2h")
    private Float qua2h;

    @Column(name = "qua_6h")
    private Float qua6h;

    @Column(name = "qua_12h")
    private Float qua12h;

    @Column(name = "qua_24h")
    private Float qua24h;

    @Column(name = "qua_48h")
    private Float qua48h;

    @Column(name = "qua_72h")
    private Float qua72h;

    @Column(name = "qua_96h")
    private Float qua96h;

    @Column(name = "qua_120h")
    private Float qua120h;

    @Column(name = "tong")
    private Float tong;

}
