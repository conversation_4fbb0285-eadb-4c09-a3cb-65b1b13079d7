package nocsystem.indexmanager.models.canhbao.TrungTam;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "trungtam_khaithac_log_detail")
public class TonTTKTBill {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "ma_phieugui")
    private String maPhieuGui;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "huyen_nhan")
    private Integer huyenNhan;

    @Column(name = "ten_huyen_nhan")
    private String tenHuyenNhan;

    @Column(name = "ttkt_from")
    private String ttktFrom;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "huyen_phat")
    private Integer huyenPhat;

    @Column(name = "ten_huyen_phat")
    private String tenHuyenPhat;

    @Column(name = "ttkt_to")
    private String ttktTo;

    @Column(name = "ma_dv_viettel")
    private String maDvViettel;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "time_tac_dong")
    private String timeTacDong;

    @Column(name = "trang_thai")
    private String trangThai;

    @Column(name = "ma_buucuc_ht")
    private String maBuuCucHT;

    @Column(name = "chi_nhanh_ht")
    private String chiNhanhHT;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "ma_khgui")
    private String maKHGui;

    @Column(name = "ma_buucuc_phat")
    private String maBuuCucPhat;

    @Column(name = "time_pcp")
    private String timePCP;

    @Column(name = "time_gach_bp")
    private String timeGachBP;

    @Column(name = "ngay_gui_bp")
    private String ngayguiBP;

    @Column(name = "danh_gia")
    private String danhGia;

    @Column(name = "loai_pg")
    private Integer loaiPG;

    @Column(name = "lan_phat")
    private Integer lanPhat;

    @Column(name = "tg_con_phat")
    private Float tgConPhat;

    @Column(name = "buu_ta_phat")
    private String buuTaPhat;

    @Column(name = "tien_cod")
    private Long tienCOD;

    @Column(name = "khau_fm")
    private Float khauFM;

    @Column(name = "khau_mm")
    private Float khauMM;

    @Column(name = "khau_lm")
    private Float khauLM;

    @Column(name = "tg_quydinh")
    private Integer tgQuyDinh;

    @Column(name = "tg_tt_luyke")
    private Integer tgTTLuyKe;

    @Column(name = "tg_chenhlech")
    private Integer tgChenhLech;

    @Column(name = "loai_vung")
    private String loaiVung;

    @Column(name = "nhom_dv")
    private String nhomDV;

    @Column(name = "is_checked")
    private String isChecked;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "moc_ton_xe")
    private String mocTonXe;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

}
