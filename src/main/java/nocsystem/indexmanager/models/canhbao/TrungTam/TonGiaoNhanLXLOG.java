package nocsystem.indexmanager.models.canhbao.TrungTam;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "ton_giaonhan_laixe_log")
public class TonGiaoNhanLXLOG {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chi_nhanh_ht")
    private String chiNhanh;

    @Column(name = "ma_buucuc_ht")
    private String buuCuc;

    @Column(name = "ngay_gui_bp")
    private String ngayTaoDon;

    @Column(name = "nhom_dv")
    private String nhomDV;

    @Column(name = "loai_vung")
    private String loaiVung;

    @Column(name = "ma_phieugui")
    private String maPhieuGui;

    @Column(name = "moc_ton_xe")
    private String mocTon;

    @Column(name = "qua_0h")
    private Long qua0h;

    @Column(name = "qua_2h")
    private Long qua2h;

    @Column(name = "qua_6h")
    private Long qua6h;

    @Column(name = "qua_12h")
    private Long qua12h;

    @Column(name = "qua_24h")
    private Long qua24h;

    @Column(name = "qua_48h")
    private Long qua48h;

    @Column(name = "qua_72h")
    private Long qua72h;

    @Column(name = "qua_96h")
    private Long qua96h;

    @Column(name = "qua_120h")
    private Long qua120h;

    @Column(name = "tongsl")
    private Long tong;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

}
