package nocsystem.indexmanager.models.canhbao.TonPhat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "tonphatkhaumm")
public class TonPhatKhauMM {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "loai_ton")
    private String loaiTon;

    @Column(name = "loai_don")
    private String loaiDon;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "dg_moc_mm")
    private String dGMocMM;

    @Column(name = "ma_buu_cuc")
    private String maBuuCuc;

    @Column(name = "buu_ta_phat")
    private String buuTaPhat;

    @Column(name = "sltp_nhanh")
    private Long sLTPNhanh;

    @Column(name = "sltp_cham")
    private Long sLTPCham;

    @Column(name = "sltp_hoatoc")
    private Long sLTPHoaToc;

    @Column(name = "tongsl")
    private Long tongSL;

}
