package nocsystem.indexmanager.models.canhbao.TonPhat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "tonphat_nguong_hienthi")
public class TonPhatNguongNew {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "don_vi")
    private String tinhPhat;

    @Column(name = "loai_ton")
    private String loaiTon;

    @Column(name = "nguong_ton")
    private String nguongTon;

    @Column(name = "ma_buucuc_phat")
    private String maBuuCuc;

    @Column(name = "buu_ta_phat")
    private String buuTaPhat;

    @Column(name = "dv_nhanh")
    private Long dvNhanh;

    @Column(name = "dv_cham")
    private Long dvCham;

    @Column(name = "dv_hoatoc")
    private Long dvHoaToc;

    @Column(name = "tongsl")
    private Long tongSL;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

}
