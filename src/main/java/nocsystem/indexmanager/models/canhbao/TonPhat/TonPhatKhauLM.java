package nocsystem.indexmanager.models.canhbao.TonPhat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
@Table(name = "tonphat_khau_hienthi")
public class TonPhatKhauLM {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "loai_ton")
    private String loaiTon;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "ma_buu_cuc")
    private String maBuuCuc;

    @Column(name = "buu_ta_phat")
    private String buuTaPhat;

    @Column(name = "dg_moc_mm")
    private String dgMocMM;

    @Column(name = "nhom_dv")
    private String nhomDV;

    @Column(name = "lm_vang")
    private Long lMVang;

    @Column(name = "lm_do_1")
    private Long lMDo1;

    @Column(name = "lm_do_2")
    private Long lMDo2;

    @Column(name = "lm_do_3")
    private Long lMDo3;

    @Column(name = "lm_do_4")
    private Long lMDo4;

    @Column(name = "lm_do_5")
    private Long lMDo5;

    @Column(name = "lm_do_6")
    private Long lMDo6;

    @Column(name = "lm_do_7")
    private Long lMDo7;

    @Column(name = "lm_do_8")
    private Long lMDo8;

    @Column(name = "lm_do_9")
    private Long lMDo9;

    @Column(name = "tongsl")
    private Long tongSL;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

}
