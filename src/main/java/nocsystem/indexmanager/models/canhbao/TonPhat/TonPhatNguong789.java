package nocsystem.indexmanager.models.canhbao.TonPhat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "tonphatnguong789")
public class TonPhatNguong789 {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "loai_ton")
    private String loaiTon;

    @Column(name = "loai_don")
    private String loaiDon;

    @Column(name = "ma_buu_cuc")
    private String maBuuCuc;

    @Column(name = "buu_ta_phat")
    private String buuTaPhat;

    @Column(name = "sln7_nhanh")
    private Long sln7Nhanh;

    @Column(name = "sln7_cham")
    private Long sln7Cham;

    @Column(name = "sln7_hoatoc")
    private Long sln7HoaToc;

    @Column(name = "sln7_tongsl")
    private Long sln7TongSL;

    @Column(name = "sln8_nhanh")
    private Long sln8Nhanh;

    @Column(name = "sln8_cham")
    private Long sln8Cham;

    @Column(name = "sln8_hoatoc")
    private Long sln8HoaToc;

    @Column(name = "sln8_tongsl")
    private Long sln8TongSL;

    @Column(name = "sln9_nhanh")
    private Long sln9Nhanh;

    @Column(name = "sln9_cham")
    private Long sln9Cham;

    @Column(name = "sln9_hoatoc")
    private Long sln9HoaToc;

    @Column(name = "sln9_tongsl")
    private Long sln9TongSL;

    @Column(name = "tongsl_n789")
    private Long tongSLN789;

    @Column(name = "tongsl")
    private Long tongSL;

    @Column(name = "tile_n9")
    private Float tiLeN9;

    @Column(name = "tile_n789")
    private Float tiLeN789;
}
