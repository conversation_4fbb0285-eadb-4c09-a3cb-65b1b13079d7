package nocsystem.indexmanager.models.canhbao.TonPhat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
//@Table(name = "all_tonthu")
@Table(name = "tonthu_tonghop_v2")
public class TonThu {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chi_nhanh")
    private String chiNhanh;

    @Column(name = "vung_con")
    private String vungCon;

    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "buu_ta_nhan")
    private String buuTaNhan;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "trang_thai")
    private String trangThai;

    @Column(name = "tong_sl")
    private Long tongSL;

    @Column(name = "ca")
    private String ca;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "loai_hang")
    private String loaiHH;

    @Column(name = "kh_dacthu_gui")
    private String khDacThuGui;

    @Column(name = "kh_dacthu_nhan")
    private String khDacThuNhan;

    @Column(name = "ma_khgui")
    private String maKhGui;

    @Column(name = "shop_ton")
    private String shopTon;

    @Column(name = "version")
    private Long version;
}
