package nocsystem.indexmanager.models.canhbao.TonPhat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "tonphat_tonghop_v2")
public class TonPhat {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chi_nhanh")
    private String chiNhanh;

    @Column(name = "loai_ton")
    private String loaiTon;

    @Column(name = "vung_con")
    private String vungCon;

    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "buu_ta_phat")
    private String buuTaPhat;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "trang_thai")
    private String trangThai;

    @Column(name = "tong_sl")
    private Long tongSL;

    @Column(name = "nhom_dv")
    private String nhomDV;

    @Column(name = "version")
    private Long version;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "loai_hh")
    private String loaiHH;

    @Column(name = "kh_dacthu_gui")
    private String khDacThuGui;

    @Column(name = "kh_dacthu_nhan")
    private String khDacThuNhan;

}
