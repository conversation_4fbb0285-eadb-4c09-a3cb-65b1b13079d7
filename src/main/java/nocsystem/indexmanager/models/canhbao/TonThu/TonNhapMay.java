package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;
import nocsystem.indexmanager.models.canhbao.TonPhat.TonHanhTrinhKey;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@Entity
//@IdClass(TonNhapMayKey.class)
@Table(name = "sl_ton_nhap_may_chua_ket_noi_ngay")
public class TonNhapMay {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_nhan")
    private String chiNhanh;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCuc;

    @Column(name = "tong_sl")
    private Long tongSl;

    @Column(name = "type")
    private Long type;

    @Column(name = "status")
    private Long status;

    @Column(name = "update_at")
    private String update_at;


}
