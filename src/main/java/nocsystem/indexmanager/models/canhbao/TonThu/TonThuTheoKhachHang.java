package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "tonthukh_bs")
public class TonThuTheoKhachHang {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chi_nhanh_nhan")
    private String chiNhanhNhan;

    @Column(name = "buu_cuc_nhan")
    private String buuCucNhan;

    @Column(name = "buu_ta_nhan")
    private String buuTaNhan;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "kh_0")
    private Long khachHang0;

    @Column(name = "kh_1")
    private Long khachHang1;

    @Column(name = "kh_2")
    private Long khachHang2;

    @Column(name = "kh_3")
    private Long khachHang3;

    @Column(name = "kh_4")
    private Long khachHang4;

    @Column(name = "kh_5")
    private Long khachHang5;

    @Column(name = "kh_6")
    private Long khachHang6;

    @Column(name = "kh_7")
    private Long khachHang7;

    @Column(name = "kh_8")
    private Long khachHang8;

    @Column(name = "tong")
    private Long tong;

    @Column(name = "ca")
    private String ca;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;
}
