package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@Entity
@IdClass(TonNhapMayKey.class)
//@Table(name = "dash_ton_nhap_may_excel")
@Table(name = "ton_nhap_may_chua_ket_noi")
public class TonNhapMay2 {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "id_phieugui")
    private String idPhieuGui;

    @Id
    @Column(name = "ma_phieugui")
    private String maPhieuGui;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "huyen_nhan")
    private String huyenNhan;

    @Column(name = "ten_huyen_nhan")
    private String tenHuyenNhan;

    @Column(name = "ttkt_from")
    private String ttktFrom;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "huyen_phat")
    private String huyenPhat;

    @Column(name = "ten_huyen_phat")
    private String tenHuyenPhat;

    @Column(name = "ttkt_to")
    private String ttktTo;

    @Column(name = "ma_dv_viettel")
    private String maDvViettel;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "time_tac_dong")
    private String timeTacDong;

    @Column(name = "trang_thai")
    private String trangThai;

    @Column(name = "ma_buucuc_ht")
    private String maBuuCucHt;

    @Column(name = "chi_nhanh_ht")
    private String chiNhanhHt;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "ma_khgui")
    private String maKhGui;

    @Column(name = "ma_buucuc_phat")
    private String maBuuCucPhat;

    @Column(name = "time_pcp")
    private String timePCP;

    @Column(name = "time_gach_bp")
    private String timeGachBP;

    @Column(name = "ngay_gui_bp")
    private String ngayGuiBP;

    @Column(name = "danh_gia")
    private String danhGia;

    @Column(name = "loai_pg")
    private String loaiPG;

    @Column(name = "lan_phat")
    private String lanPhat;

    @Column(name = "tg_con_phat")
    private String tgConPhat;

    @Column(name = "buu_ta_phat")
    private String buuTaPhat;

    @Column(name = "tien_cod")
    private String tienCOD;

    @Column(name = "khau_fm")
    private String khauFM;

    @Column(name = "khau_mm")
    private String khauMM;

    @Column(name = "khau_lm")
    private String khauLM;

    @Column(name = "tg_quydinh")
    private String tgQuyDinh;

    @Column(name = "tg_tt_luyke")
    private String tgTTLuyKe;

    @Column(name = "tg_chenhlech")
    private String tgChenhLech;

    @Column(name = "ton_xu_ly")
    private String tonXuLy;

    @Column(name = "moc_tg")
    private String mocTG;

    @Column(name = "moc_ton_xl")
    private String mocTonXL;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "nhom_dv")
    private String nhomDV;

    @Id
    @Column(name = "status")
    private Long status;

    @Column(name = "update_at")
    private String updatedAt;

    @Column(name = "nguoi_nhap_may")
    private String nguoiNhapMay;

    @Column(name = "ngay_nhap_may")
    private String ngayNhapMay;

    @Column(name = "loai_hh")
    private String loaiHH;

    @Column(name = "tien_cuoc")
    private String tienCuoc;

    @Column(name = "ma_dv_cong_them")
    private String maDvCongThem;

    @Column(name = "trong_luong")
    private String trongLuong;

    @Column(name = "time_ps")
    private String timePs;

    @Column(name = "loai_don")
    private String loaiDon;

}
