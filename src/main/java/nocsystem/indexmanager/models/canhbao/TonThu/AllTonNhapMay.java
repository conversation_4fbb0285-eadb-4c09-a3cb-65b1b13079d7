package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "all_ton_nhap_may_chua_ket_noi")
public class AllTonNhapMay {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_nhan")
    private String chiNhanh;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "tong_sl")
    private Long tongSl;

    @Column(name = "status")
    private Long status;

    @Column(name = "update_at")
    private String updateAt;

    @Column(name = "dich_vu")
    private String dichVu;

    @Column(name = "loai_don")
    private String loaiDon;

    @Column(name = "sl_ntc")
    private Long slNTC;

    @Column(name = "tong_kl")
    private Long klChuaKetNoi;

    @Column(name = "kl_ntc")
    private Long klNTC;

}
