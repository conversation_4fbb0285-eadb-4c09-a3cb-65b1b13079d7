package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "all_ton_hanh_trinh_cuoi_thu")
public class AllTonHanhTrinh {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "tong_sl")
    private Long tongSl;

    @Column(name = "status")
    private Long status;

    @Column(name = "update_at")
    private String updateAt;

    @Column(name = "type")
    private Long type;
}
