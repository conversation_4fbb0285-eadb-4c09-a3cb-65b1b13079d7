package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
//@Table(name = "tonthu_detail")
@Table(name = "tonthu_chitiet_v2")
public class TonThuBill {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "ma_phieugui")
    private String maPhieuGui;

    @Column(name = "trang_thai")
    private String trangThai;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "kh_vip")
    private Integer khVIP;

    @Column(name = "ngay_tao")
    private Date ngayTao;

    @Column(name = "ngay_duyet")
    private String ngayDuyet;

    @Column(name = "ngay_hen_thu")
    private String ngayHenThu;

    @Column(name = "tinh_khgui")
    private String tinhKHGui;

    @Column(name = "ten_khgui")
    private String tenKHGui;

    @Column(name = "diachi_khgui")
    private String diaChiKHGui;

    @Column(name = "tinh_khnhan")
    private String tinhKHNhan;

    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "chi_nhanh")
    private String chiNhanh;

    @Column(name = "ma_dv_viettel")
    private String maDVViettel;

    @Column(name = "tong_tien")
    private Long tongTien;

    @Column(name = "buuta_nhan")
    private String buuTaNhan;

    @Column(name = "ten_buu_ta")
    private String tenBuuTa;

    @Column(name = "tg_ton")
    private Long tgTon;

    @Column(name = "ngay_ton")
    private Integer ngayTon;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "nhom_dv")
    private String nhomDV;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

    @Column(name = "ca")
    private String ca;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "vung_con")
    private String vungCon;

    @Column(name = "loai_hang")
    private String loaiHH;

    @Column(name = "kh_dacthu_gui")
    private String khDacThuGui;

    @Column(name = "kh_dacthu_nhan")
    private String khDacThuNhan;

    @Column(name = "ma_khgui")
    private String maKhGui;

    @Column(name = "id_phuongxa_nhan")
    private String idPhuongXaNhan;
    @Column(name = "ten_phuongxa_nhan")
    private String tenPhuongXaNhan;

    @Column(name = "loai_don")
    private String loaiDon;

    @Column(name = "ten_quanhuyen_nhan")
    private String tenQuanHuyenNhan;

    @Column(name = "ma_dv_congthem")
    private String maDichVuCongThem;

    @Column(name = "version")
    private Long version;

    @Column(name = "time_102_cuoicung")
    private String time102CuoiCung;

    @Column(name = "so_lan_102_luy_ke")
    private Integer soLan102LuyKe;

    @Column(name = "so_lan_102_trong_ngay")
    private Integer soLan102TrongNgay;
}
