package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "all_ton_chua_pcp")
public class AllChuaPCP {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chi_nhanh_ht")
    private String chiNhanhHT;

    @Column(name = "ma_buucuc_ht")
    private String maBuuCucHT;

    @Column(name = "tong_sl")
    private Long tongSl;

    @Column(name = "status")
    private Long status;

    @Column(name = "update_at")
    private String updateAt;

}
