package nocsystem.indexmanager.models.canhbao.TonThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "tonthutt_bs")
public class TonThuTheoTrangThai {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chi_nhanh_nhan")
    private String chiNhanhNhan;

    @Column(name = "buu_cuc_nhan")
    private String buuCucNhan;

    @Column(name = "buu_ta_nhan")
    private String buuTaNhan;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "ca")
    private String ca;

    @Column(name = "tt_100")
    private Float trangThai100;

    @Column(name = "tt_102")
    private Float trangThai102;

    @Column(name = "tt_103")
    private Float trangThai103;

    @Column(name = "tt_104")
    private Float trangThai104;

    @Column(name = "tong")
    private Float tong;

    @Column(name = "status")
    private Integer status;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;
}
