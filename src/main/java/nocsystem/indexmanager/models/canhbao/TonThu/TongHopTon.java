package nocsystem.indexmanager.models.canhbao.TonThu;

import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Synchronize;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Entity
@Subselect("(select a from AllTonNhapMay a) " +
        "union all (select b from AllChuaPCP b) " +
        "union all (select c from AllTonHanhTrinh c)")
@Synchronize({"AllTonHanhTrinh","AllTonNhapMay","AllChuaPCP"})
@Immutable
@Table(name = "all_ton_hanh_trinh_cuoi_thu")
public class TongHopTon {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "tong_sl")
    private Long tongSl;

    @Column(name = "status")
    private Long status;

    @Column(name = "update_at")
    private String updateAt;

    @Column(name = "type")
    private Long type;

    public TongHopTon() {

    }

    public LocalDate getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(LocalDate ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getTinhNhan() {
        return tinhNhan;
    }

    public void setTinhNhan(String tinhNhan) {
        this.tinhNhan = tinhNhan;
    }

    public String getMaBuuCucGoc() {
        return maBuuCucGoc;
    }

    public void setMaBuuCucGoc(String maBuuCucGoc) {
        this.maBuuCucGoc = maBuuCucGoc;
    }

    public Long getTongSl() {
        return tongSl;
    }

    public void setTongSl(Long tongSl) {
        this.tongSl = tongSl;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(String updateAt) {
        this.updateAt = updateAt;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }
}
