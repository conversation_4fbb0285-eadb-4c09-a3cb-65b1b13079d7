package nocsystem.indexmanager.models.DmChatLuong;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="dmchatluongkho")

public class DmChatLuongKho {

    @Id
    @Column(name="ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name="tl_don_lay_nhan_dung_gio")
    private Float tlDonLayNhanDungGio;

    @Column(name="tl_don_lay_tc")
    private Float tlDonLayTC;

    @Column(name="tl_giao_dung_gio")
    private Float tlGiaoDungGio;

    @Column(name="tl_giao_tc")
    private Float tlGiaoTC;

    @Column(name="tl_ton_pc_nhan")
    private Float tlTonPCNhan;

    @Column(name="tl_ton_nhan")
    private Float tlTonNhan;

    @Column(name="tl_ton_tt_bc_nhan")
    private Float tlTonTTBcNhan;

    @Column(name="tl_ton_giao")
    private Float tlTonGiao;

    @Column(name="tg_khai_thac_tb_ttkt")
    private Float tgKhaiThacTBTTKT;

    @Column(name="tl_khai_thac_kpi_3h_ttkt")
    private Float tlKhaiThacKPI3hTTKT;

    @Column(name="ma_tinh")
    private String maTinh;

    @Column(name="ten_tinh")
    private String tenTinh;

    @Column(name="ma_buu_cuc")
    private String maBuuCuc;

    @Column(name="tb_chat_luong")
    private Float tbChatLuong;

}
