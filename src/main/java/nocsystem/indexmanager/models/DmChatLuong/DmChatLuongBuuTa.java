package nocsystem.indexmanager.models.DmChatLuong;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="dmchatluongbuuta")
public class DmChatLuongBuuTa {

    @Id
    @Column(name="ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name="ma_nv")
    private String maNhanVien;

    @Column(name="ma_buu_cuc")
    private String maBuuCuc;

    @Column(name="ma_chi_nhanh")
    private String maChiNhanh;

    @Column(name="tl_nhan_tc")
    private Float tlNhanTC;

    @Column(name="tl_nhan_dg")
    private Float tlNhanDG;

    @Column(name="tl_ton_lay")
    private Float tlTonLay;

    @Column(name="tl_giao_tc")
    private Float tlGiaoTC;

    @Column(name="tl_giao_tc_dg")
    private Float tlGiaoTDG;

    @Column(name="tb_chat_luong")
    private Float tbChatLuong;

}
