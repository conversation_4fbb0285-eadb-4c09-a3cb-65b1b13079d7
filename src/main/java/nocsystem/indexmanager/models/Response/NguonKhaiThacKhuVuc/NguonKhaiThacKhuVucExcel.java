package nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;

public class NguonKhaiThacKhuVucExcel {
    private String ttktCha;
    private String ttktQuetNhan;
    private Long slNho;
    private Double tlNho;
    private Double nguonSlNho;
    private Double nguonTlNho;
    private Double tongNguonNho;
    private Long slLon;
    private Double tlLon;
    private Double nguonSlLon;
    private Double nguonTlLon;
    private Double tongNguonLon;
    private Long tongSl;
    private Double tongTl;
    private Double tongTlTinhNguon;
    private Long tongNguonSl;
    private Double tongNguonTl;
    private Double tongNguonTruocKpi;
    private Double tongNguonSauKpi;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public NguonKhaiThacKhuVucExcel() {
    }

    public NguonKhaiThacKhuVucExcel(String ttktCha, String ttktQuet<PERSON>han, <PERSON> slNho, Double tlNho, Double nguonSlNho, Double nguonTlNho, Double tongNguonNho, Long slLon, Double tlLon, Double nguonSlLon, Double nguonTlLon, Double tongNguonLon, Long tongSl, Double tongTl, Double tongTlTinhNguon, Long tongNguonSl, Double tongNguonTl, Double tongNguonTruocKpi, Double tongNguonSauKpi) throws NoSuchFieldException, IllegalAccessException {
        this.ttktCha = ttktCha;
        this.ttktQuetNhan = ttktQuetNhan;
        this.slNho = slNho;
        this.tlNho = tlNho;
        this.nguonSlNho = nguonSlNho;
        this.nguonTlNho = nguonTlNho;
        this.tongNguonNho = tongNguonNho;
        this.slLon = slLon;
        this.tlLon = tlLon;
        this.nguonSlLon = nguonSlLon;
        this.nguonTlLon = nguonTlLon;
        this.tongNguonLon = tongNguonLon;
        this.tongSl = tongSl;
        this.tongTl = tongTl;
        this.tongTlTinhNguon = tongTlTinhNguon;
        this.tongNguonSl = tongNguonSl;
        this.tongNguonTl = tongNguonTl;
        this.tongNguonTruocKpi = tongNguonTruocKpi;
        this.tongNguonSauKpi = tongNguonSauKpi;
        this.objectHashMap = convert(this);
    }

    public String getTtktCha() {
        return ttktCha;
    }

    public void setTtktCha(String ttktCha) {
        this.ttktCha = ttktCha;
    }

    public String getTtktQuetNhan() {
        return ttktQuetNhan;
    }

    public void setTtktQuetNhan(String ttktQuetNhan) {
        this.ttktQuetNhan = ttktQuetNhan;
    }

    public Long getSlNho() {
        return slNho;
    }

    public void setSlNho(Long slNho) {
        this.slNho = slNho;
    }

    public Double getTlNho() {
        return tlNho;
    }

    public void setTlNho(Double tlNho) {
        this.tlNho = tlNho;
    }

    public Double getNguonSlNho() {
        return nguonSlNho;
    }

    public void setNguonSlNho(Double nguonSlNho) {
        this.nguonSlNho = nguonSlNho;
    }

    public Double getNguonTlNho() {
        return nguonTlNho;
    }

    public void setNguonTlNho(Double nguonTlNho) {
        this.nguonTlNho = nguonTlNho;
    }

    public Double getTongNguonNho() {
        return tongNguonNho;
    }

    public void setTongNguonNho(Double tongNguonNho) {
        this.tongNguonNho = tongNguonNho;
    }

    public Long getSlLon() {
        return slLon;
    }

    public void setSlLon(Long slLon) {
        this.slLon = slLon;
    }

    public Double getTlLon() {
        return tlLon;
    }

    public void setTlLon(Double tlLon) {
        this.tlLon = tlLon;
    }

    public Double getNguonSlLon() {
        return nguonSlLon;
    }

    public void setNguonSlLon(Double nguonSlLon) {
        this.nguonSlLon = nguonSlLon;
    }

    public Double getNguonTlLon() {
        return nguonTlLon;
    }

    public void setNguonTlLon(Double nguonTlLon) {
        this.nguonTlLon = nguonTlLon;
    }

    public Double getTongNguonLon() {
        return tongNguonLon;
    }

    public void setTongNguonLon(Double tongNguonLon) {
        this.tongNguonLon = tongNguonLon;
    }

    public Long getTongSl() {
        return tongSl;
    }

    public void setTongSl(Long tongSl) {
        this.tongSl = tongSl;
    }

    public Double getTongTl() {
        return tongTl;
    }

    public void setTongTl(Double tongTl) {
        this.tongTl = tongTl;
    }

    public Double getTongTlTinhNguon() {
        return tongTlTinhNguon;
    }

    public void setTongTlTinhNguon(Double tongTlTinhNguon) {
        this.tongTlTinhNguon = tongTlTinhNguon;
    }

    public Long getTongNguonSl() {
        return tongNguonSl;
    }

    public void setTongNguonSl(Long tongNguonSl) {
        this.tongNguonSl = tongNguonSl;
    }

    public Double getTongNguonTl() {
        return tongNguonTl;
    }

    public void setTongNguonTl(Double tongNguonTl) {
        this.tongNguonTl = tongNguonTl;
    }

    public Double getTongNguonTruocKpi() {
        return tongNguonTruocKpi;
    }

    public void setTongNguonTruocKpi(Double tongNguonTruocKpi) {
        this.tongNguonTruocKpi = tongNguonTruocKpi;
    }

    public Double getTongNguonSauKpi() {
        return tongNguonSauKpi;
    }

    public void setTongNguonSauKpi(Double tongNguonSauKpi) {
        this.tongNguonSauKpi = tongNguonSauKpi;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }
}
