package nocsystem.indexmanager.models.Response.NguonKhaiThacKhuVuc;

import lombok.Getter;
import lombok.Setter;
import org.apache.htrace.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class NguonKhaiThacKhuVucRequestBody {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate ngayBaoCao;
    private Integer luyKe = 0;
    private String ttktCha = "";
    private String ttktQuetNhan = "";
    private List<String> maDichVu = new ArrayList<>();
    private Integer page = 0;
    private Integer pageSize = 30;
}
