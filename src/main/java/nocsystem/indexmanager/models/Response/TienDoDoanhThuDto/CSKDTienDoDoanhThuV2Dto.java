package nocsystem.indexmanager.models.Response.TienDoDoanhThuDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CSKDTienDoDoanhThuV2Dto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh = "";

    @JsonProperty("ma_buucuc")
    private String maBuuCuc = "";

    @JsonProperty("nhom_dt")
    private String nhomDt = "";

    /* Ch<PERSON>h là kế hoạch của tháng đó */
    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tl_doanhThu")
    private Float tlDoanhThu;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;

    @JsonProperty("thang_truoc")
    private Float thangTruoc = Float.valueOf(0);

    @JsonProperty("nam_truoc")
    private Float namTruoc = Float.valueOf(0);

    @JsonProperty("cung_ky_ngay")
    private Float cungKyNgay = Float.valueOf(0);

    @JsonProperty("cung_ky_thang")
    private Float cungKyThang = Float.valueOf(0);

    @JsonProperty("cung_ky_nam")
    private Float cungKyNam = Float.valueOf(0);

    @JsonProperty("tong_DT_ngay")
    private Float tongDTNgay = Float.valueOf(0);

    @JsonProperty("tl_hoan_thanh_ngay")
    private Float tiLeHoanThanhNgay = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay")
    private Float ttCungKyNgay = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay_percent")
    private Float ttCungKyNgayPercent = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay")
    private Float tTTbnNgay = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay_percent")
    private Float ttTbnNgayPercent = Float.valueOf(0);
}