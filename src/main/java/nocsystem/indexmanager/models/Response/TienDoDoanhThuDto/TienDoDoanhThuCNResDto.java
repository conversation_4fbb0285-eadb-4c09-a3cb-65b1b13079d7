package nocsystem.indexmanager.models.Response.TienDoDoanhThuDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;



@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto

public class TienDoDoanhThuCNResDto {

    private int id;

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ten_chinhanh")
    private String tenChiNhanh;

    @JsonProperty("luy_ke_now")
    private Float luyKeNow;

}
