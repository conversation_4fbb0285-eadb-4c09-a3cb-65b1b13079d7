package nocsystem.indexmanager.models.Response.TienDoDoanhThuDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CSKDTienDoDoanhThuSMSDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh = "";

    @JsonProperty("ma_buucuc")
    private String maBuuCuc = "";

    @JsonProperty("nhom_dt")
    private String nhomDt = "";

    /* Chính là kế hoạch của tháng đó */
    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tl_doanhThu")
    private Float tlDoanhThu;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;

    @JsonProperty("thang_truoc")
    private Float thangTruoc = Float.valueOf(0);

    @JsonProperty("nam_truoc")
    private Float namTruoc = Float.valueOf(0);

    @JsonProperty("cung_ky_ngay")
    private Float cungKyNgay = Float.valueOf(0);

    @JsonProperty("cung_ky_thang")
    private Float cungKyThang = Float.valueOf(0);

    @JsonProperty("cung_ky_nam")
    private Float cungKyNam = Float.valueOf(0);

    @JsonProperty("tong_DT_ngay")
    private Float tongDTNgay = Float.valueOf(0);

    @JsonProperty("tl_hoan_thanh_ngay")
    private Float tiLeHoanThanhNgay = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay")
    private Float ttCungKyNgay = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay_percent")
    private Float ttCungKyNgayPercent = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay")
    private Float tTTbnNgay = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay_percent")
    private Float ttTbnNgayPercent = Float.valueOf(0);

    @JsonProperty("ma_chinhanh_CP")
    private String maChiNhanhCP = "";

    @JsonProperty("ma_buucuc_CP")
    private String maBuuCucCP = "";

    @JsonProperty("nhom_dt_CP")
    private String nhomDtCP = "";

    @JsonProperty("ke_hoach_CP")
    private Float keHoachCP = Float.valueOf(0);

    @JsonProperty("thuc_hien_CP")
    private Float thucHienCP = Float.valueOf(0);

    @JsonProperty("tlht_CP")
    private Float tlHoanThanhCP;

    @JsonProperty("tl_doanhThu_CP")
    private Float tlDoanhThuCP;

    @JsonProperty("tiendo_CP")
    private Float tienDoCP;

    @JsonProperty("tt_thang_CP")
    private Float ttThangCP;

    @JsonProperty("tt_tbn_thang_CP")
    private Float ttTbnThangCP;

    @JsonProperty("tt_nam_CP")
    private Float ttNamCP;

    @JsonProperty("tt_tbn_nam_CP")
    private Float ttTbnNamCP;

    @JsonProperty("thang_truoc_CP")
    private Float thangTruocCP = Float.valueOf(0);

    @JsonProperty("nam_truoc_CP")
    private Float namTruocCP = Float.valueOf(0);

    @JsonProperty("cung_ky_ngay_CP")
    private Float cungKyNgayCP = Float.valueOf(0);

    @JsonProperty("cung_ky_thang_CP")
    private Float cungKyThangCP = Float.valueOf(0);

    @JsonProperty("cung_ky_nam_CP")
    private Float cungKyNamCP = Float.valueOf(0);

    @JsonProperty("tong_DT_ngay_CP")
    private Float tongDTNgayCP = Float.valueOf(0);

    @JsonProperty("tl_hoan_thanh_ngay_CP")
    private Float tiLeHoanThanhNgayCP = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay_CP")
    private Float ttCungKyNgayCP = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay_percent_CP")
    private Float ttCungKyNgayPercentCP = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay_CP")
    private Float tTTbnNgayCP = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay_percent_CP")
    private Float ttTbnNgayPercentCP = Float.valueOf(0);

    @JsonProperty("ma_chinhanh_LOG")
    private String maChiNhanhLOG = "";

    @JsonProperty("ma_buucuc_LOG")
    private String maBuuCucLOG = "";

    @JsonProperty("nhom_dt_LOG")
    private String nhomDtLOG = "";

    @JsonProperty("ke_hoach_LOG")
    private Float keHoachLOG = Float.valueOf(0);

    @JsonProperty("thuc_hien_LOG")
    private Float thucHienLOG = Float.valueOf(0);

    @JsonProperty("tlht_LOG")
    private Float tlHoanThanhLOG;

    @JsonProperty("tl_doanhThu_LOG")
    private Float tlDoanhThuLOG;

    @JsonProperty("tiendo_LOG")
    private Float tienDoLOG;

    @JsonProperty("tt_thang_LOG")
    private Float ttThangLOG;

    @JsonProperty("tt_tbn_thang_LOG")
    private Float ttTbnThangLOG;

    @JsonProperty("tt_nam_LOG")
    private Float ttNamLOG;

    @JsonProperty("tt_tbn_nam_LOG")
    private Float ttTbnNamLOG;

    @JsonProperty("thang_truoc_LOG")
    private Float thangTruocLOG = Float.valueOf(0);

    @JsonProperty("nam_truoc_LOG")
    private Float namTruocLOG = Float.valueOf(0);

    @JsonProperty("cung_ky_ngay_LOG")
    private Float cungKyNgayLOG = Float.valueOf(0);

    @JsonProperty("cung_ky_thang_LOG")
    private Float cungKyThangLOG = Float.valueOf(0);

    @JsonProperty("cung_ky_nam_LOG")
    private Float cungKyNamLOG = Float.valueOf(0);

    @JsonProperty("tong_DT_ngay_LOG")
    private Float tongDTNgayLOG = Float.valueOf(0);

    @JsonProperty("tl_hoan_thanh_ngay_LOG")
    private Float tiLeHoanThanhNgayLOG = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay_LOG")
    private Float ttCungKyNgayLOG = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay_percent_LOG")
    private Float ttCungKyNgayPercentLOG = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay_LOG")
    private Float tTTbnNgayLOG = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay_percent_LOG")
    private Float ttTbnNgayPercentLOG = Float.valueOf(0);
}