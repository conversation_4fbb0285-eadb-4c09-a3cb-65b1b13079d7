package nocsystem.indexmanager.models.Response.TienDoDoanhThuDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TienDoDTConvertTienDoLogisticDto {
    @JsonProperty("loai_dichvu")
    private String dichVu;

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tien_do")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;
}
