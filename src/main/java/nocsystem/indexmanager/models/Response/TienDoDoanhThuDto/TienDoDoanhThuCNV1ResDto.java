package nocsystem.indexmanager.models.Response.TienDoDoanhThuDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
/**
 *     @JpaDto    giải quyết không cần new đối tượng  trong querry
 */
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TienDoDoanhThuCNV1ResDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh = "";

    @JsonProperty("ten_chi_nhanh")
    private String tenChiNhanh = "";

    @JsonProperty("nhom_doanhthu")
    private String nhomDoanhThu = "";

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tiendo")
    private Float tienDo = Float.valueOf(0);

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("luy_ke")
    private Float luyKe = Float.valueOf(0);

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam = Float.valueOf(0);
}
