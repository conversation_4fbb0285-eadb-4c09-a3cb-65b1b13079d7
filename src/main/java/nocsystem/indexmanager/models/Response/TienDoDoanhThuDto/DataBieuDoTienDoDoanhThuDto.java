package nocsystem.indexmanager.models.Response.TienDoDoanhThuDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class DataBieuDoTienDoDoanhThuDto {
    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("nhom_doanthu")
    private String nhomDt;

    @JsonProperty("tl_tt_ngay")
    private Float tiLeTangTruongNgay = Float.valueOf(0);
}
