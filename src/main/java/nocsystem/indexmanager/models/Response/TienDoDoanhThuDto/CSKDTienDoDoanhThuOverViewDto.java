package nocsystem.indexmanager.models.Response.TienDoDoanhThuDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CSKDTienDoDoanhThuOverViewDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("nhom_dt")
    private String nhomDt;

    /* Chính là kế hoạch của tháng đó */
    @JsonProperty("ke_hoach")
    private Float keHoach;

    /* Chính là giá trị lũy kế thực hiện tính từ đầu tháng đến ngày hiện tại của tháng*/
    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tl_doanhThu")
    private Float tlDoanhThu = Float.valueOf(0);

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;

    @JsonProperty("cung_ky_ngay")
    private Float cungKyNgay = Float.valueOf(0);

    @JsonProperty("cung_ky_thang")
    private Float cungKyThang = Float.valueOf(0);

    @JsonProperty("thang_truoc")
    private Float thangTruoc = Float.valueOf(0);

    @JsonProperty("nam_truoc")
    private Float namTruoc = Float.valueOf(0);

    @JsonProperty("cung_ky_nam")
    private Float cungKyNam = Float.valueOf(0);

    /*Doanh thu thực hiện chỉ tính trong ngày đó*/
    @JsonProperty("thngay")
    private Float thucHienNgay = Float.valueOf(0);

    /*Giá trị thực hiện ngày trước đó có thể là ngày hôm trước (N - 1), ngày (N - 1) của tháng trước đó (T - 1)*/
    @JsonProperty("thngay_mm1")
    private Float thucHienNgayTruocDo = Float.valueOf(0);

    @JsonProperty("ngay_quydoi_thang")
    private Float ngayQuyDoiThang = Float.valueOf(0);

    @JsonProperty("tl_tt_ngay")
    private Float tiLeTangTruongNgay = Float.valueOf(0);

    @JsonProperty("hs_ngay")
    private Float hsNgay = Float.valueOf(0);
}
