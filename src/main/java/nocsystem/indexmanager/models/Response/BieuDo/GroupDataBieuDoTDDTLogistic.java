package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class GroupDataBieuDoTDDTLogistic {
    @JsonProperty("date")
    private String date;

    @JsonProperty("FORWARDING")
    private Float FORWARDING = Float.valueOf(0);

    @JsonProperty("KHO_VAN")
    private Float KHOVAN = Float.valueOf(0);
}
