package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BieuDoTtTienDoDoanhThuJPADto {
    @JsonProperty("tl_tt_ngay")
    private Float tiLeTangTruongNgay = Float.valueOf(0);

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("loai_dichvu")
    private String loaiDichVu;
}
