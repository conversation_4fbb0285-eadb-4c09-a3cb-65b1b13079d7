package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class GroupDataBieuDoDTCP {
    @JsonProperty("date")
    private String date;

    @JsonProperty("NoN_COD")
    private Float dtNoNCOD = Float.valueOf(0);

    @JsonProperty("COD")
    private Float dtCOD = Float.valueOf(0);

    @JsonProperty("EXP")
    private Float exp = Float.valueOf(0);
}
