package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BieuDoTtTienDoDoanhThuCPResponse {
    @JsonProperty("date")
    private List<String> date;

    @JsonProperty("NoN_COD")
    private List<Float> DTNoNCOD;

    @JsonProperty("COD")
    private List<Float> DTCOD;

    @JsonProperty("EXP")
    private List<Float> EXP;
}
