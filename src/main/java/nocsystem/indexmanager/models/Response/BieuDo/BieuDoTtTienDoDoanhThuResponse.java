package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BieuDoTtTienDoDoanhThuResponse {
    @JsonProperty("date")
    private List<String> Date;

    @JsonProperty("dt_chuyenphat")
    private List<Float> dtChuyenPhat;

    @JsonProperty("dt_logistics")
    private List<Float> dtLogistics;
}
