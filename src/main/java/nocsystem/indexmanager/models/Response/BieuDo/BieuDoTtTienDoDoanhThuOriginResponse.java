package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BieuDoTtTienDoDoanhThuOriginResponse {
    @JsonProperty("date")
    private String date;

    @JsonProperty("dt_chuyenphat")
    private Float dtChuyenPhat = Float.valueOf(0);

    @JsonProperty("dt_logistics")
    private Float dtLogistics = Float.valueOf(0);
}
