package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BieuDoTtTienDoDTLogisticRepo {
    @JsonProperty("date")
    private List<String> date;

    @JsonProperty("forwarding")
    private List<Float> forwarding;

    @JsonProperty("kho_van")
    private List<Float> khoVan;
}
