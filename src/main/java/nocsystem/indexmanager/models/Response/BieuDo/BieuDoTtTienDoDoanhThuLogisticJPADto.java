package nocsystem.indexmanager.models.Response.BieuDo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BieuDoTtTienDoDoanhThuLogisticJPADto {
    @JsonProperty("tl_tt_ngay")
    private Float tiLeTangTruongNgay = Float.valueOf(0);

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("loai_dichvu")
    private String loaiDichVu;
}
