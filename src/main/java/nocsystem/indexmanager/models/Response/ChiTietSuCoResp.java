package nocsystem.indexmanager.models.Response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChiTietSuCoResp {
    private Integer capSuCo;
    private Double xuHuongChuyenCap;
    private Integer chiSoTonKhaiThac;
    private Integer chiSoLacTuyen;
    private Integer chiSoTonXuat;
    private Integer chiSoTonPhat;
    private Integer chiSoBienDongNhanSu;
    private Integer chiSoTonPhatDo789;
    private Double tongThoiGianSuCo;
    private Double nguyCoChuyenCap;

    private Double tgTonKhaiThac;
    private Double tgLacTuyen;
    private Double tgTonXuat;
    private Double tgTonPhat;
    private Double tgTonDo789;

    private Integer slDonTonKhaiThac;
    private Integer slDonTonPhat;
    private Integer slDonTonXuat;
    private Integer slNhanSuThieu;
    private Integer capDoDuBao;
    private List<String> danhSachNguyenNhan;
}
