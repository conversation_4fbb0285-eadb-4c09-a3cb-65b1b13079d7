package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import java.time.LocalDate;

public class TonPhatKhauMMMainDashResDTO {

    private LocalDate ngayBaoCao;

    private String donVi;

    private Long tongSL;

    private Long tongDif;

    private Float tiLe;

    private Float tiLeDif;

    public TonPhatKhauMMMainDashResDTO(String donVi, Long tongSL) {
        this.donVi = donVi;
        this.tongSL = tongSL;
    }

    public TonPhatKhauMMMainDashResDTO() {
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public LocalDate getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(LocalDate ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public Long getTongSL() {
        return tongSL;
    }

    public void setTongSL(Long tongSL) {
        this.tongSL = tongSL;
    }

    public Long getTongDif() {
        return tongDif;
    }

    public void setTongDif(Long tongDif) {
        this.tongDif = tongDif;
    }

    public Float getTiLe() {
        return tiLe;
    }

    public void setTiLe(Float tiLe) {
        this.tiLe = tiLe;
    }

    public Float getTiLeDif() {
        return tiLeDif;
    }

    public void setTiLeDif(Float tiLeDif) {
        this.tiLeDif = tiLeDif;
    }
}
