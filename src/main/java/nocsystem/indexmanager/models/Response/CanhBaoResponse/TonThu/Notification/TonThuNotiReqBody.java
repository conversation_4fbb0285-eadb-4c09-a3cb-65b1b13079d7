package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonThuNotiReqBody {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("buu_cuc")
    private String buuCuc;

    @JsonProperty("loai_canh_bao")
    private List<String> loaiCanhBao;
}
