package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
public class SMSNotiResponseDTO{

//    @Override
//    public int compareTo(SMSNotiResponseDTO t) {
//        return this.getTong().compareTo(t.getTong());
//    }

    private String donVi;

    private Long tongTonThu;

    private Float tlTonThu;

    private List<String> listTonThu;

    private Long tongTonPhat;

    private Float tlTonPhat;

    private List<String> listTonPhat;

    private Long tongTTKT;

    private Float tlQuaChiTieu;

    private Float tlConChiTieu;

    private Long quaChiTieu;

    private Long conChiTieu;

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getTongTonThu() {
        return tongTonThu;
    }

    public void setTongTonThu(Long tongTonThu) {
        this.tongTonThu = tongTonThu;
    }

    public Float getTlTonThu() {
        return tlTonThu;
    }

    public void setTlTonThu(Float tlTonThu) {
        this.tlTonThu = tlTonThu;
    }

    public List<String> getListTonThu() {
        return listTonThu;
    }

    public void setListTonThu(List<String> listTonThu) {
        this.listTonThu = listTonThu;
    }

    public Long getTongTonPhat() {
        return tongTonPhat;
    }

    public void setTongTonPhat(Long tongTonPhat) {
        this.tongTonPhat = tongTonPhat;
    }

    public Float getTlTonPhat() {
        return tlTonPhat;
    }

    public void setTlTonPhat(Float tlTonPhat) {
        this.tlTonPhat = tlTonPhat;
    }

    public List<String> getListTonPhat() {
        return listTonPhat;
    }

    public void setListTonPhat(List<String> listTonPhat) {
        this.listTonPhat = listTonPhat;
    }

    public Long getTongTTKT() {
        return tongTTKT;
    }

    public void setTongTTKT(Long tongTTKT) {
        this.tongTTKT = tongTTKT;
    }

    public Float getTlQuaChiTieu() {
        return tlQuaChiTieu;
    }

    public void setTlQuaChiTieu(Float tlQuaChiTieu) {
        this.tlQuaChiTieu = tlQuaChiTieu;
    }

    public Float getTlConChiTieu() {
        return tlConChiTieu;
    }

    public void setTlConChiTieu(Float tlConChiTieu) {
        this.tlConChiTieu = tlConChiTieu;
    }

    public Long getQuaChiTieu() {
        return quaChiTieu;
    }

    public void setQuaChiTieu(Long quaChiTieu) {
        this.quaChiTieu = quaChiTieu;
    }

    public Long getConChiTieu() {
        return conChiTieu;
    }

    public void setConChiTieu(Long conChiTieu) {
        this.conChiTieu = conChiTieu;
    }
}
