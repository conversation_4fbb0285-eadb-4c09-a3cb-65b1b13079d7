package nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonGiaoNhanLXLOGResponseDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("buu_cuc")
    private String buuCuc;

    @JsonProperty("loai_vung")
    private String loaiVung;

    @JsonProperty("nhom_dv")
    private String nhomDV;

    @JsonProperty("ma_phieugui")
    private String maPhieuGui;

    @JsonProperty("ngay_tao_don")
    private String ngayTaoDon;

    @JsonProperty("moc_ton")
    private String mocTon;

    @JsonProperty("qua_0h")
    private Long qua0h;

    @JsonProperty("qua_2h")
    private Long qua2h;

    @JsonProperty("qua_6h")
    private Long qua6h;

    @JsonProperty("qua_12h")
    private Long qua12h;

    @JsonProperty("qua_24h")
    private Long qua24h;

    @JsonProperty("qua_48h")
    private Long qua48h;

    @JsonProperty("qua_72h")
    private Long qua72h;

    @JsonProperty("qua_96h")
    private Long qua96h;

    @JsonProperty("qua_120h")
    private Long qua120h;

    @JsonProperty("tong")
    private Long tong;
}
