package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

public class TonPhatNguongMainDashResDTO {


    private Long sln7TongSL;

    private Long sln7Dif;

    private Long sln8TongSL;

    private Long sln8Dif;

    private Long sln9TongSL;

    private Long sln9Dif;

    private Long tongSLN789;

    private Long tongSLN789Dif;

    private Float tiLeN789;

    private Float tiLeN789Dif;

    private Long tongSL;

    public TonPhatNguongMainDashResDTO(Long sln7TongSL, Long sln8TongSL, Long sln9TongSL, Long tongSLN789, Long tongSL) {
        this.sln7TongSL = sln7TongSL;
        this.sln8TongSL = sln8TongSL;
        this.sln9TongSL = sln9TongSL;
        this.tongSLN789 = tongSLN789;
        this.tongSL = tongSL;
    }

    public TonPhatNguongMainDashResDTO() {
    }

    public Long getTongSL() {
        return tongSL;
    }

    public void setTongSL(Long tongSL) {
        this.tongSL = tongSL;
    }

    public Long getSln7TongSL() {
        return sln7TongSL;
    }

    public void setSln7TongSL(Long sln7TongSL) {
        this.sln7TongSL = sln7TongSL;
    }

    public Long getSln7Dif() {
        return sln7Dif;
    }

    public void setSln7Dif(Long sln7Dif) {
        this.sln7Dif = sln7Dif;
    }

    public Long getSln8TongSL() {
        return sln8TongSL;
    }

    public void setSln8TongSL(Long sln8TongSL) {
        this.sln8TongSL = sln8TongSL;
    }

    public Long getSln8Dif() {
        return sln8Dif;
    }

    public void setSln8Dif(Long sln8Dif) {
        this.sln8Dif = sln8Dif;
    }

    public Long getSln9TongSL() {
        return sln9TongSL;
    }

    public void setSln9TongSL(Long sln9TongSL) {
        this.sln9TongSL = sln9TongSL;
    }

    public Long getSln9Dif() {
        return sln9Dif;
    }

    public void setSln9Dif(Long sln9Dif) {
        this.sln9Dif = sln9Dif;
    }

    public Long getTongSLN789() {
        return tongSLN789;
    }

    public void setTongSLN789(Long tongSLN789) {
        this.tongSLN789 = tongSLN789;
    }

    public Long getTongSLN789Dif() {
        return tongSLN789Dif;
    }

    public void setTongSLN789Dif(Long tongSLN789Dif) {
        this.tongSLN789Dif = tongSLN789Dif;
    }

    public Float getTiLeN789() {
        return tiLeN789;
    }

    public void setTiLeN789(Float tiLeN789) {
        this.tiLeN789 = tiLeN789;
    }

    public Float getTiLeN789Dif() {
        return tiLeN789Dif;
    }

    public void setTiLeN789Dif(Float tiLeN789Dif) {
        this.tiLeN789Dif = tiLeN789Dif;
    }
}
