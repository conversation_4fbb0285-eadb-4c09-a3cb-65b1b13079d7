package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

public class TonPhatNguongTop15N789DashResDTO implements Comparable<TonPhatNguongTop15N789DashResDTO>{

    @Override
    public int compareTo(TonPhatNguongTop15N789DashResDTO t){
        return this.getsL789().compareTo(t.getsL789());
    }

    private String donVi;

    private Long sL7;

    private Long sL8;

    private Long sL9;

    private Long sL789;

    public Long getTongSL() {
        return tongSL;
    }

    public void setTongSL(Long tongSL) {
        this.tongSL = tongSL;
    }

    private Long tongSL;

    private Float tL789;

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getsL7() {
        return sL7;
    }

    public void setsL7(Long sL7) {
        this.sL7 = sL7;
    }

    public Long getsL8() {
        return sL8;
    }

    public void setsL8(Long sL8) {
        this.sL8 = sL8;
    }

    public Long getsL9() {
        return sL9;
    }

    public void setsL9(Long sL9) {
        this.sL9 = sL9;
    }

    public Long getsL789() {
        return sL789;
    }

    public void setsL789(Long sL789) {
        this.sL789 = sL789;
    }

    public Float gettL789() {
        return tL789;
    }

    public void settL789(Float tL789) {
        this.tL789 = tL789;
    }

    public TonPhatNguongTop15N789DashResDTO(String donVi, Long sL7, Long sL8, Long sL9, Long sL789, Long tongSL) {
        this.donVi = donVi;
        this.sL7 = sL7;
        this.sL8 = sL8;
        this.sL9 = sL9;
        this.sL789 = sL789;
        this.tongSL = tongSL;
    }
}
