package nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonThuBillDashboarResponseDTO {

    @JsonProperty("ma_phieugui")
    private String maPhieuGui;

    @JsonProperty("trang_thai")
    private String trangThai;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("vung_con")
    private String vungCon;

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("buuta_nhan")
    private String buuTaNhan;

    @JsonProperty("loai_canh_bao")
    private String loaiCanhBao;

    @JsonProperty("time_tao_don")
    private String timeTaoDon;
}
