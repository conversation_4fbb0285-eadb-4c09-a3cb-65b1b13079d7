package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatNguongHNIHCMResDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("sln9_cham")
    private List<Long> sLN9Cham;

    @JsonProperty("sln9_nhanh")
    private List<Long> sLN9Nhanh;

    @JsonProperty("sln9_hoatoc")
    private List<Long> sLN9HoaToc;

    @JsonProperty("tile_9")
    private List<Float> tiLe9;

}
