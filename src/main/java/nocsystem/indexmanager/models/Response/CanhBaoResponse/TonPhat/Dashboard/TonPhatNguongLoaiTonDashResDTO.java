package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatNguongLoaiTonDashResDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("sln789")
    private List<Float> sLN789;

    @JsonProperty("sl_ton_phat")
    private List<Float> sLTP;

    @JsonProperty("tl_789")
    private List<Float> tL789;

}
