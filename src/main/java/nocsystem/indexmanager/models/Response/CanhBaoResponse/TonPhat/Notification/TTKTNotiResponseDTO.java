package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
public class TTKTNotiResponseDTO implements Comparable<TTKTNotiResponseDTO>{

    @Override
    public int compareTo(TTKTNotiResponseDTO t) {
        return this.getTong().compareTo(t.getTong());
    }

    private String donVi;

    private Long quaChiTieu;

    private Long conChiTieu;

    private Long tong;

    private Long qua0h;

    private Long qua2h;

    private Long qua6h;

    private Long qua12h;

    private Long qua24h;

    private Long qua48h;

    private Long qua72h;

    private Long qua96h;

    private Long qua120h;

    private Float tlQuaChiTieu;

    private Float tlConChiTieu;

    private List<String> tenTop10;

    private List<Long> slTop10;

    public TTKTNotiResponseDTO(String donVi, Long tong, Long qua0h, Long qua2h, <PERSON> qua6h, <PERSON> qua12h, Long qua24h, <PERSON> qua48h, Long qua72h, Long qua96h, <PERSON> qua120h) {
        this.donVi = donVi;
        this.tong = tong;
        this.qua0h = qua0h;
        this.qua2h = qua2h;
        this.qua6h = qua6h;
        this.qua12h = qua12h;
        this.qua24h = qua24h;
        this.qua48h = qua48h;
        this.qua72h = qua72h;
        this.qua96h = qua96h;
        this.qua120h = qua120h;
    }

    public TTKTNotiResponseDTO(Long quaChiTieu, Long conChiTieu, Long tong, Float tlQuaChiTieu, Float tlConChiTieu) {
        this.quaChiTieu = quaChiTieu;
        this.conChiTieu = conChiTieu;
        this.tong = tong;
        this.tlQuaChiTieu = tlQuaChiTieu;
        this.tlConChiTieu = tlConChiTieu;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getQuaChiTieu() {
        return quaChiTieu;
    }

    public void setQuaChiTieu(Long quaChiTieu) {
        this.quaChiTieu = quaChiTieu;
    }

    public Long getConChiTieu() {
        return conChiTieu;
    }

    public void setConChiTieu(Long conChiTieu) {
        this.conChiTieu = conChiTieu;
    }

    public Long getTong() {
        return tong;
    }

    public void setTong(Long tong) {
        this.tong = tong;
    }

    public Float getTlQuaChiTieu() {
        return tlQuaChiTieu;
    }

    public void setTlQuaChiTieu(Float tlQuaChiTieu) {
        this.tlQuaChiTieu = tlQuaChiTieu;
    }

    public Float getTlConChiTieu() {
        return tlConChiTieu;
    }

    public void setTlConChiTieu(Float tlConChiTieu) {
        this.tlConChiTieu = tlConChiTieu;
    }

    public List<String> getTenTop10() {
        return tenTop10;
    }

    public void setTenTop10(List<String> tenTop10) {
        this.tenTop10 = tenTop10;
    }

    public List<Long> getSlTop10() {
        return slTop10;
    }

    public void setSlTop10(List<Long> slTop10) {
        this.slTop10 = slTop10;
    }

    public Long getQua0h() {
        return qua0h;
    }

    public void setQua0h(Long qua0h) {
        this.qua0h = qua0h;
    }

    public Long getQua2h() {
        return qua2h;
    }

    public void setQua2h(Long qua2h) {
        this.qua2h = qua2h;
    }

    public Long getQua6h() {
        return qua6h;
    }

    public void setQua6h(Long qua6h) {
        this.qua6h = qua6h;
    }

    public Long getQua12h() {
        return qua12h;
    }

    public void setQua12h(Long qua12h) {
        this.qua12h = qua12h;
    }

    public Long getQua24h() {
        return qua24h;
    }

    public void setQua24h(Long qua24h) {
        this.qua24h = qua24h;
    }

    public Long getQua48h() {
        return qua48h;
    }

    public void setQua48h(Long qua48h) {
        this.qua48h = qua48h;
    }

    public Long getQua72h() {
        return qua72h;
    }

    public void setQua72h(Long qua72h) {
        this.qua72h = qua72h;
    }

    public Long getQua96h() {
        return qua96h;
    }

    public void setQua96h(Long qua96h) {
        this.qua96h = qua96h;
    }

    public Long getQua120h() {
        return qua120h;
    }

    public void setQua120h(Long qua120h) {
        this.qua120h = qua120h;
    }
}
