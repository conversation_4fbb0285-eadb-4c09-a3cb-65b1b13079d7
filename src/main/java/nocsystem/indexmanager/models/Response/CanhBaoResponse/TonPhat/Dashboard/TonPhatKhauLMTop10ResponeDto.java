package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDate;

@Setter
@Getter
@NoArgsConstructor
public class TonPhatKhauLMTop10ResponeDto {

     String tinhPhat;
     String loaiTon;
     String loaiCanhBao;
     String maBuuCuc;
     String buuTaPhat;
     Long tongSL;

    public TonPhatKhauLMTop10ResponeDto( String tinhPhat, String loaiTon, String loaiCanhBao, String maBuuCuc, String buuTaPhat, Long tongSL) {
        this.tinhPhat = tinhPhat;
        this.loaiTon = loaiTon;
        this.loaiCanhBao = loaiCanhBao;
        this.maBuuCuc = maBuuCuc;
        this.buuTaPhat = buuTaPhat;
        this.tongSL = tongSL;
    }
}
