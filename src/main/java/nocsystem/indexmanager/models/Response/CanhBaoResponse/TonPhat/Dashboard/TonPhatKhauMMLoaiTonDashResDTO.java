package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import java.util.List;

public class TonPhatKhauMMLoaiTonDashResDTO {

    private String loaiTon;

    private  Long tongSL;

    private List<Float> listTongSL;

    private List<Float> tongSLLoi;

    private List<Float> tiLe;

    public TonPhatKhauMMLoaiTonDashResDTO(List<Float> listTongSL, List<Float> tongSLLoi, List<Float> tiLe) {
        this.listTongSL = listTongSL;
        this.tongSLLoi = tongSLLoi;
        this.tiLe = tiLe;
    }

    public List<Float> getListTongSL() {
        return listTongSL;
    }

    public void setListTongSL(List<Float> listTongSL) {
        this.listTongSL = listTongSL;
    }

    public TonPhatKhauMMLoaiTonDashResDTO(){

    }

    public TonPhatKhauMMLoaiTonDashResDTO(String loaiTon, Long tongSL) {
        this.loaiTon = loaiTon;
        this.tongSL = tongSL;
    }

    public String getLoaiTon() {
        return loaiTon;
    }

    public void setLoaiTon(String loaiTon) {
        this.loaiTon = loaiTon;
    }

    public Long getTongSL() {
        return tongSL;
    }

    public void setTongSL(Long tongSL) {
        this.tongSL = tongSL;
    }

    public List<Float> getTongSLLoi() {
        return tongSLLoi;
    }

    public void setTongSLLoi(List<Float> tongSLLoi) {
        this.tongSLLoi = tongSLLoi;
    }

    public List<Float> getTiLe() {
        return tiLe;
    }

    public void setTiLe(List<Float> tiLe) {
        this.tiLe = tiLe;
    }
}
