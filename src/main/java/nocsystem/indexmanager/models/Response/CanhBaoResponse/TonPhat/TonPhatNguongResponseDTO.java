package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatNguongResponseDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("tinh_phat")
    private String tinhPhat;

    @JsonProperty("loai_ton")
    private String loaiTon;

    @JsonProperty("loai_don")
    private String loaiDon;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("buu_ta_phat")
    private String buuTaPhat;

    @JsonProperty("sln7_nhanh")
    private Long sln7Nhanh;

    @JsonProperty("sln7_cham")
    private Long sln7Cham;

    @JsonProperty("sln7_hoatoc")
    private Long sln7HoaToc;

    @JsonProperty("sln7_tongsl")
    private Long sln7TongSL;

    @JsonProperty("sln8_nhanh")
    private Long sln8Nhanh;

    @JsonProperty("sln8_cham")
    private Long sln8Cham;

    @JsonProperty("sln8_hoatoc")
    private Long sln8HoaToc;

    @JsonProperty("sln8_tongsl")
    private Long sln8TongSL;

    @JsonProperty("sln9_nhanh")
    private Long sln9Nhanh;

    @JsonProperty("sln9_cham")
    private Long sln9Cham;

    @JsonProperty("sln9_hoatoc")
    private Long sln9HoaToc;

    @JsonProperty("sln9_tongsl")
    private Long sln9TongSL;

    @JsonProperty("tongsl_n789")
    private Long tongSLN789;

    @JsonProperty("tile_n789")
    private Float tiLeN789;

    @JsonProperty("tongsl")
    private Long tongSL;
}
