package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;


public class TonThuTop10CGTResDTO implements Comparable<TonThuTop10CGTResDTO> {

    @Override
    public int compareTo(TonThuTop10CGTResDTO t) {
        return this.getChuaGanTuyen().compareTo(t.getChuaGanTuyen());
    }

    private String donVi;

    private Long chuaGanTuyen;

    public TonThuTop10CGTResDTO(String donVi, Long chuaGanTuyen) {
        this.donVi = donVi;
        this.chuaGanTuyen = chuaGanTuyen;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getChuaGanTuyen() {
        return chuaGanTuyen;
    }

    public void setChuaGanTuyen(Long chuaGanTuyen) {
        this.chuaGanTuyen = chuaGanTuyen;
    }
}
