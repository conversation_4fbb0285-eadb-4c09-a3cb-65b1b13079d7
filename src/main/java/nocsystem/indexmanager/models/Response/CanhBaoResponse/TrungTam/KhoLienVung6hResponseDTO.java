package nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class KhoLienVung6hResponseDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("buu_cuc")
    private String buuCuc;

    @JsonProperty("ma_phieugui")
    private String maPhieuGui;

    @JsonProperty("ngay_tao_don")
    private String ngayTaoDon;

    @JsonProperty("moc_ton")
    private String mocTon;

    @JsonProperty("qua_6h")
    private Float qua6h;

    @JsonProperty("qua_12h")
    private Float qua12h;

    @JsonProperty("qua_24h")
    private Float qua24h;

    @JsonProperty("qua_48h")
    private Float qua48h;

    @JsonProperty("qua_72h")
    private Float qua72h;

    @JsonProperty("qua_96h")
    private Float qua96h;

    @JsonProperty("qua_120h")
    private Float qua120h;

    @JsonProperty("tong")
    private Float tong;
}
