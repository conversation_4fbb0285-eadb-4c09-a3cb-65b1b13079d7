package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonThuTheoKHDTO {

    @JsonProperty("ngay_baocao")
    private String ngayBaoCao;

    @JsonProperty("tinh_nhan")
    private List<String> tinhNhan;

    @JsonProperty("chi_nhanh_nhan")
    private List<String> chiNhanhNhan;

    @JsonProperty("vungCon")
    private String vungCon;

    @JsonProperty("buu_cuc_nhan")
    private List<String> buuCucNhan;

    @JsonProperty("buu_ta_nhan")
    private List<String> buuTa<PERSON>han;

    @JsonProperty("loai_canh_bao")
    private List<String> loaiCanhBao;

    @JsonProperty("trang_thai")
    private String trangThai;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("page_size")
    private Integer pageSize;

    @JsonProperty("sort")
    private String sort;

    @JsonProperty("ma_doitac")
    private String maDoiTac;

    @JsonProperty("loai_hh")
    private String loaiHH;

    @JsonProperty("khDacThuGui")
    private String khDacThuGui;

    @JsonProperty("khDacThuNhan")
    private String khDacThuNhan;

    @JsonProperty("maKhGui")
    private String maKhGui;

}
