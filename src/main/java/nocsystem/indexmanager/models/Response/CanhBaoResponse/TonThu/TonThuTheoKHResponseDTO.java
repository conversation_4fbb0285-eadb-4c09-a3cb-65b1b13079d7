package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Notification.TTKTNotiResponseDTO;
import org.jetbrains.annotations.NotNull;

import javax.persistence.Column;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
public class TonThuTheoKHResponseDTO implements Comparable<TonThuTheoKHResponseDTO> {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh_nhan")
    private String chiNhanhNhan;

    @JsonProperty("buu_cuc_nhan")
    private String buuCucNhan;

    @JsonProperty("buu_ta_nhan")
    private String buuTaNhan;

    @JsonProperty("loai_canh_bao")
    private String loaiCanhBao;

    @JsonProperty("kh_0")
    private Long khachHang0;

    @JsonProperty("kh_1")
    private Long khachHang1;

    @JsonProperty("kh_2")
    private Long khachHang2;

    @JsonProperty("kh_3")
    private Long khachHang3;

    @JsonProperty("kh_4")
    private Long khachHang4;

    @JsonProperty("kh_5")
    private Long khachHang5;

    @JsonProperty("kh_6")
    private Long khachHang6;

    @JsonProperty("kh_7")
    private Long khachHang7;

    @JsonProperty("kh_8")
    private Long khachHang8;

    @JsonProperty("tong")
    private Long tong;

    public TonThuTheoKHResponseDTO(String chiNhanhNhan, Long tong) {
        this.chiNhanhNhan = chiNhanhNhan;
        this.tong = tong;
    }

    public TonThuTheoKHResponseDTO(LocalDate ngayBaoCao, String chiNhanhNhan, String buuCucNhan, String buuTaNhan, String loaiCanhBao, Long khachHang0, Long khachHang1, Long khachHang2, Long khachHang3, Long khachHang4, Long khachHang5, Long khachHang6, Long khachHang7, Long khachHang8, Long tong) {
        this.ngayBaoCao = ngayBaoCao;
        this.chiNhanhNhan = chiNhanhNhan;
        this.buuCucNhan = buuCucNhan;
        this.buuTaNhan = buuTaNhan;
        this.loaiCanhBao = loaiCanhBao;
        this.khachHang0 = khachHang0;
        this.khachHang1 = khachHang1;
        this.khachHang2 = khachHang2;
        this.khachHang3 = khachHang3;
        this.khachHang4 = khachHang4;
        this.khachHang5 = khachHang5;
        this.khachHang6 = khachHang6;
        this.khachHang7 = khachHang7;
        this.khachHang8 = khachHang8;
        this.tong = tong;
    }

    public TonThuTheoKHResponseDTO(Long tong) {
        this.tong = tong;
    }



    @Override
    public int compareTo(TonThuTheoKHResponseDTO t) {
        return this.getTong().compareTo(t.getTong());
    }
}
