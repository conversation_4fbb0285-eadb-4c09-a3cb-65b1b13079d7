package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import java.time.LocalDate;


public class TonPhatKhauMMTop15DashResDTO implements Comparable<TonPhatKhauMMTop15DashResDTO> {

    @Override
    public int compareTo(TonPhatKhauMMTop15DashResDTO t) {
        return this.getSlMM().compareTo(t.getSlMM());
    }

    private String donVi;

    private Long dvNhanh;

    private Long dvCham;

    private Long dvHoaToc;

    private Long slMM;

    private Long tong;

    private Float tlLoiMM;

    public TonPhatKhauMMTop15DashResDTO(String donVi, Long dvCham, Long dvNhanh, Long dvHoaToc, Long slMM) {
        this.donVi = donVi;
        this.dvNhanh = dvNhanh;
        this.dvCham = dvCham;
        this.dvHoaToc = dvHoaToc;
        this.slMM = slMM;
    }

    public TonPhatKhauMMTop15DashResDTO() {
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getDvNhanh() {
        return dvNhanh;
    }

    public void setDvNhanh(Long dvNhanh) {
        this.dvNhanh = dvNhanh;
    }

    public Long getDvCham() {
        return dvCham;
    }

    public void setDvCham(Long dvCham) {
        this.dvCham = dvCham;
    }

    public Long getDvHoaToc() {
        return dvHoaToc;
    }

    public void setDvHoaToc(Long dvHoaToc) {
        this.dvHoaToc = dvHoaToc;
    }

    public Long getSlMM() {
        return slMM;
    }

    public void setSlMM(Long slMM) {
        this.slMM = slMM;
    }

    public Long getTong() {
        return tong;
    }

    public void setTong(Long tong) {
        this.tong = tong;
    }

    public Float getTlLoiMM() {
        return tlLoiMM;
    }

    public void setTlLoiMM(Float tlLoiMM) {
        this.tlLoiMM = tlLoiMM;
    }
}
