package nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonTTKTBillResponseDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("ma_phieugui")
    private String maPhieuGui;

    @JsonProperty("tinh_nhan")
    private String tinhNhan;

    @JsonProperty("huyen_nhan")
    private Integer huyenNhan;

    @JsonProperty("ten_huyen_nhan")
    private String tenHuyenNhan;

    @JsonProperty("ttkt_from")
    private String ttktFrom;

    @JsonProperty("tinh_phat")
    private String tinhPhat;

    @JsonProperty("huyen_phat")
    private Integer huyenPhat;

    @JsonProperty("ten_huyen_phat")
    private String tenHuyenPhat;

    @JsonProperty("ttkt_to")
    private String ttktTo;

    @JsonProperty("ma_dv_viettel")
    private String maDvViettel;

    @JsonProperty("ma_buucuc_goc")
    private String maBuuCucGoc;

    @JsonProperty("time_tac_dong")
    private String timeTacDong;

    @JsonProperty("trang_thai")
    private String trangThai;

    @JsonProperty("ma_buucuc_ht")
    private String maBuuCucHT;

    @JsonProperty("chi_nhanh_ht")
    private String chiNhanhHT;

    @JsonProperty("ma_doitac")
    private String maDoiTac;

    @JsonProperty("ma_khgui")
    private String maKHGui;

    @JsonProperty("ma_buucuc_phat")
    private String maBuuCucPhat;

    @JsonProperty("time_pcp")
    private String timePCP;

    @JsonProperty("time_gach_bp")
    private String timeGachBP;

    @JsonProperty("ngay_gui_bp")
    private String ngayguiBP;

    @JsonProperty("danh_gia")
    private String danhGia;

    @JsonProperty("loai_pg")
    private Integer loaiPG;

    @JsonProperty("lan_phat")
    private Integer lanPhat;

    @JsonProperty("tg_con_phat")
    private Float tgConPhat;

    @JsonProperty("buu_ta_phat")
    private String buuTaPhat;

    @JsonProperty("tien_cod")
    private Long tienCOD;

    @JsonProperty("khau_fm")
    private Float khauFM;

    @JsonProperty("khau_mm")
    private Float khauMM;

    @JsonProperty("khau_lm")
    private Float khauLM;

    @JsonProperty("tg_quydinh")
    private Integer tgQuyDinh;

    @JsonProperty("tg_tt_luyke")
    private Integer tgTTLuyKe;

    @JsonProperty("tg_chenhlech")
    private Integer tgChenhLech;

    @JsonProperty("loai_vung")
    private String loaiVung;

    @JsonProperty("nhom_dv")
    private String nhomDV;

    @JsonProperty("is_checked")
    private String isChecked;

    @JsonProperty("loai_canh_bao")
    private String loaiCanhBao;

    @JsonProperty("moc_ton_xe")
    private String mocTonXe;

    @JsonProperty("time_tinh_toan")
    private String timeTinhToan;
}
