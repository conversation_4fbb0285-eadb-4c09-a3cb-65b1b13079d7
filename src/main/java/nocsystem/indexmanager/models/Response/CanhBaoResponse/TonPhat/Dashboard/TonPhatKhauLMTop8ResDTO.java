package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;


import java.util.List;

public class TonPhatKhauLMTop8ResDTO implements Comparable<TonPhatKhauLMTop8ResDTO> {

    @Override
    public int compareTo(TonPhatKhauLMTop8ResDTO t) {
        return this.getTongSL().compareTo(t.getTongSL());
    }

    private String donVi;

    private Long lMVang;

    private Long lMDo1;

    private Long lMDo2;

    private Long lMDo3;

    private Long lMDo4;

    private Long lMDo5;

    private Long lMDo6;

    private Long lMDo7;

    private Long lMDo8;

    private Long lMDo9;

    private Long tongSL;

    private Long quaChiTieu;

    private Float tlQuaChiTieu;

    private Long conChiTieu;

    private Float tlConChiTieu;

    private List<String> tenTop10;

    private List<Long> slTop10;

    public TonPhatKhauLMTop8ResDTO(String donVi, Long lMVang, Long lMDo1, Long lMDo2, Long lMDo3, Long lMDo4, Long lMDo5, Long lMDo6, <PERSON> lMDo7, <PERSON> lMDo8, Long lMDo9, Long tongSL) {
        this.donVi = donVi;
        this.lMVang = lMVang;
        this.lMDo1 = lMDo1;
        this.lMDo2 = lMDo2;
        this.lMDo3 = lMDo3;
        this.lMDo4 = lMDo4;
        this.lMDo5 = lMDo5;
        this.lMDo6 = lMDo6;
        this.lMDo7 = lMDo7;
        this.lMDo8 = lMDo8;
        this.lMDo9 = lMDo9;
        this.tongSL = tongSL;
    }

    public TonPhatKhauLMTop8ResDTO() {
    }

    public TonPhatKhauLMTop8ResDTO(Long tongSL) {
        this.tongSL = tongSL;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getlMVang() {
        return lMVang;
    }

    public void setlMVang(Long lMVang) {
        this.lMVang = lMVang;
    }

    public Long getlMDo1() {
        return lMDo1;
    }

    public void setlMDo1(Long lMDo1) {
        this.lMDo1 = lMDo1;
    }

    public Long getlMDo2() {
        return lMDo2;
    }

    public void setlMDo2(Long lMDo2) {
        this.lMDo2 = lMDo2;
    }

    public Long getlMDo3() {
        return lMDo3;
    }

    public void setlMDo3(Long lMDo3) {
        this.lMDo3 = lMDo3;
    }

    public Long getlMDo4() {
        return lMDo4;
    }

    public void setlMDo4(Long lMDo4) {
        this.lMDo4 = lMDo4;
    }

    public Long getlMDo5() {
        return lMDo5;
    }

    public void setlMDo5(Long lMDo5) {
        this.lMDo5 = lMDo5;
    }

    public Long getlMDo6() {
        return lMDo6;
    }

    public void setlMDo6(Long lMDo6) {
        this.lMDo6 = lMDo6;
    }

    public Long getlMDo7() {
        return lMDo7;
    }

    public void setlMDo7(Long lMDo7) {
        this.lMDo7 = lMDo7;
    }

    public Long getlMDo8() {
        return lMDo8;
    }

    public void setlMDo8(Long lMDo8) {
        this.lMDo8 = lMDo8;
    }

    public Long getlMDo9() {
        return lMDo9;
    }

    public void setlMDo9(Long lMDo9) {
        this.lMDo9 = lMDo9;
    }

    public Long getTongSL() {
        return tongSL;
    }

    public void setTongSL(Long tongSL) {
        this.tongSL = tongSL;
    }

    public Long getQuaChiTieu() {
        return quaChiTieu;
    }

    public void setQuaChiTieu(Long quaChiTieu) {
        this.quaChiTieu = quaChiTieu;
    }

    public Float getTlQuaChiTieu() {
        return tlQuaChiTieu;
    }

    public void setTlQuaChiTieu(Float tlquaChiTieu) {
        this.tlQuaChiTieu = tlquaChiTieu;
    }

    public Long getConChiTieu() {
        return conChiTieu;
    }

    public void setConChiTieu(Long conChiTieu) {
        this.conChiTieu = conChiTieu;
    }

    public Float getTlConChiTieu() {
        return tlConChiTieu;
    }

    public void setTlConChiTieu(Float tlConChiTieu) {
        this.tlConChiTieu = tlConChiTieu;
    }

    public List<String> getTenTop10() {
        return tenTop10;
    }

    public void setTenTop10(List<String> tenTop10) {
        this.tenTop10 = tenTop10;
    }

    public List<Long> getSlTop10() {
        return slTop10;
    }

    public void setSlTop10(List<Long> slTop10) {
        this.slTop10 = slTop10;
    }
}
