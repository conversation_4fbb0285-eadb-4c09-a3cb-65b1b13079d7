package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard.TonPhatKhauMMTop15DashResDTO;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatKhauLMResponseDTO implements Comparable<TonPhatKhauLMResponseDTO>{

    @Override
    public int compareTo(TonPhatKhauLMResponseDTO t) {
        return this.getTongSL().compareTo(t.getTongSL());
    }

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("tinh_phat")
    private String tinhPhat;

    @JsonProperty("loai_ton")
    private String loaiTon;

    @JsonProperty("nguong_ton")
    private String loaiCanhBao;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("buu_ta_phat")
    private String buuTaPhat;

    @JsonProperty("lm_vang")
    private Long lMVang;

    @JsonProperty("lm_do_1")
    private Long lMDo1;

    @JsonProperty("lm_do_2")
    private Long lMDo2;

    @JsonProperty("lm_do_3")
    private Long lMDo3;

    @JsonProperty("lm_do_4")
    private Long lMDo4;

    @JsonProperty("lm_do_5")
    private Long lMDo5;

    @JsonProperty("lm_do_6")
    private Long lMDo6;

    @JsonProperty("lm_do_7")
    private Long lMDo7;

    @JsonProperty("lm_do_8")
    private Long lMDo8;

    @JsonProperty("lm_do_9")
    private Long lMDo9;

    @JsonProperty("tongsl")
    private Long tongSL;
}
