package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

public class TonPhatNguongTop15N9DashResDTO implements Comparable<TonPhatNguongTop15N9DashResDTO>{

    @Override
    public int compareTo(TonPhatNguongTop15N9DashResDTO t){
        return this.getSlN9().compareTo(t.getSlN9());
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getDvNhanh() {
        return dvNhanh;
    }

    public void setDvNhanh(Long dvNhanh) {
        this.dvNhanh = dvNhanh;
    }

    public Long getDvCham() {
        return dvCham;
    }

    public void setDvCham(Long dvCham) {
        this.dvCham = dvCham;
    }

    public Long getDvHoaToc() {
        return dvHoaToc;
    }

    public void setDvHoaToc(Long dvHoaToc) {
        this.dvHoaToc = dvHoaToc;
    }

    public Long getTong() {
        return tong;
    }

    public void setTong(Long tong) {
        this.tong = tong;
    }

    public Float getTlN9() {
        return tlN9;
    }

    public void setTlN9(Float tlN9) {
        this.tlN9 = tlN9;
    }

    public Long getSlN9() {
        return slN9;
    }

    public void setSlN9(Long slN9) {
        this.slN9 = slN9;
    }

    private String donVi;

    private Long dvNhanh;

    private Long dvCham;

    private Long dvHoaToc;

    private Long slN9;

    private Long tong;

    private Float tlN9;

    public TonPhatNguongTop15N9DashResDTO(String donVi, Long dvNhanh, Long dvCham, Long dvHoaToc, Long slN9, Long tong) {
        this.donVi = donVi;
        this.dvNhanh = dvNhanh;
        this.dvCham = dvCham;
        this.dvHoaToc = dvHoaToc;
        this.slN9 = slN9;
        this.tong = tong;
    }
}
