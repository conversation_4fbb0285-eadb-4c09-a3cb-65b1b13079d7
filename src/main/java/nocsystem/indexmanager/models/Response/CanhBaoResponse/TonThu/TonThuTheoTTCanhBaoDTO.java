package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor

public class TonThuTheoTTCanhBaoDTO {
    @JsonProperty("loai_canh_bao")
    private String loaiCanhBao;

    @JsonProperty("tt_100")
    private Float trangThai100;

    @JsonProperty("tt_102")
    private Float trangThai102;

    @JsonProperty("tt_103")
    private Float trangThai103;

    @JsonProperty("tt_104")
    private Float trangThai104;

    @JsonProperty("tong")
    private Float tong;

}
