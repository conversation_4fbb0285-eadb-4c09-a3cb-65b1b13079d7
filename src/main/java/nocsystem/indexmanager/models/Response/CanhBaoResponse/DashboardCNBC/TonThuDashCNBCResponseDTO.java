package nocsystem.indexmanager.models.Response.CanhBaoResponse.DashboardCNBC;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.TonThuTheoKHResponseDTO;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonThuDashCNBCResponseDTO implements Comparable<TonThuDashCNBCResponseDTO>{

    @JsonProperty("chi_nhanh_nhan")
    private String chiNhanhNhan;

    @JsonProperty("vung_con")
    private String vungCon;

    @JsonProperty("buu_cuc_nhan")
    private String buuCucNhan;

    @JsonProperty("buu_ta_nhan")
    private String buuTaNhan;

    @JsonProperty("xanh")
    private Long xanh;

    @JsonProperty("vang")
    private Long vang;

    @JsonProperty("do_1")
    private Long do1;

    @JsonProperty("do_2")
    private Long do2;

    @JsonProperty("do_3")
    private Long do3;

    @JsonProperty("tong")
    private Long tong;

//    @JsonProperty("shop_ton")
    private Long shopTon;

    public TonThuDashCNBCResponseDTO(String chiNhanhNhan, Long xanh, Long vang, Long do1, Long do2, Long do3, Long tong) {
        this.chiNhanhNhan = chiNhanhNhan;
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        if(xanh != null && vang != null && do1 != null && do2 != null && do3 != null) {
            this.tong = xanh + vang +do1 + do2 + do3;
        }
    }

    public TonThuDashCNBCResponseDTO(String chiNhanhNhan, String vungCon, String buuCucNhan, Long xanh, Long vang, Long do1, Long do2, Long do3, Long tong) {
        this.chiNhanhNhan = chiNhanhNhan;
        this.vungCon = vungCon;
        this.buuCucNhan = buuCucNhan;
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        if(xanh != null && vang != null && do1 != null && do2 != null && do3 != null) {
            this.tong = xanh + vang +do1 + do2 + do3;
        }
    }

    public TonThuDashCNBCResponseDTO(Long xanh, Long vang, Long do1, Long do2, Long do3, Long tong) {
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        if(xanh != null && vang != null && do1 != null && do2 != null && do3 != null) {
            this.tong = xanh + vang +do1 + do2 + do3;
        }
    }

    public TonThuDashCNBCResponseDTO(Long xanh, Long vang, Long do1, Long do2, Long do3, Long tong, Long shopTon) {
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        if(xanh != null && vang != null && do1 != null && do2 != null && do3 != null) {
            this.tong = xanh + vang +do1 + do2 + do3;
        }
        this.shopTon = shopTon;
    }

    public TonThuDashCNBCResponseDTO(Long tong) {
        this.tong = tong;
    }

    @Override
    public int compareTo(TonThuDashCNBCResponseDTO t) {
        return this.getTong().compareTo(t.getTong());
    }
}
