package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatKhauMMDTO {

    @JsonProperty("ngay_baocao")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate ngayBaoCao;

    @JsonProperty("tinh_phat")
    private List<String> tinhPhat;

    @JsonProperty("loai_ton")
    private List<String> loaiTon;

    @JsonProperty("loai_don")
    private List<String> loaiDon;

    @JsonProperty("loai_canh_bao")
    private List<String> loaiCanhBao;

    @JsonProperty("dg_moc_mm")
    private List<String> dGMocMM;

    @JsonProperty("ma_buu_cuc")
    private List<String> maBuuCuc;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("page_size")
    private Integer pageSize;

    @JsonProperty("sort")
    private String sort;
}
