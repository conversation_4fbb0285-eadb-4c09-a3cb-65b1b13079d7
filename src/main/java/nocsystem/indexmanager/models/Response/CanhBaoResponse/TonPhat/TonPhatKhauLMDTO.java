package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatKhauLMDTO {

    @JsonProperty("ngay_baocao")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate ngayBaoCao;

    @JsonProperty("tinh_phat")
    private List<String> maTinh;

    @JsonProperty("loai_ton")
    private List<String> loaiTon;

    @JsonProperty("ma_buu_cuc")
    private List<String> maBuuCuc;

    @JsonProperty("nguong_ton")
    private List<String> maCanhBao;

    @JsonProperty("loai_dich_vu")
    private List<String> loaiDichVu;

    @JsonProperty("loi_khau")
    private List<String> loiKhau;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("page_size")
    private Integer pageSize;

    @JsonProperty("sort")
    private String sort;
}
