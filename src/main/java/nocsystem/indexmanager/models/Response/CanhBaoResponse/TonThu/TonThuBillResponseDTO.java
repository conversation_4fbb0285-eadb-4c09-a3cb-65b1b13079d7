package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
public class TonThuBillResponseDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("ma_phieugui")
    private String maPhieuGui;

    @JsonProperty("trang_thai")
    private String trangThai;

    @JsonProperty("ma_doitac")
    private String maDoiTac;

    @JsonProperty("kh_vip")
    private Integer khVIP;

    @JsonProperty("ngay_tao")
    private String ngayTao;

    @JsonProperty("ngay_duyet")
    private String ngayDuyet;

    @JsonProperty("ngay_hen_thu")
    private String ngayHenThu;

    @JsonProperty("tinh_khgui")
    private String tinhKHGui;

    @JsonProperty("ten_khgui")
    private String tenKHGui;

    @JsonProperty("diachi_khgui")
    private String diaChiKHGui;

    @JsonProperty("tinh_khnhan")
    private String tinhKHNhan;

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("ma_dv_viettel")
    private String maDVViettel;

    @JsonProperty("tong_tien")
    private Long tongTien;

    @JsonProperty("buuta_nhan")
    private String buuTaNhan;

    @JsonProperty("ten_buu_ta")
    private String tenBuuTa;

    @JsonProperty("tg_ton")
    private Long tgTon;

    @JsonProperty("ngay_ton")
    private Integer ngayTon;

    @JsonProperty("loai_canh_bao")
    private String loaiCanhBao;

    @JsonProperty("nhom_dv")
    private String nhomDV;

    @JsonProperty("time_tinh_toan")
    private String timeTinhToan;

    @JsonProperty("kh_dacthu_gui")
    private String khDacThuGui;

    @JsonProperty("kh_dacthu_nhan")
    private String khDacThuNhan;

    @JsonProperty("ma_khgui")
    private String maKhGui;

    private String idPhuongXaNhan;
    private String tenPhuongXaNhan;

    @JsonProperty("ten_quanhuyen_nhan")
    private String tenQuanHuyenNhan;

    @JsonProperty("ma_dv_congthem")
    private String maDichVuCongThem;

    @Column(name = "time_102_cuoicung")
    private String time102CuoiCung;

    @Column(name = "so_lan_102_luy_ke")
    private Integer soLan102LuyKe;

    @Column(name = "so_lan_102_trong_ngay")
    private Integer soLan102TrongNgay;


    @JsonProperty("laoi_don")
    private String loaiDon;

    public TonThuBillResponseDTO() {
    }

    public TonThuBillResponseDTO(LocalDate ngayBaoCao, String maPhieuGui, String trangThai, String maDoiTac, Integer khVIP, String ngayTao, String ngayDuyet, String ngayHenThu, String tinhKHGui, String tenKHGui, String diaChiKHGui, String tinhKHNhan, String maBuuCuc, String chiNhanh, String maDVViettel, Long tongTien, String buuTaNhan, String tenBuuTa, Long tgTon, Integer ngayTon, String loaiCanhBao, String nhomDV, String timeTinhToan, String khDacThuGui, String khDacThuNhan, String maKhGui, String idPhuongXaNhan, String tenPhuongXaNhan) {
        this.ngayBaoCao = ngayBaoCao;
        this.maPhieuGui = maPhieuGui;
        this.trangThai = trangThai;
        this.maDoiTac = maDoiTac;
        this.khVIP = khVIP;
        this.ngayTao = ngayTao;
        this.ngayDuyet = ngayDuyet;
        this.ngayHenThu = ngayHenThu;
        this.tinhKHGui = tinhKHGui;
        this.tenKHGui = tenKHGui;
        this.diaChiKHGui = diaChiKHGui;
        this.tinhKHNhan = tinhKHNhan;
        this.maBuuCuc = maBuuCuc;
        this.chiNhanh = chiNhanh;
        this.maDVViettel = maDVViettel;
        this.tongTien = tongTien;
        this.buuTaNhan = buuTaNhan;
        this.tenBuuTa = tenBuuTa;
        this.tgTon = tgTon;
        this.ngayTon = ngayTon;
        this.loaiCanhBao = loaiCanhBao;
        this.nhomDV = nhomDV;
        this.timeTinhToan = timeTinhToan;
        this.khDacThuGui = khDacThuGui;
        this.khDacThuNhan = khDacThuNhan;
        this.maKhGui = maKhGui;
        this.idPhuongXaNhan = idPhuongXaNhan;
        this.tenPhuongXaNhan = tenPhuongXaNhan;
    }

    public TonThuBillResponseDTO(LocalDate ngayBaoCao, String maPhieuGui, String trangThai, String maDoiTac, String ngayTao, String ngayDuyet, String ngayHenThu, String tinhKHGui, String tenKHGui, String diaChiKHGui, String tinhKHNhan, String maBuuCuc, String chiNhanh, String maDVViettel, Long tongTien, String buuTaNhan, String tenBuuTa, Long tgTon, Integer ngayTon, String loaiCanhBao, String nhomDV, String timeTinhToan, String khDacThuGui, String khDacThuNhan, String maKhGui, String idPhuongXaNhan, String tenPhuongXaNhan, String loaiDon, String tenQuanHuyenNhan, String maDichVuCongThem, String time102CuoiCung, Integer soLan102LuyKe, Integer soLan102TrongNgay) {
        this.ngayBaoCao = ngayBaoCao;
        this.maPhieuGui = maPhieuGui;
        this.trangThai = trangThai;
        this.maDoiTac = maDoiTac;
        this.ngayTao = ngayTao;
        this.ngayDuyet = ngayDuyet;
        this.ngayHenThu = ngayHenThu;
        this.tinhKHGui = tinhKHGui;
        this.tenKHGui = tenKHGui;
        this.diaChiKHGui = diaChiKHGui;
        this.tinhKHNhan = tinhKHNhan;
        this.maBuuCuc = maBuuCuc;
        this.chiNhanh = chiNhanh;
        this.maDVViettel = maDVViettel;
        this.tongTien = tongTien;
        this.buuTaNhan = buuTaNhan;
        this.tenBuuTa = tenBuuTa;
        this.tgTon = tgTon;
        this.ngayTon = ngayTon;
        this.loaiCanhBao = loaiCanhBao;
        this.nhomDV = nhomDV;
        this.timeTinhToan = timeTinhToan;
        this.khDacThuGui = khDacThuGui;
        this.khDacThuNhan = khDacThuNhan;
        this.maKhGui = maKhGui;
        this.idPhuongXaNhan = idPhuongXaNhan;
        this.tenPhuongXaNhan = tenPhuongXaNhan;
        this.loaiDon = loaiDon;
        this.tenQuanHuyenNhan = tenQuanHuyenNhan;
        this.maDichVuCongThem = maDichVuCongThem;
        this.time102CuoiCung = time102CuoiCung;
        this.soLan102LuyKe = soLan102LuyKe;
        this.soLan102TrongNgay = soLan102TrongNgay;
    }
}
