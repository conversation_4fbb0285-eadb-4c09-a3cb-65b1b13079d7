package nocsystem.indexmanager.models.Response.CanhBaoResponse.TrungTam;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class KhoLienVung6hDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private List<String> chiNhanh;

    @JsonProperty("buu_cuc")
    private List<String> buuCuc;

    @JsonProperty("loai_vung")
    private List<String> loaiVung;

    @JsonProperty("nhom_dich_vu")
    private List<String> nhomDV;

    @JsonProperty("ma_phieugui")
    private List<String> maPhieuGui;

    @JsonProperty("moc_ton")
    private List<String> mocTon;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("page_size")
    private Integer pageSize;

    @JsonProperty("sort")
    private String sort;
}
