package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
public class TonPhatNguongLoaiTonDashDTO {

    private String loaiTon;

    private Long sln7Nhanh;

    private Long sln7Cham;

    private Long sln7HoaToc;

    private Long sln7TongSL;

    private Long sln8Nhanh;

    private Long sln8Cham;

    private Long sln8HoaToc;

    private Long sln8TongSL;

    private Long sln9Nhanh;

    private Long sln9Cham;

    private Long sln9HoaToc;

    private Long sln9TongSL;

    private Long tongSLNhanh;

    private Long tongSLCham;

    private Long tongSLHoaToc;

    public String getLoaiTon() {
        return loaiTon;
    }

    public void setLoaiTon(String loaiTon) {
        this.loaiTon = loaiTon;
    }

    public Long getSln7Nhanh() {
        return sln7Nhanh;
    }

    public void setSln7Nhanh(Long sln7Nhanh) {
        this.sln7Nhanh = sln7Nhanh;
    }

    public Long getSln7Cham() {
        return sln7Cham;
    }

    public void setSln7Cham(Long sln7Cham) {
        this.sln7Cham = sln7Cham;
    }

    public Long getSln7HoaToc() {
        return sln7HoaToc;
    }

    public void setSln7HoaToc(Long sln7HoaToc) {
        this.sln7HoaToc = sln7HoaToc;
    }

    public Long getSln7TongSL() {
        return sln7TongSL;
    }

    public void setSln7TongSL(Long sln7TongSL) {
        this.sln7TongSL = sln7TongSL;
    }

    public Long getSln8Nhanh() {
        return sln8Nhanh;
    }

    public void setSln8Nhanh(Long sln8Nhanh) {
        this.sln8Nhanh = sln8Nhanh;
    }

    public Long getSln8Cham() {
        return sln8Cham;
    }

    public void setSln8Cham(Long sln8Cham) {
        this.sln8Cham = sln8Cham;
    }

    public Long getSln8HoaToc() {
        return sln8HoaToc;
    }

    public void setSln8HoaToc(Long sln8HoaToc) {
        this.sln8HoaToc = sln8HoaToc;
    }

    public Long getSln8TongSL() {
        return sln8TongSL;
    }

    public void setSln8TongSL(Long sln8TongSL) {
        this.sln8TongSL = sln8TongSL;
    }

    public Long getSln9Nhanh() {
        return sln9Nhanh;
    }

    public void setSln9Nhanh(Long sln9Nhanh) {
        this.sln9Nhanh = sln9Nhanh;
    }

    public Long getSln9Cham() {
        return sln9Cham;
    }

    public void setSln9Cham(Long sln9Cham) {
        this.sln9Cham = sln9Cham;
    }

    public Long getSln9HoaToc() {
        return sln9HoaToc;
    }

    public void setSln9HoaToc(Long sln9HoaToc) {
        this.sln9HoaToc = sln9HoaToc;
    }

    public Long getSln9TongSL() {
        return sln9TongSL;
    }

    public void setSln9TongSL(Long sln9TongSL) {
        this.sln9TongSL = sln9TongSL;
    }

    public Long getTongSLNhanh() {
        return tongSLNhanh;
    }

    public void setTongSLNhanh(Long tongSLNhanh) {
        this.tongSLNhanh = tongSLNhanh;
    }

    public Long getTongSLCham() {
        return tongSLCham;
    }

    public void setTongSLCham(Long tongSLCham) {
        this.tongSLCham = tongSLCham;
    }

    public Long getTongSLHoaToc() {
        return tongSLHoaToc;
    }

    public void setTongSLHoaToc(Long tongSLHoaToc) {
        this.tongSLHoaToc = tongSLHoaToc;
    }
}
