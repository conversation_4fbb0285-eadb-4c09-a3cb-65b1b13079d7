package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatNguongNewResponseDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("tinh_phat")
    private String tinhPhat;

    @JsonProperty("loai_ton")
    private String loaiTon;

    @JsonProperty("nguong_ton")
    private String nguongTon;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("buu_ta_phat")
    private String buuTaPhat;

    @JsonProperty("dv_nhanh")
    private Long dvNhanh;

    @JsonProperty("dv_cham")
    private Long dvCham;

    @JsonProperty("dv_hoatoc")
    private Long dvHoaToc;

    @JsonProperty("tongsl")
    private Long tongSL;
}
