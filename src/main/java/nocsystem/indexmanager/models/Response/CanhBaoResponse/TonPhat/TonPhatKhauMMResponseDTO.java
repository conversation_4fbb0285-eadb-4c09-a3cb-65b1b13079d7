package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatKhauMMResponseDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("tinh_phat")
    private String tinhPhat;

    @JsonProperty("loai_ton")
    private String loaiTon;

    @JsonProperty("loai_don")
    private String loaiDon;

    @JsonProperty("loai_canh_bao")
    private String loaiCanhBao;

    @JsonProperty("dg_moc_mm")
    private String dGMocMM;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("buu_ta_phat")
    private String buuTaPhat;

    @JsonProperty("sltp_nhanh")
    private Long sLTPNhanh;

    @JsonProperty("sltp_cham")
    private Long sLTPCham;

    @JsonProperty("sltp_hoatoc")
    private Long sLTPHoaToc;

    @JsonProperty("tongsl")
    private Long tongSL;
}
