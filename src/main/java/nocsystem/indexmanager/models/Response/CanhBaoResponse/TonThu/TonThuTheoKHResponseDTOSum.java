package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
public class TonThuTheoKHResponseDTOSum {

     private String donVi;
     private Long khachHang0;

     private Long khachHang1;

     private Long khachHang2;

     private <PERSON> khachHang3;

     private Long khachHang4;

     private  Long khachHang5;

     private Long khachHang6;

     private  Long khachHang7;

     private  Long khachHang8;

     private Long tong;


     public TonThuTheoKHResponseDTOSum(String donVi, Long khachHang0, Long khachHang1, Long khachHang2, Long khachHang3, Long khachHang4,
                                       Long khachHang5, Long khachHang6, Long khachHang7, Long khachHang8, Long tong) {
          this.donVi= donVi;
          this.khachHang0 = khachHang0;
          this.khachHang1 = khachHang1;
          this.khachHang2 = khachHang2;
          this.khachHang3 = khachHang3;
          this.khachHang4 = khachHang4;
          this.khachHang5 = khachHang5;
          this.khachHang6 = khachHang6;
          this.khachHang7 = khachHang7;
          this.khachHang8 = khachHang8;
          this.tong = tong;
     }

     public TonThuTheoKHResponseDTO mapTwoDto(){
          TonThuTheoKHResponseDTO tTTheoKHTmp = new TonThuTheoKHResponseDTO();
          tTTheoKHTmp.setLoaiCanhBao(this.getDonVi());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang0());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang1());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang2());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang3());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang4());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang5());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang6());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang7());
          tTTheoKHTmp.setKhachHang0(this.getKhachHang8());
          tTTheoKHTmp.setKhachHang0(this.getTong());
          return tTTheoKHTmp;
     }

}
