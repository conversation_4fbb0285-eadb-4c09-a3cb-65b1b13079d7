package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;

import java.time.LocalDate;
import java.util.List;


public class TonThuTop10DoResDTO implements Comparable<TonThuTop10DoResDTO> {

    @Override
    public int compareTo(TonThuTop10DoResDTO t) {
        return this.getTong().compareTo(t.getTong());
    }

    private LocalDate ngayBaoCao;

    private String donVi;

    private Long xanh;

    private Long vang;

    private Long do_1;

    private Long do_2;

    private Long do_3;

    private Long tong;

    private Long quaChiTieu;

    private Long conChiTieu;

    private Float tlQuaChiTieu;

    private Float tlConChiTieu;

    private List<String> tenTop10;

    private List<Long> slTop10;

    public LocalDate getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(LocalDate ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getDo_1() {
        return do_1;
    }

    public void setDo_1(Long do_1) {
        this.do_1 = do_1;
    }

    public Long getDo_2() {
        return do_2;
    }

    public void setDo_2(Long do_2) {
        this.do_2 = do_2;
    }

    public Long getDo_3() {
        return do_3;
    }

    public void setDo_3(Long do_3) {
        this.do_3 = do_3;
    }

    public Long getTong() {
        return tong;
    }

    public void setTong(Long tong) {
        this.tong = tong;
    }

    public Long getXanh() {
        return xanh;
    }

    public void setXanh(Long xanh) {
        this.xanh = xanh;
    }

    public Long getVang() {
        return vang;
    }

    public void setVang(Long vang) {
        this.vang = vang;
    }

    public Long getQuaChiTieu() {
        return quaChiTieu;
    }

    public void setQuaChiTieu(Long quaChiTieu) {
        this.quaChiTieu = quaChiTieu;
    }

    public Long getConChiTieu() {
        return conChiTieu;
    }

    public void setConChiTieu(Long conChiTieu) {
        this.conChiTieu = conChiTieu;
    }

    public Float getTlQuaChiTieu() {
        return tlQuaChiTieu;
    }

    public void setTlQuaChiTieu(Float tlQuaChiTieu) {
        this.tlQuaChiTieu = tlQuaChiTieu;
    }

    public Float getTlConChiTieu() {
        return tlConChiTieu;
    }

    public void setTlConChiTieu(Float tlConChiTieu) {
        this.tlConChiTieu = tlConChiTieu;
    }

    public List<String> getTenTop10() {
        return tenTop10;
    }

    public void setTenTop10(List<String> tenTop10) {
        this.tenTop10 = tenTop10;
    }

    public List<Long> getSlTop10() {
        return slTop10;
    }

    public void setSlTop10(List<Long> slTop10) {
        this.slTop10 = slTop10;
    }

    public TonThuTop10DoResDTO(String donVi, Long do_1, Long do_2, Long do_3, Long tong) {
        this.donVi = donVi;
        this.do_1 = do_1;
        this.do_2 = do_2;
        this.do_3 = do_3;
        this.tong = tong;
    }

    public TonThuTop10DoResDTO(Long xanh, Long vang, Long do_1, Long do_2, Long do_3, Long tong) {
        this.xanh = xanh;
        this.vang = vang;
        this.do_1 = do_1;
        this.do_2 = do_2;
        this.do_3 = do_3;
        this.tong = tong;
    }

    public TonThuTop10DoResDTO() {
    }
}
