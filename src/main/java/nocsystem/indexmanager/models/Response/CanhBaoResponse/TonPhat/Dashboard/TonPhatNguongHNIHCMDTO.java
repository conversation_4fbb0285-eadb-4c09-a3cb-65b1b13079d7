package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonPhat.Dashboard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonPhatNguongHNIHCMDTO {

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("sln9_nhanh")
    private Long sln9Nhanh;

    @JsonProperty("sln9_cham")
    private Long sln9Cham;

    @JsonProperty("sln9_hoatoc")
    private Long sln9HoaToc;

    @JsonProperty("sln9_tongsl")
    private Long sln9TongSL;

    @JsonProperty("tongsl")
    private Long tongSL;
}
