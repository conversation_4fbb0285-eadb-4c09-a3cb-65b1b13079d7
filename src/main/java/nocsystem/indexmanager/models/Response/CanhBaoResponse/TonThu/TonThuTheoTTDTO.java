package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class TonThuTheoTTDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("tinh_nhan")
    private List<String> tinhNhan;

    @JsonProperty("chi_nhanh_nhan")
    private List<String> chiNhanhNhan;

    @JsonProperty("buu_cuc_nhan")
    private List<String> buuCucNhan;

    @JsonProperty("buu_ta_nhan")
    private List<String> buuTaNhan;

    @JsonProperty("loai_canh_bao")
    private List<String> loaiCanhBao;

}
