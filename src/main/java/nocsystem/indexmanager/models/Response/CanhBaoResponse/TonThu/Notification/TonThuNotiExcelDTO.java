package nocsystem.indexmanager.models.Response.CanhBaoResponse.TonThu.Notification;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public class TonThuNotiExcelDTO{

    private String donVi;

    private Long tong;

    private Long xanh;

    private Long vang;

    private Long do1;

    private Long do2;

    private Long do3;

    private String loaiCanhBao;

    public TonThuNotiExcelDTO(String donVi, Long tong, String loaiCanhBao) {
        this.donVi = donVi;
        this.tong = tong;
        this.loaiCanhBao = loaiCanhBao;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getTong() {
        return tong;
    }

    public void setTong(Long tong) {
        this.tong = tong;
    }

    public Long getXanh() {
        return xanh;
    }

    public void setXanh(Long xanh) {
        this.xanh = xanh;
    }

    public Long getVang() {
        return vang;
    }

    public void setVang(Long vang) {
        this.vang = vang;
    }

    public Long getDo1() {
        return do1;
    }

    public void setDo1(Long do1) {
        this.do1 = do1;
    }

    public Long getDo2() {
        return do2;
    }

    public void setDo2(Long do2) {
        this.do2 = do2;
    }

    public Long getDo3() {
        return do3;
    }

    public void setDo3(Long do3) {
        this.do3 = do3;
    }

    public String getLoaiCanhBao() {
        return loaiCanhBao;
    }

    public void setLoaiCanhBao(String loaiCanhBao) {
        this.loaiCanhBao = loaiCanhBao;
    }
}
