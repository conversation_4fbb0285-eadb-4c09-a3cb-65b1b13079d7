package nocsystem.indexmanager.models.Response.DashBoardChiNhanh;

import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TonDetailBillResponse {
    private String maPhieuGui;
    private String tenBaoCao;
    private String danhGia;
    private String chiNhanh;
    private String buuCucNhan;
    private String buuCucPhat;
    private String buuCucHienTai;
    private String trangThai;
    private String tuyenBuuTa;
    private String loaiTon;
    private String maDichVu;
    private String loaiDichVu;
    private String loaiDon;
    private String ncodCod;
    private String tgTaoDonKhauNhan;
    private String tgThuDonKhauNhan;
    private String tgYeuCauLayKhauNhan;
    private String tgBanGiaoKhauNhan;
    private String tgBCPNhanBanGiaoKhauPhat;
    private String ngayNhapMay;
    private String thoiGianPCPKhauPhat;
    private String tgQdPhatKhauPhat;
    private String tgTinhKpiKhauPhat;
    private String tgPhatLan1KhauPhat;
    private String tgPhatThanhCongKhauPhat;
    private String tgPhatQdKhauPhat;
}
