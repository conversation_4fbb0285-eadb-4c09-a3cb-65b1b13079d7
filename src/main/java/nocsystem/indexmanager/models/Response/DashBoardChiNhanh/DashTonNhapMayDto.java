package nocsystem.indexmanager.models.Response.DashBoardChiNhanh;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;


public class DashTonNhapMayDto {

    String ngayBaoCao;
    String idPhieuGui;
    String maPhieuGui;
    String tinhNhan;
    String huyenNhan;
    String tenHuyenNhan;
    String ttktFrom;
    String tinhPhat;
    String huyenPhat;
    String tenHuyenPhat;
    String ttktTo;
    String maDvViettel;
    String maBuuCucGoc;
    String timeTacDong;
    String trangThai;
    String maBuuCucHt;
    String chiNhanhHt;
    String maDoiTac;
    String maKhGui;
    String maBuuCucPhat;
    String timePCP;
    String timeGachBP;
    String ngayGuiBP;
    String danhGia;
    String loaiPG;
    String lanPhat;
    String tgConPhat;
    String buuTaPhat;
    String tienCOD;
    String khauFM;
    String khauMM;

    String khauLM;

    String tgQuyDinh;

    String tgTTLuyKe;

    String tgChenhLech;

    String tonXuLy;

    String mocTG;

    String mocTonXL;

    String loaiCanhBao;

    String nhomDV;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public DashTonNhapMayDto() {}

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public DashTonNhapMayDto(String ngayBaoCao, String idPhieuGui, String maPhieuGui, String tinhNhan, String huyenNhan, String tenHuyenNhan, String ttktFrom, String tinhPhat, String huyenPhat, String tenHuyenPhat, String ttktTo, String maDvViettel, String maBuuCucGoc, String timeTacDong, String trangThai, String maBuuCucHt, String chiNhanhHt, String maDoiTac, String maKhGui, String maBuuCucPhat, String timePCP, String timeGachBP, String ngayGuiBP, String danhGia, String loaiPG, String lanPhat, String tgConPhat, String buuTaPhat, String tienCOD, String khauFM, String khauMM, String khauLM, String tgQuyDinh, String tgTTLuyKe, String tgChenhLech, String tonXuLy, String mocTG, String mocTonXL, String loaiCanhBao, String nhomDV) throws NoSuchFieldException, IllegalAccessException {
        this.ngayBaoCao = ngayBaoCao;
        this.idPhieuGui = idPhieuGui;
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tenHuyenNhan = tenHuyenNhan;
        this.ttktFrom = ttktFrom;
        this.tinhPhat = tinhPhat;
        this.huyenPhat = huyenPhat;
        this.tenHuyenPhat = tenHuyenPhat;
        this.ttktTo = ttktTo;
        this.maDvViettel = maDvViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.timeTacDong = timeTacDong;
        this.trangThai = trangThai;
        this.maBuuCucHt = maBuuCucHt;
        this.chiNhanhHt = chiNhanhHt;
        this.maDoiTac = maDoiTac;
        this.maKhGui = maKhGui;
        this.maBuuCucPhat = maBuuCucPhat;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.ngayGuiBP = ngayGuiBP;
        this.danhGia = danhGia;
        this.loaiPG = loaiPG;
        this.lanPhat = lanPhat;
        this.tgConPhat = tgConPhat;
        this.buuTaPhat = buuTaPhat;
        this.tienCOD = tienCOD;
        this.khauFM = khauFM;
        this.khauMM = khauMM;
        this.khauLM = khauLM;
        this.tgQuyDinh = tgQuyDinh;
        this.tgTTLuyKe = tgTTLuyKe;
        this.tgChenhLech = tgChenhLech;
        this.tonXuLy = tonXuLy;
        this.mocTG = mocTG;
        this.mocTonXL = mocTonXL;
        this.loaiCanhBao = loaiCanhBao;
        this.nhomDV = nhomDV;
        this.objectHashMap = convert(this);
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getIdPhieuGui() {
        return idPhieuGui;
    }

    public void setIdPhieuGui(String idPhieuGui) {
        this.idPhieuGui = idPhieuGui;
    }

    public String getMaPhieuGui() {
        return maPhieuGui;
    }

    public void setMaPhieuGui(String maPhieuGui) {
        this.maPhieuGui = maPhieuGui;
    }

    public String getTinhNhan() {
        return tinhNhan;
    }

    public void setTinhNhan(String tinhNhan) {
        this.tinhNhan = tinhNhan;
    }

    public String getHuyenNhan() {
        return huyenNhan;
    }

    public void setHuyenNhan(String huyenNhan) {
        this.huyenNhan = huyenNhan;
    }

    public String getTenHuyenNhan() {
        return tenHuyenNhan;
    }

    public void setTenHuyenNhan(String tenHuyenNhan) {
        this.tenHuyenNhan = tenHuyenNhan;
    }

    public String getTtktFrom() {
        return ttktFrom;
    }

    public void setTtktFrom(String ttktFrom) {
        this.ttktFrom = ttktFrom;
    }

    public String getTinhPhat() {
        return tinhPhat;
    }

    public void setTinhPhat(String tinhPhat) {
        this.tinhPhat = tinhPhat;
    }

    public String getHuyenPhat() {
        return huyenPhat;
    }

    public void setHuyenPhat(String huyenPhat) {
        this.huyenPhat = huyenPhat;
    }

    public String getTenHuyenPhat() {
        return tenHuyenPhat;
    }

    public void setTenHuyenPhat(String tenHuyenPhat) {
        this.tenHuyenPhat = tenHuyenPhat;
    }

    public String getTtktTo() {
        return ttktTo;
    }

    public void setTtktTo(String ttktTo) {
        this.ttktTo = ttktTo;
    }

    public String getMaDvViettel() {
        return maDvViettel;
    }

    public void setMaDvViettel(String maDvViettel) {
        this.maDvViettel = maDvViettel;
    }

    public String getMaBuuCucGoc() {
        return maBuuCucGoc;
    }

    public void setMaBuuCucGoc(String maBuuCucGoc) {
        this.maBuuCucGoc = maBuuCucGoc;
    }

    public String getTimeTacDong() {
        return timeTacDong;
    }

    public void setTimeTacDong(String timeTacDong) {
        this.timeTacDong = timeTacDong;
    }

    public String getTrangThai() {
        return trangThai;
    }

    public void setTrangThai(String trangThai) {
        this.trangThai = trangThai;
    }

    public String getMaBuuCucHt() {
        return maBuuCucHt;
    }

    public void setMaBuuCucHt(String maBuuCucHt) {
        this.maBuuCucHt = maBuuCucHt;
    }

    public String getChiNhanhHt() {
        return chiNhanhHt;
    }

    public void setChiNhanhHt(String chiNhanhHt) {
        this.chiNhanhHt = chiNhanhHt;
    }

    public String getMaDoiTac() {
        return maDoiTac;
    }

    public void setMaDoiTac(String maDoiTac) {
        this.maDoiTac = maDoiTac;
    }

    public String getMaKhGui() {
        return maKhGui;
    }

    public void setMaKhGui(String maKhGui) {
        this.maKhGui = maKhGui;
    }

    public String getMaBuuCucPhat() {
        return maBuuCucPhat;
    }

    public void setMaBuuCucPhat(String maBuuCucPhat) {
        this.maBuuCucPhat = maBuuCucPhat;
    }

    public String getTimePCP() {
        return timePCP;
    }

    public void setTimePCP(String timePCP) {
        this.timePCP = timePCP;
    }

    public String getTimeGachBP() {
        return timeGachBP;
    }

    public void setTimeGachBP(String timeGachBP) {
        this.timeGachBP = timeGachBP;
    }

    public String getNgayGuiBP() {
        return ngayGuiBP;
    }

    public void setNgayGuiBP(String ngayGuiBP) {
        this.ngayGuiBP = ngayGuiBP;
    }

    public String getDanhGia() {
        return danhGia;
    }

    public void setDanhGia(String danhGia) {
        this.danhGia = danhGia;
    }

    public String getLoaiPG() {
        return loaiPG;
    }

    public void setLoaiPG(String loaiPG) {
        this.loaiPG = loaiPG;
    }

    public String getLanPhat() {
        return lanPhat;
    }

    public void setLanPhat(String lanPhat) {
        this.lanPhat = lanPhat;
    }

    public String getTgConPhat() {
        return tgConPhat;
    }

    public void setTgConPhat(String tgConPhat) {
        this.tgConPhat = tgConPhat;
    }

    public String getBuuTaPhat() {
        return buuTaPhat;
    }

    public void setBuuTaPhat(String buuTaPhat) {
        this.buuTaPhat = buuTaPhat;
    }

    public String getTienCOD() {
        return tienCOD;
    }

    public void setTienCOD(String tienCOD) {
        this.tienCOD = tienCOD;
    }

    public String getKhauFM() {
        return khauFM;
    }

    public void setKhauFM(String khauFM) {
        this.khauFM = khauFM;
    }

    public String getKhauMM() {
        return khauMM;
    }

    public void setKhauMM(String khauMM) {
        this.khauMM = khauMM;
    }

    public String getKhauLM() {
        return khauLM;
    }

    public void setKhauLM(String khauLM) {
        this.khauLM = khauLM;
    }

    public String getTgQuyDinh() {
        return tgQuyDinh;
    }

    public void setTgQuyDinh(String tgQuyDinh) {
        this.tgQuyDinh = tgQuyDinh;
    }

    public String getTgTTLuyKe() {
        return tgTTLuyKe;
    }

    public void setTgTTLuyKe(String tgTTLuyKe) {
        this.tgTTLuyKe = tgTTLuyKe;
    }

    public String getTgChenhLech() {
        return tgChenhLech;
    }

    public void setTgChenhLech(String tgChenhLech) {
        this.tgChenhLech = tgChenhLech;
    }

    public String getTonXuLy() {
        return tonXuLy;
    }

    public void setTonXuLy(String tonXuLy) {
        this.tonXuLy = tonXuLy;
    }

    public String getMocTG() {
        return mocTG;
    }

    public void setMocTG(String mocTG) {
        this.mocTG = mocTG;
    }

    public String getMocTonXL() {
        return mocTonXL;
    }

    public void setMocTonXL(String mocTonXL) {
        this.mocTonXL = mocTonXL;
    }

    public String getLoaiCanhBao() {
        return loaiCanhBao;
    }

    public void setLoaiCanhBao(String loaiCanhBao) {
        this.loaiCanhBao = loaiCanhBao;
    }

    public String getNhomDV() {
        return nhomDV;
    }

    public void setNhomDV(String nhomDV) {
        this.nhomDV = nhomDV;
    }
}
