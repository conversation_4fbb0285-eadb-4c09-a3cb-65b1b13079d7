package nocsystem.indexmanager.models.Response.DashBoardChiNhanh;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Getter
@Setter
public class BillDetailThu {
    private String maPhieuGui;
    private LocalDateTime ngayTao;
    private LocalDateTime ngayHenThu;

    public BillDetailThu(String maPhieuGui, Date ngayTao, String ngayHenThu) {

        this.maPhieuGui = maPhieuGui;

        if (ngayHenThu != null) {
            this.ngayTao = LocalDateTime.ofInstant(ngayTao.toInstant(), ZoneId.systemDefault());
        } else {
            this.ngayTao = null;
        }
        if (ngayHenThu == null) {
            this.ngayHenThu = null;
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            this.ngayHenThu = LocalDateTime.parse(ngayHenThu, formatter);
        }
    }

    public BillDetailThu(String maPhieuGui, LocalDateTime ngayTao, LocalDateTime ngayHenThu) {
        this.maPhieuGui = maPhieuGui;
        this.ngayTao = ngayTao;
        this.ngayHenThu = ngayHenThu;
    }

}
