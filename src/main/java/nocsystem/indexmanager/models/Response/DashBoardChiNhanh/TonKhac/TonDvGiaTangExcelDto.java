package nocsystem.indexmanager.models.Response.DashBoardChiNhanh.TonKhac;

import lombok.*;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TonDvGiaTangExcelDto {
    private String ngayBaoCao;
    private String maPhieuGui;
    private String tinhNhan;
    private String huyenNhan;
    private String tenHuyenNhan;
    private String tinhPhat;
    private String huyenPhat;
    private String tenHuyenPhat;
    private String maDvViettel;
    private String maBuuCucGoc;
    private String timeTacDong;
    private String trangThai;
    private String buuCucHT;
    private String chiNhanhHT;
    private String maDvGiaTang;
    private String maDoiTac;
    private String maKHGui;
    private String maBuuCucPhat;
    private String ngayGuiBP;
    private Long tienCOD;
    private String nguoiNhapMay;
    private String ngayNhapMay;
    private Double trongLuong;
    private String loaiHH;
    private Long tienCuoc;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public TonDvGiaTangExcelDto(String ngayBaocao, String maPhieuGui, String tinhNhan, String huyenNhan, String tenHuyenNhan, String tinhPhat, String huyenPhat, String tenHuyenPhat, String maDvViettel, String maBuuCucGoc, String timeTacDong, String trangThai, String buuCucHT, String chiNhanhHT, String maDvGiaTang, String maDoiTac, String maKHGui, String maBuuCucPhat, String ngayGuiBP, Long tienCOD, String nguoiNhapMay, String ngayNhapMay, Double trongLuong, String loaiHH, Long tienCuoc) throws NoSuchFieldException, IllegalAccessException {
        this.ngayBaoCao = ngayBaocao;
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tenHuyenNhan = tenHuyenNhan;
        this.tinhPhat = tinhPhat;
        this.huyenPhat = huyenPhat;
        this.tenHuyenPhat = tenHuyenPhat;
        this.maDvViettel = maDvViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.timeTacDong = timeTacDong;
        this.trangThai = trangThai;
        this.buuCucHT = buuCucHT;
        this.chiNhanhHT = chiNhanhHT;
        this.maDvGiaTang = maDvGiaTang;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.maBuuCucPhat = maBuuCucPhat;
        this.ngayGuiBP = ngayGuiBP;
        this.tienCOD = tienCOD;
        this.nguoiNhapMay = nguoiNhapMay;
        this.ngayNhapMay = ngayNhapMay;
        this.trongLuong = trongLuong;
        this.loaiHH = loaiHH;
        this.tienCuoc = tienCuoc;
        this.objectHashMap = convert(this);
    }
}
