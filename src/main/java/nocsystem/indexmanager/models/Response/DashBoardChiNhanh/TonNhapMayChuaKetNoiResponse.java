package nocsystem.indexmanager.models.Response.DashBoardChiNhanh;

import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TonNhapMayChuaKetNoiResponse {
    LocalDate ngayBaoCao;
    private Long tongSl;
    private Long slTon;
    private Float tlTon;

    public TonNhapMayChuaKetNoiResponse(LocalDate ngayBaoCao, Long tongSl, Long slTon) {
        this.ngayBaoCao = ngayBaoCao;
        this.tongSl = tongSl;
        this.slTon = slTon;
//        this.tlTon = (tongSl == 0 || tongSl == null || slTon == null) ? null : (slTon.floatValue() / tongSl * 100);
    }

    public TonNhapMayChuaKetNoiResponse(LocalDate ngayBaoCao, Long tongSl) {
        this.ngayBaoCao = ngayBaoCao;
        this.tongSl = tongSl;
    }
}
