package nocsystem.indexmanager.models.Response.DashBoardChiNhanh;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BillDetailPhat {
    private String maPhieuGui;
    private LocalDateTime thoiGianPhanCong;
    private LocalDateTime ngayGuiBuuPham;

    public BillDetailPhat(String maPhieuGui, Date thoiGianPhanCong, String ngayguiBP) {
        this.maPhieuGui = maPhieuGui;
        if (thoiGianPhanCong != null) {
            this.thoiGianPhanCong = LocalDateTime.ofInstant(thoiGianPhanCong.toInstant(), ZoneId.systemDefault());
        } else {
            this.thoiGianPhanCong = null;
        }

        if (ngayguiBP == null) {
            this.ngayGuiBuuPham = null;
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            this.ngayGuiBuuPham = LocalDateTime.parse(ngayguiBP, formatter);
        }
    }
}
