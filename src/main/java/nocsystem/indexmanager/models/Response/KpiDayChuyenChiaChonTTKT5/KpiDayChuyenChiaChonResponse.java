package nocsystem.indexmanager.models.Response.KpiDayChuyenChiaChonTTKT5;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
public class KpiDayChuyenChiaChonResponse {
    private Integer stt;
    private String title;
    private String unit;
    private Double kpi;
    private Double result;

    public KpiDayChuyenChiaChonResponse(Integer stt, String title, Double kpi, Double result) {
        this.stt = stt;
        this.title = title;
        setKpi(kpi);
        setResult(result);
        setUnit(stt);
    }

    public void setUnit(Integer stt) {
        if (stt == 1) {
            this.unit = "Phút";
        }
        if (stt == 2 || stt == 3) {
            this.unit = "Tải/kiện";
        }
        if (stt == 4 || stt == 5) {
            this.unit = "Bưu phẩm";
        }
        if (stt == 6 || stt == 7) {
            this.unit = "%";
        }
    }

    public void setKpi(Double kpi) {
        this.kpi = Math.round(kpi * 100.0) / 100.0;
    }

    public void setResult(Double result) {
        this.result = Math.round(result * 100.0) / 100.0;
    }
}
