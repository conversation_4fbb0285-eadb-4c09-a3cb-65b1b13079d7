package nocsystem.indexmanager.models.Response.sanLuongCamKet;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class SanLuongCamKetExcelTemplate {
    private final SXSSFWorkbook workbook;
    private SXSSFSheet sheet;
    List<Map<String, Object>> dataList;
    Map<String, String> mapHeader;
    List<String> headerRow;
    String sheetName;

    public SanLuongCamKetExcelTemplate(
            List<Map<String, Object>> dataList,
            Map<String, String> mapHeader,
            List<String> headerRow,
            String sheetName) {
        this.dataList = dataList;
        this.mapHeader = mapHeader;
        this.headerRow = headerRow;
        this.sheetName = sheetName;
        workbook = new SXSSFWorkbook(1);
    }

    private void createCell(SXSSFRow row, int columnCount, Object value, CellStyle style) {
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else {
            cell.setCellValue((LocalDate) value);
        }
        cell.setCellStyle(style);
    }

    private void createHeaderRowAll() {
        sheet = workbook.createSheet(sheetName);
        workbook.setCompressTempFiles(true);
        SXSSFRow row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        int soCot = 0;
        for (String x : headerRow) {
            createCell(row, soCot, mapHeader.get(x), style);
            soCot++;
        }
    }

    public void writeDataAll() throws IOException {
        int rownum = 1;
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        for (Map<String, Object> x : dataList) {
            SXSSFRow row = sheet.createRow(rownum);
            createCell(row, 0, rownum, style);
            for (int i = 1; i < headerRow.size(); i++) {
                createCell(row, i, x.get(headerRow.get(i)), style);
            }
            rownum++;
        }
    }

    public void exportDataToExcel(HttpServletResponse response) throws IOException {
        createHeaderRowAll();
        writeDataAll();
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.close();
    }
}
