package nocsystem.indexmanager.models.Response.sanLuongCamKet;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;

public class SanLuongCamKetExcel {
    private Integer stt;
    private String maChiNhanh;
    private String cusid;
    private String maKhachHang;
    private String maDichVu;
    private String sanLuongCamKet;
    private Long sanLuongGui;
    private Double doanhThu;
    private Double tlSLCK;
    private Double tlSLCKDuKienThang;
    private String tgCapNhatBangGia;
    private HashMap<String, Object> objectHashMap = new HashMap<>();

    public SanLuongCamKetExcel(Integer stt, String maChiNhanh, String cusid, String maKhachHang, String maDichVu, String sanLuongCamKet, Long sanLuongGui, Double doanhThu, Double tlSLCK, Double tlSLCKDuKienThang, String tgCapNhatBangGia) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.maChiNhanh = maChiNhanh;
        this.cusid = cusid;
        this.maKhachHang = maKhachHang;
        this.maDichVu = maDichVu;
        this.sanLuongCamKet = sanLuongCamKet;
        this.sanLuongGui = sanLuongGui;
        this.doanhThu = doanhThu;
        this.tlSLCK = tlSLCK;
        this.tlSLCKDuKienThang = tlSLCKDuKienThang;
        this.tgCapNhatBangGia = tgCapNhatBangGia;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public Integer getStt() {
        return stt;
    }

    public void setStt(Integer stt) {
        this.stt = stt;
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getCusid() {
        return cusid;
    }

    public void setCusid(String cusid) {
        this.cusid = cusid;
    }

    public String getMaKhachHang() {
        return maKhachHang;
    }

    public void setMaKhachHang(String maKhachHang) {
        this.maKhachHang = maKhachHang;
    }

    public String getMaDichVu() {
        return maDichVu;
    }

    public void setMaDichVu(String maDichVu) {
        this.maDichVu = maDichVu;
    }

    public String getSanLuongCamKet() {
        return sanLuongCamKet;
    }

    public void setSanLuongCamKet(String sanLuongCamKet) {
        this.sanLuongCamKet = sanLuongCamKet;
    }

    public Long getSanLuongGui() {
        return sanLuongGui;
    }

    public void setSanLuongGui(Long sanLuongGui) {
        this.sanLuongGui = sanLuongGui;
    }

    public Double getDoanhThu() {
        return doanhThu;
    }

    public void setDoanhThu(Double doanhThu) {
        this.doanhThu = doanhThu;
    }

    public Double getTlSLCK() {
        return tlSLCK;
    }

    public void setTlSLCK(Double tlSLCK) {
        this.tlSLCK = tlSLCK;
    }

    public Double getTlSLCKDuKienThang() {
        return tlSLCKDuKienThang;
    }

    public void setTlSLCKDuKienThang(Double tlSLCKDuKienThang) {
        this.tlSLCKDuKienThang = tlSLCKDuKienThang;
    }

    public String getTgCapNhatBangGia() {
        return tgCapNhatBangGia;
    }

    public void setTgCapNhatBangGia(String tgCapNhatBangGia) {
        this.tgCapNhatBangGia = tgCapNhatBangGia;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }
}
