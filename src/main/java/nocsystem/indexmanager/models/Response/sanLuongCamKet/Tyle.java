package nocsystem.indexmanager.models.Response.sanLuongCamKet;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Tyle {
    private String maChiNhanh;
    private String cusid;
    private String maDichVu;
    private String tgCapNhatBangGia;
    private Double tlSLCK;
    private Double tlSLCKDuKienThang;

    public Tyle() {
    }

    public Tyle(String cusid, String maDichVu, String tgCapNhatBangGia, Double tlSLCK, Double tlSLCKDuKienThang) {
        this.cusid = cusid;
        this.maDichVu = maDichVu;
        this.tgCapNhatBangGia = tgCapNhatBangGia;
        this.tlSLCK = (double) Math.round(tlSLCK * 100) / 100;
        this.tlSLCKDuKienThang = (double) Math.round(tlSLCKDuKienThang * 100) / 100;
    }
}
