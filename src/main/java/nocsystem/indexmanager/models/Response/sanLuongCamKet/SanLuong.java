package nocsystem.indexmanager.models.Response.sanLuongCamKet;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
public class SanLuong {
    private String maChiNhanh;
    private String cusid;
    private String maKhachHang;
    private String maDichVu;
    private String sanLuongCamKet;
    private Long sanLuongGui;
    private Double doanhThu;

    public SanLuong() {
    }

    public SanLuong(String maChiNhanh, String cusid, String maKhachHang, String maDichVu, String sanLuongCamKet, Long sanLuongGui, Double doanhThu) {
        this.maChiNhanh = maChiNhanh;
        this.cusid = cusid;
        this.maKhachHang = maKhachHang;
        this.maDichVu = maDichVu;
        this.sanLuongCamKet = sanLuongCamKet;
        this.sanLuongGui = sanLuongGui;
        this.doanhThu = doanhThu;
    }
}
