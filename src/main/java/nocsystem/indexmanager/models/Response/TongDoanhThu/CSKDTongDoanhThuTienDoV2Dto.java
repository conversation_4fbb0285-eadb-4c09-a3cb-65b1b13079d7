package nocsystem.indexmanager.models.Response.TongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CSKDTongDoanhThuTienDoV2Dto {
    @JsonProperty("nhom_doanhthu")
    private LocalDate ngayBaoCao;
//    @JsonProperty("ngay_baocao")
//    private LocalDate ngayBaoCao;

//    @JsonProperty("tong_doanhthu")
//    private float tlDoanhThu;
    @JsonProperty("nhom_doanhthu")
    private String nhomDt;

    @JsonProperty("ke_hoach")
    private float keHoach;

    @JsonProperty("thuc_hien")
    private float thucHien;

    @JsonProperty("tlht")
    private float tlHoanThanh;

    @JsonProperty("tiendo")
    private float tienDo;

    @JsonProperty("tt_thang")
    private float ttThang;

    @JsonProperty("tt_tbn_thang")
    private float ttTbnThang;

    @JsonProperty("tt_nam")
    private float ttNam;

    @JsonProperty("tt_tbn_nam")
    private float ttTbnNam;
}
