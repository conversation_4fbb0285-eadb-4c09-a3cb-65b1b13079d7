package nocsystem.indexmanager.models.Response.TongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TongDoanhThuCNV1ResDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ten_chi_nhanh")
    private String tenChiNhanh;

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("tong_doanh_thu")
    private Float tongDoanhThu;

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;

    @JsonProperty("ke_hoach")
    private Float keHoach;

    @JsonProperty("thuc_hien")
    private Float thucHien;
}
