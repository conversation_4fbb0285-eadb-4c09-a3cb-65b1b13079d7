package nocsystem.indexmanager.models.Response.TongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CSKDTongDoanhThuOverViewDto {
    @JsonProperty("tong_doanhthu")
    private Float tongDoanhThu = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam = Float.valueOf(0);

    @JsonProperty("ma_chi_nhanh")
    private String maChiNhanh = "";

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc = "";
}
