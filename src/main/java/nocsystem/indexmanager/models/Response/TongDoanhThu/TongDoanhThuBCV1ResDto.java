package nocsystem.indexmanager.models.Response.TongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TongDoanhThuBCV1ResDto {
    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ten_chi_nhanh")
    private String tenChiNhanh;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("tong_doanh_thu")
    private Float tongDoanhThu = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam = Float.valueOf(0);
}
