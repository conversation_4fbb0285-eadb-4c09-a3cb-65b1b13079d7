package nocsystem.indexmanager.models.Response.TongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TongDoanhThuDetailScreenDto {
    @JsonProperty("ma_chi_nhanh")
    private String maChiNhanh = "";

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc = "";

    @JsonProperty("tong_doanhthu")
    private Float tongDoanhThu = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlHoanThanh = Float.valueOf(0);

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam = Float.valueOf(0);

    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("namtruoc")
    private Float namTruoc = Float.valueOf(0);

    @JsonProperty("thangtruoc")
    private Float thangTruoc = Float.valueOf(0);

    @JsonProperty("cung_ky_ngay")
    private Float cungKyNgay = Float.valueOf(0);

    @JsonProperty("cung_ky_thang")
    private Float cungKyThang = Float.valueOf(0);

    @JsonProperty("cung_ky_nam")
    private Float cungKyNam = Float.valueOf(0);
}