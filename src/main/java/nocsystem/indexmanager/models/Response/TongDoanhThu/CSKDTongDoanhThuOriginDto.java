package nocsystem.indexmanager.models.Response.TongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CSKDTongDoanhThuOriginDto {
    @JsonProperty("tong_doanhthu")
    private Float tlDoanhThu = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam = Float.valueOf(0);

    @JsonProperty("ma_chi_nhanh")
    private String maChiNhanh = "";

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc = "";

    @JsonProperty("tong_DT_ngay")
    private Float tongDTNgay = Float.valueOf(0);

    @JsonProperty("tl_hoan_thanh_ngay")
    private Float tiLeHoanThanhNgay = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay")
    private Float ttCungKyNgay = Float.valueOf(0);

    @JsonProperty("tt_cung_ky_ngay_percent")
    private Float ttCungKyNgayPercent = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay")
    private Float tTTbnNgay = Float.valueOf(0);

    @JsonProperty("tt_tbn_ngay_percent")
    private Float ttTbnNgayPercent = Float.valueOf(0);
}
