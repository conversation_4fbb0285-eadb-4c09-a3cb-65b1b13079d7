package nocsystem.indexmanager.models.Response.SanLuongDuKienDen;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class SanLuongDuKienDenBCPResponse {
    private String donVi;
    private String chiNhanh;
    private String buuCuc;
    private Long tongTrongLuong;
    private Long caSangNgayN;
    private Long caChieuNgayN;
    private Long caToiNgayN;
    private Long tongSLNgayN;
    private Long caSangNgayN1;
    private Long caChieuNgayN1;
    private Long caToiNgayN1;
    private Long tongSLNgayN1;
    private Long caSangNgayN2;
    private Long caChieuNgayN2;
    private Long caToiNgayN2;
    private Long tongSLNgayN2;
    private Long tongSLDuKien;
    private Long thoiGianCapNhat;
}
