package nocsystem.indexmanager.models.Response.ReconciliationGrab;

import lombok.*;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class GrabDoiSoatByMaVanDonDto {
    private String maVanDon;

//    private LocalDateTime ngayNhapMay;
//
//    private LocalDateTime ngayPhatThanhCong;

    private Timestamp ngayNhapMay;

    private Timestamp ngayPhatThanhCong;

    private BigInteger orderId;

    private Double khoangCach;

    private String maChiNhanhGoc;

    private String tenChiNhanhGoc;

    private String maBuuCucGoc;

    private String tenBuuCucGoc;

    private String maKhachHangGoc;

    private String tenKhachHangGoc;

    private String maChiNhanhPhat;

    private String tenChiNhanhPhat;

    private String maBuuCucPhat;

    private String tenBuuCucPhat;

    private String tenKhachHangNhan;

    private String zone;

    private String maDichVuVietTel;

    private BigDecimal trongLuong;

    private Integer vtpTrangThaiCuoiCung;

    private BigDecimal vtpTongTien;

    private BigDecimal vtpCuocChieuDi;

    private BigDecimal vtpPhuPhi;

    private BigDecimal vtpChietKhau;

    private Integer grabTrangThai;

    private BigDecimal grabTongTien;

    private BigDecimal grabCuocChieuDi;

    private BigDecimal grabPhuPhi;

    private BigDecimal grabChieuKhau;

    private BigDecimal chenhLechTongTien;

    private BigDecimal chenhLechCuocChieuDi;

    private BigDecimal chenhLechPhuPhi;

    private BigDecimal chenhLechChietKhau;

    private String ghiChu;

    private Integer fileId;

    public GrabDoiSoatByMaVanDonDto(String maVanDon, Timestamp ngayNhapMay, Timestamp ngayPhatThanhCong, BigInteger orderId, Double khoangCach, String maChiNhanhGoc, String tenChiNhanhGoc, String maBuuCucGoc, String tenBuuCucGoc, String maKhachHangGoc, String tenKhachHangGoc, String maChiNhanhPhat, String tenChiNhanhPhat, String maBuuCucPhat, String tenBuuCucPhat, String tenKhachHangNhan, String zone, String maDichVuVietTel, BigDecimal trongLuong, Integer vtpTrangThaiCuoiCung, BigDecimal vtpTongTien, BigDecimal vtpCuocChieuDi, BigDecimal vtpPhuPhi, BigDecimal vtpChietKhau, Integer grabTrangThai, BigDecimal grabTongTien, BigDecimal grabCuocChieuDi, BigDecimal grabPhuPhi, BigDecimal grabChieuKhau, BigDecimal chenhLechTongTien, BigDecimal chenhLechCuocChieuDi, BigDecimal chenhLechPhuPhi, BigDecimal chenhLechChietKhau, String ghiChu, Integer fileId) {
        this.maVanDon = maVanDon;
        this.ngayNhapMay = ngayNhapMay;
        this.ngayPhatThanhCong = ngayPhatThanhCong;
        this.orderId = orderId;
        this.khoangCach = khoangCach;
        this.maChiNhanhGoc = maChiNhanhGoc;
        this.tenChiNhanhGoc = tenChiNhanhGoc;
        this.maBuuCucGoc = maBuuCucGoc;
        this.tenBuuCucGoc = tenBuuCucGoc;
        this.maKhachHangGoc = maKhachHangGoc;
        this.tenKhachHangGoc = tenKhachHangGoc;
        this.maChiNhanhPhat = maChiNhanhPhat;
        this.tenChiNhanhPhat = tenChiNhanhPhat;
        this.maBuuCucPhat = maBuuCucPhat;
        this.tenBuuCucPhat = tenBuuCucPhat;
        this.tenKhachHangNhan = tenKhachHangNhan;
        this.zone = zone;
        this.maDichVuVietTel = maDichVuVietTel;
        this.trongLuong = trongLuong;
        this.vtpTrangThaiCuoiCung = vtpTrangThaiCuoiCung;
        this.vtpTongTien = vtpTongTien;
        this.vtpCuocChieuDi = vtpCuocChieuDi;
        this.vtpPhuPhi = vtpPhuPhi;
        this.vtpChietKhau = vtpChietKhau;
        this.grabTrangThai = grabTrangThai;
        this.grabTongTien = grabTongTien;
        this.grabCuocChieuDi = grabCuocChieuDi;
        this.grabPhuPhi = grabPhuPhi;
        this.grabChieuKhau = grabChieuKhau;
        this.chenhLechTongTien = chenhLechTongTien;
        this.chenhLechCuocChieuDi = chenhLechCuocChieuDi;
        this.chenhLechPhuPhi = chenhLechPhuPhi;
        this.chenhLechChietKhau = chenhLechChietKhau;
        this.ghiChu = ghiChu;
        this.fileId = fileId;
    }
}
