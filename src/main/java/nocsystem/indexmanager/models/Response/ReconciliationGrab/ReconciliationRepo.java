package nocsystem.indexmanager.models.Response.ReconciliationGrab;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
@Builder
public class ReconciliationRepo implements Serializable {
    @JsonProperty("ma_vandon")
    private String maVanDon;

    @JsonProperty("ngay_nhap_may")
    private Timestamp ngayNhapMay;

    @JsonProperty("ngay_phat_thanhcong")
    private Timestamp ngayPhatThanhCong;

    @JsonProperty("order_id")
    private BigInteger orderId;

    @JsonProperty("khoang_cach")
    private Double khoangCach;

    @JsonProperty("ma_cn_goc")
    private String maChiNhanhGoc;

    @JsonProperty("ten_cn_goc")
    private String tenChiNhanhGoc;

    @JsonProperty("ma_bc_goc")
    private String maBuuCucGoc;

    @JsonProperty("ten_bc_goc")
    private String tenBuuCucGoc;

    @JsonProperty("ma_kh_goc")
    private String maKhachHangGoc;

    @JsonProperty("ten_kh_goc")
    private String tenKhachHangGoc;

    @JsonProperty("ma_cn_phat")
    private String maChiNhanhPhat;

    @JsonProperty("ten_cn_phat")
    private String tenChiNhanhPhat;

    @JsonProperty("ma_bc_phat")
    private String maBuuCucPhat;

    @JsonProperty("ten_bc_phat")
    private String tenBuuCucPhat;

    @JsonProperty("ten_kh_nhan")
    private String tenKhachHangNhan;

    @JsonProperty("zone")
    private String zone;

    @JsonProperty("ma_dichvu_viettel")
    private String maDichVuVietTel;

    @JsonProperty("trong_luong")
    private BigDecimal trongLuong;

    @JsonProperty("vtp_trangthai_cuoicung")
    private Integer vtpTrangThaiCuoiCung;

    @JsonProperty("vtp_tongtien")
    private BigDecimal vtpTongTien;

    @JsonProperty("vtp_cuoc_chieudi")
    private BigDecimal vtpCuocChieuDi;

    @JsonProperty("vtp_phuphi")
    private BigDecimal vtpPhuPhi;

    @JsonProperty("vtp_chietkhau")
    private BigDecimal vtpChietKhau;

    @JsonProperty("grab_trangthai")
    private Integer grabTrangThai;

    @JsonProperty("grab_tongtien")
    private BigDecimal grabTongTien;

    @JsonProperty("grab_cuoc_chieudi")
    private BigDecimal grabCuocChieuDi;

    @JsonProperty("grab_phuphi")
    private BigDecimal grabPhuPhi;

    @JsonProperty("grab_chieukhau")
    private BigDecimal grabChieuKhau;

    @JsonProperty("chenhlech_tongtien")
    private BigDecimal chenhLechTongTien;

    @JsonProperty("chenhlech_cuoc_chieudi")
    private BigDecimal chenhLechCuocChieuDi;

    @JsonProperty("chenhlech_phuphi")
    private BigDecimal chenhLechPhuPhi;

    @JsonProperty("chenhlech_chietkhau")
    private BigDecimal chenhLechChietKhau;

    @JsonProperty("ghi_chu")
    private String ghiChu;
}
