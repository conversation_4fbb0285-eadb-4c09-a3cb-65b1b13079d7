package nocsystem.indexmanager.models.Response.ReconciliationGrab;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
public class TongHopDoiSoatGrapResponse {
//    private LocalDate ngayNhapMay;

//    private String ngayPhatThanhCong;

    private String maChiNhanh;

    private String chiNhanh;

    private String maBuuCuc;

    private String buuCuc;

    private Long vtpSoLuongDon;

    private BigDecimal vtpTongTien;

    private BigDecimal vtpCuocChieuDi;

    private BigDecimal vtpPhuPhi;

    private BigDecimal vtpChietKhau;

    private Long grabSoLuongDon;

    private BigDecimal grabTongTien;

    private BigDecimal grabCuocChieuDi;

    private BigDecimal grabPhuPhi;

    private BigDecimal grabChieuKhau;

    private Long chenhlechSoLuongDon;

    private BigDecimal chenhLechTongTien;

    private BigDecimal chenhLechChietKhau;

    private String ghiChu;

    private Integer fileId;

    public TongHopDoiSoatGrapResponse(String maChiNhanh, String chiNhanh, String maBuuCuc, String buuCuc,
                                      Long vtpSoLuongDon, BigDecimal vtpTongTien, BigDecimal vtpCuocChieuDi, BigDecimal vtpPhuPhi, BigDecimal vtpChietKhau,
                                      Long grabSoLuongDon, BigDecimal grabTongTien, BigDecimal grabCuocChieuDi, BigDecimal grabPhuPhi, BigDecimal grabChieuKhau,
                                      Long chenhlechSoLuongDon, BigDecimal chenhLechTongTien, BigDecimal chenhLechChietKhau
    ) {
        this.maChiNhanh = maChiNhanh;
        this.chiNhanh = chiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.buuCuc = buuCuc;
        this.vtpSoLuongDon = vtpSoLuongDon;
        this.vtpTongTien = vtpTongTien;
        this.vtpCuocChieuDi = vtpCuocChieuDi;
        this.vtpPhuPhi = vtpPhuPhi;
        this.vtpChietKhau = vtpChietKhau;
        this.grabSoLuongDon = grabSoLuongDon;
        this.grabTongTien = grabTongTien;
        this.grabCuocChieuDi = grabCuocChieuDi;
        this.grabPhuPhi = grabPhuPhi;
        this.grabChieuKhau = grabChieuKhau;
        this.chenhlechSoLuongDon = chenhlechSoLuongDon;
        this.chenhLechTongTien = chenhLechTongTien;
        this.chenhLechChietKhau = chenhLechChietKhau;
    }
}
