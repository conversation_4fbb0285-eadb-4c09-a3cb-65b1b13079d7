package nocsystem.indexmanager.models.Response.ReconciliationGrab;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class HistoryImportExcelGrabDto {
    private int id;

    private String fileName;

    private String directory;

    private String createdBy;

    private LocalDateTime createdAt;

    private Short typeUpload;

    private Short dataSync;
}
