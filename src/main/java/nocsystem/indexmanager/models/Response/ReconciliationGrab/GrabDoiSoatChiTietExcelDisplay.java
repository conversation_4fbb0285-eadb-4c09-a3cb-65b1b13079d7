package nocsystem.indexmanager.models.Response.ReconciliationGrab;

import lombok.Getter;
import lombok.Setter;


import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.HashMap;

@Getter
@Setter
public class GrabDoiSoatChiTietExcelDisplay {

    String maVanDon;

    String ngayNhapMay;

    String ngayPhatThanhCong;

    BigInteger orderId;

    Double khoangCach;

    String maChiNhanhGoc;

    String tenChiNhanhGoc;

    String maBuuCucGoc;

    String tenBuuCucGoc;

    String maKhachHangGoc;

    String tenKhachHangGoc;

    String maChiNhanhPhat;

    String tenChiNhanhPhat;

    String maBuuCucPhat;

    String tenBuuCucPhat;

    String tenKhachHangNhan;

    String vung;

    String maDichVuVietTel;

    BigDecimal trongLuong;

    Integer vtpTrangThaiCuoiCung;

    BigDecimal vtpTongTien;

    BigDecimal vtpCuocChieuDi;

    BigDecimal vtpPhuPhi;

    BigDecimal vtpChietKhau;

    Integer grabTrangThai;

    BigDecimal grabTongTien;

    BigDecimal grabCuocChieuDi;

    BigDecimal grabPhuPhi;

    BigDecimal grabChieuKhau;

    BigDecimal chenhLechTongTien;

    BigDecimal chenhLechCuocChieuDi;

    BigDecimal chenhLechPhuPhi;

    BigDecimal chenhLechChietKhau;

    String ghiChu;

    Integer fileId;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {

        HashMap<String, Object> map = new HashMap<>();
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            }  else if (fieldValue instanceof Integer) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof Double) {
                targetField.set(targetObject, Double.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof Float) {
                targetField.set(targetObject, Float.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof Boolean) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof LocalDate) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }

            if (!field.getName().equals("objectHashMap")) {
                map.put(field.getName(), fieldValue);

            }
            i++;
        }
        return map;
    }
//else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
//        targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
//    }
    public GrabDoiSoatChiTietExcelDisplay(String maVanDon, String ngayNhapMay, String ngayPhatThanhCong, BigInteger orderId, Double khoangCach,
                                          String maChiNhanhGoc, String tenChiNhanhGoc, String maBuuCucGoc, String tenBuuCucGoc, String maKhachHangGoc,
                                          String tenKhachHangGoc, String maChiNhanhPhat, String tenChiNhanhPhat, String maBuuCucPhat, String tenBuuCucPhat,
                                          String tenKhachHangNhan, String vung, String maDichVuVietTel, BigDecimal trongLuong, Integer vtpTrangThaiCuoiCung,
                                          BigDecimal vtpTongTien, BigDecimal vtpCuocChieuDi, BigDecimal vtpPhuPhi, BigDecimal vtpChietKhau, Integer grabTrangThai,
                                          BigDecimal grabTongTien, BigDecimal grabCuocChieuDi, BigDecimal grabPhuPhi, BigDecimal grabChieuKhau, BigDecimal chenhLechTongTien,
                                          BigDecimal chenhLechCuocChieuDi, BigDecimal chenhLechPhuPhi, BigDecimal chenhLechChietKhau, String ghiChu, Integer fileId) throws NoSuchFieldException, IllegalAccessException {
        this.maVanDon = maVanDon;
        this.ngayNhapMay = ngayNhapMay;
        this.ngayPhatThanhCong = ngayPhatThanhCong;
        this.orderId = orderId;
        this.khoangCach = khoangCach;
        this.maChiNhanhGoc = maChiNhanhGoc;

        this.tenChiNhanhGoc = tenChiNhanhGoc;
        this.maBuuCucGoc = maBuuCucGoc;
        this.tenBuuCucGoc = tenBuuCucGoc;
        this.maKhachHangGoc = maKhachHangGoc;
        this.tenKhachHangGoc = tenKhachHangGoc;
        this.maChiNhanhPhat = maChiNhanhPhat;
        this.tenChiNhanhPhat = tenChiNhanhPhat;
        this.maBuuCucPhat = maBuuCucPhat;
        this.tenBuuCucPhat = tenBuuCucPhat;
        this.tenKhachHangNhan = tenKhachHangNhan;

        this.vung = vung;
        this.maDichVuVietTel = maDichVuVietTel;
        this.trongLuong = trongLuong;
        this.vtpTrangThaiCuoiCung = vtpTrangThaiCuoiCung;
        this.vtpTongTien = vtpTongTien;

        this.vtpCuocChieuDi = vtpCuocChieuDi;
        this.vtpPhuPhi = vtpPhuPhi;
        this.vtpChietKhau = vtpChietKhau;
        this.grabTrangThai = grabTrangThai;
        this.grabTongTien = grabTongTien;

        this.grabCuocChieuDi = grabCuocChieuDi;
        this.grabPhuPhi = grabPhuPhi;
        this.grabChieuKhau = grabChieuKhau;

        this.chenhLechTongTien = chenhLechTongTien;
        this.chenhLechCuocChieuDi = chenhLechCuocChieuDi;

        this.chenhLechPhuPhi = chenhLechPhuPhi;
        this.chenhLechChietKhau = chenhLechChietKhau;
        this.ghiChu = ghiChu;
        this.fileId = fileId;
        this.objectHashMap = convert(this);
    }

    public GrabDoiSoatChiTietExcelDisplay() {
    }
}
