package nocsystem.indexmanager.models.Response.ReconciliationGrab;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class GrabReconciliationByFileIdDto {
    private LocalDateTime createdAt;

    private String maVanDon;

    private String trangThai;

    private Float tongTien;

    private Float cuocChuyenDi;

    private Float phuPhi;

    private String chietKhau;

    private Integer fileId;
}
