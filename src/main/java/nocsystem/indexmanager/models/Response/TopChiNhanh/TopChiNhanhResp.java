package nocsystem.indexmanager.models.Response.TopChiNhanh;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import nocsystem.indexmanager.models.TopChiNhanh.TopChiNhanhDTO;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TopChiNhanhResp {
    private List<TopChiNhanhDTO> thuThanhCongList;
    private List<TopChiNhanhDTO> thuThanhCongDGList;
    private List<TopChiNhanhDTO> phatThanhCongList;
    private List<TopChiNhanhDTO> phatThanhCongDGList;
    private List<TopChiNhanhDTO> hoanList;
    private List<TopChiNhanhDTO> noiTinhList;
    private List<TopChiNhanhDTO> firstMileList;
    private List<TopChiNhanhDTO> lastMileList;
}
