package nocsystem.indexmanager.models.Response.hieuquaxe;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DanhSachXeChuY {
    private Integer chieu;
    private Double hqxLuyKe;
    private String maChuyenxe;
    private String tenChuyenxe;
    private String biensoXe;
    private Double tongTaiTrongHienTai;  //Tổng tải trong hiện tại trên xe:Tổng khối lượng tính cước của hàng hóa đang tồn trên xe tính đến thời điểm hiện tại
    private Double taitrongXe;

    private String diemHienTaiBc;
    private String diemHienTaiCn;
    private String diemCuoiBc;
    private String diemCuoiCn;

    private Double sokmConLai;
    private String sdtLaixe;
    private String tenLaixe;

}
