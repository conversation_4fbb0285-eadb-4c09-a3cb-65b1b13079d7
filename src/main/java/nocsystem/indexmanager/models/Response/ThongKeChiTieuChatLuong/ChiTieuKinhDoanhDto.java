package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class ChiTieuKinhDoanhDto {
    @JsonProperty("ke_hoach")
    private float keHoach;

    @JsonProperty("tong_doanh_thu_chuyen_phat")
    private float thucHienCP;

    @JsonProperty("tlht_tong_doanh_thu_theo_kh")
    private float tlHoanThanh;

    @JsonProperty("tong_doanh_thu_luy_ke")
    private float tlDoanhThu;

    @JsonProperty("tiendo")
    private float tienDo;

    @JsonProperty("tang_truong_thang")
    private float ttThang;

    @JsonProperty("tt_tbn_thang")
    private float ttTbnThang;

    @JsonProperty("tang_truong_nam")
    private float ttNam;

    @JsonProperty("tt_tbn_nam")
    private float ttTbnNam;
}
