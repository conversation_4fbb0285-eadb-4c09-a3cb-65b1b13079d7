package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class ChiTieuChatLuongDto {
    @JsonProperty("chi_tieu_kinh_doanh")
    private ChiTieuKinhDoanhDto chiTieuKinhDoanh;

    @JsonProperty("top_15_chi_nhanh_cao_nhat")
    private List<Top15ChiNhanhCaoNhatDto> top15ChiNhanhCaoNhatDto;

    @JsonProperty("chi_tieu_giao")
    private ChiTieuGiaoDto chiTieuGiaoDto;

    @JsonProperty("chi_tieu_nhan")
    private ChiTieuNhanDto chiTieuNhanDto;

    @JsonProperty("tl_dung_toan_trinh_ngay")
    private TLDungKPIToanTrinhNgayDto tlDungKPIToanTrinhNgayDto;

    @JsonProperty("tg_toan_trinh_trung_binh_ngay")
    private ThoiGianToanTrinhTBNgayDto thoiGianToanTrinhTBNgayDto;

    @JsonProperty("export_date")
    private String exportDate;

    @JsonProperty("noti_date")
    private String notiDate;
}
