package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class ThoiGianToanTrinhTBNgayDto {
    @JsonProperty("tgtb_toan_trinh_noi_tinh")
    private Float tgtb_toan_trinh_noi_tinh;

    @JsonProperty("tgtb_toan_trinh_noi_tinh_hni_hcm")
    private Float tgtb_toan_trinh_noi_tinh_hni_hcm;

    @JsonProperty("tgtb_toan_trinh_noi_mien")
    private Float tgtb_toan_trinh_noi_mien;

    @JsonProperty("tgtb_lien_mien_bo")
    private Float tgtb_lien_mien_bo;

    @JsonProperty("tgtb_lien_mien_bay")
    private Float tgtb_lien_mien_bay;

    @JsonProperty("tong")
    private Double tong;
}
