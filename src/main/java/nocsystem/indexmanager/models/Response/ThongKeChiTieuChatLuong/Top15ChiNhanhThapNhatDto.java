package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class Top15ChiNhanhThapNhatDto {
//    @JsonProperty("thoiGian")
//    private String thoi_giam;
//
//    @JsonProperty("chiNhanh")
//    private String chi_nhanh;
//
//    @JsonProperty("buuCuc")
//    private String buu_cuc;
//
//    @JsonProperty("kieuXepHang")
//    private Integer kieu_xep_hang;
//
//    @JsonProperty("slAll")
//    private Integer sl_all;
//
//    @JsonProperty("ptc")
//    private Integer ptc;
//
//    @JsonProperty("tlPtc")
//    private Float tl_ptc;
//
//    @JsonProperty("ptcNss")
//    private Integer ptc_nss;
//
//    @JsonProperty("tlPtcNss")
//    private Float tl_ptc_nss;
//
//    @JsonProperty("cl")
//    private Float cl;

    @JsonProperty("chi_nhanh")
    private String chi_nhanh;

    @JsonProperty("buu_cuc")
    private String buu_cuc;

    @JsonProperty("sl_all")
    private Integer sl_all;

    @JsonProperty("ptc")
    private Integer ptc;

    @JsonProperty("tl_ptc")
    private Float tl_ptc;

    @JsonProperty("ptc_ngay_sosanh")
    private int ptc_ngay_sosanh;

    @JsonProperty("tl_ptc_ngay_sosanh")
    private Float tl_ptc_ngay_sosanh;

    @JsonProperty("chenh_lech")
    private Float chenh_lech;
}
