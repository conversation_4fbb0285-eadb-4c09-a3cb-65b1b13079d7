package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class ChiTieuGiaoDto {
    @JsonProperty("tong_san_luong_giao_lk")
    private int tong_sl_giao;

    @JsonProperty("tong_san_luong_giao_thanh_cong_lk")
    private int tong_sl_gtc;

    // TODO chưa biết
    @JsonProperty("tongTon")
    private int san_luong_ton_giao_ngay;

    @JsonProperty("ty_le_giao_thanh_cong_lk")
    private Float lm_ty_le_gtc_tong;

    @JsonProperty("ty_le_giao_thanh_cong_lan_dau_ngay")
    private Double tl_ptc_landau;
}
