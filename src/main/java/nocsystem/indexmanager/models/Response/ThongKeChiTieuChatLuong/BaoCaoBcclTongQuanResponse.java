package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BaoCaoBcclTongQuanResponse {
    @JsonProperty("tl_dung_tt_noi_tinh")
    private int tl_dung_tt_noi_tinh;

    @JsonProperty("tl_dung_tt_noi_mien")
    private int tl_dung_tt_noi_mien;

    @JsonProperty("tl_dung_tt_lien_mien_bo")
    private Float tl_dung_tt_lien_mien_bo;

    @JsonProperty("tl_dung_tt_lien_mien_bay")
    private Float tl_dung_tt_lien_mien_bay;

    @JsonProperty("tl_dung_tt_noi_tinh_hni_hcm")
    private Float tl_dung_tt_noi_tinh_hni_hcm;

    @JsonProperty("tl_dung_tt")
    private Float tl_dung_tt;

    @JsonProperty("tgtb_toan_trinh_noi_tinh")
    private Float tgtb_toan_trinh_noi_tinh;

    @JsonProperty("tgtb_toan_trinh_noi_tinh_hni_hcm")
    private Float tgtb_toan_trinh_noi_tinh_hni_hcm;

    @JsonProperty("tgtb_toan_trinh_noi_mien")
    private Float tgtb_toan_trinh_noi_mien;

    @JsonProperty("tgtb_cham_lien_mien")
    private Float tgtb_cham_lien_mien;

    @JsonProperty("tgtb_nhanh_lien_mien")
    private Float tgtb_nhanh_lien_mien;

    @JsonProperty("tong_san_luong_don_tmdt")
    private long tong_san_luong_don_tmdt;

    @JsonProperty("sl_don_ntc")
    private long sl_don_ntc;

    @JsonProperty("fm_ty_le_ntc")
    private Float fm_ty_le_ntc;

    @JsonProperty("tong_sl_giao")
    private int tong_sl_giao;

    @JsonProperty("tong_sl_gtc")
    private int tong_sl_gtc;

    @JsonProperty("lm_ty_le_gtc_tong")
    private Float lm_ty_le_gtc_tong;
}
