package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class TLDungKPIToanTrinhNgayDto {
    @JsonProperty("tl_dung_tt_noi_tinh")
    private int tl_dung_tt_noi_tinh;

    @JsonProperty("tl_dung_tt_noi_mien")
    private int tl_dung_tt_noi_mien;

    @JsonProperty("tl_dung_tt_lien_mien_bo")
    private Float tl_dung_tt_lien_mien_bo;

    @JsonProperty("tl_dung_tt_lien_mien_bay")
    private Float tl_dung_tt_lien_mien_bay;

//    @JsonProperty("tl_dung_tt")
//    private Float tl_dung_tt;

    @JsonProperty("tl_dung_tt_noi_tinh_hni_hcm")
    private Float tl_dung_tt_noi_tinh_hni_hcm;

    @JsonProperty("tong")
    private Float tong;
}
