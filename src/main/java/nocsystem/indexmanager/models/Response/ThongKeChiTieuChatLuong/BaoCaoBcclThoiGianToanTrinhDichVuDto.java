package nocsystem.indexmanager.models.Response.ThongKeChiTieuChatLuong;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class BaoCaoBcclThoiGianToanTrinhDichVuDto {
    @JsonProperty("tgtb_noi_tinh_hni")
    private Float tgtb_noi_tinh_hni;

    @JsonProperty("tgtb_noi_tinh_hcm")
    private Float tgtb_noi_tinh_hcm;

    @JsonProperty("tgtb_noi_mien")
    private Float tgtb_noi_mien;

    @JsonProperty("tgtb_lien_mien")
    private Float tgtb_lien_mien;
}
