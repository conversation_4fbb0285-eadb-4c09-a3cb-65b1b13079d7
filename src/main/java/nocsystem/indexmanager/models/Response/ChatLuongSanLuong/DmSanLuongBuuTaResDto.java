package nocsystem.indexmanager.models.Response.ChatLuongSanLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto

public class DmSanLuongBuuTaResDto {

    @JsonProperty("ma_nv")
    private String maNhanVien;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("ma_chi_nhanh")
    private String maChiNhanh;

    @JsonProperty("tong_don_giao_tc")
    private Integer tongDonGiaoTC;

    @JsonProperty("tong_don_dang_giao")
    private Integer tongDonDangGiao;

    @JsonProperty("tong_don_delay_giao_hang")
    private Integer tongDonDelayGiaoHang;

    @JsonProperty("tong_don_nhan_tc")
    private Integer tongDonNhanTC;

    @JsonProperty("tong_don_dang_nhan")
    private Integer tongDonDangNhan;

    @JsonProperty("tong_don_delay_nhan")
    private Integer tongDonDelayNhan;

    @JsonProperty("tong_don_huy_nhan")
    private Integer tongDonHuyNhan;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;
}
