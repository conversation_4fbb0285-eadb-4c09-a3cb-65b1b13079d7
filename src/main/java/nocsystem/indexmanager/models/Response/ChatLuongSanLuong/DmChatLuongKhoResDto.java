package nocsystem.indexmanager.models.Response.ChatLuongSanLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class DmChatLuongKhoResDto {

    @JsonProperty("tl_don_lay_nhan_dung_gio")
    private Float tlDonLayNhanDungGio;

    @JsonProperty("tl_don_lay_tc")
    private Float tlDonLayTC;

    @JsonProperty("tl_giao_dung_gio")
    private Float tlGiaoDungGio;

    @JsonProperty("tl_giao_tc")
    private Float tlGiaoTC;

    @JsonProperty("tl_ton_pc_nhan")
    private Float tlTonPC<PERSON>han;

    @JsonProperty("tl_ton_nhan")
    private Float tlTonNhan;

    @JsonProperty("tl_ton_tt_bc_nhan")
    private Float tlTonTTBcNhan;

    @JsonProperty("tl_ton_giao")
    private Float tlTonGiao;

    @JsonProperty("tg_khai_thac_tb_ttkt")
    private Float tgKhaiThacTBTTKT;

    @JsonProperty("tl_khai_thac_kpi_3h_ttkt")
    private Float tlKhaiThacKPI3hTTKT;

    @JsonProperty("ten_tinh")
    private String tenTinh;

    @JsonProperty("ma_tinh")
    private String maTinh;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("ma_buu_ta")
    private String maBuuTa;

    @JsonProperty("tb_chat_luong")
    private Float tbChatLuong;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;
}
