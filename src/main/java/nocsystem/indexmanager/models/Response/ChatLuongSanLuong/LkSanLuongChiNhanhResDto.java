package nocsystem.indexmanager.models.Response.ChatLuongSanLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public
class LkSanLuongChiNhanhResDto {
    @JsonProperty("update_at")
    private LocalDate updateAt;

    @JsonProperty("sl_don_lay_nhan_tc")
    private Long slDonLayNhanTC;

    @JsonProperty("sl_don_da_giao_tc")
    private Long slDonDaGiaoTC;

    @JsonProperty("sl_don_ton_phan_cong_nhan")
    private Long slDonTonPhanCongNhan;

    @JsonProperty("sl_don_lay_ton_nhan")
    private Long slDonLayTonNhan;

    @JsonProperty("sl_don_ton_xuat_tt_hub_sub_bc")
    private Long slDonTonXuatTTHubSubBC;

    @JsonProperty("sl_don_ton_xuat_tt_ttkt")
    private Long slDonTonXuatTTTTKT;

    @JsonProperty("ma_chi_nhanh")
    private String maChiNhanh;

    @JsonProperty("ten_tinh")
    private String tenTinh;

}
