package nocsystem.indexmanager.models.Response.ChatLuongSanLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto

public class DmChatLuongBuuTaResDto {


    @JsonProperty("ma_nv")
    private String maNhanVien;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("ma_chi_nhanh")
    private String maChiNhanh;

    @JsonProperty("tl_nhan_tc")
    private Float tlNhanTC;

    @JsonProperty("tl_nhan_dg")
    private Float tlNhanDG;

    @JsonProperty("tl_ton_lay")
    private Float tlTonLay;

    @JsonProperty("tl_giao_tc")
    private Float tlGiaoTC;

    @JsonProperty("tl_giao_tc_dg")
    private Float tlGiaoTDG;

    @JsonProperty("tb_chat_luong")
    private Float tbChatLuong;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;
}
