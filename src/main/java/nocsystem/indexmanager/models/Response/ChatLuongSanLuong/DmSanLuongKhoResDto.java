package nocsystem.indexmanager.models.Response.ChatLuongSanLuong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;
import java.time.ZonedDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class DmSanLuongKhoResDto {

    @JsonProperty("sl_don_lay_nhan_tc")
    private Long slDonLayNhanTC;

    @JsonProperty("sl_don_da_phan_cong_giao")
    private Long slDonDaPhanCongGiao;

    @JsonProperty("sl_don_da_giao_tc")
    private Long slDonDaGiaoTC;

    @JsonProperty("sl_don_ton_phan_cong_nhan")
    private Long slDonTonPhanCongNhan;

    @JsonProperty("sl_don_lay_ton_nhan")
    private Long slDonLayTonNhan;

    @JsonProperty("sl_don_ton_xuat_tt_hub_sub_bc")
    private Long slDonTonXuatTTHubSubBC;

    @JsonProperty("sl_don_ton_xuat_tt_ttkt")
    private Long slDonTonXuatTTTTKT;

    @JsonProperty("sl_don_ton_giao")
    private Long slDonTonGiao;

    @JsonProperty("ten_tinh")
    private String tenTinh;

    @JsonProperty("ma_tinh")
    private String maTinh;

    @JsonProperty("ma_buu_cuc")
    private String maBuuCuc;

    @JsonProperty("ma_buu_ta")
    private String maBuuTa;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;
}
