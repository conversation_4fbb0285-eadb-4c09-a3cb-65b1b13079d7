package nocsystem.indexmanager.models.Response.DieuHanhTon;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

public class DieuHanhTonThuExcelDetail {
    private Integer stt;
    private String maPhieuGui;
    private String maTrangThai;
    private String maDoiTac;
    private String thoiGianTaoDon;
    private String timeKhachHenLay;
    private String tinhKHGui;
    private String maKHGui;
    private String tinhPhat;
    private String maBuuCucGoc;
    private String chiNhanh;
    private String maDVViettel;
    private Double tongCuoc;
    private String buuTaNhan;
    private String tenBuuTa;
    private String ngayTon;
    private Double gioTon;
    private String thoiGian;

    private String danhGiaDon;
    private float thoiGianTon;
    private HashMap<String, Object> objectHashMap = new HashMap<>();

    public DieuHanhTonThuExcelDetail(Integer stt, String maPhieuGui, String maTrangThai, String maDoiTac, String thoiGianTaoDon, String timeKhachHenLay, String tinhKHGui, String maKHGui, String tinhPhat, String chiNhanh, String maDVViettel, Double tongCuoc, String ngayTon, Double gioTon, LocalDate thoiGian) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.maPhieuGui = maPhieuGui;
        this.maTrangThai = maTrangThai;
        this.maDoiTac = maDoiTac;
        this.thoiGianTaoDon = thoiGianTaoDon;
        this.timeKhachHenLay = timeKhachHenLay;
        this.tinhKHGui = tinhKHGui;
        this.maKHGui = maKHGui;
        this.tinhPhat = tinhPhat;
        this.chiNhanh = chiNhanh;
        this.maDVViettel = maDVViettel;
        this.tongCuoc = tongCuoc;
        this.ngayTon = ngayTon;
        this.gioTon = gioTon;
        this.thoiGian = thoiGian.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        this.objectHashMap = convert(this);
    }

    public DieuHanhTonThuExcelDetail(Integer stt, String maPhieuGui, String maTrangThai, String maDoiTac, String thoiGianTaoDon, String timeKhachHenLay, String tinhKHGui, String maKHGui, String tinhPhat, String maBuuCucGoc, String chiNhanh, String maDVViettel, Double tongCuoc, String ngayTon, Double gioTon, LocalDate thoiGian,String danhGiaDon, float thoiGianTon) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.maPhieuGui = maPhieuGui;
        this.maTrangThai = maTrangThai;
        this.maDoiTac = maDoiTac;
        this.thoiGianTaoDon = thoiGianTaoDon;
        this.timeKhachHenLay = timeKhachHenLay;
        this.tinhKHGui = tinhKHGui;
        this.maKHGui = maKHGui;
        this.tinhPhat = tinhPhat;
        this.maBuuCucGoc = maBuuCucGoc;
        this.chiNhanh = chiNhanh;
        this.maDVViettel = maDVViettel;
        this.tongCuoc = tongCuoc;
        this.ngayTon = ngayTon;
        this.gioTon = gioTon;
        this.thoiGian = thoiGian.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        this.danhGiaDon = danhGiaDon;
        this.thoiGianTon = thoiGianTon;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public Integer getStt() {
        return stt;
    }

    public void setStt(Integer stt) {
        this.stt = stt;
    }

    public String getMaPhieuGui() {
        return maPhieuGui;
    }

    public void setMaPhieuGui(String maPhieuGui) {
        this.maPhieuGui = maPhieuGui;
    }

    public String getMaTrangThai() {
        return maTrangThai;
    }

    public void setMaTrangThai(String maTrangThai) {
        this.maTrangThai = maTrangThai;
    }

    public String getMaDoiTac() {
        return maDoiTac;
    }

    public void setMaDoiTac(String maDoiTac) {
        this.maDoiTac = maDoiTac;
    }

    public String getThoiGianTaoDon() {
        return thoiGianTaoDon;
    }

    public void setThoiGianTaoDon(String thoiGianTaoDon) {
        this.thoiGianTaoDon = thoiGianTaoDon;
    }

    public String getTimeKhachHenLay() {
        return timeKhachHenLay;
    }

    public void setTimeKhachHenLay(String timeKhachHenLay) {
        this.timeKhachHenLay = timeKhachHenLay;
    }

    public String getTinhKHGui() {
        return tinhKHGui;
    }

    public void setTinhKHGui(String tinhKHGui) {
        this.tinhKHGui = tinhKHGui;
    }

    public String getChiNhanh() {
        return chiNhanh;
    }

    public void setChiNhanh(String chiNhanh) {
        this.chiNhanh = chiNhanh;
    }

    public String getMaKHGui() {
        return maKHGui;
    }

    public void setMaKHGui(String maKHGui) {
        this.maKHGui = maKHGui;
    }

    public String getTinhPhat() {
        return tinhPhat;
    }

    public void setTinhPhat(String tinhPhat) {
        this.tinhPhat = tinhPhat;
    }

    public String getMaBuuCucGoc() {
        return maBuuCucGoc;
    }

    public void setMaBuuCucGoc(String maBuuCucGoc) {
        this.maBuuCucGoc = maBuuCucGoc;
    }

    public String getMaDVViettel() {
        return maDVViettel;
    }

    public void setMaDVViettel(String maDVViettel) {
        this.maDVViettel = maDVViettel;
    }

    public Double getTongCuoc() {
        return tongCuoc;
    }

    public void setTongCuoc(Double tongCuoc) {
        this.tongCuoc = tongCuoc;
    }

    public String getBuuTaNhan() {
        return buuTaNhan;
    }

    public void setBuuTaNhan(String buuTaNhan) {
        this.buuTaNhan = buuTaNhan;
    }

    public String getTenBuuTa() {
        return tenBuuTa;
    }

    public void setTenBuuTa(String tenBuuTa) {
        this.tenBuuTa = tenBuuTa;
    }

    public String getNgayTon() {
        return ngayTon;
    }

    public void setNgayTon(String ngayTon) {
        this.ngayTon = ngayTon;
    }

    public String getThoiGian() {
        return thoiGian;
    }

    public void setThoiGian(String thoiGian) {
        this.thoiGian = thoiGian;
    }

    public String getDanhGiaDon() {
        return danhGiaDon;
    }

    public void setDanhGiaDon(String danhGiaDon) {
        this.danhGiaDon = danhGiaDon;
    }

    public float getThoiGianTon() {
        return thoiGianTon;
    }

    public void setThoiGianTon(float thoiGianTon) {
        this.thoiGianTon = thoiGianTon;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }
}
