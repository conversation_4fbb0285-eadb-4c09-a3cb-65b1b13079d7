package nocsystem.indexmanager.models.Response.DieuHanhTon;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DieuHanhTonThuResponse {
    private String chiNhanh;
    private String buuCuc;
    private String tuyenBuuTa;
    private Long thuTh1d;
    private Long thuTh2d;
    private Long thuTh3d;
    private Long thuThl3d;
    private Long thuThTong;
    private Long thuDh6h;
    private Long thuDh12h;
    private Long thuDh18h;
    private Long thuDh24h;
    private Long thuDhTong;
    private Long thuQh1d;
    private Long thuQh2d;
    private Long thuQh3d;
    private Long thuQh4d;
    private Long thuQh5d;
    private Long thuQhl5d;
    private Long thuQhTong;

    public DieuHanhTonThuResponse() {
    }

    public DieuHanhTonThuResponse(String chiNhanh, String buuCuc, String tuyenBuuTa, Long thuTh1d, Long thuTh2d, Long thuTh3d, <PERSON> thuThl3d, <PERSON> thuThTong, <PERSON> thuDh6h, <PERSON> thuDh12h, <PERSON> thuDh18h, <PERSON> thuDh24h, <PERSON> thuDhTong, <PERSON> thuQh1d, Long thuQh2d, Long thuQh3d, Long thuQh4d, Long thuQh5d, Long thuQhl5d, Long thuQhTong) {
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.tuyenBuuTa = tuyenBuuTa;
        this.thuTh1d = thuTh1d;
        this.thuTh2d = thuTh2d;
        this.thuTh3d = thuTh3d;
        this.thuThl3d = thuThl3d;
        this.thuThTong = thuThTong;
        this.thuDh6h = thuDh6h;
        this.thuDh12h = thuDh12h;
        this.thuDh18h = thuDh18h;
        this.thuDh24h = thuDh24h;
        this.thuDhTong = thuDhTong;
        this.thuQh1d = thuQh1d;
        this.thuQh2d = thuQh2d;
        this.thuQh3d = thuQh3d;
        this.thuQh4d = thuQh4d;
        this.thuQh5d = thuQh5d;
        this.thuQhl5d = thuQhl5d;
        this.thuQhTong = thuQhTong;
    }

    public DieuHanhTonThuResponse(String chiNhanh, String buuCuc, Long thuTh1d, Long thuTh2d, Long thuTh3d, Long thuThl3d, Long thuThTong, Long thuDh6h, Long thuDh12h, Long thuDh18h, Long thuDh24h, Long thuDhTong, Long thuQh1d, Long thuQh2d, Long thuQh3d, Long thuQh4d, Long thuQh5d, Long thuQhl5d, Long thuQhTong) {
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.thuTh1d = thuTh1d;
        this.thuTh2d = thuTh2d;
        this.thuTh3d = thuTh3d;
        this.thuThl3d = thuThl3d;
        this.thuThTong = thuThTong;
        this.thuDh6h = thuDh6h;
        this.thuDh12h = thuDh12h;
        this.thuDh18h = thuDh18h;
        this.thuDh24h = thuDh24h;
        this.thuDhTong = thuDhTong;
        this.thuQh1d = thuQh1d;
        this.thuQh2d = thuQh2d;
        this.thuQh3d = thuQh3d;
        this.thuQh4d = thuQh4d;
        this.thuQh5d = thuQh5d;
        this.thuQhl5d = thuQhl5d;
        this.thuQhTong = thuQhTong;
    }

    public DieuHanhTonThuResponse(String chiNhanh, Long thuTh1d, Long thuTh2d, Long thuTh3d, Long thuThl3d, Long thuThTong, Long thuDh6h, Long thuDh12h, Long thuDh18h, Long thuDh24h, Long thuDhTong, Long thuQh1d, Long thuQh2d, Long thuQh3d, Long thuQh4d, Long thuQh5d, Long thuQhl5d, Long thuQhTong) {
        this.chiNhanh = chiNhanh;
        this.thuTh1d = thuTh1d;
        this.thuTh2d = thuTh2d;
        this.thuTh3d = thuTh3d;
        this.thuThl3d = thuThl3d;
        this.thuThTong = thuThTong;
        this.thuDh6h = thuDh6h;
        this.thuDh12h = thuDh12h;
        this.thuDh18h = thuDh18h;
        this.thuDh24h = thuDh24h;
        this.thuDhTong = thuDhTong;
        this.thuQh1d = thuQh1d;
        this.thuQh2d = thuQh2d;
        this.thuQh3d = thuQh3d;
        this.thuQh4d = thuQh4d;
        this.thuQh5d = thuQh5d;
        this.thuQhl5d = thuQhl5d;
        this.thuQhTong = thuQhTong;
    }

    public DieuHanhTonThuResponse(Long thuTh1d, Long thuTh2d, Long thuTh3d, Long thuThl3d, Long thuThTong, Long thuDh6h, Long thuDh12h, Long thuDh18h, Long thuDh24h, Long thuDhTong, Long thuQh1d, Long thuQh2d, Long thuQh3d, Long thuQh4d, Long thuQh5d, Long thuQhl5d, Long thuQhTong) {
        this.thuTh1d = thuTh1d;
        this.thuTh2d = thuTh2d;
        this.thuTh3d = thuTh3d;
        this.thuThl3d = thuThl3d;
        this.thuThTong = thuThTong;
        this.thuDh6h = thuDh6h;
        this.thuDh12h = thuDh12h;
        this.thuDh18h = thuDh18h;
        this.thuDh24h = thuDh24h;
        this.thuDhTong = thuDhTong;
        this.thuQh1d = thuQh1d;
        this.thuQh2d = thuQh2d;
        this.thuQh3d = thuQh3d;
        this.thuQh4d = thuQh4d;
        this.thuQh5d = thuQh5d;
        this.thuQhl5d = thuQhl5d;
        this.thuQhTong = thuQhTong;
    }
}
