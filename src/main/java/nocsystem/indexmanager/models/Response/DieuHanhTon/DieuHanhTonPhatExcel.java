package nocsystem.indexmanager.models.Response.DieuHanhTon;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;

public class DieuHanhTonPhatExcel {
    private Integer stt;
    private String chiNhanh;
    private String buuCuc;
    private String tuyenBuuTa;
    private Long phatTh1d;
    private Long phatTh2d;
    private Long phatTh3d;
    private Long phatThl3d;
    private Long phatThTong;
    private Long phatDh6h;
    private Long phatDh12h;
    private Long phatDh18h;
    private Long phatDh24h;
    private Long phatDhTong;
    private Long phatQh1d;
    private Long phatQh2d;
    private Long phatQh3d;
    private Long phatQh4d;
    private Long phatQh5d;
    private Long phatQh6d;
    private Long phatQh7d;
    private Long phatQhl7d;
    private Long phatQhTong;
    private HashMap<String, Object> objectHashMap = new HashMap<>();

    public DieuHanhTonPhatExcel(Integer stt, String chiNhanh, Long phatTh1d, Long phatTh2d, <PERSON> phatTh3d, <PERSON> phatThl3d, <PERSON> phatThTong, Long phatDh6h, Long phatDh12h, Long phatDh18h, Long phatDh24h, Long phatDhTong, Long phatQh1d, Long phatQh2d, Long phatQh3d, Long phatQh4d, Long phatQh5d, Long phatQh6d, Long phatQh7d, Long phatQhl7d, Long phatQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.phatTh1d = phatTh1d;
        this.phatTh2d = phatTh2d;
        this.phatTh3d = phatTh3d;
        this.phatThl3d = phatThl3d;
        this.phatThTong = phatThTong;
        this.phatDh6h = phatDh6h;
        this.phatDh12h = phatDh12h;
        this.phatDh18h = phatDh18h;
        this.phatDh24h = phatDh24h;
        this.phatDhTong = phatDhTong;
        this.phatQh1d = phatQh1d;
        this.phatQh2d = phatQh2d;
        this.phatQh3d = phatQh3d;
        this.phatQh4d = phatQh4d;
        this.phatQh5d = phatQh5d;
        this.phatQh6d = phatQh6d;
        this.phatQh7d = phatQh7d;
        this.phatQhl7d = phatQhl7d;
        this.phatQhTong = phatQhTong;
        this.objectHashMap = convert(this);;
    }

    public DieuHanhTonPhatExcel(Integer stt, String chiNhanh, String buuCuc, Long phatTh1d, Long phatTh2d, Long phatTh3d, Long phatThl3d, Long phatThTong, Long phatDh6h, Long phatDh12h, Long phatDh18h, Long phatDh24h, Long phatDhTong, Long phatQh1d, Long phatQh2d, Long phatQh3d, Long phatQh4d, Long phatQh5d, Long phatQh6d, Long phatQh7d, Long phatQhl7d, Long phatQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.phatTh1d = phatTh1d;
        this.phatTh2d = phatTh2d;
        this.phatTh3d = phatTh3d;
        this.phatThl3d = phatThl3d;
        this.phatThTong = phatThTong;
        this.phatDh6h = phatDh6h;
        this.phatDh12h = phatDh12h;
        this.phatDh18h = phatDh18h;
        this.phatDh24h = phatDh24h;
        this.phatDhTong = phatDhTong;
        this.phatQh1d = phatQh1d;
        this.phatQh2d = phatQh2d;
        this.phatQh3d = phatQh3d;
        this.phatQh4d = phatQh4d;
        this.phatQh5d = phatQh5d;
        this.phatQh6d = phatQh6d;
        this.phatQh7d = phatQh7d;
        this.phatQhl7d = phatQhl7d;
        this.phatQhTong = phatQhTong;
        this.objectHashMap = convert(this);;
    }

    public DieuHanhTonPhatExcel(Integer stt, String chiNhanh, String buuCuc, String tuyenBuuTa, Long phatTh1d, Long phatTh2d, Long phatTh3d, Long phatThl3d, Long phatThTong, Long phatDh6h, Long phatDh12h, Long phatDh18h, Long phatDh24h, Long phatDhTong, Long phatQh1d, Long phatQh2d, Long phatQh3d, Long phatQh4d, Long phatQh5d, Long phatQh6d, Long phatQh7d, Long phatQhl7d, Long phatQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.tuyenBuuTa = tuyenBuuTa;
        this.phatTh1d = phatTh1d;
        this.phatTh2d = phatTh2d;
        this.phatTh3d = phatTh3d;
        this.phatThl3d = phatThl3d;
        this.phatThTong = phatThTong;
        this.phatDh6h = phatDh6h;
        this.phatDh12h = phatDh12h;
        this.phatDh18h = phatDh18h;
        this.phatDh24h = phatDh24h;
        this.phatDhTong = phatDhTong;
        this.phatQh1d = phatQh1d;
        this.phatQh2d = phatQh2d;
        this.phatQh3d = phatQh3d;
        this.phatQh4d = phatQh4d;
        this.phatQh5d = phatQh5d;
        this.phatQh6d = phatQh6d;
        this.phatQh7d = phatQh7d;
        this.phatQhl7d = phatQhl7d;
        this.phatQhTong = phatQhTong;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public Integer getStt() {
        return stt;
    }

    public void setStt(Integer stt) {
        this.stt = stt;
    }

    public String getChiNhanh() {
        return chiNhanh;
    }

    public void setChiNhanh(String chiNhanh) {
        this.chiNhanh = chiNhanh;
    }

    public String getBuuCuc() {
        return buuCuc;
    }

    public void setBuuCuc(String buuCuc) {
        this.buuCuc = buuCuc;
    }

    public String getTuyenBuuTa() {
        return tuyenBuuTa;
    }

    public void setTuyenBuuTa(String tuyenBuuTa) {
        this.tuyenBuuTa = tuyenBuuTa;
    }

    public Long getPhatTh1d() {
        return phatTh1d;
    }

    public void setPhatTh1d(Long phatTh1d) {
        this.phatTh1d = phatTh1d;
    }

    public Long getPhatTh2d() {
        return phatTh2d;
    }

    public void setPhatTh2d(Long phatTh2d) {
        this.phatTh2d = phatTh2d;
    }

    public Long getPhatTh3d() {
        return phatTh3d;
    }

    public void setPhatTh3d(Long phatTh3d) {
        this.phatTh3d = phatTh3d;
    }

    public Long getPhatThl3d() {
        return phatThl3d;
    }

    public void setPhatThl3d(Long phatThl3d) {
        this.phatThl3d = phatThl3d;
    }

    public Long getPhatThTong() {
        return phatThTong;
    }

    public void setPhatThTong(Long phatThTong) {
        this.phatThTong = phatThTong;
    }

    public Long getPhatDh6h() {
        return phatDh6h;
    }

    public void setPhatDh6h(Long phatDh6h) {
        this.phatDh6h = phatDh6h;
    }

    public Long getPhatDh12h() {
        return phatDh12h;
    }

    public void setPhatDh12h(Long phatDh12h) {
        this.phatDh12h = phatDh12h;
    }

    public Long getPhatDh18h() {
        return phatDh18h;
    }

    public void setPhatDh18h(Long phatDh18h) {
        this.phatDh18h = phatDh18h;
    }

    public Long getPhatDh24h() {
        return phatDh24h;
    }

    public void setPhatDh24h(Long phatDh24h) {
        this.phatDh24h = phatDh24h;
    }

    public Long getPhatDhTong() {
        return phatDhTong;
    }

    public void setPhatDhTong(Long phatDhTong) {
        this.phatDhTong = phatDhTong;
    }

    public Long getPhatQh1d() {
        return phatQh1d;
    }

    public void setPhatQh1d(Long phatQh1d) {
        this.phatQh1d = phatQh1d;
    }

    public Long getPhatQh2d() {
        return phatQh2d;
    }

    public void setPhatQh2d(Long phatQh2d) {
        this.phatQh2d = phatQh2d;
    }

    public Long getPhatQh3d() {
        return phatQh3d;
    }

    public void setPhatQh3d(Long phatQh3d) {
        this.phatQh3d = phatQh3d;
    }

    public Long getPhatQh4d() {
        return phatQh4d;
    }

    public void setPhatQh4d(Long phatQh4d) {
        this.phatQh4d = phatQh4d;
    }

    public Long getPhatQh5d() {
        return phatQh5d;
    }

    public void setPhatQh5d(Long phatQh5d) {
        this.phatQh5d = phatQh5d;
    }

    public Long getPhatQh6d() {
        return phatQh6d;
    }

    public void setPhatQh6d(Long phatQh6d) {
        this.phatQh6d = phatQh6d;
    }

    public Long getPhatQh7d() {
        return phatQh7d;
    }

    public void setPhatQh7d(Long phatQh7d) {
        this.phatQh7d = phatQh7d;
    }

    public Long getPhatQhl7d() {
        return phatQhl7d;
    }

    public void setPhatQhl7d(Long phatQhl7d) {
        this.phatQhl7d = phatQhl7d;
    }

    public Long getPhatQhTong() {
        return phatQhTong;
    }

    public void setPhatQhTong(Long phatQhTong) {
        this.phatQhTong = phatQhTong;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }
}
