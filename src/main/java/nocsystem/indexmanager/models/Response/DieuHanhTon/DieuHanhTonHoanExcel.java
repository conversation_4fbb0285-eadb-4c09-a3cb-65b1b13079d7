package nocsystem.indexmanager.models.Response.DieuHanhTon;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;

public class DieuHanhTonHoanExcel {
    private Integer stt;
    private String chiNhanh;
    private String buuCuc;
    private String tuyenBuuTa;
    private Long hoanTh1d;
    private Long hoanTh2d;
    private <PERSON> hoanTh3d;
    private Long hoanThl3d;
    private Long hoanThTong;
    private Long hoanDh6h;
    private Long hoanDh12h;
    private Long hoanDh18h;
    private Long hoanDh24h;
    private Long hoanDhTong;
    private Long hoanQh1d;
    private Long hoanQh2d;
    private Long hoanQh3d;
    private Long hoanQh4d;
    private <PERSON> hoanQh5d;
    private <PERSON> hoanQh6d;
    private Long hoanQh7d;
    private Long hoanQhl7d;
    private Long hoanQhTong;
    private HashMap<String, Object> objectHashMap = new HashMap<>();

    public DieuHanhTonHoanExcel(Integer stt, String chiNhanh, <PERSON> hoanTh1d, <PERSON> hoanTh2d, <PERSON> hoanTh3d, <PERSON> hoanThl3d, <PERSON> hoanTh<PERSON>ong, Long hoanDh6h, Long hoanDh12h, <PERSON> hoanDh18h, Long hoanDh24h, Long hoanDhTong, Long hoanQh1d, Long hoanQh2d, Long hoanQh3d, Long hoanQh4d, Long hoanQh5d, Long hoanQh6d, Long hoanQh7d, Long hoanQhl7d, Long hoanQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.hoanTh1d = hoanTh1d;
        this.hoanTh2d = hoanTh2d;
        this.hoanTh3d = hoanTh3d;
        this.hoanThl3d = hoanThl3d;
        this.hoanThTong = hoanThTong;
        this.hoanDh6h = hoanDh6h;
        this.hoanDh12h = hoanDh12h;
        this.hoanDh18h = hoanDh18h;
        this.hoanDh24h = hoanDh24h;
        this.hoanDhTong = hoanDhTong;
        this.hoanQh1d = hoanQh1d;
        this.hoanQh2d = hoanQh2d;
        this.hoanQh3d = hoanQh3d;
        this.hoanQh4d = hoanQh4d;
        this.hoanQh5d = hoanQh5d;
        this.hoanQh6d = hoanQh6d;
        this.hoanQh7d = hoanQh7d;
        this.hoanQhl7d = hoanQhl7d;
        this.hoanQhTong = hoanQhTong;
        this.objectHashMap = convert(this);
    }

    public DieuHanhTonHoanExcel(Integer stt, String chiNhanh, String buuCuc, Long hoanTh1d, Long hoanTh2d, Long hoanTh3d, Long hoanThl3d, Long hoanThTong, Long hoanDh6h, Long hoanDh12h, Long hoanDh18h, Long hoanDh24h, Long hoanDhTong, Long hoanQh1d, Long hoanQh2d, Long hoanQh3d, Long hoanQh4d, Long hoanQh5d, Long hoanQh6d, Long hoanQh7d, Long hoanQhl7d, Long hoanQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.hoanTh1d = hoanTh1d;
        this.hoanTh2d = hoanTh2d;
        this.hoanTh3d = hoanTh3d;
        this.hoanThl3d = hoanThl3d;
        this.hoanThTong = hoanThTong;
        this.hoanDh6h = hoanDh6h;
        this.hoanDh12h = hoanDh12h;
        this.hoanDh18h = hoanDh18h;
        this.hoanDh24h = hoanDh24h;
        this.hoanDhTong = hoanDhTong;
        this.hoanQh1d = hoanQh1d;
        this.hoanQh2d = hoanQh2d;
        this.hoanQh3d = hoanQh3d;
        this.hoanQh4d = hoanQh4d;
        this.hoanQh5d = hoanQh5d;
        this.hoanQh6d = hoanQh6d;
        this.hoanQh7d = hoanQh7d;
        this.hoanQhl7d = hoanQhl7d;
        this.hoanQhTong = hoanQhTong;
        this.objectHashMap = convert(this);
    }

    public DieuHanhTonHoanExcel(Integer stt, String chiNhanh, String buuCuc, String tuyenBuuTa, Long hoanTh1d, Long hoanTh2d, Long hoanTh3d, Long hoanThl3d, Long hoanThTong, Long hoanDh6h, Long hoanDh12h, Long hoanDh18h, Long hoanDh24h, Long hoanDhTong, Long hoanQh1d, Long hoanQh2d, Long hoanQh3d, Long hoanQh4d, Long hoanQh5d, Long hoanQh6d, Long hoanQh7d, Long hoanQhl7d, Long hoanQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.tuyenBuuTa = tuyenBuuTa;
        this.hoanTh1d = hoanTh1d;
        this.hoanTh2d = hoanTh2d;
        this.hoanTh3d = hoanTh3d;
        this.hoanThl3d = hoanThl3d;
        this.hoanThTong = hoanThTong;
        this.hoanDh6h = hoanDh6h;
        this.hoanDh12h = hoanDh12h;
        this.hoanDh18h = hoanDh18h;
        this.hoanDh24h = hoanDh24h;
        this.hoanDhTong = hoanDhTong;
        this.hoanQh1d = hoanQh1d;
        this.hoanQh2d = hoanQh2d;
        this.hoanQh3d = hoanQh3d;
        this.hoanQh4d = hoanQh4d;
        this.hoanQh5d = hoanQh5d;
        this.hoanQh6d = hoanQh6d;
        this.hoanQh7d = hoanQh7d;
        this.hoanQhl7d = hoanQhl7d;
        this.hoanQhTong = hoanQhTong;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public Integer getStt() {
        return stt;
    }

    public void setStt(Integer stt) {
        this.stt = stt;
    }

    public String getChiNhanh() {
        return chiNhanh;
    }

    public void setChiNhanh(String chiNhanh) {
        this.chiNhanh = chiNhanh;
    }

    public String getBuuCuc() {
        return buuCuc;
    }

    public void setBuuCuc(String buuCuc) {
        this.buuCuc = buuCuc;
    }

    public String getTuyenBuuTa() {
        return tuyenBuuTa;
    }

    public void setTuyenBuuTa(String tuyenBuuTa) {
        this.tuyenBuuTa = tuyenBuuTa;
    }

    public Long getHoanTh1d() {
        return hoanTh1d;
    }

    public void setHoanTh1d(Long hoanTh1d) {
        this.hoanTh1d = hoanTh1d;
    }

    public Long getHoanTh2d() {
        return hoanTh2d;
    }

    public void setHoanTh2d(Long hoanTh2d) {
        this.hoanTh2d = hoanTh2d;
    }

    public Long getHoanTh3d() {
        return hoanTh3d;
    }

    public void setHoanTh3d(Long hoanTh3d) {
        this.hoanTh3d = hoanTh3d;
    }

    public Long getHoanThl3d() {
        return hoanThl3d;
    }

    public void setHoanThl3d(Long hoanThl3d) {
        this.hoanThl3d = hoanThl3d;
    }

    public Long getHoanThTong() {
        return hoanThTong;
    }

    public void setHoanThTong(Long hoanThTong) {
        this.hoanThTong = hoanThTong;
    }

    public Long getHoanDh6h() {
        return hoanDh6h;
    }

    public void setHoanDh6h(Long hoanDh6h) {
        this.hoanDh6h = hoanDh6h;
    }

    public Long getHoanDh12h() {
        return hoanDh12h;
    }

    public void setHoanDh12h(Long hoanDh12h) {
        this.hoanDh12h = hoanDh12h;
    }

    public Long getHoanDh18h() {
        return hoanDh18h;
    }

    public void setHoanDh18h(Long hoanDh18h) {
        this.hoanDh18h = hoanDh18h;
    }

    public Long getHoanDh24h() {
        return hoanDh24h;
    }

    public void setHoanDh24h(Long hoanDh24h) {
        this.hoanDh24h = hoanDh24h;
    }

    public Long getHoanDhTong() {
        return hoanDhTong;
    }

    public void setHoanDhTong(Long hoanDhTong) {
        this.hoanDhTong = hoanDhTong;
    }

    public Long getHoanQh1d() {
        return hoanQh1d;
    }

    public void setHoanQh1d(Long hoanQh1d) {
        this.hoanQh1d = hoanQh1d;
    }

    public Long getHoanQh2d() {
        return hoanQh2d;
    }

    public void setHoanQh2d(Long hoanQh2d) {
        this.hoanQh2d = hoanQh2d;
    }

    public Long getHoanQh3d() {
        return hoanQh3d;
    }

    public void setHoanQh3d(Long hoanQh3d) {
        this.hoanQh3d = hoanQh3d;
    }

    public Long getHoanQh4d() {
        return hoanQh4d;
    }

    public void setHoanQh4d(Long hoanQh4d) {
        this.hoanQh4d = hoanQh4d;
    }

    public Long getHoanQh5d() {
        return hoanQh5d;
    }

    public void setHoanQh5d(Long hoanQh5d) {
        this.hoanQh5d = hoanQh5d;
    }

    public Long getHoanQh6d() {
        return hoanQh6d;
    }

    public void setHoanQh6d(Long hoanQh6d) {
        this.hoanQh6d = hoanQh6d;
    }

    public Long getHoanQh7d() {
        return hoanQh7d;
    }

    public void setHoanQh7d(Long hoanQh7d) {
        this.hoanQh7d = hoanQh7d;
    }

    public Long getHoanQhl7d() {
        return hoanQhl7d;
    }

    public void setHoanQhl7d(Long hoanQhl7d) {
        this.hoanQhl7d = hoanQhl7d;
    }

    public Long getHoanQhTong() {
        return hoanQhTong;
    }

    public void setHoanQhTong(Long hoanQhTong) {
        this.hoanQhTong = hoanQhTong;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }
}
