package nocsystem.indexmanager.models.Response.DieuHanhTon;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;

public class DieuHanhTonPhatExcelDetail {
    private Integer stt;
    private String maPhieuGui;
    private String tinhNhan;
    private String huyenNhan;
    private String tinhPhat;
    private String huyenPhat;
    private String maBuuCucPhatThucTe;
    private String maDVViettel;
    private String maBuuCucGoc;
    private String maTrangThai;
    private String maBuuCucHT;
    private String maChiNhanhHT;
    private String maDoiTac;
    private String maKHGui;
    private String maBuuCucPhat;
    private String timePCP;
    private String timeGachBP;
    private String timeGachBP2;
    private String timeGachBP3;
    private String ngayGuiBP;
    private Integer loaiPG;
    private Integer soLanGiao;
    private String tuyenBuuTa;
    private Double tienCOD;
    private String tgQuyDinhPhat;
    private String tgChenhLechPhat;
    private String ngayBaoCao;
    private String noiKho;
    private String typeGiao;
    private String danhGiaTon;
    private float thoiGianTon;
    private HashMap<String, Object> objectHashMap = new HashMap<>();

    public DieuHanhTonPhatExcelDetail(Integer stt, String maPhieuGui, String tinhNhan, String huyenNhan, String tinhPhat, String huyenPhat, String maDVViettel, String maBuuCucGoc, String maTrangThai, String maBuuCucHT, String maChiNhanhHT, String maDoiTac, String maKHGui, String maBuuCucPhat, String timePCP, String timeGachBP, String timeGachBP2, String timeGachBP3, String ngayGuiBP, Integer soLanGiao, String tuyenBuuTa, Double tienCOD, String tgQuyDinhPhat, String tgChenhLechPhat, LocalDate ngayBaoCao, Integer noiKho, String typeGiao) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tinhPhat = tinhPhat;
        this.huyenPhat = huyenPhat;
        this.maDVViettel = maDVViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maTrangThai = maTrangThai;
        this.maBuuCucHT = maBuuCucHT;
        this.maChiNhanhHT = maChiNhanhHT;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.maBuuCucPhat = maBuuCucPhat;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.timeGachBP2 = timeGachBP2;
        this.timeGachBP3 = timeGachBP3;
        this.ngayGuiBP = ngayGuiBP;
        this.loaiPG = tienCOD == 0.0 ? 2 : 1;
        this.soLanGiao = soLanGiao;
        this.tuyenBuuTa = tuyenBuuTa;
        this.tienCOD = tienCOD;
        this.tgQuyDinhPhat = tgQuyDinhPhat;
        this.tgChenhLechPhat = tgChenhLechPhat;
        this.ngayBaoCao = ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        this.noiKho = noiKho == 0 ? "" : "NOI KHO";
        this.typeGiao = typeGiao;
        this.objectHashMap = convert(this);
    }

    public DieuHanhTonPhatExcelDetail(Integer stt, String maPhieuGui, String tinhNhan, String huyenNhan, String tinhPhat, String huyenPhat, String maBuuCucPhatThucTe, String maDVViettel, String maBuuCucGoc, String maTrangThai, String maBuuCucHT, String maChiNhanhHT, String maDoiTac, String maKHGui, String maBuuCucPhat, String timePCP, String timeGachBP, String timeGachBP2, String timeGachBP3, String ngayGuiBP, Integer soLanGiao, String tuyenBuuTa, Double tienCOD, String tgQuyDinhPhat, String tgChenhLechPhat, LocalDate ngayBaoCao, Integer noiKho, String typeGiao,String danhGiaTon,float thoiGianTon) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tinhPhat = tinhPhat;
        this.huyenPhat = huyenPhat;
        this.maBuuCucPhatThucTe = maBuuCucPhatThucTe;
        this.maDVViettel = maDVViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maTrangThai = maTrangThai;
        this.maBuuCucHT = maBuuCucHT;
        this.maChiNhanhHT = maChiNhanhHT;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.maBuuCucPhat = maBuuCucPhat;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.timeGachBP2 = timeGachBP2;
        this.timeGachBP3 = timeGachBP3;
        this.ngayGuiBP = ngayGuiBP;
        this.loaiPG = tienCOD == 0.0 ? 2 : 1;
        this.soLanGiao = soLanGiao;
        this.tuyenBuuTa = tuyenBuuTa;
        this.tienCOD = tienCOD;
        this.tgQuyDinhPhat = tgQuyDinhPhat;
        this.tgChenhLechPhat = tgChenhLechPhat;
        this.ngayBaoCao = ngayBaoCao.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        this.noiKho = noiKho == 0 ? "" : "NOI KHO";
        this.typeGiao = typeGiao;
        this.danhGiaTon = danhGiaTon;
        this.thoiGianTon = thoiGianTon;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public Integer getStt() {
        return stt;
    }

    public void setStt(Integer stt) {
        this.stt = stt;
    }

    public String getMaPhieuGui() {
        return maPhieuGui;
    }

    public void setMaPhieuGui(String maPhieuGui) {
        this.maPhieuGui = maPhieuGui;
    }

    public String getTinhNhan() {
        return tinhNhan;
    }

    public void setTinhNhan(String tinhNhan) {
        this.tinhNhan = tinhNhan;
    }

    public String getHuyenNhan() {
        return huyenNhan;
    }

    public void setHuyenNhan(String huyenNhan) {
        this.huyenNhan = huyenNhan;
    }

    public String getTinhPhat() {
        return tinhPhat;
    }

    public void setTinhPhat(String tinhPhat) {
        this.tinhPhat = tinhPhat;
    }

    public String getHuyenPhat() {
        return huyenPhat;
    }

    public void setHuyenPhat(String huyenPhat) {
        this.huyenPhat = huyenPhat;
    }

    public String getMaBuuCucPhatThucTe() {
        return maBuuCucPhatThucTe;
    }

    public void setMaBuuCucPhatThucTe(String maBuuCucPhatThucTe) {
        this.maBuuCucPhatThucTe = maBuuCucPhatThucTe;
    }

    public String getMaDVViettel() {
        return maDVViettel;
    }

    public void setMaDVViettel(String maDVViettel) {
        this.maDVViettel = maDVViettel;
    }

    public String getMaBuuCucGoc() {
        return maBuuCucGoc;
    }

    public void setMaBuuCucGoc(String maBuuCucGoc) {
        this.maBuuCucGoc = maBuuCucGoc;
    }

    public String getMaTrangThai() {
        return maTrangThai;
    }

    public void setMaTrangThai(String maTrangThai) {
        this.maTrangThai = maTrangThai;
    }

    public String getMaBuuCucHT() {
        return maBuuCucHT;
    }

    public void setMaBuuCucHT(String maBuuCucHT) {
        this.maBuuCucHT = maBuuCucHT;
    }

    public String getMaChiNhanhHT() {
        return maChiNhanhHT;
    }

    public void setMaChiNhanhHT(String maChiNhanhHT) {
        this.maChiNhanhHT = maChiNhanhHT;
    }

    public String getMaDoiTac() {
        return maDoiTac;
    }

    public void setMaDoiTac(String maDoiTac) {
        this.maDoiTac = maDoiTac;
    }

    public String getMaKHGui() {
        return maKHGui;
    }

    public void setMaKHGui(String maKHGui) {
        this.maKHGui = maKHGui;
    }

    public String getMaBuuCucPhat() {
        return maBuuCucPhat;
    }

    public void setMaBuuCucPhat(String maBuuCucPhat) {
        this.maBuuCucPhat = maBuuCucPhat;
    }

    public String getTimePCP() {
        return timePCP;
    }

    public void setTimePCP(String timePCP) {
        this.timePCP = timePCP;
    }

    public String getTimeGachBP() {
        return timeGachBP;
    }

    public void setTimeGachBP(String timeGachBP) {
        this.timeGachBP = timeGachBP;
    }

    public String getTimeGachBP2() {
        return timeGachBP2;
    }

    public void setTimeGachBP2(String timeGachBP2) {
        this.timeGachBP2 = timeGachBP2;
    }

    public String getTimeGachBP3() {
        return timeGachBP3;
    }

    public void setTimeGachBP3(String timeGachBP3) {
        this.timeGachBP3 = timeGachBP3;
    }

    public String getNgayGuiBP() {
        return ngayGuiBP;
    }

    public void setNgayGuiBP(String ngayGuiBP) {
        this.ngayGuiBP = ngayGuiBP;
    }

    public Integer getLoaiPG() {
        return loaiPG;
    }

    public void setLoaiPG(Integer loaiPG) {
        this.loaiPG = loaiPG;
    }

    public Integer getSoLanGiao() {
        return soLanGiao;
    }

    public void setSoLanGiao(Integer soLanGiao) {
        this.soLanGiao = soLanGiao;
    }

    public String getTuyenBuuTa() {
        return tuyenBuuTa;
    }

    public void setTuyenBuuTa(String tuyenBuuTa) {
        this.tuyenBuuTa = tuyenBuuTa;
    }

    public Double getTienCOD() {
        return tienCOD;
    }

    public void setTienCOD(Double tienCOD) {
        this.tienCOD = tienCOD;
    }

    public String getTgQuyDinhPhat() {
        return tgQuyDinhPhat;
    }

    public void setTgQuyDinhPhat(String tgQuyDinhPhat) {
        this.tgQuyDinhPhat = tgQuyDinhPhat;
    }

    public String getTgChenhLechPhat() {
        return tgChenhLechPhat;
    }

    public void setTgChenhLechPhat(String tgChenhLechPhat) {
        this.tgChenhLechPhat = tgChenhLechPhat;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getNoiKho() {
        return noiKho;
    }

    public void setNoiKho(String noiKho) {
        this.noiKho = noiKho;
    }

    public String getTypeGiao() {
        return typeGiao;
    }

    public void setTypeGiao(String typeGiao) {
        this.typeGiao = typeGiao;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public String getDanhGiaTon() {
        return danhGiaTon;
    }

    public void setDanhGiaTon(String danhGiaTon) {
        this.danhGiaTon = danhGiaTon;
    }

    public float getThoiGianTon() {
        return thoiGianTon;
    }

    public void setThoiGianTon(float thoiGianTon) {
        this.thoiGianTon = thoiGianTon;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }
}
