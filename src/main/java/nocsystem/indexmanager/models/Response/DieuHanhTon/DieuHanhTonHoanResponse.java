package nocsystem.indexmanager.models.Response.DieuHanhTon;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DieuHanhTonHoanResponse {
    private String chiNhanh;
    private String buuCuc;
    private String tuyenBuuTa;
    private Long hoanTh1d;
    private Long hoanTh2d;
    private <PERSON> hoanTh3d;
    private Long hoanThl3d;
    private Long hoanThTong;
    private Long hoanDh6h;
    private Long hoanDh12h;
    private Long hoanDh18h;
    private Long hoanDh24h;
    private Long hoanDhTong;
    private Long hoanQh1d;
    private Long hoanQh2d;
    private Long hoanQh3d;
    private Long hoanQh4d;
    private Long hoanQh5d;
    private Long hoanQh6d;
    private <PERSON> hoanQh7d;
    private Long hoanQhl7d;
    private Long hoanQhTong;

    public DieuHanhTonHoanResponse() {
    }

    public DieuHanhTonHoanResponse(String chiNhanh, String buuCuc, String tuyenBuuTa, Long hoanTh1d, <PERSON> hoanTh2d, Long hoanTh3d, <PERSON> hoanThl3d, <PERSON> hoanTh<PERSON>ong, <PERSON> hoanDh6h, <PERSON> hoanDh12h, Long hoanDh18h, Long hoanDh24h, Long hoanDhTong, Long hoanQh1d, Long hoanQh2d, Long hoanQh3d, Long hoanQh4d, Long hoanQh5d, Long hoanQh6d, Long hoanQh7d, Long hoanQhl7d, Long hoanQhTong) {
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.tuyenBuuTa = tuyenBuuTa;
        this.hoanTh1d = hoanTh1d;
        this.hoanTh2d = hoanTh2d;
        this.hoanTh3d = hoanTh3d;
        this.hoanThl3d = hoanThl3d;
        this.hoanThTong = hoanThTong;
        this.hoanDh6h = hoanDh6h;
        this.hoanDh12h = hoanDh12h;
        this.hoanDh18h = hoanDh18h;
        this.hoanDh24h = hoanDh24h;
        this.hoanDhTong = hoanDhTong;
        this.hoanQh1d = hoanQh1d;
        this.hoanQh2d = hoanQh2d;
        this.hoanQh3d = hoanQh3d;
        this.hoanQh4d = hoanQh4d;
        this.hoanQh5d = hoanQh5d;
        this.hoanQh6d = hoanQh6d;
        this.hoanQh7d = hoanQh7d;
        this.hoanQhl7d = hoanQhl7d;
        this.hoanQhTong = hoanQhTong;
    }

    public DieuHanhTonHoanResponse(String chiNhanh, String buuCuc, Long hoanTh1d, Long hoanTh2d, Long hoanTh3d, Long hoanThl3d, Long hoanThTong, Long hoanDh6h, Long hoanDh12h, Long hoanDh18h, Long hoanDh24h, Long hoanDhTong, Long hoanQh1d, Long hoanQh2d, Long hoanQh3d, Long hoanQh4d, Long hoanQh5d, Long hoanQh6d, Long hoanQh7d, Long hoanQhl7d, Long hoanQhTong) {
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.hoanTh1d = hoanTh1d;
        this.hoanTh2d = hoanTh2d;
        this.hoanTh3d = hoanTh3d;
        this.hoanThl3d = hoanThl3d;
        this.hoanThTong = hoanThTong;
        this.hoanDh6h = hoanDh6h;
        this.hoanDh12h = hoanDh12h;
        this.hoanDh18h = hoanDh18h;
        this.hoanDh24h = hoanDh24h;
        this.hoanDhTong = hoanDhTong;
        this.hoanQh1d = hoanQh1d;
        this.hoanQh2d = hoanQh2d;
        this.hoanQh3d = hoanQh3d;
        this.hoanQh4d = hoanQh4d;
        this.hoanQh5d = hoanQh5d;
        this.hoanQh6d = hoanQh6d;
        this.hoanQh7d = hoanQh7d;
        this.hoanQhl7d = hoanQhl7d;
        this.hoanQhTong = hoanQhTong;
    }

    public DieuHanhTonHoanResponse(String chiNhanh, Long hoanTh1d, Long hoanTh2d, Long hoanTh3d, Long hoanThl3d, Long hoanThTong, Long hoanDh6h, Long hoanDh12h, Long hoanDh18h, Long hoanDh24h, Long hoanDhTong, Long hoanQh1d, Long hoanQh2d, Long hoanQh3d, Long hoanQh4d, Long hoanQh5d, Long hoanQh6d, Long hoanQh7d, Long hoanQhl7d, Long hoanQhTong) {
        this.chiNhanh = chiNhanh;
        this.hoanTh1d = hoanTh1d;
        this.hoanTh2d = hoanTh2d;
        this.hoanTh3d = hoanTh3d;
        this.hoanThl3d = hoanThl3d;
        this.hoanThTong = hoanThTong;
        this.hoanDh6h = hoanDh6h;
        this.hoanDh12h = hoanDh12h;
        this.hoanDh18h = hoanDh18h;
        this.hoanDh24h = hoanDh24h;
        this.hoanDhTong = hoanDhTong;
        this.hoanQh1d = hoanQh1d;
        this.hoanQh2d = hoanQh2d;
        this.hoanQh3d = hoanQh3d;
        this.hoanQh4d = hoanQh4d;
        this.hoanQh5d = hoanQh5d;
        this.hoanQh6d = hoanQh6d;
        this.hoanQh7d = hoanQh7d;
        this.hoanQhl7d = hoanQhl7d;
        this.hoanQhTong = hoanQhTong;
    }

    public DieuHanhTonHoanResponse(Long hoanTh1d, Long hoanTh2d, Long hoanTh3d, Long hoanThl3d, Long hoanThTong, Long hoanDh6h, Long hoanDh12h, Long hoanDh18h, Long hoanDh24h, Long hoanDhTong, Long hoanQh1d, Long hoanQh2d, Long hoanQh3d, Long hoanQh4d, Long hoanQh5d, Long hoanQh6d, Long hoanQh7d, Long hoanQhl7d, Long hoanQhTong) {
        this.hoanTh1d = hoanTh1d;
        this.hoanTh2d = hoanTh2d;
        this.hoanTh3d = hoanTh3d;
        this.hoanThl3d = hoanThl3d;
        this.hoanThTong = hoanThTong;
        this.hoanDh6h = hoanDh6h;
        this.hoanDh12h = hoanDh12h;
        this.hoanDh18h = hoanDh18h;
        this.hoanDh24h = hoanDh24h;
        this.hoanDhTong = hoanDhTong;
        this.hoanQh1d = hoanQh1d;
        this.hoanQh2d = hoanQh2d;
        this.hoanQh3d = hoanQh3d;
        this.hoanQh4d = hoanQh4d;
        this.hoanQh5d = hoanQh5d;
        this.hoanQh6d = hoanQh6d;
        this.hoanQh7d = hoanQh7d;
        this.hoanQhl7d = hoanQhl7d;
        this.hoanQhTong = hoanQhTong;
    }
}

