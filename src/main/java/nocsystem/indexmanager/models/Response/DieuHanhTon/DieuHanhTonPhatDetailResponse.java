package nocsystem.indexmanager.models.Response.DieuHanhTon;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class DieuHanhTonPhatDetailResponse {
    private String maPhieuGui;
    private String tinhNhan;
    private String huyenNhan;
    private String tinhPhat;
    private String huyenPhat;
    private String maBuuCucPhatThucTe;
    private String maDVViettel;
    private String maBuuCucGoc;
    private String maTrangThai;
    private String maBuuCucHT;
    private String maChiNhanhHT;
    private String maDoiTac;
    private String maKHGui;
    private String maBuuCucPhat;
    private String timePCP;
    private String timeGachBP;
    private String timeGachBP2;
    private String timeGachBP3;
    private String ngayGuiBP;
    private Integer soLanGiao;
    private String tuyenBuuTa;
    private Double tienCOD;
    private String tgQuyDinhPhat;
    private String tgChenhLechPhat;
    private LocalDate ngayBaoCao;
    private Integer noiKho;
    private String typeGiao;
    private String danhGiaTon;
    private float thoiGianTon;

    public DieuHanhTonPhatDetailResponse(String maPhieuGui, String tinhNhan, String huyenNhan, String tinhPhat, String huyenPhat, String maDVViettel, String maBuuCucGoc, String maTrangThai, String maBuuCucHT, String maChiNhanhHT, String maDoiTac, String maKHGui, String maBuuCucPhat, String timePCP, String timeGachBP, String timeGachBP2, String timeGachBP3, String ngayGuiBP, Integer soLanGiao, String tuyenBuuTa, Double tienCOD, String tgQuyDinhPhat, String tgChenhLechPhat, LocalDate ngayBaoCao, Integer noiKho, String typeGiao) {
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tinhPhat = tinhPhat;
        this.huyenPhat = huyenPhat;
        this.maDVViettel = maDVViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maTrangThai = maTrangThai;
        this.maBuuCucHT = maBuuCucHT;
        this.maChiNhanhHT = maChiNhanhHT;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.maBuuCucPhat = maBuuCucPhat;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.timeGachBP2 = timeGachBP2;
        this.timeGachBP3 = timeGachBP3;
        this.ngayGuiBP = ngayGuiBP;
        this.soLanGiao = soLanGiao;
        this.tuyenBuuTa = tuyenBuuTa;
        this.tienCOD = tienCOD;
        this.tgQuyDinhPhat = tgQuyDinhPhat;
        this.tgChenhLechPhat = tgChenhLechPhat;
        this.ngayBaoCao = ngayBaoCao;
        this.noiKho = noiKho;
        this.typeGiao = typeGiao;
    }

    public DieuHanhTonPhatDetailResponse(String maPhieuGui, String tinhNhan, String huyenNhan, String tinhPhat, String huyenPhat, String maBuuCucPhatThucTe, String maDVViettel, String maBuuCucGoc, String maTrangThai, String maBuuCucHT, String maChiNhanhHT, String maDoiTac, String maKHGui, String maBuuCucPhat, String timePCP, String timeGachBP, String timeGachBP2, String timeGachBP3, String ngayGuiBP, Integer soLanGiao, String tuyenBuuTa, Double tienCOD, String tgQuyDinhPhat, String tgChenhLechPhat, LocalDate ngayBaoCao, Integer noiKho, String typeGiao) {
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tinhPhat = tinhPhat;
        this.huyenPhat = huyenPhat;
        this.maBuuCucPhatThucTe = maBuuCucPhatThucTe;
        this.maDVViettel = maDVViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maTrangThai = maTrangThai;
        this.maBuuCucHT = maBuuCucHT;
        this.maChiNhanhHT = maChiNhanhHT;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.maBuuCucPhat = maBuuCucPhat;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.timeGachBP2 = timeGachBP2;
        this.timeGachBP3 = timeGachBP3;
        this.ngayGuiBP = ngayGuiBP;
        this.soLanGiao = soLanGiao;
        this.tuyenBuuTa = tuyenBuuTa;
        this.tienCOD = tienCOD;
        this.tgQuyDinhPhat = tgQuyDinhPhat;
        this.tgChenhLechPhat = tgChenhLechPhat;
        this.ngayBaoCao = ngayBaoCao;
        this.noiKho = noiKho;
        this.typeGiao = typeGiao;
    }
}
