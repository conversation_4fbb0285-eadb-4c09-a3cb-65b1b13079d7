package nocsystem.indexmanager.models.Response.DieuHanhTon;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DieuHanhTonPhatResponse {
    private String chiNhanh;
    private String buuCuc;
    private String tuyenBuuTa;
    private Long phatTh1d;
    private Long phatTh2d;
    private Long phatTh3d;
    private Long phatThl3d;
    private Long phatThTong;
    private Long phatDh6h;
    private Long phatDh12h;
    private Long phatDh18h;
    private Long phatDh24h;
    private Long phatDhTong;
    private Long phatQh1d;
    private Long phatQh2d;
    private Long phatQh3d;
    private Long phatQh4d;
    private Long phatQh5d;
    private Long phatQh6d;
    private Long phatQh7d;
    private Long phatQhl7d;
    private Long phatQhTong;

    public DieuHanhTonPhatResponse() {
    }

    public DieuHanhTonPhatResponse(String chiNhanh, String buuCuc, String tuyenBuuTa, Long phatTh1d, Long phatTh2d, Long phatTh3d, Long phatThl3d, <PERSON> phatThTong, Long phatDh6h, Long phatDh12h, Long phatDh18h, Long phatDh24h, Long phatDhTong, Long phatQh1d, Long phatQh2d, Long phatQh3d, Long phatQh4d, Long phatQh5d, Long phatQh6d, Long phatQh7d, Long phatQhl7d, Long phatQhTong) {
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.tuyenBuuTa = tuyenBuuTa;
        this.phatTh1d = phatTh1d;
        this.phatTh2d = phatTh2d;
        this.phatTh3d = phatTh3d;
        this.phatThl3d = phatThl3d;
        this.phatThTong = phatThTong;
        this.phatDh6h = phatDh6h;
        this.phatDh12h = phatDh12h;
        this.phatDh18h = phatDh18h;
        this.phatDh24h = phatDh24h;
        this.phatDhTong = phatDhTong;
        this.phatQh1d = phatQh1d;
        this.phatQh2d = phatQh2d;
        this.phatQh3d = phatQh3d;
        this.phatQh4d = phatQh4d;
        this.phatQh5d = phatQh5d;
        this.phatQh6d = phatQh6d;
        this.phatQh7d = phatQh7d;
        this.phatQhl7d = phatQhl7d;
        this.phatQhTong = phatQhTong;
    }

    public DieuHanhTonPhatResponse(String chiNhanh, String buuCuc, Long phatTh1d, Long phatTh2d, Long phatTh3d, Long phatThl3d, Long phatThTong, Long phatDh6h, Long phatDh12h, Long phatDh18h, Long phatDh24h, Long phatDhTong, Long phatQh1d, Long phatQh2d, Long phatQh3d, Long phatQh4d, Long phatQh5d, Long phatQh6d, Long phatQh7d, Long phatQhl7d, Long phatQhTong) {
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.phatTh1d = phatTh1d;
        this.phatTh2d = phatTh2d;
        this.phatTh3d = phatTh3d;
        this.phatThl3d = phatThl3d;
        this.phatThTong = phatThTong;
        this.phatDh6h = phatDh6h;
        this.phatDh12h = phatDh12h;
        this.phatDh18h = phatDh18h;
        this.phatDh24h = phatDh24h;
        this.phatDhTong = phatDhTong;
        this.phatQh1d = phatQh1d;
        this.phatQh2d = phatQh2d;
        this.phatQh3d = phatQh3d;
        this.phatQh4d = phatQh4d;
        this.phatQh5d = phatQh5d;
        this.phatQh6d = phatQh6d;
        this.phatQh7d = phatQh7d;
        this.phatQhl7d = phatQhl7d;
        this.phatQhTong = phatQhTong;
    }

    public DieuHanhTonPhatResponse(String chiNhanh, Long phatTh1d, Long phatTh2d, Long phatTh3d, Long phatThl3d, Long phatThTong, Long phatDh6h, Long phatDh12h, Long phatDh18h, Long phatDh24h, Long phatDhTong, Long phatQh1d, Long phatQh2d, Long phatQh3d, Long phatQh4d, Long phatQh5d, Long phatQh6d, Long phatQh7d, Long phatQhl7d, Long phatQhTong) {
        this.chiNhanh = chiNhanh;
        this.phatTh1d = phatTh1d;
        this.phatTh2d = phatTh2d;
        this.phatTh3d = phatTh3d;
        this.phatThl3d = phatThl3d;
        this.phatThTong = phatThTong;
        this.phatDh6h = phatDh6h;
        this.phatDh12h = phatDh12h;
        this.phatDh18h = phatDh18h;
        this.phatDh24h = phatDh24h;
        this.phatDhTong = phatDhTong;
        this.phatQh1d = phatQh1d;
        this.phatQh2d = phatQh2d;
        this.phatQh3d = phatQh3d;
        this.phatQh4d = phatQh4d;
        this.phatQh5d = phatQh5d;
        this.phatQh6d = phatQh6d;
        this.phatQh7d = phatQh7d;
        this.phatQhl7d = phatQhl7d;
        this.phatQhTong = phatQhTong;
    }

    public DieuHanhTonPhatResponse(Long phatTh1d, Long phatTh2d, Long phatTh3d, Long phatThl3d, Long phatThTong, Long phatDh6h, Long phatDh12h, Long phatDh18h, Long phatDh24h, Long phatDhTong, Long phatQh1d, Long phatQh2d, Long phatQh3d, Long phatQh4d, Long phatQh5d, Long phatQh6d, Long phatQh7d, Long phatQhl7d, Long phatQhTong) {
        this.phatTh1d = phatTh1d;
        this.phatTh2d = phatTh2d;
        this.phatTh3d = phatTh3d;
        this.phatThl3d = phatThl3d;
        this.phatThTong = phatThTong;
        this.phatDh6h = phatDh6h;
        this.phatDh12h = phatDh12h;
        this.phatDh18h = phatDh18h;
        this.phatDh24h = phatDh24h;
        this.phatDhTong = phatDhTong;
        this.phatQh1d = phatQh1d;
        this.phatQh2d = phatQh2d;
        this.phatQh3d = phatQh3d;
        this.phatQh4d = phatQh4d;
        this.phatQh5d = phatQh5d;
        this.phatQh6d = phatQh6d;
        this.phatQh7d = phatQh7d;
        this.phatQhl7d = phatQhl7d;
        this.phatQhTong = phatQhTong;
    }
}
