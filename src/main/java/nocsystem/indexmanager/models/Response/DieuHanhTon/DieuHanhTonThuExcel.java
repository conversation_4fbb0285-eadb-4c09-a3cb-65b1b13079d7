package nocsystem.indexmanager.models.Response.DieuHanhTon;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;

public class DieuHanhTonThuExcel {
    private Integer stt;
    private String chiNhanh;
    private String buuCuc;
    private String tuyenBuuTa;
    private Long thuTh1d;
    private Long thuTh2d;
    private Long thuTh3d;
    private Long thuThl3d;
    private Long thuThTong;
    private Long thuDh6h;
    private Long thuDh12h;
    private Long thuDh18h;
    private Long thuDh24h;
    private Long thuDhTong;
    private Long thuQh1d;
    private Long thuQh2d;
    private Long thuQh3d;
    private Long thuQh4d;
    private Long thuQh5d;
    private Long thuQhl5d;
    private Long thuQhTong;
    private HashMap<String, Object> objectHashMap = new HashMap<>();

    public DieuHanhTonThuExcel(Integer stt, String chiNhanh, Long thuTh1d, <PERSON> thuTh2d, <PERSON> thuTh3d, <PERSON> thuThl3d, <PERSON> thuThTong, <PERSON> thuDh6h, <PERSON> thuDh12h, <PERSON> thuDh18h, <PERSON> thuDh24h, <PERSON> thuDhTong, Long thuQh1d, Long thuQh2d, Long thuQh3d, Long thuQh4d, Long thuQh5d, Long thuQhl5d, Long thuQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.thuTh1d = thuTh1d;
        this.thuTh2d = thuTh2d;
        this.thuTh3d = thuTh3d;
        this.thuThl3d = thuThl3d;
        this.thuThTong = thuThTong;
        this.thuDh6h = thuDh6h;
        this.thuDh12h = thuDh12h;
        this.thuDh18h = thuDh18h;
        this.thuDh24h = thuDh24h;
        this.thuDhTong = thuDhTong;
        this.thuQh1d = thuQh1d;
        this.thuQh2d = thuQh2d;
        this.thuQh3d = thuQh3d;
        this.thuQh4d = thuQh4d;
        this.thuQh5d = thuQh5d;
        this.thuQhl5d = thuQhl5d;
        this.thuQhTong = thuQhTong;
        this.objectHashMap = convert(this);
    }

    public DieuHanhTonThuExcel(Integer stt, String chiNhanh, String buuCuc, Long thuTh1d, Long thuTh2d, Long thuTh3d, Long thuThl3d, Long thuThTong, Long thuDh6h, Long thuDh12h, Long thuDh18h, Long thuDh24h, Long thuDhTong, Long thuQh1d, Long thuQh2d, Long thuQh3d, Long thuQh4d, Long thuQh5d, Long thuQhl5d, Long thuQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.thuTh1d = thuTh1d;
        this.thuTh2d = thuTh2d;
        this.thuTh3d = thuTh3d;
        this.thuThl3d = thuThl3d;
        this.thuThTong = thuThTong;
        this.thuDh6h = thuDh6h;
        this.thuDh12h = thuDh12h;
        this.thuDh18h = thuDh18h;
        this.thuDh24h = thuDh24h;
        this.thuDhTong = thuDhTong;
        this.thuQh1d = thuQh1d;
        this.thuQh2d = thuQh2d;
        this.thuQh3d = thuQh3d;
        this.thuQh4d = thuQh4d;
        this.thuQh5d = thuQh5d;
        this.thuQhl5d = thuQhl5d;
        this.thuQhTong = thuQhTong;
        this.objectHashMap = convert(this);
    }

    public DieuHanhTonThuExcel(Integer stt, String chiNhanh, String buuCuc, String tuyenBuuTa, Long thuTh1d, Long thuTh2d, Long thuTh3d, Long thuThl3d, Long thuThTong, Long thuDh6h, Long thuDh12h, Long thuDh18h, Long thuDh24h, Long thuDhTong, Long thuQh1d, Long thuQh2d, Long thuQh3d, Long thuQh4d, Long thuQh5d, Long thuQhl5d, Long thuQhTong) throws NoSuchFieldException, IllegalAccessException {
        this.stt = stt;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.tuyenBuuTa = tuyenBuuTa;
        this.thuTh1d = thuTh1d;
        this.thuTh2d = thuTh2d;
        this.thuTh3d = thuTh3d;
        this.thuThl3d = thuThl3d;
        this.thuThTong = thuThTong;
        this.thuDh6h = thuDh6h;
        this.thuDh12h = thuDh12h;
        this.thuDh18h = thuDh18h;
        this.thuDh24h = thuDh24h;
        this.thuDhTong = thuDhTong;
        this.thuQh1d = thuQh1d;
        this.thuQh2d = thuQh2d;
        this.thuQh3d = thuQh3d;
        this.thuQh4d = thuQh4d;
        this.thuQh5d = thuQh5d;
        this.thuQhl5d = thuQhl5d;
        this.thuQhTong = thuQhTong;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public Integer getStt() {
        return stt;
    }

    public void setStt(Integer stt) {
        this.stt = stt;
    }

    public String getChiNhanh() {
        return chiNhanh;
    }

    public void setChiNhanh(String chiNhanh) {
        this.chiNhanh = chiNhanh;
    }

    public String getBuuCuc() {
        return buuCuc;
    }

    public void setBuuCuc(String buuCuc) {
        this.buuCuc = buuCuc;
    }

    public String getTuyenBuuTa() {
        return tuyenBuuTa;
    }

    public void setTuyenBuuTa(String tuyenBuuTa) {
        this.tuyenBuuTa = tuyenBuuTa;
    }

    public Long getThuTh1d() {
        return thuTh1d;
    }

    public void setThuTh1d(Long thuTh1d) {
        this.thuTh1d = thuTh1d;
    }

    public Long getThuTh2d() {
        return thuTh2d;
    }

    public void setThuTh2d(Long thuTh2d) {
        this.thuTh2d = thuTh2d;
    }

    public Long getThuTh3d() {
        return thuTh3d;
    }

    public void setThuTh3d(Long thuTh3d) {
        this.thuTh3d = thuTh3d;
    }

    public Long getThuThl3d() {
        return thuThl3d;
    }

    public void setThuThl3d(Long thuThl3d) {
        this.thuThl3d = thuThl3d;
    }

    public Long getThuThTong() {
        return thuThTong;
    }

    public void setThuThTong(Long thuThTong) {
        this.thuThTong = thuThTong;
    }

    public Long getThuDh6h() {
        return thuDh6h;
    }

    public void setThuDh6h(Long thuDh6h) {
        this.thuDh6h = thuDh6h;
    }

    public Long getThuDh12h() {
        return thuDh12h;
    }

    public void setThuDh12h(Long thuDh12h) {
        this.thuDh12h = thuDh12h;
    }

    public Long getThuDh18h() {
        return thuDh18h;
    }

    public void setThuDh18h(Long thuDh18h) {
        this.thuDh18h = thuDh18h;
    }

    public Long getThuDh24h() {
        return thuDh24h;
    }

    public void setThuDh24h(Long thuDh24h) {
        this.thuDh24h = thuDh24h;
    }

    public Long getThuDhTong() {
        return thuDhTong;
    }

    public void setThuDhTong(Long thuDhTong) {
        this.thuDhTong = thuDhTong;
    }

    public Long getThuQh1d() {
        return thuQh1d;
    }

    public void setThuQh1d(Long thuQh1d) {
        this.thuQh1d = thuQh1d;
    }

    public Long getThuQh2d() {
        return thuQh2d;
    }

    public void setThuQh2d(Long thuQh2d) {
        this.thuQh2d = thuQh2d;
    }

    public Long getThuQh3d() {
        return thuQh3d;
    }

    public void setThuQh3d(Long thuQh3d) {
        this.thuQh3d = thuQh3d;
    }

    public Long getThuQh4d() {
        return thuQh4d;
    }

    public void setThuQh4d(Long thuQh4d) {
        this.thuQh4d = thuQh4d;
    }

    public Long getThuQh5d() {
        return thuQh5d;
    }

    public void setThuQh5d(Long thuQh5d) {
        this.thuQh5d = thuQh5d;
    }

    public Long getThuQhl5d() {
        return thuQhl5d;
    }

    public void setThuQhl5d(Long thuQhl5d) {
        this.thuQhl5d = thuQhl5d;
    }

    public Long getThuQhTong() {
        return thuQhTong;
    }

    public void setThuQhTong(Long thuQhTong) {
        this.thuQhTong = thuQhTong;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }
}
