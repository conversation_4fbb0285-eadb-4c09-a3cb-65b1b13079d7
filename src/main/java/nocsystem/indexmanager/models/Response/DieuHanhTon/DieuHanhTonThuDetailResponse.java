package nocsystem.indexmanager.models.Response.DieuHanhTon;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DieuHanhTonThuDetailResponse {
    private String maPhieuGui;
    private String maTrangThai;
    private String maDoiTac;
    private String thoiGianTaoDon;
    private String timeKhachHenLay;
    private String tinhNhan;
    private String maKHGui;
    private String tinhPhat;
    private String maBuuCucGoc;
    private String maDVViettel;
    private Double tongCuoc;
    private String updateTime;
    private LocalDate thoiGian;
    private String danhGiaDon;
    private float thoiGianTon;
}
