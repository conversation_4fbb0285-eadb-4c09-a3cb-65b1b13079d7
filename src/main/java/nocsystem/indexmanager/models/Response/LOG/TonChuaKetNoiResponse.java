package nocsystem.indexmanager.models.Response.LOG;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TonChuaKetNoiResponse {
    private String chiNhanh;
    private String maBuuCucGoc;
    private Long slChuaKetNoi;
    private Long slPhaiKetNoi;
    private Float klChuaKetNoi;
    private Float klPhaiKetNoi;
    private Float tlSlChuaKetNoi;
    private Float tlKlChuaKetNoi;

    public TonChuaKetNoiResponse(String chiNhanh, String maBuuCucGoc, Long slChuaKetNoi, Long slPhaiKetNoi, Long klChuaKetNoi, Long klPhaiKetNoi) {
        this.chiNhanh = chiNhanh;
        this.maBuuCucGoc = maBuuCucGoc;
        this.slChuaKetNoi = slChuaKetNoi;
        this.slPhaiKetNoi = slPhaiKetNoi;
        this.klChuaKetNoi = klChuaKetNoi == null ? null : (float)klChuaKetNoi/1000;
        this.klPhaiKetNoi = klPhaiKetNoi == null ? null : (float)klPhaiKetNoi/1000;
    }

    public TonChuaKetNoiResponse(Long slChuaKetNoi, Long slPhaiKetNoi, Long klChuaKetNoi, Long klPhaiKetNoi) {
        this.slChuaKetNoi = slChuaKetNoi;
        this.slPhaiKetNoi = slPhaiKetNoi;
        this.klChuaKetNoi = klChuaKetNoi == null ? null : (float)klChuaKetNoi/1000;
        this.klPhaiKetNoi = klPhaiKetNoi == null ? null : (float)klPhaiKetNoi/1000;
    }

    public TonChuaKetNoiResponse(String chiNhanh, Long slChuaKetNoi, Long slPhaiKetNoi, Long klChuaKetNoi, Long klPhaiKetNoi) {
        this.chiNhanh = chiNhanh;
        this.slChuaKetNoi = slChuaKetNoi;
        this.slPhaiKetNoi = slPhaiKetNoi;
        this.klChuaKetNoi = klChuaKetNoi == null ? null : (float)klChuaKetNoi/1000;
        this.klPhaiKetNoi = klPhaiKetNoi == null ? null : (float)klPhaiKetNoi/1000;
    }
}
