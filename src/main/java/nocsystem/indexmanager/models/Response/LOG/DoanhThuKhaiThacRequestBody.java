package nocsystem.indexmanager.models.Response.LOG;

import lombok.Getter;
import lombok.Setter;
import org.apache.htrace.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class DoanhThuKhaiThacRequestBody {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate ngayBaoCao;
    private Integer luyKe = 0;
    private String chiNhanh = "";
    private String buuCuc = "";
    private String ttkt = "";
    private String ttktCha = "";
    private List<String> maDichVu = new ArrayList<>();
    private Integer page = 0;
    private Integer pageSize = 30;
}
