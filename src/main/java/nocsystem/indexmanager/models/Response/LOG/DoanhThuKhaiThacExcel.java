package nocsystem.indexmanager.models.Response.LOG;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;

public class DoanhThuKhaiThacExcel {
    private String chiNhanh;
    private String buuCuc;
    private Long slNho;
    private Double tlNho;
    private Double doanhThuSlNho;
    private Double doanhThuTlNho;
    private Double tongDoanhThuNho;
    private Long slLon;
    private Double tlLon;
    private Double doanhThuSlLon;
    private Double doanhThuTlLon;
    private Double tongDoanhThuLon;
    private Long tongSl;
    private Double tongTl;
    private Double tongTlDTKT;
    private Long tongDoanhThuSl;
    private Double tongDoanhThuTl;
    private Double tongDoanhThuTruocKpi;
    private Double tongDoanhThuSauKpi;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public String getChiNhanh() {
        return chiNhanh;
    }

    public void setChiNhanh(String chiNhanh) {
        this.chiNhanh = chiNhanh;
    }

    public String getBuuCuc() {
        return buuCuc;
    }

    public void setBuuCuc(String buuCuc) {
        this.buuCuc = buuCuc;
    }

    public Long getSlNho() {
        return slNho;
    }

    public void setSlNho(Long slNho) {
        this.slNho = slNho;
    }

    public Double getTlNho() {
        return tlNho;
    }

    public void setTlNho(Double tlNho) {
        this.tlNho = tlNho;
    }

    public Double getDoanhThuSlNho() {
        return doanhThuSlNho;
    }

    public void setDoanhThuSlNho(Double doanhThuSlNho) {
        this.doanhThuSlNho = doanhThuSlNho;
    }

    public Double getDoanhThuTlNho() {
        return doanhThuTlNho;
    }

    public void setDoanhThuTlNho(Double doanhThuTlNho) {
        this.doanhThuTlNho = doanhThuTlNho;
    }

    public Double getTongDoanhThuNho() {
        return tongDoanhThuNho;
    }

    public void setTongDoanhThuNho(Double tongDoanhThuNho) {
        this.tongDoanhThuNho = tongDoanhThuNho;
    }

    public Long getSlLon() {
        return slLon;
    }

    public void setSlLon(Long slLon) {
        this.slLon = slLon;
    }

    public Double getTlLon() {
        return tlLon;
    }

    public void setTlLon(Double tlLon) {
        this.tlLon = tlLon;
    }

    public Double getDoanhThuSlLon() {
        return doanhThuSlLon;
    }

    public void setDoanhThuSlLon(Double doanhThuSlLon) {
        this.doanhThuSlLon = doanhThuSlLon;
    }

    public Double getDoanhThuTlLon() {
        return doanhThuTlLon;
    }

    public void setDoanhThuTlLon(Double doanhThuTlLon) {
        this.doanhThuTlLon = doanhThuTlLon;
    }

    public Double getTongDoanhThuLon() {
        return tongDoanhThuLon;
    }

    public void setTongDoanhThuLon(Double tongDoanhThuLon) {
        this.tongDoanhThuLon = tongDoanhThuLon;
    }

    public Long getTongSl() {
        return tongSl;
    }

    public void setTongSl(Long tongSl) {
        this.tongSl = tongSl;
    }

    public Double getTongTl() {
        return tongTl;
    }

    public void setTongTl(Double tongTl) {
        this.tongTl = tongTl;
    }

    public Double getTongTlDTKT() {
        return tongTlDTKT;
    }

    public void setTongTlDTKT(Double tongTlDTKT) {
        this.tongTlDTKT = tongTlDTKT;
    }

    public Long getTongDoanhThuSl() {
        return tongDoanhThuSl;
    }

    public void setTongDoanhThuSl(Long tongDoanhThuSl) {
        this.tongDoanhThuSl = tongDoanhThuSl;
    }

    public Double getTongDoanhThuTl() {
        return tongDoanhThuTl;
    }

    public void setTongDoanhThuTl(Double tongDoanhThuTl) {
        this.tongDoanhThuTl = tongDoanhThuTl;
    }

    public Double getTongDoanhThuTruocKpi() {
        return tongDoanhThuTruocKpi;
    }

    public void setTongDoanhThuTruocKpi(Double tongDoanhThuTruocKpi) {
        this.tongDoanhThuTruocKpi = tongDoanhThuTruocKpi;
    }

    public Double getTongDoanhThuSauKpi() {
        return tongDoanhThuSauKpi;
    }

    public void setTongDoanhThuSauKpi(Double tongDoanhThuSauKpi) {
        this.tongDoanhThuSauKpi = tongDoanhThuSauKpi;
    }

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public DoanhThuKhaiThacExcel() {
    }

    public DoanhThuKhaiThacExcel(String chiNhanh, String buuCuc, Long slNho, Double tlNho, Double doanhThuSlNho, Double doanhThuTlNho, Double tongDoanhThuNho, Long slLon, Double tlLon, Double doanhThuSlLon, Double doanhThuTlLon, Double tongDoanhThuLon, Long tongSl, Double tongTl, Double tongTlDTKT, Long tongDoanhThuSl, Double tongDoanhThuTl, Double tongDoanhThuTruocKpi, Double tongDoanhThuSauKpi) throws NoSuchFieldException, IllegalAccessException {
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.slNho = slNho;
        this.tlNho = tlNho;
        this.doanhThuSlNho = doanhThuSlNho;
        this.doanhThuTlNho = doanhThuTlNho;
        this.tongDoanhThuNho = tongDoanhThuNho;
        this.slLon = slLon;
        this.tlLon = tlLon;
        this.doanhThuSlLon = doanhThuSlLon;
        this.doanhThuTlLon = doanhThuTlLon;
        this.tongDoanhThuLon = tongDoanhThuLon;
        this.tongSl = tongSl;
        this.tongTl = tongTl;
        this.tongTlDTKT = tongTlDTKT;
        this.tongDoanhThuSl = tongDoanhThuSl;
        this.tongDoanhThuTl = tongDoanhThuTl;
        this.tongDoanhThuTruocKpi = tongDoanhThuTruocKpi;
        this.tongDoanhThuSauKpi = tongDoanhThuSauKpi;
        this.objectHashMap = convert(this);
    }

    public DoanhThuKhaiThacExcel(String chiNhanh, Long slNho, Double tlNho, Double doanhThuSlNho, Double doanhThuTlNho, Double tongDoanhThuNho, Long slLon, Double tlLon, Double doanhThuSlLon, Double doanhThuTlLon, Double tongDoanhThuLon, Long tongSl, Double tongTl, Double tongTlDTKT, Long tongDoanhThuSl, Double tongDoanhThuTl, Double tongDoanhThuTruocKpi, Double tongDoanhThuSauKpi) throws NoSuchFieldException, IllegalAccessException {
        this.chiNhanh = chiNhanh;
        this.slNho = slNho;
        this.tlNho = tlNho;
        this.doanhThuSlNho = doanhThuSlNho;
        this.doanhThuTlNho = doanhThuTlNho;
        this.tongDoanhThuNho = tongDoanhThuNho;
        this.slLon = slLon;
        this.tlLon = tlLon;
        this.doanhThuSlLon = doanhThuSlLon;
        this.doanhThuTlLon = doanhThuTlLon;
        this.tongDoanhThuLon = tongDoanhThuLon;
        this.tongSl = tongSl;
        this.tongTl = tongTl;
        this.tongTlDTKT = tongTlDTKT;
        this.tongDoanhThuSl = tongDoanhThuSl;
        this.tongDoanhThuTl = tongDoanhThuTl;
        this.tongDoanhThuTruocKpi = tongDoanhThuTruocKpi;
        this.tongDoanhThuSauKpi = tongDoanhThuSauKpi;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }
}
