package nocsystem.indexmanager.models.Response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DubaoChuyenCapResponse {
    private String maChiNhanh;
    private String maBuuCuc;
    private Integer capSuCo;
    private Double xuHuongChuyenCap;
    private Integer chiSoTonKhaiThac;
    private Integer chiSoLacTuyen;
    private Integer chiSoTonXuat;
    private Integer chiSoTonPhat;
    private Integer chiSoBienDongNhanSu;
    private Integer chiSoTonPhatDo789;
    private Double tongThoiGianSuCo;
    private Integer capDoDuBao;
    private Integer vuotKhaNang;
    private Long khoiLuongDuBao;
    private Integer thoiGianDuKienChuyenCap;
    private Double khaNangChuyenCap;
    private Integer nhanVienThieu;
    private Integer khoiLuongTang;
    private Long tonDongCao;
    private Long thoiGianPhatSinhSuCo;
    private Long tgCapNhat;
    private Long capDoAnhHuong;
    private Double doTinCayDuBao;
    private Integer slTonXuat;
    private Integer slPhaiKhaiThac;
    private Integer slDaKhaiThac;
    private Integer slTonPhat;
}
