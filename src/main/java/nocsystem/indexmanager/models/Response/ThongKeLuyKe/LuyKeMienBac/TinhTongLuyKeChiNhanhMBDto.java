package nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@JpaDto
@Data
/*
 * Tính tổng lũy kế chi nhánh miền Bắc
 */
public class TinhTongLuyKeChiNhanhMBDto {
    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private String chiNhanh = "";

    @JsonProperty("buucuc")
    private String buuCuc = "";

    @JsonProperty("mien")
    private String mien = "";

    @JsonProperty("tlht")
    private Float tlht = Float.valueOf(0);

    @JsonProperty("tiendo")
    private Float tienDo = Float.valueOf(0);

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam = Float.valueOf(0);

    @JsonProperty("danh_gia")
    private Integer danhgia;

    @JsonProperty("ke_hoach")
    private Float khthang = Float.valueOf(0);

    @JsonProperty("luy_ke_thang")
    private Float lkthang = Float.valueOf(0);

    @JsonProperty("tiendo_ngay")
    private Float tienDoNgay;

    @JsonProperty("kh_den_ngay")
    private Float keHoachThangDenNgay;

    @JsonProperty("tlht_ngay")
    private Float tiLeHoanThanhNgay;

    @JsonProperty("thuc_hien_ngay")
    private Float thucHienNgay;
}
