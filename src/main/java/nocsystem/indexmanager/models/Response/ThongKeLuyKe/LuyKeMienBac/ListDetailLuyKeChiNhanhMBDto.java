package nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@JpaDto
@Data
public class ListDetailLuyKeChiNhanhMBDto {
    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private String chiNhanh = "";

    @JsonProperty("buucuc")
    private String buuCuc = "";

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("mien")
    private String mien = "";

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;

    @JsonProperty("danh_gia")
    private Integer danhgia;

    @JsonProperty("ke_hoach")
    private Float khthang;

    @JsonProperty("luy_ke_thang")
    private Float lkthang;

    @JsonProperty("tiendo_ngay")
    private Float tienDoNgay;

    @JsonProperty("kh_den_ngay")
    private Float keHoachThangDenNgay;

    @JsonProperty("tlht_ngay")
    private Float tiLeHoanThanhNgay;

    @JsonProperty("thuc_hien_ngay")
    private Float thucHienNgay;
}
