package nocsystem.indexmanager.models.Response.ThongKeLuyKe;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;


@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto

public class TongTienDoLuyKeTCTCNResDto {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("mien")
    private String mien;

    @JsonProperty("buucuc")
    private String buuCuc;

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tiendo")
    private Float tienDo = Float.valueOf(0);

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam = Float.valueOf(0);

    @JsonProperty("danh_gia")
    private Integer danhgia;

    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("luy_ke_thang")
    private Float luyKeThang = Float.valueOf(0);

    @JsonProperty("tiendo_ngay")
    private Float tienDoNgay;

    @JsonProperty("kh_den_ngay")
    private Float keHoachThangDenNgay;

    @JsonProperty("tlht_ngay")
    private Float tiLeHoanThanhNgay;

    @JsonProperty("thuc_hien_ngay")
    private Float thucHienNgay;
}
