package nocsystem.indexmanager.models.Response.ThongKeLuyKe.LuyKeMienBac;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@JpaDto
@Data
public class ListDetailLuyKeBuuCucMTDto {
    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("buucuc")
    private String buuCuc;

    @JsonProperty("mien")
    private String mien;

    @JsonProperty("tlht")
    private float tlht;

    @JsonProperty("tiendo")
    private float tienDo;

    @JsonProperty("tt_thang")
    private float ttThang;

    @JsonProperty("tt_tbn_thang")
    private float ttTbnThang;

    @JsonProperty("tt_nam")
    private float ttNam;

    @JsonProperty("tt_tbn_nam")
    private float ttTbnNam;

    @JsonProperty("danh_gia")
    private Integer danhgia;
}
