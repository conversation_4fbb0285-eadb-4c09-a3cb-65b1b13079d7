package nocsystem.indexmanager.models.Response.ThongKeLuyKe;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto

public class TienDoluyKeTCTCNResDto {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("mien")
    private String mien;

    @JsonProperty("buucuc")
    private String buuCuc;

    /* Tương đương tỉ lệ hoàn thành tháng */
    @JsonProperty("tlht")
    private Float tlht;

    /* Tương đương tiến độ đến ngày */
    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;

    @JsonProperty("danh_gia")
    private Integer danhgia;

    @JsonProperty("ke_hoach")
    private Float keHoach;

    @JsonProperty("luy_ke_thang")
    private Float luyKeThang;

    /* Tương đương Tiến độ ngày */
    @JsonProperty("tiendo_ngay")
    private Float tienDoNgay;

    @JsonProperty("kh_den_ngay")
    private Float keHoachThangDenNgay;

    @JsonProperty("tlht_ngay")
    private Float tiLeHoanThanhNgay;

//    @JsonProperty("tlht_thang")
//    private Float tiLeHoanThanhThang;

    @JsonProperty("thuc_hien_ngay")
    private Float thucHienNgay;

//    @JsonProperty("tien_do_den_ngay")
//    private Float tienDoDenNgay;

    @JsonProperty("thang_truoc")
    private Float thangTruoc;

    @JsonProperty("nam_truoc")
    private Float namTruoc;

    @JsonProperty("cung_ky_ngay")
    private Float cungKyNgay;

    @JsonProperty("cung_ky_thang")
    private Float cungKyThang;

    @JsonProperty("cung_ky_nam")
    private Float cungKyNam;

    @JsonProperty("ngay_quydoi_thang")
    private Float ngayQuyDoiThang;

    @JsonProperty("hs_ngay")
    private Float heSoNgay;
}
