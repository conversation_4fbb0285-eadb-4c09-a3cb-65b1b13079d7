package nocsystem.indexmanager.models.Response.LogWebview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class ChiTietChuyenXeDTO {

    @JsonProperty("maChuyenXe")
    private String maChuyenXe;

    @JsonProperty("dvvc")
    private String dvvc;

    @JsonProperty("tuyen")
    private String tuyen;

    @JsonProperty("bienSoXe")
    private String bienSoXe;

    @JsonProperty("userLai")
    private String userLai;

    @JsonProperty("soDienThoai")
    private String soDienThoai;

    @JsonProperty("sanLuong")
    private Long sanLuong;

    @JsonProperty("trongLuong")
    private Float trongLuong;

    @JsonProperty("trongTai")
    private Long trongTai;

    @JsonProperty("tgChoKhaiThac")
    private Long tgChoKhaiThac;

    @JsonProperty("tgDuKien")
    private String tgDuKien;

    @JsonProperty("timeCheckin")
    private String timeCheckin;

    public ChiTietChuyenXeDTO(String maChuyenXe, String dvvc, String tuyen, String bienSoXe, String userLai, String soDienThoai, Long sanLuong, Float trongLuong, Long trongTai, Long tgChoKhaiThac, Long tgDuKien, String timeCheckin) {
        this.maChuyenXe = maChuyenXe;
        this.dvvc = dvvc;
        this.tuyen = tuyen;
        this.bienSoXe = bienSoXe;
        this.userLai = userLai;
        this.soDienThoai = soDienThoai;
        this.sanLuong = sanLuong;
        this.trongLuong = trongLuong;
        this.trongTai = trongTai;
        this.tgChoKhaiThac = tgChoKhaiThac;
        this.tgDuKien = chuyenNgayTuLongThanhString(tgDuKien);
        this.timeCheckin = timeCheckin;
    }

    public String chuyenNgayTuLongThanhString(Long ngay) {
        if (ngay != null) {
            Timestamp ts = new Timestamp(ngay);
            Date date = new Date(ts.getTime());
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm dd-MM-yyyy");
            return sdf.format(date);
        }
        return null;
    }
}
