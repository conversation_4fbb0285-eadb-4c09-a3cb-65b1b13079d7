package nocsystem.indexmanager.models.Response.LogWebview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class XeChoKhaiThacSumDTO {

    @JsonProperty("soLuongTTKT1")
    private Long soLuongTTKT1;

    @JsonProperty("khoiLuongTTKT1")
    private Double khoiLuongTTKT1;

    @JsonProperty("sanLuongTTKT1")
    private Long sanLuongTTKT1;

    @JsonProperty("soLuongTTKT2")
    private Long soLuongTTKT2;

    @JsonProperty("khoiLuongTTKT2")
    private Double khoiLuongTTKT2;

    @JsonProperty("sanLuongTTKT2")
    private Long sanLuongTTKT2;

    @JsonProperty("soLuongTTKT3")
    private Long soLuongTTKT3;

    @JsonProperty("khoiLuongTTKT3")
    private Double khoiLuongTTKT3;

    @JsonProperty("sanLuongTTKT3")
    private Long sanLuongTTKT3;

    @JsonProperty("soLuongTTKT4")
    private Long soLuongTTKT4;

    @JsonProperty("khoiLuongTTKT4")
    private Double khoiLuongTTKT4;

    @JsonProperty("sanLuongTTKT4")
    private Long sanLuongTTKT4;

    @JsonProperty("soLuongTTKT5")
    private Long soLuongTTKT5;

    @JsonProperty("khoiLuongTTKT5")
    private Double khoiLuongTTKT5;

    @JsonProperty("sanLuongTTKT5")
    private Long sanLuongTTKT5;

    @JsonProperty("soLuongMBCG")
    private Long soLuongMBCG;

    @JsonProperty("khoiLuongMBCG")
    private Double khoiLuongMBCG;

    @JsonProperty("sanLuongMBCG")
    private Long sanLuongMBCG;

    @JsonProperty("soLuongMNMG")
    private Long soLuongMNMG;

    @JsonProperty("khoiLuongMNMG")
    private Double khoiLuongMNMG;

    @JsonProperty("sanLuongMNMG")
    private Long sanLuongMNMG;
}
