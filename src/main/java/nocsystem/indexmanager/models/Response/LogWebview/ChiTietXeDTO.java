package nocsystem.indexmanager.models.Response.LogWebview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang.time.DateUtils;

import javax.security.auth.login.FailedLoginException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.TimeZone;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ChiTietXeDTO {

    @JsonProperty("maChuyenXe")
    private String maChuyenXe;

    @JsonProperty("bienSoXe")
    private String bienSoXe;

    @JsonProperty("trongLuong")
    private Float trongLuong;

    @JsonProperty("sanLuong")
    private Long sanLuong;

    @JsonProperty("tgCho")
    private Long tgCho;

    @JsonProperty("tgDuKienDen")
    private Date tgDuKienDen;


    public ChiTietXeDTO(String bienSoXe, Float trongLuong, Long tgCho) {
        this.bienSoXe = bienSoXe;
        this.trongLuong = trongLuong;
        this.tgCho = tgCho;
    }

    public ChiTietXeDTO(String bienSoXe, Date tgDuKienDen) {
        this.bienSoXe = bienSoXe;
        this.tgDuKienDen = tgDuKienDen;
    }

    public ChiTietXeDTO(String bienSoXe, Float trongLuong, Long sanLuong, Long tgCho, Long tgDuKienDen) {
        this.bienSoXe = bienSoXe;
        this.trongLuong = trongLuong;
        this.sanLuong = sanLuong;
        this.tgCho = tgCho;
        this.tgDuKienDen = null;
    }

    public ChiTietXeDTO(String maChuyenXe, String bienSoXe, Float trongLuong, Long sanLuong, Long tgCho, Long tgDuKienDen) {
        this.maChuyenXe = maChuyenXe;
        this.bienSoXe = bienSoXe;
        this.trongLuong = trongLuong;
        this.sanLuong = sanLuong;
        this.tgCho = tgCho;
        this.tgDuKienDen = null;
    }

    public String chuyenNgayTuLongThanhString(Long ngay) {
        if (ngay != null) {
            Timestamp ts = new Timestamp(ngay);
            Date date = new Date(ts.getTime());
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm dd-MM-yyyy");
            return sdf.format(date);
        }
        return null;
    }
}
