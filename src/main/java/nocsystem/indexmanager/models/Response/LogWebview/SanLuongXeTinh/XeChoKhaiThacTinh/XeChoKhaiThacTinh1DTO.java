package nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class XeChoKhaiThacTinh1DTO {
    private String ttkt;
    private Long duoi30p;
    private Long duoi1h;
    private Long duoi2h;
    private Long tren2h;
    private Long tong;


    public XeChoKhaiThacTinh1DTO() {
    }

    public XeChoKhaiThacTinh1DTO(String ttkt, Long duoi30p, Long duoi1h, Long duoi2h, Long tren2h, Long tong) {
        this.ttkt = ttkt;
        this.duoi30p = duoi30p;
        this.duoi1h = duoi1h;
        this.duoi2h = duoi2h;
        this.tren2h = tren2h;
        this.tong = tong;
    }

    public XeChoKhaiThacTinh1DTO(Long duoi30p, Long duoi1h, Long duoi2h, Long tren2h, Long tong) {
        this.duoi30p = duoi30p;
        this.duoi1h = duoi1h;
        this.duoi2h = duoi2h;
        this.tren2h = tren2h;
        this.tong = tong;
    }

    public String getTtkt() {
        return ttkt;
    }

    public void setTtkt(String ttkt) {
        this.ttkt = ttkt;
    }

    public Long getDuoi30p() {
        return duoi30p;
    }

    public void setDuoi30p(Long duoi30p) {
        this.duoi30p = duoi30p;
    }

    public Long getDuoi1h() {
        return duoi1h;
    }

    public void setDuoi1h(Long duoi1h) {
        this.duoi1h = duoi1h;
    }

    public Long getDuoi2h() {
        return duoi2h;
    }

    public void setDuoi2h(Long duoi2h) {
        this.duoi2h = duoi2h;
    }

    public Long getTren2h() {
        return tren2h;
    }

    public void setTren2h(Long tren2h) {
        this.tren2h = tren2h;
    }

    public Long getTong() {
        return tong;
    }

    public void setTong(Long tong) {
        this.tong = tong;
    }

}