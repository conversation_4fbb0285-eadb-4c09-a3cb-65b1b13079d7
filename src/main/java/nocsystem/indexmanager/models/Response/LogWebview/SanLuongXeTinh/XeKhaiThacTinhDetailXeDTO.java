package nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh;


public class XeKhaiThacTinhDetailXeDTO {
    private String maChuyenXe;
    private String bienSoXe;
    private String tuyen;
    private String dvvc;
    private String timeCheckin;
    private String userLai;
    private String soDienThoai;
    private Long sanLuong;
    private Double trongLuong;
    private Long trongTai;
    private Double thoiGianCho;

    public XeKhaiThacTinhDetailXeDTO() {
    }

    public XeKhaiThacTinhDetailXeDTO(String maChuyenXe, String bienSoXe, String tuyen, String dvvc, String timeCheckin, String userLai, String soDienThoai, Long sanLuong, Double trongLuong, Long trongTai, Double thoiGianCho) {
        this.maChuyenXe = maChuyenXe;
        this.bienSoXe = bienSoXe;
        this.tuyen = tuyen;
        this.dvvc = dvvc;
        this.timeCheckin = timeCheckin;
        this.userLai = userLai;
        this.soDienThoai = soDienThoai;
        this.sanLuong = sanLuong;
        this.trongLuong = trongLuong;
        this.trongTai = trongTai;
        this.thoiGianCho = thoiGianCho;
    }

    public XeKhaiThacTinhDetailXeDTO(String maChuyenXe, String bienSoXe, String tuyen, String dvvc, String timeCheckin, String userLai, String soDienThoai, Long sanLuong, Double trongLuong) {
        this.maChuyenXe = maChuyenXe;
        this.bienSoXe = bienSoXe;
        this.tuyen = tuyen;
        this.dvvc = dvvc;
        this.timeCheckin = timeCheckin;
        this.userLai = userLai;
        this.soDienThoai = soDienThoai;
        this.sanLuong = sanLuong;
        this.trongLuong = trongLuong;
    }

    public String getMaChuyenXe() {
        return maChuyenXe;
    }

    public void setMaChuyenXe(String maChuyenXe) {
        this.maChuyenXe = maChuyenXe;
    }

    public String getBienSoXe() {
        return bienSoXe;
    }

    public void setBienSoXe(String bienSoXe) {
        this.bienSoXe = bienSoXe;
    }

    public String getTuyen() {
        return tuyen;
    }

    public void setTuyen(String tuyen) {
        this.tuyen = tuyen;
    }

    public String getDvvc() {
        return dvvc;
    }

    public void setDvvc(String dvvc) {
        this.dvvc = dvvc;
    }

    public String getTimeCheckin() {
        return timeCheckin;
    }

    public void setTimeCheckin(String timeCheckin) {
        this.timeCheckin = timeCheckin;
    }

    public String getUserLai() {
        return userLai;
    }

    public void setUserLai(String userLai) {
        this.userLai = userLai;
    }

    public String getSoDienThoai() {
        return soDienThoai;
    }

    public void setSoDienThoai(String soDienThoai) {
        this.soDienThoai = soDienThoai;
    }

    public Long getSanLuong() {
        return sanLuong;
    }

    public void setSanLuong(Long sanLuong) {
        this.sanLuong = sanLuong;
    }

    public Double getTrongLuong() {
        return trongLuong;
    }

    public void setTrongLuong(Double trongLuong) {
        this.trongLuong = trongLuong;
    }

    public Long getTrongTai() {
        return trongTai;
    }

    public void setTrongTai(Long trongTai) {
        this.trongTai = trongTai;
    }

    public Double getThoiGianCho() {
        return thoiGianCho;
    }

    public void setThoiGianCho(Double thoiGianCho) {
        this.thoiGianCho = thoiGianCho;
    }
}
