package nocsystem.indexmanager.models.Response.LogWebview.SanLuongXeTinh.XeChoKhaiThacTinh;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class XeChoKhaiThacTinh2DTO {
    private String ttkt;
    private Double duoi30p;
    private Double duoi1h;
    private Double duoi2h;
    private Double tren2h;
    private Double tong;

    public XeChoKhaiThacTinh2DTO() {
    }

    public XeChoKhaiThacTinh2DTO(String ttkt, Double duoi30p, Double duoi1h, Double duoi2h, Double tren2h, Double tong) {
        this.ttkt = ttkt;
        this.duoi30p = (double) Math.round(duoi30p * 100) / 100;
        this.duoi1h = (double) Math.round(duoi1h * 100) / 100;
        this.duoi2h = (double) Math.round(duoi2h * 100) / 100;
        this.tren2h = (double) Math.round(tren2h * 100) / 100;
        this.tong = (double) Math.round(tong * 100) / 100;
    }

    public XeChoKhaiThacTinh2DTO(Double duoi30p, Double duoi1h, Double duoi2h, Double tren2h, Double tong) {
        this.duoi30p = (double) Math.round(duoi30p * 100) / 100;
        this.duoi1h = (double) Math.round(duoi1h * 100) / 100;
        this.duoi2h = (double) Math.round(duoi2h * 100) / 100;
        this.tren2h = (double) Math.round(tren2h * 100) / 100;
        this.tong = (double) Math.round(tong * 100) / 100;
    }

    public String getTtkt() {
        return ttkt;
    }

    public void setTtkt(String ttkt) {
        this.ttkt = ttkt;
    }

    public Double getDuoi30p() {
        return duoi30p;
    }

    public void setDuoi30p(Double duoi30p) {
        this.duoi30p = duoi30p;
    }

    public Double getDuoi1h() {
        return duoi1h;
    }

    public void setDuoi1h(Double duoi1h) {
        this.duoi1h = duoi1h;
    }

    public Double getDuoi2h() {
        return duoi2h;
    }

    public void setDuoi2h(Double duoi2h) {
        this.duoi2h = duoi2h;
    }

    public Double getTren2h() {
        return tren2h;
    }

    public void setTren2h(Double tren2h) {
        this.tren2h = tren2h;
    }

    public Double getTong() {
        return tong;
    }

    public void setTong(Double tong) {
        this.tong = tong;
    }
}