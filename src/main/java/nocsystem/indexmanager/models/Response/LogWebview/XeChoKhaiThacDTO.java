package nocsystem.indexmanager.models.Response.LogWebview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
public class XeChoKhaiThacDTO {

    @JsonProperty("ttkt")
    private String ttkt;

    @JsonProperty("soLuongDuoi30p")
    private Long soLuongDuoi30p;

    @JsonProperty("soLuongDuoi1h")
    private Long soLuongDuoi1h;

    @JsonProperty("soLuongDuoi2h")
    private Long soLuongDuoi2h;

    @JsonProperty("soLuongTren2h")
    private Long soLuongTren2h;

    @JsonProperty("soLuongTong")
    private Long soLuongTong;

    @JsonProperty("khoiLuongDuoi30p")
    private Double khoiLuongDuoi30p;

    @JsonProperty("khoiLuongDuoi1h")
    private Double khoiLuongDuoi1h;

    @JsonProperty("khoiLuongDuoi2h")
    private Double khoiLuongDuoi2h;

    @JsonProperty("khoiLuongTren2h")
    private Double khoiLuongTren2h;

    @JsonProperty("khoiLuongTong")
    private Double khoiLuongTong;

    @JsonProperty("sanLuongDuoi30p")
    private Long sanLuongDuoi30p;

    @JsonProperty("sanLuongDuoi1h")
    private Long sanLuongDuoi1h;

    @JsonProperty("sanLuongDuoi2h")
    private Long sanLuongDuoi2h;

    @JsonProperty("sanLuongTren2h")
    private Long sanLuongTren2h;

    @JsonProperty("sanLuongTong")
    private Long sanLuongTong;

    public XeChoKhaiThacDTO() {
    }

    public XeChoKhaiThacDTO(String ttkt, Long soLuongDuoi30p, Long soLuongDuoi1h, Long soLuongDuoi2h, Long soLuongTren2h, Long soLuongTong, Double khoiLuongDuoi30p, Double khoiLuongDuoi1h, Double khoiLuongDuoi2h, Double khoiLuongTren2h, Double khoiLuongTong, Long sanLuongDuoi30p, Long sanLuongDuoi1h, Long sanLuongDuoi2h, Long sanLuongTren2h, Long sanLuongTong) {
        this.ttkt = ttkt;
        this.soLuongDuoi30p = soLuongDuoi30p != null ? soLuongDuoi30p : 0;
        this.soLuongDuoi1h = soLuongDuoi1h != null ? soLuongDuoi1h : 0;
        this.soLuongDuoi2h = soLuongDuoi2h != null ? soLuongDuoi2h : 0;
        this.soLuongTren2h = soLuongTren2h != null ? soLuongTren2h : 0;
        this.soLuongTong = soLuongTong != null ? soLuongTong : 0;
        this.khoiLuongDuoi30p = khoiLuongDuoi30p != null ? (double) Math.round(khoiLuongDuoi30p * 100.0) / 100.0 : 0.0;
        this.khoiLuongDuoi1h = khoiLuongDuoi1h != null ? (double) Math.round(khoiLuongDuoi1h * 100.0) / 100.0 : 0.0;
        this.khoiLuongDuoi2h = khoiLuongDuoi2h != null ? (double) Math.round(khoiLuongDuoi2h * 100.0) / 100.0 : 0.0;
        this.khoiLuongTren2h = khoiLuongTren2h != null ? (double) Math.round(khoiLuongTren2h * 100.0) / 100.0 : 0.0;
        this.khoiLuongTong = khoiLuongTong != null ? (double) Math.round(khoiLuongTong * 100.0) / 100.0 : 0.0;
        this.sanLuongDuoi30p = sanLuongDuoi30p != null ? sanLuongDuoi30p : 0L;
        this.sanLuongDuoi1h = sanLuongDuoi1h != null ? sanLuongDuoi1h : 0L;
        this.sanLuongDuoi2h = sanLuongDuoi2h != null ? sanLuongDuoi2h : 0L;
        this.sanLuongTren2h = sanLuongTren2h != null ? sanLuongTren2h : 0L;
        this.sanLuongTong =  sanLuongTong != null ? sanLuongTong : 0L;
    }

    public XeChoKhaiThacDTO(Long soLuongDuoi30p, Long soLuongDuoi1h, Long soLuongDuoi2h, Long soLuongTren2h, Long soLuongTong, Double khoiLuongDuoi30p, Double khoiLuongDuoi1h, Double khoiLuongDuoi2h, Double khoiLuongTren2h, Double khoiLuongTong, Long sanLuongDuoi30p, Long sanLuongDuoi1h, Long sanLuongDuoi2h, Long sanLuongTren2h, Long sanLuongTong) {
        this.soLuongDuoi30p = soLuongDuoi30p != null ? soLuongDuoi30p : 0;
        this.soLuongDuoi1h = soLuongDuoi1h != null ? soLuongDuoi1h : 0;
        this.soLuongDuoi2h = soLuongDuoi2h != null ? soLuongDuoi2h : 0;
        this.soLuongTren2h = soLuongTren2h != null ? soLuongTren2h : 0;
        this.soLuongTong = soLuongTong != null ? soLuongTong : 0;
        this.khoiLuongDuoi30p = khoiLuongDuoi30p != null ? (double) Math.round(khoiLuongDuoi30p * 100.0) / 100.0 : 0.0;
        this.khoiLuongDuoi1h = khoiLuongDuoi1h != null ? (double) Math.round(khoiLuongDuoi1h * 100.0) / 100.0 : 0.0;
        this.khoiLuongDuoi2h = khoiLuongDuoi2h != null ? (double) Math.round(khoiLuongDuoi2h * 100.0) / 100.0 : 0.0;
        this.khoiLuongTren2h = khoiLuongTren2h != null ? (double) Math.round(khoiLuongTren2h * 100.0) / 100.0 : 0.0;
        this.khoiLuongTong = khoiLuongTong != null ? (double) Math.round(khoiLuongTong * 100.0) / 100.0 : 0.0;
        this.sanLuongDuoi30p = sanLuongDuoi30p != null ? sanLuongDuoi30p : 0L;
        this.sanLuongDuoi1h = sanLuongDuoi1h != null ? sanLuongDuoi1h : 0L;
        this.sanLuongDuoi2h = sanLuongDuoi2h != null ? sanLuongDuoi2h : 0L;
        this.sanLuongTren2h = sanLuongTren2h != null ? sanLuongTren2h : 0L;
        this.sanLuongTong =  sanLuongTong != null ? sanLuongTong : 0L;
    }
}