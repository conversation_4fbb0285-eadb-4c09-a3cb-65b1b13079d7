package nocsystem.indexmanager.models.Response.LogWebview;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class XeDangKhaiThacDTO {

    @JsonProperty("TTKT")
    private String TTKT;

    @JsonProperty("soLuong")
    private Long soLuong;

    @JsonProperty("trongLuong")
    private Double trongLuong;

    @JsonProperty("sanLuong")
    private Long sanLuong;

    public XeDangKhaiThacDTO(Long soLuong, Double trongLuong, Long sanLuong) {
        this.soLuong = soLuong;
        this.trongLuong = trongLuong;
        this.sanLuong = sanLuong;
    }
}
