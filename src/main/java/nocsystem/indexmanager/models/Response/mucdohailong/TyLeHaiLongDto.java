package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

import java.text.DecimalFormat;
import java.time.LocalDate;

@Getter
@Setter
public class TyLeHaiLongDto {
    private static final DecimalFormat df = new DecimalFormat("#.##");
    private LocalDate ngayBaoCao;

    private String vung;

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongDG;

    private Long tongDGHaiLong;

    private Long tongDGKhongHaiLong;

    private Double tyLeHL;

    private Boolean danhGiaTLHLTong;

    private Long tongDGKhauNhan;

    private Long tongDGKhauNhanHaiLong;

    private Long tongDGKhauNhanKhongHaiLong;

    private Double tyLeHLKhauNhan;

    private Boolean danhGiaTLHLKhauNhan;

    private Long tongDGKhauGiao;

    private Long tongDGKhauGiaoHaiLong;

    private Long tongDGKhauGiaoKhongHaiLong;

    private Double tyLeHLKhauGiao;

    private Boolean danhGiaTLHLKhauGiao;

    private Long tongDGKenhBT;

    private Long tongDGKenhBTHaiLong;

    private Long tongDGKenhBC;

    private Long tongDGKenhBCHaiLong;

    public TyLeHaiLongDto() {
    }

    public TyLeHaiLongDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen, Long tongDG, Long tongDGHaiLong,
                          Long tongDGKhauNhan, Long tongDGKhauNhanHaiLong,
                          Long tongDGKhauGiao, Long tongDGKhauGiaoHaiLong) {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongDG = tongDG;
        this.tongDGHaiLong = tongDGHaiLong;
        this.tongDGKhongHaiLong = tongDG - tongDGHaiLong;
        this.tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
        this.tongDGKhauNhan = tongDGKhauNhan;
        this.tongDGKhauNhanHaiLong = tongDGKhauNhanHaiLong;
        this.tongDGKhauNhanKhongHaiLong = tongDGKhauNhan - tongDGKhauNhanHaiLong;
        this.tyLeHLKhauNhan = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDGKhauNhan, this.tongDGKhauNhanHaiLong);
        this.tongDGKhauGiao = tongDGKhauGiao;
        this.tongDGKhauGiaoHaiLong = tongDGKhauGiaoHaiLong;
        this.tongDGKhauGiaoKhongHaiLong = tongDGKhauGiao - tongDGKhauGiaoHaiLong;
        this.tyLeHLKhauGiao = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDGKhauGiao, this.tongDGKhauGiaoHaiLong);
    }


    public TyLeHaiLongDto(Long tongDG, Long tongDGHaiLong, Long tongDGKhauNhan, Long tongDGKhauNhanHaiLong, Long tongDGKhauGiao, Long tongDGKhauGiaoHaiLong, Long tongDGKenhBT, Long tongDGKenhBTHaiLong, Long tongDGKenhBC, Long tongDGKenhBCHaiLong) {
        this.tongDG = tongDG;
        this.tongDGHaiLong = tongDGHaiLong;
        this.tongDGKhauNhan = tongDGKhauNhan;
        this.tongDGKhauNhanHaiLong = tongDGKhauNhanHaiLong;
        this.tongDGKhauGiao = tongDGKhauGiao;
        this.tongDGKhauGiaoHaiLong = tongDGKhauGiaoHaiLong;
        this.tongDGKenhBT = tongDGKenhBT;
        this.tongDGKenhBTHaiLong = tongDGKenhBTHaiLong;
        this.tongDGKenhBC = tongDGKenhBC;
        this.tongDGKenhBCHaiLong = tongDGKenhBCHaiLong;
    }
}
