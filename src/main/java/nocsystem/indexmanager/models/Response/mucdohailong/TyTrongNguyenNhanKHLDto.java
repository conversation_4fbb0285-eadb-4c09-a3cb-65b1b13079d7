package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

@Getter
@Setter
public class TyTrongNguyenNhanKHLDto {
    private String name;
    private Double tyTrong;
    private Long tongDGKhongHLTheoNhom;

    public TyTrongNguyenNhanKHLDto() {
    }

    public TyTrongNguyenNhanKHLDto(String name, Long tongDGKhongHaiLong, Long tongDG, Long tongDGKhongHLTheoNhom) {
        this.name = name;
        this.tyTrong = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, tongDGKhongHaiLong);
        this.tongDGKhongHLTheoNhom = tongDGKhongHLTheoNhom;
    }
}
