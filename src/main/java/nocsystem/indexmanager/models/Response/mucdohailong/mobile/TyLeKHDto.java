package nocsystem.indexmanager.models.Response.mucdohailong.mobile;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

@Getter
@Setter
public class TyLeKHDto {
    private String name;

    private Long tu = 0L;

    private Long mau = 0L;

    private Double tyLe = (double) 0;

    private Long tuKhauNhan = 0L;

    private Long mauKhauNhan = 0L;

    private Double tyLeKhauNhan = (double) 0;

    private Long tuKhauGiao = 0L;

    private Long mauKhauGiao = 0L;

    private Double tyLeKhauGiao = (double) 0;

//    private Long tongDG;
//
//    private Long tongDGHaiLong;
//
//    private Double tyLeHL;
//
//    private Long tongDGKhauNhan;
//
//    private Long tongDGKhauNhanHaiLong;
//
//    private Double tyLeHLKhauNhan;
//
//    private Long tongDGKhauGiao;
//
//    private Long tongDGKhauGiaoHaiLong;
//
//    private Double tyLeHLKhauGiao;

    public TyLeKHDto() {
    }

    public TyLeKHDto(String name, Long tongKhaoSat, Long tongPhanHoi, Long tongKhaoSatKhauNhan, Long tongPhanHoiKhauNhan, Long tongKhaoSatKhauGiao, Long tongPhanHoiKhauGiao, Long tongDG, Long tongDGHaiLong, Long tongDGKhauNhan, Long tongDGKhauNhanHaiLong, Long tongDGKhauGiao, Long tongDGKhauGiaoHaiLong) {
        switch (name) {
            case "tyLeKHPhanHoi":
                this.name = name;
                this.mau = (null == tongKhaoSat || tongKhaoSat == 0) ? 0 : tongKhaoSat;
                this.tu = (null == tongPhanHoi || tongPhanHoi == 0) ? 0 : tongPhanHoi;
                this.tyLe = MucDoHaiLongUtils.getTyLeHaiLong(this.mau, this.tu);
                this.mauKhauNhan = (null == tongKhaoSatKhauNhan || tongKhaoSatKhauNhan == 0) ? 0 : tongKhaoSatKhauNhan;
                this.tuKhauNhan = (null == tongPhanHoiKhauNhan || tongPhanHoiKhauNhan == 0) ? 0 : tongPhanHoiKhauNhan;
                this.tyLeKhauNhan = MucDoHaiLongUtils.getTyLeHaiLong(this.mauKhauNhan, this.tuKhauNhan);
                this.mauKhauGiao = (null == tongKhaoSatKhauGiao || tongKhaoSatKhauGiao == 0) ? 0 : tongKhaoSatKhauGiao;
                this.tuKhauGiao = (null == tongPhanHoiKhauGiao || tongPhanHoiKhauGiao == 0) ? 0 : tongPhanHoiKhauGiao;
                this.tyLeKhauGiao = MucDoHaiLongUtils.getTyLeHaiLong(this.mauKhauGiao, this.tuKhauGiao);
                break;
            case "tyLeKHHaiLong":
                this.name = name;
                this.mau = (null == tongDG || tongDG == 0) ? 0 : tongDG;
                this.tu = (null == tongDGHaiLong || tongDGHaiLong == 0) ? 0 : tongDGHaiLong;
                this.tyLe = MucDoHaiLongUtils.getTyLeHaiLong(this.mau, this.tu);
                this.mauKhauNhan = (null == tongDGKhauNhan || tongDGKhauNhan == 0) ? 0 : tongDGKhauNhan;
                this.tuKhauNhan = (null == tongDGKhauNhanHaiLong || tongDGKhauNhanHaiLong == 0) ? 0 : tongDGKhauNhanHaiLong;
                this.tyLeKhauNhan = MucDoHaiLongUtils.getTyLeHaiLong(this.mauKhauNhan, this.tuKhauNhan);
                this.mauKhauGiao = (null == tongDGKhauGiao || tongDGKhauGiao == 0) ? 0 : tongDGKhauGiao;
                this.tuKhauGiao = (null == tongDGKhauGiaoHaiLong || tongDGKhauGiaoHaiLong == 0) ? 0 : tongDGKhauGiaoHaiLong;
                this.tyLeKhauGiao = MucDoHaiLongUtils.getTyLeHaiLong(this.mauKhauGiao, this.tuKhauGiao);
                break;
        }
    }

}
