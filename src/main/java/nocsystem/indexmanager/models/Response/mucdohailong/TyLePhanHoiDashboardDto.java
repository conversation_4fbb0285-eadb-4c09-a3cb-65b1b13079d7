package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

@Getter
@Setter
public class TyLePhanHoiDashboardDto {
    private String type;

    private Double tyLePhanHoi;

    private Long tongPhanHoi;

    private Long tongMauKhaoSat;

    private Double tangTruongThang;

    public TyLePhanHoiDashboardDto() {
    }

    public TyLePhanHoiDashboardDto(TyLePhanHoiDto tyLePhanHoiDto, String type) {
        switch (type) {
            case "Tong":
                this.type = type;
                this.tongPhanHoi = (null == tyLePhanHoiDto.getTongPhanHoi() || tyLePhanHoiDto.getTongPhanHoi() == 0) ? 0 : tyLePhanHoiDto.getTongPhanHoi();
                this.tongMauKhaoSat = (null == tyLePhanHoiDto.getTongKhaoSat() || tyLePhanHoiDto.getTongKhaoSat() == 0) ? 0 : tyLePhanHoiDto.getTongKhaoSat();
                this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(this.tongMauKhaoSat, this.tongPhanHoi);
                break;
            case "KhauGiao":
                this.type = type;
                this.tongPhanHoi = (null == tyLePhanHoiDto.getTongPhanHoiKhauGiao() || tyLePhanHoiDto.getTongPhanHoiKhauGiao() == 0) ? 0 : tyLePhanHoiDto.getTongPhanHoiKhauGiao();
                this.tongMauKhaoSat = (null == tyLePhanHoiDto.getTongKhaoSatKhauGiao() || tyLePhanHoiDto.getTongKhaoSatKhauGiao() == 0) ? 0 : tyLePhanHoiDto.getTongKhaoSatKhauGiao();
                this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(this.tongMauKhaoSat, this.tongPhanHoi);
                break;
            case "KhauNhan":
                this.type = type;
                this.tongPhanHoi = (null == tyLePhanHoiDto.getTongPhanHoiKhauNhan() || tyLePhanHoiDto.getTongPhanHoiKhauNhan() == 0) ? 0 : tyLePhanHoiDto.getTongPhanHoiKhauNhan();
                this.tongMauKhaoSat = (null == tyLePhanHoiDto.getTongKhaoSatKhauNhan() || tyLePhanHoiDto.getTongKhaoSatKhauNhan() == 0) ? 0 : tyLePhanHoiDto.getTongKhaoSatKhauNhan();
                this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(this.tongMauKhaoSat, this.tongPhanHoi);
                break;
            case "KenhBT":
                this.type = type;
                this.tongPhanHoi = (null == tyLePhanHoiDto.getTongPhanHoiKenhBT() || tyLePhanHoiDto.getTongPhanHoiKenhBT() == 0) ? 0 : tyLePhanHoiDto.getTongPhanHoiKenhBT();
                this.tongMauKhaoSat = (null == tyLePhanHoiDto.getTongKhaoSatKenhBT() || tyLePhanHoiDto.getTongKhaoSatKenhBT() == 0) ? 0 : tyLePhanHoiDto.getTongKhaoSatKenhBT();
                this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(this.tongMauKhaoSat, this.tongPhanHoi);
                break;
            case "KenhBC":
                this.type = type;
                this.tongPhanHoi = (null == tyLePhanHoiDto.getTongPhanHoiKenhBC() || tyLePhanHoiDto.getTongPhanHoiKenhBC() == 0) ? 0 : tyLePhanHoiDto.getTongPhanHoiKenhBC();
                this.tongMauKhaoSat = (null == tyLePhanHoiDto.getTongKhaoSatKenhBC() || tyLePhanHoiDto.getTongKhaoSatKenhBC() == 0) ? 0 : tyLePhanHoiDto.getTongKhaoSatKenhBC();
                this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(this.tongMauKhaoSat, this.tongPhanHoi);
                break;
        }
    }
}
