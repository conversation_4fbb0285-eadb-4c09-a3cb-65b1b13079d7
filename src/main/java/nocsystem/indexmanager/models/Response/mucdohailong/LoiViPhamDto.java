package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LoiViPhamDto {
    private String vung;

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongDanhGiaNghiepVu;

    private Long tongDanhGiaCLPV;

    private Long tongDanhGiaCLDV;

    private Long tongDanhGiaKiNang;

    private Long tongDanhGiaLoiIchCN;

    private Long tongDanhGiaKhac;

    private Long tong;

    private Long tongThaiDoKem;

    private Long tongGachTrangThaiAo;

    private Long tongLoiNghiemTrong;

    public LoiViPhamDto() {
    }

    public LoiViPhamDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen, Long tongDanhGiaNghiepVu, Long tongDanhGiaCLPV, Long tongDanhGiaCLDV, Long tongDanhGiaKiNang, Long tongDanhGiaLoiIchCN, Long tongDanhGiaKhac, Long tongThaiDoKem, Long tongGachTrangThaiAo, Long tongLoiNghiemTrong) {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongDanhGiaNghiepVu = tongDanhGiaNghiepVu;
        this.tongDanhGiaCLPV = tongDanhGiaCLPV;
        this.tongDanhGiaCLDV = tongDanhGiaCLDV;
        this.tongDanhGiaKiNang = tongDanhGiaKiNang;
        this.tongDanhGiaLoiIchCN = tongDanhGiaLoiIchCN;
        this.tongDanhGiaKhac = tongDanhGiaKhac;
        this.tong = tongDanhGiaNghiepVu + tongDanhGiaCLPV + tongDanhGiaCLDV + tongDanhGiaKiNang + tongDanhGiaLoiIchCN + tongDanhGiaKhac;
        this.tongThaiDoKem = tongThaiDoKem;
        this.tongGachTrangThaiAo = tongGachTrangThaiAo;
        this.tongLoiNghiemTrong = tongLoiNghiemTrong;
    }

    public LoiViPhamDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen, Long tongDanhGiaNghiepVu, Long tongDanhGiaCLPV, Long tongDanhGiaCLDV, Long tongDanhGiaKiNang, Long tongDanhGiaLoiIchCN, Long tongDanhGiaKhac, Long tong, Long tongThaiDoKem, Long tongGachTrangThaiAo, Long tongLoiNghiemTrong) {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongDanhGiaNghiepVu = tongDanhGiaNghiepVu;
        this.tongDanhGiaCLPV = tongDanhGiaCLPV;
        this.tongDanhGiaCLDV = tongDanhGiaCLDV;
        this.tongDanhGiaKiNang = tongDanhGiaKiNang;
        this.tongDanhGiaLoiIchCN = tongDanhGiaLoiIchCN;
        this.tongDanhGiaKhac = tongDanhGiaKhac;
        this.tong = tong;
        this.tongThaiDoKem = tongThaiDoKem;
        this.tongGachTrangThaiAo = tongGachTrangThaiAo;
        this.tongLoiNghiemTrong = tongLoiNghiemTrong;
    }
}
