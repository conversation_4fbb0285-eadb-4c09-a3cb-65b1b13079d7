package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;

@Getter
@Setter
public class TyLeHaiLongWithKPIDashboardDto {

    private static final DecimalFormat df = new DecimalFormat("#.##");

    private String name;

    private Double tyLeHL;

    private Double tyLeHLKenhBT;

    private Double tyLeHLKenhBC;

    private Double tangTruongCungKy;

    private boolean danhGiaTLHL;

    private Double kpi;

    public TyLeHaiLongWithKPIDashboardDto() {
    }

    public TyLeHaiLongWithKPIDashboardDto(LocalDate ngayBaoCao, String name,
                                          Long tongDG, Long tongDGHaiLong,
                                          Long tongDGKenhBT, Long tongDGKenhBTHaiLong,
                                          Long tongDGKenhBC, Long tongDGKenhBCHaiLong) throws IOException {
        this.name = name;
        this.tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, tongDGHaiLong);
        this.tyLeHLKenhBT = MucDoHaiLongUtils.getTyLeHaiLong(tongDGKenhBT, tongDGKenhBTHaiLong);
        this.tyLeHLKenhBC = MucDoHaiLongUtils.getTyLeHaiLong(tongDGKenhBC, tongDGKenhBCHaiLong);
//        this.kpi = MucDoHaiLongUtils.getKPICurrentMonth(ngayBaoCao);
    }

    public TyLeHaiLongWithKPIDashboardDto(String name,
                                          Long tongDG, Long tongDGHaiLong,
                                          Long tongDGKenhBT, Long tongDGKenhBTHaiLong,
                                          Long tongDGKenhBC, Long tongDGKenhBCHaiLong, Double kpi) throws IOException {
        this.name = name;
        this.tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, tongDGHaiLong);
        this.tyLeHLKenhBT = MucDoHaiLongUtils.getTyLeHaiLong(tongDGKenhBT, tongDGKenhBTHaiLong);
        this.tyLeHLKenhBC = MucDoHaiLongUtils.getTyLeHaiLong(tongDGKenhBC, tongDGKenhBCHaiLong);
        this.kpi = kpi;
    }
}
