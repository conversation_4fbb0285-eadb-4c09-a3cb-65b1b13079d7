package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.HashMap;

@Getter
@Setter
public class TyLePhanHoiExcelDto {
    private static final DecimalFormat df = new DecimalFormat("#.##");

    private String vung;

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongKhaoSat;

    private Long tongPhanHoi;

    private Long tongKHTuDanhGia;

    private Double tyLePhanHoi;

    private Long tongKhaoSatKhauGiao;

    private Long tongPhanHoiKhauGiao;

    private Long tongKHTuDanhGiaKhauGiao;

    private Double tyLePhanHoiKhauGiao;

    private Long tongKhaoSatKhauNhan;

    private Long tongPhanHoiKhauNhan;

    private Long tongKHTuDanhGiaKhauNhan;

    private Double tyLePhanHoiKhauNhan;

    private Long tongKhaoSatKenhBT;

    private Long tongPhanHoiKenhBT;

    private Long tongKHTuDanhGiaKenhBT;

    private Long tongKhaoSatKenhBC;

    private Long tongPhanHoiKenhBC;

    private Long tongKHTuDanhGiaKenhBC;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public TyLePhanHoiExcelDto() {
    }

    public TyLePhanHoiExcelDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen,
                               Long tongKhaoSat, Long tongPhanHoi, Long tongKHTuDanhGia,
                               Long tongKhaoSatKhauGiao, Long tongPhanHoiKhauGiao, Long tongKHTuDanhGiaKhauGiao,
                               Long tongKhaoSatKhauNhan, Long tongPhanHoiKhauNhan, Long tongKHTuDanhGiaKhauNhan) throws NoSuchFieldException, IllegalAccessException {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongKhaoSat = tongKhaoSat - tongKHTuDanhGia;
        this.tongPhanHoi = tongPhanHoi - tongKHTuDanhGia;
        this.tongKHTuDanhGia = tongKHTuDanhGia;
        Double tyLePH;
        if (tongKhaoSat > 0 && tongPhanHoi > 0) {
            tyLePH = Double.parseDouble(df.format((double) tongPhanHoi / tongKhaoSat * 100));
        } else {
            tyLePH = (double) 0;
        }
        this.tyLePhanHoi = tyLePH;
        this.tongKhaoSatKhauNhan = tongKhaoSatKhauNhan - tongKHTuDanhGiaKhauNhan;
        this.tongPhanHoiKhauNhan = tongPhanHoiKhauNhan - tongKHTuDanhGiaKhauNhan;
        this.tongKHTuDanhGiaKhauNhan = tongKHTuDanhGiaKhauNhan;
        Double tyLePHKhauNhan;
        if (tongKhaoSatKhauNhan > 0 && tongPhanHoiKhauNhan > 0) {
            tyLePHKhauNhan = Double.parseDouble(df.format((double) tongPhanHoiKhauNhan / tongKhaoSatKhauNhan * 100));
        } else {
            tyLePHKhauNhan = (double) 0;
        }
        this.tyLePhanHoiKhauNhan = tyLePHKhauNhan;
        this.tongKhaoSatKhauGiao = tongKhaoSatKhauGiao - tongKHTuDanhGiaKhauGiao;
        this.tongPhanHoiKhauGiao = tongPhanHoiKhauGiao - tongKHTuDanhGiaKhauGiao;
        this.tongKHTuDanhGiaKhauGiao = tongKHTuDanhGiaKhauGiao;
        Double tyLePHKhauGiao;
        if (tongKhaoSatKhauGiao > 0 && tongPhanHoiKhauGiao > 0) {
            tyLePHKhauGiao = Double.parseDouble(df.format((double) tongPhanHoiKhauGiao / tongKhaoSatKhauGiao * 100));
        } else {
            tyLePHKhauGiao = (double) 0;
        }
        this.tyLePhanHoiKhauGiao = tyLePHKhauGiao;
        this.objectHashMap = convert(this);
    }

    public TyLePhanHoiExcelDto(String maChiNhanh,
                               Long tongKhaoSat, Long tongPhanHoi, Long tongKHTuDanhGia,
                               Long tongKhaoSatKhauGiao, Long tongPhanHoiKhauGiao, Long tongKHTuDanhGiaKhauGiao,
                               Long tongKhaoSatKhauNhan, Long tongPhanHoiKhauNhan, Long tongKHTuDanhGiaKhauNhan) throws NoSuchFieldException, IllegalAccessException {
        this.maChiNhanh = maChiNhanh;
        this.tongKhaoSat = tongKhaoSat;
        this.tongPhanHoi = tongPhanHoi;
        this.tongKHTuDanhGia = tongKHTuDanhGia;
        this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSat + tongKHTuDanhGia, tongPhanHoi + tongKHTuDanhGia);
        this.tongKhaoSatKhauGiao = tongKhaoSatKhauGiao;
        this.tongPhanHoiKhauGiao = tongPhanHoiKhauGiao;
        this.tongKHTuDanhGiaKhauGiao = tongKHTuDanhGiaKhauGiao;
        this.tyLePhanHoiKhauGiao = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSatKhauGiao + tongKHTuDanhGiaKhauGiao, tongPhanHoiKhauGiao + tongKHTuDanhGiaKhauGiao);
        this.tongKhaoSatKhauNhan = tongKhaoSatKhauNhan;
        this.tongPhanHoiKhauNhan = tongPhanHoiKhauNhan;
        this.tongKHTuDanhGiaKhauNhan = tongKHTuDanhGiaKhauNhan;
        this.tyLePhanHoiKhauNhan = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSatKhauNhan + tongKHTuDanhGiaKhauNhan, tongPhanHoiKhauNhan + tongKHTuDanhGiaKhauNhan);
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        HashMap<String, Object> map = new HashMap<>();
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            if (!field.getName().equals("objectHashMap")) {
                map.put(field.getName(), fieldValue);
            }
            i++;
        }
        return map;
    }
}
