package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;

@Getter
@Setter
public class TyLeHaiLongChiTietExcelDto {
    private String ngayKhaoSat;

    private String ngayKHPhanHoi;

    private String kenhGuiKS;

    private String kenhPhanHoi;

    private String maBillDanhGia;

    private String maTrangThai;

    private String thoiGianCapNhatTT;

    private String lyDoPhatKTC;

    private String maKH;

    private String maDoiTac;

    private String nhomKH;

    private String doiTuongKH;

    private String doiTuongKHDanhGia;

    private String kqDanhGiaMDHL;

    private String kqKSDonGiaoThatBai;

    private String nnKhongHL;

    private String nnKhongHLSauXacMinh;

    private String phanLoaiDG;

    private String maBuuTa;

    private String tenBuuTa;

    private String sdtBuuTa;

    private String chucDanh;

    private String vung;

    private String chiNhanh;

    private String buuCuc;

    private String khauDG;

    private String yeuCauDvi;

    private String lyDoDviYCXacMinh;

    private String ndXacMinhVoiKH;

    private String trangThaiXuLy;

    private String nguoiXacMinh;

    private String maKhieuNaiPhatSinh;

    private String ngayXacMinh;

    private String chiTietNguyenNhanKHL;

    private String quyHoachLoiTheoThe;

    private String kenhBaoCao;

    private Integer soLoi;

    // first mile
    private String fmTGNhanTC;

    private String fmTGLaiXeXacNhanBG;

    private Double fmTongKhau;

    private Double fmChenhLechKPI;

    private String fmDanhGia;

    // middle mile
    private String mmTGLaiXeXacNhanBG;

    private String mmTGBCPXacNhanBG;

    private Double mmTongKhau;

    private Double mmChenhLechKPI;

    private String mmDanhGia;

    // last mile
    private String lmTGBCPXacNhanBG;

    private String lmTGPhatLanDau;

    private Double lmTongKhau;

    private Double lmChenhLechKPI;

    private String lmDanhGia;

    // toan trinh buu gui
    private Double tgTTThucTe;

    private Double tgChiTieuPhatLanDau;

    private Double chenhLechKPI;

    private String danhGia;


    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public TyLeHaiLongChiTietExcelDto() {
    }

    public TyLeHaiLongChiTietExcelDto(String ngayKhaoSat, String ngayKHPhanHoi, String kenhGuiKS, String kenhPhanHoi, String maBillDanhGia, String maTrangThai, String thoiGianCapNhatTT, String lyDoPhatKTC, String maKH, String maDoiTac, String nhomKH, String doiTuongKH, String doiTuongKHDanhGia, String kqDanhGiaMDHL, String kqKSDonGiaoThatBai, String nnKhongHL, String nnKhongHLSauXacMinh, String phanLoaiDG, String maBuuTa, String tenBuuTa, String sdtBuuTa, String chucDanh, String vung, String chiNhanh, String buuCuc, String khauDG, String yeuCauDvi, String lyDoDviYCXacMinh, String ndXacMinhVoiKH, String trangThaiXuLy, String nguoiXacMinh, String maKhieuNaiPhatSinh, String ngayXacMinh, String chiTietNguyenNhanKHL, String quyHoachLoiTheoThe, String kenhBaoCao, Integer soLoi, String fmTGNhanTC, String fmTGLaiXeXacNhanBG, Double fmTongKhau, Double fmChenhLechKPI, String fmDanhGia, String mmTGLaiXeXacNhanBG, String mmTGBCPXacNhanBG, Double mmTongKhau, Double mmChenhLechKPI, String mmDanhGia, String lmTGBCPXacNhanBG, String lmTGPhatLanDau, Double lmTongKhau, Double lmChenhLechKPI, String lmDanhGia, Double tgTTThucTe, Double tgChiTieuPhatLanDau, Double chenhLechKPI, String danhGia) throws NoSuchFieldException, IllegalAccessException {
        this.ngayKhaoSat = ngayKhaoSat;
        this.ngayKHPhanHoi = ngayKHPhanHoi;
        this.kenhGuiKS = kenhGuiKS;
        this.kenhPhanHoi = kenhPhanHoi;
        this.maBillDanhGia = maBillDanhGia;
        this.maTrangThai = maTrangThai;
        this.thoiGianCapNhatTT = thoiGianCapNhatTT;
        this.lyDoPhatKTC = lyDoPhatKTC;
        this.maKH = maKH;
        this.maDoiTac = maDoiTac;
        this.nhomKH = nhomKH;
        this.doiTuongKH = doiTuongKH;
        this.doiTuongKHDanhGia = doiTuongKHDanhGia;
        this.kqDanhGiaMDHL = kqDanhGiaMDHL;
        this.kqKSDonGiaoThatBai = kqKSDonGiaoThatBai;
        this.nnKhongHL = nnKhongHL;
        this.nnKhongHLSauXacMinh = nnKhongHLSauXacMinh;
        this.phanLoaiDG = phanLoaiDG;
        this.maBuuTa = maBuuTa;
        this.tenBuuTa = tenBuuTa;
        this.sdtBuuTa = sdtBuuTa;
        this.chucDanh = chucDanh;
        this.vung = vung;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.khauDG = khauDG;
        this.yeuCauDvi = yeuCauDvi;
        this.lyDoDviYCXacMinh = lyDoDviYCXacMinh;
        this.ndXacMinhVoiKH = ndXacMinhVoiKH;
        this.trangThaiXuLy = trangThaiXuLy;
        this.nguoiXacMinh = nguoiXacMinh;
        this.maKhieuNaiPhatSinh = maKhieuNaiPhatSinh;
        this.ngayXacMinh = ngayXacMinh;
        this.chiTietNguyenNhanKHL = chiTietNguyenNhanKHL;
        this.quyHoachLoiTheoThe = quyHoachLoiTheoThe;
        this.kenhBaoCao = kenhBaoCao;
        this.soLoi = soLoi;
        this.fmTGNhanTC = fmTGNhanTC;
        this.fmTGLaiXeXacNhanBG = fmTGLaiXeXacNhanBG;
        this.fmTongKhau = fmTongKhau;
        this.fmChenhLechKPI = fmChenhLechKPI;
        this.fmDanhGia = fmDanhGia;
        this.mmTGLaiXeXacNhanBG = mmTGLaiXeXacNhanBG;
        this.mmTGBCPXacNhanBG = mmTGBCPXacNhanBG;
        this.mmTongKhau = mmTongKhau;
        this.mmChenhLechKPI = mmChenhLechKPI;
        this.mmDanhGia = mmDanhGia;
        this.lmTGBCPXacNhanBG = lmTGBCPXacNhanBG;
        this.lmTGPhatLanDau = lmTGPhatLanDau;
        this.lmTongKhau = lmTongKhau;
        this.lmChenhLechKPI = lmChenhLechKPI;
        this.lmDanhGia = lmDanhGia;
        this.tgTTThucTe = tgTTThucTe;
        this.tgChiTieuPhatLanDau = tgChiTieuPhatLanDau;
        this.chenhLechKPI = chenhLechKPI;
        this.danhGia = danhGia;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        HashMap<String, Object> map = new HashMap<>();
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class){
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class){
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            if (!field.getName().equals("objectHashMap")) {
                map.put(field.getName(), fieldValue);
            }
            i++;
        }
        return map;
    }
}
