package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.HashMap;

@Getter
@Setter
public class TyLeHaiLongExcelDto {
    private static final DecimalFormat df = new DecimalFormat("#.##");

    private String vung;

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongDG;

    private Long tongDGHaiLong;

    private Long tongDGKhongHaiLong;

    private Double tyLeHL;

    private String danhGiaTLHLTong;

    private Long tongDGKhauNhan;

    private Long tongDGKhauNhanHaiLong;

    private Long tongDGKhauNhanKhongHaiLong;

    private Double tyLeHLKhauNhan;

    private String danhGiaTLHLKhauNhan;

    private Long tongDGKhauGiao;

    private Long tongDGKhauGiaoHaiLong;

    private Long tongDGKhauGiaoKhongHaiLong;

    private Double tyLeHLKhauGiao;

    private String danhGiaTLHLKhauGiao;

    private Long tongDGKenhBT;

    private Long tongDGKenhBTHaiLong;

    private Long tongDGKenhBC;

    private Long tongDGKenhBCHaiLong;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public TyLeHaiLongExcelDto() {
    }

    public TyLeHaiLongExcelDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen, Long tongDG, Long tongDGHaiLong, String danhGiaTLHLTong,
                               Long tongDGKhauNhan, Long tongDGKhauNhanHaiLong, String danhGiaTLHLKhauNhan,
                               Long tongDGKhauGiao, Long tongDGKhauGiaoHaiLong, String danhGiaTLHLKhauGiao) throws NoSuchFieldException, IllegalAccessException {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongDG = tongDG;
        this.tongDGHaiLong = tongDGHaiLong;
        this.tongDGKhongHaiLong = tongDG - tongDGHaiLong;
        this.tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
        this.danhGiaTLHLTong = danhGiaTLHLTong;
        this.tongDGKhauNhan = tongDGKhauNhan;
        this.tongDGKhauNhanHaiLong = tongDGKhauNhanHaiLong;
        this.tongDGKhauNhanKhongHaiLong = tongDGKhauNhan - tongDGKhauNhanHaiLong;
        this.tyLeHLKhauNhan = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDGKhauNhan, this.tongDGKhauNhanHaiLong);
        this.danhGiaTLHLKhauNhan = danhGiaTLHLKhauNhan;
        this.tongDGKhauGiao = tongDGKhauGiao;
        this.tongDGKhauGiaoHaiLong = tongDGKhauGiaoHaiLong;
        this.tongDGKhauGiaoKhongHaiLong = tongDGKhauGiao - tongDGKhauGiaoHaiLong;
        this.tyLeHLKhauGiao = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDGKhauGiao, this.tongDGKhauGiaoHaiLong);
        this.danhGiaTLHLKhauGiao = danhGiaTLHLKhauGiao;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        HashMap<String, Object> map = new HashMap<>();
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            if (!field.getName().equals("objectHashMap")) {
                map.put(field.getName(), fieldValue);
            }
            i++;
        }
        return map;
    }
}
