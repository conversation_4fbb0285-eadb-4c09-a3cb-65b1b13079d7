package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.HashMap;

@Getter
@Setter
public class LoiViPhamExcelDto {
    private static final DecimalFormat df = new DecimalFormat("#.##");

    private String vung;

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongDanhGiaNghiepVu;

    private Long tongDanhGiaCLPV;

    private Long tongDanhGiaCLDV;

    private Long tongDanhGiaKiNang;

    private Long tongDanhGiaLoiIchCN;

    private Long tongDanhGiaKhac;

    private Long tong;

    private Long tongThaiDoKem;

    private Long tongGachTrangThaiAo;

    private Long tongLoiNghiemTrong;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public LoiViPhamExcelDto() {
    }

    public LoiViPhamExcelDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen, Long tongDanhGiaNghiepVu, Long tongDanhGiaCLPV, Long tongDanhGiaCLDV, Long tongDanhGiaKiNang, Long tongDanhGiaLoiIchCN, Long tongDanhGiaKhac, Long tongThaiDoKem, Long tongGachTrangThaiAo, Long tongLoiNghiemTrong) throws NoSuchFieldException, IllegalAccessException {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongDanhGiaNghiepVu = tongDanhGiaNghiepVu;
        this.tongDanhGiaCLPV = tongDanhGiaCLPV;
        this.tongDanhGiaCLDV = tongDanhGiaCLDV;
        this.tongDanhGiaKiNang = tongDanhGiaKiNang;
        this.tongDanhGiaLoiIchCN = tongDanhGiaLoiIchCN;
        this.tongDanhGiaKhac = tongDanhGiaKhac;
        this.tong = tongDanhGiaNghiepVu + tongDanhGiaCLPV + tongDanhGiaCLDV + tongDanhGiaKiNang + tongDanhGiaLoiIchCN + tongDanhGiaKhac;
        this.tongThaiDoKem = tongThaiDoKem;
        this.tongGachTrangThaiAo = tongGachTrangThaiAo;
        this.tongLoiNghiemTrong = tongLoiNghiemTrong;
        this.objectHashMap = convert(this);
    }

    public LoiViPhamExcelDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen, Long tongDanhGiaNghiepVu, Long tongDanhGiaCLPV, Long tongDanhGiaCLDV, Long tongDanhGiaKiNang, Long tongDanhGiaLoiIchCN, Long tongDanhGiaKhac, Long tong, Long tongThaiDoKem, Long tongGachTrangThaiAo, Long tongLoiNghiemTrong) throws NoSuchFieldException, IllegalAccessException {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongDanhGiaNghiepVu = tongDanhGiaNghiepVu;
        this.tongDanhGiaCLPV = tongDanhGiaCLPV;
        this.tongDanhGiaCLDV = tongDanhGiaCLDV;
        this.tongDanhGiaKiNang = tongDanhGiaKiNang;
        this.tongDanhGiaLoiIchCN = tongDanhGiaLoiIchCN;
        this.tongDanhGiaKhac = tongDanhGiaKhac;
        this.tong = tong;
        this.tongThaiDoKem = tongThaiDoKem;
        this.tongGachTrangThaiAo = tongGachTrangThaiAo;
        this.tongLoiNghiemTrong = tongLoiNghiemTrong;
        this.objectHashMap = convert(this);
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        HashMap<String, Object> map = new HashMap<>();
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            if (!field.getName().equals("objectHashMap")) {
                map.put(field.getName(), fieldValue);
            }
            i++;
        }
        return map;
    }
}
