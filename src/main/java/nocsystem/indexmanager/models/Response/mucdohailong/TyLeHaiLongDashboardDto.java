package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

@Getter
@Setter
public class TyLeHaiLongDashboardDto {
    private String type;

    private Double tyLeHaiLong;

    private Long tongDGHaiLong;

    private Long tongDG;

    private Double tangTruongThang;

    private Double kpi;

    private boolean danhGiaTLHL;

    private String updateAt;

    public TyLeHaiLongDashboardDto() {
    }

    public TyLeHaiLongDashboardDto(TyLeHaiLongDto tyLeHaiLongDto, String type) {
        switch (type) {
            case "Tong":
                this.type = type;
                this.tongDGHaiLong = (null == tyLeHaiLongDto.getTongDGHaiLong() || tyLeHaiLongDto.getTongDGHaiLong() == 0) ? 0 : tyLeHaiLongDto.getTongDGHaiLong();
                this.tongDG = (null == tyLeHaiLongDto.getTongDG() || tyLeHaiLongDto.getTongDG() == 0) ? 0 : tyLeHaiLongDto.getTongDG();
                this.tyLeHaiLong = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
                break;
            case "KhauGiao":
                this.type = type;
                this.tongDGHaiLong = (null == tyLeHaiLongDto.getTongDGKhauGiaoHaiLong() || tyLeHaiLongDto.getTongDGKhauGiaoHaiLong() == 0) ? 0 : tyLeHaiLongDto.getTongDGKhauGiaoHaiLong();
                this.tongDG = (null == tyLeHaiLongDto.getTongDGKhauGiao() || tyLeHaiLongDto.getTongDGKhauGiao() == 0) ? 0 : tyLeHaiLongDto.getTongDGKhauGiao();
                this.tyLeHaiLong = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
                break;
            case "KhauNhan":
                this.type = type;
                this.tongDGHaiLong = (null == tyLeHaiLongDto.getTongDGKhauNhanHaiLong() || tyLeHaiLongDto.getTongDGKhauNhanHaiLong() == 0) ? 0 : tyLeHaiLongDto.getTongDGKhauNhanHaiLong();
                this.tongDG = (null == tyLeHaiLongDto.getTongDGKhauNhan() || tyLeHaiLongDto.getTongDGKhauNhan() == 0) ? 0 : tyLeHaiLongDto.getTongDGKhauNhan();
                this.tyLeHaiLong = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
                break;
            case "KenhBT":
                this.type = type;
                this.tongDGHaiLong = (null == tyLeHaiLongDto.getTongDGKenhBTHaiLong() || tyLeHaiLongDto.getTongDGKenhBTHaiLong() == 0) ? 0 : tyLeHaiLongDto.getTongDGKenhBTHaiLong();
                this.tongDG = (null == tyLeHaiLongDto.getTongDGKenhBT() || tyLeHaiLongDto.getTongDGKenhBT() == 0) ? 0 : tyLeHaiLongDto.getTongDGKenhBT();
                this.tyLeHaiLong = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
                break;
            case "KenhBC":
                this.type = type;
                this.tongDGHaiLong = (null == tyLeHaiLongDto.getTongDGKenhBCHaiLong() || tyLeHaiLongDto.getTongDGKenhBCHaiLong() == 0) ? 0 : tyLeHaiLongDto.getTongDGKenhBCHaiLong();
                this.tongDG = (null == tyLeHaiLongDto.getTongDGKenhBC() || tyLeHaiLongDto.getTongDGKenhBC() == 0) ? 0 : tyLeHaiLongDto.getTongDGKenhBC();
                this.tyLeHaiLong = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
                break;
        }

    }
}
