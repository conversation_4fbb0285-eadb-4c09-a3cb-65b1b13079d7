package nocsystem.indexmanager.models.Response.mucdohailong.mobile;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

@Getter
@Setter
public class MucDoHaiLongOverview {

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongKhaoSat = 0L;

    private Long tongPhanHoi = 0L;

    private Double tyLePhanHoi = (double) 0;

    private Long tongDG = 0L;

    private Long tongDGHaiLong = 0L;

    private Double tyLeHL = (double) 0;

    private Long tongDGKhongHaiLong = 0L;

    private Double tyLeNguyenNhanKHL = (double) 0;

    private Long tongSoLoiNghiemTrong = 0L;

    private Double tyleSoLoiNghiemTrong = (double) 0;

    private String updatedTime;

    public MucDoHaiLongOverview() {
    }

    public MucDoHaiLongOverview(String maChiNhanh, String maBuuCuc, String maTuyen, Long tongKhaoSat, Long tongPhanHoi, Long tongDG, Long tongDGHaiLong, <PERSON> thaiDoPhucVuKem, Long loigachTTAo) {
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongKhaoSat = (null == tongKhaoSat || tongKhaoSat == 0) ? 0 : tongKhaoSat;
        this.tongPhanHoi = (null == tongPhanHoi || tongPhanHoi == 0) ? 0 : tongPhanHoi;
        this.tongDG = (null == tongDG || tongDG == 0) ? 0 : tongDG;
        this.tongDGHaiLong = (null == tongDGHaiLong || tongDGHaiLong == 0) ? 0 : tongDGHaiLong;
        this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(this.tongKhaoSat, this.tongPhanHoi);
        this.tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(this.tongDG, this.tongDGHaiLong);
        this.tongDGKhongHaiLong = tongDG - tongDGHaiLong;
        this.tyLeNguyenNhanKHL = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, this.tongDGKhongHaiLong);
        this.tongSoLoiNghiemTrong = thaiDoPhucVuKem + loigachTTAo;
        this.tyleSoLoiNghiemTrong = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, this.tongSoLoiNghiemTrong);
    }
}
