package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

import java.text.DecimalFormat;
import java.time.LocalDate;

@Getter
@Setter
public class TopTenChiNhanhDto {

    private static final DecimalFormat df = new DecimalFormat("#.##");

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongDG;

    private Long tongDGHaiLong;

    private Double tyLeHL;

    private Double kpi;

    private String updated_at;

    public TopTenChiNhanhDto() {
    }

    public TopTenChiNhanhDto(String maChiNhanh, String maBuuCuc, String maTuyen, Long tongDG, Long tongDGHaiLong, LocalDate ngayBaoCao) {
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongDG = tongDG;
        this.tongDGHaiLong = tongDGHaiLong;
        this.tyLeHL = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, tongDGHaiLong);
//        this.kpi = MucDoHaiLongUtils.getKPICurrentMonth(ngayBaoCao);
    }
}
