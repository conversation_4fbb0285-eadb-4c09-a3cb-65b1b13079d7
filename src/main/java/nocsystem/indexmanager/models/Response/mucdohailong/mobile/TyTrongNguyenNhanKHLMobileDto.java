package nocsystem.indexmanager.models.Response.mucdohailong.mobile;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

@Getter
@Setter
public class TyTrongNguyenNhanKHLMobileDto {
    private String name;
    private Double tyTrong;
    private Long tongDGKhongHaiLong;
    private Long tongDG;

    public TyTrongNguyenNhanKHLMobileDto() {
    }

    public TyTrongNguyenNhanKHLMobileDto(String name, Long tongDGKhongHaiLong, Long tongDG) {
        this.name = name;
        this.tyTrong = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, tongDGKhongHaiLong);
        this.tongDGKhongHaiLong = tongDGKhongHaiLong;
        this.tongDG = tongDG;
    }
}
