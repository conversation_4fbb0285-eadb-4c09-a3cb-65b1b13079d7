package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

import java.text.DecimalFormat;

@Getter
@Setter
public class TyLePhanHoiDto {
    private static final DecimalFormat df = new DecimalFormat("#.##");

    private String vung;

    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongKhaoSat;

    private Long tongPhanHoi;

    private Long tongKHTuDanhGia;

    private Double tyLePhanHoi;

    private Long tongKhaoSatKhauGiao;

    private Long tongPhanHoiKhauGiao;

    private Long tongKHTuDanhGiaKhauGiao;

    private Double tyLePhanHoiKhauGiao;

    private Long tongKhaoSatKhauNhan;

    private Long tongPhanHoiKhauNhan;

    private Long tongKHTuDanhGiaKhauNhan;

    private Double tyLePhanHoiKhauNhan;

    private Long tongKhaoSatKenhBT;

    private Long tongPhanHoiKenhBT;

    private Long tongKHTuDanhGiaKenhBT;

    private Long tongKhaoSatKenhBC;

    private Long tongPhanHoiKenhBC;

    private Long tongKHTuDanhGiaKenhBC;

    public TyLePhanHoiDto() {
    }

    public TyLePhanHoiDto(String vung, String maChiNhanh, String maBuuCuc, String maTuyen,
                          Long tongKhaoSat, Long tongPhanHoi, Long tongKHTuDanhGia,
                          Long tongKhaoSatKhauGiao, Long tongPhanHoiKhauGiao, Long tongKHTuDanhGiaKhauGiao,
                          Long tongKhaoSatKhauNhan, Long tongPhanHoiKhauNhan, Long tongKHTuDanhGiaKhauNhan) throws NoSuchFieldException, IllegalAccessException {
        this.vung = vung;
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongKhaoSat = tongKhaoSat - tongKHTuDanhGia;
        this.tongPhanHoi = tongPhanHoi - tongKHTuDanhGia;
        this.tongKHTuDanhGia = tongKHTuDanhGia;
        this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSat, tongPhanHoi);
        this.tongKhaoSatKhauNhan = tongKhaoSatKhauNhan - tongKHTuDanhGiaKhauNhan;
        this.tongPhanHoiKhauNhan = tongPhanHoiKhauNhan - tongKHTuDanhGiaKhauNhan;
        this.tongKHTuDanhGiaKhauNhan = tongKHTuDanhGiaKhauNhan;
        this.tyLePhanHoiKhauNhan = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSatKhauNhan, tongPhanHoiKhauNhan);
        this.tongKhaoSatKhauGiao = tongKhaoSatKhauGiao - tongKHTuDanhGiaKhauGiao;
        this.tongPhanHoiKhauGiao = tongPhanHoiKhauGiao - tongKHTuDanhGiaKhauGiao;
        this.tongKHTuDanhGiaKhauGiao = tongKHTuDanhGiaKhauGiao;
        this.tyLePhanHoiKhauGiao = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSatKhauGiao, tongPhanHoiKhauGiao);
    }


    public TyLePhanHoiDto(Long tongKhaoSat, Long tongPhanHoi, Long tongKHTuDanhGia,
                          Long tongKhaoSatKhauGiao, Long tongPhanHoiKhauGiao, Long tongKHTuDanhGiaKhauGiao,
                          Long tongKhaoSatKhauNhan, Long tongPhanHoiKhauNhan, Long tongKHTuDanhGiaKhauNhan,
                          Long tongKhaoSatKenhBT, Long tongPhanHoiKenhBT, Long tongKHTuDanhGiaKenhBT,
                          Long tongKhaoSatKenhBC, Long tongPhanHoiKenhBC, Long tongKHTuDanhGiaKenhBC) {
        this.tongKhaoSat = tongKhaoSat;
        this.tongPhanHoi = tongPhanHoi;
        this.tongKHTuDanhGia = tongKHTuDanhGia;
        this.tongKhaoSatKhauGiao = tongKhaoSatKhauGiao;
        this.tongPhanHoiKhauGiao = tongPhanHoiKhauGiao;
        this.tongKHTuDanhGiaKhauGiao = tongKHTuDanhGiaKhauGiao;
        this.tongKhaoSatKhauNhan = tongKhaoSatKhauNhan;
        this.tongPhanHoiKhauNhan = tongPhanHoiKhauNhan;
        this.tongKHTuDanhGiaKhauNhan = tongKHTuDanhGiaKhauNhan;
        this.tongKhaoSatKenhBT = tongKhaoSatKenhBT;
        this.tongPhanHoiKenhBT = tongPhanHoiKenhBT;
        this.tongKHTuDanhGiaKenhBT = tongKHTuDanhGiaKenhBT;
        this.tongKhaoSatKenhBC = tongKhaoSatKenhBC;
        this.tongPhanHoiKenhBC = tongPhanHoiKenhBC;
        this.tongKHTuDanhGiaKenhBC = tongKHTuDanhGiaKenhBC;
    }

    public TyLePhanHoiDto(String maChiNhanh,
                          Long tongKhaoSat, Long tongPhanHoi, Long tongKHTuDanhGia,
                          Long tongKhaoSatKhauGiao, Long tongPhanHoiKhauGiao, Long tongKHTuDanhGiaKhauGiao,
                          Long tongKhaoSatKhauNhan, Long tongPhanHoiKhauNhan, Long tongKHTuDanhGiaKhauNhan) throws NoSuchFieldException, IllegalAccessException {
        this.maChiNhanh = maChiNhanh;
        this.tongKhaoSat = tongKhaoSat;
        this.tongPhanHoi = tongPhanHoi;
        this.tongKHTuDanhGia = tongKHTuDanhGia;
        this.tyLePhanHoi = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSat + tongKHTuDanhGia, tongPhanHoi + tongKHTuDanhGia);
        ;
        this.tongKhaoSatKhauGiao = tongKhaoSatKhauGiao;
        this.tongPhanHoiKhauGiao = tongPhanHoiKhauGiao;
        this.tongKHTuDanhGiaKhauGiao = tongKHTuDanhGiaKhauGiao;
        this.tyLePhanHoiKhauGiao = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSatKhauGiao + tongKHTuDanhGiaKhauGiao, tongPhanHoiKhauGiao + tongKHTuDanhGiaKhauGiao);
        ;
        this.tongKhaoSatKhauNhan = tongKhaoSatKhauNhan;
        this.tongPhanHoiKhauNhan = tongPhanHoiKhauNhan;
        this.tongKHTuDanhGiaKhauNhan = tongKHTuDanhGiaKhauNhan;
        this.tyLePhanHoiKhauNhan = MucDoHaiLongUtils.getTyLeHaiLong(tongKhaoSatKhauNhan + tongKHTuDanhGiaKhauNhan, tongPhanHoiKhauNhan + tongKHTuDanhGiaKhauNhan);
        ;
    }
}
