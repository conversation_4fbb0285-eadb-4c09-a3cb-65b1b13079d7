package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TopViPhamNhieuNhatDto {
    private String maChiNhanh;

    private String maBuuCuc;

    private String maTuyen;

    private Long tongThaiDoKemLk;

    private Long tongGachTTAoLk;

    private Long tongDGLoiLk;

    private Long tongThaiDoKemTrongNgay;

    private Long tongGachTTAoTrongNgay;

    private Long tongDGLoiTrongNgay;

    private Long tongThaiDoKemLapLai;

    private Long tongGachTTAoLapLai;

    private Long tongDGLoiLapLai;

    public TopViPhamNhieuNhatDto() {
    }

    public TopViPhamNhieuNhatDto(String maChiNhanh, String maBuuCuc, String maTuyen, Long tongThaiDoKemLk, Long tongGachTTAoLk, Long tongDGLoiLk, Long tongThaiDoKemTrongNgay, Long tongGachTTAoTrongNgay, Long tongDGLoiTrongNgay, Long tongThaiDoKemLapLai, Long tongGachTTAoLapLai, Long tongDGLoiLapLai) {
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.maTuyen = maTuyen;
        this.tongThaiDoKemLk = tongThaiDoKemLk;
        this.tongGachTTAoLk = tongGachTTAoLk;
        this.tongDGLoiLk = tongDGLoiLk;
        this.tongThaiDoKemTrongNgay = tongThaiDoKemTrongNgay;
        this.tongGachTTAoTrongNgay = tongGachTTAoTrongNgay;
        this.tongDGLoiTrongNgay = tongDGLoiTrongNgay;
        this.tongThaiDoKemLapLai = tongThaiDoKemLapLai;
        this.tongGachTTAoLapLai = tongGachTTAoLapLai;
        this.tongDGLoiLapLai = tongDGLoiLapLai;
    }
}
