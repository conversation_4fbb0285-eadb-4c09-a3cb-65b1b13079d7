package nocsystem.indexmanager.models.Response.mucdohailong;

import lombok.Getter;
import lombok.Setter;
import nocsystem.indexmanager.util.MucDoHaiLongUtils;

@Getter
@Setter
public class TyTrongNguyenNhanKHLCaoNhatDto {
    private String name;
    private Double tyTrongThangN;
    private Long tongDGKhongHLThangN;
    private Double tyTrongThangTruoc;
    private Long tongDGKhongHLThangTruoc;

    public TyTrongNguyenNhanKHLCaoNhatDto() {
    }

    public TyTrongNguyenNhanKHLCaoNhatDto(String name, Long tongDGKhongHaiLong, Long tongDG, Long tongDGKhongHLThangN) {
        this.name = name;
        this.tyTrongThangN = MucDoHaiLongUtils.getTyLeHaiLong(tongDG, tongDGKhongHaiLong);
        this.tongDGKhongHLThangN = tongDGKhongHLThangN;
    }
}
