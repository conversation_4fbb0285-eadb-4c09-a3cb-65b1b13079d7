package nocsystem.indexmanager.models.Response.TienDoLogisticDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TienDoDTLogisticChiNhanhV1Dto {
    /*
     * dichVu: 1-Forwarding, 2-Kho Vận
     */
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ten_chi_nhanh")
    private String tenChiNhanh;

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("loai_dichvu")
    private String dichVu;

    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tiendo")
    private Float tienDo = Float.valueOf(0);

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);
}
