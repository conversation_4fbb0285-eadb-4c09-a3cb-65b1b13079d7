package nocsystem.indexmanager.models.Response.TienDoLogisticDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;
import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TienDoDoanhThuLogisticV1Dto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh = "";

    @JsonProperty("ma_buucuc")
    private String maBuuCuc = "";

    @JsonProperty("loai_dichvu")
    private String dichVu = "";

    @JsonProperty("ke_hoach")
    private Float keHoach;

    @JsonProperty("thuc_hien")
    private Float thucHien;

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("namtruoc")
    private Float namTruoc = Float.valueOf(0);

    @JsonProperty("thangtruoc")
    private Float thangTruoc = Float.valueOf(0);

    @JsonProperty("ngay_cung_ky")
    private Float ngayCungKy = Float.valueOf(0);

    @JsonProperty("thang_cung_ky")
    private Float thangCungKy = Float.valueOf(0);

    @JsonProperty("nam_cung_ky")
    private Float namCungKy = Float.valueOf(0);
}
