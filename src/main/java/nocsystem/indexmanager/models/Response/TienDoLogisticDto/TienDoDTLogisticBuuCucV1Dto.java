package nocsystem.indexmanager.models.Response.TienDoLogisticDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TienDoDTLogisticBuuCucV1Dto {
    /*
     * dichVu: 1-Forwarding, 2-Kho Vận
     */
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ten_chi_nhanh")
    private String tenChiNhanh;

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("loai_dichvu")
    private String dichVu;

    @JsonProperty("ke_hoach")
    private Float keHoach;

    @JsonProperty("thuc_hien")
    private Float thucHien;

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;
}
