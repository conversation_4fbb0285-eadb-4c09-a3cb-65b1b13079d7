package nocsystem.indexmanager.models.Response.TienDoLogisticDto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
public class ParamTienDoDTLogisticDto {
    private List<TienDoDoanhThuLogisticOverViewDto> tienDoDoanhThuLogisticCNModel = new ArrayList<>();

    private String maChiNhanh = "";

    private Boolean calculateDashBoard = false;
}
