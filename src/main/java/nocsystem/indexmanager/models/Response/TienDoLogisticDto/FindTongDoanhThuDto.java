package nocsystem.indexmanager.models.Response.TienDoLogisticDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.models.Response.ChiSoKinhDoanh.CSKDTienDoDoanhThuResDto;
import nocsystem.indexmanager.models.Response.ChiSoKinhDoanh.CSKDTongDoanhThuResDto;
import nocsystem.indexmanager.models.TienDoDoanhThuLogistic.TienDoDoanhThuLogistic;

import java.util.List;

@Data
public class FindTongDoanhThuDto {
    @JsonProperty("content")
    private List<TienDoDoanhThuLogisticV1Dto> tienDoDoanhThuLogistic;

    @JsonProperty("total")
    private CSKDTongDoanhThuResDto tienDoDoanhThu;
}
