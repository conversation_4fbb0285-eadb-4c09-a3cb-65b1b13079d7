package nocsystem.indexmanager.models.Response.ThongKeTongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class ThongKeTongDoanhThuNCV1ResDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("tong_doanh_thu")
    private Float tongDoanhThu;

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;
}
