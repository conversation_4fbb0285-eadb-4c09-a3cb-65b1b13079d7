package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BaoCaoKPIPhatBillResDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("ma_phieugui")
    private String maPhieuGui;

    @JsonProperty("tinh_nhan")
    private String tinhNhan;

    @JsonProperty("tinh_phat")
    private String tinhPhat;

    @JsonProperty("huyen_nhan")
    private String huyenNhan;

    @JsonProperty("huyen_phat")
    private String huyenPhat;

    @JsonProperty("ma_xanhan")
    private String maXaNhan;

    @JsonProperty("ma_xaphat")
    private String maXaPhat;

    @JsonProperty("ma_dv_viettel")
    private String maDVViettel;

    @JsonProperty("ma_buucuc_goc")
    private String maBuuCucGoc;

    @JsonProperty("ma_buucuc_phat")
    private String maBuuCucPhat;

    @JsonProperty("time_yeucau_layhang")
    private Date timeYeuCauLayHang;

    @JsonProperty("ngay_gui_bp")
    private Date ngayGuiBP;

    @JsonProperty("ma_trangthai")
    private String maTrangThai;

    @JsonProperty("ma_buucuc_ht")
    private String maBuuCucHT;

    @JsonProperty("ma_chinhanh_ht")
    private String maChiNhanhHT;

    @JsonProperty("ma_doitac")
    private String maDoiTac;

    @JsonProperty("ma_khgui")
    private String maKHGui;

    @JsonProperty("ma_dichvu_doitac")
    private String maDichVuDoiTac;

    @JsonProperty("tg_quydinhphat")
    private Date tgQuyDinhPhat;

    @JsonProperty("tg_quydinh")
    private Float tgQuyDinh;

    @JsonProperty("tg_chenhlechphat")
    private String tgChenhLechPhat;

    @JsonProperty("tg_chenhlech")
    private String tgChenhLech;

    @JsonProperty("time_pcp")
    private Date timePCP;

    @JsonProperty("time_gach_bp")
    private Date timeGachBP;

    @JsonProperty("time_gach_bp2")
    private Date timeGachBP2;

    @JsonProperty("time_gach_bp3")
    private Date timeGachBP3;

    @JsonProperty("ly_do_tt")
    private String lyDoTT;

    @JsonProperty("ly_do_tt_2")
    private String lyDoTT2;

    @JsonProperty("ly_do_tt_3")
    private String lyDoTT3;

    @JsonProperty("tien_cod")
    private Float tienCOD;

    @JsonProperty("tienhang")
    private Float tienhang;

    @JsonProperty("trong_luong")
    private Float trongLuong;

    @JsonProperty("tong_cuoc")
    private Float tongCuoc;

    @JsonProperty("canh_bao")
    private String canhBao;

    @JsonProperty("tuyen_buuta")
    private String tuyenBuuTa;

    @JsonProperty("so_lan_giao")
    private Integer soLanGiao;

    @JsonProperty("tg_ptc")
    private Date tgPTC;

    @JsonProperty("danhgia_time_gach_bp1")
    private String dgTimeGachBP1;

    @JsonProperty("danhgia_time_gach_bp2")
    private String dgTimeGachBP2;

    @JsonProperty("danhgia_time_gach_bp3")
    private String dgTimeGachBP3;

    @JsonProperty("ma_buucuc_phat_thucte")
    private String maBuuCucPhatThucTe;

    @JsonProperty("ton")
    private Integer ton;

    @JsonProperty("phat")
    private Integer phat;

    @JsonProperty("vung_phat")
    private String vungPhat;

    @JsonProperty("chieu")
    private String chieu;
    @JsonProperty("tg502")
    private Date tg502;

    @JsonProperty("tg_nhantai_bcp") // -> tg_nhantai
    private Date tgNhanTai;


    public BaoCaoKPIPhatBillResDTO(LocalDate ngayBaoCao, String maPhieuGui, String tinhNhan, String tinhPhat, String huyenNhan, String huyenPhat, String maXaNhan, String maXaPhat, String maDVViettel, String maBuuCucGoc, String maBuuCucPhat, Date timeYeuCauLayHang, Date ngayGuiBP, String maTrangThai, String maBuuCucHT, String maChiNhanhHT, String maDoiTac, String maKHGui, String maDichVuDoiTac, Date tgQuyDinhPhat, Float tgQuyDinh, String tgChenhLechPhat, String tgChenhLech, Date timePCP, Date timeGachBP, Date timeGachBP2, Date timeGachBP3, String lyDoTT, String lyDoTT2, String lyDoTT3, Float tienCOD, Float tienhang, Float trongLuong, Float tongCuoc, String canhBao, String tuyenBuuTa, Integer soLanGiao, Date tgPTC, String dgTimeGachBP1, String dgTimeGachBP2, String dgTimeGachBP3, String maBuuCucPhatThucTe) {
        this.ngayBaoCao = ngayBaoCao;
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.tinhPhat = tinhPhat;
        this.huyenNhan = huyenNhan;
        this.huyenPhat = huyenPhat;
        this.maXaNhan = maXaNhan;
        this.maXaPhat = maXaPhat;
        this.maDVViettel = maDVViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maBuuCucPhat = maBuuCucPhat;
        this.timeYeuCauLayHang = timeYeuCauLayHang;
        this.ngayGuiBP = ngayGuiBP;
        this.maTrangThai = maTrangThai;
        this.maBuuCucHT = maBuuCucHT;
        this.maChiNhanhHT = maChiNhanhHT;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.maDichVuDoiTac = maDichVuDoiTac;
        this.tgQuyDinhPhat = tgQuyDinhPhat;
        this.tgQuyDinh = tgQuyDinh;
        this.tgChenhLechPhat = tgChenhLechPhat;
        this.tgChenhLech = tgChenhLech;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.timeGachBP2 = timeGachBP2;
        this.timeGachBP3 = timeGachBP3;
        this.lyDoTT = lyDoTT;
        this.lyDoTT2 = lyDoTT2;
        this.lyDoTT3 = lyDoTT3;
        this.tienCOD = tienCOD;
        this.tienhang = tienhang;
        this.trongLuong = trongLuong;
        this.tongCuoc = tongCuoc;
        this.canhBao = canhBao;
        this.tuyenBuuTa = tuyenBuuTa;
        this.soLanGiao = soLanGiao;
        this.tgPTC = tgPTC;
        this.dgTimeGachBP1 = dgTimeGachBP1;
        this.dgTimeGachBP2 = dgTimeGachBP2;
        this.dgTimeGachBP3 = dgTimeGachBP3;
        this.maBuuCucPhatThucTe = maBuuCucPhatThucTe;
    }

    public BaoCaoKPIPhatBillResDTO(LocalDate ngayBaoCao, String maPhieuGui, String tinhNhan, String tinhPhat, String huyenNhan, String huyenPhat, String maXaNhan, String maXaPhat, String maDVViettel, String maBuuCucGoc, String maBuuCucPhat, Date timeYeuCauLayHang, Date ngayGuiBP, String maTrangThai, String maBuuCucHT, String maChiNhanhHT, String maDoiTac, String maKHGui, String maDichVuDoiTac, Date tgQuyDinhPhat, Float tgQuyDinh, String tgChenhLechPhat, String tgChenhLech, Date timePCP, Date timeGachBP, Date timeGachBP2, Date timeGachBP3, String lyDoTT, String lyDoTT2, String lyDoTT3, Float tienCOD, Float tienhang, Float trongLuong, Float tongCuoc, String canhBao, String tuyenBuuTa, Integer soLanGiao, Date tgPTC, String dgTimeGachBP1, String dgTimeGachBP2, String dgTimeGachBP3, String maBuuCucPhatThucTe, Date tgNhanTai) {
        this.ngayBaoCao = ngayBaoCao;
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.tinhPhat = tinhPhat;
        this.huyenNhan = huyenNhan;
        this.huyenPhat = huyenPhat;
        this.maXaNhan = maXaNhan;
        this.maXaPhat = maXaPhat;
        this.maDVViettel = maDVViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maBuuCucPhat = maBuuCucPhat;
        this.timeYeuCauLayHang = timeYeuCauLayHang;
        this.ngayGuiBP = ngayGuiBP;
        this.maTrangThai = maTrangThai;
        this.maBuuCucHT = maBuuCucHT;
        this.maChiNhanhHT = maChiNhanhHT;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.maDichVuDoiTac = maDichVuDoiTac;
        this.tgQuyDinhPhat = tgQuyDinhPhat;
        this.tgQuyDinh = tgQuyDinh;
        this.tgChenhLechPhat = tgChenhLechPhat;
        this.tgChenhLech = tgChenhLech;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.timeGachBP2 = timeGachBP2;
        this.timeGachBP3 = timeGachBP3;
        this.lyDoTT = lyDoTT;
        this.lyDoTT2 = lyDoTT2;
        this.lyDoTT3 = lyDoTT3;
        this.tienCOD = tienCOD;
        this.tienhang = tienhang;
        this.trongLuong = trongLuong;
        this.tongCuoc = tongCuoc;
        this.canhBao = canhBao;
        this.tuyenBuuTa = tuyenBuuTa;
        this.soLanGiao = soLanGiao;
        this.tgPTC = tgPTC;
        this.dgTimeGachBP1 = dgTimeGachBP1;
        this.dgTimeGachBP2 = dgTimeGachBP2;
        this.dgTimeGachBP3 = dgTimeGachBP3;
        this.maBuuCucPhatThucTe = maBuuCucPhatThucTe;
        this.tgNhanTai = tgNhanTai;
    }

    public String getMaPhieuGui() {
        return maPhieuGui;
    }

    public void setMaPhieuGui(String maPhieuGui) {
        this.maPhieuGui = maPhieuGui;
    }

    public Integer getTon() {
        return ton;
    }

    public void setTon(Integer ton) {
        this.ton = ton;
    }

    public Integer getPhat() {
        return phat;
    }

    public void setPhat(Integer phat) {
        this.phat = phat;
    }

}
