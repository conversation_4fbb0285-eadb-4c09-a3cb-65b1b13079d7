package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DashboardTikTokKpiThuResponse {

    @JsonProperty("tong_sl_thu")
    private Long tongSlThu;
    @JsonProperty("chenh_lech_tong_thu")
    private Long chenhLechSlThu;

    @JsonProperty("sl_ttc")
    private Long slThuThanhCong;
    @JsonProperty("chenh_lech_ttc")
    private Long chenhLechThuThanhCong;
    @JsonProperty("ty_le_ttc")
    private Double tyLeThuThanhCong;

    @JsonProperty("sl_thu_dung_gio")
    private Long slThuThanhCongDungGio;
    @JsonProperty("chenh_lech_thu_dung_gio")
    private Long chenhLechThuThanhCongDungGio;
    @JsonProperty("ty_le_thu_dung_gio")
    private Double tyLeThuThanhCongDungGio;

    @JsonProperty("sl_thu_lan1_dung_han")
    private Long slThuLan1DungHan;
    @JsonProperty("chenh_lech_thu_lan1_dung_han")
    private Long chenhLechThuLan1DungHan;
    @JsonProperty("ty_le_thu_lan1_dung_han")
    private Double tyLeThuDungLan1DungHan;

    @JsonProperty("sl_ton")
    private Long slTon;
    @JsonProperty("chenh_lech_ton")
    private Long chenhLechTon;

    @JsonProperty("sl_huy")
    private Long slHuy;
    @JsonProperty("chenh_lech_huy")
    private Long chenhLechHuy;

    @JsonIgnore
    private LocalDate ngayBaoCao;



    public DashboardTikTokKpiThuResponse(Long tongSlThu, Long slThuThanhCong, Long slThuThanhCongDungGio, Long slThuLan1DungHan, Long slTon, Long slHuy, LocalDate ngayBaoCao) {
        this.tongSlThu = tongSlThu;
        this.slThuThanhCong = slThuThanhCong;
        this.slThuThanhCongDungGio = slThuThanhCongDungGio;
        this.slThuLan1DungHan = slThuLan1DungHan;
        this.slTon = slTon;
        this.slHuy = slHuy;
        this.ngayBaoCao = ngayBaoCao;
    }

    public void setValueChenhLech(Long chenhLechSlThu, Long chenhLechThuThanhCong, Long chenhLechThuThanhCongDungGio, Long chenhLechThuLan1DungHan, Long chenhLechTon, Long chenhLechHuy) {
        this.chenhLechSlThu = chenhLechSlThu;
        this.chenhLechThuThanhCong = chenhLechThuThanhCong;
        this.chenhLechThuThanhCongDungGio = chenhLechThuThanhCongDungGio;
        this.chenhLechThuLan1DungHan = chenhLechThuLan1DungHan;
        this.chenhLechTon = chenhLechTon;
        this.chenhLechHuy = chenhLechHuy;
    }

    public void setTongSanLuong(Long slTongthu, Long slTTC, Long slThuThanhCongDungGio, Long slThuLan1DungHan, Long slTon, Long slHuy){
        this.tongSlThu = slTongthu;
        this.slThuThanhCong = slTTC;
        this.slThuThanhCongDungGio = slThuThanhCongDungGio;
        this.slThuLan1DungHan = slThuLan1DungHan;
        this.slTon = slTon;
        this.slHuy = slHuy;

    }

    public void setTyLe(Double tyLeThuThanhCong, Double tyLeThuThanhCongDungGio, Double tyLeThuDungLan1DungHan) {
        this.tyLeThuThanhCong = tyLeThuThanhCong;
        this.tyLeThuThanhCongDungGio = tyLeThuThanhCongDungGio;
        this.tyLeThuDungLan1DungHan = tyLeThuDungLan1DungHan;
    }
}
