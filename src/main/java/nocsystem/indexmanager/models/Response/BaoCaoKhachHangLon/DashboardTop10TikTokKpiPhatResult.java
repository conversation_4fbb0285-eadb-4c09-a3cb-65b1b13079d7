package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DashboardTop10TikTokKpiPhatResult {
    String donVi;
    private Long slPhatTC;
    private Long slPhaiPhat;
    private Long slPhaiPhatLk;
    private Long slPtcDungGio;
    private Long slPtcDungGioLan1;

    private Long tongSlPhat;
    private Float tlPhatThanhCong;
    private Float tlPhatThanhCongDungGio;

    private Float tlPhatThanhCongLan1;

    public DashboardTop10TikTokKpiPhatResult(String donVi, Long slPhatTC, Long slPhaiPhat, Long slPtcDungGio, Long slPtcDungGioLan1) {
        this.donVi = donVi;
        this.slPhatTC = slPhatTC;
        this.slPhaiPhat = slPhaiPhat;
        this.slPtcDungGio = slPtcDungGio;
        this.slPtcDungGioLan1 = slPtcDungGioLan1;
    }

    public DashboardTop10TikTokKpiPhatResult(String donVi, Long slPhaiPhat, Long slPhaiPhatLk) {
        this.donVi = donVi;
        this.slPhaiPhat = slPhaiPhat;
        this.slPhaiPhatLk = slPhaiPhatLk;
    }
}
