package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BaoCaoKPIThuResDTO {

    @JsonProperty("thoi_gian")
    private LocalDate thoiGian;

    @JsonProperty("vung")
    private String vung;

    @JsonProperty("chi_nhanh")
    private String chiNhanh;

    @JsonProperty("buu_cuc")
    private String buuCuc;

    @JsonProperty("dich_vu")
    private String dichVu;

    @JsonProperty("loai_don")
    private String loaiDon;

    @JsonProperty("doi_tac")
    private String doiTac;

    @JsonProperty("sl")
    private Long sL;

    @JsonProperty("sl_thu_tc")
    private Long slThuTC;

    @JsonProperty("sl_thu_dg")
    private Long slThuDG;

    @JsonProperty("sl_thu_dg_1")
    private Long slThuDG1;

    @JsonProperty("tl_thu_tc")
    private Float tlThuTC;

    @JsonProperty("tl_thu_dg")
    private Float tlThuDG;

    @JsonProperty("tl_thu_dg_1")
    private Float tlThuDG1;

    @JsonProperty("huy")
    private Long huy;

    @JsonProperty("xanh")
    private Long xanh;

    @JsonProperty("vang")
    private Long vang;

    @JsonProperty("do_1")
    private Long do1;

    @JsonProperty("do_2")
    private Long do2;

    @JsonProperty("do_3")
    private Long do3;

    @JsonProperty("tong_ton")
    private Long tongTon;
}
