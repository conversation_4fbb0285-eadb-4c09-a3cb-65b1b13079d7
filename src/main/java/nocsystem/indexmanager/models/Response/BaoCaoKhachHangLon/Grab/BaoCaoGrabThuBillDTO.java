package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.HashMap;


@NoArgsConstructor
@AllArgsConstructor
public class BaoCaoGrabThuBillDTO {

    private String maPhieuGui;

    private String tinhNhan;

    private String tinhPhat;

    private String huyenNhan;

    private String huyenPhat;

    private String maBuuCucGoc;

    private String maBuuCucHT;

    private String timeYeuCauLayHang;

    private String timeTaoDon;

    private String timeNhapMay;

    private String timeQuyDinhThu;

    private String maTrangThai;

    private String maDoiTac;

    private String maKhachHang;

    private String maDichVu;

    private String loaiDon;

    private String trongLuong;

    private String loaiVung;

    private String maVanDonGrab;

    private String xaNhan;

    private String xaPhat;

    private String soKm;

    private String requestTime;

    private Long tongCuoc;

    private Long tongCOD;

    private String lyDoHuy;

    private String trangThaiGrab;

    private String trangThaiVTP;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public BaoCaoGrabThuBillDTO(String maPhieuGui, String tinhNhan, String tinhPhat, String huyenNhan, String huyenPhat, String maBuuCucGoc, String maBuuCucHT, String timeYeuCauLayHang, String timeTaoDon, String timeNhapMay, String timeQuyDinhThu, String maTrangThai, String maDoiTac, String maKhachHang, String maDichVu, String loaiDon, String trongLuong, String loaiVung) throws NoSuchFieldException, IllegalAccessException {
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.tinhPhat = tinhPhat;
        this.huyenNhan = huyenNhan;
        this.huyenPhat = huyenPhat;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maBuuCucHT = maBuuCucHT;
        this.timeYeuCauLayHang = timeYeuCauLayHang;
        this.timeTaoDon = timeTaoDon;
        this.timeNhapMay = timeNhapMay;
        this.timeQuyDinhThu = timeQuyDinhThu;
        this.maTrangThai = maTrangThai;
        this.maDoiTac = maDoiTac;
        this.maKhachHang = maKhachHang;
        this.maDichVu = maDichVu;
        this.loaiDon = loaiDon;
        this.trongLuong = trongLuong;
        this.loaiVung = loaiVung;
        this.objectHashMap = convert(this);
    }

    public static final DecimalFormat df = new DecimalFormat("0.00");

    public BaoCaoGrabThuBillDTO(String maPhieuGui, String tinhNhan, String tinhPhat, String huyenNhan, String huyenPhat, String maBuuCucGoc, String maBuuCucHT, String timeYeuCauLayHang, String timeTaoDon, String timeNhapMay, String timeQuyDinhThu, String maTrangThai, String maDoiTac, String maKhachHang, String maDichVu, String loaiDon, String trongLuong, String loaiVung, String maVanDonGrab, String xaNhan, String xaPhat, Float soKm, Float requestTime, Long tongCuoc, Long tongCOD, String lyDoHuy, String trangThaiGrab, String trangThaiVTP) throws NoSuchFieldException, IllegalAccessException {
        this.maPhieuGui = maPhieuGui;
        this.tinhNhan = tinhNhan;
        this.tinhPhat = tinhPhat;
        this.huyenNhan = huyenNhan;
        this.huyenPhat = huyenPhat;
        this.maBuuCucGoc = maBuuCucGoc;
        this.maBuuCucHT = maBuuCucHT;
        this.timeYeuCauLayHang = timeYeuCauLayHang;
        this.timeTaoDon = timeTaoDon;
        this.timeNhapMay = timeNhapMay;
        this.timeQuyDinhThu = timeQuyDinhThu;
        this.maTrangThai = maTrangThai;
        this.maDoiTac = maDoiTac;
        this.maKhachHang = maKhachHang;
        this.maDichVu = maDichVu;
        this.loaiDon = loaiDon;
        this.trongLuong = trongLuong;
        this.loaiVung = loaiVung;
        this.maVanDonGrab = maVanDonGrab;
        this.xaNhan = xaNhan;
        this.xaPhat = xaPhat;
        if(soKm == null)
            this.soKm = null;
        else
            this.soKm = df.format(soKm);
        if(requestTime == null)
            this.requestTime = null;
        else
            this.requestTime = df.format(requestTime);
        this.tongCuoc = tongCuoc;
        this.tongCOD = tongCOD;
        this.lyDoHuy = lyDoHuy;
        this.trangThaiGrab = trangThaiGrab;
        this.trangThaiVTP = trangThaiVTP;
        this.objectHashMap = convert(this);
    }

    public String getMaPhieuGui() {
        return maPhieuGui;
    }

    public void setMaPhieuGui(String maPhieuGui) {
        this.maPhieuGui = maPhieuGui;
    }

    public String getTinhNhan() {
        return tinhNhan;
    }

    public void setTinhNhan(String tinhNhan) {
        this.tinhNhan = tinhNhan;
    }

    public String getTinhPhat() {
        return tinhPhat;
    }

    public void setTinhPhat(String tinhPhat) {
        this.tinhPhat = tinhPhat;
    }

    public String getHuyenNhan() {
        return huyenNhan;
    }

    public void setHuyenNhan(String huyenNhan) {
        this.huyenNhan = huyenNhan;
    }

    public String getHuyenPhat() {
        return huyenPhat;
    }

    public void setHuyenPhat(String huyenPhat) {
        this.huyenPhat = huyenPhat;
    }

    public String getMaBuuCucGoc() {
        return maBuuCucGoc;
    }

    public void setMaBuuCucGoc(String maBuuCucGoc) {
        this.maBuuCucGoc = maBuuCucGoc;
    }

    public String getMaBuuCucHT() {
        return maBuuCucHT;
    }

    public void setMaBuuCucHT(String maBuuCucHT) {
        this.maBuuCucHT = maBuuCucHT;
    }

    public String getTimeYeuCauLayHang() {
        return timeYeuCauLayHang;
    }

    public void setTimeYeuCauLayHang(String timeYeuCauLayHang) {
        this.timeYeuCauLayHang = timeYeuCauLayHang;
    }

    public String getTimeTaoDon() {
        return timeTaoDon;
    }

    public void setTimeTaoDon(String timeTaoDon) {
        this.timeTaoDon = timeTaoDon;
    }

    public String getTimeNhapMay() {
        return timeNhapMay;
    }

    public void setTimeNhapMay(String timeNhapMay) {
        this.timeNhapMay = timeNhapMay;
    }

    public String getTimeQuyDinhThu() {
        return timeQuyDinhThu;
    }

    public void setTimeQuyDinhThu(String timeQuyDinhThu) {
        this.timeQuyDinhThu = timeQuyDinhThu;
    }

    public String getMaTrangThai() {
        return maTrangThai;
    }

    public void setMaTrangThai(String maTrangThai) {
        this.maTrangThai = maTrangThai;
    }

    public String getMaDoiTac() {
        return maDoiTac;
    }

    public void setMaDoiTac(String maDoiTac) {
        this.maDoiTac = maDoiTac;
    }

    public String getMaKhachHang() {
        return maKhachHang;
    }

    public void setMaKhachHang(String maKhachHang) {
        this.maKhachHang = maKhachHang;
    }

    public String getTrongLuong() {
        return trongLuong;
    }

    public void setTrongLuong(String trongLuong) {
        this.trongLuong = trongLuong;
    }

    public String getLoaiVung() {
        return loaiVung;
    }

    public void setLoaiVung(String loaiVung) {
        this.loaiVung = loaiVung;
    }

    public String getMaDichVu() {
        return maDichVu;
    }

    public void setMaDichVu(String maDichVu) {
        this.maDichVu = maDichVu;
    }

    public String getLoaiDon() {
        return loaiDon;
    }

    public void setLoaiDon(String loaiDon) {
        this.loaiDon = loaiDon;
    }

    public String getMaVanDonGrab() {
        return maVanDonGrab;
    }

    public void setMaVanDonGrab(String maVanDonGrab) {
        this.maVanDonGrab = maVanDonGrab;
    }

    public String getXaNhan() {
        return xaNhan;
    }

    public void setXaNhan(String xaNhan) {
        this.xaNhan = xaNhan;
    }

    public String getXaPhat() {
        return xaPhat;
    }

    public void setXaPhat(String xaPhat) {
        this.xaPhat = xaPhat;
    }

    public String getSoKm() {
        return soKm;
    }

    public void setSoKm(String soKm) {
        this.soKm = soKm;
    }

    public String getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }

    public Long getTongCuoc() {
        return tongCuoc;
    }

    public void setTongCuoc(Long tongCuoc) {
        this.tongCuoc = tongCuoc;
    }

    public Long getTongCOD() {
        return tongCOD;
    }

    public void setTongCOD(Long tongCOD) {
        this.tongCOD = tongCOD;
    }

    public String getLyDoHuy() {
        return lyDoHuy;
    }

    public void setLyDoHuy(String lyDoHuy) {
        this.lyDoHuy = lyDoHuy;
    }

    public String getTrangThaiGrab() {
        return trangThaiGrab;
    }

    public void setTrangThaiGrab(String trangThaiGrab) {
        this.trangThaiGrab = trangThaiGrab;
    }

    public String getTrangThaiVTP() {
        return trangThaiVTP;
    }

    public void setTrangThaiVTP(String trangThaiVTP) {
        this.trangThaiVTP = trangThaiVTP;
    }
}
