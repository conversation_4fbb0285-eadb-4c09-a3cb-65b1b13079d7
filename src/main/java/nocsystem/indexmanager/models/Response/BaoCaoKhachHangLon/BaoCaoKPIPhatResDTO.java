package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BaoCaoKPIPhatResDTO {

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("doi_tac")
    private String doiTac;

    @JsonProperty("dich_vu")
    private String dichVu;

    @JsonProperty("loai_baocao")
    private Integer loaiBaoCao;

    @JsonProperty("sl_ptc_dunggio")
    private Integer slPTCDungGio;

    @JsonProperty("sl_ptc_dunggio_landau")
    private Integer slPTCDungGioLanDau;

    @JsonProperty("sl_phaiphat")
    private Integer slPhaiPhat;

    @JsonProperty("sl_chua_pcp")
    private Integer slChuaPCP;

    @JsonProperty("sl_pcp")
    private Integer slPCP;

    @JsonProperty("tt500")
    private Integer tt500;

    @JsonProperty("tt_khac")
    private Integer ttKhac;

    @JsonProperty("sl_ptc")
    private Integer slPTC;

    @JsonProperty("tl_ptc")
    private Float tlPTC;

    @JsonProperty("tl_ptc_dunggio")
    private Float tlPTCDungGio;

    @JsonProperty("tl_ptc_dunggio_landau")
    private Float tlPTCDungGioLanDau;

    @JsonProperty("ton_xanh")
    private Integer tonXanh;

    @JsonProperty("ton_vang")
    private Integer tonVang;

    @JsonProperty("ton_do_1")
    private Integer tonDo1;

    @JsonProperty("ton_do_2")
    private Integer tonDo2;

    @JsonProperty("ton_do_3")
    private Integer tonDo3;

    @JsonProperty("ton_do_4")
    private Integer tonDo4;

    @JsonProperty("ton_do_5")
    private Integer tonDo5;

    @JsonProperty("ton_do_6")
    private Integer tonDo6;

    @JsonProperty("ton_do_7")
    private Integer tonDo7;

    @JsonProperty("ton_do_8")
    private Integer tonDo8;

    @JsonProperty("ton_do_9")
    private Integer tonDo9;

    @JsonProperty("tong_ton")
    private Integer tongTon;
}
