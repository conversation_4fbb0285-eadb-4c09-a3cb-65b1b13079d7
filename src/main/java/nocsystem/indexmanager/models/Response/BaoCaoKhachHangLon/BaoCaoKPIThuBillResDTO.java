package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BaoCaoKPIThuBillResDTO {

    @JsonProperty("thoi_gian")
    private LocalDate thoiGian;

    @JsonProperty("ma_phieugui")
    private String maPhieuGui;

    @JsonProperty("vung")
    private String vung;

    @JsonProperty("tinh_nhan")
    private String tinhNhan;

    @JsonProperty("tinh_phat")
    private String tinhPhat;

    @JsonProperty("huyen_nhan")
    private String huyenNhan;

    @JsonProperty("huyen_phat")
    private String huyenPhat;

    @JsonProperty("ma_xanhan")
    private String maXaNhan;

    @JsonProperty("ma_xaphat")
    private String maXaPhat;

    @JsonProperty("ma_dv_viettel")
    private String maDVViettel;

    @JsonProperty("ma_buucuc_goc")
    private String maBuuCucGoc;

    @JsonProperty("ma_buucuc_phat")
    private String maBuuCucPhat;

    @JsonProperty("time_yeucau_layhang")
    private Date timeYeuCauLayHang;

    @JsonProperty("thoigiantaodon")
    private Date thoiGianTaoDon;

    @JsonProperty("time_thulan1")
    private Date timeThuLan1;

    @JsonProperty("time_thulan2")
    private Date timeThuLan2;

    @JsonProperty("time_thulan3")
    private Date timeThuLan3;

    @JsonProperty("time_nhap_may")
    private Date timeNhapMay;

    @JsonProperty("ma_trangthai")
    private String maTrangThai;

    @JsonProperty("ma_doitac")
    private String maDoiTac;

    @JsonProperty("ma_khgui")
    private String maKHGui;

    @JsonProperty("ma_dichvu_doitac")
    private String maDichVuDoiTac;

    @JsonProperty("tien_cod")
    private Integer tienCOD;

    @JsonProperty("tienhang")
    private Integer tienhang;

    @JsonProperty("trong_luong")
    private Float trongLuong;

    @JsonProperty("tong_cuoc")
    private Integer tongCuoc;

    @JsonProperty("canh_bao")
    private String canhBao;

    @JsonProperty("ly_do_tt1")
    private String lyDoTT1;

    @JsonProperty("ly_do_tt2")
    private String lyDoTT2;

    @JsonProperty("ly_do_tt3")
    private String lyDoTT3;

    @JsonProperty("buu_ta_ntc")
    private String buuTaNTC;

    @JsonProperty("buu_ta_td_1")
    private String buuTaTD1;

    @JsonProperty("chenh_lech")
    private Float chenhLech;

    @JsonProperty("danh_gia")
    private String danhGia;

    @JsonProperty("time_kenoidi_tai_goc")
    private Date timeKetNoiDiTaiGoc;

    @JsonProperty("loai_don")
    private String loaiDon;

    @JsonProperty("buu_ta_tonthu")
    private String buu_ta_tonthu;

    @JsonProperty("time_ht")
    private Date time_ht;
}
