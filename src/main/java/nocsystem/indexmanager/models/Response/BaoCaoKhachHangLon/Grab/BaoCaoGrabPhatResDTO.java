package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;


@NoArgsConstructor
public class BaoCaoGrabPhatResDTO {

    private String donVi;

    private Long slPhaiPhat;

    private Long slTonNhanBanGiao;

    private Long tt500;

    private Long ttKhac;

    private Long slPTC;

    private Float tlHoan;

    private Long slHoan;

    private Long slHuy;

    private Long slPTCDungGio;

    private Float tlPTCDungGio;

    private Float tlPTC;

    private Long tongTon;

    @JsonIgnore
    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public BaoCaoGrabPhatResDTO(String donVi, Long slPhaiPhat, Long slTonNhanBanGiao, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slPTCDungGio, Long tongTon) throws NoSuchFieldException, IllegalAccessException {
        this.donVi = donVi;
        this.slPhaiPhat = slPhaiPhat;
        this.slTonNhanBanGiao = slTonNhanBanGiao;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slPTCDungGio = slPTCDungGio;
        this.tongTon = tongTon;
        this.objectHashMap = convert(this);
    }

    public BaoCaoGrabPhatResDTO(String donVi, Long slPhaiPhat, Long slTonNhanBanGiao, Long tt500, Long ttKhac, Long slPTC, Float tlHoan, Long slHoan, Long slHuy, Long slPTCDungGio, Float tlPTCDungGio, Float tlPTC, Long tongTon) throws NoSuchFieldException, IllegalAccessException {
        this.donVi = donVi;
        this.slPhaiPhat = slPhaiPhat;
        this.slTonNhanBanGiao = slTonNhanBanGiao;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.tlHoan = tlHoan;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slPTCDungGio = slPTCDungGio;
        this.tlPTCDungGio = tlPTCDungGio;
        this.tlPTC = tlPTC;
        this.tongTon = tongTon;
        this.objectHashMap = convert(this);
    }

    public Long getTongTon() {
        return tongTon;
    }

    public void setTongTon(Long tongTon) {
        this.tongTon = tongTon;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getSlPhaiPhat() {
        return slPhaiPhat;
    }

    public void setSlPhaiPhat(Long slPhaiPhat) {
        this.slPhaiPhat = slPhaiPhat;
    }

    public Long getSlTonNhanBanGiao() {
        return slTonNhanBanGiao;
    }

    public void setSlTonNhanBanGiao(Long slTonNhanBanGiao) {
        this.slTonNhanBanGiao = slTonNhanBanGiao;
    }

    public Long getTt500() {
        return tt500;
    }

    public void setTt500(Long tt500) {
        this.tt500 = tt500;
    }

    public Long getTtKhac() {
        return ttKhac;
    }

    public void setTtKhac(Long ttKhac) {
        this.ttKhac = ttKhac;
    }

    public Long getSlPTC() {
        return slPTC;
    }

    public void setSlPTC(Long slPTC) {
        this.slPTC = slPTC;
    }

    public Float getTlHoan() {
        return tlHoan;
    }

    public void setTlHoan(Float tlHoan) {
        this.tlHoan = tlHoan;
    }

    public Long getSlHoan() {
        return slHoan;
    }

    public void setSlHoan(Long slHoan) {
        this.slHoan = slHoan;
    }

    public Long getSlHuy() {
        return slHuy;
    }

    public void setSlHuy(Long slHuy) {
        this.slHuy = slHuy;
    }

    public Long getSlPTCDungGio() {
        return slPTCDungGio;
    }

    public void setSlPTCDungGio(Long slPTCDungGio) {
        this.slPTCDungGio = slPTCDungGio;
    }

    public Float getTlPTCDungGio() {
        return tlPTCDungGio;
    }

    public void setTlPTCDungGio(Float tlPTCDungGio) {
        this.tlPTCDungGio = tlPTCDungGio;
    }

    public Float getTlPTC() {
        return tlPTC;
    }

    public void setTlPTC(Float tlPTC) {
        this.tlPTC = tlPTC;
    }
}
