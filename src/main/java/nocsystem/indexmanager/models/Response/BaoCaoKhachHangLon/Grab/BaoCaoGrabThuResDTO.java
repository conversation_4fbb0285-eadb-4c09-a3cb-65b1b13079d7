package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon.Grab;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;


@NoArgsConstructor
@AllArgsConstructor
public class BaoCaoGrabThuResDTO {

    private String donVi;

    private Long sL;

    private Long dropOff;

    private Long slThuTC;

    private Long slThuDG;

    private Float tlThuTC;

    private Float tlThuDG;

    private Long tonSLAConHan;

    private Long tonSLAQuaHan;

    private Long huy;

    private Long tongTon;

    @JsonIgnore
    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            } else if (targetField.getType() == int.class || targetField.getType() == Integer.class) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == boolean.class || targetField.getType() == Boolean.class) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (targetField.getType() == LocalDate.class) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            objectHashMap.put(field.getName(), fieldValue);
            i++;
        }
        return objectHashMap;
    }

    public BaoCaoGrabThuResDTO(String donVi, Long slPhatSinh, Long slDropOff, Long slThuTC, Long slThuDG, Long tonSLAConHan, Long tonSLAQuaHan, Long huy, Long tongTon) throws NoSuchFieldException, IllegalAccessException {
        this.donVi = donVi;
        this.sL = slPhatSinh;
        this.dropOff = slDropOff;
        this.slThuTC = slThuTC;
        this.slThuDG = slThuDG;
        this.tonSLAConHan = tonSLAConHan;
        this.tonSLAQuaHan = tonSLAQuaHan;
        this.huy = huy;
        this.tongTon = tongTon;
        this.objectHashMap = convert(this);
    }

    public BaoCaoGrabThuResDTO(String donVi, Long sL, Long dropOff, Long slThuTC, Long slThuDG, Float tlThuTC, Float tlThuDG, Long tonSLAConHan, Long tonSLAQuaHan, Long huy, Long tongTon) throws NoSuchFieldException, IllegalAccessException {
        this.donVi = donVi;
        this.sL = sL;
        this.dropOff = dropOff;
        this.slThuTC = slThuTC;
        this.slThuDG = slThuDG;
        this.tlThuTC = tlThuTC;
        this.tlThuDG = tlThuDG;
        this.tonSLAConHan = tonSLAConHan;
        this.tonSLAQuaHan = tonSLAQuaHan;
        this.huy = huy;
        this.tongTon = tongTon;
        this.objectHashMap = convert(this);
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getsL() {
        return sL;
    }

    public void setsL(Long sL) {
        this.sL = sL;
    }

    public Long getDropOff() {
        return dropOff;
    }

    public void setDropOff(Long dropOff) {
        this.dropOff = dropOff;
    }

    public Long getSlThuTC() {
        return slThuTC;
    }

    public void setSlThuTC(Long slThuTC) {
        this.slThuTC = slThuTC;
    }

    public Long getSlThuDG() {
        return slThuDG;
    }

    public void setSlThuDG(Long slThuDG) {
        this.slThuDG = slThuDG;
    }

    public Float getTlThuTC() {
        return tlThuTC;
    }

    public void setTlThuTC(Float tlThuTC) {
        this.tlThuTC = tlThuTC;
    }

    public Float getTlThuDG() {
        return tlThuDG;
    }

    public void setTlThuDG(Float tlThuDG) {
        this.tlThuDG = tlThuDG;
    }

    public Long getTonSLAConHan() {
        return tonSLAConHan;
    }

    public void setTonSLAConHan(Long tonSLAConHan) {
        this.tonSLAConHan = tonSLAConHan;
    }

    public Long getTonSLAQuaHan() {
        return tonSLAQuaHan;
    }

    public void setTonSLAQuaHan(Long tonSLAQuaHan) {
        this.tonSLAQuaHan = tonSLAQuaHan;
    }

    public Long getHuy() {
        return huy;
    }

    public void setHuy(Long huy) {
        this.huy = huy;
    }

    public Long getTongTon() {
        return tongTon;
    }

    public void setTongTon(Long tongTon) {
        this.tongTon = tongTon;
    }
}
