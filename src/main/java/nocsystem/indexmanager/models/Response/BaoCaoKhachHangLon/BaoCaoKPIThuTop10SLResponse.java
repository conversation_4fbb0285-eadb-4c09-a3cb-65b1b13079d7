package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public class BaoCaoKPIThuTop10SLResponse implements Comparable<BaoCaoKPIThuTop10SLResponse>{

    @Override
    public int compareTo(BaoCaoKPIThuTop10SLResponse t) {
        return this.getsL().compareTo(t.getsL());
    }

    private String donVi;

    private Long sL;

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getsL() {
        return sL;
    }

    public void setsL(Long sL) {
        this.sL = sL;
    }
}
