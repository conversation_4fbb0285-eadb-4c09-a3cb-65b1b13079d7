package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import java.time.LocalDate;


public class BaoCaoKPIThuResDTO2 {

    private LocalDate thoiGian;

    private String donVi;

    private String vungCon;

    private Long sL;

    private Long dropOff;

    private Long slThuTC;

    private Long huy;

    private Long giaiTrinh;

    private Long chuaGiaiTrinh;

    private Long slThuDG;

    private Long slThuDG1;

    private Float tlThuTC;

    private Float tlThuDG;

    private Float tlThuDG1;

    private Long xanh;

    private Long vang;

    private Long do1;

    private Long do2;

    private Long do3;

    private Long tongTon;

    private Long tonSLAQuaHan;

    private Long tonSLAConHan;

    private Long tongSlLayHangDungHen;

    private Long tongSlLayHangTcDungHen;

    private Long tongSlTrongKy;

    private Float tlLayHangDungHen;

    private Float tlLayHangTcDungHen;

    public BaoCaoKPIThuResDTO2() {

    }

    public BaoCaoKPIThuResDTO2(String donVi, <PERSON> sL, Long dropOff, <PERSON> slThuTC, <PERSON> huy, <PERSON> giaiTrinh, <PERSON> chuaGiaiTrinh, Long slThuDG, Long slThuDG1, Long xanh, Long vang, Long do1, Long do2, Long do3, Long tongTon, Long tonSLAQuaHan, Long tonSLAConHan) {
        this.donVi = donVi;
        this.sL = sL;
        this.dropOff = dropOff;
        this.slThuTC = slThuTC;
        this.huy = huy;
        this.giaiTrinh = giaiTrinh;
        this.chuaGiaiTrinh = chuaGiaiTrinh;
        this.slThuDG = slThuDG;
        this.slThuDG1 = slThuDG1;
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.tongTon = tongTon;
        this.tonSLAQuaHan = tonSLAQuaHan;
        this.tonSLAConHan = tonSLAConHan;
    }

    public BaoCaoKPIThuResDTO2(String donVi, Long sL, Long dropOff, Long slThuTC, Long huy, Long giaiTrinh, Long chuaGiaiTrinh, Long slThuDG, Long slThuDG1, Long xanh, Long vang, Long do1, Long do2, Long do3, Long tongTon, Long tonSLAQuaHan, Long tonSLAConHan, Long tongSlLayHangDungHen, Long tongSlLayHangTcDungHen, Long tongSlTrongKy) {
        this.donVi = donVi;
        this.sL = sL;
        this.dropOff = dropOff;
        this.slThuTC = slThuTC;
        this.huy = huy;
        this.giaiTrinh = giaiTrinh;
        this.chuaGiaiTrinh = chuaGiaiTrinh;
        this.slThuDG = slThuDG;
        this.slThuDG1 = slThuDG1;
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.tongTon = tongTon;
        this.tonSLAQuaHan = tonSLAQuaHan;
        this.tonSLAConHan = tonSLAConHan;
        this.tongSlLayHangDungHen = tongSlLayHangDungHen;
        this.tongSlLayHangTcDungHen = tongSlLayHangTcDungHen;
        this.tongSlTrongKy = tongSlTrongKy;
    }

    public BaoCaoKPIThuResDTO2(String donVi, Long tongTon, Long tonSLAQuaHan, Long tonSLAConHan) {
        this.donVi = donVi;
        this.tongTon = tongTon;
        this.tonSLAQuaHan = tonSLAQuaHan;
        this.tonSLAConHan = tonSLAConHan;
    }

    public BaoCaoKPIThuResDTO2(Long xanh, Long vang, Long do1, Long do2, Long do3, Long tongTon) {
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.tongTon = tongTon;
    }

    public BaoCaoKPIThuResDTO2(String donVi, String vungCon, Long sL, Long dropOff, Long slThuTC, Long huy, Long giaiTrinh, Long chuaGiaiTrinh, Long slThuDG, Long slThuDG1, Long xanh, Long vang, Long do1, Long do2, Long do3, Long tongTon, Long tonSLAQuaHan, Long tonSLAConHan, Long tongSlLayHangDungHen, Long tongSlLayHangTcDungHen, Long tongSlTrongKy) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.sL = sL;
        this.dropOff = dropOff;
        this.slThuTC = slThuTC;
        this.huy = huy;
        this.giaiTrinh = giaiTrinh;
        this.chuaGiaiTrinh = chuaGiaiTrinh;
        this.slThuDG = slThuDG;
        this.slThuDG1 = slThuDG1;
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.tongTon = tongTon;
        this.tonSLAQuaHan = tonSLAQuaHan;
        this.tonSLAConHan = tonSLAConHan;
        this.tongSlLayHangDungHen = tongSlLayHangDungHen;
        this.tongSlLayHangTcDungHen = tongSlLayHangTcDungHen;
        this.tongSlTrongKy = tongSlTrongKy;
    }

    public BaoCaoKPIThuResDTO2(String donVi, String vungCon, Long sL, Long dropOff, Long slThuTC, Long huy, Long giaiTrinh, Long chuaGiaiTrinh, Long slThuDG, Long slThuDG1, Long xanh, Long vang, Long do1, Long do2, Long do3, Long tongTon, Long tonSLAQuaHan, Long tonSLAConHan) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.sL = sL;
        this.dropOff = dropOff;
        this.slThuTC = slThuTC;
        this.huy = huy;
        this.giaiTrinh = giaiTrinh;
        this.chuaGiaiTrinh = chuaGiaiTrinh;
        this.slThuDG = slThuDG;
        this.slThuDG1 = slThuDG1;
        this.xanh = xanh;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.tongTon = tongTon;
        this.tonSLAQuaHan = tonSLAQuaHan;
        this.tonSLAConHan = tonSLAConHan;
    }

    public LocalDate getThoiGian() {
        return thoiGian;
    }

    public void setThoiGian(LocalDate thoiGian) {
        this.thoiGian = thoiGian;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getsL() {
        return sL;
    }

    public void setsL(Long sL) {
        this.sL = sL;
    }

    public Long getSlThuTC() {
        return slThuTC;
    }

    public void setSlThuTC(Long slThuTC) {
        this.slThuTC = slThuTC;
    }

    public Long getSlThuDG() {
        return slThuDG;
    }

    public void setSlThuDG(Long slThuDG) {
        this.slThuDG = slThuDG;
    }

    public Long getSlThuDG1() {
        return slThuDG1;
    }

    public void setSlThuDG1(Long slThuDG1) {
        this.slThuDG1 = slThuDG1;
    }

    public Float getTlThuTC() {
        return tlThuTC;
    }

    public void setTlThuTC(Float tlThuTC) {
        this.tlThuTC = tlThuTC;
    }

    public Float getTlThuDG() {
        return tlThuDG;
    }

    public void setTlThuDG(Float tlThuDG) {
        this.tlThuDG = tlThuDG;
    }

    public Float getTlThuDG1() {
        return tlThuDG1;
    }

    public void setTlThuDG1(Float tlThuDG1) {
        this.tlThuDG1 = tlThuDG1;
    }

    public Long getHuy() {
        return huy;
    }

    public void setHuy(Long huy) {
        this.huy = huy;
    }

    public Long getXanh() {
        return xanh;
    }

    public void setXanh(Long xanh) {
        this.xanh = xanh;
    }

    public Long getVang() {
        return vang;
    }

    public void setVang(Long vang) {
        this.vang = vang;
    }

    public Long getDo1() {
        return do1;
    }

    public void setDo1(Long do1) {
        this.do1 = do1;
    }

    public Long getDo2() {
        return do2;
    }

    public void setDo2(Long do2) {
        this.do2 = do2;
    }

    public Long getDo3() {
        return do3;
    }

    public void setDo3(Long do3) {
        this.do3 = do3;
    }

    public Long getTongTon() {
        return tongTon;
    }

    public void setTongTon(Long tongTon) {
        this.tongTon = tongTon;
    }

    public Long getDropOff() {
        return dropOff;
    }

    public void setDropOff(Long dropOff) {
        this.dropOff = dropOff;
    }

    public Long getGiaiTrinh() {
        return giaiTrinh;
    }

    public void setGiaiTrinh(Long giaiTrinh) {
        this.giaiTrinh = giaiTrinh;
    }

    public Long getChuaGiaiTrinh() {
        return chuaGiaiTrinh;
    }

    public void setChuaGiaiTrinh(Long chuaGiaiTrinh) {
        this.chuaGiaiTrinh = chuaGiaiTrinh;
    }

    public Long getTonSLAQuaHan() {
        return tonSLAQuaHan;
    }

    public void setTonSLAQuaHan(Long tonSLAQuaHan) {
        this.tonSLAQuaHan = tonSLAQuaHan;
    }

    public Long getTonSLAConHan() {
        return tonSLAConHan;
    }

    public void setTonSLAConHan(Long tonSLAConHan) {
        this.tonSLAConHan = tonSLAConHan;
    }

    public Long getTongSlLayHangDungHen() {
        return tongSlLayHangDungHen;
    }

    public void setTongSlLayHangDungHen(Long tongSlLayHangDungHen) {
        this.tongSlLayHangDungHen = tongSlLayHangDungHen;
    }

    public Long getTongSlLayHangTcDungHen() {
        return tongSlLayHangTcDungHen;
    }

    public void setTongSlLayHangTcDungHen(Long tongSlLayHangTcDungHen) {
        this.tongSlLayHangTcDungHen = tongSlLayHangTcDungHen;
    }

    public Long getTongSlTrongKy() {
        return tongSlTrongKy;
    }

    public void setTongSlTrongKy(Long tongSlTrongKy) {
        this.tongSlTrongKy = tongSlTrongKy;
    }

    public Float getTlLayHangDungHen() {
        return tlLayHangDungHen;
    }

    public void setTlLayHangDungHen(Float tlLayHangDungHen) {
        this.tlLayHangDungHen = tlLayHangDungHen;
    }

    public Float getTlLayHangTcDungHen() {
        return tlLayHangTcDungHen;
    }

    public void setTlLayHangTcDungHen(Float tlLayHangTcDungHen) {
        this.tlLayHangTcDungHen = tlLayHangTcDungHen;
    }

    public String getVungCon() {
        return vungCon;
    }

    public void setVungCon(String vungCon) {
        this.vungCon = vungCon;
    }

    @Override
    public String toString() {
        return "BaoCaoKPIThuResDTO2{" +
                "thoiGian=" + thoiGian +
                ", donVi='" + donVi + '\'' +
                ", sL=" + sL +
                ", slThuTC=" + slThuTC +
                ", slThuDG=" + slThuDG +
                ", slThuDG1=" + slThuDG1 +
                ", tlThuTC=" + tlThuTC +
                ", tlThuDG=" + tlThuDG +
                ", tlThuDG1=" + tlThuDG1 +
                ", huy=" + huy +
                ", xanh=" + xanh +
                ", vang=" + vang +
                ", do1=" + do1 +
                ", do2=" + do2 +
                ", do3=" + do3 +
                ", tongTon=" + tongTon +
                '}';
    }
}
