package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class DashboardTikTokKpiPhatResponse {

    /*sl phai phat + sl ptc*/
    @JsonProperty("tong_sl_phat")
    private Long tongSlPhat;

    @JsonProperty("chenh_lech_tong_phat")
    private Long chechLechTongPhat;

    @JsonProperty("sl_ptc")
    private Long slPTC;

    @JsonProperty("chenh_lech_ptc")
    private Long chenhLechSlPTC;

    /*sl PTC / tong sl phat*/
    @JsonProperty("ty_le_ptc")
    private Double tyLePTC;

    /*Thanh cong dung gio*/
    @JsonProperty("sl_phat_dung_han_tt")
    private Long slPhatThanhCongDungGio;

    @JsonProperty("chenh_lech_phat_dung_han_tt")
    private Long chechLechPhatThanhCongDungGio;

    /* sl ptc dung gio / sl ptc*/
    @JsonProperty("ty_le_phat_dung_han_tt")
    private Double tyLePhatDungHanThucTe;

    @JsonProperty("sl_phat_lan1_dung_han")
    private Long slPhatLan1DungHan;

    @JsonProperty("chenh_lech_phat_lan1_dung_han")
    private Long chenhLechPhatLan1DungHan;

    /* sl ptc dung gio lan1 / sl ptc */
    @JsonProperty("ty_le_phat_lan1_dung_han")
    private Double tyLePhatLan1DungHan;

    /* tong ton */
    @JsonProperty("sl_ton_phat")
    private Long slTonPhat;

    @JsonProperty("chenh_lech_ton_phat")
    private Long chenhLechTonPhat;

    /* sl_phaiphat */
    @JsonProperty("sl_phai_phat")
    private Long slPhaiPhat;

    @JsonProperty("chenh_lech_phai_phat")
    private Long chenhLechPhaiPhat;

    @JsonIgnore
    private LocalDate ngayBaoCao;

    @JsonIgnore
    private Long slPhaiPhatLuyKe;

    public DashboardTikTokKpiPhatResponse(Long slPhaiPhat, Long slPhaiPhatLuyKe) {
        this.slPhaiPhat = slPhaiPhat;
        this.slPhaiPhatLuyKe = slPhaiPhatLuyKe;
    }

    public DashboardTikTokKpiPhatResponse(long tongSlPhat, long slPTC, long slPhatThanhCongDungGio, long slPhatLan1DungHan, long slTonPhat, long slPhaiPhat, LocalDate ngayBaoCao) {
        this.tongSlPhat = tongSlPhat;
        this.slPTC = slPTC;
        this.slPhatThanhCongDungGio = slPhatThanhCongDungGio;
        this.slPhatLan1DungHan = slPhatLan1DungHan;
        this.slTonPhat = slTonPhat;
        this.slPhaiPhat = slPhaiPhat;
        this.ngayBaoCao = ngayBaoCao;
    }

    public DashboardTikTokKpiPhatResponse(Long tongSlPhat, Long chechLechTongPhat, Long slPTC, Long chenhLechSlPTC, Double tyLePTC, Long slPhatThanhCongDungGio, Long chechLechPhatThanhCongDungGio, Double tyLePhatDungHanThucTe, Long slPhatLan1DungHan, Long chenhLechPhatLan1DungHan, Double tyLePhatLan1DungHan, Long slTonPhat, Long chenhLechTonPhat, Long slPhaiPhat, Long chenhLechPhaiPhat) {
        this.tongSlPhat = tongSlPhat;
        this.chechLechTongPhat = chechLechTongPhat;
        this.slPTC = slPTC;
        this.chenhLechSlPTC = chenhLechSlPTC;
        this.tyLePTC = tyLePTC;
        this.slPhatThanhCongDungGio = slPhatThanhCongDungGio;
        this.chechLechPhatThanhCongDungGio = chechLechPhatThanhCongDungGio;
        this.tyLePhatDungHanThucTe = tyLePhatDungHanThucTe;
        this.slPhatLan1DungHan = slPhatLan1DungHan;
        this.chenhLechPhatLan1DungHan = chenhLechPhatLan1DungHan;
        this.tyLePhatLan1DungHan = tyLePhatLan1DungHan;
        this.slTonPhat = slTonPhat;
        this.chenhLechTonPhat = chenhLechTonPhat;
        this.slPhaiPhat = slPhaiPhat;
        this.chenhLechPhaiPhat = chenhLechPhaiPhat;
    }

    public void setValueChenhLech(Long chechLechTongPhat, Long chenhLechSlPTC, Long chechLechPhatThanhCongDungGio, Long chenhLechPhatLan1DungHan, Long chenhLechTonPhat, Long chenhLechPhaiPhat) {
        this.chechLechTongPhat = chechLechTongPhat;
        this.chenhLechSlPTC = chenhLechSlPTC;
        this.chechLechPhatThanhCongDungGio = chechLechPhatThanhCongDungGio;
        this.chenhLechPhatLan1DungHan = chenhLechPhatLan1DungHan;
        this.chenhLechTonPhat = chenhLechTonPhat;
        this.chenhLechPhaiPhat = chenhLechPhaiPhat;
    }

    public void setTongSanLuong(Long tongSlPhat,
                                Long slPTC,
                                Long slPhatThanhCongDungGio,
                                Long slPhatLan1DungHan,
                                Long slTonPhat,
                                Long slPhaiPhat
                                ){
        this.tongSlPhat = tongSlPhat;
        this.slPTC = slPTC;
        this.slPhatThanhCongDungGio = slPhatThanhCongDungGio;
        this.slPhatLan1DungHan = slPhatLan1DungHan;
        this.slTonPhat = slTonPhat;
        this.slPhaiPhat = slPhaiPhat;

    }

    public void setTyLe(Double tyLePTC, Double tyLePhatDungHanThucTe, Double tyLePhatLan1DungHan) {
        this.tyLePTC = tyLePTC;
        this.tyLePhatDungHanThucTe = tyLePhatDungHanThucTe;
        this.tyLePhatLan1DungHan = tyLePhatLan1DungHan;
    }
}
