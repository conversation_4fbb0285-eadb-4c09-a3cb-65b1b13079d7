package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import lombok.AllArgsConstructor;

import java.time.LocalDate;

@AllArgsConstructor
public class BaoCaoKPIPhatResDTO2 {

    private LocalDate ngayBaoCao;

    private String donVi;

    private String vungCon;

    private Long tongSanLuong;

    private Long slPTCDungGio;

    private Long slPTCDungGioLanDau;

    private Long slPhaiPhat;

    private Long slChuaPCP;

    private Long tt500;

    private Long ttKhac;

    private Long slPTC;

    private Float tlPTC;

    private Long slHoan;

    private Long slHuy;

    private Long slDenBu;

    private Float tlPTCDungGio;

    private Float tlPTCDungGioLanDau;

    private Long tonXanh;

    private Long tonVang;

    private Long tonDo1;

    private Long tonDo2;

    private Long tonDo3;

    private Long tonDo4;

    private Long tonDo5;

    private Long tonDo6;

    private Long tonDo7;

    private Long tonDo8;

    private Long tonDo9;

    private Long tongTon;

    private Long tuHoan;

    private <PERSON> mauHoan;
    private Long tuHoanLk;

    private Long mauHoanLk;

    private Float tlHoan;

    private String vungPhat;

    private Long tuGiaoHangDungHen;

    private Long mauGiaoHangDungHen;

    private Long tuGiaoThanhCongLanDau;

    private Long mauGiaoThanhCongLanDau;

    private Float tlGiaoHangDungHen;

    private Float tlGiaoThanhCongLanDau;

    private Long tuGiaoThanhCongLanDauLK;

    private Long mauGiaoThanhCongLanDauLK;

    private Float tlGiaoThanhCongLanDauLK;


    public BaoCaoKPIPhatResDTO2() {

    }

    public BaoCaoKPIPhatResDTO2(String donVi, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk, Long tuGiaoHangDungHen, Long mauGiaoHangDungHen, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau) {
        this.donVi = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
        this.tuGiaoHangDungHen = tuGiaoHangDungHen;
        this.mauGiaoHangDungHen = mauGiaoHangDungHen;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan) {
        this.donVi = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk) {
        this.donVi = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
    }

    //truyền thêm 1 biến thừa để tránh trùng lặp constructor
    public BaoCaoKPIPhatResDTO2(String donVi, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau2) {
        this.donVi = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    //truyền thêm 1 biến thừa để tránh trùng lặp constructor
    public BaoCaoKPIPhatResDTO2(String donVi, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau2) {
        this.donVi = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    //truyền thêm 1 biến thừa để tránh trùng lặp constructor
    public BaoCaoKPIPhatResDTO2(String donVi,String vungCon, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau2) {
        this.donVi = donVi;
        this.vungCon = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    //truyền thêm 1 biến thừa để tránh trùng lặp constructor
    public BaoCaoKPIPhatResDTO2(String donVi, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau, Long tuGiaoThanhCongLanDauLK, Long mauGiaoThanhCongLanDauLK, Long mauGiaoThanhCongLanDauLK2) {
        this.donVi = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
        this.tuGiaoThanhCongLanDauLK = tuGiaoThanhCongLanDauLK;
        this.mauGiaoThanhCongLanDauLK = mauGiaoThanhCongLanDauLK;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan, Long tuGiaoHangDungHen, Long mauGiaoHangDungHen, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau) {
        this.donVi = donVi;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuGiaoHangDungHen = tuGiaoHangDungHen;
        this.mauGiaoHangDungHen = mauGiaoHangDungHen;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, Long slPhaiPhat, Long tt500, Long ttKhac, Long tongTon) {
        this.donVi = donVi;
        this.slPhaiPhat = slPhaiPhat;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.tongTon = tongTon;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, Long tuHoan, Long mauHoan) {
        this.donVi = donVi;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
    }

    public BaoCaoKPIPhatResDTO2(Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon) {
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, String vungCon, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau2) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, String vungCon, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan, Long tuGiaoHangDungHen, Long mauGiaoHangDungHen, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuGiaoHangDungHen = tuGiaoHangDungHen;
        this.mauGiaoHangDungHen = mauGiaoHangDungHen;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, String vungCon, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, String vungCon, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau, Long tuGiaoThanhCongLanDauLK, Long mauGiaoThanhCongLanDauLK, Long mauGiaoThanhCongLanDauLK2) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
        this.tuGiaoThanhCongLanDauLK = tuGiaoThanhCongLanDauLK;
        this.mauGiaoThanhCongLanDauLK = mauGiaoThanhCongLanDauLK;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, String vungCon, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk, Long tuGiaoHangDungHen, Long mauGiaoHangDungHen, Long tuGiaoThanhCongLanDau, Long mauGiaoThanhCongLanDau) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
        this.tuGiaoHangDungHen = tuGiaoHangDungHen;
        this.mauGiaoHangDungHen = mauGiaoHangDungHen;
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, String vungCon, Long tongSanLuong, Long slPTCDungGio, Long slPTCDungGioLanDau, Long slPhaiPhat, Long slChuaPCP, Long tt500, Long ttKhac, Long slPTC, Long slHoan, Long slHuy, Long slDenBu, Long tonXanh, Long tonVang, Long tonDo1, Long tonDo2, Long tonDo3, Long tonDo4, Long tonDo5, Long tonDo6, Long tonDo7, Long tonDo8, Long tonDo9, Long tongTon, Long tuHoan, Long mauHoan,Long tuHoanLk,Long mauHoanLk) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.tongSanLuong = tongSanLuong;
        this.slPTCDungGio = slPTCDungGio;
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
        this.slPhaiPhat = slPhaiPhat;
        this.slChuaPCP = slChuaPCP;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.slPTC = slPTC;
        this.slHoan = slHoan;
        this.slHuy = slHuy;
        this.slDenBu = slDenBu;
        this.tonXanh = tonXanh;
        this.tonVang = tonVang;
        this.tonDo1 = tonDo1;
        this.tonDo2 = tonDo2;
        this.tonDo3 = tonDo3;
        this.tonDo4 = tonDo4;
        this.tonDo5 = tonDo5;
        this.tonDo6 = tonDo6;
        this.tonDo7 = tonDo7;
        this.tonDo8 = tonDo8;
        this.tonDo9 = tonDo9;
        this.tongTon = tongTon;
        this.tuHoan = tuHoan;
        this.mauHoan = mauHoan;
        this.tuHoanLk = tuHoanLk;
        this.mauHoanLk = mauHoanLk;
    }

    public BaoCaoKPIPhatResDTO2(String donVi, String vungCon, Long slPhaiPhat, Long tt500, Long ttKhac, Long tongTon) {
        this.donVi = donVi;
        this.vungCon = vungCon;
        this.slPhaiPhat = slPhaiPhat;
        this.tt500 = tt500;
        this.ttKhac = ttKhac;
        this.tongTon = tongTon;
    }

    public LocalDate getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(LocalDate ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getDonVi() {
        return donVi;
    }

    public void setDonVi(String donVi) {
        this.donVi = donVi;
    }

    public Long getTongSanLuong() {
        return tongSanLuong;
    }

    public void setTongSanLuong(Long tongSanLuong) {
        this.tongSanLuong = tongSanLuong;
    }

    public Long getSlPTCDungGio() {
        return slPTCDungGio;
    }

    public void setSlPTCDungGio(Long slPTCDungGio) {
        this.slPTCDungGio = slPTCDungGio;
    }

    public Long getSlPTCDungGioLanDau() {
        return slPTCDungGioLanDau;
    }

    public void setSlPTCDungGioLanDau(Long slPTCDungGioLanDau) {
        this.slPTCDungGioLanDau = slPTCDungGioLanDau;
    }

    public Long getSlPhaiPhat() {
        return slPhaiPhat;
    }

    public void setSlPhaiPhat(Long slPhaiPhat) {
        this.slPhaiPhat = slPhaiPhat;
    }

    public Long getSlChuaPCP() {
        return slChuaPCP;
    }

    public void setSlChuaPCP(Long slChuaPCP) {
        this.slChuaPCP = slChuaPCP;
    }

    public Long getTt500() {
        return tt500;
    }

    public void setTt500(Long tt500) {
        this.tt500 = tt500;
    }

    public Long getTtKhac() {
        return ttKhac;
    }

    public void setTtKhac(Long ttKhac) {
        this.ttKhac = ttKhac;
    }

    public Long getSlPTC() {
        return slPTC;
    }

    public void setSlPTC(Long slPTC) {
        this.slPTC = slPTC;
    }

    public Float getTlPTC() {
        return tlPTC;
    }

    public void setTlPTC(Float tlPTC) {
        this.tlPTC = tlPTC;
    }

    public Float getTlPTCDungGio() {
        return tlPTCDungGio;
    }

    public void setTlPTCDungGio(Float tlPTCDungGio) {
        this.tlPTCDungGio = tlPTCDungGio;
    }

    public Float getTlPTCDungGioLanDau() {
        return tlPTCDungGioLanDau;
    }

    public void setTlPTCDungGioLanDau(Float tlPTCDungGioLanDau) {
        this.tlPTCDungGioLanDau = tlPTCDungGioLanDau;
    }

    public Long getTonXanh() {
        return tonXanh;
    }

    public void setTonXanh(Long tonXanh) {
        this.tonXanh = tonXanh;
    }

    public Long getTonVang() {
        return tonVang;
    }

    public void setTonVang(Long tonVang) {
        this.tonVang = tonVang;
    }

    public Long getTonDo1() {
        return tonDo1;
    }

    public void setTonDo1(Long tonDo1) {
        this.tonDo1 = tonDo1;
    }

    public Long getTonDo2() {
        return tonDo2;
    }

    public void setTonDo2(Long tonDo2) {
        this.tonDo2 = tonDo2;
    }

    public Long getTonDo3() {
        return tonDo3;
    }

    public void setTonDo3(Long tonDo3) {
        this.tonDo3 = tonDo3;
    }

    public Long getTonDo4() {
        return tonDo4;
    }

    public void setTonDo4(Long tonDo4) {
        this.tonDo4 = tonDo4;
    }

    public Long getTonDo5() {
        return tonDo5;
    }

    public void setTonDo5(Long tonDo5) {
        this.tonDo5 = tonDo5;
    }

    public Long getTonDo6() {
        return tonDo6;
    }

    public void setTonDo6(Long tonDo6) {
        this.tonDo6 = tonDo6;
    }

    public Long getTonDo7() {
        return tonDo7;
    }

    public void setTonDo7(Long tonDo7) {
        this.tonDo7 = tonDo7;
    }

    public Long getTonDo8() {
        return tonDo8;
    }

    public void setTonDo8(Long tonDo8) {
        this.tonDo8 = tonDo8;
    }

    public Long getTonDo9() {
        return tonDo9;
    }

    public void setTonDo9(Long tonDo9) {
        this.tonDo9 = tonDo9;
    }

    public Long getTongTon() {
        return tongTon;
    }

    public void setTongTon(Long tongTon) {
        this.tongTon = tongTon;
    }

    public Long getSlHoan() {
        return slHoan;
    }

    public void setSlHoan(Long slHoan) {
        this.slHoan = slHoan;
    }

    public Long getSlHuy() {
        return slHuy;
    }

    public void setSlHuy(Long slHuy) {
        this.slHuy = slHuy;
    }

    public Long getSlDenBu() {
        return slDenBu;
    }

    public void setSlDenBu(Long slDenBu) {
        this.slDenBu = slDenBu;
    }

    public Float getTlHoan() {
        return tlHoan;
    }

    public void setTlHoan(Float tlHoan) {
        this.tlHoan = tlHoan;
    }

    public Long getTuHoan() {
        return tuHoan;
    }

    public void setTuHoan(Long tuHoan) {
        this.tuHoan = tuHoan;
    }

    public Long getMauHoan() {
        return mauHoan;
    }

    public Long getTuHoanLk() {
        return tuHoanLk;
    }

    public void setTuHoanLk(Long tuHoanLk) {
        this.tuHoanLk = tuHoanLk;
    }

    public Long getMauHoanLk() {
        return mauHoanLk;
    }

    public void setMauHoanLk(Long mauHoanLk) {
        this.mauHoanLk = mauHoanLk;
    }

    public void setMauHoan(Long mauHoan) {
        this.mauHoan = mauHoan;
    }

    public Long getTuGiaoHangDungHen() {
        return tuGiaoHangDungHen;
    }

    public void setTuGiaoHangDungHen(Long tuGiaoHangDungHen) {
        this.tuGiaoHangDungHen = tuGiaoHangDungHen;
    }

    public Long getMauGiaoHangDungHen() {
        return mauGiaoHangDungHen;
    }

    public void setMauGiaoHangDungHen(Long mauGiaoHangDungHen) {
        this.mauGiaoHangDungHen = mauGiaoHangDungHen;
    }

    public Long getTuGiaoThanhCongLanDau() {
        return tuGiaoThanhCongLanDau;
    }

    public void setTuGiaoThanhCongLanDau(Long tuGiaoThanhCongLanDau) {
        this.tuGiaoThanhCongLanDau = tuGiaoThanhCongLanDau;
    }

    public Long getMauGiaoThanhCongLanDau() {
        return mauGiaoThanhCongLanDau;
    }

    public void setMauGiaoThanhCongLanDau(Long mauGiaoThanhCongLanDau) {
        this.mauGiaoThanhCongLanDau = mauGiaoThanhCongLanDau;
    }

    public Float getTlGiaoHangDungHen() {
        return tlGiaoHangDungHen;
    }

    public void setTlGiaoHangDungHen(Float tlGiaoHangDungHen) {
        this.tlGiaoHangDungHen = tlGiaoHangDungHen;
    }

    public Float getTlGiaoThanhCongLanDau() {
        return tlGiaoThanhCongLanDau;
    }

    public void setTlGiaoThanhCongLanDau(Float tlGiaoThanhCongLanDau) {
        this.tlGiaoThanhCongLanDau = tlGiaoThanhCongLanDau;
    }

    public Long getTuGiaoThanhCongLanDauLK() {
        return tuGiaoThanhCongLanDauLK;
    }

    public void setTuGiaoThanhCongLanDauLK(Long tuGiaoThanhCongLanDauLK) {
        this.tuGiaoThanhCongLanDauLK = tuGiaoThanhCongLanDauLK;
    }

    public Long getMauGiaoThanhCongLanDauLK() {
        return mauGiaoThanhCongLanDauLK;
    }

    public void setMauGiaoThanhCongLanDauLK(Long mauGiaoThanhCongLanDauLK) {
        this.mauGiaoThanhCongLanDauLK = mauGiaoThanhCongLanDauLK;
    }

    public Float getTlGiaoThanhCongLanDauLK() {
        return tlGiaoThanhCongLanDauLK;
    }

    public void setTlGiaoThanhCongLanDauLK(Float tlGiaoThanhCongLanDauLK) {
        this.tlGiaoThanhCongLanDauLK = tlGiaoThanhCongLanDauLK;
    }

    public String getVungCon() {
        return vungCon;
    }

    public void setVungCon(String vungCon) {
        this.vungCon = vungCon;
    }
}
