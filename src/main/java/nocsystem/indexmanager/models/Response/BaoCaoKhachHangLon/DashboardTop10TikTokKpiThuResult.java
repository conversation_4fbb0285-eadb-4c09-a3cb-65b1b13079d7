package nocsystem.indexmanager.models.Response.BaoCaoKhachHangLon;

import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DashboardTop10TikTokKpiThuResult {
    String donVi;
    private Long slPhatSinh;
    private Long slThuThanhCong;
    private Long slHuy;
    //slThuThanhCong/(slPhatSinh -Slhuy -slDropoff) *100
    private Float tlThuThanhCong;
    //
    private Long slThuThanhCongDunggio;
    //slThuThanhCongDunggio/slThuThanhCong
    private Float tlThuThanhCongDungGio;

    private Long slThuThanhCongDungGioLan1;
    //slThuThanhCongDungGioLan1/slThuThanhCong
    private Float tlThuThanhCongDungGioLan1;

    public DashboardTop10TikTokKpiThuResult(String donVi, Long slPhatSinh, Long slThuThanhCong, Long slThuThanhCongDunggio, Long slHuy, Long slThuThanhCongDungGioLan1) {
        this.donVi = donVi;
        this.slPhatSinh = slPhatSinh;
        this.slThuThanhCong = slThuThanhCong;
        this.slThuThanhCongDunggio = slThuThanhCongDunggio;
        this.slHuy = slHuy;
        this.slThuThanhCongDungGioLan1 = slThuThanhCongDungGioLan1;
    }

    public DashboardTop10TikTokKpiThuResult(String donVi, Float tlThuThanhCong, Float tlThuThanhCongDungGio, Float tlThuThanhCongDungGioLan1) {
        this.donVi = donVi;
        this.tlThuThanhCong = tlThuThanhCong;
        this.tlThuThanhCongDungGio = tlThuThanhCongDungGio;
        this.tlThuThanhCongDungGioLan1 = tlThuThanhCongDungGioLan1;
    }
}
