package nocsystem.indexmanager.models.Response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChiSoBuuCucResponse {
    private String maChiNhanh;
    private String maBuuCuc;
    private Integer slPhaiKhaiThac;
    private Integer slDaKhaiThacN1;
    private Integer slLacTuyen;
    private Integer slDenTrongCa;
    private Integer slTonChuaKetNoi;
    private Integer slNhapDoanhThuN1;
    private Integer slTonPhat;
    private Integer slTonChuaPcp;
    private Integer nangLucPhatBq;
    private Integer slPtcMax7ngay;
    private Integer slTonPhatDo789;
    private Integer buutaN1;
    private Integer buutaBq7ngay;
    private Integer slDuKienDenHt;
    private Integer buutaThieu;
    private Long tgCapNhat;
    private Long slTonPhatTonPcp;
}
