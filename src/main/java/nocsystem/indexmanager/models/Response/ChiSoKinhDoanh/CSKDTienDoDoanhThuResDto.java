package nocsystem.indexmanager.models.Response.ChiSoKinhDoanh;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JpaDto
public class CSKDTienDoDoanhThuResDto {

    @JsonProperty("nhom_dt")
    private String nhomDt;

    @JsonProperty("ngay_baocao")
    private LocalDate ngayBaoCao;

    @JsonProperty("ke_hoach")
    private float keHoach;

    @JsonProperty("thuc_hien")
    private float thucHien;

    @JsonProperty("tlht")
    private float tlht;

    @JsonProperty("tien_do")
    private float tienDo;

    @JsonProperty("tt_thang")
    private float ttThang;

    @JsonProperty("tt_tbn_thang")
    private float ttTbnThang;

    @JsonProperty("tt_nam")
    private float ttNam;

    @JsonProperty("tt_tbn_nam")
    private float ttTbnNam;

}
