package nocsystem.indexmanager.models.Response.danhgianguonluc;

import lombok.Data;

@Data
public class DgnlXeTaiDangChayDTO {
    private Long ngayBaocao;
    private Long version;
    private Long ngayKhoihanh;

    private String maChuyenxe;
    private String maDvc;
    private Integer chieu;

    // Hành trình
    private String maBuucuc;
    private String maCn;
    private Long thuTu;

    // Điểm hành trình
    private Boolean isDiemdungHt;
    private Boolean isDaQua;
    private Long tgCheckinDukien;
    private Long tgCheckoutDukien;
    private Long tgCheckinThucte;
    private Long tgCheckoutThucte;

    private Double klLenThucte;
    private Double klLenDukien;
    private Double tongKlLenThucteLuyke;

    private Double klXuongThucte;
    private Double klXuongDukien;
    private Double tongKlXuongThucteLuyke;

    // Thông tin chung xe
    private Long trangthaiXe;
    private String biensoXe;
    private Double taitrongXe;
    private String tenChuyenxe;
    private String tenLaixe;
    private String sdtLaixe;

    // Doanh thu
    private Double soKmSovoiDiemTruocdo;
    private Double soKmSovoiDiemSaudo;
    private Double soKmLuykeDendiem;
    private Double soKmConLai;
    private Integer isHanhtrinh;
    private Integer isDiemdau;
    private Integer isDiemcuoi;

    // Hiệu quả xe
    private Double hqxLuyke;
    private Double hqx1km;
    private Double tileLapday;

    private Long tgCapnhat;
}
