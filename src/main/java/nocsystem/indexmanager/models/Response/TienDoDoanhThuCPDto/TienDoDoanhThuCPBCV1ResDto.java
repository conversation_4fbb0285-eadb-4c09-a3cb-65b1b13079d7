package nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
public class TienDoDoanhThuCPBCV1ResDto {
    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ten_chi_nhanh")
    private String tenChiNhanh;

    @JsonProperty("loai_dichvu")
    private String loaiDichVu;

    @JsonProperty("thuc_hien")
    private Float thucHien;

    @JsonProperty("ke_hoach")
    private Float keHoach;

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;
}
