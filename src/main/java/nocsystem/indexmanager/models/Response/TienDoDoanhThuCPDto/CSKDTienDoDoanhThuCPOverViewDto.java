package nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class CSKDTienDoDoanhThuCPOverViewDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("ma_buucuc")
    private String maBuuCuc;

    @JsonProperty("loai_dichvu")
    private String loaiDichVu;

    @JsonProperty("ke_hoach")
    private Float keHoach;

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tl_doanhThu")
    private Float tlDoanhThu;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("tt_tbn_nam")
    private Float ttTbnNam;

    @JsonProperty("cung_ky_ngay")
    private Float cungKyNgay = Float.valueOf(0);

    @JsonProperty("cung_ky_thang")
    private Float cungKyThang = Float.valueOf(0);

    @JsonProperty("thang_truoc")
    private Float thangTruoc = Float.valueOf(0);

    @JsonProperty("nam_truoc")
    private Float namTruoc = Float.valueOf(0);

    @JsonProperty("cung_ky_nam")
    private Float cungKyNam = Float.valueOf(0);

    @JsonProperty("thngay")
    private Float thucHienNgay = Float.valueOf(0);
}
