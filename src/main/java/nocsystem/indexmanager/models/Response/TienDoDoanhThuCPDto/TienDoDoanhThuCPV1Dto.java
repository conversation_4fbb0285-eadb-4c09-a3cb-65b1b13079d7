package nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import nocsystem.indexmanager.util.JpaDto;

import java.time.LocalDate;

@Data
@JpaDto
@NoArgsConstructor
@AllArgsConstructor
public class TienDoDoanhThuCPV1Dto {

    @JsonProperty("loai_dichvu")
    private String dichVu = "";

    @JsonProperty("tlht")
    private Float tlHoanThanh;

    @JsonProperty("tiendo")
    private Float tienDo;

    @JsonProperty("tt_thang")
    private Float ttThang;

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang;

    @JsonProperty("tt_nam")
    private Float ttNam;

    @JsonProperty("ke_hoach")
    private Float keHoach;

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);

    @JsonProperty("nam_truoc")
    private Float namTruoc = Float.valueOf(0);

    @JsonProperty("thang_truoc")
    private Float thangTruoc = Float.valueOf(0);

    @JsonProperty("cung_ky_ngay")
    private Float cungKyNgay = Float.valueOf(0);

    @JsonProperty("cung_ky_thang")
    private Float cungKyThang = Float.valueOf(0);

    @JsonProperty("cung_ky_nam")
    private Float cungKyNam = Float.valueOf(0);
}
