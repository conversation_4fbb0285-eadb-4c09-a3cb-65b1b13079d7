package nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.util.JpaDto;

@Data
@JpaDto
public class FindTotalTienDoDoanhThuCPBCDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh = "";

    @JsonProperty("ma_buucuc")
    private String maBuuCuc = "";

    @JsonProperty("loai_dichvu")
    private String loaiDichVu = "";

    @JsonProperty("tlht")
    private Float tlht;

    @JsonProperty("tiendo")
    private Float tienDo = Float.valueOf(0);

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);
}
