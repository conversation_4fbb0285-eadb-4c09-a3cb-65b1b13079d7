package nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.util.JpaDto;

import javax.persistence.Column;
import java.util.Date;

@Data
@JpaDto
public class FindTotalTienDoDoanhThuCPCNDto {
    @JsonProperty("ma_chinhanh")
    private String maChiNhanh;

    @JsonProperty("loai_dichvu")
    private String loaiDichVu;

    @JsonProperty("tlht")
    private Float tlHoanThanh = Float.valueOf(0);

    @JsonProperty("tiendo")
    private Float tienDo = Float.valueOf(0);

    @JsonProperty("tt_thang")
    private Float ttThang = Float.valueOf(0);

    @JsonProperty("tt_tbn_thang")
    private Float ttTbnThang = Float.valueOf(0);

    @JsonProperty("tt_nam")
    private Float ttNam = Float.valueOf(0);

    @JsonProperty("ke_hoach")
    private Float keHoach = Float.valueOf(0);

    @JsonProperty("thuc_hien")
    private Float thucHien = Float.valueOf(0);
}
