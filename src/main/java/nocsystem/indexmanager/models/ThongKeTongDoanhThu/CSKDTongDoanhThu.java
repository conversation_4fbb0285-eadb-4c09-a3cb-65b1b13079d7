package nocsystem.indexmanager.models.ThongKeTongDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="cskd_tongdoanhthu")
public class CSKDTongDoanhThu {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tl_doanhthu")
    private float tlDoanhThu;

    @Column(name = "ke_hoach")
    private float keHoach;

    @Column(name = "thuc_hien")
    private float thucHien;

    @Column(name = "tlht")
    private float tlHoanThanh;

    @Column(name = "tiendo")
    private float tienDo;

    @Column(name = "tt_thang")
    private float ttThang;

    @Column(name = "tt_tbn_thang")
    private float ttTbnThang;

    @Column(name = "tt_nam")
    private float ttNam;

    @Column(name = "tt_tbn_nam")
    private float ttTbnNam;
}
