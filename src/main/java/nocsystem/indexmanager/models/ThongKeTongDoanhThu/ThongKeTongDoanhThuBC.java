package nocsystem.indexmanager.models.ThongKeTongDoanhThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
@Table(name = "cskd_tongdoanhthu_cn")
public class ThongKeTongDoanhThuBC {
    @Id
    @Column(name = "ma_chinhanh")
    private String maChiNhanh;

    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tong_doanh_thu")
    private Float tongDoanhThu;

    @Column(name = "tlht")
    private Float tlht;

    @Column(name = "tt_thang")
    private Float ttThang;

    @Column(name = "tt_nam")
    private Float ttNam;

    @Column(name = "tt_tbn_thang")
    private Float ttTbnThang;

    @Column(name = "tt_tbn_nam")
    private Float ttTbnNam;

    @Column(name = "thuc_hien")
    private Float thucHien;

    @Column(name = "ke_hoach")
    private Float keHoach;

    @Column(name = "tiendo")
    private Float tienDo;

    @Column(name = "cung_ky_ngay")
    private Float cungKyNgay;

    @Column(name = "cung_ky_thang")
    private Float cungKyThang;

    @Column(name = "cung_ky_nam")
    private Float cungKyNam;

    @Column(name = "thangtruoc")
    private Float thangTruoc;

    @Column(name = "namtruoc")
    private Float namTruoc;
}
