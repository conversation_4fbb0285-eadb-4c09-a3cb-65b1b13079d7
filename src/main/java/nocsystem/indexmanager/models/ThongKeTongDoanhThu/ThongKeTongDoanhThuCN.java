package nocsystem.indexmanager.models.ThongKeTongDoanhThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
@Table(name = "cskd_tiendodoanhthu_tk_tdt_cn")
public class ThongKeTongDoanhThuCN {
    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tong_doanh_thu")
    private Float tongDoanhThu;

    @Column(name = "tlht")
    private Float tlht;

    @Column(name = "tiendo")
    private Float tiendo;

    @Column(name = "tt_thang")
    private Float ttThang;

    @Column(name = "tt_nam")
    private Float ttNam;

    @Column(name = "tt_tbn_thang")
    private Float ttTbnThang;

    @Column(name = "tt_tbn_nam")
    private Float ttTbnNam;
}
