package nocsystem.indexmanager.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "dm_buucuc_clone")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DmBuuCucClone implements Serializable {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "ten_buucuc")
    private String tenBuuCuc;

    @Column(name = "dia_chi")
    private String diaChi;

    @Column(name = "dept_code")
    private String deptCode;

    @Column(name = "ma_cn")
    private String maCn;

    @Column(name = "ma_vung_con")
    private String maVungCon;

    @Column(name = "create_time")
    private String createTime;

    @Column(name = "update_time")
    private String updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "update_by")
    private String updateBy;

    @Column(name = "is_active")
    private Integer isActive;

    @Column(name = "is_chtt")
    private Integer isChtt;

    @Column(name = "cap_buucuc")
    private Integer capBuuCuc;

    @Column(name = "ma_quanhuyen")
    private String maQuanHuyen;

    @Column(name = "ten_quanhuyen")
    private String tenQuanHuyen;

    @Column(name = "ma_phuongxa")
    private String maPhuongXa;

    @Column(name = "ten_phuongxa")
    private String tenPhuongXa;

    @Column(name = "ma_vung_log")
    private String maVungLog;

    @Column(name = "ma_tinh_quan")
    private String maTinhQuan;

    @Column(name = "kho_vung")
    private Integer khoVung;

    @Column(name = "ma_don_vi_cap_tren")
    private String maDonViCapTren;

    @Column(name = "ttkt")
    private Integer ttkt;

    @Column(name = "doi_van_chuyen")
    private Integer doiVanChuyen;

    @Column(name = "loai_don_vi_log")
    private Integer loaiDonViLog;

    @Column(name = "is_buucuc_phongban")
    private Integer isBuucucPhongban;

    @Column(name = "cn_id")
    private Long cnId;

    @Column(name = "cn_vung_id")
    private Long cnVungId;

    @Column(name = "ma_cn_cu")
    private String maCnCu;
}
