package nocsystem.indexmanager.models.ThongKeLuyKe;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="cskd_tiendothluyke_bc")
public class TienDoLuyKeDetail {
    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chinhanh")
    private String chiNhanh;

    @Column(name = "mien")
    private String mien;

    @Column(name = "buucuc")
    private String buuCuc;

    @Column(name = "tlht")
    private Float tlht;

    @Column(name = "tiendo")
    private Float tienDo;

    @Column(name = "tt_thang")
    private Float ttThang;

    @Column(name = "tt_tbn_thang")
    private Float ttTbnThang;

    @Column(name = "tt_nam")
    private Float ttNam;

    @Column(name = "tt_tbn_nam")
    private Float ttTbnNam;

    @Column(name = "danhgia")
    private Integer danhgia;

    @Column(name = "lkthang")
    private Float lkthang;

    @Column(name = "khthang")
    private Float khthang;

    @Column(name = "tiendo_ngay")
    private Float tienDoNgay;

    @Column(name = "khthang_ngay")
    private Float keHoachThangDenNgay;

    @Column(name = "tlht_ngay")
    private Float tiLeHoanThanhNgay;

    @Column(name = "thngay")
    private Float thucHienNgay;

    @Column(name = "cung_ky_ngay")
    private Float cungKyNgay;

    @Column(name = "cung_ky_thang")
    private Float cungKyThang;

    @Column(name = "cung_ky_nam")
    private Float cungKyNam;

    @Column(name = "thangtruoc")
    private Float thangTruoc;

    @Column(name = "namtruoc")
    private Float namTruoc;

    @Column(name = "ngay_quydoi_thang")
    private Float ngayQuyDoiThang;

    @Column(name = "hs_ngay")
    private Float heSoNgay;
}
