package nocsystem.indexmanager.models.ThongKeLuyKe;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="cskd_tiendothluyke_bc")

public class TienDoLuyKeTCTBuuCuc {
    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "chinhanh")
    private String chiNhanh;

    @Column(name = "mien")
    private String mien;

    @Column(name = "buucuc")
    private String buuCuc;

    @Column(name = "tlht")
    private Float tlht;

    @Column(name = "tiendo")
    private Float tienDo;

    @Column(name = "tt_thang")
    private Float ttThang;

    @Column(name = "tt_tbn_thang")
    private Float ttTbnThang;

    @Column(name = "tt_nam")
    private Float ttNam;

    @Column(name = "tt_tbn_nam")
    private Float ttTbnNam;

    @Column(name = "danhgia")
    private Integer danhgia;

    @Column(name = "lkthang")
    private Float lkthang;

    @Column(name = "khthang")
    private Float khthang;

    @Column(name = "tiendo_ngay")
    private Float tienDoNgay;

    @Column(name = "khthang_ngay")
    private Float keHoachThangDenNgay;

    @Column(name = "tlht_ngay")
    private Float tiLeHoanThanhNgay;

    @Column(name = "thngay")
    private Float thucHienNgay;
}
