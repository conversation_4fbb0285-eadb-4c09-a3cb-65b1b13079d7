package nocsystem.indexmanager.models.TopChiNhanh;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "top_chinhanh_cldv")
@IdClass(TopChiNhanhKey.class)
public class TopChiNhanh implements Serializable {
    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Id
    @Column(name = "ma_cn")
    private String maChiNhanh;

    @Id
    @Column(name = "ma_bc")
    private String maBuuCuc;

    @Column(name = "tl_thuc_hien")
    private Float tlThucHien;

    @Column(name = "tl_hoanthanh")
    private Float tlHoanThanh;

    @Column(name = "kpi")
    private Float kpi;

    @Column(name = "tgtt")
    private Float thoiGianToanTrinh;

    @Id
    @Column(name = "type")
    private String type;

    @Id
    @Column(name = "is_luyke")
    private Integer luyKe;

}
