package nocsystem.indexmanager.models.DieuHanhTon;

import javax.persistence.*;
import java.time.LocalDate;

@Entity
@IdClass(DieuHanhTonHoanPhatChiTietKey.class)
@Table(name = "bcvh_khl_chitiet")
public class DieuHanhTonHoanPhatChiTiet {
    @Id
    @Column(name="ma_phieugui")
    private String maPhieuGui;
    @Id
    @Column(name="ngay_baocao")
    private LocalDate ngayBaoCao;
    @Id
    @Column(name="status")
    private Integer status;
    @Column(name="tinh_nhan")
    private String tinhNhan;
    @Column(name="huyen_nhan")
    private String huyenNhan;
    @Column(name="tinh_phat")
    private String tinhPhat;
    @Column(name="huyen_phat")
    private String huyenPhat;
    @Column(name="ma_buucuc_phat_thucte")
    private String maBuuCucPhatThucTe;
    @Column(name="ma_dv_viettel")
    private String maDVViettel;
    @Column(name="ma_buucuc_goc")
    private String maBuuCucGoc;
    @Column(name="ma_trangthai")
    private String maTrangThai;
    @Column(name="ma_buucuc_ht")
    private String maBuuCucHT;
    @Column(name="ma_chinhanh_ht")
    private String maChiNhanhHT;
    @Column(name="ma_doitac")
    private String maDoiTac;
    @Column(name="ma_khgui")
    private String maKHGui;
    @Column(name="ma_buucuc_phat")
    private String maBuuCucPhat;
    @Column(name="time_pcp")
    private String timePCP;
    @Column(name="time_gach_bp")
    private String timeGachBP;
    @Column(name="time_gach_bp2")
    private String timeGachBP2;
    @Column(name="time_gach_bp3")
    private String timeGachBP3;
    @Column(name="ngay_gui_bp")
    private String ngayGuiBP;
    @Column(name="so_lan_giao")
    private Integer soLanGiao;
    @Column(name="tuyen_buuta")
    private String tuyenBuuTa;
    @Column(name="tien_cod")
    private Double tienCOD;
    @Column(name="tg_quydinhphat")
    private String tgQuyDinhPhat;
    @Column(name="tg_chenhlechphat")
    private String tgChenhLechPhat;
    @Column(name="noi_kho")
    private Integer noiKho;
    @Column(name="type_giao")
    private String typeGiao;
    @Column(name="type_pb")
    private String typePB;
    @Column(name="danh_gia_ton")
    private String danhGiaTon;

    @Column(name="thoi_gian_ton")
    private float thoiGianTon;



}
