package nocsystem.indexmanager.models.DieuHanhTon;

import javax.persistence.*;
import java.time.LocalDate;

@Entity
@IdClass(DieuHanhTonHoanPhatKey.class)
@Table(name = "bcvh_khl_tonghop")
public class DieuHanhTonHoanPhat {
    @Id
    @Column(name="ngay_baocao")
    private LocalDate ngayBaoCao;
    @Id
    @Column(name="vung_phat")
    private String vungPhat;
    @Id
    @Column(name="cn_phat")
    private String cnPhat;
    @Id
    @Column(name="ma_buucuc_phat")
    private String maBuuCucPhat;
    @Id
    @Column(name="ma_doitac")
    private String maDoiTac;
    @Id
    @Column(name="loai_baocao")
    private Integer loaiBaoCao;
    @Id
    @Column(name="ma_dv_viettel")
    private String maDVViettel;
    @Id
    @Column(name="tuyen_buuta")
    private String tuyenBuuTa;
    @Id
    @Column(name="status")
    private Integer status;
    @Column(name="phat_th1d")
    private Long phatTH1d;
    @Column(name="phat_th2d")
    private Long phatTH2d;
    @Column(name="phat_th3d")
    private Long phatTH3d;
    @Column(name="phat_thl3d")
    private Long phatTHL3d;
    @Column(name="phat_dh6h")
    private Long phatDH6h;
    @Column(name="phat_dh12h")
    private Long phatDH12h;
    @Column(name="phat_dh18h")
    private Long phatDH18h;
    @Column(name="phat_dh24h")
    private Long phatDH24h;
    @Column(name="phat_qh1d")
    private Long phatQH1d;
    @Column(name="phat_qh2d")
    private Long phatQH2d;
    @Column(name="phat_qh3d")
    private Long phatQH3d;
    @Column(name="phat_qh4d")
    private Long phatQH4d;
    @Column(name="phat_qh5d")
    private Long phatQH5d;
    @Column(name="phat_qh6d")
    private Long phatQH6d;
    @Column(name="phat_qh7d")
    private Long phatQH7d;
    @Column(name="phat_qhl7d")
    private Long phatQHL7d;
    @Column(name="hoan_th1d")
    private Long hoanTH1d;
    @Column(name="hoan_th2d")
    private Long hoanTH2d;
    @Column(name="hoan_th3d")
    private Long hoanTH3d;
    @Column(name="hoan_thl3d")
    private Long hoanTHL3d;
    @Column(name="hoan_dh6h")
    private Long hoanDH6h;
    @Column(name="hoan_dh12h")
    private Long hoanDH12h;
    @Column(name="hoan_dh18h")
    private Long hoanDH18h;
    @Column(name="hoan_dh24h")
    private Long hoanDH24h;
    @Column(name="hoan_qh1d")
    private Long hoanQH1d;
    @Column(name="hoan_qh2d")
    private Long hoanQH2d;
    @Column(name="hoan_qh3d")
    private Long hoanQH3d;
    @Column(name="hoan_qh4d")
    private Long hoanQH4d;
    @Column(name="hoan_qh5d")
    private Long hoanQH5d;
    @Column(name="hoan_qh6d")
    private Long hoanQH6d;
    @Column(name="hoan_qh7d")
    private Long hoanQH7d;
    @Column(name="hoan_qhl7d")
    private Long hoanQHL7d;
}
