package nocsystem.indexmanager.models.DieuHanhTon;


import javax.persistence.*;
import java.sql.Timestamp;
import java.time.LocalDate;

@Entity
@IdClass(DieuHanhTonThuChiTietKey.class)
@Table(name = "bao_cao_khl_chi_tiet_ton")
public class DieuHanhTonThuChiTiet {
    @Id
    @Column(name="ma_phieugui")
    private String maPhieuGui;
    @Id
    @Column(name="status")
    private Integer status;
    @Column(name="ma_trangthai")
    private String maTrangThai;
    @Column(name="ma_doitac")
    private String maDoiTac;
    @Column(name="thoigiantaodon")
    private String thoiGianTaoDon;
    @Column(name="time_khach_henlay")
    private Timestamp timeKhachHenLay;
    @Column(name="tinh_nhan")
    private String tinhNhan;
    @Column(name="ma_khgui")
    private String maKHGui;
    @Column(name="tinh_phat")
    private String tinhPhat;
    @Column(name="ma_buucuc_goc")
    private String maBuuCucGoc;
    @Column(name="ma_dv_viettel")
    private String maDVViettel;
    @Column(name="tong_cuoc")
    private Double tongCuoc;
    @Column(name="update_time")
    private Timestamp updateTime;
    @Column(name="thoi_gian")
    private LocalDate thoiGian;
    @Column(name="type_pb")
    private String typePB;

    @Column(name = "danh_gia_don")
    private String danhGiaDon;
    @Column(name = "thoi_gian_ton")
    private float thoiGianTon;


}
