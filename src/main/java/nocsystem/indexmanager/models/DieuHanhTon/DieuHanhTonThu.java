package nocsystem.indexmanager.models.DieuHanhTon;

import javax.persistence.*;
import java.time.LocalDate;

@Entity
@IdClass(DieuHanhTonThuKey.class)
@Table(name = "bao_cao_khl_thu_ngay")
public class DieuHanhTonThu {
    @Id
    @Column(name="thoi_gian")
    private LocalDate thoiGian;
    @Id
    @Column(name="vung")
    private String vung;
    @Id
    @Column(name="chi_nhanh")
    private String chiNhanh;
    @Id
    @Column(name="buu_cuc")
    private String buuCuc;
    @Id
    @Column(name="doi_tac")
    private String doiTac;
    @Id
    @Column(name="dich_vu")
    private String dichVu;
    @Id
    @Column(name="loai_don")
    private String loaiDon;
    @Id
    @Column(name="tuyen_buuta")
    private String tuyenBuuTa;
    @Id
    @Column(name="status")
    private Integer status;
    @Column(name="thu_th1d")
    private Long thuTH1d;
    @Column(name="thu_th2d")
    private Long thuTH2d;
    @Column(name="thu_th3d")
    private Long thuTH3d;
    @Column(name="thu_thl3d")
    private Long thuTHL3d;
    @Column(name="thu_dh6h")
    private Long thuDH6h;
    @Column(name="thu_dh12h")
    private Long thuDH12h;
    @Column(name="thu_dh18h")
    private Long thuDH18h;
    @Column(name="thu_dh24h")
    private Long thuDH24h;
    @Column(name="thu_qh1d")
    private Long thuQH1d;
    @Column(name="thu_qh2d")
    private Long thuQH2d;
    @Column(name="thu_qh3d")
    private Long thuQH3d;
    @Column(name="thu_qh4d")
    private Long thuQH4d;
    @Column(name="thu_qh5d")
    private Long thuQH5d;
    @Column(name="thu_qhl5d")
    private Long thuQHL5d;
}
