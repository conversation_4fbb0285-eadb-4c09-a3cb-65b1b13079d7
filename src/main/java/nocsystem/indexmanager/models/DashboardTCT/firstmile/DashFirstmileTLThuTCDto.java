package nocsystem.indexmanager.models.DashboardTCT.firstmile;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@Getter
@Setter
@ToString
public class DashFirstmileTLThuTCDto implements Serializable {

    @JsonProperty("ngayBaoCao")
    private Date ngayBaoCao;

    @JsonProperty("tyLeN")
    private Float tyLeN;

    public DashFirstmileTLThuTCDto() {
    }

    public DashFirstmileTLThuTCDto(Date ngayBaoCao, Float tyLeN) {
        this.ngayBaoCao = ngayBaoCao;
        this.tyLeN = tyLeN;
    }
}