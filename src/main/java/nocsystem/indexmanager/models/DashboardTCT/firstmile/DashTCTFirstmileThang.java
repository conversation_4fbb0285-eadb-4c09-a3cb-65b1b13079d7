package nocsystem.indexmanager.models.DashboardTCT.firstmile;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(name = "dash_tct_firstmile_thang")
@IdClass(DashTCTFirstmileKey.class)
public class DashTCTFirstmileThang implements Serializable {
    @Id
    @Column(name = "ngay_baocao")
    @Temporal(TemporalType.DATE)
    private Date ngayBaoCao;

    @Id
    @Column(name = "chinhanh")
    private String chiNhanh;

    @Id
    @Column(name = "buucuc")
    private String buuCuc;

    @Column(name = "stt")
    private String stt;

    @Column(name = "type")
    private Integer type;

    @Id
    @Column(name = "title")
    private String title;

    @Column(name = "sanluong_n")
    private Long sanluongN;

    @Column(name = "tyle_n")
    private Float tyLeN;

    @Column(name = "sanluong_n1")
    private Long sanluongN1;

    @Column(name = "tyle_n1")
    private Float tyLeN1;

    @Column(name = "tangtruong")
    private Long tangTruong;

    @Column(name = "tyle_tangtruong")
    private Float tyLeTangTruong;

    @Column(name = "kpi")
    private Float kpi;

    public DashTCTFirstmileThang() {
    }

    public DashTCTFirstmileThang(Date ngayBaoCao, String chiNhanh, String buuCuc, String stt, Integer type, String title, Long sanluongN, Float tyLeN, Long sanluongN1, Float tyLeN1, Long tangTruong, Float tyLeTangTruong, Float kpi) {
        this.ngayBaoCao = ngayBaoCao;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.stt = stt;
        this.type = type;
        this.title = title;
        this.sanluongN = sanluongN;
        this.tyLeN = tyLeN;
        this.sanluongN1 = sanluongN1;
        this.tyLeN1 = tyLeN1;
        this.tangTruong = tangTruong;
        this.tyLeTangTruong = tyLeTangTruong;
        this.kpi = kpi;
    }

    public DashTCTFirstmileThang(Date ngayBaoCao, String chiNhanh, String stt, Integer type, String title, Long sanluongN, Float tyLeN, Long sanluongN1, Float tyLeN1, Long tangTruong, Float tyLeTangTruong, Float kpi) {
        this.ngayBaoCao = ngayBaoCao;
        this.chiNhanh = chiNhanh;
        this.stt = stt;
        this.type = type;
        this.title = title;
        this.sanluongN = sanluongN;
        this.tyLeN = tyLeN;
        this.sanluongN1 = sanluongN1;
        this.tyLeN1 = tyLeN1;
        this.tangTruong = tangTruong;
        this.tyLeTangTruong = tyLeTangTruong;
        this.kpi = kpi;
    }

    public DashTCTFirstmileThang(Date ngayBaoCao, String stt, Integer type, String title, Long sanluongN, Float tyLeN, Long sanluongN1, Float tyLeN1, Long tangTruong, Float tyLeTangTruong, Float kpi) {
        this.ngayBaoCao = ngayBaoCao;
        this.stt = stt;
        this.type = type;
        this.title = title;
        this.sanluongN = sanluongN;
        this.tyLeN = tyLeN;
        this.sanluongN1 = sanluongN1;
        this.tyLeN1 = tyLeN1;
        this.tangTruong = tangTruong;
        this.tyLeTangTruong = tyLeTangTruong;
        this.kpi = kpi;
    }
}
