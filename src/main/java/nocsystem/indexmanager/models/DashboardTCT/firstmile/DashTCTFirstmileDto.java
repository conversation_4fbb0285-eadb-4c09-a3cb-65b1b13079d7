package nocsystem.indexmanager.models.DashboardTCT.firstmile;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@Getter
@Setter
@ToString
public class DashTCTFirstmileDto implements Serializable {
    @JsonProperty("ngayBaoCao")
    private Date ngayBaoCao;

    @JsonProperty("chiNhanh")
    private String chiNhanh;

    @JsonProperty("buuCuc")
    private String buuCuc;

    @JsonProperty("stt")
    private String stt;

    @JsonProperty("type")
    private Integer type;

    @JsonProperty("title")
    private String title;

    @JsonProperty("sanLuongN")
    private Long sanluongN;

    @JsonProperty("tyLeN")
    private Float tyLeN;

    @JsonProperty("sanLuongN1")
    private Long sanluongN1;

    @JsonProperty("tyLeN1")
    private Float tyLeN1;

    @JsonProperty("tangTruong")
    private Long tangTruong;

    @JsonProperty("tyLeTangTruong")
    private Float tyLeTangTruong;

    @JsonProperty("kpi")
    private Float kpi;

    public DashTCTFirstmileDto() {
    }

    public DashTCTFirstmileDto(Date ngayBaoCao, String chiNhanh, String buuCuc, String stt, Integer type, String title, Long sanluongN, Float tyLeN, Long sanluongN1, Float tyLeN1, Long tangTruong, Float tyLeTangTruong, Float kpi) {
        this.ngayBaoCao = ngayBaoCao;
        this.chiNhanh = chiNhanh;
        this.buuCuc = buuCuc;
        this.stt = stt;
        this.type = type;
        this.title = title;
        this.sanluongN = sanluongN;
        this.tyLeN = tyLeN;
        this.sanluongN1 = sanluongN1;
        this.tyLeN1 = tyLeN1;
        this.tangTruong = tangTruong;
        this.tyLeTangTruong = tyLeTangTruong;
        this.kpi = kpi;
    }

    public DashTCTFirstmileDto(Date ngayBaoCao, String chiNhanh, String stt, Integer type, String title, Long sanluongN, Float tyLeN, Long sanluongN1, Float tyLeN1, Long tangTruong, Float tyLeTangTruong, Float kpi) {
        this.ngayBaoCao = ngayBaoCao;
        this.chiNhanh = chiNhanh;
        this.stt = stt;
        this.type = type;
        this.title = title;
        this.sanluongN = sanluongN;
        this.tyLeN = tyLeN;
        this.sanluongN1 = sanluongN1;
        this.tyLeN1 = tyLeN1;
        this.tangTruong = tangTruong;
        this.tyLeTangTruong = tyLeTangTruong;
        this.kpi = kpi;
    }

    public DashTCTFirstmileDto(Date ngayBaoCao, String stt, Integer type, String title, Long sanluongN, Float tyLeN, Long sanluongN1, Float tyLeN1, Long tangTruong, Float tyLeTangTruong, Float kpi) {
        this.ngayBaoCao = ngayBaoCao;
        this.stt = stt;
        this.type = type;
        this.title = title;
        this.sanluongN = sanluongN;
        this.tyLeN = tyLeN;
        this.sanluongN1 = sanluongN1;
        this.tyLeN1 = tyLeN1;
        this.tangTruong = tangTruong;
        this.tyLeTangTruong = tyLeTangTruong;
        this.kpi = kpi;
    }

    public DashTCTFirstmileDto(String stt, Integer type, String title) {
        this.stt = stt;
        this.type = type;
        this.title = title;
    }
}
