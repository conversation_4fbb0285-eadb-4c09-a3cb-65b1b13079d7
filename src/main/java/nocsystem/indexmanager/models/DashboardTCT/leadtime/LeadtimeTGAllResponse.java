package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LeadtimeTGAllResponse {
    private Double tgTtAll;
    private Double tgTtN1All;
    private Double tgTtTangTruongAll;
    private Double tgFmAll;
    private Double tgFmN1All;
    private Double tgFmTangTruongAll;
    private Double tgMmAll;
    private Double tgMmN1All;
    private Double tgMmTangTruongAll;
    private Double tgLmAll;
    private Double tgLmN1All;
    private Double tgLmTangTruongAll;
    private Double tgNoiTinhAll;
    private Double tgNoiTinhN1All;
    private Double tgNoiTinhTangTruongAll;
    private Double tgNoiMienAll;
    private Double tgNoiMienN1All;
    private Double tgNoiMienTangTruongAll;
    private Double tgLienMienAll;
    private Double tgLienMienN1All;
    private Double tgLienMienTangTruongAll;

}
