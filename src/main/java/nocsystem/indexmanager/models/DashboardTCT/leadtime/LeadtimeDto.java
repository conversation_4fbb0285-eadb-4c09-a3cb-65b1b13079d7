package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.Date;

@Data
@Getter
@Setter
@ToString
public class LeadtimeDto {
    private Double tgTtAll;

    private Double tgFmAll;

    private Double tgMmAll;

    private Double tgLmAll;

    private Double tgNoiTinhAll;

    private Double tgNoiMienAll;

    private Double tgLienMienAll;

    private Double tgTtNhanh;

    private Double tgFmNhanh;

    private Double tgMmNhanh;

    private Double tgLmNhanh;

    private Double tgNoiTinhNhanh;

    private Double tgNoiMienNhanh;

    private Double tgLienMienNhanh;

    private Double tgTtTK;

    private Double tgFmTK;

    private Double tgMmTK;

    private Double tgLmTK;

    private Double tgNoiTinhTK;

    private Double tgNoiMienTK;

    private Double tgLienMienTK;

    private Double tgTtKienTK;

    private Double tgFmKienTK;

    private Double tgMmKienTK;

    private Double tgLmKienTK;

    private Double tgNoiTinhKienTK;

    private Double tgNoiMienKienTK;

    private Double tgLienMienKienTK;

    private Double tgTtTachKienTK;

    private Double tgFmTachKienTK;

    private Double tgMmTachKienTK;

    private Double tgLmTachKienTK;

    private Double tgNoiTinhTachKienTK;

    private Double tgNoiMienTachKienTK;

    private Double tgLienMienTachKienTK;

    public LeadtimeDto() {
    }

    public LeadtimeDto(Double tgTtAll, Double tgFmAll, Double tgMmAll, Double tgLmAll, Double tgNoiTinhAll, Double tgNoiMienAll, Double tgLienMienAll, Double tgTtNhanh, Double tgFmNhanh, Double tgMmNhanh, Double tgLmNhanh, Double tgNoiTinhNhanh, Double tgNoiMienNhanh, Double tgLienMienNhanh, Double tgTtTK, Double tgFmTK, Double tgMmTK, Double tgLmTK, Double tgNoiTinhTK, Double tgNoiMienTK, Double tgLienMienTK, Double tgTtKienTK, Double tgFmKienTK, Double tgMmKienTK, Double tgLmKienTK, Double tgNoiTinhKienTK, Double tgNoiMienKienTK, Double tgLienMienKienTK, Double tgTtTachKienTK, Double tgFmTachKienTK, Double tgMmTachKienTK, Double tgLmTachKienTK, Double tgNoiTinhTachKienTK, Double tgNoiMienTachKienTK, Double tgLienMienTachKienTK) {
        this.tgTtAll = tgTtAll;
        this.tgFmAll = tgFmAll;
        this.tgMmAll = tgMmAll;
        this.tgLmAll = tgLmAll;
        this.tgNoiTinhAll = tgNoiTinhAll;
        this.tgNoiMienAll = tgNoiMienAll;
        this.tgLienMienAll = tgLienMienAll;
        this.tgTtNhanh = tgTtNhanh;
        this.tgFmNhanh = tgFmNhanh;
        this.tgMmNhanh = tgMmNhanh;
        this.tgLmNhanh = tgLmNhanh;
        this.tgNoiTinhNhanh = tgNoiTinhNhanh;
        this.tgNoiMienNhanh = tgNoiMienNhanh;
        this.tgLienMienNhanh = tgLienMienNhanh;
        this.tgTtTK = tgTtTK;
        this.tgFmTK = tgFmTK;
        this.tgMmTK = tgMmTK;
        this.tgLmTK = tgLmTK;
        this.tgNoiTinhTK = tgNoiTinhTK;
        this.tgNoiMienTK = tgNoiMienTK;
        this.tgLienMienTK = tgLienMienTK;
        this.tgTtKienTK = tgTtKienTK;
        this.tgFmKienTK = tgFmKienTK;
        this.tgMmKienTK = tgMmKienTK;
        this.tgLmKienTK = tgLmKienTK;
        this.tgNoiTinhKienTK = tgNoiTinhKienTK;
        this.tgNoiMienKienTK = tgNoiMienKienTK;
        this.tgLienMienKienTK = tgLienMienKienTK;
        this.tgTtTachKienTK = tgTtTachKienTK;
        this.tgFmTachKienTK = tgFmTachKienTK;
        this.tgMmTachKienTK = tgMmTachKienTK;
        this.tgLmTachKienTK = tgLmTachKienTK;
        this.tgNoiTinhTachKienTK = tgNoiTinhTachKienTK;
        this.tgNoiMienTachKienTK = tgNoiMienTachKienTK;
        this.tgLienMienTachKienTK = tgLienMienTachKienTK;
    }
}
