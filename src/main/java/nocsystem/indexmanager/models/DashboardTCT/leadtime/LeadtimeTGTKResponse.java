package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LeadtimeTGTKResponse {
    private Double tgTtTK;
    private Double tgTtN1TK;
    private Double tgTtTangTruongTK;
    private Double tgFmTK;
    private Double tgFmN1TK;
    private Double tgFmTangTruongTK;
    private Double tgMmTK;
    private Double tgMmN1TK;
    private Double tgMmTangTruongTK;
    private Double tgLmTK;
    private Double tgLmN1TK;
    private Double tgLmTangTruongTK;
    private Double tgNoiTinhTK;
    private Double tgNoiTinhN1TK;
    private Double tgNoiTinhTangTruongTK;
    private Double tgNoiMienTK;
    private Double tgNoiMienN1TK;
    private Double tgNoiMienTangTruongTK;
    private Double tgLienMienTK;
    private Double tgLienMienN1TK;
    private Double tgLienMienTangTruongTK;
}
