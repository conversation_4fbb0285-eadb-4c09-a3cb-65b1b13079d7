package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(name = "dash_tct_leadtime_ngay")
@IdClass(DashTCTLeadtimeKey.class)
public class DashTCTLeadtimeNgay implements Serializable {
    @Id
    @Column(name = "ngay_baocao")
    @Temporal(TemporalType.DATE)
    private Date ngayBaoCao;

    @Id
    @Column(name = "ma_chinhanh")
    private String maChiNhanh;

    @Id
    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "tg_tt_all")
    private Double tgTtAll;

    @Column(name = "tg_fm_all")
    private Double tgFmAll;

    @Column(name = "tg_mm_all")
    private Double tgMmAll;

    @Column(name = "tg_lm_all")
    private Double tgLmAll;

    @Column(name = "tg_noitinh_all")
    private Double tgNoiTinhAll;

    @Column(name = "tg_noimien_all")
    private Double tgNoiMienAll;

    @Column(name = "tg_lienmien_all")
    private Double tgLienMienAll;

    @Column(name = "tg_tt_nhanh")
    private Double tgTtNhanh;

    @Column(name = "tg_fm_nhanh")
    private Double tgFmNhanh;

    @Column(name = "tg_mm_nhanh")
    private Double tgMmNhanh;

    @Column(name = "tg_lm_nhanh")
    private Double tgLmNhanh;

    @Column(name = "tg_noitinh_nhanh")
    private Double tgNoiTinhNhanh;

    @Column(name = "tg_noimien_nhanh")
    private Double tgNoiMienNhanh;

    @Column(name = "tg_lienmien_nhanh")
    private Double tgLienMienNhanh;

    @Column(name = "tg_tt_tk")
    private Double tgTtTk;

    @Column(name = "tg_fm_tk")
    private Double tgFmTk;

    @Column(name = "tg_mm_tk")
    private Double tgMmTk;

    @Column(name = "tg_lm_tk")
    private Double tgLmTk;

    @Column(name = "tg_noitinh_tk")
    private Double tgNoiTinhTk;

    @Column(name = "tg_noimien_tk")
    private Double tgNoiMienTk;

    @Column(name = "tg_lienmien_tk")
    private Double tgLienMienTk;

    @Column(name = "tg_tt_kien_tk")
    private Double tgTtKienTk;

    @Column(name = "tg_fm_kien_tk")
    private Double tgFmKienTk;

    @Column(name = "tg_mm_kien_tk")
    private Double tgMmKienTk;

    @Column(name = "tg_lm_kien_tk")
    private Double tgLmKienTk;

    @Column(name = "tg_noitinh_kien_tk")
    private Double tgNoiTinhKienTk;

    @Column(name = "tg_noimien_kien_tk")
    private Double tgNoiMienKienTk;

    @Column(name = "tg_lienmien_kien_tk")
    private Double tgLienMienKienTk;

    @Column(name = "tg_tt_tach_kien_tk")
    private Double tgTtTachKienTk;

    @Column(name = "tg_fm_tach_kien_tk")
    private Double tgFmTachKienTk;

    @Column(name = "tg_mm_tach_kien_tk")
    private Double tgMmTachKienTk;

    @Column(name = "tg_lm_tach_kien_tk")
    private Double tgLmTachKienTk;

    @Column(name = "tg_noitinh_tach_kien_tk")
    private Double tgNoiTinhTachKienTk;

    @Column(name = "tg_noimien_tach_kien_tk")
    private Double tgNoiMienTachKienTk;

    @Column(name = "tg_lienmien_tach_kien_tk")
    private Double tgLienMienTachKienTk;

    @Column(name = "tg_mm_noimien")
    private Double tgMmNoiMien;

    @Column(name = "tg_mm_lienmien")
    private Double tgMmLienMien;
}
