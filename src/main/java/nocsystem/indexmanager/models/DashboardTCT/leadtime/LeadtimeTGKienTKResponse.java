package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LeadtimeTGKienTKResponse {
    private Double tgTtKienTK;
    private Double tgTtN1KienTK;
    private Double tgTtTangTruongKienTK;
    private Double tgFmKienTK;
    private Double tgFmN1KienTK;
    private Double tgFmTangTruongKienTK;
    private Double tgMmKienTK;
    private Double tgMmN1KienTK;
    private Double tgMmTangTruongKienTK;
    private Double tgLmKienTK;
    private Double tgLmN1KienTK;
    private Double tgLmTangTruongKienTK;
    private Double tgNoiTinhKienTK;
    private Double tgNoiTinhN1KienTK;
    private Double tgNoiTinhTangTruongKienTK;
    private Double tgNoiMienKienTK;
    private Double tgNoiMienN1KienTK;
    private Double tgNoiMienTangTruongKienTK;
    private Double tgLienMienKienTK;
    private Double tgLienMienN1KienTK;
    private Double tgLienMienTangTruongKienTK;
}
