package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@Data
public class LeadtimeChartTTAllResponse {
    private Date ngayBaoCao;
    private Double tgTtAll;
    private Double tgNoiTinhAll;
    private Double tgNoiMienAll;
    private Double tgLienMienAll;

    public LeadtimeChartTTAllResponse() {
    }

    public LeadtimeChartTTAllResponse(Date ngayBaoCao, Double tgTtAll, Double tgNoiTinhAll, Double tgNoiMienAll, Double tgLienMienAll) {
        this.ngayBaoCao = ngayBaoCao;
        this.tgTtAll = tgTtAll;
        this.tgNoiTinhAll = tgNoiTinhAll;
        this.tgNoiMienAll = tgNoiMienAll;
        this.tgLienMienAll = tgLienMienAll;
    }
}
