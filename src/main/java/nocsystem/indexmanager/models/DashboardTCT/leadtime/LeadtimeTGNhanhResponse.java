package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LeadtimeTGNhanhResponse {
    private Double tgTtNhanh;
    private Double tgTtN1Nhanh;
    private Double tgTtTangTruongNhanh;
    private Double tgFmNhanh;
    private Double tgFmN1Nhanh;
    private Double tgFmTangTruongNhanh;
    private Double tgMmNhanh;
    private Double tgMmN1Nhanh;
    private Double tgMmTangTruongNhanh;
    private Double tgLmNhanh;
    private Double tgLmN1Nhanh;
    private Double tgLmTangTruongNhanh;
    private Double tgNoiTinhNhanh;
    private Double tgNoiTinhN1Nhanh;
    private Double tgNoiTinhTangTruongNhanh;
    private Double tgNoiMienNhanh;
    private Double tgNoiMienN1Nhanh;
    private Double tgNoiMienTangTruongNhanh;
    private Double tgLienMienNhanh;
    private Double tgLienMienN1Nhanh;
    private Double tgLienMienTangTruongNhanh;
}
