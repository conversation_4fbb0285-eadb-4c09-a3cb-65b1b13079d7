package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LeadtimeTGTachKienTKResponse {
    private Double tgTtTachKienTK;
    private Double tgTtN1TachKienTK;
    private Double tgTtTangTruongTachKienTK;
    private Double tgFmTachKienTK;
    private Double tgFmN1TachKienTK;
    private Double tgFmTangTruongTachKienTK;
    private Double tgMmTachKienTK;
    private Double tgMmN1TachKienTK;
    private Double tgMmTangTruongTachKienTK;
    private Double tgLmTachKienTK;
    private Double tgLmN1TachKienTK;
    private Double tgLmTangTruongTachKienTK;
    private Double tgNoiTinhTachKienTK;
    private Double tgNoiTinhN1TachKienTK;
    private Double tgNoiTinhTangTruongTachKienTK;
    private Double tgNoiMienTachKienTK;
    private Double tgNoiMienN1TachKienTK;
    private Double tgNoiMienTangTruongTachKienTK;
    private Double tgLienMienTachKienTK;
    private Double tgLienMienN1TachKienTK;
    private Double tgLienMienTangTruongTachKienTK;
}
