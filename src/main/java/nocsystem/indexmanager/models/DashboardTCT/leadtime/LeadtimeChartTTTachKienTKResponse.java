package nocsystem.indexmanager.models.DashboardTCT.leadtime;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@Data
public class LeadtimeChartTTTachKienTKResponse {
    private Date ngayBaoCao;
    private Double tgTtTachKienTK;
    private Double tgNoiTinhTachKienTK;
    private Double tgNoiMienTachKienTK;
    private Double tgLienMienTachKienTK;

    public LeadtimeChartTTTachKienTKResponse() {
    }

    public LeadtimeChartTTTachKienTKResponse(Date ngayBaoCao, Double tgTtTachKienTK, Double tgNoiTinhTachKienTK, Double tgNoiMienTachKienTK, Double tgLienMienTachKienTK) {
        this.ngayBaoCao = ngayBaoCao;
        this.tgTtTachKienTK = tgTtTachKienTK;
        this.tgNoiTinhTachKienTK = tgNoiTinhTachKienTK;
        this.tgNoiMienTachKienTK = tgNoiMienTachKienTK;
        this.tgLienMienTachKienTK = tgLienMienTachKienTK;
    }
}
