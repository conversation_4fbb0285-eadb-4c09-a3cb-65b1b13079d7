package nocsystem.indexmanager.models.Grab;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


@Getter
@Setter
//@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "grab_doi_soat")
public class GrabDoiSoat {
    @Id
    @Column(name = "ma_vandon")
    private String maVanDon;

//    @Column(name = "ngay_nhap_may")
//    private LocalDateTime ngayNhapMay;
//
//    @Column(name = "ngay_phat_thanhcong")
//    private LocalDateTime ngayPhatThanhCong;

    //    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ngay_nhap_may")
    private Date ngayNhapMay;

    //    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ngay_phat_thanhcong")
    private LocalDateTime ngayPhatThanhCong;

    @Column(name = "order_id")
    private BigInteger orderId;

    @Column(name = "khoang_cach")
    private Double khoangCach;

    @Column(name = "ma_cn_goc")
    private String maChiNhanhGoc;

    @Column(name = "ten_cn_goc")
    private String tenChiNhanhGoc;

    @Column(name = "ma_bc_goc")
    private String maBuuCucGoc;

    @Column(name = "ten_bc_goc")
    private String tenBuuCucGoc;

    @Column(name = "ma_kh_goc")
    private String maKhachHangGoc;

    @Column(name = "ten_kh_goc")
    private String tenKhachHangGoc;

    @Column(name = "ma_cn_phat")
    private String maChiNhanhPhat;

    @Column(name = "ten_cn_phat")
    private String tenChiNhanhPhat;

    @Column(name = "ma_bc_phat")
    private String maBuuCucPhat;

    @Column(name = "ten_bc_phat")
    private String tenBuuCucPhat;

    @Column(name = "ten_kh_nhan")
    private String tenKhachHangNhan;

    @Column(name = "vung")
    private String vung;

    @Column(name = "ma_dichvu_viettel")
    private String maDichVuVietTel;

    @Column(name = "trong_luong")
    private BigDecimal trongLuong;

    @Column(name = "vtp_trangthai_cuoicung")
    private Integer vtpTrangThaiCuoiCung;

    @Column(name = "vtp_tongtien")
    private BigDecimal vtpTongTien;

    @Column(name = "vtp_cuoc_chieudi")
    private BigDecimal vtpCuocChieuDi;

    @Column(name = "vtp_phuphi")
    private BigDecimal vtpPhuPhi;

    @Column(name = "vtp_chietkhau")
    private BigDecimal vtpChietKhau;

    @Column(name = "grab_trangthai")
    private Integer grabTrangThai;

    @Column(name = "grab_tongtien")
    private BigDecimal grabTongTien;

    @Column(name = "grab_cuoc_chieudi")
    private BigDecimal grabCuocChieuDi;

    @Column(name = "grab_phuphi")
    private BigDecimal grabPhuPhi;

    @Column(name = "grab_chieukhau")
    private BigDecimal grabChieuKhau;

    @Column(name = "chenhlech_tongtien")
    private BigDecimal chenhLechTongTien;

    @Column(name = "chenhlech_cuoc_chieudi")
    private BigDecimal chenhLechCuocChieuDi;

    @Column(name = "chenhlech_phuphi")
    private BigDecimal chenhLechPhuPhi;

    @Column(name = "chenhlech_chietkhau")
    private BigDecimal chenhLechChietKhau;

    @Column(name = "ghi_chu")
    private String ghiChu;

    @Column(name = "file_id")
    private Integer fileId;

    public GrabDoiSoat(String maVanDon, Date ngayNhapMay, LocalDateTime ngayPhatThanhCong) {
        this.maVanDon = maVanDon;
        this.ngayNhapMay = ngayNhapMay;
        this.ngayPhatThanhCong = ngayPhatThanhCong;
    }

    public GrabDoiSoat(String maVanDon, Date ngayNhapMay, LocalDateTime ngayPhatThanhCong, BigInteger orderId, Double khoangCach, String maChiNhanhGoc, String tenChiNhanhGoc, String maBuuCucGoc, String tenBuuCucGoc, String maKhachHangGoc, String tenKhachHangGoc, String maChiNhanhPhat, String tenChiNhanhPhat, String maBuuCucPhat, String tenBuuCucPhat, String tenKhachHangNhan, String vung, String maDichVuVietTel, BigDecimal trongLuong, Integer vtpTrangThaiCuoiCung, BigDecimal vtpTongTien, BigDecimal vtpCuocChieuDi, BigDecimal vtpPhuPhi, BigDecimal vtpChietKhau, Integer grabTrangThai, BigDecimal grabTongTien, BigDecimal grabCuocChieuDi, BigDecimal grabPhuPhi, BigDecimal grabChieuKhau, BigDecimal chenhLechTongTien, BigDecimal chenhLechCuocChieuDi, BigDecimal chenhLechPhuPhi, BigDecimal chenhLechChietKhau, String ghiChu, Integer fileId) {
        this.maVanDon = maVanDon;
        this.ngayNhapMay = ngayNhapMay;
        this.ngayPhatThanhCong = ngayPhatThanhCong;
        this.orderId = orderId;
        this.khoangCach = khoangCach;
        this.maChiNhanhGoc = maChiNhanhGoc;
        this.tenChiNhanhGoc = tenChiNhanhGoc;
        this.maBuuCucGoc = maBuuCucGoc;
        this.tenBuuCucGoc = tenBuuCucGoc;
        this.maKhachHangGoc = maKhachHangGoc;
        this.tenKhachHangGoc = tenKhachHangGoc;
        this.maChiNhanhPhat = maChiNhanhPhat;
        this.tenChiNhanhPhat = tenChiNhanhPhat;
        this.maBuuCucPhat = maBuuCucPhat;
        this.tenBuuCucPhat = tenBuuCucPhat;
        this.tenKhachHangNhan = tenKhachHangNhan;
        this.vung = vung;
        this.maDichVuVietTel = maDichVuVietTel;
        this.trongLuong = trongLuong;
        this.vtpTrangThaiCuoiCung = vtpTrangThaiCuoiCung;
        this.vtpTongTien = vtpTongTien;
        this.vtpCuocChieuDi = vtpCuocChieuDi;
        this.vtpPhuPhi = vtpPhuPhi;
        this.vtpChietKhau = vtpChietKhau;
        this.grabTrangThai = grabTrangThai;
        this.grabTongTien = grabTongTien;
        this.grabCuocChieuDi = grabCuocChieuDi;
        this.grabPhuPhi = grabPhuPhi;
        this.grabChieuKhau = grabChieuKhau;
        this.chenhLechTongTien = chenhLechTongTien;
        this.chenhLechCuocChieuDi = chenhLechCuocChieuDi;
        this.chenhLechPhuPhi = chenhLechPhuPhi;
        this.chenhLechChietKhau = chenhLechChietKhau;
        this.ghiChu = ghiChu;
        this.fileId = fileId;
    }
}
