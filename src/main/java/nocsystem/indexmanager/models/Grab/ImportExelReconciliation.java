package nocsystem.indexmanager.models.Grab;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
//@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "import_exel_reconciliation")
public class ImportExelReconciliation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private int id;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "directory")
    private String directory;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "type_upload")
    private Short typeUpload;

    @Column(name = "data_syn")
    private Short dataSync;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public ImportExelReconciliation(int id, String fileName, String directory, String createdBy, LocalDateTime createdAt, Short typeUpload, Short dataSync, LocalDateTime updatedAt) {
        this.id = id;
        this.fileName = fileName;
        this.directory = directory;
        this.createdBy = createdBy;
        this.createdAt = createdAt;
        this.typeUpload = typeUpload;
        this.dataSync = dataSync;
        this.updatedAt = updatedAt;
    }
}
