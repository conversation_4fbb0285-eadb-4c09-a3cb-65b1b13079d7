package nocsystem.indexmanager.models.Grab;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
//@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "grab_tong_hop_doi_soat")
public class TongHopDoiSoatGrap {
    @Column(name = "ngay_nhap_may")
    private LocalDate ngayNhapMay;

    @Column(name = "ngay_phat_thanhcong")
    private LocalDate ngayPhatThanhCong;

    @Id
    @Column(name = "ma_chi_nhanh")
    private String maChiNhanh;

    @Column(name = "chi_nhanh")
    private String chiNhanh;

    @Column(name = "ma_buu_cuc")
    private String maBuuCuc;

    @Column(name = "buu_cuc")
    private String buuCuc;

    @Column(name = "vtp_so_luong_don")
    private String vtpSoLuongDon;

    @Column(name = "vtp_tongtien")
    private String vtpTongTien;

    @Column(name = "vtp_cuoc_chieudi")
    private String vtpCuocChieuDi;

    @Column(name = "vtp_phuphi")
    private BigDecimal vtpPhuPhi;

    @Column(name = "vtp_chietkhau")
    private BigDecimal vtpChietKhau;

    @Column(name = "grab_so_luong_don")
    private Integer grabSoLuongDon;

    @Column(name = "grab_tongtien")
    private BigDecimal grabTongTien;

    @Column(name = "grab_cuoc_chieudi")
    private BigDecimal grabCuocChieuDi;

    @Column(name = "grab_phuphi")
    private BigDecimal grabPhuPhi;

    @Column(name = "grab_chieukhau")
    private BigDecimal grabChieuKhau;

    @Column(name = "chenhlech_so_luong_don")
    private Integer chenhlechSoLuongDon;

    @Column(name = "chenhlech_tongtien")
    private BigDecimal chenhLechTongTien;

    @Column(name = "chenhlech_chietkhau")
    private BigDecimal chenhLechChietKhau;

    @Column(name = "ghi_chu")
    private String ghiChu;

    @Column(name = "file_id")
    private Integer fileId;

    public TongHopDoiSoatGrap(String maChiNhanh, LocalDate ngayNhapMay, LocalDate ngayPhatThanhCong, String chiNhanh, String maBuuCuc, String buuCuc, String vtpSoLuongDon, String vtpTongTien, String vtpCuocChieuDi, BigDecimal vtpPhuPhi, BigDecimal vtpChietKhau, Integer grabSoLuongDon, BigDecimal grabTongTien, BigDecimal grabCuocChieuDi, BigDecimal grabPhuPhi, BigDecimal grabChieuKhau, BigDecimal chenhLechTongTien, BigDecimal chenhLechChietKhau, String ghiChu, Integer fileId) {
        this.maChiNhanh = maChiNhanh;
        this.ngayNhapMay = ngayNhapMay;
        this.ngayPhatThanhCong = ngayPhatThanhCong;
        this.chiNhanh = chiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.buuCuc = buuCuc;
        this.vtpSoLuongDon = vtpSoLuongDon;
        this.vtpTongTien = vtpTongTien;
        this.vtpCuocChieuDi = vtpCuocChieuDi;
        this.vtpPhuPhi = vtpPhuPhi;
        this.vtpChietKhau = vtpChietKhau;
        this.grabSoLuongDon = grabSoLuongDon;
        this.grabTongTien = grabTongTien;
        this.grabCuocChieuDi = grabCuocChieuDi;
        this.grabPhuPhi = grabPhuPhi;
        this.grabChieuKhau = grabChieuKhau;
        this.chenhLechTongTien = chenhLechTongTien;
        this.chenhLechChietKhau = chenhLechChietKhau;
        this.ghiChu = ghiChu;
        this.fileId = fileId;
    }
}
