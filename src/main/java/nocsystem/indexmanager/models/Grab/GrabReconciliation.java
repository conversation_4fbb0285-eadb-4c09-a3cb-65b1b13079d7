package nocsystem.indexmanager.models.Grab;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Getter
@Setter
//@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "grab_reconciliation")
public class GrabReconciliation implements Serializable {
    @Id
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "ma_van_don")
    private String maVanDon;

    @Column(name = "trang_thai")
    private Integer trangThai;

    @Column(name = "tong_tien")
    private BigDecimal tongTien;

    @Column(name = "cuoc_chuyen_di")
    private BigDecimal cuocChuyenDi;

    @Column(name = "phu_phi")
    private BigDecimal phuPhi;

    @Column(name = "chiet_khau")
    private BigDecimal chietKhau;

    @Column(name = "file_id")
    private Integer fileId;

    public GrabReconciliation(LocalDateTime createdAt, String maVanDon, Integer trangThai, BigDecimal tongTien, BigDecimal cuocChuyenDi, BigDecimal phuPhi, BigDecimal chietKhau, Integer fileId) {
        this.createdAt = createdAt;
        this.maVanDon = maVanDon;
        this.trangThai = trangThai;
        this.tongTien = tongTien;
        this.cuocChuyenDi = cuocChuyenDi;
        this.phuPhi = phuPhi;
        this.chietKhau = chietKhau;
        this.fileId = fileId;
    }
}
