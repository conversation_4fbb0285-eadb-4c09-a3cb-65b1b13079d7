package nocsystem.indexmanager.models.DmSanLuong;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="lksanluongbuuta")
public class LKSanLuongBuuTa {

    @Id
    @Column(name="update_at")
    private LocalDate updateAt;

    @Column(name="ma_nv")
    private String maNhanVien;

    @Column(name="ma_buu_cuc")
    private String maBuuCuc;

    @Column(name="ma_chi_nhanh")
    private String maChiNhanh;

    @Column(name="tong_don_giao_tc")
    private Integer tongDonGiaoTC;

    @Column(name="tong_don_nhan_tc")
    private Integer tongDonNhanTC;

    @Column(name="tong_don_dang_nhan")
    private Integer tongDonDangNhan;

    @Column(name="tong_don_delay_nhan")
    private Integer tongDonDelayNhan;

    @Column(name="tong_don_huy_nhan")
    private Integer tongDonHuyNhan;

}
