package nocsystem.indexmanager.models.DmSanLuong;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor

@Entity
@Table(name="lksanluongkho")

public class LKSanLuongKho {

    @Id
    @Column(name="update_at")
    private LocalDate updateAt;

    @Column(name="sl_don_lay_nhan_tc")
    private Long slDonLayNhanTC;

    @Column(name="sl_don_da_giao_tc")
    private Long slDonDaGiaoTC;

    @Column(name="sl_don_ton_phan_cong_nhan")
    private Long slDonTonPhanCongNhan;

    @Column(name="sl_don_lay_ton_nhan")
    private Long slDonLayTonNhan;

    @Column(name="sl_don_ton_xuat_tt_hub_sub_bc")
    private Long slDonTonXuatTTHubSubBC;

    @Column(name="sl_don_ton_xuat_tt_ttkt")
    private Long slDonTonXuatTTTTKT;

    @Column(name="ma_tinh")
    private String maTinh;

    @Column(name="ten_tinh")
    private String tenTinh;

    @Column(name="ma_buu_cuc")
    private String maBuuCuc;

}
