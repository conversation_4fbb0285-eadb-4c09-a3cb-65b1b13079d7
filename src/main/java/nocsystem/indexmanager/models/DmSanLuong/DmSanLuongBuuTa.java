package nocsystem.indexmanager.models.DmSanLuong;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.ZonedDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="dmsanluongbuuta")
public class DmSanLuongBuuTa {

    @Id
    @Column(name="ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name="ma_nv")
    private String maNhanVien;

    @Column(name="ma_buu_cuc")
    private String maBuuCuc;

    @Column(name="ma_chi_nhanh")
    private String maChiNhanh;

    @Column(name="tong_don_giao_tc")
    private Integer tongDonGiaoTC;

    @Column(name="tong_don_dang_giao")
    private Integer tongDonDangGiao;

    @Column(name="tong_don_delay_giao_hang")
    private Integer tongDonDelayGiaoHang;

    @Column(name="tong_don_nhan_tc")
    private Integer tongDonNhanTC;

    @Column(name="tong_don_dang_nhan")
    private Integer tongDonDangNhan;

    @Column(name="tong_don_delay_nhan")
    private Integer tongDonDelayNhan;

    @Column(name="tong_don_huy_nhan")
    private Integer tongDonHuyNhan;

}
