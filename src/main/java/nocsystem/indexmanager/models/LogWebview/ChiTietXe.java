package nocsystem.indexmanager.models.LogWebview;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "sl_xe_cho_va_dang_khaithac")
public class ChiTietXe {

    @Id
    @Column(name = "ma_chuyen_xe")
    private String maChuyenXe;

    @Column(name = "don_vi_van_chuyen")
    private String dvvc;

    @Column(name = "tuyen")
    private String tuyen;

    @Column(name = "bien_so_xe")
    private String bienSoXe;

    @Column(name = "time_checkin")
    private String timeCheckin;

    @Column(name = "user_lai")
    private String userLai;

    @Column(name = "so_dien_thoai")
    private String soDienThoai;

    @Column(name = "san_luong")
    private Long sanLuong;

    @Column(name = "trong_luong")
    private Float trongLuong;

    @Column(name = "status")
    private Integer status;

    @Column(name = "trong_tai")
    private Long trongTai;

    @Column(name = "thoi_gian_cho")
    private Long thoiGianCho;

    @Column(name = "trung_tam_khai_thac")
    private String trungTamKhaiThac;

    @Column(name = "hanh_trinh")
    private String hanhTrinh;

    @Column(name = "type_cho")
    private Integer typeCho;

    @Column(name = "type")
    private Integer type;

    @Column(name = "time_du_kien")
    private Long timeDuKien;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

}
