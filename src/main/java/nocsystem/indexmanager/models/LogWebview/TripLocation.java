package nocsystem.indexmanager.models.LogWebview;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@Entity
@Table(name = "trip_location")
public class TripLocation {

    @Id
    @Column(name = "trip_id")
    private Long tripId;

    @Column(name = "arrival_time")
    private Date arrivalTime;

    @Column(name = "reg_no")
    private String regNo;

}
