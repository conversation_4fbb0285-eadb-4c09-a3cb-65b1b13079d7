package nocsystem.indexmanager.models.LogWebview;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "sl_xe_cho_va_dang_khaithac_tinh")
@IdClass(SanLuongXeTinhKey.class)
public class SanLuongXeTinh implements Serializable {

    @Id
    @Column(name = "bien_so_xe")
    private String bienSoXe;

    @Id
    @Column(name = "status")
    private Integer status;

    @Column(name = "ma_chuyen_xe")
    private String maChuyenXe;

    @Column(name = "don_vi_van_chuyen")
    private String dvvc;

    @Column(name = "tuyen")
    private String tuyen;

    @Column(name = "time_checkin")
    private String timeCheckin;

    @Column(name = "user_lai")
    private String userLai;

    @Column(name = "so_dien_thoai")
    private String soDienThoai;

    @Column(name = "san_luong")
    private Long sanLuong;

    @Column(name = "trong_luong")
    private Double trongLuong;

    @Column(name = "trong_tai")
    private Long trongTai;

    @Column(name = "thoi_gian_cho")
    private Double thoiGianCho;

    @Column(name = "trung_tam_khai_thac_tinh")
    private String trungTamKhaiThacTinh;

    @Column(name = "hanh_trinh")
    private String hanhTrinh;

    @Column(name = "type_cho")
    private Integer typeCho;

    @Column(name = "type")
    private Integer type;

    @Column(name = "time_du_kien")
    private Long timeDuKien;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

}
