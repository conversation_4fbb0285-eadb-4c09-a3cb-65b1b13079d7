package nocsystem.indexmanager.models.TienDoDoanhThuChuyenPhat;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "cskd_tiendodoanhthu_cp")
public class TienDoDoanhThuCP {
    @Id
    @Column(name = "dich_vu")
    private String dichVu;

    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tlht")
    private Float tlHoanThanh;

    @Column(name = "tiendo")
    private Float tienDo;

    @Column(name = "tt_thang")
    private Float ttThang;

    @Column(name = "tt_tbn_thang")
    private Float ttTbnThang;

    @Column(name = "tt_nam")
    private Float ttNam;

    @Column(name = "ke_hoach")
    private Float keHoach;

    @Column(name = "thuc_hien")
    private Float thucHien;

    @Column(name = "thangtruoc")
    private Float thangTruoc = Float.valueOf(0);

    @Column(name = "namtruoc")
    private Float namTruoc = Float.valueOf(0);

    @Column(name = "cung_ky_ngay")
    private Float cungKyNgay = Float.valueOf(0);

    @Column(name = "cung_ky_thang")
    private Float cungKyThang = Float.valueOf(0);

    @Column(name = "cung_ky_nam")
    private Float cungKyNam = Float.valueOf(0);

    @Column(name = "tl_tt_ngay")
    private Float tiLeTangTruongNgay;
}
