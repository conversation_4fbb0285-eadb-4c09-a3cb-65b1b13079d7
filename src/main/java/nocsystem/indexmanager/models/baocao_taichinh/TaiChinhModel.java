package nocsystem.indexmanager.models.baocao_taichinh;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import lombok.Data;

import javax.persistence.*;

import java.lang.reflect.Method;

@Entity
@Table(name = "bao_cao_tai_chinh",
        uniqueConstraints = @UniqueConstraint(columnNames = {"thang", "loai_bang", "ma_so"}))
@IdClass(TaiChinhModelId.class)
@ToString
@Setter
@Getter
public class TaiChinhModel {
    @Id
    @Column(name = "thang")
    private Integer thang;
    @Column(name = "ngay_baocao")
    private String ngay_baocao;
    @Id
    @Column(name = "ma_so")
    private String ma_so;

    @Column(name = "hop_nhat")
    private Float hop_nhat;

    @Column(name = "cty_me")
    private Float cty_me;

    @Column(name = "cty_tmdt")
    private Float cty_tmdt;

    @Column(name = "cty_congnghe")
    private Float cty_congnghe;

    @Column(name = "cty_log")
    private Float cty_log;

    @Column(name = "campuchia_nguyente")
    private Float campuchia_nguyente;

    @Column(name = "campuchia_chuyendoi")
    private Float campuchia_chuyendoi;

    @Column(name = "myanmar_nguyente")
    private Float myanmar_nguyente;

    @Column(name = "myanmar_chuyendoi")
    private Float myanmar_chuyendoi;

    @Id
    @Column(name = "loai_bang")
    private String loai_bang;

    public void setProperty(String propertyName, Object value) {
        try {
            String setIsterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
            Method setIsterMethod = null;
            if(value != null) {
                setIsterMethod = getClass().getMethod(setIsterMethodName, value.getClass());
            }
            else  return;

            setIsterMethod.invoke(this, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

