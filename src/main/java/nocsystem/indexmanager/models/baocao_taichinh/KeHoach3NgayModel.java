package nocsystem.indexmanager.models.baocao_taichinh;



import javax.persistence.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.lang.reflect.Method;

@Entity
@Table(name = "ke_hoach_bc_ngay3",
        uniqueConstraints = @UniqueConstraint(columnNames = {"ky", "loai_bang","tan_suat","chi_tieu"}))
@IdClass(KeHoach3NgayId.class)
@ToString
@Setter
@Getter
public class KeHoach3NgayModel {
    @Id
    @Column(name = "ky")
    private Integer ky;

    @Id
    @Column(name = "chi_tieu")
    private String chi_tieu;

    @Id
    @Column(name = "tan_suat")
    private String tan_suat;

    @Id
    @Column(name = "loai_bang")
    private String loai_bang;

    @Column(name = "hop_nhat")
    private Float hop_nhat;

    @Column(name = "cty_me")
    private Float cty_me;

    @Column(name = "cty_tmdt")
    private Float cty_tmdt;

    @Column(name = "cty_congnghe")
    private Float cty_congnghe;

    @Column(name = "cty_log")
    private Float cty_log;

    @Column(name = "campuchia_nguyente")
    private Float campuchia_nguyente;

    @Column(name = "campuchia_chuyendoi")
    private Float campuchia_chuyendoi;

    @Column(name = "myanmar_nguyente")
    private Float myanmar_nguyente;

    @Column(name = "myanmar_chuyendoi")
    private Float myanmar_chuyendoi;


    public void setProperty(String propertyName, Object value) {
        try {
            String setIsterMethodName = "set" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
            Method setIsterMethod = null;
            if(value != null) {
                setIsterMethod = getClass().getMethod(setIsterMethodName, value.getClass());
            }
            else  return;

            setIsterMethod.invoke(this, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
