package nocsystem.indexmanager.models.mucdohailong;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "kpi_mucdohailong")
public class KpiMucDoHaiLong {
    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "ma_vung")
    private String maVung;

    @Column(name = "ten_vung")
    private String tenVung;

    @Column(name = "ma_cn")
    private String maChiNhanh;

    @Column(name = "ten_cn")
    private String tenChiNhanh;

    @Column(name = "tieu_chi")
    private String tieuChi;

    @Column(name = "kpi_thang_1")
    private Double kpiThang01;

    @Column(name = "kpi_thang_2")
    private Double kpiThang02;

    @Column(name = "kpi_thang_3")
    private Double kpiThang03;

    @Column(name = "kpi_thang_4")
    private Double kpiThang04;

    @Column(name = "kpi_thang_5")
    private Double kpiThang05;

    @Column(name = "kpi_thang_6")
    private Double kpiThang06;

    @Column(name = "kpi_thang_7")
    private Double kpiThang07;

    @Column(name = "kpi_thang_8")
    private Double kpiThang08;

    @Column(name = "kpi_thang_9")
    private Double kpiThang09;

    @Column(name = "kpi_thang_10")
    private Double kpiThang10;

    @Column(name = "kpi_thang_11")
    private Double kpiThang11;

    @Column(name = "kpi_thang_12")
    private Double kpiThang12;

    @Column(name = "nam")
    private String nam;

    public KpiMucDoHaiLong() {
    }

    public KpiMucDoHaiLong(String maVung, String tenVung, String maChiNhanh, String tenChiNhanh, String tieuChi, Double kpiThang1, Double kpiThang2, Double kpiThang3, Double kpiThang4, Double kpiThang5, Double kpiThang6, Double kpiThang7, Double kpiThang8, Double kpiThang9, Double kpiThang10, Double kpiThang11, Double kpiThang12, String nam) {
        this.maVung = maVung;
        this.tenVung = tenVung;
        this.maChiNhanh = maChiNhanh;
        this.tenChiNhanh = tenChiNhanh;
        this.tieuChi = tieuChi;
        this.kpiThang01 = kpiThang1;
        this.kpiThang02 = kpiThang2;
        this.kpiThang03 = kpiThang3;
        this.kpiThang04 = kpiThang4;
        this.kpiThang05 = kpiThang5;
        this.kpiThang06 = kpiThang6;
        this.kpiThang07 = kpiThang7;
        this.kpiThang08 = kpiThang8;
        this.kpiThang09 = kpiThang9;
        this.kpiThang10 = kpiThang10;
        this.kpiThang11 = kpiThang11;
        this.kpiThang12 = kpiThang12;
        this.nam = nam;
    }
}
