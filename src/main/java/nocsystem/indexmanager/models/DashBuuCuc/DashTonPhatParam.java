package nocsystem.indexmanager.models.DashBuuCuc;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class DashTonPhatParam {
    String ngayBaoCao;
    List<String> chiNhanhPhat;
    List<String> buuCuc;
    String nguongTon;
    String tuyenBuuTa;
    String mauLuaChon;
    //
    // 2023-08-16 ngocnt add 1 param trangThai
    String trangThai;
    //2023-09-07 ngocnt add 2 param for sort mobile order, orderBy
    String order ="";
    String orderBy ="tong";
    String loaiDichVu;
    String vungCon;
    String loaiHH;
    String maDoiTac;
    String khDacThuGui;
    String khDacThuNhan;
    int page;
    int pageSize;

}
