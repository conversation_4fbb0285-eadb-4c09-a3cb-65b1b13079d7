package nocsystem.indexmanager.models.DashBuuCuc;

public class DashboardTongTonPhatdto {
    private String tongSl;
    private Long vang;
    private Long do1;
    private Long do2;
    private  Long do3;
    private  Long do4;
    private Long do5;
    private Long do6;
    private Long do7;
    private  Long do8;
    private Long do9;
    private Long chuaXacDinh;
    private Long tong;

    public DashboardTongTonPhatdto() {
    }

    public DashboardTongTonPhatdto(String tongSl, Long vang, Long do1, <PERSON> do2, <PERSON> do3, <PERSON> do4, <PERSON> do5, <PERSON> do6, <PERSON> do7, <PERSON> do8, <PERSON> do9, <PERSON> chuaXacDinh, Long tong) {
        this.tongSl = tongSl;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.do4 = do4;
        this.do5 = do5;
        this.do6 = do6;
        this.do7 = do7;
        this.do8 = do8;
        this.do9 = do9;
        this.chuaXacDinh = chuaXacDinh;
        this.tong = tong;
    }

    public String getTongSl() {
        return tongSl;
    }

    public void setTongSl(String tongSl) {
        this.tongSl = tongSl;
    }

    public Long getVang() {
        return vang;
    }

    public void setVang(Long vang) {
        this.vang = vang;
    }

    public Long getDo1() {
        return do1;
    }

    public void setDo1(Long do1) {
        this.do1 = do1;
    }

    public Long getDo2() {
        return do2;
    }

    public void setDo2(Long do2) {
        this.do2 = do2;
    }

    public Long getDo3() {
        return do3;
    }

    public void setDo3(Long do3) {
        this.do3 = do3;
    }

    public Long getDo4() {
        return do4;
    }

    public void setDo4(Long do4) {
        this.do4 = do4;
    }

    public Long getDo5() {
        return do5;
    }

    public void setDo5(Long do5) {
        this.do5 = do5;
    }

    public Long getDo6() {
        return do6;
    }

    public void setDo6(Long do6) {
        this.do6 = do6;
    }

    public Long getDo7() {
        return do7;
    }

    public void setDo7(Long do7) {
        this.do7 = do7;
    }

    public Long getDo8() {
        return do8;
    }

    public void setDo8(Long do8) {
        this.do8 = do8;
    }

    public Long getDo9() {
        return do9;
    }

    public void setDo9(Long do9) {
        this.do9 = do9;
    }

    public Long getTong() {
        return tong;
    }

    public void setTong(Long tong) {
        this.tong = tong;
    }

    public Long getChuaXacDinh() {
        return chuaXacDinh;
    }

    public void setChuaXacDinh(Long chuaXacDinh) {
        this.chuaXacDinh = chuaXacDinh;
    }
}
