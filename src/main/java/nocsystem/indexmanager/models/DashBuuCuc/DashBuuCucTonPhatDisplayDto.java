package nocsystem.indexmanager.models.DashBuuCuc;

import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class DashBuuCucTonPhatDisplayDto {
    String maPhieuGui;
    String chiNhanhPhat;
    String vungCon;
    String buuCucPhat;
    String tuyenBuuTaPhat;
    Long vang =0L;
    Long do1=0L;
    Long do2=0L;
    Long do3=0L;
    Long do4=0L;
    Long do5=0L;
    Long do6=0L;
    Long do7=0L;
    Long do8=0L;
    Long do9=0L;
    Long chuaXacDinh=0L;
    Long tong=0L;


    public DashBuuCucTonPhatDisplayDto(String chiNhanhPhat, Long vang, Long do1, Long do2, Long do3, Long do4, Long do5, Long do6, Long do7, Long do8, Long do9, Long chuaXacDinh, Long tong) {
        this.chiNhanhPhat = chiNhanhPhat;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.do4 = do4;
        this.do5 = do5;
        this.do6 = do6;
        this.do7 = do7;
        this.do8 = do8;
        this.do9 = do9;
        this.chuaXacDinh = chuaXacDinh;
        this.tong = tong;
    }

    public DashBuuCucTonPhatDisplayDto(String chiNhanhPhat, String vungCon, String buuCucPhat, Long vang, Long do1, Long do2, Long do3, Long do4, Long do5, Long do6, Long do7, Long do8, Long do9, Long chuaXacDinh, Long tong) {
        this.chiNhanhPhat = chiNhanhPhat;
        this.vungCon = vungCon;
        this.buuCucPhat = buuCucPhat;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.do4 = do4;
        this.do5 = do5;
        this.do6 = do6;
        this.do7 = do7;
        this.do8 = do8;
        this.do9 = do9;
        this.chuaXacDinh = chuaXacDinh;
        this.tong = tong;
    }

    public DashBuuCucTonPhatDisplayDto(String chiNhanhPhat, String vungCon, String buuCucPhat, String tuyenBuuTaPhat, Long vang, Long do1, Long do2, Long do3, Long do4, Long do5, Long do6, Long do7, Long do8, Long do9, Long chuaXacDinh, Long tong) {
        this.chiNhanhPhat = chiNhanhPhat;
        this.vungCon = vungCon;
        this.buuCucPhat = buuCucPhat;
        this.tuyenBuuTaPhat = tuyenBuuTaPhat;
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.do4 = do4;
        this.do5 = do5;
        this.do6 = do6;
        this.do7 = do7;
        this.do8 = do8;
        this.do9 = do9;
        this.chuaXacDinh = chuaXacDinh;
        this.tong = tong;
    }

    public DashBuuCucTonPhatDisplayDto(Long vang, Long do1, Long do2, Long do3, Long do4, Long do5, Long do6, Long do7, Long do8, Long do9, Long chuaXacDinh, Long tong) {
        this.vang = vang;
        this.do1 = do1;
        this.do2 = do2;
        this.do3 = do3;
        this.do4 = do4;
        this.do5 = do5;
        this.do6 = do6;
        this.do7 = do7;
        this.do8 = do8;
        this.do9 = do9;
        this.chuaXacDinh = chuaXacDinh;
        this.tong = tong;
    }
}
