package nocsystem.indexmanager.models.DashBuuCuc;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.HashMap;


@NoArgsConstructor
@Getter
@Setter
public class DashBuuCucTonPhatChiTietExcelDto {

    private String maPhieuGui;


    private String ngayBaoCao;


    private String tinhNhan;


    private String huyenNhan;


    private String tenHuyenNhan;


    private String ttktFrom;


    private String chiNhanhPhat;


    private String huyenPhat;


    private String tenHuyenPhat;


    private String ttktTo;


    private String maDvViettel;


    private String maBuuCucGoc;


    private String timeTacDong;


    private String trangThai;


    private String maBuuCucHT;


    private String chiNhanhHT;


    private String maDoiTac;

    private String maKHGui;


    private String buuCucPhat;


    private String timePCP;



    private String timeGachBP;


    private String ngayguiBP;


    private String danhGia;


    private String loaiPG;


    private String lanPhat;


    private String tgConPhat;


    private String tuyenBuuTaPhat;


    private String tienCOD;


    private String khauFM;


    private String khauMM;


    private String khauLM;


    private String tgQuyDinh;


    private String tgTTLuyKe;


    private String tgChenhLech;


    private String dgMocMM;


    private String nguongTon;


    private String loaiPCP;


    private String loaiCanhBao;


    private String dgMocLM;


    private String loaiDichVu;


    private Long version;


    private String timeTinhToan;

    private String trongLuong;

    private String tienCuoc;

    private String loaiHangHoa;

    private String khDacThuGui;

    private String khDacThuNhan;
    private String idPhuongXaPhat;
    private String tenPhuongXaPhat;
    private String maDichVuCongThem;
    private String tgTonPcpCuoiCung;
    private String timePcpDauTien;

    private String loaiDon;

    public HashMap<String, Object> objectHashMap = new HashMap<>();

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> convert(Object targetObject) throws IllegalAccessException, NoSuchFieldException {

        HashMap<String, Object> map = new HashMap<>();
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        int i = 0;
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(this);
            Field targetField = targetObject.getClass().getDeclaredField(field.getName());
            targetField.setAccessible(true);
            if (targetField.getType() == String.class) {
                targetField.set(targetObject, String.valueOf(fieldValue));
            }  else if (fieldValue instanceof Integer) {
                targetField.set(targetObject, Integer.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof Double) {
                targetField.set(targetObject, Double.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof Float) {
                targetField.set(targetObject, Float.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof Boolean) {
                targetField.set(targetObject, Boolean.valueOf(String.valueOf(fieldValue)));
            } else if (fieldValue instanceof LocalDate) {
                targetField.set(targetObject, LocalDate.parse(String.valueOf(fieldValue)));
            }
            if (!field.getName().equals("objectHashMap")) {
                map.put(field.getName(), fieldValue);
            }
            i++;
        }
        return map;
    }

    public DashBuuCucTonPhatChiTietExcelDto(String maPhieuGui, String ngayBaoCao, String tinhNhan, String huyenNhan, String tenHuyenNhan, String ttktFrom,
                                            String chiNhanhPhat, String huyenPhat, String tenHuyenPhat, String ttktTo, String maDvViettel, String maBuuCucGoc,
                                            String timeTacDong, String trangThai, String maBuuCucHT, String chiNhanhHT, String maDoiTac, String maKHGui,
                                            String buuCucPhat, String timePCP, String timeGachBP, String ngayguiBP, String danhGia, String loaiPG, String lanPhat,
                                            String tgConPhat, String tuyenBuuTaPhat, String tienCOD, String khauFM, String khauMM, String khauLM, String tgQuyDinh,
                                            String tgTTLuyKe, String tgChenhLech, String dgMocMM, String nguongTon,
                                            String loaiPCP, String loaiCanhBao, String dgMocLM, String loaiDichVu, Long version, String timeTinhToan,
                                            String trongLuong, String tienCuoc, String loaiHangHoa, String khDacThuGui, String khDacThuNhan,String idPhuongXaPhat,String tenPhuongXaPhat, String maDichVuCongThem, String tgTonPcpCuoiCung, String timePcpDauTien
    , String loaiDon)
            throws NoSuchFieldException, IllegalAccessException {
        this.maPhieuGui = maPhieuGui;
        this.ngayBaoCao = ngayBaoCao;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tenHuyenNhan = tenHuyenNhan;
        this.ttktFrom = ttktFrom;
        this.chiNhanhPhat = chiNhanhPhat;
        this.huyenPhat = huyenPhat;
        this.tenHuyenPhat = tenHuyenPhat;
        this.ttktTo = ttktTo;
        this.maDvViettel = maDvViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.timeTacDong = timeTacDong;
        this.trangThai = trangThai;
        this.maBuuCucHT = maBuuCucHT;
        this.chiNhanhHT = chiNhanhHT;
        this.maDoiTac = maDoiTac;
        this.maKHGui = maKHGui;
        this.buuCucPhat = buuCucPhat;
        this.timePCP = timePCP;
        this.timeGachBP = timeGachBP;
        this.ngayguiBP = ngayguiBP;
        this.danhGia = danhGia;
        this.loaiPG = loaiPG;
        this.lanPhat = lanPhat;
        this.tgConPhat = tgConPhat;
        this.tuyenBuuTaPhat = tuyenBuuTaPhat;
        this.tienCOD = tienCOD;
        this.khauFM = khauFM;
        this.khauMM = khauMM;
        this.khauLM = khauLM;
        this.tgQuyDinh = tgQuyDinh;
        this.tgTTLuyKe = tgTTLuyKe;
        this.tgChenhLech = tgChenhLech;
        this.dgMocMM = dgMocMM;
        this.nguongTon = nguongTon;
        this.loaiPCP = loaiPCP;
        this.loaiCanhBao = loaiCanhBao;
        this.dgMocLM = dgMocLM;
        this.loaiDichVu = loaiDichVu;
        this.version = version;
        this.timeTinhToan = timeTinhToan;
        this.trongLuong = trongLuong;
        this.tienCuoc = tienCuoc;
        this.loaiHangHoa = loaiHangHoa;
        this.khDacThuGui = khDacThuGui;
        this.khDacThuNhan = khDacThuNhan;
        this.idPhuongXaPhat = idPhuongXaPhat;
        this.tenPhuongXaPhat = tenPhuongXaPhat;
        this.maDichVuCongThem = maDichVuCongThem;
        this.tgTonPcpCuoiCung = tgTonPcpCuoiCung;
        this.timePcpDauTien = timePcpDauTien;
        this.loaiDon = loaiDon;
        this.objectHashMap = convert(this);
    }

//    public DashBuuCucTonPhatChiTietExcelDto(String maPhieuGui, String trangThai, String loaiCanhBao, String chiNhanhPhat, String buuCucPhat, String tuyenBuuTaPhat) throws NoSuchFieldException, IllegalAccessException {
//        this.maPhieuGui = maPhieuGui;
//        this.trangThai = trangThai;
//        this.loaiCanhBao = loaiCanhBao;
//        this.chiNhanhPhat = chiNhanhPhat;
//        this.buuCucPhat = buuCucPhat;
//        this.tuyenBuuTaPhat = tuyenBuuTaPhat;
//        this.objectHashMap = convert(this);
//    }


    public String getMaPhieuGui() {
        return maPhieuGui;
    }

    public void setMaPhieuGui(String maPhieuGui) {
        this.maPhieuGui = maPhieuGui;
    }

    public String getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(String ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public String getTinhNhan() {
        return tinhNhan;
    }

    public void setTinhNhan(String tinhNhan) {
        this.tinhNhan = tinhNhan;
    }

    public String getHuyenNhan() {
        return huyenNhan;
    }

    public void setHuyenNhan(String huyenNhan) {
        this.huyenNhan = huyenNhan;
    }

    public String getTenHuyenNhan() {
        return tenHuyenNhan;
    }

    public void setTenHuyenNhan(String tenHuyenNhan) {
        this.tenHuyenNhan = tenHuyenNhan;
    }

    public String getTtktFrom() {
        return ttktFrom;
    }

    public void setTtktFrom(String ttktFrom) {
        this.ttktFrom = ttktFrom;
    }

    public String getChiNhanhPhat() {
        return chiNhanhPhat;
    }

    public void setChiNhanhPhat(String chiNhanhPhat) {
        this.chiNhanhPhat = chiNhanhPhat;
    }

    public String getHuyenPhat() {
        return huyenPhat;
    }

    public void setHuyenPhat(String huyenPhat) {
        this.huyenPhat = huyenPhat;
    }

    public String getTenHuyenPhat() {
        return tenHuyenPhat;
    }

    public void setTenHuyenPhat(String tenHuyenPhat) {
        this.tenHuyenPhat = tenHuyenPhat;
    }

    public String getTtktTo() {
        return ttktTo;
    }

    public void setTtktTo(String ttktTo) {
        this.ttktTo = ttktTo;
    }

    public String getMaDvViettel() {
        return maDvViettel;
    }

    public void setMaDvViettel(String maDvViettel) {
        this.maDvViettel = maDvViettel;
    }

    public String getMaBuuCucGoc() {
        return maBuuCucGoc;
    }

    public void setMaBuuCucGoc(String maBuuCucGoc) {
        this.maBuuCucGoc = maBuuCucGoc;
    }

    public String getTimeTacDong() {
        return timeTacDong;
    }

    public void setTimeTacDong(String timeTacDong) {
        this.timeTacDong = timeTacDong;
    }

    public String getTrangThai() {
        return trangThai;
    }

    public void setTrangThai(String trangThai) {
        this.trangThai = trangThai;
    }

    public String getMaBuuCucHT() {
        return maBuuCucHT;
    }

    public void setMaBuuCucHT(String maBuuCucHT) {
        this.maBuuCucHT = maBuuCucHT;
    }

    public String getChiNhanhHT() {
        return chiNhanhHT;
    }

    public void setChiNhanhHT(String chiNhanhHT) {
        this.chiNhanhHT = chiNhanhHT;
    }

    public String getMaDoiTac() {
        return maDoiTac;
    }

    public void setMaDoiTac(String maDoiTac) {
        this.maDoiTac = maDoiTac;
    }

    public String getMaKHGui() {
        return maKHGui;
    }

    public void setMaKHGui(String maKHGui) {
        this.maKHGui = maKHGui;
    }

    public String getBuuCucPhat() {
        return buuCucPhat;
    }

    public void setBuuCucPhat(String buuCucPhat) {
        this.buuCucPhat = buuCucPhat;
    }

    public String getTimePCP() {
        return timePCP;
    }

    public void setTimePCP(String timePCP) {
        this.timePCP = timePCP;
    }

    public String getTimeGachBP() {
        return timeGachBP;
    }

    public void setTimeGachBP(String timeGachBP) {
        this.timeGachBP = timeGachBP;
    }

    public String getNgayguiBP() {
        return ngayguiBP;
    }

    public void setNgayguiBP(String ngayguiBP) {
        this.ngayguiBP = ngayguiBP;
    }

    public String getDanhGia() {
        return danhGia;
    }

    public void setDanhGia(String danhGia) {
        this.danhGia = danhGia;
    }

    public String getLoaiPG() {
        return loaiPG;
    }

    public void setLoaiPG(String loaiPG) {
        this.loaiPG = loaiPG;
    }

    public String getLanPhat() {
        return lanPhat;
    }

    public void setLanPhat(String lanPhat) {
        this.lanPhat = lanPhat;
    }

    public String getTgConPhat() {
        return tgConPhat;
    }

    public void setTgConPhat(String tgConPhat) {
        this.tgConPhat = tgConPhat;
    }

    public String getTuyenBuuTaPhat() {
        return tuyenBuuTaPhat;
    }

    public void setTuyenBuuTaPhat(String tuyenBuuTaPhat) {
        this.tuyenBuuTaPhat = tuyenBuuTaPhat;
    }

    public String getTienCOD() {
        return tienCOD;
    }

    public void setTienCOD(String tienCOD) {
        this.tienCOD = tienCOD;
    }

    public String getKhauFM() {
        return khauFM;
    }

    public void setKhauFM(String khauFM) {
        this.khauFM = khauFM;
    }

    public String getKhauMM() {
        return khauMM;
    }

    public void setKhauMM(String khauMM) {
        this.khauMM = khauMM;
    }

    public String getKhauLM() {
        return khauLM;
    }

    public void setKhauLM(String khauLM) {
        this.khauLM = khauLM;
    }

    public String getTgQuyDinh() {
        return tgQuyDinh;
    }

    public void setTgQuyDinh(String tgQuyDinh) {
        this.tgQuyDinh = tgQuyDinh;
    }

    public String getTgTTLuyKe() {
        return tgTTLuyKe;
    }

    public void setTgTTLuyKe(String tgTTLuyKe) {
        this.tgTTLuyKe = tgTTLuyKe;
    }

    public String getTgChenhLech() {
        return tgChenhLech;
    }

    public void setTgChenhLech(String tgChenhLech) {
        this.tgChenhLech = tgChenhLech;
    }

    public String getDgMocMM() {
        return dgMocMM;
    }

    public void setDgMocMM(String dgMocMM) {
        this.dgMocMM = dgMocMM;
    }

    public String getNguongTon() {
        return nguongTon;
    }

    public void setNguongTon(String nguongTon) {
        this.nguongTon = nguongTon;
    }

    public String getLoaiPCP() {
        return loaiPCP;
    }

    public void setLoaiPCP(String loaiPCP) {
        this.loaiPCP = loaiPCP;
    }

    public String getLoaiCanhBao() {
        return loaiCanhBao;
    }

    public void setLoaiCanhBao(String loaiCanhBao) {
        this.loaiCanhBao = loaiCanhBao;
    }

    public String getDgMocLM() {
        return dgMocLM;
    }

    public void setDgMocLM(String dgMocLM) {
        this.dgMocLM = dgMocLM;
    }

    public String getLoaiDichVu() {
        return loaiDichVu;
    }

    public void setLoaiDichVu(String loaiDichVu) {
        this.loaiDichVu = loaiDichVu;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getTimeTinhToan() {
        return timeTinhToan;
    }

    public void setTimeTinhToan(String timeTinhToan) {
        this.timeTinhToan = timeTinhToan;
    }

    public String getTrongLuong() {
        return trongLuong;
    }

    public void setTrongLuong(String trongLuong) {
        this.trongLuong = trongLuong;
    }

    public String getTienCuoc() {
        return tienCuoc;
    }

    public void setTienCuoc(String tienCuoc) {
        this.tienCuoc = tienCuoc;
    }

    public String getLoaiHangHoa() {
        return loaiHangHoa;
    }

    public void setLoaiHangHoa(String loaiHangHoa) {
        this.loaiHangHoa = loaiHangHoa;
    }

}
