package nocsystem.indexmanager.models.DashBuuCuc;

import nocsystem.indexmanager.config.ListContentPageDto;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Objects;

public class ListContentPageCustomDto<T, D>{
    private long total = 0;
    private long offset = 0;
    private int limit = 0;
    private String time;
    private D tong;
    private List<T> content;

    public ListContentPageCustomDto(Page<?> page, List<T> content) {
        this.content = content;
        this.offset = page.getPageable().getOffset() + 1; // by default page's offset is 0
        this.total = page.getTotalElements();
        this.limit = page.getSize();
    }

    public ListContentPageCustomDto(Page<?> page) {
        this.content = (List<T>) page.getContent();
        this.offset = page.getPageable().getOffset() + 1; // by default page's offset is 0
        this.total = page.getTotalElements();
        this.limit = page.getSize();
    }

    public ListContentPageCustomDto() {
    }
    public static final ListContentPageCustomDto EMPTY() {
        return new ListContentPageCustomDto();
    }
    public ListContentPageCustomDto(long total, long offset, int limit, List<T> content, D tong) {
        this.total = total;
        this.offset = offset;
        this.limit = limit;
        this.content = content;
        this.tong = tong;
    }

    public long getTotal() {
        return total;
    }

    public long getOffset() {
        return offset;
    }

    public int getLimit() {
        return limit;
    }

    public List<T> getContent() {
        return content;
    }

    public D getTong() {
        return tong;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getTime() {
        return time;
    }
}
