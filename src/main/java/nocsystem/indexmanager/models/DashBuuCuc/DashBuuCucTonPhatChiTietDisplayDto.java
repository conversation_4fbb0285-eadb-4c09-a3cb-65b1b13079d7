package nocsystem.indexmanager.models.DashBuuCuc;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@NoArgsConstructor
@Getter
@Setter
public class DashBuuCucTonPhatChiTietDisplayDto {
    String maPhieuGui;
    String trangThai;
    String loaiCanhBao;
    String chiNhanhPhat;
    String vungCon;
    String buuCucPhat;
    String tuyenBuuTaPhat;
    String loaiDichVu;
    String nguongTon;
    String tgPcp;

    public DashBuuCucTonPhatChiTietDisplayDto(String maPhieuGui, String trangThai, String loaiCanhBao, String chiNhanhPhat, String vungCon, String buuCucPhat, String tuyenBuuTaPhat) {
        this.maPhieuGui = maPhieuGui;
        this.trangThai = trangThai;
        this.loaiCanhBao = loaiCanhBao;
        this.chiNhanhPhat = chiNhanhPhat;
        this.buuCucPhat = buuCucPhat;
        this.vungCon = vungCon;
        this.tuyenBuuTaPhat = tuyenBuuTaPhat;
    }

    public DashBuuCucTonPhatChiTietDisplayDto(String maPhieuGui, String trangThai, String loaiCanhBao, String chiNhanhPhat, String vungCon, String buuCucPhat, String tuyenBuuTaPhat, String loaiDichVu, String nguongTon, String tgPcp) {
        this.maPhieuGui = maPhieuGui;
        this.trangThai = trangThai;
        this.loaiCanhBao = loaiCanhBao;
        this.chiNhanhPhat = chiNhanhPhat;
        this.buuCucPhat = buuCucPhat;
        this.tuyenBuuTaPhat = tuyenBuuTaPhat;
        this.loaiDichVu = loaiDichVu;
        this.nguongTon = nguongTon;
        this.vungCon = vungCon;
        this.tgPcp = tgPcp;
    }
}
