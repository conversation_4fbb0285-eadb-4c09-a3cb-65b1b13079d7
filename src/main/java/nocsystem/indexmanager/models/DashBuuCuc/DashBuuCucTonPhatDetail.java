package nocsystem.indexmanager.models.DashBuuCuc;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
@AllArgsConstructor
@Entity
@Getter
@Setter
@Table(name = "tonphat_chitiet_v2")
public class DashBuuCucTonPhatDetail  {
    @Id
    @Column(name = "ma_phieugui")
    private String maPhieuGui;

    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "vung_con")
    private String vungCon;

    @Column(name = "huyen_nhan")
    private String huyenNhan;

    @Column(name = "ten_huyen_nhan")
    private String tenHuyenNhan;

    @Column(name = "ttkt_from")
    private String ttktFrom;

    @Column(name = "tinh_phat")
    private String chiNhanhPhat;

    @Column(name = "huyen_phat")
    private String huyenPhat;

    @Column(name = "ten_huyen_phat")
    private String tenHuyenPhat;

    @Column(name = "ttkt_to")
    private String ttktTo;

    @Column(name = "ma_dv_viettel")
    private String maDvViettel;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "time_tac_dong")
    private String timeTacDong;

    @Column(name = "trang_thai")
    private String trangThai;

    @Column(name = "ma_buucuc_ht")
    private String maBuuCucHT;

    @Column(name = "chi_nhanh_ht")
    private String chiNhanhHT;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "ma_khgui")
    private String maKHGui;

    @Column(name = "ma_buucuc_phat")
    private String buuCucPhat;

    @Column(name = "time_pcp")
    private String timePCP;

    @Column(name = "time_gach_bp")
    private String timeGachBP;

    @Column(name = "ngay_gui_bp")
    private String ngayguiBP;

    @Column(name = "danh_gia")
    private String danhGia;

    @Column(name = "loai_pg")
    private String loaiPG;

    @Column(name = "lan_phat")
    private String lanPhat;

    @Column(name = "tg_con_phat")
    private String tgConPhat;

    @Column(name = "buu_ta_phat")
    private String tuyenBuuTaPhat;

    @Column(name = "tien_cod")
    private String tienCOD;

    @Column(name = "khau_fm")
    private String khauFM;

    @Column(name = "khau_mm")
    private String khauMM;

    @Column(name = "khau_lm")
    private String khauLM;

    @Column(name = "tg_quydinh")
    private String tgQuyDinh;

    @Column(name = "tg_tt_luyke")
    private String tgTTLuyKe;

    @Column(name = "tg_chenhlech")
    private String tgChenhLech;

    @Column(name = "dg_moc_mm")
    private String dgMocMM;

    @Column(name = "is_checked")
    private String nguongTon;

    @Column(name = "loai_pcp")
    private String loaiPCP;

    @Column(name = "loai_canh_bao")
    private String loaiCanhBao;

    @Column(name = "dg_moc_lm")
    private String dgMocLM;

    @Column(name = "nhom_dv")
    private String loaiDichVu;

    @Column(name = "version")
    private Long version;

    @Column(name = "time_tinh_toan")
    private String timeTinhToan;

    @Column(name = "kh_dacthu_gui")
    private String khDacThuGui;

    @Column(name = "kh_dacthu_nhan")
    private String khDacThuNhan;


    // 2023-08-16 ngocnt add column for change request
    @Column(name = "trong_luong")
    private String trongLuong;
    @Column(name = "tien_cuoc")
    private String tienCuoc;
    @Column(name = "loai_hh")
    private String loaiHangHoa;
    @Column(name = "id_phuongxa_phat")
    private String idPhuongXaPhat;
    @Column(name = "ten_phuongxa_phat")
    private String tenPhuongXaPhat;

    @Column(name = "ma_dv_cong_them")
    private String maDichVuCongThem;

    @Column(name = "tg_ton_pcp_cuoi_cung")
    private String tgTonPcpCuoiCung;

    @Column(name = "time_pcp_dau_tien")
    private String timePcpDauTien;

    @Column(name = "loai_don")
    private String loaiDon;

    public DashBuuCucTonPhatDetail(String maPhieuGui, LocalDate ngayBaoCao, String tinhNhan, String huyenNhan, String tenHuyenNhan, String ttktFrom, String chiNhanhPhat, String huyenPhat, String tenHuyenPhat, String ttktTo, String maDvViettel, String maBuuCucGoc, String timeTacDong, String trangThai, String maBuuCucHT) {
        this.maPhieuGui = maPhieuGui;
        this.ngayBaoCao = ngayBaoCao;
        this.tinhNhan = tinhNhan;
        this.huyenNhan = huyenNhan;
        this.tenHuyenNhan = tenHuyenNhan;
        this.ttktFrom = ttktFrom;
        this.chiNhanhPhat = chiNhanhPhat;
        this.huyenPhat = huyenPhat;
        this.tenHuyenPhat = tenHuyenPhat;
        this.ttktTo = ttktTo;
        this.maDvViettel = maDvViettel;
        this.maBuuCucGoc = maBuuCucGoc;
        this.timeTacDong = timeTacDong;
        this.trangThai = trangThai;
        this.maBuuCucHT = maBuuCucHT;
    }

    public DashBuuCucTonPhatDetail() {
    }
}
