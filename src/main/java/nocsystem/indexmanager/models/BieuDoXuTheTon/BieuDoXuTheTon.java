package nocsystem.indexmanager.models.BieuDoXuTheTon;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "bieu_do_xu_the_ton")
@IdClass(BieuDoXuTheTonKey.class)
public class BieuDoXuTheTon {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Id
    @Column(name = "ma_cn")
    private String maChiNhanh;

    @Id
    @Column(name = "ma_bc")
    private String maBuuCuc;

    @Id
    @Column(name = "loai_ton")
    private String loaiTon;

    @Column(name = "tong_sl")
    private Long tongSl;
}
