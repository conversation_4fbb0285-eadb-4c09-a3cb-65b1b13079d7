package nocsystem.indexmanager.models.TienDoDoanhThu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import nocsystem.indexmanager.models.Response.ChiSoKinhDoanh.CSKDTienDoDoanhThuResDto;
import nocsystem.indexmanager.models.Response.ChiSoKinhDoanh.CSKDTongDoanhThuResDto;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuCPDto.TienDoDoanhThuCPV1Dto;

import java.util.List;

@Data
public class FindCSKDTienDoDoanhThuDto {
    @JsonProperty("content")
    private List<TienDoDoanhThuCPV1Dto> tienDoDoanhThuCP;

    @JsonProperty("total")
    private CSKDTongDoanhThuResDto tongDoanhThu;
}
