package nocsystem.indexmanager.models.TienDoDoanhThu;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
@Table(name = "cskd_tiendodoanhthu_bc")
public class TienDoDoanhThuBuuCuc {
    @Id
    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "ma_chinhanh")
    private String maChiNhanh;

    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "nhom_doanhthu")
    private String nhomDoanhThu;

    @Column(name = "thuc_hien")
    private Float thucHien;

    @Column(name = "ke_hoach")
    private Float keHoach;

    @Column(name = "tlht")
    private Float tlht;

    @Column(name = "tiendo")
    private Float tienDo;

    @Column(name = "tt_thang")
    private Float ttThang;

    @Column(name = "tt_tbn_thang")
    private Float ttTbnThang;

    @Column(name = "tt_nam")
    private Float ttNam;

    @Column(name = "tt_tbn_nam")
    private Float ttTbnNam;

    @Column(name = "thangtruoc")
    private Float thangTruoc;

    @Column(name = "namtruoc")
    private Float namTruoc;

    @Column(name = "cung_ky_ngay")
    private Float cungKyNgay;

    @Column(name = "cung_ky_thang")
    private Float cungKyThang;

    @Column(name = "cung_ky_nam")
    private Float cungKyNam;

    /*Giá trị thực hiện ngày*/
    @Column(name = "thngay")
    private Float thucHienNgay = Float.valueOf(0);

    /*Giá trị thực hiện ngày trước đó có thể là ngày hôm trước (N - 1), ngày (N - 1) của tháng trước đó (T - 1)*/
    @Column(name = "thngay_mm1")
    private Float thucHienNgayTruocDo = Float.valueOf(0);

    @Column(name = "ngay_quydoi_thang")
    private Float ngayQuyDoiThang = Float.valueOf(0);

    @Column(name = "tl_tt_ngay")
    private Float tiLeTangTruongNgay = Float.valueOf(0);

    @Column(name = "hs_ngay")
    private Float hsNgay = Float.valueOf(0);

    public TienDoDoanhThuBuuCuc() {
    }
}
