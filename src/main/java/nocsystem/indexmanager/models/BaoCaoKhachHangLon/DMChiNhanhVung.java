package nocsystem.indexmanager.models.BaoCaoKhachHangLon;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

/**
 * Table dm_chinhanh_vung.
 */
@Entity
@Table(name = "dm_chinhanh_vung")
public class DMChiNhanhVung implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;
    @Column(name = "ma_vung")
    private String maVung;

    @Column(name = "ten_vung")
    private String tenVung;
    @Column(name = "dept_code")
    private String deptCode;
    @Column(name = "create_time")
    private String createTime;
    @Column(name = "update_time")
    private String updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "update_by")
    private String updateBy;
    @ManyToOne
    @JoinColumn(name = "cn_id", referencedColumnName = "id")
    private DMChiNhanh dmChiNhanh;

    public DMChiNhanhVung(DMChiNhanhVung dmChiNhanhVung) {
        this.maVung = dmChiNhanhVung.getMaVung();
        this.tenVung = dmChiNhanhVung.getTenVung();
        this.dmChiNhanh = dmChiNhanhVung.dmChiNhanh;
        this.createTime = dmChiNhanhVung.getCreateTime();
        this.updateTime = dmChiNhanhVung.getUpdateTime();
        this.createBy = dmChiNhanhVung.getCreateBy();
        this.updateBy = dmChiNhanhVung.getUpdateBy();
    }

    public DMChiNhanhVung() {

    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getMaVung() {
        return maVung;
    }

    public void setMaVung(String maVung) {
        this.maVung = maVung;
    }

    public String getTenVung() {
        return tenVung;
    }

    public void setTenVung(String tenVung) {
        this.tenVung = tenVung;
    }

//    public String getPlToChuc() {
//        return plToChuc;
//    }
//
//    public void setPlToChuc(String plToChuc) {
//        this.plToChuc = plToChuc;
//    }

    public DMChiNhanh getDmChiNhanh() {
        return dmChiNhanh;
    }

    public void setDmChiNhanh(DMChiNhanh dmChiNhanh) {
        this.dmChiNhanh = dmChiNhanh;
    }
}
