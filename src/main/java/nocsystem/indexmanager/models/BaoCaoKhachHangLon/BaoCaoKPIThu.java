package nocsystem.indexmanager.models.BaoCaoKhachHangLon;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "bao_cao_khl_thu_ngay")
public class BaoCaoKPIThu {

    @Id
    @Column(name = "thoi_gian")
    private LocalDate thoiGian;

    @Column(name = "vung")
    private String vung;

    @Column(name = "chi_nhanh")
    private String chiNhanh;

    @Column(name = "buu_cuc")
    private String buuCuc;

    @Column(name = "dich_vu")
    private String dichVu;

    @Column(name = "loai_don")
    private String loaiDon;

    @Column(name = "doi_tac")
    private String doiTac;

    @Column(name = "sl")
    private Long sL;

    @Column(name = "sl_thu_tc")
    private Long slThuTC;

    @Column(name = "sl_thu_dg")
    private Long slThuDG;

    @Column(name = "sl_thu_dg_1")
    private Long slThuDG1;

    @Column(name = "tl_thu_tc")
    private Float tlThuTC;

    @Column(name = "tl_thu_dg")
    private Float tlThuDG;

    @Column(name = "tl_thu_dg_1")
    private Float tlThuDG1;

    @Column(name = "huy")
    private Long huy;

    @Column(name = "xanh")
    private Long xanh;

    @Column(name = "vang")
    private Long vang;

    @Column(name = "do_1")
    private Long do1;

    @Column(name = "do_2")
    private Long do2;

    @Column(name = "do_3")
    private Long do3;

    @Column(name = "tong_ton")
    private Long tongTon;

    @Column(name = "tuyen_buuta")
    private String tuyenBuuTa;

    @Column(name = "status")
    private Integer status;

    @Column(name = "tong_ton_sla")
    private Long tongTonSLA;

    @Column(name = "giai_trinh")
    private Long giaiTrinh;

    @Column(name = "chua_giai_trinh")
    private Long chuaGiaiTrinh;

    @Column(name = "qua_han")
    private Long quaHanSLA;

    @Column(name = "chua_qua_han")
    private Long chuaQuaHanSLA;

    @Column(name = "update_time")
    private String updatedAt;

    @Column(name = "tong_sl_layhang_dunghen")
    private Long tongSlLayHangDungHen;

    @Column(name = "tong_sl_layhang_tc_dunghen")
    private Long tongSlLayHangTcDungHen;

    @Column(name = "tong_sl_trong_ky")
    private Long tongSlTrongKy;

    @Column(name = "vung_con")
    private String vungCon;
}
