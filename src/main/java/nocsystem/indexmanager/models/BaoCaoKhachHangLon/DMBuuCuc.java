package nocsystem.indexmanager.models.BaoCaoKhachHangLon;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Set;

/**
 * Table dm_buucuc_new
 */
@Entity
@Table(name = "dm_buucuc_new")
//@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class DMBuuCuc implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;
    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "ten_buucuc")
    private String tenBuuCuc;

    @Column(name = "dia_chi")
    private String diaChi;
    @Column(name = "dept_code")
    private String deptCode;
    @Column(name = "ma_cn")
    private String maCn;
    @Column(name = "ma_vung_con")
    private String maVung;

    @Column(name = "create_time")
    private String createTime;
    @Column(name = "update_time")
    private String updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "update_by")
    private String updateBy;
    @Column(name = "is_active")
    private Integer isActive;
    @ManyToOne
    @JoinColumn(name = "cn_id", referencedColumnName = "id")
    private DMChiNhanh dmChiNhanh;
    @ManyToOne
    @JoinColumn(name = "cn_vung_id", referencedColumnName = "id")
    private DMChiNhanhVung dmChiNhanhVung;

    @Column(name = "is_chtt")
    private Integer isCHTT;

    public DMBuuCuc() {
    }

    public DMBuuCuc(DMBuuCuc dmBuuCuc) {
        this.maBuuCuc = dmBuuCuc.getMaBuuCuc();
        this.tenBuuCuc = dmBuuCuc.getTenBuuCuc();
        this.diaChi = dmBuuCuc.getDiaChi();
        this.dmChiNhanh = dmBuuCuc.dmChiNhanh;
        this.maCn = dmBuuCuc.getMaCn();
        this.maVung = dmBuuCuc.getMaVung();
        this.isActive = dmBuuCuc.getIsActive();
        this.dmChiNhanhVung = dmBuuCuc.dmChiNhanhVung;
        this.createTime = dmBuuCuc.getCreateTime();
        this.updateTime = dmBuuCuc.getUpdateTime();
        this.createBy = dmBuuCuc.getCreateBy();
        this.updateBy = dmBuuCuc.getUpdateBy();
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getIsActive() {
        return isActive;
    }

    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }

    public String getMaCn() {
        return maCn;
    }

    public void setMaCn(String maCn) {
        this.maCn = maCn;
    }

    public String getMaVung() {
        return maVung;
    }

    public void setMaVung(String maVung) {
        this.maVung = maVung;
    }

    public DMBuuCuc(Set<DMBuuCuc> dmChiNhanhs) {
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }


    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public String getTenBuuCuc() {
        return tenBuuCuc;
    }

    public void setTenBuuCuc(String tenBuuCuc) {
        this.tenBuuCuc = tenBuuCuc;
    }

    public String getDiaChi() {
        return diaChi;
    }

    public void setDiaChi(String diaChi) {
        this.diaChi = diaChi;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public DMChiNhanh getDmChiNhanh() {
        return dmChiNhanh;
    }

    public void setDmChiNhanh(DMChiNhanh dmChiNhanh) {
        this.dmChiNhanh = dmChiNhanh;
    }

    public DMChiNhanhVung getDmChiNhanhVung() {
        return dmChiNhanhVung;
    }

    public void setDmChiNhanhVung(DMChiNhanhVung dmChiNhanhVung) {
        this.dmChiNhanhVung = dmChiNhanhVung;
    }
}
