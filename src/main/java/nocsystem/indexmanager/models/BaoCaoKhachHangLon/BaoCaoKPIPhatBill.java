package nocsystem.indexmanager.models.BaoCaoKhachHangLon;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
//@Table(name = "noc_bcvh_kpi_phat_bill")
@Table(name = "bcvh_khl_chitiet")
public class BaoCaoKPIPhatBill {

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "ma_phieugui")
    private String maPhieuGui;

    @Column(name = "vung_phat")
    private String vungPhat;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "huyen_nhan")
    private String huyenNhan;

    @Column(name = "huyen_phat")
    private String huyenPhat;

    @Column(name = "ma_xanhan")
    private String maXaNhan;

    @Column(name = "ma_xaphat")
    private String maXaPhat;

    @Column(name = "ma_dv_viettel")
    private String maDVViettel;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "ma_buucuc_phat")
    private String maBuuCucPhat;

    @Column(name = "time_yeucau_layhang")
    private Date timeYeuCauLayHang;

    @Column(name = "ngay_gui_bp")
    private Date ngayGuiBP;

    @Column(name = "ma_trangthai")
    private String maTrangThai;

    @Column(name = "ma_buucuc_ht")
    private String maBuuCucHT;

    @Column(name = "ma_chinhanh_ht")
    private String maChiNhanhHT;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "ma_khgui")
    private String maKHGui;

    @Column(name = "ma_dichvu_doitac")
    private String maDichVuDoiTac;

    @Column(name = "tg_quydinhphat")
    private Date tgQuyDinhPhat;

    @Column(name = "tg_quydinh")
    private Float tgQuyDinh;

    @Column(name = "tg_chenhlechphat")
    private String tgChenhLechPhat;

    @Column(name = "tg_chenhlech")
    private String tgChenhLech;

    @Column(name = "time_pcp")
    private Date timePCP;

    @Column(name = "time_gach_bp")
    private Date timeGachBP;

    @Column(name = "time_gach_bp2")
    private Date timeGachBP2;

    @Column(name = "time_gach_bp3")
    private Date timeGachBP3;

    @Column(name = "ly_do_tt")
    private String lyDoTT;

    @Column(name = "ly_do_tt_2")
    private String lyDoTT2;

    @Column(name = "ly_do_tt_3")
    private String lyDoTT3;

    @Column(name = "tien_cod")
    private Float tienCOD;

    @Column(name = "tienhang")
    private Float tienhang;

    @Column(name = "trong_luong")
    private Float trongLuong;

    @Column(name = "tong_cuoc")
    private Float tongCuoc;

    @Column(name = "loai_canhbao")
    private String canhBao;

    @Column(name = "tuyen_buuta")
    private String tuyenBuuTa;

    @Column(name = "so_lan_giao")
    private Integer soLanGiao;

    @Column(name = "status")
    private Integer status;

    @Column(name = "phai_phat")
    private Integer phaiPhat;

    @Column(name = "ton")
    private Integer ton;

    @Column(name = "phat")
    private Integer phat;

    @Column(name = "tg_ptc")
    private Date tgPTC;

    @Column(name = "danhgia_time_gach_bp1")
    private String dgTimeGachBP1;

    @Column(name = "danhgia_time_gach_bp2")
    private String dgTimeGachBP2;

    @Column(name = "danhgia_time_gach_bp3")
    private String dgTimeGachBP3;

    @Column(name = "ma_buucuc_phat_thucte")
    private String maBuuCucPhatThucTe;

    @Column(name = "tg_nhantai")
    private Date tgNhanTai;

    @Column(name = "vung_con")
    private String vungCon;

//    @Column(name = "chieu")
//    private String chieu;
//
//    @Column(name = "tg_502")
//    private Date tg502;

}
