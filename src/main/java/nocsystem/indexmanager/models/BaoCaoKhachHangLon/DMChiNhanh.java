package nocsystem.indexmanager.models.BaoCaoKhachHangLon;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

/**
 * Table dm_chinhanh_new.
 */
@Entity
@Table(name = "dm_chinhanh_new")
public class DMChiNhanh implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;
    @Column(name = "ma_cn")
    private String maCn;
    @Column(name = "ten_cn")
    private String tenCn;
    @Column(name = "dept_code")
    private String deptCode;
    @Column(name = "create_time")
    private String createTime;
    @Column(name = "update_time")
    private String updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "update_by")
    private String updateBy;
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "vung_mien_id", referencedColumnName = "id")
    private DMVungMien dmVungMien;

    public DMChiNhanh(DMChiNhanh dmChiNhanh) {
        this.maCn = dmChiNhanh.getMaCn();
        this.tenCn = dmChiNhanh.getTenCn();
        this.deptCode = dmChiNhanh.getDeptCode();
        this.dmVungMien = dmChiNhanh.dmVungMien;
        this.createTime = dmChiNhanh.getCreateTime();
        this.updateTime = dmChiNhanh.getUpdateTime();
        this.createBy = dmChiNhanh.getCreateBy();
        this.updateBy = dmChiNhanh.getUpdateBy();
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public DMChiNhanh(Set<DMChiNhanh> dmChiNhanhs) {
    }

    public DMChiNhanh() {

    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMaCn() {
        return maCn;
    }

    public void setMaCn(String maCn) {
        this.maCn = maCn;
    }

    public String getTenCn() {
        return tenCn;
    }

    public void setTenCn(String tenCn) {
        this.tenCn = tenCn;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public DMVungMien getDmVungMien() {
        return dmVungMien;
    }

    public void setDmVungMien(DMVungMien dmVungMien) {
        this.dmVungMien = dmVungMien;
    }

    public DMChiNhanh dmVungMien(DMVungMien dmVungMien) {
        this.setDmVungMien(dmVungMien);
        return this;
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
