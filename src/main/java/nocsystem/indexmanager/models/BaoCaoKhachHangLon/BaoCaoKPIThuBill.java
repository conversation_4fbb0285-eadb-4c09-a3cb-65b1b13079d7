package nocsystem.indexmanager.models.BaoCaoKhachHangLon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
@Table(name = "bao_cao_khl_chi_tiet_ton")
public class BaoCaoKPIThuBill {

    //    @Id
//    @Column(name = "ngay_baocao")
//    private LocalDate ngayBaoCao;
    @Id
    @Column(name = "ma_phieugui")
    private String maPhieuGui;


    @Column(name = "thoi_gian")
    private LocalDate thoiGian;

    @Column(name = "vung")
    private String vung;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "huyen_nhan")
    private String huyenNhan;

    @Column(name = "huyen_phat")
    private String huyenPhat;

    @Column(name = "ma_xanhan")
    private String maXaNhan;

    @Column(name = "ma_xaphat")
    private String maXaPhat;

    @Column(name = "ma_dv_viettel")
    private String maDVViettel;

    @Column(name = "ma_buucuc_goc")
    private String maBuuCucGoc;

    @Column(name = "ma_buucuc_phat")
    private String maBuuCucPhat;

    @Column(name = "time_yeucau_layhang")
    private Date timeYeuCauLayHang;

    @Column(name = "thoigiantaodon")
    private Date thoiGianTaoDon;

    @Column(name = "time_thulan1")
    private Date timeThuLan1;

    @Column(name = "time_thulan2")
    private Date timeThuLan2;

    @Column(name = "time_thulan3")
    private Date timeThuLan3;

    @Column(name = "time_nhap_may")
    private Date timeNhapMay;

    @Column(name = "ma_trangthai")
    private String maTrangThai;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "ma_khgui")
    private String maKHGui;

    @Column(name = "ma_dichvu_doitac")
    private String maDichVuDoiTac;

    @Column(name = "tien_cod")
    private Integer tienCOD;

    @Column(name = "tienhang")
    private Integer tienhang;

    @Column(name = "trong_luong")
    private Float trongLuong;

    @Column(name = "tong_cuoc")
    private Integer tongCuoc;

    @Column(name = "canh_bao")
    private String canhBao;

    @Column(name = "status")
    private Integer status;

    @Column(name = "ly_do_tt1")
    private String lyDoTT1;

    @Column(name ="ly_do_tt2")
    private String lyDoTT2;

    @Column(name ="ly_do_tt3")
    private String lyDoTT3;

    @Column(name ="buu_ta_ntc")
    private String buuTaNTC;

    @Column(name ="buu_ta_td_1")
    private String buuTaTD1;

    @Column(name ="chenh_lech")
    private Float chenhLech;

    @Column(name ="danh_gia")
    private String danhGia;

    @Column(name = "time_kenoidi_tai_goc")
    private Date timeKetNoiDiTaiGoc;

    @Column(name ="loai_don")
    private String loaiDon;

    @Column(name ="ton")
    private int ton;

    @Column(name ="buu_ta_tonthu")
    private String buu_ta_tonthu;

    @Column(name = "time_ht")
    private Date time_ht;

    @Column(name ="vung_con")
    private String vungCon;
}
