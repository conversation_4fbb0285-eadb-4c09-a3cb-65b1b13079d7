package nocsystem.indexmanager.models.BaoCaoKhachHangLon;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDate;
@Data
@Getter
@Setter
@Entity
@Table(name = "chiso_tiktok")
public class ChisoTiktok {

    @Id
    @Column(name = "ngay_baocao", nullable = false)
    private LocalDate ngayBaocao;

    @Column(name = "ma_chiso")
    private String maChiso;

    @Column(name = "giatri_vtp")
    private Double giatriVtp;

    @Column(name = "giatri_tiktok")
    private Double giatriTiktok;

    @Column(name = "kpi_tiktok")
    private Double kpiTiktok;

    @Column(name = "ghi_chu")
    private String ghiChu;

    @Column(name = "updated_at")
    private Long updatedAt;

    @Column(name = "ma_doitac")
    private String maDoiTac;

    @Column(name = "giatri_doitac")
    private Double giatriDoiTac;
    @Column(name = "kpi_doitac")
    private Double kpiDoiTac;



}
