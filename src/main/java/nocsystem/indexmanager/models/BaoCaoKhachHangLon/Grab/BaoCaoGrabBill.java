package nocsystem.indexmanager.models.BaoCaoKhachHangLon.Grab;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
@Table(name = "bao_cao_grab_chi_tiet_don")
public class BaoCaoGrabBill {

    @Id
    @Column(name = "thoi_gian")
    private LocalDate ngayBaoCao;

    @Column(name = "ma_phieu_gui")
    private String maPhieuGui;

    @Column(name = "tinh_nhan")
    private String tinhNhan;

    @Column(name = "tinh_phat")
    private String tinhPhat;

    @Column(name = "huyen_nhan")
    private String huyenNhan;

    @Column(name = "huyen_phat")
    private String huyenPhat;

    @Column(name = "ma_dv")
    private String maDichVu;

    @Column(name = "ma_bc_goc")
    private String maBCGoc;

    @Column(name = "ma_bc_ht")
    private String maBCHt;

    @Column(name = "time_yc_lay_hang")
    private String timeYeuCauLayHang;

    @Column(name = "time_tao_don")
    private String timeTaoDon;

    @Column(name = "time_nhap_may")
    private String timeNhapMay;

    @Column(name = "time_quy_dinh_thu")
    private String timeQuyDinhThu;

    @Column(name = "ma_trang_thai")
    private String maTrangThai;

    @Column(name = "ma_doi_tac")
    private String maDoiTac;

    @Column(name = "ma_kh")
    private String maKH;

    @Column(name = "ma_dv_grab")
    private String maDvGrab;

    @Column(name = "loai_don")
    private String loaiDon;

    @Column(name = "trong_luong")
    private String trongLuong;

    @Column(name = "loai_vung")
    private String loaiVung;

    @Column(name = "status")
    private Integer status;

    @Column(name = "updated_at")
    private String updatedAt;

    @Column(name = "time_quy_dinh_phat")
    private String timeQuyDinhPhat;

    @Column(name = "time_thu_tc")
    private String timeThuTC;

    @Column(name = "time_phat_tc")
    private Date timePhatTC;

    @Column(name = "vung_phat")
    private String vungPhat;

    @Column(name = "vung_thu")
    private String vungThu;

    @Column(name = "time_phan_cong_phat")
    private Date timePhanCongPhat;

    @Column(name = "ma_van_don_grab")
    private String maVanDonGrab;

    @Column(name = "xa_nhan")
    private String xaNhan;

    @Column(name = "xa_phat")
    private String xaPhat;

    @Column(name = "so_km")
    private Float soKm;

    @Column(name = "request_time")
    private Float requestTime;

    @Column(name = "tong_cuoc")
    private Long tongCuoc;

    @Column(name = "tong_cod")
    private Long tongCOD;

    @Column(name = "ly_do_huy")
    private String lyDoHuy;

    @Column(name = "trang_thai_grab")
    private String trangThaiGrab;

    @Column(name = "ten_trang_thai_vtp")
    private String trangThaiVTP;

    @Column(name = "chi_nhanh_nhan")
    private String chiNhanhNhan;

    @Column(name = "chi_nhanh_phat")
    private String chiNhanhPhat;

    @Column(name = "ma_bc_phat")
    private String maBuuCucPhat;

    @Column(name = "time_hoan_huy")
    private Date timeHoanHuy;
}
