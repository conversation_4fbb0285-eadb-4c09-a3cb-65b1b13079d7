package nocsystem.indexmanager.models.BaoCaoKhachHangLon.Grab;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "bao_cao_grab_tong_hop")
public class BaoCaoGrab {

    @Id
    @Column(name = "thoi_gian")
    private LocalDate ngayBaoCao;

    @Column(name = "vung")
    private String vung;

    @Column(name = "chi_nhanh")
    private String chiNhanh;

    @Column(name = "buu_cuc")
    private String buuCuc;

    @Column(name = "buu_ta")
    private String buuTa;

    @Column(name = "ma_doi_tac")
    private String maDoiTac;

    @Column(name = "ma_dv")
    private String maDichVu;

    @Column(name = "loai_don")
    private String loaiDon;

    @Column(name = "sl_ps")
    private Long slPhatSinh;

    @Column(name = "sl_thu_tc")
    private Long slThuTC;

    @Column(name = "sl_thu_tc_dg")
    private Long slThuTCDungGio;

    @Column(name = "sl_thu_ton")
    private Long slThuTon;

    @Column(name = "sl_thu_huy")
    private Long slThuHuy;

    @Column(name = "sl_phai_Phat")
    private Long slPhaiPhat;

    @Column(name = "sl_phat_ton")
    private Long slPhatTon;

    @Column(name = "sl_phat_tc")
    private Long slPhatTC;

    @Column(name = "sl_phat_tc_dg")
    private Long slPhatTCDungGio;

    @Column(name = "sl_phat_hoan")
    private Long slPhatHoan;

    @Column(name = "sl_phat_huy")
    private Long slPhatHuy;

    @Column(name = "ton_phat_500")
    private Long tonPhat500;

    @Column(name = "ton_phat_khac_500")
    private Long tonPhatKhac500;

    @Column(name = "ton_thu_con_han")
    private Long tonThuConHan;

    @Column(name = "ton_thu_qua_han")
    private Long tonThuQuaHan;

    @Column(name = "status")
    private Integer status;

    @Column(name = "updated_at")
    private String updatedAt;

    @Column(name = "ton_nhan_bg")
    private Long tonNhanBG;

    @Column(name = "sl_dropoff")
    private Long  slDropOff;

}
