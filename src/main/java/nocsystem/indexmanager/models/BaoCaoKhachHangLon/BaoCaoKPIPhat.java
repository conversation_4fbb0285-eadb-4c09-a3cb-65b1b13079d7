package nocsystem.indexmanager.models.BaoCaoKhachHangLon;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@Entity
//@Table(name = "noc_bcvh_kpi_phat")
@Table(name = "bcvh_khl_tonghop")
public class BaoCaoKPIPhat {


    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Id
    @Column(name = "vung_phat")
    private String vungPhat;

    @Column(name = "cn_phat")
    private String chiNhanh;

    @Column(name = "ma_buucuc_phat")
    private String buuCuc;

    @Column(name = "ma_doitac")
    private String doiTac;

    @Column(name = "ma_dv_viettel")
    private String dichVu;

    @Column(name = "loai_baocao")
    private Integer loaiBaoCao;

    @Column(name = "tong_sanluong")
    private Long tongSanLuong;

    @Column(name = "sl_ptc_dunggio")
    private Long slPTCDungGio;

    @Column(name = "sl_ptc_dunggio_landau")
    private Long slPTCDungGioLanDau;

    @Column(name = "sl_phaiphat")
    private Long slPhaiPhat;

    @Column(name = "sl_chua_pcp")
    private Long slChuaPCP;

    @Column(name = "sl_hoan")
    private Long slHoan;

    @Column(name = "sl_huy")
    private Long slHuy;

    @Column(name = "sl_denbu")
    private Long slDenBu;

    @Column(name = "ton_500")
    private Long tt500;

    @Column(name = "ton_khac")
    private Long ttKhac;

    @Column(name = "sl_ptc")
    private Long slPTC;

    @Column(name = "tl_ptc")
    private Float tlPTC;

    @Column(name = "tl_ptc_dunggio")
    private Float tlPTCDungGio;

    @Column(name = "tl_ptc_dunggio_landau")
    private Float tlPTCDungGioLanDau;

    @Column(name = "ton_xanh")
    private Long tonXanh;

    @Column(name = "ton_vang")
    private Long tonVang;

    @Column(name = "ton_do_1")
    private Long tonDo1;

    @Column(name = "ton_do_2")
    private Long tonDo2;

    @Column(name = "ton_do_3")
    private Long tonDo3;

    @Column(name = "ton_do_4")
    private Long tonDo4;

    @Column(name = "ton_do_5")
    private Long tonDo5;

    @Column(name = "ton_do_6")
    private Long tonDo6;

    @Column(name = "ton_do_7")
    private Long tonDo7;

    @Column(name = "ton_do_8")
    private Long tonDo8;

    @Column(name = "ton_do_9")
    private Long tonDo9;

    @Column(name = "tong_ton")
    private Long tongTon;

    @Column(name = "tuyen_buuta")
    private String tuyenBuuTa;

    @Column(name = "status")
    private Integer status;

    @Column(name = "phai_phat_lk")
    private Long phaiPhatLK;

    @Column(name = "ton_khac_lk")
    private Long tonKhacLK;

    @Column(name = "updated_at")
    private String updatedAt;

    @Column(name = "tu_hoan")
    private Long tuHoan;

    @Column(name = "mau_hoan")
    private Long mauHoan;

    @Column(name = "tu_hoan_lk")
    private Long tuHoanLK;

    @Column(name = "mau_hoan_lk")
    private Long mauHoanLK;

    @Column(name = "tu_giaohang_dunghen")
    private Long tuGiaoHangDungHen;

    @Column(name = "mau_giaohang_dunghen")
    private Long mauGiaoHangDungHen;

    @Column(name = "tu_giao_thanhcong_landdau")
    private Long tuGiaoThanhCongLanDau;

    @Column(name = "mau_giao_thanhcong_landdau")
    private Long mauGiaoThanhCongLanDau;

    @Column(name = "tu_giao_thanhcong_landau_lk")
    private Long tuGiaoThanhCongLanDauLK;

    @Column(name = "mau_giao_thanhcong_landau_lk")
    private Long mauGiaoThanhCongLanDauLK;

    @Column(name = "vung_con")
    private String vungCon;

}
