package nocsystem.indexmanager.models.TienDoDoanhThuLogistic;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Date;

@Data
@Entity
@Table(name = "cskd_tiendodoanhthu_logistic_bc")
public class TienDoDoanhThuLogisticBuuCuc {
    /*
     * dichVu: 1-Forwarding, 2-Kho Vận
     */

    @Id
    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Column(name = "ma_chinhanh")
    private String maChiNhanh;

    @Column(name = "dich_vu")
    private String dichVu;

    @Column(name = "ke_hoach")
    private Float keHoach;

    @Column(name = "thuc_hien")
    private Float thucHien;

    @Column(name = "tlht")
    private Float tlht;

    @Column(name = "tiendo")
    private Float tienDo;

    @Column(name = "tt_thang")
    private Float ttThang;

    @Column(name = "tt_tbn_thang")
    private Float ttTbnThang;

    @Column(name = "tt_nam")
    private Float ttNam;

    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;

    @Column(name = "thangtruoc")
    private Float thangTruoc;

    @Column(name = "namtruoc")
    private Float namTruoc;

    @Column(name = "cung_ky_ngay")
    private Float cungKyNgay;

    @Column(name = "cung_ky_thang")
    private Float cungKyThang;

    @Column(name = "cung_ky_nam")
    private Float cungKyNam;

    @Column(name = "tl_tt_ngay")
    private Float tiLeTangTruongNgay = Float.valueOf(0);
}
