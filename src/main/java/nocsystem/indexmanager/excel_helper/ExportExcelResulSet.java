package nocsystem.indexmanager.excel_helper;

import com.github.andrewoma.dexx.collection.HashMap;
import nocsystem.indexmanager.dao.AbstractDao;
import nocsystem.indexmanager.util.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;

public class ExportExcelResulSet extends AbstractDao {

    private final Logger log = LoggerFactory.getLogger(ExportExcelResulSet.class);

    private SXSSFWorkbook workbook;
    private SXSSFSheet sheet;

    Connection conn;
    PreparedStatement stmt;
    ResultSet resultSets;
    Map<String, String> mapHeader1;
    List<String> headerRow1;
    List<String> headerRow2;
    String sheetName;
    public Boolean flag = false;
    public Integer numberRow;

    public List<ByteArrayOutputStream> outputStreamList;

    public ExportExcelResulSet() {
        workbook = new SXSSFWorkbook(16);
    }


    public ExportExcelResulSet(
            Connection conn,
            PreparedStatement stmt,
            ResultSet dataList,
            Map<String, String> mapHeader1,
            List<String> headerRow1,
            List<String> headerRow2,
            Integer numberRow,
            String sheetName

    ) {
        this.conn = conn;
        this.stmt = stmt;
        this.resultSets = dataList;
        this.mapHeader1 = mapHeader1;
        this.headerRow1 = headerRow1;
        this.headerRow2 = headerRow2;
        this.sheetName = sheetName;
        this.numberRow = numberRow;
        this.outputStreamList = new ArrayList<>();
        workbook = new SXSSFWorkbook(16);
    }

    private void createCell(SXSSFRow row, int columnCount, Object value, CellStyle style) {

        Cell cell = row.createCell(columnCount);

        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue((Long) value);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Float) {
            cell.setCellValue((Float) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof BigInteger) {
            cell.setCellValue(((BigInteger) value).longValue());
        } else {
            cell.setCellValue((LocalDate) value);
        }

        cell.setCellStyle(style);

    }


    private void createHeaderRowAll() {

        sheet = workbook.createSheet(sheetName);
        workbook.setCompressTempFiles(true);
        SXSSFRow row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        Map<String, Integer> mapHeader2 = new LinkedHashMap<>();
        for (String x : headerRow1) {
            if (!mapHeader2.containsKey(x)) {
                mapHeader2.put(x, 1);
            } else {
                mapHeader2.put(x, mapHeader2.get(x) + 1);
            }
        }

        int soCot = 0;
        for (String x : headerRow1) {
            createCell(row, soCot, x, style);
            soCot++;
        }
        SXSSFRow row1 = sheet.createRow(1);

        soCot = 0;
        for (String x : headerRow2) {
            createCell(row1, soCot, mapHeader1.get(x), style);
            sheet.setColumnWidth(soCot, ((int) (mapHeader1.get(x).length() * 1.5)) * 210);
            soCot++;
        }

        for (int j = 0; j < headerRow1.size(); j++) {
            if (mapHeader1.get(headerRow2.get(j)).equals(headerRow1.get(j))) {
                sheet.addMergedRegion(new CellRangeAddress(0, 1, j, j));
            }
        }

        for (int i = 0; i < headerRow1.size(); i++) {
            if (mapHeader2.get(headerRow1.get(i)) > 1) {
                sheet.addMergedRegion(new CellRangeAddress(0, 0, i, i + mapHeader2.get(headerRow1.get(i)) - 1));
                mapHeader2.put(headerRow1.get(i), 1);
            }
        }
    }


    private void createSingleHeaderRowAll() {

        sheet = workbook.createSheet(sheetName);
        workbook.setCompressTempFiles(true);
        SXSSFRow row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        Map<String, Integer> mapHeader2 = new LinkedHashMap<>();
        for (String x : headerRow1) {
            if (!mapHeader2.containsKey(x)) {
                mapHeader2.put(x, 1);
            } else {
                mapHeader2.put(x, mapHeader2.get(x) + 1);
            }
        }

        int soCot = 0;
        for (String x : headerRow1) {
            createCell(row, soCot, x, style);
            soCot++;
        }

        for (int i = 0; i < headerRow1.size(); i++) {
            if (mapHeader2.get(headerRow1.get(i)) > 1) {
                sheet.addMergedRegion(new CellRangeAddress(0, 0, i, i + mapHeader2.get(headerRow1.get(i)) - 1));
                mapHeader2.put(headerRow1.get(i), 1);
            }
        }
    }


    private void createSingleHeaderTonHanhTrinh() {

        sheet = workbook.createSheet(sheetName);
        workbook.setCompressTempFiles(true);
        SXSSFRow row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        Map<String, Integer> mapHeader2 = new LinkedHashMap<>();
        for (String x : headerRow1) {
            if(x.equals("UPDATED_AT")) continue;
            if (!mapHeader2.containsKey(x)) {
                mapHeader2.put(x, 1);
            } else {
                mapHeader2.put(x, mapHeader2.get(x) + 1);
            }
        }

        int soCot = 0;
        for (String x : headerRow1) {
            if(x.equals("UPDATED_AT")) continue;
            createCell(row, soCot, x, style);
            soCot++;
        }

        for (int i = 0; i < headerRow1.size(); i++) {
            if(headerRow1.get(i).equals("UPDATED_AT")) continue;

            if (mapHeader2.get(headerRow1.get(i)) > 1) {
                sheet.addMergedRegion(new CellRangeAddress(0, 0, i, i + mapHeader2.get(headerRow1.get(i)) - 1));
                mapHeader2.put(headerRow1.get(i), 1);
            }
        }
    }




    public void writeDataToStream(int limit) throws IOException, SQLException {


        int rownum = 2;
        int countColumn = 0;
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

//        resultSets.setFetchSize(1024);

        resultSets.setFetchSize(100);
        log.info(" chay den resultSets.setFetchSize(100) ");

        int numberColum = headerRow2.size();
        createHeaderRowAll();

        log.info(" createHeaderRowAll() done ");

        int count = 0;
        boolean flag = false;
        ByteArrayOutputStream tempOutputStream = null;
        boolean check = false;
        log.info(" bat dau den while (rs.next) ");
        while (resultSets.next()) {

            check = true;
            if (!flag) {
                tempOutputStream = new ByteArrayOutputStream(1024 * 1024);
                flag = true;
            }
            else {
                if(count >= limit) {
                    workbook.write(tempOutputStream);

                    workbook.close();
                    workbook.dispose();
                    System.gc();

                    outputStreamList.add(tempOutputStream);

                    this.workbook = null;

                    this.workbook = new SXSSFWorkbook(16);

                    createHeaderRowAll();
                    rownum = 2;
                    count = -1;
                    flag = false;
                }
            }
            SXSSFRow row = sheet.createRow(rownum);
            createCell(row, 0, ++countColumn, style);
            for (int i = 1; i < numberColum; i++) {
                Cell cell = row.createCell(i);
                cell.setCellValue(resultSets.getString(headerRow2.get(i)));
            }
            rownum++;
            count++;
        }
        log.info("   chay xong while rs.next  ");
        if(check){
            if(count <= limit && count > -1){
                workbook.write(tempOutputStream);
                outputStreamList.add(tempOutputStream);
            }
        }

        releaseConnect(conn, stmt, resultSets);
        workbook.close();
        workbook.dispose();
        System.gc();

        log.info("   writeDataToStream done   ");


    }


    public void writeDataToStream(int limit, String ngayBaoCao, Map<String, Boolean> dictValueConvert) throws IOException, SQLException {


        int rownum = 1;
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);


        resultSets.setFetchSize(100);
        log.info(" chay den resultSets.setFetchSize(100) ");

        int numberColum = headerRow2.size();
        createSingleHeaderRowAll();

        log.info(" createHeaderRowAll() done ");

        int count = 0;
        boolean flag = false;
        ByteArrayOutputStream tempOutputStream = null;
        boolean check = false;
        log.info(" bat dau den while (rs.next) ");
        while (resultSets.next()) {

            check = true;
            if (!flag) {
                tempOutputStream = new ByteArrayOutputStream(1024 * 1024);
                flag = true;
            }
            else {
                if(count >= limit) {
                    workbook.write(tempOutputStream);

                    workbook.close();
                    workbook.dispose();
                    System.gc();

                    outputStreamList.add(tempOutputStream);

                    this.workbook = null;

                    this.workbook = new SXSSFWorkbook(16);

                    createSingleHeaderRowAll();
                    rownum = 1;
                    count = -1;
                    flag = false;
                }
            }
            SXSSFRow row = sheet.createRow(rownum);
            Cell cell_ngayBC = row.createCell(0);
            cell_ngayBC.setCellValue(ngayBaoCao);
            for (int i = 1; i < numberColum; i++) {
                Cell cell = row.createCell(i);
                if(dictValueConvert.containsKey(headerRow2.get(i))){
                    cell.setCellValue(StringUtils.longTimestamp2DateString(resultSets.getString(headerRow2.get(i))));
                }
                else cell.setCellValue(resultSets.getString(headerRow2.get(i)));
            }
            rownum++;
            count++;
        }
        log.info("   chay xong while rs.next  ");
        if(check){
            if(count <= limit && count > -1){
                workbook.write(tempOutputStream);
                outputStreamList.add(tempOutputStream);
            }
        }

        releaseConnect(conn, stmt, resultSets);
        workbook.close();
        workbook.dispose();
        System.gc();

        log.info("   writeDataToStream done   ");


    }


    public void writeDataToStreamTonHanhTrinh(int limit, String ngayBaoCao, Map<String, Boolean> dictValueConvert) throws IOException, SQLException {


        int rownum = 1;
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);


        resultSets.setFetchSize(100);
        log.info(" chay den resultSets.setFetchSize(100) ");

        headerRow2.remove("UPDATED_AT");
        int numberColum = headerRow2.size();
        createSingleHeaderTonHanhTrinh();

        log.info(" createHeaderRowAll() done ");

        int count = 0;
        boolean flag = false;
        ByteArrayOutputStream tempOutputStream = null;
        boolean check = false;
        log.info(" bat dau den while (rs.next) ");
        while (resultSets.next()) {

            check = true;
            if (!flag) {
                tempOutputStream = new ByteArrayOutputStream(1024 * 1024);
                flag = true;
            }
            else {
                if(count >= limit) {
                    workbook.write(tempOutputStream);

                    workbook.close();
                    workbook.dispose();
                    System.gc();

                    outputStreamList.add(tempOutputStream);

                    this.workbook = null;

                    this.workbook = new SXSSFWorkbook(16);

                    createSingleHeaderRowAll();
                    rownum = 1;
                    count = -1;
                    flag = false;
                }
            }
            SXSSFRow row = sheet.createRow(rownum);
            Cell cell_ngayBC = row.createCell(0);
            cell_ngayBC.setCellValue(StringUtils.longTimestamp2DateString(resultSets.getString("UPDATED_AT")));
            for (int i = 1; i < numberColum; i++) {
                Cell cell = row.createCell(i);
                if(dictValueConvert.containsKey(headerRow2.get(i))){
                    cell.setCellValue(StringUtils.longTimestamp2DateString(resultSets.getString(headerRow2.get(i))));
                }
                else cell.setCellValue(resultSets.getString(headerRow2.get(i)));
            }
            rownum++;
            count++;
        }
        log.info("   chay xong while rs.next  ");
        if(check){
            if(count <= limit && count > -1){
                workbook.write(tempOutputStream);
                outputStreamList.add(tempOutputStream);
            }
        }

        releaseConnect(conn, stmt, resultSets);
        workbook.close();
        workbook.dispose();
        System.gc();

        log.info("   writeDataToStream done   ");


    }
}
