package nocsystem.indexmanager.excel_helper;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HeaderExcel {
    List<String> listHeader;
    Map<String, String> mapHeader;

    public HeaderExcel() {
    }

    public HeaderExcel(List<String> listHeader, Map<String, String> mapHeader) {
        this.listHeader = listHeader;
        this.mapHeader = mapHeader;
    }

    public List<String> getListHeader() {
        return listHeader;
    }

    public void setListHeader(List<String> listHeader) {
        this.listHeader = listHeader;
    }

    public Map<String, String> getMapHeader() {
        return mapHeader;
    }

    public void setMapHeader(Map<String, String> mapHeader) {
        this.mapHeader = mapHeader;
    }


    public HeaderExcel getColumFromSql(ResultSetMetaData metaData) throws SQLException {

        int columnCount = metaData.getColumnCount();
        List<String> listColums = new ArrayList<>();
        listColums.add("NGAY_BAO_CAO");
        Map<String, String> mapHeader = new HashMap<>();
        mapHeader.put("NGAY_BAO_CAO", "NGAY_BAO_CAO");
        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            listColums.add(columnName);
            mapHeader.put(columnName, columnName);
        }
        return new HeaderExcel(listColums, mapHeader);
    }

}
