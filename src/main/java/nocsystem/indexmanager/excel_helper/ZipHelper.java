package nocsystem.indexmanager.excel_helper;

import nocsystem.indexmanager.common.StringCommon;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipHelper {
    private static final Logger log = LoggerFactory.getLogger(ZipHelper.class);

    public void returnZipFile(HttpServletResponse response, byte[] zippedBytes, String tenBaoCao)  {

        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", "attachment; filename=" + StringCommon.convertToSnakeCase(tenBaoCao) + ".zip");

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            log.info("  in returnZipFile ");
            outputStream.write(zippedBytes);
        } catch (Exception e) {
            log.info(" loi ham returnZipFile ");
            e.printStackTrace();
        }
    }


    private void addToZip(String entryName, byte[] data, ZipOutputStream zipOut) throws IOException {
        zipOut.putNextEntry(new ZipEntry(entryName));
        zipOut.write(data);
        zipOut.closeEntry();
    }
    public byte[] zipStreams(List<ByteArrayOutputStream> outputStreamList, String tenBaoCao) throws IOException {

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ZipOutputStream zipOut = new ZipOutputStream(bos)) {

            log.info(" trong zipStreams ");

            int i = 0;
            for (ByteArrayOutputStream outputStream : outputStreamList) {
                i++;
                addToZip(tenBaoCao + "_" + i + ".xlsx", outputStream.toByteArray(), zipOut);
            }
            zipOut.finish();
            log.info(" neu khong den day thi loi byte[] zipStreams ");

            return bos.toByteArray();
        }
    }

}
