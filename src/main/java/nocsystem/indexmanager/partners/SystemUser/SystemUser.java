package nocsystem.indexmanager.partners.SystemUser;

import nocsystem.indexmanager.exception.BadRequestException;
import nocsystem.indexmanager.exception.PermissionDeniedException;
import nocsystem.indexmanager.models.Response.Partner.SystemUserModel;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class SystemUser {
    @Value("${system-user.domain}")
    private String domain;

    public SystemUserModel getOfficePermission(String maNhanVien, String maChiNhanh, String maBuuCuc) throws IOException {
        SystemUserModel systemUser = new SystemUserModel();
        List<String> maChiNhanhPermission = List.of("HNI", "HCM", "AGG", "BDG", "BDH");
        List<String> maBuuCucPermission = List.of("ANBH", "ATY", "BDGTHA", "BHCN", "TNG");

        if (!maChiNhanhPermission.contains(maChiNhanh) && !maChiNhanh.isEmpty()) {
            throw new PermissionDeniedException("Bạn không có quyền truy cập vào chi nhánh: " + maChiNhanh);
        }

        if (!maBuuCucPermission.contains(maBuuCuc) && !maBuuCuc.isEmpty()) {
            throw new PermissionDeniedException("Bạn không có quyền truy cập vào bưu cục: " + maBuuCuc);
        }

        systemUser.setDanhMucBuuCuc(maBuuCucPermission);
        systemUser.setDanhMucChiNhanh(maChiNhanhPermission);

        return systemUser;
    }

}
