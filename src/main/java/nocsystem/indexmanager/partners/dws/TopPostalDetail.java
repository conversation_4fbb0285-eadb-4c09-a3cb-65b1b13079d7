package nocsystem.indexmanager.partners.dws;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TopPostalDetail {
    @JsonProperty("postal_code")
    String postalCode;

    @JsonProperty("postal_name")
    String postalName;

    @JsonProperty("branch_code")
    String branchCode;

    @JsonProperty("branch_name")
    String branchName;

    @JsonProperty("total_processed_bills")
    Double totalProcessedBills;

    @JsonProperty("total_wrong_bills")
    Double totalWrongBills;

    @JsonProperty("wrong_rate")
    Double wrongRate;

    @JsonProperty("revenue")
    Double revenue;
}
