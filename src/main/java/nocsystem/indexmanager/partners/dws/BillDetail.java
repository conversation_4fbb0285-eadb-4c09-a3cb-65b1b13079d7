package nocsystem.indexmanager.partners.dws;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class BillDetail {

    @JsonProperty("id")
    String id;

    @JsonProperty("bill_code")
    String billCode;

    @JsonProperty("measurement_time")
    String measurementTime;

    @JsonProperty("mining_center_code")
    String miningCenterCode;

    @JsonProperty("prev_weight_kg")
    Double prevWeightKg;

    @JsonProperty("prev_dimension")
    String prevDimension;

    @JsonProperty("prev_conversion_weigh")
    Double prevConversionWeigh;

    @JsonProperty("weight_kg")
    Double weightKg;

    @JsonProperty("dimension")
    String dimension;

    @JsonProperty("conversion_weigh")
    Double conversionWeigh;

    @JsonProperty("differential_weight_kg")
    Double differentialWeightKg;

    @JsonProperty("differential_price")
    Double differentialPrice;

    @JsonProperty("image_urls")
    List<String> imageUrls = new ArrayList<>();

    @JsonProperty("calc_price_weight")
    Double calcPriceWeight;

    @JsonProperty("prev_calc_price_weight")
    Double prevCalcPriceWeight;
}
