package nocsystem.indexmanager.partners.dws.controller;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.partners.dws.BillInfo;
import nocsystem.indexmanager.partners.dws.service.DwsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;

@RestController
@RequestMapping("/api/v1/partner/dws")
public class DwsController {

    @Autowired
    private DwsService dwsService;

    @GetMapping("/bills")
    public ResponseEntity<SimpleAPIResponse> getBillList(@RequestParam("bill-type") String billType,
                                                         @RequestParam("from-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fromDay,
                                                         @RequestParam("to-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toDay,
                                                         @RequestParam(value = "branch-codes", required = false) String branchCodes,
                                                         @RequestParam(value = "origin-postal-codes", required = false) String originPostalCodes,
                                                         @RequestParam(value = "mining-center-codes", required = false) String miningCenterCodes,
                                                         @RequestParam("page") int page,
                                                         @RequestParam("page-size") int pageSize,
                                                         @RequestParam(value = "keyword", required = false) String keyword){
        ListContentPageDto<BillInfo> rs = dwsService.getBills(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes, page, pageSize, keyword);
        SimpleAPIResponse response = new SimpleAPIResponse(rs);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/excel/export")
    public void testExcel(@RequestParam("bill-type") String billType,
                     @RequestParam("from-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fromDay,
                     @RequestParam("to-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toDay,
                     @RequestParam(value = "branch-codes", required = false) String branchCodes,
                     @RequestParam(value = "origin-postal-codes", required = false) String originPostalCodes,
                     @RequestParam(value = "mining-center-codes", required = false) String miningCenterCodes,
                     @RequestParam(value = "keyword", required = false) String keyword,
                     HttpServletResponse response){
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=DanhSachBills.xlsx";
        response.setHeader(headerKey, headerValue);

        dwsService.exportExcelLBills(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes, keyword, response);
    }

    @GetMapping("/bill-detail")
    public ResponseEntity<SimpleAPIResponse> getBillInfoDetail(@RequestParam("bill-code") String billCode){
        SimpleAPIResponse response = dwsService.getBillInfoDetail(billCode);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/bills-chart")
    public ResponseEntity<SimpleAPIResponse> getBillsChart(
                    @RequestParam("bill-type") String billType,
                    @RequestParam("from-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fromDay,
                    @RequestParam("to-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toDay,
                    @RequestParam(value = "branch-codes", required = false) String branchCodes,
                    @RequestParam(value = "origin-postal-codes", required = false) String originPostalCodes,
                    @RequestParam(value = "mining-center-codes", required = false) String miningCenterCodes){
        SimpleAPIResponse response = dwsService.getBillsChart(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/bills-statistics")
    public ResponseEntity<SimpleAPIResponse> getBillStatistics(
            @RequestParam("bill-type") String billType,
            @RequestParam("from-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fromDay,
            @RequestParam("to-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toDay,
            @RequestParam(value = "branch-codes", required = false) String branchCodes,
            @RequestParam(value = "origin-postal-codes", required = false) String originPostalCodes,
            @RequestParam(value = "mining-center-codes", required = false) String miningCenterCodes){
        SimpleAPIResponse response = dwsService.getBillStatistics(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/top-branch")
    public ResponseEntity<SimpleAPIResponse> getTopBranches(
            @RequestParam("from-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fromDay,
            @RequestParam("to-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toDay,
            @RequestParam(value = "num-of-top", required = false) Integer topNumber){
        SimpleAPIResponse response = dwsService.getTopBranch(fromDay, toDay, topNumber);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/top-postal")
    public ResponseEntity<SimpleAPIResponse> getTopPostal(
            @RequestParam("from-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fromDay,
            @RequestParam("to-day") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate toDay,
            @RequestParam(value = "num-of-top", required = false) Integer topNumber){
        SimpleAPIResponse response = dwsService.getTopPostal(fromDay, toDay, topNumber);
        return ResponseEntity.ok(response);
    }
}
