package nocsystem.indexmanager.partners.dws;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class BillsStatisticsChart {

    @JsonProperty("dates")
    List<String> dates;

    @JsonProperty("total_wrong_bills")
    List<Double> totalWrongBills;

    @JsonProperty("total_processed_bills")
    List<Double> totalProcessedBills;

    @JsonProperty("wrong_rates")
    List<Double> wrongRates;

    @JsonProperty("differential_revenues")
    List<Double> differentialRevenues;
}
