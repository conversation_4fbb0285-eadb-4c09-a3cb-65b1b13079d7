package nocsystem.indexmanager.partners.dws;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class BillInfo {
    @JsonProperty("id")
    String id;

    @JsonProperty("bill_code")
    String billCode;

    @JsonProperty("measurement_time")
    String measurementTime;

    @JsonProperty("mining_center_code")
    String miningCenterCode;

    @JsonProperty("mining_center_name")
    String miningCenterName;

    @JsonProperty("branch_code")
    String branchCode;

    @JsonProperty("branch_name")
    String branchName;

    @JsonProperty("origin_postal_code")
    String originPostalCode;

    @JsonProperty("origin_postal_name")
    String originPostalName;

    @JsonProperty("line")
    String line;

    @JsonProperty("postman_code")
    String postManCode;

    @JsonProperty("postman_name")
    String postManName;

    @JsonProperty("prev_weight_kg")
    Double prevWeightKg;

    @JsonProperty("weight_kg")
    Double weightKg;

    @JsonProperty("calc_price_weight")
    Double calcPriceWeight;

    @JsonProperty("prev_calc_price_weight")
    Double prevCalcPriceWeight;

    @JsonProperty("differential_weight_kg")
    Double differentialWeightKg;

    @JsonProperty("differential_price")
    Double getDifferentialPrice;

    @JsonProperty("punished_points")
    int punishedPoints;

    @JsonProperty("report_code")
    String reportCode;

}
