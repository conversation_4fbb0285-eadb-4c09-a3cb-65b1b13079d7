package nocsystem.indexmanager.partners.dws.service;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.partners.dws.BillDetail;
import nocsystem.indexmanager.partners.dws.BillInfo;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;

public interface DwsService {

    ListContentPageDto<BillInfo> getBills(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes, Integer page, Integer pageSize, String keyword);

    void exportExcelLBills(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes, String keyword, HttpServletResponse response);

    SimpleAPIResponse getBillInfoDetail(String id);

    SimpleAPIResponse getBillsChart(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes);

    SimpleAPIResponse getBillStatistics(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes);

    SimpleAPIResponse getTopBranch(LocalDate fromDay, LocalDate toDay, Integer numberOfTop);

    SimpleAPIResponse getTopPostal(LocalDate fromDay, LocalDate toDay, Integer numberOfTop);

}
