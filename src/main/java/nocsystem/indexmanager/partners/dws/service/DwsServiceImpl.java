package nocsystem.indexmanager.partners.dws.service;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.exception.BadRequestException;
import nocsystem.indexmanager.models.Response.ListContentPageDto;
import nocsystem.indexmanager.partners.dws.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static nocsystem.indexmanager.global.variable.ListVariableLocation.*;

@Service
public class DwsServiceImpl implements DwsService {

    private final Logger log = LoggerFactory.getLogger(DwsServiceImpl.class);
    @Value("${partner.dws.token:}")
    private String dwsToken;

    @Value("${partner.dws.base-url:}")
    private String dwsBaseUrl;

    private WebClient webClient;

    private final int TIMEOUT_RESPONSE_EXPORT_EXCEL = 150;

    @Autowired
    public void init(WebClient.Builder builder){
        webClient = builder.baseUrl(dwsBaseUrl)
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024))
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader("Authorization", "Bearer " +  dwsToken)
                .build();
    }

    public ListContentPageDto<BillInfo> getBills(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes, Integer page, Integer pageSize, String keyword){

        ListContentPageDto<BillInfo> content = new ListContentPageDto<>();
        if(!isViewAllDwsReport.equalsIgnoreCase("true") && !isLanhDaoChiNhanh.equalsIgnoreCase("true") && !isLanhDaoBuuCuc.equalsIgnoreCase("true")){
            log.info("User không phải role LĐ chi nhánh , LĐ bưu cục, TTVH, CTy LOG");
            return content;
        }
        Map<String, List<String>> params = getValidatedParams(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes, keyword);
        Integer pageWms = page - 1;
        params.put("page", Collections.singletonList(pageWms.toString()));
        params.put("page-size", Collections.singletonList(pageSize.toString()));
        log.debug("Params: {}", CollectionUtils.toMultiValueMap(params));
        
        DwsListBillResponse<BillInfo> data = webClient.get().uri(uriBuilder -> uriBuilder.path("/api/v1/noc/bills")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<DwsListBillResponse<BillInfo>>() {
                })
                .doOnError(e -> {
                    log.error("{}", e.getLocalizedMessage());
                })
                .block();

        if(data != null){
            if(data.getHttpStatusCode() == 200){
                content = new ListContentPageDto<>(data.getData().getCount(), 0, 0, data.getData().getItems());
                return content;
            }else{
                log.error("Dws error code:{}: {}", data.getDwsErrorCode(), data.getDetail());
                throw new BadRequestException(data.getDetail());

            }
        }else{
            String message = "Get Response from Api List Bills is NUll";
            log.error("{}", message);
            throw new BadRequestException(message);
        }
    }

    public void exportExcelLBills(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes, String keyword, HttpServletResponse response){
        Map<String, List<String>> params = getValidatedParams(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes, keyword);
        byte[] data = webClient.get().uri(uriBuilder -> uriBuilder.path("/api/v1/noc/bills/export")
                .queryParams(CollectionUtils.toMultiValueMap(params))
                .build()
                )
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .retrieve()
                .bodyToMono(byte[].class)
                .doOnError(e -> {
                    log.error("Error when Call Api Export Excel bill because of: {}", e.getLocalizedMessage());
                })
                .block();
        try{
            ServletOutputStream outputStream = response.getOutputStream();
            if(data == null){
                data = new byte[100];
            }
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("{}", e.getLocalizedMessage());
            log.error("{}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public SimpleAPIResponse getBillInfoDetail(String billCode) {
        DwsBillDetailResponse<BillDetail> dataRes = webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/v1/noc/bills/{billCode}")
                        .build(billCode))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<DwsBillDetailResponse<BillDetail>>() {
                })
                .doOnError(e -> {
                    log.error("Error when Call Api Get Bill Info Detail because of: {} at: {}",e.getMessage(), e.getLocalizedMessage());
                })
                .block();
        if(dataRes != null){
            if(dataRes.getHttpStatusCode() == 200 && dataRes.getData() != null){
                List<BillDetail> billDetail = dataRes.getData().getItems();
                return new SimpleAPIResponse(billDetail);
            }else{
                log.error("Dws error code:{}: {}", dataRes.getDwsErrorCode(), dataRes.getDetail());
                throw new BadRequestException(dataRes.getDetail());
            }
        }else{
            String message = "Get Response from Api Bill Detail is NUll";
            log.error("{}", message);
            throw new BadRequestException(message);
        }
    }

    @Override
    public SimpleAPIResponse getBillsChart(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes) {

        Map<String, List<String>> params = getValidatedParams(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes, null);

        DwsBillsStatisticsChartResponse data = webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/v1/noc/dashboard/bills-statistics-chart")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(DwsBillsStatisticsChartResponse.class).doOnError(e -> {
                    log.error("Error when Call Api get Bill Chart because of: {} at: {}", e.getMessage(), e.getLocalizedMessage());
                })
                .block();

        if(data != null){
            if(data.getHttpStatusCode() == 200){
                BillsStatisticsChart dataChart = data.getData();
                return new SimpleAPIResponse(dataChart);
            }else{
                log.error("Dws error code:{}: {}", data.getDwsErrorCode(), data.getDetail());
                throw new BadRequestException(data.getDetail());
            }
        }else{
            String message = "Get Response from Api Bills Chart is NUll";
            log.error("{}", message);
            throw new BadRequestException(message);
        }
    }

    @Override
    public SimpleAPIResponse getBillStatistics(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes) {
        Map<String, List<String>> params = getValidatedParams(billType, fromDay, toDay, branchCodes, originPostalCodes, miningCenterCodes, null);
        DwsBillStatisticsResponse res = webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/v1/noc/dashboard/bills-statistics")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(DwsBillStatisticsResponse.class)
                .doOnError(e -> {
                    log.error("Error when Call Api get Bill Statistics because of: {} at: {}", e.getMessage(), e.getLocalizedMessage());
                })
                .block();

        if(res != null){
            if(res.getHttpStatusCode() == 200){
                BillsStatisticsInfo dataChart = res.getData();
                return new SimpleAPIResponse(dataChart);
            }else{
                log.error("Dws error code:{}: {}", res.getDwsErrorCode(), res.getDetail());
                throw new BadRequestException(res.getDetail());
            }
        }else{
            String message = "Get Response from Api Bills Chart is NUll";
            log.error("{}", message);
            throw new BadRequestException(message);
        }
    }

    @Override
    public SimpleAPIResponse getTopBranch(LocalDate fromDay, LocalDate toDay, Integer topNumber) {

        if(!isViewAllDwsReport.equalsIgnoreCase("true")){
            TopBranchDetail topBranchDetail = new TopBranchDetail();
            return new SimpleAPIResponse(List.of(topBranchDetail));
        }
        Map<String, List<String>> params = new HashMap<>();
        if(topNumber != null){
            params.put("num-of-top", Collections.singletonList(topNumber.toString()));
        }
        params.put("from-day", Collections.singletonList(fromDay.toString()));
        params.put("to-day", Collections.singletonList(toDay.toString()));

        DwsTopBranchResponse res = webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/v1/noc/dashboard/top-branch")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(DwsTopBranchResponse.class)
                .doOnError(e -> {
                    log.error("Error when Call Api get Top Branches because of: {} at: {}", e.getMessage(), e.getLocalizedMessage());
                })
                .block();

        if(res != null){
            if(res.getHttpStatusCode() == 200){
                List<TopBranchDetail> dataChart = res.getItems();
                return new SimpleAPIResponse(dataChart);
            }else{
                log.error("Dws error code:{}: {}", res.getDwsErrorCode(), res.getDetail());
                throw new BadRequestException(res.getDetail());
            }
        }else{
            String message = "Get Response from Api Bills Chart is NUll";
            log.error("{}", message);
            throw new BadRequestException(message);
        }
    }

    @Override
    public SimpleAPIResponse getTopPostal(LocalDate fromDay, LocalDate toDay, Integer topNumber) {
        if(!isViewAllDwsReport.equalsIgnoreCase("true")){
            TopPostalDetail topPostalDetail = new TopPostalDetail();
            return new SimpleAPIResponse(List.of(topPostalDetail));
        }
        Map<String, List<String>> params = new HashMap<>();
        if(topNumber != null){
            params.put("num-of-top", Collections.singletonList(topNumber.toString()));
        }
        params.put("from-day", Collections.singletonList(fromDay.toString()));
        params.put("to-day", Collections.singletonList(toDay.toString()));

        DwsTopPostalResponse res = webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/v1/noc/dashboard/top-postal")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(DwsTopPostalResponse.class)
                .doOnError(e -> {
                    log.error("Error when Call Api get Top Postal because of: {} at: {}", e.getMessage(), e.getLocalizedMessage());
                })
                .block();

        if(res != null){
            if(res.getHttpStatusCode() == 200){
                List<TopPostalDetail> dataChart = res.getItems();
                return new SimpleAPIResponse(dataChart);
            }else{
                log.error("Dws error code:{}: {}", res.getDwsErrorCode(), res.getDetail());
                throw new BadRequestException(res.getDetail());
            }
        }else{
            String message = "Get Response from Api Bills Chart is NUll";
            log.error("{}", message);
            throw new BadRequestException(message);
        }
    }

    private Map<String, List<String>> getValidatedParams(String billType, LocalDate fromDay, LocalDate toDay, String branchCodes, String originPostalCodes, String miningCenterCodes, String keyword){

        Map<String, List<String>> params = new HashMap<>();
        params.put("from-day", Collections.singletonList(fromDay.toString()));
        params.put("to-day", Collections.singletonList(toDay.toString()));
        params.put("bill-type", Collections.singletonList(billType));

        if(miningCenterCodes != null && !miningCenterCodes.equalsIgnoreCase("")){
            params.put("mining-center-codes", Collections.singletonList(miningCenterCodes));
        }
        if(keyword != null && !keyword.equalsIgnoreCase("")){
            params.put("keyword", Collections.singletonList(keyword));
        }
        if(isViewAllDwsReport.equalsIgnoreCase("true")){
            log.info("=====> isAdmin: {}", isAdmin);
            if(branchCodes != null && !branchCodes.isEmpty()){
                params.put("branch-codes", Collections.singletonList(branchCodes));
            }
            if(originPostalCodes != null && !originPostalCodes.isEmpty()){
                params.put("origin-postal-codes", Collections.singletonList(originPostalCodes));
            }

        }else{
            branchCodes = getIntersectionValues(branchCodes, listChiNhanhVeriable);
            params.put("branch-codes", Collections.singletonList(branchCodes));
            if(isLanhDaoChiNhanh.equalsIgnoreCase("true")){
                log.info("isLanhDaoChiNhanh: {}", isLanhDaoChiNhanh);
                if(originPostalCodes != null && !originPostalCodes.isEmpty()){
                    params.put("origin-postal-codes", Collections.singletonList(originPostalCodes));
                }
            }
            if(isLanhDaoBuuCuc.equalsIgnoreCase("true")){
                log.info("isLanhDaoBuuCuc: {}", isLanhDaoBuuCuc);
                originPostalCodes = getIntersectionValues(originPostalCodes, listBuuCucVeriable);
                params.put("origin-postal-codes", Collections.singletonList(originPostalCodes));
            }
        }
        log.info("=======> Chi nhánh: {}", branchCodes);
        log.info("=======> Bưu Cuc: {}", originPostalCodes);

        return params;
    }

    private String getIntersectionValues(String requestValues, List<String> ssoList){
        if(ssoList == null){
            ssoList = new ArrayList<>();
        }
        if(requestValues != null && !requestValues.equals("")){
            List<String> requestList = Arrays.stream(requestValues.split(",")).map(String::trim).collect(Collectors.toList());
            List<String> result = requestList.stream().filter(ssoList::contains).collect(Collectors.toList());
            return String.join(",", result);
        }
        return String.join(",", ssoList);
    }

}
