package nocsystem.indexmanager.partners.luong.serviceImpl;

import nocsystem.indexmanager.partners.luong.repository.GiaoChiTieuKhmRepo;
import nocsystem.indexmanager.partners.luong.response.*;
import nocsystem.indexmanager.partners.luong.repository.GiaoChiTieuRepositoryBC;
import nocsystem.indexmanager.partners.luong.repository.GiaoChiTieuRepositoryCN;
import nocsystem.indexmanager.partners.luong.services.GiaoChiTieuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.math.BigInteger;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

@Component
public class GiaoChiTieuImpl implements GiaoChiTieuService {


    @Autowired
    private GiaoChiTieuRepositoryBC giaoChiTieuRepositoryBC;

    @Autowired
    private GiaoChiTieuRepositoryCN giaoChiTieuRepositoryCN;

    @Autowired
    private GiaoChiTieuKhmRepo giaoChiTieuKhmRepo;

    public LocalDate convertDate(String input) {
        /**
         *   hàm chuyển dạng 06/2023 -> 2023-06-31
         */

        int month = Integer.parseInt(input.substring(0, 2));
        int year = Integer.parseInt(input.substring(3));

        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        return lastDayOfMonth;
    }

    @Override
    public List<TienDoDoanhThuTlhtResult> tongHopTyLeDTThang(String timeSelect, int type) {


        LocalDate lastDayOfMonth = convertDate(timeSelect);

        List<TienDoDoanhThuTlhtResult> tienDoDoanhThuTlhtResults = new ArrayList<>();

        if (type == 2) {


            List<ChiTieuDoanhThuBCDto> cskdTongDoanhThu = giaoChiTieuRepositoryBC.findCSKDTienDoDoanhThuBC(lastDayOfMonth);

            if(cskdTongDoanhThu.isEmpty()){
                return null;
            }

            HashMap<String, List<FieldNecessary>> objectcheck = new HashMap<>();

            for (ChiTieuDoanhThuBCDto chiTieuDoanhThuBCDto : cskdTongDoanhThu) {
                String maCNAndBC = chiTieuDoanhThuBCDto.getMaCN() + "," + chiTieuDoanhThuBCDto.getMaBC();


                if(chiTieuDoanhThuBCDto.getNhomDoanhThu().equals("DT-CP")){
                    FieldNecessary fieldNecessary = new FieldNecessary();
                    fieldNecessary.setNhomDoanhThu("DT-CP");


                    Float floatValue = chiTieuDoanhThuBCDto.getTyLeHoanThanh();

                    DecimalFormat decimalFormat = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat.applyPattern("#0.#####");

                    String formattedString = null;
                    if(floatValue != null){
                        formattedString = decimalFormat.format(floatValue);
                    }

                    fieldNecessary.setTyLeHoanThanh(formattedString);

//                    fieldNecessary.setTyLeHoanThanh(chiTieuDoanhThuBCDto.getTyLeHoanThanh());
//                    fieldNecessary.setDoanhThuKeHoach(chiTieuDoanhThuBCDto.getDoanhThuKeHoach());
//                    fieldNecessary.setDoanhThuThucHien(chiTieuDoanhThuBCDto.getDoanhThuThucHien());



                    Float floatValue1 = chiTieuDoanhThuBCDto.getDoanhThuKeHoach();


                    DecimalFormat decimalFormat1 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat1.applyPattern("#0.#####");
                    String formattedString1 = null;
                    if(floatValue1 != null){
                        formattedString1 = decimalFormat1.format(floatValue1);
                    }


//                    fieldNecessary.setDoanhThuKeHoach(chiTieuDoanhThuBCDto.getDoanhThuKeHoach());
//                    fieldNecessary.setDoanhThuKeHoach(integerValue);
                    fieldNecessary.setDoanhThuKeHoach(formattedString1);



                    Float floatValue2 = chiTieuDoanhThuBCDto.getDoanhThuThucHien();

                    DecimalFormat decimalFormat2 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat2.applyPattern("#0.#####");
                    String formattedString2 = null;
                    if(floatValue2 != null){
                        formattedString2 = decimalFormat2.format(floatValue2);
                    }


//                    fieldNecessary.setDoanhThuThucHien(chiTieuDoanhThuBCDto.getDoanhThuThucHien());
//                    fieldNecessary.setDoanhThuThucHien(integerValue1);
                    fieldNecessary.setDoanhThuThucHien(formattedString2);


                    if(objectcheck.containsKey(maCNAndBC)){
                        objectcheck.get(maCNAndBC).add(fieldNecessary);
                    }
                    else {
                        List<FieldNecessary> l = new ArrayList<>();
                        l.add(fieldNecessary);
                        objectcheck.put(maCNAndBC, l);
                    }
                }
                else if(chiTieuDoanhThuBCDto.getNhomDoanhThu().equals("DT-LOGISTIC")){

                    FieldNecessary fieldNecessary = new FieldNecessary();
                    fieldNecessary.setNhomDoanhThu("DT-LOGISTIC");


                    Float floatValue = chiTieuDoanhThuBCDto.getTyLeHoanThanh();

                    DecimalFormat decimalFormat = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat.applyPattern("#0.#####");
                    String formattedString = null;
                    if(floatValue != null){
                        formattedString = decimalFormat.format(floatValue);
                    }

//                    fieldNecessary.setTyLeHoanThanh(chiTieuDoanhThuBCDto.getTyLeHoanThanh());
                    fieldNecessary.setTyLeHoanThanh(formattedString);



                    Float floatValue1 = chiTieuDoanhThuBCDto.getDoanhThuKeHoach();

                    DecimalFormat decimalFormat1 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat1.applyPattern("#0.#####");

                    String formattedString1 = null;
                    if(floatValue1 != null){
                        formattedString1 = decimalFormat1.format(floatValue1);
                    }


//                    fieldNecessary.setDoanhThuKeHoach(chiTieuDoanhThuBCDto.getDoanhThuKeHoach());
//                    fieldNecessary.setDoanhThuKeHoach(integerValue);
                    fieldNecessary.setDoanhThuKeHoach(formattedString1);


                    Float floatValue2 = chiTieuDoanhThuBCDto.getDoanhThuThucHien();

                    DecimalFormat decimalFormat2 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat2.applyPattern("#0.#####");
                    String formattedString2 = null;
                    if(floatValue2 != null){
                        formattedString2 = decimalFormat2.format(floatValue2);
                    }



//                    fieldNecessary.setDoanhThuThucHien(chiTieuDoanhThuBCDto.getDoanhThuThucHien());
//                    fieldNecessary.setDoanhThuThucHien(integerValue1);
                    fieldNecessary.setDoanhThuThucHien(formattedString2);

                    if(objectcheck.containsKey(maCNAndBC)){
                        objectcheck.get(maCNAndBC).add(fieldNecessary);
                    }
                    else {
                        List<FieldNecessary> l = new ArrayList<>();
                        l.add(fieldNecessary);
                        objectcheck.put(maCNAndBC, l);
                    }
                }
            }
            for (HashMap.Entry<String, List<FieldNecessary>> entry : objectcheck.entrySet()) {
                String key = entry.getKey();
                List<FieldNecessary> value = entry.getValue();

                TienDoDoanhThuTlhtResult tienDoDoanhThuTlhtResult = new TienDoDoanhThuTlhtResult();

                tienDoDoanhThuTlhtResult.setThang(timeSelect);
                tienDoDoanhThuTlhtResult.setMaCN(key.split(",")[0]);
                tienDoDoanhThuTlhtResult.setMaBC(key.split(",")[1]);
                for(FieldNecessary o : value){
                    if(o.getNhomDoanhThu().equals("DT-CP")){
                        tienDoDoanhThuTlhtResult.setTyLeHoanThanhDTCP(o.getTyLeHoanThanh());
                        tienDoDoanhThuTlhtResult.setDoanhThuKeHoachCP(o.getDoanhThuKeHoach());
                        tienDoDoanhThuTlhtResult.setDoanhThuThucHienCP(o.getDoanhThuThucHien());
                    }
                    else if(o.getNhomDoanhThu().equals("DT-LOGISTIC")){
                        tienDoDoanhThuTlhtResult.setTyLeHoanThanhDTLogistic(o.getTyLeHoanThanh());
                        tienDoDoanhThuTlhtResult.setDoanhThuKeHoachLogistic(o.getDoanhThuKeHoach());
                        tienDoDoanhThuTlhtResult.setDoanhThuThucHienLogistic(o.getDoanhThuThucHien());
                    }
                }

                tienDoDoanhThuTlhtResults.add(tienDoDoanhThuTlhtResult);
            }
        }
        else if(type == 1){
            List<ChiTieuDoanhThuCNDto> cskdTongDoanhThu = giaoChiTieuRepositoryCN.findCSKDTienDoDoanhThuCN(lastDayOfMonth);

            if(cskdTongDoanhThu.isEmpty()){
                return null;
            }

            HashMap<String, List<FieldNecessary>> objectcheck = new HashMap<>();

            for (ChiTieuDoanhThuCNDto chiTieuDoanhThuCNDto : cskdTongDoanhThu) {
                String maCNAndBC = chiTieuDoanhThuCNDto.getMaCN() + "," + chiTieuDoanhThuCNDto.getMaCN();

                if(chiTieuDoanhThuCNDto.getNhomDoanhThu().equals("DT-CP")){
                    FieldNecessary fieldNecessary = new FieldNecessary();
                    fieldNecessary.setNhomDoanhThu("DT-CP");


                    Float floatValue = chiTieuDoanhThuCNDto.getTyLeHoanThanh();

                    DecimalFormat decimalFormat = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat.applyPattern("#0.#####");

                    String formattedString = null;
                    if(floatValue != null){
                        formattedString = decimalFormat.format(floatValue);
                    }



//                    fieldNecessary.setTyLeHoanThanh(chiTieuDoanhThuCNDto.getTyLeHoanThanh());
                    fieldNecessary.setTyLeHoanThanh(formattedString);


                    Float floatValue1 = chiTieuDoanhThuCNDto.getDoanhThuKeHoach();

                    DecimalFormat decimalFormat1 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat1.applyPattern("#0.#####");
                    String formattedString1 = null;
                    if(floatValue1 != null){
                        formattedString1 = decimalFormat1.format(floatValue1);
                    }

//                    fieldNecessary.setDoanhThuKeHoach(chiTieuDoanhThuCNDto.getDoanhThuKeHoach());
                    fieldNecessary.setDoanhThuKeHoach(formattedString1);

                    Float floatValue2 = chiTieuDoanhThuCNDto.getDoanhThuThucHien();

                    DecimalFormat decimalFormat2 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat2.applyPattern("#0.#####");
                    String formattedString2 = null;
                    if(floatValue2 != null){
                        formattedString2 = decimalFormat2.format(floatValue2);
                    }

//                    fieldNecessary.setDoanhThuThucHien(chiTieuDoanhThuCNDto.getDoanhThuThucHien());
                    fieldNecessary.setDoanhThuThucHien(formattedString2);

                    if(objectcheck.containsKey(maCNAndBC)){
                        objectcheck.get(maCNAndBC).add(fieldNecessary);
                    }
                    else {
                        List<FieldNecessary> l = new ArrayList<>();
                        l.add(fieldNecessary);
                        objectcheck.put(maCNAndBC, l);
                    }
                }
                else if(chiTieuDoanhThuCNDto.getNhomDoanhThu().equals("DT-LOGISTIC")){

                    FieldNecessary fieldNecessary = new FieldNecessary();
                    fieldNecessary.setNhomDoanhThu("DT-LOGISTIC");


                    Float floatValue = chiTieuDoanhThuCNDto.getTyLeHoanThanh();

                    DecimalFormat decimalFormat = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat.applyPattern("#0.#####");
                    String formattedString = null;
                    if(floatValue != null){
                        formattedString = decimalFormat.format(floatValue);
                    }



                    Float floatValue1 = chiTieuDoanhThuCNDto.getDoanhThuKeHoach();

                    DecimalFormat decimalFormat1 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat1.applyPattern("#0.#####");
                    String formattedString1 = null;
                    if(floatValue1 != null){
                        formattedString1 = decimalFormat1.format(floatValue1);
                    }



                    Float floatValue2 = chiTieuDoanhThuCNDto.getDoanhThuThucHien();

                    DecimalFormat decimalFormat2 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
                    decimalFormat2.applyPattern("#0.#####");
                    String formattedString2 = null;
                    if( floatValue2 != null){
                        formattedString2 = decimalFormat2.format(floatValue2);
                    }




                    fieldNecessary.setTyLeHoanThanh(formattedString);
                    fieldNecessary.setDoanhThuKeHoach(formattedString1);
                    fieldNecessary.setDoanhThuThucHien(formattedString2);


//                    fieldNecessary.setTyLeHoanThanh(chiTieuDoanhThuCNDto.getTyLeHoanThanh());
//                    fieldNecessary.setDoanhThuKeHoach(chiTieuDoanhThuCNDto.getDoanhThuKeHoach());
//                    fieldNecessary.setDoanhThuThucHien(chiTieuDoanhThuCNDto.getDoanhThuThucHien());

                    if(objectcheck.containsKey(maCNAndBC)){
                        objectcheck.get(maCNAndBC).add(fieldNecessary);
                    }
                    else {
                        List<FieldNecessary> l = new ArrayList<>();
                        l.add(fieldNecessary);
                        objectcheck.put(maCNAndBC, l);
                    }
                }
            }
            for (HashMap.Entry<String, List<FieldNecessary>> entry : objectcheck.entrySet()) {
                String key = entry.getKey();
                List<FieldNecessary> value = entry.getValue();

                TienDoDoanhThuTlhtResult tienDoDoanhThuTlhtResult = new TienDoDoanhThuTlhtResult();

                tienDoDoanhThuTlhtResult.setThang(timeSelect);
                tienDoDoanhThuTlhtResult.setMaCN(key.split(",")[0]);
                tienDoDoanhThuTlhtResult.setMaBC(key.split(",")[1]);
                for(FieldNecessary fieldNecessary : value){
                    if(fieldNecessary.getNhomDoanhThu().equals("DT-CP")){
                        tienDoDoanhThuTlhtResult.setTyLeHoanThanhDTCP(fieldNecessary.getTyLeHoanThanh());
                        tienDoDoanhThuTlhtResult.setDoanhThuKeHoachCP(fieldNecessary.getDoanhThuKeHoach());
                        tienDoDoanhThuTlhtResult.setDoanhThuThucHienCP(fieldNecessary.getDoanhThuThucHien());
                    }
                    else if(fieldNecessary.getNhomDoanhThu().equals("DT-LOGISTIC")){
                        tienDoDoanhThuTlhtResult.setTyLeHoanThanhDTLogistic(fieldNecessary.getTyLeHoanThanh());
                        tienDoDoanhThuTlhtResult.setDoanhThuKeHoachLogistic(fieldNecessary.getDoanhThuKeHoach());
                        tienDoDoanhThuTlhtResult.setDoanhThuThucHienLogistic(fieldNecessary.getDoanhThuThucHien());
                    }
                }

                tienDoDoanhThuTlhtResults.add(tienDoDoanhThuTlhtResult);
            }
        }

        return tienDoDoanhThuTlhtResults;
    }

    @Override
    public List<GiaoChiTieuTlhtResult> tongHopTyLeDTThangKHM(String timeSelect, int type) {

        LocalDate lastDayOfMonth = convertDate(timeSelect);

        List<DoanhThuKhachHangMoiDto> doanhThuKhachHangMoiDtos = giaoChiTieuKhmRepo.findGiaoChiTieuKhm(lastDayOfMonth, type);
        List<GiaoChiTieuTlhtResult>  giaoChiTieuTlhtResults = new ArrayList<>();
        if(doanhThuKhachHangMoiDtos.isEmpty()){
            return null;
        }
        for(DoanhThuKhachHangMoiDto doanhThuKhachHangMoiDto : doanhThuKhachHangMoiDtos){
            GiaoChiTieuTlhtResult giaoChiTieuTlhtResult =  new GiaoChiTieuTlhtResult();
            giaoChiTieuTlhtResult.setThang(timeSelect);
            giaoChiTieuTlhtResult.setMaCN(doanhThuKhachHangMoiDto.getMaCN());
            giaoChiTieuTlhtResult.setMaBC(doanhThuKhachHangMoiDto.getMaBC());


            Float floatValue = doanhThuKhachHangMoiDto.getTyLeHoanThanhKHM();

            DecimalFormat decimalFormat = (DecimalFormat) NumberFormat.getInstance(Locale.US);
            decimalFormat.applyPattern("#0.#####");
            String formattedString = null;
            if(floatValue != null){
                formattedString = decimalFormat.format(floatValue);
            }



            BigInteger floatValue1 = doanhThuKhachHangMoiDto.getDoanhThuKHM();

            DecimalFormat decimalFormat1 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
            decimalFormat1.applyPattern("#0.#####");
            String formattedString1 = null;

            if(floatValue1 != null){
                formattedString1 =  decimalFormat1.format(floatValue1);
            }


            BigInteger floatValue2 = doanhThuKhachHangMoiDto.getChiTieuGiao();

            DecimalFormat decimalFormat2 = (DecimalFormat) NumberFormat.getInstance(Locale.US);
            decimalFormat2.applyPattern("#0.#####");
            String formattedString2 = null;
            if(floatValue2 != null){
                formattedString2 =   decimalFormat2.format(floatValue2);
            }

//            giaoChiTieuTlhtResult.setTyLeHoanThanhKhm(o.getTyLeHoanThanhKHM());
//            giaoChiTieuTlhtResult.setDoanhThuKhm(o.getDoanhThuKHM());
//            giaoChiTieuTlhtResult.setChiTieuGiao(o.getChiTieuGiao());

            giaoChiTieuTlhtResult.setTyLeHoanThanhKhm(formattedString);
            giaoChiTieuTlhtResult.setDoanhThuKhm(formattedString1);
            giaoChiTieuTlhtResult.setChiTieuGiao(formattedString2);

            giaoChiTieuTlhtResults.add(giaoChiTieuTlhtResult);

        }

        return giaoChiTieuTlhtResults;
    }
}
