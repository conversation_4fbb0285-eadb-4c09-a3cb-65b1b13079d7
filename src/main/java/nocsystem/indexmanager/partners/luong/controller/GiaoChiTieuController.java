package nocsystem.indexmanager.partners.luong.controller;

import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.models.Response.TienDoDoanhThuDto.ChiTieuDoanhThuTlhtBody;
import nocsystem.indexmanager.partners.luong.response.GiaoChiTieuTlhtResult;
import nocsystem.indexmanager.partners.luong.response.TienDoDoanhThuTlhtResult;
import nocsystem.indexmanager.partners.luong.services.GiaoChiTieuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1")
public class GiaoChiTieuController {

    @Autowired
    private GiaoChiTieuService giaoChiTieuService;
    @PostMapping("/cskd-tien-do-doanh-thu/get-tlht")
    public SimpleAPIResponse tienDoDoanhThuTlht(
            @RequestBody ChiTieuDoanhThuTlhtBody chiTieuDoanhThuTlhtBody
    ) {
        String timeSelect = chiTieuDoanhThuTlhtBody.getTimeSelect();
        int type = chiTieuDoanhThuTlhtBody.getType();
        List<TienDoDoanhThuTlhtResult> tienDoDoanhThu =
                giaoChiTieuService.tongHopTyLeDTThang(timeSelect, type);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDoanhThu);
        return simpleAPIResponse;
    }

    @PostMapping("/cskd-tien-do-doanh-thu/bu-luong/doanh-thu-tlht-khm")
    public SimpleAPIResponse tienDoDoanhThuTlhtKHM(
            @RequestBody ChiTieuDoanhThuTlhtBody chiTieuDoanhThuTlhtBody
    ) {
        String timeSelect = chiTieuDoanhThuTlhtBody.getTimeSelect();
        int type = chiTieuDoanhThuTlhtBody.getType();
        List<GiaoChiTieuTlhtResult> tienDoDoanhThu =
                giaoChiTieuService.tongHopTyLeDTThangKHM(timeSelect, type);
        SimpleAPIResponse simpleAPIResponse = new SimpleAPIResponse();
        simpleAPIResponse.setData(tienDoDoanhThu);
        return simpleAPIResponse;
    }
}
