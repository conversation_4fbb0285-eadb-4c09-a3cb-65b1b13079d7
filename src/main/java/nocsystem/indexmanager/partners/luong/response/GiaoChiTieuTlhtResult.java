package nocsystem.indexmanager.partners.luong.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigInteger;

public class GiaoChiTieuTlhtResult {

    @JsonProperty("thang")
    private String thang;

    @JsonProperty("ma_chi_nhanh")
    private String maCN;
    @JsonProperty("ma_buu_cuc")
    private String maBC;

    @JsonProperty("ty_le_hoan_thanh_khm")
//    private Float tyLeHoanThanhKhm;
    private String tyLeHoanThanhKhm;

    @JsonProperty("doanh_thu_khm")
//    private BigInteger doanhThuKhm;
    private String doanhThuKhm;

    @JsonProperty("chi_tieu_giao")
//    private BigInteger chiTieuGiao;
    private String chiTieuGiao;


    public GiaoChiTieuTlhtResult(String thang, String maCN, String maBC, String tyLeHoanThanhKhm, String doanhThuKhm, String chiTieuGiao) {
        this.thang = thang;
        this.maCN = maCN;
        this.maBC = maBC;
        this.tyLeHoanThanhKhm = tyLeHoanThanhKhm;
        this.doanhThuKhm = doanhThuKhm;
        this.chiTieuGiao = chiTieuGiao;
    }

    public GiaoChiTieuTlhtResult() {
    }

    public String getThang() {
        return thang;
    }

    public void setThang(String thang) {
        this.thang = thang;
    }

    public String getMaCN() {
        return maCN;
    }

    public void setMaCN(String maCN) {
        this.maCN = maCN;
    }

    public String getMaBC() {
        return maBC;
    }

    public void setMaBC(String maBC) {
        this.maBC = maBC;
    }

    public String getTyLeHoanThanhKhm() {
        return tyLeHoanThanhKhm;
    }

    public void setTyLeHoanThanhKhm(String tyLeHoanThanhKhm) {
        this.tyLeHoanThanhKhm = tyLeHoanThanhKhm;
    }

    public String getDoanhThuKhm() {
        return doanhThuKhm;
    }

    public void setDoanhThuKhm(String doanhThuKhm) {
        this.doanhThuKhm = doanhThuKhm;
    }

    public String getChiTieuGiao() {
        return chiTieuGiao;
    }

    public void setChiTieuGiao(String chiTieuGiao) {
        this.chiTieuGiao = chiTieuGiao;
    }
}
