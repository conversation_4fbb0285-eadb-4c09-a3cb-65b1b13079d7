package nocsystem.indexmanager.partners.luong.response;

import java.util.HashMap;
import java.util.List;

public class ObjectConvert {
    private HashMap<String, List<HashMap<String, Float>>> listHashMap;

    public ObjectConvert(HashMap<String, List<HashMap<String, Float>>> listHashMap) {
        this.listHashMap = listHashMap;
    }

    public ObjectConvert() {
    }

    public HashMap<String, List<HashMap<String, Float>>> getListHashMap() {
        return listHashMap;
    }

    public void setListHashMap(HashMap<String, List<HashMap<String, Float>>> listHashMap) {
        this.listHashMap = listHashMap;
    }
}
