package nocsystem.indexmanager.partners.luong.response;

public class ChiTieuDoanhThuBCDto {

    private String maCN;
    private String maBC;
    private String nhomDoanhThu;
    private Float tyLeHoanThanh;

    private Float doanhThuThucHien;
    private Float doanhThuKeHoach;

    public ChiTieuDoanhThuBCDto(String maCN, String maBC, String nhomDoanhThu, Float tyLeHoanThanh, Float doanhThuThucHien, Float doanhThuKeHoach) {
        this.maCN = maCN;
        this.maBC = maBC;
        this.nhomDoanhThu = nhomDoanhThu;
        this.tyLeHoanThanh = tyLeHoanThanh;
        this.doanhThuThucHien = doanhThuThucHien;
        this.doanhThuKeHoach = doanhThuKeHoach;
    }

    public ChiTieuDoanhThuBCDto() {
    }

    public String getMaCN() {
        return maCN;
    }

    public void setMaCN(String maCN) {
        this.maCN = maCN;
    }

    public String getMaBC() {
        return maBC;
    }

    public void setMaBC(String maBC) {
        this.maBC = maBC;
    }

    public String getNhomDoanhThu() {
        return nhomDoanhThu;
    }

    public void setNhomDoanhThu(String nhomDoanhThu) {
        this.nhomDoanhThu = nhomDoanhThu;
    }

    public Float getTyLeHoanThanh() {
        return tyLeHoanThanh;
    }

    public void setTyLeHoanThanh(Float tyLeHoanThanh) {
        this.tyLeHoanThanh = tyLeHoanThanh;
    }

    public Float getDoanhThuThucHien() {
        return doanhThuThucHien;
    }

    public void setDoanhThuThucHien(Float doanhThuThucHien) {
        this.doanhThuThucHien = doanhThuThucHien;
    }

    public Float getDoanhThuKeHoach() {
        return doanhThuKeHoach;
    }

    public void setDoanhThuKeHoach(Float doanhThuKeHoach) {
        this.doanhThuKeHoach = doanhThuKeHoach;
    }
}
