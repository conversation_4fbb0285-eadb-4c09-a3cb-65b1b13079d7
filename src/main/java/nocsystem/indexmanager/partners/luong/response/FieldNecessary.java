package nocsystem.indexmanager.partners.luong.response;



public class FieldNecessary {
    private String nhomDoanhThu;
//    private Float tyLeHoanThanh;
    private String tyLeHoanThanh;
//    private Float doanhThuKeHoach;
    private String doanhThuKeHoach;
//    private Float doanhThuThucHien;
    private String doanhThuThucHien;

    public FieldNecessary(String nhomDoanhThu, String tyLeHoanThanh, String doanhThuKeHoach, String doanhThuThucHien) {
        this.nhomDoanhThu = nhomDoanhThu;
        this.tyLeHoanThanh = tyLeHoanThanh;
        this.doanhThuKeHoach = doanhThuKeHoach;
        this.doanhThuThucHien = doanhThuThucHien;
    }

    public FieldNecessary() {
    }

    public String getNhomDoanhThu() {
        return nhomDoanhThu;
    }

    public void setNhomDoanhThu(String nhomDoanhThu) {
        this.nhomDoanhThu = nhomDoanhThu;
    }

    public String getTyLeHoanThanh() {
        return tyLeHoanThanh;
    }

    public void setTyLeHoanThanh(String tyLeHoanThanh) {
        this.tyLeHoanThanh = tyLeHoanThanh;
    }

    public String getDoanhThuKeHoach() {
        return doanhThuKeHoach;
    }

    public void setDoanhThuKeHoach(String doanhThuKeHoach) {
        this.doanhThuKeHoach = doanhThuKeHoach;
    }

    public String getDoanhThuThucHien() {
        return doanhThuThucHien;
    }

    public void setDoanhThuThucHien(String doanhThuThucHien) {
        this.doanhThuThucHien = doanhThuThucHien;
    }

    public String toString(){
        return nhomDoanhThu + ", " +tyLeHoanThanh + ", "+doanhThuKeHoach + ", "+doanhThuThucHien;
    }
}
