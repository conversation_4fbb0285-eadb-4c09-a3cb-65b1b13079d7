package nocsystem.indexmanager.partners.luong.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TienDoDoanhThuTlhtResult {
    @JsonProperty("thang")
    private String thang;

    @JsonProperty("ma_chi_nhanh")
    private String maCN;
    @JsonProperty("ma_buu_cuc")
    private String maBC;
    @JsonProperty("ty_le_hoan_thanh_cp")
//    private Float tyLeHoanThanhDTCP;
    private String tyLeHoanThanhDTCP;
    @JsonProperty("ty_le_hoan_thanh_logistic")
//    private Float tyLeHoanThanhDTLogistic;
    private String tyLeHoanThanhDTLogistic;

    @JsonProperty("doanh_thu_thuc_hien_cp")
//    private Float doanhThuThucHienCP;
    private String doanhThuThucHienCP;
    @JsonProperty("doanh_thu_ke_hoach_cp")
//    private Float doanhThuKeHoachCP;
    private String doanhThuKeHoachCP;
    @JsonProperty("doanh_thu_thuc_hien_logistic")
//    private Float doanhThuThucHienLogistic;
    private String doanhThuThucHienLogistic;
    @JsonProperty("doanh_thu_ke_hoach_logistic")
//    private Float doanhThuKeHoachLogistic;
    private String doanhThuKeHoachLogistic;
    public TienDoDoanhThuTlhtResult() {
    }

//    public TienDoDoanhThuTlhtResult(String thang, String maCN, String maBC, Float tyLeHoanThanhDTCP, Float tyLeHoanThanhDTLogistic, Float doanhThuThucHienCP, Float doanhThuKeHoachCP, Float doanhThuThucHienLogistic, Float doanhThuKeHoachLogistic) {
    public TienDoDoanhThuTlhtResult(String thang, String maCN, String maBC, String tyLeHoanThanhDTCP, String tyLeHoanThanhDTLogistic, String doanhThuThucHienCP, String doanhThuKeHoachCP, String doanhThuThucHienLogistic, String doanhThuKeHoachLogistic) {
        this.thang = thang;
        this.maCN = maCN;
        this.maBC = maBC;
        this.tyLeHoanThanhDTCP = tyLeHoanThanhDTCP;
        this.tyLeHoanThanhDTLogistic = tyLeHoanThanhDTLogistic;
        this.doanhThuThucHienCP = doanhThuThucHienCP;
        this.doanhThuKeHoachCP = doanhThuKeHoachCP;
        this.doanhThuThucHienLogistic = doanhThuThucHienLogistic;
        this.doanhThuKeHoachLogistic = doanhThuKeHoachLogistic;
    }

    public String getThang() {
        return thang;
    }

    public void setThang(String thang) {
        this.thang = thang;
    }

    public String getMaCN() {
        return maCN;
    }

    public void setMaCN(String maCN) {
        this.maCN = maCN;
    }

    public String getMaBC() {
        return maBC;
    }

    public void setMaBC(String maBC) {
        this.maBC = maBC;
    }

    public String getTyLeHoanThanhDTCP() {
        return tyLeHoanThanhDTCP;
    }

    public void setTyLeHoanThanhDTCP(String tyLeHoanThanhDTCP) {
        this.tyLeHoanThanhDTCP = tyLeHoanThanhDTCP;
    }

    public String getTyLeHoanThanhDTLogistic() {
        return tyLeHoanThanhDTLogistic;
    }

    public void setTyLeHoanThanhDTLogistic(String tyLeHoanThanhDTLogistic) {
        this.tyLeHoanThanhDTLogistic = tyLeHoanThanhDTLogistic;
    }

    public String getDoanhThuThucHienCP() {
        return doanhThuThucHienCP;
    }

    public void setDoanhThuThucHienCP(String doanhThuThucHienCP) {
        this.doanhThuThucHienCP = doanhThuThucHienCP;
    }

    public String getDoanhThuKeHoachCP() {
        return doanhThuKeHoachCP;
    }

    public void setDoanhThuKeHoachCP(String doanhThuKeHoachCP) {
        this.doanhThuKeHoachCP = doanhThuKeHoachCP;
    }

    public String getDoanhThuThucHienLogistic() {
        return doanhThuThucHienLogistic;
    }

    public void setDoanhThuThucHienLogistic(String doanhThuThucHienLogistic) {
        this.doanhThuThucHienLogistic = doanhThuThucHienLogistic;
    }

    public String getDoanhThuKeHoachLogistic() {
        return doanhThuKeHoachLogistic;
    }

    public void setDoanhThuKeHoachLogistic(String doanhThuKeHoachLogistic) {
        this.doanhThuKeHoachLogistic = doanhThuKeHoachLogistic;
    }
}
