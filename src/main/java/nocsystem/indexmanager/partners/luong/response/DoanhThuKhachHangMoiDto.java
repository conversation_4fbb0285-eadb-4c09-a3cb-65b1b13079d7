package nocsystem.indexmanager.partners.luong.response;


import java.math.BigInteger;

public class DoanhThuKhachHangMoiDto {

    private String maCN;
    private String maBC;
    private Float tyLeHoanThanhKHM;
    private BigInteger doanhThuKHM;
    private BigInteger chiTieuGiao;

    public DoanhThuKhachHangMoiDto(String maCN, String maBC, Float tyLeHoanThanhKHM, BigInteger doanhThuKHM, BigInteger chiTieuGiao) {
        this.maCN = maCN;
        this.maBC = maBC;
        this.tyLeHoanThanhKHM = tyLeHoanThanhKHM;
        this.doanhThuKHM = doanhThuKHM;
        this.chiTieuGiao = chiTieuGiao;
    }

    public DoanhThuKhachHangMoiDto() {
    }

    public String getMaCN() {
        return maCN;
    }

    public void setMaCN(String maCN) {
        this.maCN = maCN;
    }

    public String getMaBC() {
        return maBC;
    }

    public void setMaBC(String maBC) {
        this.maBC = maBC;
    }

    public Float getTyLeHoanThanhKHM() {
        return tyLeHoanThanhKHM;
    }

    public void setTyLeHoanThanhKHM(Float tyLeHoanThanhKHM) {
        this.tyLeHoanThanhKHM = tyLeHoanThanhKHM;
    }

    public BigInteger getDoanhThuKHM() {
        return doanhThuKHM;
    }

    public void setDoanhThuKHM(BigInteger doanhThuKHM) {
        this.doanhThuKHM = doanhThuKHM;
    }

    public BigInteger getChiTieuGiao() {
        return chiTieuGiao;
    }

    public void setChiTieuGiao(BigInteger chiTieuGiao) {
        this.chiTieuGiao = chiTieuGiao;
    }
}
