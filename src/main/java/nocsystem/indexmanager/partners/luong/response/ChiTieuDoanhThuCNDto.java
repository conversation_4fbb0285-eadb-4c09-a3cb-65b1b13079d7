package nocsystem.indexmanager.partners.luong.response;

public class ChiTieuDoanhThuCNDto {
    private String maCN;
    private String nhomDoanhThu;
    private Float tyLeHoanThanh;

    private Float doanhThuThucHien;
    private Float doanhThuKeHoach;


    public ChiTieuDoanhThuCNDto(String maCN, String nhomDoanhThu, Float tyLeHoanThanh, Float doanhThuThucHien, Float doanhThuKeHoach) {
        this.maCN = maCN;
        this.nhomDoanhThu = nhomDoanhThu;
        this.tyLeHoanThanh = tyLeHoanThanh;
        this.doanhThuThucHien = doanhThuThucHien;
        this.doanhThuKeHoach = doanhThuKeHoach;
    }

    public ChiTieuDoanhThuCNDto() {
    }

    public String getMaCN() {
        return maCN;
    }

    public void setMaCN(String maCN) {
        this.maCN = maCN;
    }

    public String getNhomDoanhThu() {
        return nhomDoanhThu;
    }

    public void setNhomDoanhThu(String nhomDoanhThu) {
        this.nhomDoanhThu = nhomDoanhThu;
    }

    public Float getTyLeHoanThanh() {
        return tyLeHoanThanh;
    }

    public void setTyLeHoanThanh(Float tyLeHoanThanh) {
        this.tyLeHoanThanh = tyLeHoanThanh;
    }

    public Float getDoanhThuThucHien() {
        return doanhThuThucHien;
    }

    public void setDoanhThuThucHien(Float doanhThuThucHien) {
        this.doanhThuThucHien = doanhThuThucHien;
    }

    public Float getDoanhThuKeHoach() {
        return doanhThuKeHoach;
    }

    public void setDoanhThuKeHoach(Float doanhThuKeHoach) {
        this.doanhThuKeHoach = doanhThuKeHoach;
    }
}
