package nocsystem.indexmanager.partners.luong.models.doanhthukhm;


import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "csl_doanh_thu_khm")
@IdClass(DoanhThuKhachHangMoiKey.class)
public class DoanhThuKhachHangMoi implements Serializable {

    @Id
    @Column(name = "ma_cn")
    private String maChiNhanh;

    @Id
    @Column(name = "ma_buucuc")
    private String maBuuCuc;

    @Id
    @Column(name = "ngay_baocao")
    private LocalDate ngayBaoCao;


    @Column(name = "tlht_khm")
    private Float tyLeHoanThanhKHM;

    @Column(name = "type")
    private Integer type;


    @Column(name = "chi_tieu_giao")
    private BigInteger chiTieuGiao;

    @Column(name = "dt_khm")
    private BigInteger doanhThuKHM;

    public DoanhThuKhachHangMoi(String maChiNhanh, String maBuuCuc, LocalDate ngayBaoCao, Float tyLeHoanThanhKHM, Integer type, BigInteger chiTieuGiao, BigInteger doanhThuKHM) {
        this.maChiNhanh = maChiNhanh;
        this.maBuuCuc = maBuuCuc;
        this.ngayBaoCao = ngayBaoCao;
        this.tyLeHoanThanhKHM = tyLeHoanThanhKHM;
        this.type = type;
        this.chiTieuGiao = chiTieuGiao;
        this.doanhThuKHM = doanhThuKHM;
    }

    public DoanhThuKhachHangMoi() {
    }

    public String getMaChiNhanh() {
        return maChiNhanh;
    }

    public void setMaChiNhanh(String maChiNhanh) {
        this.maChiNhanh = maChiNhanh;
    }

    public String getMaBuuCuc() {
        return maBuuCuc;
    }

    public void setMaBuuCuc(String maBuuCuc) {
        this.maBuuCuc = maBuuCuc;
    }

    public LocalDate getNgayBaoCao() {
        return ngayBaoCao;
    }

    public void setNgayBaoCao(LocalDate ngayBaoCao) {
        this.ngayBaoCao = ngayBaoCao;
    }

    public Float getTyLeHoanThanhKHM() {
        return tyLeHoanThanhKHM;
    }

    public void setTyLeHoanThanhKHM(Float tyLeHoanThanhKHM) {
        this.tyLeHoanThanhKHM = tyLeHoanThanhKHM;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigInteger getChiTieuGiao() {
        return chiTieuGiao;
    }

    public void setChiTieuGiao(BigInteger chiTieuGiao) {
        this.chiTieuGiao = chiTieuGiao;
    }

    public BigInteger getDoanhThuKHM() {
        return doanhThuKHM;
    }

    public void setDoanhThuKHM(BigInteger doanhThuKHM) {
        this.doanhThuKHM = doanhThuKHM;
    }
}
