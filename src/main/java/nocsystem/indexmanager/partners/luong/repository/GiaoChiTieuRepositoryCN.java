package nocsystem.indexmanager.partners.luong.repository;

import nocsystem.indexmanager.partners.luong.response.ChiTieuDoanhThuCNDto;
import nocsystem.indexmanager.models.TienDoDoanhThu.TienDoDoanhThuChiNhanh;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDate;
import java.util.List;

public interface GiaoChiTieuRepositoryCN extends PagingAndSortingRepository<TienDoDoanhThuChiNhanh, Long> {

    @Query("SELECT new nocsystem.indexmanager.partners.luong.response.ChiTieuDoanhThuCNDto" +
            "(tddt.maChiNhanh, tddt.nhomDoanhThu, tddt.tlht, tddt.thucHien, tddt.keHoach) " +
            "FROM TienDoDoanhThuChiNhanh tddt" +
            " where tddt.ngayBaoCao = :lastDayOfMonth")
    List<ChiTieuDoanhThuCNDto> findCSKDTienDoDoanhThuCN(
            LocalDate lastDayOfMonth);
}
