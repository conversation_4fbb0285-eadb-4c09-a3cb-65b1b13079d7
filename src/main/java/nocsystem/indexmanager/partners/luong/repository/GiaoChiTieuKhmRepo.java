package nocsystem.indexmanager.partners.luong.repository;

import nocsystem.indexmanager.partners.luong.models.doanhthukhm.DoanhThuKhachHangMoi;
import nocsystem.indexmanager.partners.luong.models.doanhthukhm.DoanhThuKhachHangMoiKey;
import nocsystem.indexmanager.partners.luong.response.DoanhThuKhachHangMoiDto;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface GiaoChiTieuKhmRepo extends PagingAndSortingRepository<DoanhThuKhachHangMoi, DoanhThuKhachHangMoiKey> {

    @Query("SELECT new nocsystem.indexmanager.partners.luong.response.DoanhThuKhachHangMoiDto" +
            "(tddt.ma<PERSON><PERSON><PERSON><PERSON><PERSON>, tddt.maBuuCuc, tddt.tyLeHoanThanhKHM, tddt.doanhThuKHM, tddt.chiTieuGiao) " +
            "FROM  DoanhThuKhachHangMoi tddt" +
            " where tddt.ngayBaoCao = :lastDayOfMonth and tddt.type =:type")
    List<DoanhThuKhachHangMoiDto> findGiaoChiTieuKhm(
            LocalDate lastDayOfMonth, int type);

}
