package nocsystem.indexmanager.partners.luong.repository;

import nocsystem.indexmanager.partners.luong.response.ChiTieuDoanhThuBCDto;
import nocsystem.indexmanager.models.TienDoDoanhThu.TienDoDoanhThuBuuCuc;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface GiaoChiTieuRepositoryBC extends PagingAndSortingRepository<TienDoDoanhThuBuuCuc, Long> {

    @Query("SELECT new nocsystem.indexmanager.partners.luong.response.ChiTieuDoanhThuBCDto" +
            "(tddt.maChiNhanh, tddt.maBuuCuc, tddt.nhomDoanhThu, tddt.tlht, tddt.thucHien, tddt.keHoach) " +
            "FROM TienDoDoanhThuBuuCuc tddt" +
            " where tddt.ngayBaoCao = :lastDayOfMonth")
    List<ChiTieuDoanhThuBCDto> findCSKDTienDoDoanhThuBC(
            LocalDate lastDayOfMonth);
}
