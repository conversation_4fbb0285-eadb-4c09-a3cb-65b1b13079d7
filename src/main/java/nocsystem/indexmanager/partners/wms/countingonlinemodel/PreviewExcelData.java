package nocsystem.indexmanager.partners.wms.countingonlinemodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PreviewExcelData {

    @JsonProperty("detail")
    private String detail;

    @JsonProperty("pack_id")
    private String packId;

    @JsonProperty("total_counter_actual")
    private Integer totalCounterActual;

    @JsonProperty("parcel_id")
    private String parcelId;

    @JsonProperty("reason_id")
    private Integer reasonId;

    @JsonProperty("note")
    private String note;

    @JsonProperty("user_name")
    private String userName;
}

