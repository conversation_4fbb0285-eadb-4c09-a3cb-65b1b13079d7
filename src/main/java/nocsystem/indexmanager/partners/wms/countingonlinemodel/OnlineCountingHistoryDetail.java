package nocsystem.indexmanager.partners.wms.countingonlinemodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OnlineCountingHistoryDetail {

//    @JsonProperty("history_id")
//    private Integer historyId;

    @JsonProperty("reason_id")
    private Integer reasonId;

    @JsonProperty("parcel_id")
    private String parcelId;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("quantity")
    private Integer quantity;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("updated_by")
    private String updatedBy;

    @JsonProperty("note")
    private String note;
}
