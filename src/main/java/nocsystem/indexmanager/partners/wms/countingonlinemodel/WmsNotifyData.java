package nocsystem.indexmanager.partners.wms.countingonlinemodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WmsNotifyData {

    @JsonProperty("notify")
    private String notify;

    @JsonProperty("counter_id")
    private String counterId;

    @JsonProperty("physic_counter_id")
    private String physicCounterId;

    @JsonProperty("port_counter_id")
    private String portCounterId;

    @JsonProperty("created_at")
    private String createdAt;
}

