package nocsystem.indexmanager.partners.wms.countingonlinemodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WmsOnlineCountingData {

    @JsonProperty("packed_time")
    private String packedTime;

    @JsonProperty("pack_id")
    private String packId;

    @JsonProperty("counter_id")
    private String counterId;

    @JsonProperty("physic_counter_id")
    private String physicCounter;

    @JsonProperty("port_counter_id")
    private String portCounterId;

    @JsonProperty("total_counter_sensor")
    private Integer totalCounterSensor;

    @JsonProperty("total_counter_wms")
    private Integer totalCounterWms;

    @JsonProperty("total_counter_actual")
    private Integer totalCounterActual;

    @JsonProperty("reason_list_detail")
    private List<OnlineCountingHistoryDetail> countingOnlineHistory;

    @JsonProperty("verifier")
    private String verifier;

    @JsonProperty("postal_code")
    private String postCode;
}

