package nocsystem.indexmanager.partners.wms.countingonlinemodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WmsHistoryData {

    @JsonProperty("quantity")
    private Integer quantity;

    @JsonProperty("updated_at")
    private String updateAt;

    @JsonProperty("reason_list_detail")
    private List<OnlineCountingHistoryDetail> countingOnlineHistory;

    @JsonProperty("verifier")
    private String verifier;
}

