package nocsystem.indexmanager.partners.wms.countingonlinemodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
/*
  Set field name snake case to auto get attribute values
*/
public class CounterReportRequest {

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("page_size")
    private Integer page_size;

    @JsonProperty("pack_id")
    private String pack_id;

    @JsonProperty("counter_id")
    private String counter_id;

    @JsonProperty("physic_counter_id")
    private String physic_counter_id;

    @JsonProperty("port_counter_id")
    private String port_counter_id;

    @JsonProperty("err_reason_id")
    private String err_reason_id;

    @JsonProperty("from")
    @JsonFormat(pattern = "dd/MM/yyyy")
    private String from;

    @JsonProperty("to")
    @JsonFormat(pattern = "dd/MM/yyyy")
    private String to;

    @JsonProperty("user_name")
    private String user_name;

    @JsonProperty("postal_code")
    private String postal_code;

}
