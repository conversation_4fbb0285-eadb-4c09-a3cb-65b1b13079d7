package nocsystem.indexmanager.partners.wms.verifiermodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class VerifierReport {

    @JsonProperty("user_name")
    private String userName;

    @JsonProperty("pack_count")
    private Integer packCount;

    @JsonProperty("parcel_count")
    private Integer parcelCount;

    @JsonProperty("counter_id")
    private String counterId;

    @JsonProperty("physic_counter_id")
    private String physicCounterId;

    @JsonProperty("port_counter_id")
    private String portCounterId;

}
