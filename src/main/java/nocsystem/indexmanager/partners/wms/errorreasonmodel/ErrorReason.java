package nocsystem.indexmanager.partners.wms.errorreasonmodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ErrorReason {

    @JsonProperty("err_reason_id")
    private Integer errorReasonId;

    @JsonProperty("err_reason")
    private String errorReason;

    @JsonProperty("counter_id")
    private String counterId;

    @JsonProperty("physic_counter_id")
    private String physicCounterId;

    @JsonProperty("port_counter_id")
    private String portCounterId;

    @JsonProperty("wrong_count_pack_sensor")
    private Integer wrongCountPackSensor;

    @JsonProperty("wrong_count_parcel_sensor")
    private Integer wrongCountParcelSensor;

    @JsonProperty("wrong_count_pack_wms")
    private Integer wrongCountPackWms;

    @JsonProperty("wrong_count_parcel_wms")
    private Integer wrongCountParcelWms;

}
