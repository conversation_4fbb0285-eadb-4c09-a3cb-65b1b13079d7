package nocsystem.indexmanager.partners.wms.service;

import nocsystem.indexmanager.exception.BadRequestException;
import nocsystem.indexmanager.partners.wms.PagingContent;
import nocsystem.indexmanager.partners.wms.WmsResponse;
import nocsystem.indexmanager.partners.wms.counterstaticmodel.CounterStatics;
import nocsystem.indexmanager.partners.wms.countingonlinemodel.*;
import nocsystem.indexmanager.partners.wms.errorreasonmodel.ErrorReason;
import nocsystem.indexmanager.partners.wms.verifiermodel.VerifierReport;
import org.hibernate.service.spi.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.http.HttpStatus;
import reactor.core.publisher.Mono;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;

import static nocsystem.indexmanager.global.variable.ListVariableLocation.isViewAllCounterWmsReport;

@Service
public class WmsService {
    private final Logger log = LoggerFactory.getLogger(WmsService.class);

    private WebClient webClient;

    private final int TIMEOUT_RESPONSE_EXPORT_EXCEL = 150;

    @Value("${partner.wms.excel.template:}")
    private String filePath;

    @Value("${partner.wms.token:}")
    private String wmsToken;

    @Value("${partner.wms.base-url:}")
    private String wmsBaseUrl;


    @Autowired
    public void init(WebClient.Builder builder){
        webClient = builder.baseUrl(wmsBaseUrl)
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024))
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader("Authorization", "Bearer " +  wmsToken)
                .build();
    }

    private Map<String, List<String>> getParamFromRequest(CounterReportRequest request) throws IllegalAccessException {
        Map<String, List<String>> params = new HashMap<>();
        Field[] fields = request.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(request);
            if(value != null){
                params.put(field.getName(), Collections.singletonList(value.toString()));
            }else{
                params.put(field.getName(), Collections.singletonList(null));
            }
        }
        log.debug("Params: {}", params);
        return params;
    }

    public void exportExcelCountingOnlineReport(CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            return;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        byte[] data = webClient.get().uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/export-excel-counter")
                .queryParams(CollectionUtils.toMultiValueMap(params)).build())
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .retrieve()
                .bodyToMono(byte[].class)
                .doOnError(e -> {
                    log.error("Error when Call Api Export Excel bill WMS because of: {}", e.getLocalizedMessage());
                })
                .timeout(Duration.ofSeconds(TIMEOUT_RESPONSE_EXPORT_EXCEL))
                .block();

        try{
            ServletOutputStream outputStream = response.getOutputStream();
            if(data == null){
                data = new byte[100];
            }
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("{}", e.getLocalizedMessage());
            log.error("{}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }

    public PagingContent<List<WmsOnlineCountingData>> getCountingOnlineReport(CounterReportRequest request) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            List<WmsOnlineCountingData> data = new ArrayList<>();
            PagingContent<List<WmsOnlineCountingData>> res = new PagingContent<>();
            res.setContent(data);
            return res;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        WmsResponse<PagingContent<List<WmsOnlineCountingData>>> response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/counter")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build()
                )
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<WmsResponse<PagingContent<List<WmsOnlineCountingData>>>>() {
                }).block();

        if(response != null && response.getCode() == 200){
            return response.getData();
        }
        return null;
    }

    public List<CounterErrorReason> listCounterErrorReason(){

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
           return new ArrayList<>();
        }
        String path = "cpcms/api/v1/cat-error-reason/list";
        WmsResponse<List<CounterErrorReason>> response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path(path)
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<WmsResponse<List<CounterErrorReason>>>() {
                }).block();

        if(response != null && response.getCode() == 200){
            return response.getData();
        }
        return null;
    }

    public PagingContent<List<CounterStatics>> getCounterStaticsReport(CounterReportRequest request) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            List<CounterStatics> data = new ArrayList<>();
            PagingContent<List<CounterStatics>> res = new PagingContent<>();
            res.setContent(data);
            return res;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        WmsResponse<PagingContent<List<CounterStatics>>> response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/counter-statics")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build()).retrieve().bodyToMono(new ParameterizedTypeReference<WmsResponse<PagingContent<List<CounterStatics>>>>() {
                }).block();

        if(response != null && response.getCode() == 200){
            return response.getData();
        }

        return null;
    }

    public void exportExcelCounterStaticsReport(CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            return;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        byte[] data = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/export-excel-statics-counter")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .retrieve()
                .bodyToMono(byte[].class)
                .doOnError(e -> {
                    log.error("Error when Call Api Export Excel Counter Report because of: {}", e.getLocalizedMessage());
                })
                .timeout(Duration.ofSeconds(TIMEOUT_RESPONSE_EXPORT_EXCEL))
                .block();

        try{
            ServletOutputStream outputStream = response.getOutputStream();
            if(data == null){
                data = new byte[100];
            }
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("{}", e.getLocalizedMessage());
            log.error("{}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }

    public void exportExcelErrorReasonReport(CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {
        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            return;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        byte[] data = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/export-excel-err-reason")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .retrieve()
                .bodyToMono(byte[].class)
                .doOnError(e -> {
                    log.error("Error when Call Api Export Excel Error Reason Report because of: {}", e.getLocalizedMessage());
                })
                .timeout(Duration.ofSeconds(TIMEOUT_RESPONSE_EXPORT_EXCEL))
                .block();

        try{
            ServletOutputStream outputStream = response.getOutputStream();
            if(data == null){
                data = new byte[100];
            }
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("{}", e.getLocalizedMessage());
            log.error("{}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }


    }

    public PagingContent<List<ErrorReason>> getErrorReasonReport(CounterReportRequest request) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            List<ErrorReason> data = new ArrayList<>();
            PagingContent<List<ErrorReason>> res = new PagingContent<>();
            res.setContent(data);
            return res;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        WmsResponse<PagingContent<List<ErrorReason>>> response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/err-reason")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<WmsResponse<PagingContent<List<ErrorReason>>>>() {
        }).block();

        if(response != null && response.getCode() == 200){
            return response.getData();
        }

        return null;
    }

    public PagingContent<List<VerifierReport>> getVerifierReport(CounterReportRequest request) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            List<VerifierReport> data = new ArrayList<>();
            PagingContent<List<VerifierReport>> res = new PagingContent<>();
            res.setContent(data);
            return res;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        WmsResponse<PagingContent<List<VerifierReport>>> response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/verifier")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<WmsResponse<PagingContent<List<VerifierReport>>>>() {
                }).block();

        if(response != null && response.getCode() == 200){
            return response.getData();
        }

        return null;
    }

    public void exportExcelVerifierReport(CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {
        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            return;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        byte[] data = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/export-excel-verifier")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .retrieve()
                .bodyToMono(byte[].class)
                .doOnError(e -> {
                    log.error("Error when Call Api Export Excel Error Reason Report because of: {}", e.getLocalizedMessage());
                })
                .timeout(Duration.ofSeconds(TIMEOUT_RESPONSE_EXPORT_EXCEL))
                .block();

        try{
            ServletOutputStream outputStream = response.getOutputStream();
            if(data == null){
                data = new byte[100];
            }
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("{}", e.getLocalizedMessage());
            log.error("{}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }

    public PagingContent<List<WmsHistoryData>> getHistoryData(CounterReportRequest request) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            List<WmsHistoryData> data = new ArrayList<>();
            PagingContent<List<WmsHistoryData>> res = new PagingContent<>();
            res.setContent(data);
            return res;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        WmsResponse<PagingContent<List<WmsHistoryData>>> response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/counter-report/history-list")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<WmsResponse<PagingContent<List<WmsHistoryData>>>>() {
                }).block();

        if(response != null && response.getCode() == 200){
            return response.getData();
        }
        return null;
    }

    public WmsResponse<List<PreviewExcelData>> sendExcelFile(final MultipartFile multipartFile, String name) {
        final URI url = UriComponentsBuilder.fromHttpUrl(wmsBaseUrl + "/cpcms/api/v1/counter-report/import-counter-reason-excel").build().toUri();

        final MultipartBodyBuilder builder = new MultipartBodyBuilder();
        builder.part("file", multipartFile.getResource());
        builder.part("user_name", name);

        WmsResponse<List<PreviewExcelData>> response = webClient.post()
                .uri(url)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(builder.build()))
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<WmsResponse<List<PreviewExcelData>>>() {})
                .block();
//        if(response.getData() == null){
//            return Arrays.asList(response.getMessage());
//        }
        if(response != null && response.getCode() == 200){
            return response;
        }
        return null;
    }

    public PagingContent<List<WmsNotifyData>> getNotiData(CounterReportRequest request) throws IllegalAccessException {

        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            List<WmsNotifyData> data = new ArrayList<>();
            PagingContent<List<WmsNotifyData>> res = new PagingContent<>();
            res.setContent(data);
            return res;
        }
        Map<String, List<String>> params = getParamFromRequest(request);
        WmsResponse<PagingContent<List<WmsNotifyData>>> response = webClient.get()
                .uri(uriBuilder -> uriBuilder.path("cpcms/api/v1/noc-threshold-notify")
                        .queryParams(CollectionUtils.toMultiValueMap(params))
                        .build())
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<WmsResponse<PagingContent<List<WmsNotifyData>>>>() {
                }).block();

        if(response != null && response.getCode() == 200){
            return response.getData();
        }
        return null;
    }

    public void exportSampleExcel( HttpServletResponse response) throws IllegalAccessException, IOException {
        if(!isViewAllCounterWmsReport.equalsIgnoreCase("true")){
            return;
        }
        byte[] data =  Files.readAllBytes(Paths.get(filePath));

        try{
            ServletOutputStream outputStream = response.getOutputStream();
            if(data == null){
                data = new byte[100];
            }
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("{}", e.getLocalizedMessage());
            log.error("{}", e.getMessage());
            throw new BadRequestException(e.getMessage());
        }
    }
}
