package nocsystem.indexmanager.partners.wms.counterstaticmodel;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CounterStatics {

    @JsonProperty("historyCounts")
    private List<CountingHistory> countingHistories = new ArrayList<>();

    @JsonProperty("counter_id")
    private String counterId;

    @JsonProperty("physic_counter_id")
    private String physicCounterId;

    @JsonProperty("port_counter_id")
    private String portCounterId;

    @JsonProperty("counter_life_time")
    private Integer counterLifeTime;

    @JsonProperty("error_occur_time")
    private Double errorOccurTime;

    @JsonProperty("wrong_ratio_wms")
    private Double wrongRatioWms;

    @JsonProperty("wrong_ratio_sensor")
    private Double wrongRatioSensor;

    @JsonProperty("correct_count_wms")
    private Integer correctCountWms;

    @JsonProperty("wrong_count_wms")
    private Integer wrongCountWms;

    @JsonProperty("correct_count_sensor")
    private Integer correctCountSensor;

    @JsonProperty("wrong_count_sensor")
    private Integer wrongCountSensor;

    @JsonProperty("created_at")
    private String createdAt;

}
