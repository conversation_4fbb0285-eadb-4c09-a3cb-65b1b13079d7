package nocsystem.indexmanager.partners.wms.controller;

import nocsystem.indexmanager.config.ListContentPageDto;
import nocsystem.indexmanager.config.SimpleAPIResponse;
import nocsystem.indexmanager.partners.wms.PagingContent;
import nocsystem.indexmanager.partners.wms.WmsResponse;
import nocsystem.indexmanager.partners.wms.counterstaticmodel.CounterStatics;
import nocsystem.indexmanager.partners.wms.countingonlinemodel.*;
import nocsystem.indexmanager.partners.wms.errorreasonmodel.ErrorReason;
import nocsystem.indexmanager.partners.wms.service.WmsService;
import nocsystem.indexmanager.partners.wms.verifiermodel.VerifierReport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/v1/partner/wms")
public class WmsController {

    private final Logger log = LoggerFactory.getLogger(WmsController.class);

    @Autowired
    private WmsService wmsService;

    @PostMapping("/counter-report/online-counting-report")
    public ResponseEntity<SimpleAPIResponse> getCountingOnlineReport(@RequestBody CounterReportRequest request) throws IllegalAccessException {

        PagingContent<List<WmsOnlineCountingData>> data = wmsService.getCountingOnlineReport(request);

        if (data != null) {
            ListContentPageDto<WmsOnlineCountingData> content = new ListContentPageDto<>(data.getTotal(), data.getOffset(), data.getLimit(), data.getContent());
            return ResponseEntity.ok(new SimpleAPIResponse(content));
        }
        return ResponseEntity.ok(null);
    }

    @PostMapping("/counter-report/online-counting-report/export-excel")
    public void exportExcelOnlineCountingReport(@RequestBody CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=OnlineCountingReport.xlsx";
        response.setHeader(headerKey, headerValue);

        wmsService.exportExcelCountingOnlineReport(request, response);

    }

    @GetMapping("/counter-report/cat-error-reason")
    public ResponseEntity<SimpleAPIResponse> getListCounterErrorReason() {

        List<CounterErrorReason> data = wmsService.listCounterErrorReason();
        SimpleAPIResponse response = new SimpleAPIResponse(data);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/counter-report/counter-statics")
    public ResponseEntity<SimpleAPIResponse> getCounterStatics(@RequestBody CounterReportRequest request) throws IllegalAccessException {

        PagingContent<List<CounterStatics>> data = wmsService.getCounterStaticsReport(request);
        if (data != null) {
            ListContentPageDto<CounterStatics> content = new ListContentPageDto<>(data.getTotal(), data.getOffset(), data.getLimit(), data.getContent());
            return ResponseEntity.ok(new SimpleAPIResponse(content));
        }
        return ResponseEntity.ok(null);
    }

    @PostMapping("/counter-report/counter-statics/export-excel")
    public void exportExcelCounterStaticsReport(@RequestBody CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {

        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=CounterStaticsReport.xlsx";
        response.setHeader(headerKey, headerValue);

        wmsService.exportExcelCounterStaticsReport(request, response);
    }

    @PostMapping("/counter-report/error-reason")
    public ResponseEntity<SimpleAPIResponse> getErrorReasonReport(@RequestBody CounterReportRequest request) throws IllegalAccessException {

        PagingContent<List<ErrorReason>> data = wmsService.getErrorReasonReport(request);
        if (data != null) {
            ListContentPageDto<ErrorReason> content = new ListContentPageDto<>(data.getTotal(), data.getOffset(), data.getLimit(), data.getContent());
            return ResponseEntity.ok(new SimpleAPIResponse(content));
        }
        return ResponseEntity.ok(null);
    }

    @PostMapping("/counter-report/error-reason/export-excel")
    public void exportExcelErrorReasonReport(@RequestBody CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=ErrorReasonReport.xlsx";
        response.setHeader(headerKey, headerValue);

        wmsService.exportExcelErrorReasonReport(request, response);
    }

    @PostMapping("/counter-report/verifier")
    public ResponseEntity<SimpleAPIResponse> getVerifierReport(@RequestBody CounterReportRequest request) throws IllegalAccessException {

        PagingContent<List<VerifierReport>> data = wmsService.getVerifierReport(request);
        if (data != null) {
            ListContentPageDto<VerifierReport> content = new ListContentPageDto<>(data.getTotal(), data.getOffset(), data.getLimit(), data.getContent());
            return ResponseEntity.ok(new SimpleAPIResponse(content));
        }
        return ResponseEntity.ok(null);
    }

    @PostMapping("/counter-report/verifier/export-excel")
    public void exportExcelVerifierReport(@RequestBody CounterReportRequest request, HttpServletResponse response) throws IllegalAccessException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=VerifierReport.xlsx";
        response.setHeader(headerKey, headerValue);

        wmsService.exportExcelVerifierReport(request, response);
    }

    @PostMapping("/counter-report/history-list")
    public ResponseEntity<SimpleAPIResponse> getHistoryList(@RequestBody CounterReportRequest request) throws IllegalAccessException {

        PagingContent<List<WmsHistoryData>> data = wmsService.getHistoryData(request);
        if (data != null) {
            ListContentPageDto<WmsHistoryData> content = new ListContentPageDto<>(data.getTotal(), data.getOffset(), data.getLimit(), data.getContent());
            return ResponseEntity.ok(new SimpleAPIResponse(content));
        }
        return ResponseEntity.ok(null);
    }

    @PostMapping(value = "/counter-report/import-counter-reason-excel", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<SimpleAPIResponse> uploadExcel(
            @RequestPart MultipartFile file,
            @RequestPart(value = "user_name") String name
    ) throws Exception {
        SimpleAPIResponse response = new SimpleAPIResponse();

        try {
            WmsResponse<List<PreviewExcelData>> data = wmsService.sendExcelFile(file, name);
            response.setMessage(data.getMessage());
            response.setData(data.getData());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setData("");
        }

        return ResponseEntity.ok(response);
    }

    @PostMapping("/counter-report/noc-threshold-notify")
    public ResponseEntity<SimpleAPIResponse> getNotiData(@RequestBody CounterReportRequest request) throws IllegalAccessException {

        PagingContent<List<WmsNotifyData>> data = wmsService.getNotiData(request);
        if (data != null) {
            ListContentPageDto<WmsNotifyData> content = new ListContentPageDto<>(data.getTotal(), data.getOffset(), data.getLimit(), data.getContent());
            return ResponseEntity.ok(new SimpleAPIResponse(content));
        }
        return ResponseEntity.ok(null);
    }

    @GetMapping("counter-report/download-sample")
    public void getFile(HttpServletResponse response) throws IOException, IllegalAccessException {
        response.setContentType("application/vnd.ms.excel");
        String headerKey = "Content-Disposition";
        String headerValue = "attachment; filename=CounterReportHistorySample.xlsx";
        response.setHeader(headerKey, headerValue);

        wmsService.exportSampleExcel(response);
    }
}
