package nocsystem.indexmanager.partners.wms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PagingContent<T> {

    @JsonProperty("total")
    private long total;

    @JsonProperty("offset")
    private long offset;

    @JsonProperty("limit")
    private int limit;

    @JsonProperty("content")
    private T content;
}
