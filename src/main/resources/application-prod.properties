spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.idle-timeout=10000
spring.datasource.hikari.max-lifetime=100000

spring.datasource.url=
spring.datasource.username=
spring.datasource.password=

spring.jpa.show-sql=true
#spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect

server.servlet.context-path=/index-manager
spring.application.name=index-manager
eureka.instance.prefer-ip-address=true
eureka.client.service-url.defaultZone = *************************************/eureka
spring.cloud.config.fail-fast=false
spring.cloud.config.retry.initial-interval=1000
spring.cloud.config.retry.max-interval=2000
spring.cloud.config.retry.max-attempts=100
spring.cloud.config.uri=*************************************/config
spring.cloud.config.name =index-manager
spring.cloud.config.profile=prod # profile(s) of the property source
spring.cloud.config.label=main
server.port=7070

spring.mvc.pathmatch.matching-strategy=ant-path-matcher
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logstash.host=**************:5000

quality-server.domain=http://*************:8084

# ==============> K?t n?i SupperSet
# DB noc_index_manager
noc-index-manager.url=
noc-index-manager.username=
noc-index-manager.password=

# DB noc_base
noc-base-url=
noc-base-username=${noc-index-manager.username}
noc-base-password=${noc-index-manager.password}

# DB noc_network_monitoring
noc-network-monitoring.url=
noc-network-monitoring.username=${noc-index-manager.username}
noc-network-monitoring.password=${noc-index-manager.password}

#noc_quality
noc-quality.url=
noc-quality.username=${noc-index-manager.username}
noc-quality.password=${noc-index-manager.password}

#Supper set
superset-server.domain=http://**************:30900
superset-server.username=
superset-server.password=
superset-server.provider=
#List of prefix super-set
database.prefix=noc-index-manager,noc-quality,noc-base,noc-network-monitoring

#Metric logs
management.endpoints.web.exposure.include=health,info,prometheus

server.netty.connection-timeout=150s
server.tomcat.connection-timeout=150s
server.tomcat.max-swallow-size=-1
server.servlet.session.timeout=180s

# Config rabbit-mq
spring.rabbitmq.host=**************
spring.rabbitmq.port=5672
spring.rabbitmq.username=noc-admin
spring.rabbitmq.password=noc-admin

# Config message
message-activity-log.queue.name=activity
message-activity-log.exchange.name=activity_exchange
message-activity-log.routing.key=activity_routing_key

#config logger
message-logger.queue.name=contributed_logger
message-logger.exchange.name=contributed_logger_exchange
message-logger.routing.key=contributed_logger_routing_key
service.name=index-manager

server.connection.timeout=180000

#Web client config
webclient.proxy.enabled=false
webclient.proxy.host=*************
webclient.proxy.port=8080
webclient.ssl.ignore=false

#DWS config
partner.dws.token=eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJCVlN3RUFKbGJwVXFaTHJ6V2VPS3N1MmxkRWhUUmt1VjlWY1ZpelZVaGR3In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZbFsI8u2rpJPPGYl-IaeveOTeN5ou4cqtJzBD1PQqT7r0MQt7mlthOuN2aVeMvnGvvXEO47lssn3qfo1mbx8pAVBA4kDK-NFT3DCm7lvJ9Yr1ohdHbJAP9G793mQnl7ti_1qZuf4tU5E34kIAPLn_fMrp43QlnKEXSsVCJ6nOCgV7S6h4yM43FxWT71Wqfd8Y4RHYsc16pxu6MOIVfm5sjCEffuSprvMj7zWFfK-JsgF-Mpr-jdl0oh09xE_tc-KFRz2jNf2bEHyGluytJnr-xPdo0qk1vi0icNbBd9RPY2ZNP68IErRBKJ_f_0UjsMOCq80ZebzTShLkGCoVy4zow
partner.dws.base-url=https://gw.viettelpost.vn/dws

#WMS
partner.wms.token=
partner.wms.base-url=https://cpc.viettelpost.vn
partner.wms.excel.template=/opt/noc/noc-index-manager-module/app/template/excel/wms/CounterReportExcelSample.xlsx

# Maximum file size allowed for upload (in bytes)
spring.servlet.multipart.max-file-size=25MB
# Maximum request size allowed (in bytes)
spring.servlet.multipart.max-request-size=25MB
# Directory of storage
storage.file.directory=/home/<USER>/NOC-PROJECT/storage

#config connection hirakipool
db-min-connections=10
db-max-connections=20
db-time-out=60000
db-max-life_time=10000
db-max-connection-time-out=30000

#check-instance
instance.ip=localhost

#========================> BEGIN SET UP REDIS
#mode redis
redis.mode=

#Setup replica
redis.master.host=localhost
redis.master.port=6379

redis.slaves[0].host=localhost
redis.slaves[0].port=6379

redis.slaves[1].host=localhost
redis.slaves[1].port=6379

#Setup Redis Standalone
redis.host=*************
redis.port=6379
redis.password=noc-redis

#Setup Redis Cluster
spring.redis.cluster.nodes=*************:6001,*************:6002,*************:6004,*************:6003,*************:6005,*************:6006
spring.redis.cluster.max-redirects=3
#========================> AND SET UP REDIS

#Phoenix Connection
phoenix.config.connection=*************************,************,************
server.max-http-header-size=100000

service_name=noc-staging
application_packages=com.viettelpost.core
server_urls=https://apm-public.viettelpost.vn
capture_body=all
capture_headers=true
ignore_urls=
active=true
log_level=INFO

#Kafka config
spring.kafka.bootstrap-servers=*************:9092
topic_error_telegram=message_logger_exchange_name
group_error_telegram=group_error_telegram
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
