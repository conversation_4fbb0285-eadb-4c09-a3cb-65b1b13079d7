-- public.cskd_tiendodoanhthu_cp definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_cp;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_cp (
	dich_vu varchar NOT NULL,
	ngay_baocao date NOT NULL,
	tlht float4 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tiendo float8 NULL,
	del_flag int2 NULL DEFAULT 0,
	created timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT cskdc_tiendodoanhthu_cp_pkey PRIMARY KEY (ngay_baocao, dich_vu)
);

-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN cskd_tiendodoanhthu_cp.dich_vu IS 'NoN-COD, COD, EXP';

-- Add indexes
create index INDEX_TDDT_CP_NGAY_BAO_CAO
    on cskd_tiendodoanhthu_cp (ngay_baocao, dich_vu);