-- public.cskd_tong_doanh_thu_cn definition

-- Drop table

-- DROP TABLE public.cskd_tong_doanh_thu_cn;

CREATE TABLE IF NOT EXISTS cskd_tongdoanhthu_cn (
	ma_chinhanh varchar(50) NOT NULL,
    ten_chinhanh varchar(50) NOT NULL,
	ngay_baocao date NOT NULL,
	tong_doanh_thu float8 NULL,
	tlht float4 NULL,
	tt_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_tbn_nam float4 NULL,
	tiendo float8 NULL,
	thuc_hien float8 NULL,
	ke_hoach float8 NULL,
	thangtruoc float8 NULL,
	namtruoc float8 NULL,
	cung_ky_ngay float8 NULL,
	cung_ky_thang float8 NULL,
	cung_ky_nam float8 NULL,
	del_flag int4 NULL DEFAULT 0,
	created timestamp NULL,
	CONSTRAINT cskd_tongdoanhthu_cn_pkey PRIMARY KEY (ngay_baocao, ma_chinhanh)
);

-- Add Indexes
create index INDEX_FOR_CSKD_TDT_CN_NGAY_BAO_CAO_MA_CN
    on cskd_tongdoanhthu_cn (ngay_baocao, ma_chinhanh);