-- public.cskd_tiendodoanhthu_logistic definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_logistic;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_logistic (
	dich_vu varchar NOT NULL,
	ngay_baocao date NOT NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	CONSTRAINT cskd_tiendodoanhthu_logistic_pkey PRIMARY KEY (dich_vu, ngay_baocao)
);

COMMENT ON COLUMN cskd_tiendodoanhthu_logistic.dich_vu IS 'FORWARDING, KHO-VAN';

-- Add Indexes
create index INDEX_CSKD_TDDT_LG_LOAI_DICH_VU
    on cskd_tiendodoanhthu_logistic (ngay_baocao, dich_vu);