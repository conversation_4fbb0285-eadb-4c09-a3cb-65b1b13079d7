-- public.cskd_tiendodoanhthu_logistic_bc definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_logistic_bc;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_logistic_bc (
	ma_bu<PERSON><PERSON> varchar(50) NOT NULL,
	ma_chin<PERSON>h varchar(50) NOT NULL DEFAULT ''::character varying,
	dich_vu varchar NOT NULL,
	tlht float4 NOT NULL,
	tiendo float8 NOT NULL,
	tt_thang float4 NOT NULL,
	tt_tbn_thang float4 NOT NULL,
	tt_nam float4 NOT NULL,
    thangtruoc float8 NULL,
    namtruoc float8 NULL,
    cung_ky_ngay float8 NULL,
    cung_ky_thang float8 NULL,
    cung_ky_nam float8 NULL,
	del_flag int4 NULL DEFAULT 0,
	ngay_baocao date NOT NULL,
	created timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT cskd_tiendodoanhthu_logistic_bc_pkey PRIMARY KEY (ngay_baoca<PERSON>, ma_chin<PERSON>h, ma_buucuc, dich_vu)
);

COMMENT ON COLUMN cskd_tiendodoanhthu_logistic_bc.dich_vu IS 'FORWARDING, KHO-VAN';

-- Add Indexes
create index INDEX_CSKD_TDDT_LG_BC_LOAI_DICH_VU
    on cskd_tiendodoanhthu_logistic_bc (ngay_baocao, dich_vu);
create index INDEX_CSKD_TDDT_LG_BC_CN_BC
    on cskd_tiendodoanhthu_logistic_bc (ngay_baocao, ma_chinhanh, ma_buucuc);

-- Add column thuc_hien, ke_hoach
Alter table cskd_tiendodoanhthu_logistic_bc add column thuc_hien float8 NULL,
    add column ke_hoach float8 NULL;