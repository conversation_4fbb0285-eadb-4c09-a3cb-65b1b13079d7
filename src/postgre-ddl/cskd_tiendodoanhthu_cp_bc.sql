-- public.cskd_tiendodoanhthu_cp_bc definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_cp_bc;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_cp_bc (
	ma_buuc<PERSON> varchar(50) NOT NULL,
	ma_chin<PERSON><PERSON> varchar(50) NOT NULL,
	loai_dichvu varchar(50) NOT NULL, -- NoN-COD, COD, EXP
	tlht float4 NULL,
	thuc_hien float8 NULL,
	ke_hoach float8 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	thangtruoc float8 NULL,
	namtruoc float8 NULL,
	cung_ky_ngay float8 NULL,
	cung_ky_thang float8 NULL,
	cung_ky_nam float8 NULL,
	del_flag int4 NULL DEFAULT 0,
	ngay_baocao date NOT NULL,
	created timestamp NULL,
	CONSTRAINT cskd_tiendodoanhthu_cp_bc_pkey PRIMARY KEY (ma_chinhanh, ngay_baocao, ma_buucuc, loai_dichvu)
);

-- Column comments
COMMENT ON COLUMN public.cskd_tiendodoanhthu_cp_bc.loai_dichvu IS 'NoN-COD, COD, EXP';

-- Add indexes
create index INDEX_TDDT_CP_BC_NGAY_BAO_CAO
    on cskd_tiendodoanhthu_cp_bc (ngay_baocao);
create index INDEX_TDDT_CP_BC_MA_CHI_NHANH_BC
    on cskd_tiendodoanhthu_cp_bc (ngay_baocao, ma_chinhanh, ma_buucuc);
create index INDEX_TDDT_CP_BC_LOAI_DICH_VU
    on cskd_tiendodoanhthu_cp_bc (ngay_baocao, loai_dichvu);

-- Add column thuc_hien, ke_hoach
Alter table cskd_tiendodoanhthu_cp_cn add column thuc_hien float8 NULL,
    add column ke_hoach float8 NULL;