-- public.cskd_tiendothluyke_bc definition

-- Drop table

-- DROP TABLE public.cskd_tiendothluyke_bc;

CREATE TABLE IF NOT EXISTS cskd_tiendothluyke_bc (
	mien varchar(20) NULL,
	chinhanh varchar(20) NULL,
	buucuc varchar(20) NOT NULL,
	ngay_baocao date NOT NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_nam float4 NULL,
	danhgia int2 NULL,
	lkthang float8 NULL,
	khthang float8 NULL,
	thangtruoc float8 NULL,
	namtruoc float8 NULL,
	cung_ky_ngay float8 NULL,
	cung_ky_thang float8 NULL,
	cung_ky_nam float8 NULL,
	CONSTRAINT cskd_tiendothluyke_bc_pkey PRIMARY KEY (buucuc, ngay_baocao)
);

-- Add Indexes
create index INDEX_CSKD_TD_LUY_KE_BC_NGAY_BAO_CAO
    on cskd_tiendothluyke_bc (ngay_baocao, mien, chinhanh);
create index INDEX_CSKD_TD_LUY_KE_BC_CHI_NHANH_BC
    on cskd_tiendothluyke_bc (ngay_baocao, chinhanh, buucuc);