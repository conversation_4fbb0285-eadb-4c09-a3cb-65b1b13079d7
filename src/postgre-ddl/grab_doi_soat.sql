-- public.grab_doi_soat definition

-- Drop table

-- DROP TABLE public.grab_doi_soat;

CREATE TABLE public.grab_doi_soat
(
    ma_vandon              varchar(60) NOT NULL,
    ngay_nhap_may          timestamp NULL,
    ngay_phat_thanhcong    timestamp NULL,
    order_id               int8 NULL,
    khoang_cach            float8 NULL,
    ma_cn_goc              varchar(60) NULL,
    ten_cn_goc             varchar(100) NULL,
    ma_bc_goc              varchar(60) NULL,
    ten_bc_goc             varchar(100) NULL,
    ma_kh_goc              varchar(60) NULL,
    ten_kh_goc             varchar(300) NULL,
    ma_cn_phat             varchar(60) NULL,
    ten_cn_phat            varchar(100) NULL,
    ma_bc_phat             varchar(60) NULL,
    ten_bc_phat            varchar(100) NULL,
    ten_kh_nhan            varchar(300) NULL,
    vung                   varchar(60) NULL,
    ma_dichvu_viettel      varchar(30) NULL,
    trong_luong            numeric(32, 8) NULL,
    vtp_trangthai_cuoicung int4 NULL,
    vtp_tongtien           numeric(32, 8) NULL,
    vtp_cuoc_chieudi       numeric(32, 8) NULL,
    vtp_phuphi             int8 NULL,
    vtp_chietkhau          int8 NULL,
    grab_trangthai         int4 NULL,
    grab_tongtien          numeric(32, 8) NULL,
    grab_cuoc_chieudi      numeric(32, 8) NULL,
    grab_phuphi            numeric(32, 8) NULL,
    grab_chieukhau         numeric(32, 8) NULL,
    chenhlech_tongtien     int8 NULL,
    chenhlech_cuoc_chieudi int8 NULL,
    chenhlech_phuphi       int8 NULL,
    chenhlech_chietkhau    int8 NULL,
    ghi_chu                varchar(200) NULL,
    trangthai_thanhcong    int4 NULL,
    file_id                int4 NULL DEFAULT 0,
    reconciliation_vtp_status int2 DEFAULT 0,
    CONSTRAINT doi_soat_grab_pk PRIMARY KEY (ma_vandon)
);