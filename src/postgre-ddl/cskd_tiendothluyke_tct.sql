-- public.cskd_tiendothluyke_tct definition

-- Drop table

-- DROP TABLE public.cskd_tiendothluyke_tct;

CREATE TABLE IF NOT EXISTS cskd_tiendothluyke_tct (
	mien varchar(20) NULL,
	chinhanh varchar(20) NOT NULL,
	ngay_baocao date NOT NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_nam float4 NULL,
	danhgia int2 NULL,
	CONSTRAINT cskd_tiendothluyke_tct_pkey PRIMARY KEY (ngay_baocao, chinhanh)
);

-- Add Indexes
create index INDEX_CSKD_TD_LUY_KE_TCT_FOR_NGAY_BAO_CAO
    on cskd_tiendothluyke_tct (ngay_baocao, mien);
create index INDEX_CSKD_TD_LUY_KE_TCT_CHI_NHANH
    on cskd_tiendothluyke_tct (ngay_baocao, chinhanh);