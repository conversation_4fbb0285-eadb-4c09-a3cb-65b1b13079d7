-- public.cskd_tiendodoanhthu_logistic_cn definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_logistic_cn;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_logistic_cn (
	ma_chin<PERSON><PERSON> varchar(50) NOT NULL,
	dich_vu varchar(50) NOT NULL, -- FORWARDING: loại Forwarding, KHO-VAN: loại kho vận
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	thangtruoc float8 NULL,
	namtruoc float8 NULL,
	cung_ky_ngay float8 NULL,
	cung_ky_thang float8 NULL,
	cung_ky_nam float8 NULL,
	del_flag int4 NULL DEFAULT 0,
	ngay_baocao date NOT NULL,
	created timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT cskd_tiendodoanhthu_logistic_cn_pkey PRIMARY KEY (ma_chin<PERSON>h, ngay_baocao, dich_vu)
);

-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN cskd_tiendodoanhthu_logistic_cn.dich_vu IS 'FORWARDING: loại Forwarding, KHO-VAN: loại kho vận';

-- Add Indexes
create index INDEX_CSKD_TDDT_LG_CN_LOAI_DICH_VU
    on cskd_tiendodoanhthu_logistic_cn (ngay_baocao, dich_vu);
create index INDEX_CSKD_TDDT_LG_CN_CN_BC
    on cskd_tiendodoanhthu_logistic_cn (ngay_baocao, ma_chinhanh);

-- Add column thuc_hien, ke_hoach
Alter table cskd_tiendodoanhthu_logistic_cn add column thuc_hien float8 NULL,
    add column ke_hoach float8 NULL;