create table dmsanluongbuuta
(
    ma_buu_cuc               varchar(255),
    ma_chi_nhanh             varchar(255),
    ma_nv                    varchar(255),
    ngay_baocao              date,
    tong_don_dang_giao       int4,
    tong_don_dang_nhan       int4,
    tong_don_delay_giao_hang int4,
    tong_don_delay_nhan      int4,
    tong_don_giao_tc         int4,
    tong_don_huy_nhan       int4,
    tong_don_nhan_tc         int4,
    primary key (ma_buu_cuc,ma_chi_nhanh,ma_nv,ngay_baocao)

);

alter table dmsanluongbuuta
    owner to noc;

create index dmsanluongbuuta_ma_buu_cuc_index
    on dmsanluongbuuta (ma_buu_cuc);

create index dmsanluongbuuta_ma_chi_nhanh_index
    on dmsanluongbuuta (ma_chi_nhanh);

create index dmsanluongbuuta_ma_nv_index
    on dmsanluongbuuta (ma_nv);

create index dmsanluongbuuta_ngay_baocao_index
    on dmsanluongbuuta (ngay_baocao);