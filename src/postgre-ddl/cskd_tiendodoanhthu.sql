-- public.cskd_tiendodoanhthu definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu (
	nhom_doanhthu varchar(20) NOT NULL, -- DT-CP: loại DT CP, DT-LOGISTIC: loại logistic
	ke_hoach float8 NULL,
	thuc_hien float8 NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_nam float4 NULL,
	del_flag int2 NULL DEFAULT 0,
	ngay_baocao date NOT NULL,
	created timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT cskd_tiendodoanhthu_pkey PRIMARY KEY (nhom_doanhthu, ngay_baocao)
);

-- <PERSON><PERSON>n comments

COMMENT ON COLUMN cskd_tiendodoanhthu.nhom_doanhthu IS 'DT-CP: loại DT CP, DT-LOGISTIC: loại logistic';

-- Add indexes
create index INDEX_CSKD_TIENDODOANHTHU_NGAY_BAO_CAO
    on cskd_tiendodoanhthu (ngay_baocao);
create index INDEX_NHOM_DOANH_THU
    on cskd_tiendodoanhthu (ngay_baocao, nhom_doanhthu);