-- public.cskd_tong_doanh_thu_bc definition

-- Drop table

-- DROP TABLE public.cskd_tong_doanh_thu_bc;

CREATE TABLE IF NOT EXISTS cskd_tongdoanhthu_bc (
	ma_buuc<PERSON> varchar(50) NOT NULL,
	ma_chinhanh varchar(50) NOT NULL,
	ngay_baocao date NOT NULL,
	tong_doanh_thu float8 NULL,
	tlht float4 NULL,
	tt_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_tbn_nam float4 NULL,
    thangtruoc float8 NULL,
    namtruoc float8 NULL,
    cung_ky_ngay float8 NULL,
    cung_ky_thang float8 NULL,
    cung_ky_nam float8 NULL,
	del_flag int4 NULL DEFAULT 0,
	created timestamp NULL,
	CONSTRAINT cskd_tongdoanhthu_bc_pkey PRIMARY KEY (ngay_baocao, ma_chinhanh, ma_buucuc)
);

-- Add Indexes
create index INDEX_FOR_CSKD_TDT_BC_NGAY_BAO_CAO_MA_CN_BC
    on cskd_tongdoanhthu_bc (ngay_baocao, ma_chinhanh, ma_buucuc);
create index INDEX_FOR_CSKD_TDT_BC_NGAY_BAO_CAO_MA_BC
    on cskd_tongdoanhthu_bc (ngay_baocao, ma_buucuc);