-- public.cskd_tongdoanhthu definition

-- Drop table

-- DROP TABLE public.cskd_tongdoanhthu;

CREATE TABLE IF NOT EXISTS cskd_tongdoanhthu (
	tl_doanhthu float4 NULL,
	ke_hoach float8 NULL,
	thuc_hien float8 NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_nam float4 NULL,
	del_flag int2 NULL DEFAULT 0,
	ngay_baocao date NOT NULL,
	created timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT cskd_tongdoanhthu_pkey PRIMARY KEY (ngay_baocao)
);

-- Add Indexes
create index INDEX_CSKD_TDT_NGAY_BAO_CAO
    on cskd_tongdoanhthu (ngay_baocao);