-- public.import_exel_reconciliation definition

-- Drop table

-- DROP TABLE public.import_exel_reconciliation;

CREATE TABLE public.import_exel_reconciliation
(
    id          serial4 NOT NULL,
    file_name   varchar(50) NULL,
    directory   varchar(255) NULL,
    created_by  varchar(50) NULL,
    type_upload int2 NULL,
    created_at  timestamp NULL,
    data_syn    int2 NULL DEFAULT 0,
    updated_at  timestamp NULL,
    CONSTRAINT import_exel_reconciliation_pkey PRIMARY KEY (id)
);

alter table import_exel_reconciliation
    owner to noc;

CREATE INDEX import_exel_reconciliation_file_name_created_at ON public.import_exel_reconciliation USING btree (created_at);
CREATE INDEX import_exel_reconciliation_file_name_index ON public.import_exel_reconciliation USING btree (file_name);

-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN import_exel_reconciliation.type_upload IS '1: UPLOAD_CORRECT, 0: UPLOAD_ERROR';
COMMENT ON COLUMN import_exel_reconciliation.data_syn IS '1: SUCCESS INTEGRATE TO grab_doi_soat';