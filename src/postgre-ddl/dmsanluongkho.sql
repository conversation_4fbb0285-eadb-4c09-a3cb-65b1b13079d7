create table dmsanluongkho
(
    ma_buu_cuc                    varchar(255),
    ma_tinh                       varchar(255),
    ngay_baocao                   date,
    sl_don_da_giao_tc             int4,
    sl_don_da_phan_cong_giao      int4,
    sl_don_lay_nhan_tc            int4,
    sl_don_lay_ton_nhan           int4,
    sl_don_ton_giao               int4,
    sl_don_ton_phan_cong_nhan     int4,
    sl_don_ton_xuat_tt_hub_sub_bc int4,
    sl_don_ton_xuat_tt_ttkt       int4,
    ten_tinh                      varchar(255),
     primary key (ma_buu_cuc, ngay_baocao,ma_tinh)
);

alter table dmsanluongkho
    owner to noc;

create index dmsanluongkho_ma_buu_cuc_index
    on dmsanluongkho (ma_buu_cuc);

create index dmsanluongkho_ma_tinh_index
    on dmsanluongkho (ma_tinh);

create index dm_sanluongkho_ngay_baocao_index
    on dms<PERSON><PERSON><PERSON>kho (ngay_baocao);

