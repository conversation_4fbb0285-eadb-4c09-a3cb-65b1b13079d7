create table dmchatluongbuuta
(
    ma_buu_cuc    varchar(255),
    ma_chi_nhanh  varchar(255),
    ma_nv         varchar(255),
    ngay_baocao   date,
    tb_chat_luong  float4,
    tl_giao_tc     float4,
    tl_giao_tc_dg  float4,
    tl_nhan_dg     float4,
    tl_nhan_tc     float4,
    tl_ton_lay     float4,
     primary key (ma_buu_cuc,ma_chi_nhanh,ma_nv,ngay_baocao)
);

alter table dmchatluongbuuta
    owner to noc;

create index dmchatluongbuuta_ma_buu_cuc_index
    on dmchatluongbuuta (ma_buu_cuc);

create index dmchatluongbuuta_ma_chi_nhanh_index
    on dmchatluongbuuta (ma_chi_nhanh);

create index dmchatluongbuuta_ma_nv_index
    on dmchatluongbuuta (ma_nv);

create index dmchatluongbuuta_ngay_baocao_index
    on dmchatluongbuuta (ngay_baocao);