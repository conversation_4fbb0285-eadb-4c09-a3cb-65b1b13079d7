-- public.cskd_tiendodoanhthu_bc definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_bc;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_bc (
	ma_buucuc varchar(50) NOT NULL,
	ma_chin<PERSON>h varchar(50) NOT NULL,
	nhom_doanhthu varchar(50) NOT NULL, -- DT-CP: Doanh thu chuyen phat, DT-LOGISTIC: Doanh thu logistic
	thuc_hien float8 NULL,
	ke_hoach float8 NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_nam float4 NULL,
    thangtruoc float8 NULL,
    namtruoc float8 NULL,
    cung_ky_ngay float8 NULL,
    cung_ky_thang float8 NULL,
    cung_ky_nam float8 NULL,
	ngay_baocao date NOT NULL,
	del_flag int4 NULL DEFAULT 0,
	created timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT cskd_tiendodoanhthu_bc_pkey PRIMARY KEY (ma_chinhanh, ma_buucuc, ngay_baocao, nhom_doanhthu)
);

-- Column comments

COMMENT ON COLUMN cskd_tiendodoanhthu_bc.nhom_doanhthu IS 'DT-CP: Doanh thu chuyen phat, DT-LOGISTIC: Doanh thu logistic';

create index INDEX_CSKD_TDDT_BC_NGAY_BAO_CAO
    on cskd_tiendodoanhthu_bc (ngay_baocao);
create index INDEX_CSKD_TDDT_BC_MA_CHI_NHANH_BC
    on cskd_tiendodoanhthu_bc (ma_chinhanh, ma_buucuc);
create index INDEX_CSKD_TDDT_BC_NHOM_DOANH_THU
    on cskd_tiendodoanhthu_bc (nhom_doanhthu);
