-- public.cskd_tiendodoanhthu_cp_cn definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_cp_cn;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_cp_cn (
	ma_chin<PERSON>h varchar(50) NOT NULL,
	ten_chinhanh varchar(255) NULL,
	ten_giamdoc varchar(50) NULL,
	loai_dichvu varchar(50) NOT NULL, -- NoN-COD, COD, EXP
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	thangtruoc float8 NULL,
	namtruoc float8 NULL,
	cung_ky_ngay float8 NULL,
	cung_ky_thang float8 NULL,
	cung_ky_nam float8 NULL,
	del_flag int4 NULL DEFAULT 0,
	ngay_baocao date NOT NULL,
	created timestamp NULL,
	CONSTRAINT cskd_tiendodoanhthu_cp_cn_pkey PRIMARY KEY (ngay_baocao, ma_chinhanh, loai_dichvu)
);

-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN cskd_tiendodoanhthu_cp_cn.loai_dichvu IS 'NoN-COD, COD, EXP';

-- Add Indexes
create index INDEX_TDDT_CP_CN_NGAY_BAO_CAO
    on cskd_tiendodoanhthu_cp_cn (ngay_baocao);
create index INDEX_TDDT_CP_CN_MA_CHI_NHANH
    on cskd_tiendodoanhthu_cp_cn (ngay_baocao, ma_chinhanh);
create index INDEX_TDDT_CP_CN_LOAI_DICH_VU
    on cskd_tiendodoanhthu_cp_cn (ngay_baocao, loai_dichvu);

-- Add column thuc_hien, ke_hoach
Alter table cskd_tiendodoanhthu_cp_cn add column thuc_hien float8 NULL,
    add column ke_hoach float8 NULL;