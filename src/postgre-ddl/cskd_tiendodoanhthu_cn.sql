-- public.cskd_tiendodoanhthu_cn definition

-- Drop table

-- DROP TABLE public.cskd_tiendodoanhthu_cn;

CREATE TABLE IF NOT EXISTS cskd_tiendodoanhthu_cn (
	ma_chin<PERSON>h varchar(50) NOT NULL,
	nhom_doanhthu varchar(50) NOT NULL,
	ke_hoach float8 NULL,
	thuc_hien float8 NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_nam float4 NULL,
    thangtruoc float8 NULL,
    namtruoc float8 NULL,
    cung_ky_ngay float8 NULL,
    cung_ky_thang float8 NULL,
    cung_ky_nam float8 NULL,
	ngay_baocao date NOT NULL,
	del_flag int4 NULL DEFAULT 0,
	created timestamp NULL,
	CONSTRAINT cskd_tiendodoanhthu_cn_pkey PRIMARY KEY (ma_chinhanh, ngay_baocao, nhom_doanhthu)
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN cskd_tiendodoanhthu_cn.nhom_doanhthu IS 'DT-CP: Doanh thu chuyen phat, DT-LOGISTIC: Doanh thu logistic';

create index INDEX_CSKD_TDDT_CN_NGAY_BAO_CAO
    on cskd_tiendodoanhthu_cn (ngay_baocao);
create index INDEX_CSKD_TDDT_CN_MA_CHI_NHANH
    on cskd_tiendodoanhthu_cn (ngay_baocao, ma_chinhanh);
create index INDEX_CSKD_TDDT_CN_NHOM_DOANH_THU
    on cskd_tiendodoanhthu_cn (ngay_baocao, nhom_doanhthu);