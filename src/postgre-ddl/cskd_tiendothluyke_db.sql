-- public.cskd_tiendothluyke_db definition

-- Drop table

-- DROP TABLE public.cskd_tiendothluyke_db;

CREATE TABLE IF NOT EXISTS cskd_tiendothluyke_db (
	mien varchar(20) NOT NULL,
	ngay_baocao date NOT NULL,
	tlht float4 NULL,
	tiendo float8 NULL,
	tt_thang float4 NULL,
	tt_tbn_thang float4 NULL,
	tt_nam float4 NULL,
	tt_tbn_nam float4 NULL,
	danhgia int2 NULL,
	CONSTRAINT cskd_tiendothluyke_db_pkey PRIMARY KEY (mien, ngay_baocao)
);

create index INDEX_CSKD_TD_LUY_KE_DB_NGAY_BAO_CAO
    on cskd_tiendothluyke_db (ngay_baocao, mien);