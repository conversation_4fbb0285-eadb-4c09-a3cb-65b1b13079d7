create table dmchatluongkho
(

    ma_buu_cuc               varchar(255),
    ma_tinh                  varchar(255),
    ngay_baocao              date,
    tb_chat_luong            float4,
    ten_tinh                 varchar(255),
    tg_khai_thac_tb_ttkt     float4,
    tl_don_lay_nhan_dung_gio float4,
    tl_don_lay_tc            float4,
    tl_giao_dung_gio         float4,
    tl_giao_tc               float4,
    tl_ton_giao              float4,
    tl_khai_thac_kpi_3h_ttkt float4,
    tl_ton_nhan              float4,
    tl_ton_pc_nhan           float4,
    tl_ton_tt_bc_nhan        float4,
    primary key (ma_buu_cuc, ngay_baocao,ma_tinh)
);

alter table dmchatluongkho
    owner to noc;

create index dmchatluongkho_ma_buu_cuc_index
    on dmchatluongkho (ma_buu_cuc);

create index dmchatluongkho_ma_tinh_index
    on dmchatluongkho (ma_tinh);

create index dmchatluongkho_ngay_baocao_index
    on dmchatluongkho (ngay_baocao);
