#FROM adoptopenjdk/openjdk11:alpine-jre
FROM maven:3.8.6-openjdk-11

RUN mkdir /opt/code

# COPY source code to folder
COPY . /opt/code/

# Attach to folder
WORKDIR /opt/code

# Show detail of code folder
RUN ls

# Create file .jar
RUN mvn clean package -Dmaven.test.skip=true;

# Attach folder target
WOR<PERSON>DIR target

# Show detail of folder target
RUN ls

# show path of directory
RUN pwd

# Check exist file
RUN test -e index-manager-0.0.1-SNAPSHOT.jar  && echo file exists || echo file not found

# Run file jar
ENTRYPOINT ["java","-jar","index-manager-0.0.1-SNAPSHOT.jar", "google-chrome"]